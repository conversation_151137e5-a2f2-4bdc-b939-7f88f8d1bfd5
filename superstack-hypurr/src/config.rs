use dotenv::dotenv;
use std::sync::OnceLock;

#[derive(Debug)]
pub struct Config {
    pub hl_dir: String,
    pub hl_node: Option<String>,
    pub watch_interval_secs: u64,
    pub redis_url: String,
    pub pg_url: String,
    pub pg_max_connections: u32,
    pub state_save_interval_blocks: u64,
    pub hl_api: String,
}

impl Config {
    pub fn get() -> &'static Config {
        static INSTANCE: OnceLock<Config> = OnceLock::new();

        INSTANCE.get_or_init(|| {
            dotenv().ok();

            let hl_dir = std::env::var("HL_DIR").expect("HL_DATA_DIR not set");
            let hl_node = std::env::var("HL_NODE").ok();
            let watch_interval_secs =
                std::env::var("WATCH_INTERVAL_SECS").unwrap_or_else(|_| "60".to_string());
            let redis_url = std::env::var("REDIS_URL").expect("REDIS_URL not set");
            let pg_url = std::env::var("PG_URL").expect("PG_URL not set");
            let pg_max_connections =
                std::env::var("PG_MAX_CONNECTIONS").unwrap_or_else(|_| "100".to_string());
            let state_save_interval_blocks =
                std::env::var("STATE_SAVE_INTERVAL_BLOCKS").unwrap_or_else(|_| "10000".to_string());
            let hl_api =
                std::env::var("HL_API").unwrap_or_else(|_| "http://localhost:3001".to_string());

            Config {
                hl_dir,
                hl_node,
                watch_interval_secs: watch_interval_secs.parse().unwrap(),
                redis_url,
                pg_url,
                pg_max_connections: pg_max_connections.parse().unwrap(),
                state_save_interval_blocks: state_save_interval_blocks.parse().unwrap(),
                hl_api,
            }
        })
    }
}
