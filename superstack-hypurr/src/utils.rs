use bigdecimal::ToPrimitive;

pub fn find_value<'a>(map: &'a rmpv::Value, key: &str) -> Option<&'a rmpv::Value> {
    map.as_map().and_then(|m| m.iter().find(|(k, _)| k.as_str() == Some(key)).map(|(_, v)| v))
}

pub fn convert_px(px: u64, sz_decimals: u32, base_decimals: u32) -> f64 {
    if base_decimals > sz_decimals {
        px as f64 / 10_u64.pow(base_decimals - sz_decimals) as f64
    } else {
        px as f64 * 10_u64.pow(sz_decimals - base_decimals) as f64
    }
}

// entry decimals = 6 (usdc decimals)
// sz decimals depends on perp, e.g. 5 for BTC
// entry_px = (entry / 10**6) / (sz / 10**sz_decimals) in $
// so avg = (sum(entry) / 10**6) / (sum(sz) / 10**sz_decimals) in $
// = (sum(entry) / sum(sz)) * 10**(sz_decimals - 6) in $
pub fn calc_entry_px(amt_acc: u128, sz_acc: u128, sz_decimals: u32, base_decimals: u32) -> f64 {
    if amt_acc == 0 || sz_acc == 0 {
        return 0.0;
    }
    let amt = bigdecimal::BigDecimal::from(amt_acc);
    let exp = sz_decimals as i64 - base_decimals as i64;
    let (int_val, scale) = amt.into_bigint_and_scale();
    let amt = bigdecimal::BigDecimal::from_bigint(int_val, scale - exp);
    let res = amt / sz_acc;
    res.to_f64().unwrap_or_default()
}

pub fn get_price_decimal(sz_decimals: u8) -> u8 {
    6 - sz_decimals
}
