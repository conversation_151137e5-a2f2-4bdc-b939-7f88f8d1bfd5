use std::{
    path::PathBuf,
    sync::Arc,
    time::{Duration, SystemTime},
};
use tokio::{
    fs,
    sync::{broadcast, Mutex},
};

use super::WatcherError;

/// Fixed file watcher
pub struct FileWatcher {
    file_path: PathBuf,
    watch_interval_secs: u64,
    last_modified: Arc<Mutex<Option<SystemTime>>>,
    tx: broadcast::Sender<PathBuf>,
}

impl FileWatcher {
    pub fn new(file_path: PathBuf, watch_interval_secs: u64, channel_capacity: usize) -> Self {
        let (tx, _) = broadcast::channel(channel_capacity);
        Self { file_path, watch_interval_secs, last_modified: Arc::new(Mutex::new(None)), tx }
    }

    /// Subscribe to file updates
    pub fn subscribe(&self) -> broadcast::Receiver<PathBuf> {
        self.tx.subscribe()
    }

    async fn step(&self) -> Result<(), WatcherError> {
        let metadata = fs::metadata(&self.file_path).await.map_err(WatcherError::Io)?;
        let last_modified = metadata.modified().map_err(WatcherError::Io)?;

        let mut last_modified_guard = self.last_modified.lock().await;
        if last_modified_guard
            .as_ref()
            .map(|last_modified_old| last_modified > *last_modified_old)
            .unwrap_or(true)
        {
            self.tx.send(self.file_path.clone()).map_err(|_| WatcherError::Channel)?;
            *last_modified_guard = Some(last_modified);
        } else {
            tracing::info!("file {} not modified", self.file_path.display());
        }
        Ok(())
    }

    pub async fn run(&self) {
        tracing::info!("file_watcher start: file_path={}", self.file_path.display());

        loop {
            match self.step().await {
                Ok(_) => {}
                Err(WatcherError::Io(e)) => {
                    tracing::error!("file_watcher error: {e}");
                }
                Err(WatcherError::Channel) => {
                    tracing::error!("channel disconnected");
                    break; // exit anyway
                }
            }
            tokio::time::sleep(Duration::from_secs(self.watch_interval_secs)).await;
        }
    }
}
