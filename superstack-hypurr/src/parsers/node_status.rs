use std::{
    fs::File,
    io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Seek, Seek<PERSON><PERSON>},
    path::PathBuf,
    str::FromStr,
    sync::Arc,
    time::Duration,
};

use crate::types::block::{order_status::OrderStatusEvent, HyperBlock};
use tokio::sync::{broadcast, oneshot, Mutex};

use crate::parsers::{AsyncRxConfirm, AsyncTxConfirm};

// Result enum to represent different exit states
#[derive(Debug)]
#[allow(unused)]
enum TailFileResult {
    SwitchFiles, // Need to switch files
    Error(u64),  // Error with file position for recovery
    Eof,         // Normal end
}

pub struct NodeStatusParser {
    current_file: Arc<Mutex<Option<PathBuf>>>,
    node_status_rx: broadcast::Receiver<PathBuf>,
    subscribers: Arc<Mutex<Vec<(&'static str, AsyncTxConfirm<HyperBlock<OrderStatusEvent>>)>>>,
}

impl NodeStatusParser {
    pub fn new(node_status_file_rx: broadcast::Receiver<PathBuf>) -> Self {
        Self {
            node_status_rx: node_status_file_rx,
            subscribers: Arc::new(Mutex::new(vec![])),
            current_file: Arc::new(Mutex::new(None)),
        }
    }

    pub async fn subscribe(
        &mut self,
        name: &'static str,
    ) -> AsyncRxConfirm<HyperBlock<OrderStatusEvent>> {
        tracing::info!("registering async consumer {}", name);
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        self.subscribers.lock().await.push((name, tx));
        rx
    }

    async fn notify_subscribers(
        subscribers: &Arc<Mutex<Vec<(&'static str, AsyncTxConfirm<HyperBlock<OrderStatusEvent>>)>>>,
        block: &HyperBlock<OrderStatusEvent>,
    ) {
        let mut subscribers_guard = subscribers.lock().await;
        let mut remove = vec![];

        for (name, tx) in subscribers_guard.iter() {
            tracing::debug!("notifying async consumer {}", name);
            let (ctx, crx) = oneshot::channel();

            if tx.send((block.clone(), ctx)).await.is_err() {
                tracing::error!("async consumer {name} disconnected, removing");
                remove.push(name as &'static str);
                continue;
            }

            if crx.await.is_err() {
                tracing::error!("async consumer {name} (confirm) disconnected, removing");
                remove.push(name as &'static str);
            }
        }

        subscribers_guard.retain(|(name, _)| !remove.contains(name));
    }

    async fn tail_file(
        file_path: &PathBuf,
        mut reader: BufReader<File>,
        subscribers: &Arc<Mutex<Vec<(&'static str, AsyncTxConfirm<HyperBlock<OrderStatusEvent>>)>>>,
        current_file: &Arc<Mutex<Option<PathBuf>>>,
    ) -> anyhow::Result<TailFileResult> {
        let mut line = String::new();

        loop {
            line.clear();

            // Record position before reading line for potential recovery
            let line_start_position = match reader.stream_position() {
                Ok(pos) => pos,
                Err(e) => {
                    tracing::error!("failed to get stream position before read: {}", e);
                    return Ok(TailFileResult::Error(0));
                }
            };

            match reader.read_line(&mut line) {
                Ok(0) => {
                    // EOF reached, check if current file has changed
                    let current = current_file.lock().await;
                    if let Some(ref current_path) = *current {
                        if current_path != file_path {
                            tracing::info!(
                                "current file changed from {} to {}, switching",
                                file_path.display(),
                                current_path.display()
                            );
                            return Ok(TailFileResult::SwitchFiles);
                        }
                    }
                    drop(current);

                    // Same file, wait a bit and try again
                    tokio::time::sleep(Duration::from_millis(100)).await;
                    continue;
                }
                Ok(_) => {
                    if line.trim().is_empty() {
                        continue;
                    }

                    // Check if line is complete (ends with newline)
                    if !line.ends_with('\n') {
                        tracing::warn!(
                            "incomplete line detected, returning to position {} to retry",
                            line_start_position
                        );
                        // Seek back to the start of this line to re-read it when more data is
                        // available
                        if let Err(e) = reader.seek(SeekFrom::Start(line_start_position)) {
                            tracing::error!(
                                "failed to seek back to position {}: {}",
                                line_start_position,
                                e
                            );
                            return Ok(TailFileResult::Error(line_start_position));
                        }
                        // Wait a bit for more data to be written
                        tokio::time::sleep(Duration::from_millis(100)).await;
                        continue;
                    }

                    match HyperBlock::<OrderStatusEvent>::from_str(&line) {
                        Ok(block) => {
                            Self::notify_subscribers(subscribers, &block).await;
                        }
                        Err(e) => {
                            tracing::error!(
                                "error parsing line from {}: {}",
                                file_path.display(),
                                e,
                            );
                            // For parsing errors, continue with next line (don't retry)
                            continue;
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("error reading line from {}: {}", file_path.display(), e);
                    // Return the position where we tried to read for recovery
                    return Ok(TailFileResult::Error(line_start_position));
                }
            }
        }
    }

    async fn process_file(
        file_path: &PathBuf,
        subscribers: &Arc<Mutex<Vec<(&'static str, AsyncTxConfirm<HyperBlock<OrderStatusEvent>>)>>>,
    ) -> anyhow::Result<()> {
        let file = File::open(file_path)?;
        let reader = BufReader::new(file);

        // Read any existing content first
        for line in reader.lines() {
            let line = line?;
            if line.trim().is_empty() {
                continue;
            }
            let block = HyperBlock::<OrderStatusEvent>::from_str(&line)?;
            Self::notify_subscribers(subscribers, &block).await;
        }

        Ok(())
    }

    async fn file_watcher_task(
        mut node_status_rx: broadcast::Receiver<PathBuf>,
        current_file: Arc<Mutex<Option<PathBuf>>>,
    ) {
        tracing::info!("file watcher task started");

        while let Ok(new_file) = node_status_rx.recv().await {
            let mut current = current_file.lock().await;

            // Check if this is the same file we're already processing
            if let Some(ref existing_file) = *current {
                if *existing_file == new_file {
                    // Same file, skip
                    continue;
                }
            }

            tracing::info!("updating current file to {}", new_file.display());
            *current = Some(new_file);
        }

        tracing::error!("file watcher channel disconnected");
    }

    async fn file_processor_task(
        subscribers: Arc<Mutex<Vec<(&'static str, AsyncTxConfirm<HyperBlock<OrderStatusEvent>>)>>>,
        current_file: Arc<Mutex<Option<PathBuf>>>,
    ) {
        tracing::info!("file processor task started");

        let mut processing_file: Option<PathBuf> = None;
        let mut last_position: Option<u64> = None; // Save last file position for recovery

        loop {
            // Check if we need to switch files
            let current = {
                let guard = current_file.lock().await;
                guard.clone()
            };

            match current {
                Some(ref file_path) => {
                    if processing_file.as_ref() != Some(file_path) {
                        tracing::info!("starting to process file {}", file_path.display());
                        processing_file = Some(file_path.clone());
                        // Reset position, start from beginning for new file
                        last_position = None;

                        // Process existing content in the file
                        if let Err(e) = Self::process_file(file_path, &subscribers).await {
                            tracing::error!(
                                "error processing existing content in {}: {}",
                                file_path.display(),
                                e
                            );
                            tokio::time::sleep(Duration::from_secs(1)).await;
                            continue;
                        }
                    }

                    // Start tailing the file
                    let file = match File::open(file_path) {
                        Ok(file) => file,
                        Err(e) => {
                            tracing::error!("error opening file {}: {}", file_path.display(), e);
                            tokio::time::sleep(Duration::from_secs(1)).await;
                            continue;
                        }
                    };

                    let mut reader = BufReader::new(file);

                    // If we have a saved position, start from there; otherwise start from end
                    if let Some(pos) = last_position {
                        if let Err(e) = reader.seek(SeekFrom::Start(pos)) {
                            tracing::error!(
                                "error seeking to position {} in {}: {}",
                                pos,
                                file_path.display(),
                                e
                            );
                            tokio::time::sleep(Duration::from_secs(1)).await;
                            continue;
                        }
                        tracing::info!("resuming from position {} in {}", pos, file_path.display());
                    } else {
                        // First time processing this file, start from end
                        if let Err(e) = reader.seek(SeekFrom::End(0)) {
                            tracing::error!(
                                "error seeking to end of {}: {}",
                                file_path.display(),
                                e
                            );
                            tokio::time::sleep(Duration::from_secs(1)).await;
                            continue;
                        }
                    }

                    // Tail the file until we need to switch
                    match Self::tail_file(file_path, reader, &subscribers, &current_file).await {
                        Ok(TailFileResult::SwitchFiles) => {
                            // Reset position when switching files
                            last_position = None;
                            continue;
                        }
                        Ok(TailFileResult::Error(position)) => {
                            tracing::warn!(
                                "tailing file {} failed at position {}, will retry from this position",
                                file_path.display(),
                                position
                            );
                            // Save error position for recovery
                            last_position = Some(position);
                            tokio::time::sleep(Duration::from_secs(1)).await;
                        }
                        Ok(TailFileResult::Eof) => {
                            break; // Normal end
                        }
                        Err(e) => {
                            tracing::error!("error tailing file {}: {}", file_path.display(), e);
                            // Reset position on serious error, start from end next time
                            last_position = None;
                            tokio::time::sleep(Duration::from_secs(1)).await;
                        }
                    }
                }
                None => {
                    // No file to process yet, wait
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }
        }

        tracing::info!("file processor task stopped");
    }

    pub async fn run(self) {
        tracing::info!("node status parser start");

        let current_file = self.current_file.clone();
        let subscribers = self.subscribers.clone();

        // Start file watcher task
        let watcher_current_file = current_file.clone();
        let watcher_task = tokio::spawn(async move {
            Self::file_watcher_task(self.node_status_rx, watcher_current_file).await;
        });

        // Start file processor task
        let processor_task = tokio::spawn(async move {
            Self::file_processor_task(subscribers, current_file).await;
        });

        // Wait for either task to complete
        tokio::select! {
            _ = watcher_task => {
                tracing::error!("file watcher task completed unexpectedly");
            }
            _ = processor_task => {
                tracing::error!("file processor task completed unexpectedly");
            }
        }

        tracing::info!("node status parser stopped");
    }
}
