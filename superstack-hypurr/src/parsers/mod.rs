use crate::watcher::Upstream;
use tokio::sync::{mpsc, oneshot};
use tokio_util::sync::CancellationToken;

pub mod node_status;
pub mod state;
use node_status::NodeStatusParser;
use state::StateParser;

pub(crate) type AsyncTxConfirm<T> = mpsc::Sender<(T, oneshot::Sender<()>)>;
pub(crate) type AsyncRxConfirm<T> = mpsc::Receiver<(T, oneshot::Sender<()>)>;

pub struct Parsers {
    pub state: StateParser,
    pub node_status: NodeStatusParser,
    cancel: CancellationToken,
}

impl Parsers {
    pub fn new(upstream: &Upstream, cancel: CancellationToken) -> Self {
        // Subscribe to upstream watchers based on parser needs
        let abci_state_rx = upstream.abci_state.subscribe();
        let state = StateParser::new(abci_state_rx);
        let order_status_rx = upstream.order_status.subscribe();
        let node_status = NodeStatusParser::new(order_status_rx);

        Self { state, node_status, cancel }
    }

    /// Run all parsers concurrently with internal subscription management
    pub async fn run(self) {
        let state_task = self.state.start().await.unwrap();

        let node_status_task = tokio::spawn(async move {
            self.node_status.run().await;
        });

        // Store abort handles
        let state_abort = state_task.abort_handle();
        let node_status_abort = node_status_task.abort_handle();

        // Use select! to handle cancellation - centralized cancellation management per memory
        tokio::select! {
            _ = self.cancel.cancelled() => {
                tracing::info!("parsers cancelled, aborting tasks");
            }
            state_result = state_task => {
                if let Err(e) = state_result {
                    tracing::error!("state parser task failed: {e}");
                }
            }
            node_status_result = node_status_task => {
                if let Err(e) = node_status_result {
                    tracing::error!("node_status parser task failed: {e}");
                }
            }
        }

        state_abort.abort();
        node_status_abort.abort();
        tracing::info!("parsers stopped");
    }
}
