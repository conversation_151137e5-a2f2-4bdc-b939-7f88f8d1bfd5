use std::{fs::File, path::PathBuf, sync::Arc};

use tokio::{
    sync::{broadcast, oneshot},
    task::<PERSON><PERSON><PERSON><PERSON><PERSON>,
};

use crate::{
    parsers::{AsyncRxConfirm, AsyncTxConfirm},
    types::state::State,
};

pub struct StateParser {
    abci_state_rx: broadcast::Receiver<PathBuf>,
    state_ch_async: Vec<(&'static str, AsyncTxConfirm<Arc<State>>)>,
}

impl StateParser {
    pub fn new(abci_state_rx: broadcast::Receiver<PathBuf>) -> Self {
        Self { abci_state_rx, state_ch_async: vec![] }
    }

    pub fn subscribe(&mut self, name: &'static str) -> AsyncRxConfirm<Arc<State>> {
        tracing::info!("registering async consumer {}", name);
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        self.state_ch_async.push((name, tx));
        rx
    }

    async fn process(&mut self, rmp: &PathBuf) -> anyhow::Result<()> {
        let rmp = rmp.clone();
        let msg = tokio::task::spawn_blocking(move || {
            // 1. decode rmp
            tracing::info!("decoding {}", rmp.display());
            let now = std::time::Instant::now();
            let file = File::open(&rmp)?;
            let md = file.metadata()?;
            tracing::info!(
                "file last modify: {}",
                chrono::DateTime::<chrono::Utc>::from(md.modified()?)
            );
            let mut cursor = std::io::BufReader::new(file);
            let value: rmpv::Value = rmpv::decode::read_value(&mut cursor)?;
            tracing::info!("decoded in {:?}", now.elapsed());

            // 2. parse state
            tracing::info!("parsing {}", rmp.display());
            let now = std::time::Instant::now();
            let state = State::from_rmpv_value(&value)?;
            tracing::info!("parsed in {:?}", now.elapsed());

            Ok::<_, anyhow::Error>(Arc::new(state))
        })
        .await??;

        // 4. persist async
        let mut remove = vec![];
        for (name, tx) in self.state_ch_async.iter() {
            tracing::info!("notifying async consumer {}", name);
            let (ctx, crx) = oneshot::channel();
            if tx.send((msg.clone(), ctx)).await.is_err() {
                tracing::error!("async consumer {} disconnected, removing", name);
                remove.push(name as &'static str);
            }
            if crx.await.is_err() {
                tracing::error!("async consumer {} (confirm) disconnected, removing", name);
                remove.push(name as &'static str);
            }
        }
        self.state_ch_async.retain(|(name, _)| !remove.contains(name));

        Ok(())
    }

    pub async fn run(mut self) {
        loop {
            match self.abci_state_rx.recv().await {
                Ok(rmp) => {
                    tracing::info!("processing rmp {}", rmp.display());

                    if let Err(e) = self.process(&rmp).await {
                        tracing::error!("error processing rmp: {e}");
                    } else {
                        tracing::info!("processed rmp");
                    }
                }
                Err(_) => {
                    tracing::error!("channel disconnected");
                    break;
                }
            }
        }
        tracing::info!("state_parser stopped");
    }

    pub async fn start(mut self) -> anyhow::Result<JoinHandle<()>> {
        tracing::info!("state parser initializing");

        let path = self.abci_state_rx.recv().await?;
        self.process(&path).await?;

        tracing::info!("state parser initialized");

        Ok(tokio::spawn(async move {
            self.run().await;
        }))
    }
}
