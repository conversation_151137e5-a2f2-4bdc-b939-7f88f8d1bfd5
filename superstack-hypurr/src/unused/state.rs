use std::{collections::HashMap, sync::Arc, time::Duration};

use anyhow::Error;
use hyperliquid_rust_sdk::{InfoRequest, UserStateResponse};
use moka::future::Cache;
use serde::Deserialize;
use superstack_data::{postgres::PostgresDatabase, redis::RedisClient};
use tokio::sync::{mpsc::Receiver, oneshot};

use crate::parsers::state::{ByPerpInfo, StateMessage};

pub struct StateProcessor {
    redis_client: RedisClient,
    // db_client: PostgresDatabase,
    rx: Receiver<(StateMessage, oneshot::Sender<()>)>,
    inner: HlStateInner,
}

#[derive(Clone)]
struct HlStateInner {
    hl_info_cli: reqwest::Client,
    hl_user_cache: Cache<String, Arc<UserStateResponse>>,
}

impl HlStateInner {
    async fn parse_hl_response(
        response: reqwest::Response,
    ) -> Result<String, hyperliquid_rust_sdk::Error> {
        use hyperliquid_rust_sdk::Error;
        #[derive(Deserialize, Debug)]
        struct ErrorData {
            data: String,
            code: u16,
            msg: String,
        }

        let status_code = response.status().as_u16();
        let text = response.text().await.map_err(|e| Error::GenericRequest(e.to_string()))?;

        if status_code < 400 {
            return Ok(text);
        }
        let error_data = serde_json::from_str::<ErrorData>(&text);
        if (400..500).contains(&status_code) {
            let client_error = match error_data {
                Ok(error_data) => Error::ClientRequest {
                    status_code,
                    error_code: Some(error_data.code),
                    error_message: error_data.msg,
                    error_data: Some(error_data.data),
                },
                Err(err) => Error::ClientRequest {
                    status_code,
                    error_message: text,
                    error_code: None,
                    error_data: Some(err.to_string()),
                },
            };
            return Err(client_error);
        }

        Err(Error::ServerRequest { status_code, error_message: text })
    }

    async fn get_user_hl_state(&self, user_addr: &str) -> Option<Arc<UserStateResponse>> {
        let address = user_addr.parse().ok()?;
        match self
            .hl_user_cache
            .try_get_with(user_addr.to_owned(), async {
                let url = format!("{}/info", crate::config::Config::get().hl_api);
                let resp = self
                    .hl_info_cli
                    .post(url)
                    .json(&InfoRequest::UserState { user: address })
                    .send()
                    .await?;

                let json = Self::parse_hl_response(resp).await?;
                let user_state = serde_json::from_str::<UserStateResponse>(&json)?;
                Ok::<_, anyhow::Error>(Arc::new(user_state))
            })
            .await
        {
            Ok(user_state) => Some(user_state),
            Err(e) => {
                tracing::error!("error getting user state for {}: {}", user_addr, e);
                None
            }
        }
    }
}

impl StateProcessor {
    pub async fn new(
        redis_client: RedisClient,
        // db_client: PostgresDatabase,
        rx: Receiver<(StateMessage, oneshot::Sender<()>)>,
    ) -> Result<Self, Error> {
        // todo: support custome url
        let hl_info_cli = reqwest::Client::builder().timeout(Duration::from_secs(1)).build()?;
        let hl_user_cache =
            Cache::builder().max_capacity(2048).time_to_live(Duration::from_secs(60 * 3)).build();
        let inner = HlStateInner { hl_info_cli, hl_user_cache };
        Ok(Self { redis_client, rx, inner })
    }

    async fn process_state_msg(
        &self,
        StateMessage { perp_tops, perps, .. }: StateMessage,
    ) -> Result<(), anyhow::Error> {
        let now = chrono::Utc::now().timestamp_millis();
        // save user states to db
        // let pg = self.db_client.clone();
        // let user_states_task = tokio::spawn(async move {
        //     if let Err(e) = pg.insert_or_update_perp_user_states_chunked(&accts,
        // Some(1000)).await {         tracing::error!("Failed to save user states: {}", e);
        //     }
        // });

        #[derive(Debug, Default)]
        struct CollectedWithApi {
            long_liq_dist_acc: f64,
            long_liq_dist_count: usize,
            short_liq_dist_acc: f64,
            short_liq_dist_count: usize,
        }

        // top users
        let redis = self.redis_client.clone();
        let inner = self.inner.clone();
        let top_users_task = tokio::spawn(async move {
            // collect again info needed by perp
            let mut need_by_perp = HashMap::<String, CollectedWithApi>::new();

            for (perp_name, ByPerpInfo { px, top_users }) in perp_tops {
                let mut top_users_ok = vec![];
                for mut user_state in top_users {
                    // try get liq px
                    if let Some(hl) = inner.get_user_hl_state(&user_state.user_addr).await {
                        let liq_px = hl
                            .asset_positions
                            .iter()
                            .find(|pos| &pos.position.coin == &perp_name)
                            .and_then(|pos| pos.position.liquidation_px.as_ref())
                            .and_then(|s| s.parse::<f64>().ok());

                        if let Some(liq_px) = &liq_px {
                            let entry = need_by_perp.entry(perp_name.clone()).or_default();
                            if user_state.is_long {
                                entry.long_liq_dist_acc += (px - liq_px).abs() / px;
                                entry.long_liq_dist_count += 1;
                            } else {
                                entry.short_liq_dist_acc += (px - liq_px).abs() / px;
                                entry.short_liq_dist_count += 1;
                            }
                        }

                        user_state.liq_px = liq_px;
                    };

                    user_state.timestamp_millis = now;
                    if let Err(e) = redis.set_perp_top_user(&user_state).await {
                        tracing::error!("Failed to write top users: {}", e);
                    } else {
                        top_users_ok.push(user_state.user_addr);
                    }
                }
                if let Err(e) = redis.set_perp_top_user_ids(&perp_name, &top_users_ok).await {
                    tracing::error!("Failed to write top users ids: {}", e);
                } else {
                    tracing::info!(
                        "Wrote {} top users for perp: {}",
                        top_users_ok.len(),
                        perp_name
                    );
                }
            }
            need_by_perp
        });
        let need_by_perp = top_users_task.await?;

        // perps
        let redis = self.redis_client.clone();
        let perps_task = tokio::spawn(async move {
            let mut perps_ok = 0;
            for mut perp in perps.into_values() {
                if let Some(v) = need_by_perp.get(&perp.perp_id) {
                    perp.long_liq_dist = v.long_liq_dist_acc / v.long_liq_dist_count as f64;
                    perp.short_liq_dist = v.short_liq_dist_acc / v.short_liq_dist_count as f64;
                }
                perp.updated_at_millis = now;
                if let Err(e) = redis.set_perp_state(&perp).await {
                    tracing::error!("Failed to write perp state: {}", e);
                } else {
                    tracing::info!("Wrote perp state for token: {}", perp.perp_id);
                    perps_ok += 1;
                }
            }
            tracing::info!("Wrote {} perp states", perps_ok);
        });
        perps_task.await?;

        Ok(())
    }

    pub async fn run(mut self) {
        tracing::info!("perp state processor started");
        loop {
            match self.rx.recv().await {
                Some((state_msg, confirm)) => {
                    tracing::info!("processing state with {} tokens", state_msg.perps.len());

                    if let Err(e) = self.process_state_msg(state_msg).await {
                        tracing::error!("Error processing state: {}", e);
                    }

                    if confirm.send(()).is_err() {
                        tracing::error!("Failed to send confirmation");
                    }
                }
                None => break,
            }
        }
        tracing::info!("perp state processor stopped");
    }
}
