use bigdecimal::ToPrimitive;
use superstack_data::{
    postgres::{
        indexer::perp_books::{PerpB<PERSON>, PerpB<PERSON><PERSON>},
        PerpExchange,
    },
    redis::RedisClient,
};
use tokio::sync::{mpsc::Receiver, oneshot};

use crate::types::L4BookStates;

pub struct PerpBooksProcessor {
    redis_client: RedisClient,
    rx: Receiver<(L4BookStates, oneshot::Sender<()>)>,
}

impl PerpBooksProcessor {
    pub fn new(
        redis_client: RedisClient,
        rx: Receiver<(L4BookStates, oneshot::Sender<()>)>,
    ) -> Self {
        Self { redis_client, rx }
    }

    /// Convert L4BookStates to Vec<PerpBook>
    fn convert_l4_to_perp_books(l4_book_states: L4BookStates, timestamp: i64) -> Vec<PerpBooks> {
        let mut results = Vec::new();

        for token_stats in l4_book_states {
            let mut bid_books = Vec::new();
            let mut ask_books = Vec::new();

            // Convert bid stats to PerpBook
            for (price_str, (total_sz, order_count)) in &token_stats.bid_stats.price_intervals {
                if let Ok(px) = price_str.parse::<f64>() {
                    if let Some(sz) = total_sz.to_f64() {
                        bid_books.push(PerpBook { px, sz, n: *order_count });
                    }
                }
            }

            // Convert ask stats to PerpBook
            for (price_str, (total_sz, order_count)) in &token_stats.ask_stats.price_intervals {
                if let Ok(px) = price_str.parse::<f64>() {
                    if let Some(sz) = total_sz.to_f64() {
                        ask_books.push(PerpBook { px, sz, n: *order_count });
                    }
                }
            }

            // Sort by price (bids descending, asks ascending)
            bid_books.sort_by(|a, b| b.px.partial_cmp(&a.px).unwrap_or(std::cmp::Ordering::Equal));
            ask_books.sort_by(|a, b| a.px.partial_cmp(&b.px).unwrap_or(std::cmp::Ordering::Equal));

            results.push(PerpBooks {
                perp_id: token_stats.token,
                perp_exchange: PerpExchange::Hyperliquid,
                updated_at_millis: timestamp,
                total_sz: (token_stats.bid_stats.total_sz + token_stats.ask_stats.total_sz)
                    .to_f64()
                    .unwrap_or_default(),
                books: [bid_books, ask_books],
            });
        }

        results
    }

    async fn process_l4_book(&self, l4_book: L4BookStates) -> Result<(), anyhow::Error> {
        let timestamp = chrono::Utc::now().timestamp_millis();
        let converted_books = Self::convert_l4_to_perp_books(l4_book, timestamp);

        for books in converted_books {
            tracing::info!("Writing perp books for token: {}", books.perp_id);
            if let Err(e) = self.redis_client.set_perp_books(&books).await {
                tracing::error!("Failed to write perp books: {}", e);
            } else {
                tracing::info!(
                    "Wrote perp books for token: {} (bids: {}, asks: {})",
                    books.perp_id,
                    books.books[0].len(),
                    books.books[1].len(),
                );
            }
        }

        Ok(())
    }

    pub async fn run(mut self) {
        tracing::info!("perp books processor started");
        loop {
            match self.rx.recv().await {
                Some((l4_book, confirm)) => {
                    tracing::info!("processing l4 book with {} tokens", l4_book.len());

                    if let Err(e) = self.process_l4_book(l4_book).await {
                        tracing::error!("Error processing l4 book: {}", e);
                    }

                    if confirm.send(()).is_err() {
                        tracing::error!("Failed to send confirmation");
                    }
                }
                None => break,
            }
        }
        tracing::info!("perp books processor stopped");
    }
}
