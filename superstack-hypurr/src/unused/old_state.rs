use alloy::primitives::map::HashSet;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, fs::File, path::PathBuf};
use superstack_data::postgres::{
    indexer::{
        perp_state::PerpBriefState, perp_top_user_state::PerpTopUserState,
        perp_user_state::PerpUserState,
    },
    PerpExchange,
};
use tokio::sync::{broadcast, mpsc, oneshot};

use crate::types::state::{position::Leverage, State};

type AsyncTxConfirm = mpsc::Sender<(StateMessage, oneshot::Sender<()>)>;
type AsyncRxConfirm = mpsc::Receiver<(StateMessage, oneshot::Sender<()>)>;

const USDC_DECIMALS: u32 = 6;
const LEADERBOARD_USERS_PERCENT: u32 = 1;

pub struct StateParser {
    abci_state_rx: broadcast::Receiver<PathBuf>,
    state_ch_async: Vec<(&'static str, AsyncTxConfirm)>,
}

#[derive(C<PERSON>, Debug, Serialize, Deserialize)]
struct AccountStat {
    pub user_addr: String,
    pub account_value: u128,
    pub perps_account_value: u128,
    pub total_ntl: u128,
    pub c: i64,
    pub p: i64,
    pub positions: Vec<BriefPosition>,
}

impl From<AccountStat> for PerpUserState {
    fn from(account: AccountStat) -> Self {
        let account_value = account.account_value as f64 / 10_f64.powf(USDC_DECIMALS as f64);
        let perps_account_value =
            account.perps_account_value as f64 / 10_f64.powf(USDC_DECIMALS as f64);

        let hyperliquid_acc_collateral = account.c as f64 / 100_f64;
        let hyperliquid_acc_perps = account.p as f64 / 100_f64;

        Self {
            perp_exchange: PerpExchange::Hyperliquid,
            perp_user: account.user_addr,
            perps_account_value,
            account_value,
            hyperliquid_acc_collateral,
            hyperliquid_acc_perps,
            timestamp_millis: 0, // modify when persist
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct BriefPosition {
    pub perp_name: String,
    pub ntl: u128,
    pub is_long: bool,
    pub entry_amt: u128,
    pub entry_sz: u128,
    pub sz_decimals: u32,
    pub leverage: Option<Leverage>,
    pub funding: Option<i64>,
}

impl BriefPosition {
    pub fn new(
        perp_name: String,
        ntl: u128,
        is_long: bool,
        entry_amt: u128,
        entry_sz: u128,
        sz_decimals: u32,
        leverage: Option<Leverage>,
        funding: Option<i64>,
    ) -> Self {
        Self { perp_name, ntl, is_long, entry_amt, entry_sz, sz_decimals, leverage, funding }
    }
}

// user -> (account_value, position_of_perp)
struct CollectedPerp {
    px: f64,
    users: HashMap<String, (u128, BriefPosition)>, // user -> (account_value, pos)
}

#[derive(Debug, Default)]
struct AggregatedPerpState {
    long_ntl: u128,
    short_ntl: u128,
    long_traders: u64,
    short_traders: u64,
    long_entry_amt_acc: u128,
    long_entry_sz_acc: u128,
    short_entry_amt_acc: u128,
    short_entry_sz_acc: u128,
    long_pnl_acc: i128,
    short_pnl_acc: i128,
    sz_decimals: u32,
}

impl AggregatedPerpState {
    pub fn to_perp_brief_state(self, perp_id: String) -> PerpBriefState {
        PerpBriefState {
            perp_id,
            long_ntl: self.long_ntl,
            short_ntl: self.short_ntl,
            long_sz: self.long_entry_sz_acc as f64 / 10_u64.pow(self.sz_decimals) as f64,
            short_sz: self.short_entry_sz_acc as f64 / 10_u64.pow(self.sz_decimals) as f64,
            long_traders: self.long_traders,
            short_traders: self.short_traders,
            long_entry: crate::utils::calc_entry_px(
                self.long_entry_amt_acc,
                self.long_entry_sz_acc,
                self.sz_decimals,
                USDC_DECIMALS,
            ),
            short_entry: crate::utils::calc_entry_px(
                self.short_entry_amt_acc,
                self.short_entry_sz_acc,
                self.sz_decimals,
                USDC_DECIMALS,
            ),
            long_pnl: self.long_pnl_acc as f64 / 10_u64.pow(USDC_DECIMALS) as f64,
            short_pnl: self.short_pnl_acc as f64 / 10_u64.pow(USDC_DECIMALS) as f64,
            long_liq_dist: 0.,    // update during post processing
            short_liq_dist: 0.,   // update during post processing
            updated_at_millis: 0, // modify when persist
        }
    }
}

#[derive(Clone)]
pub struct StateMessage {
    // pub accts: Vec<PerpUserState>,
    pub perp_tops: HashMap<String, ByPerpInfo>,
    pub perps: HashMap<String, PerpBriefState>,
}

#[derive(Clone)]
pub struct ByPerpInfo {
    pub px: f64,
    pub top_users: Vec<PerpTopUserState>,
}

impl StateParser {
    pub fn new(abci_state_rx: broadcast::Receiver<PathBuf>) -> Self {
        Self { abci_state_rx, state_ch_async: vec![] }
    }

    pub fn subscribe(&mut self, name: &'static str) -> AsyncRxConfirm {
        tracing::info!("registering async consumer {}", name);
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        self.state_ch_async.push((name, tx));
        rx
    }

    // return map(perp_name -> map(user -> (account_value, position))) where users are tops of each
    // perp
    fn aggregate_accounts_by_perp(state: &State) -> HashMap<String, CollectedPerp> {
        // 1st pass: collect positions & ntl
        // map(perp_name ->  map(user -> (position)))
        let mut user_positions_by_perp: HashMap<String, HashMap<String, BriefPosition>> =
            HashMap::new();
        // map(perp_name -> map(user -> total_ntl))
        let mut users_ntl_by_perp: HashMap<String, HashMap<String, u128>> = HashMap::new();
        let mut users_account_value: HashMap<String, u128> = HashMap::new();
        let mut perps_px: HashMap<String, u64> = HashMap::new();
        for (user_addr, user_stat) in state.user_states.iter() {
            for position in user_stat.positions.iter() {
                let asset_idx = position.asset_idx;
                let perp_name = match state.meta.get_perp_name_by_idx(asset_idx) {
                    Some(name) => name,
                    None => {
                        tracing::error!("perp not found for asset_idx {asset_idx}");
                        continue;
                    }
                };
                let perp_px = match perps_px.get(perp_name) {
                    Some(px) => *px,
                    _ => match state.meta.get_perp_px_by_idx(asset_idx) {
                        Some(px) => {
                            perps_px.insert(perp_name.to_owned(), px);
                            px
                        }
                        None => {
                            tracing::error!("perp px not found for asset_idx {asset_idx}");
                            continue;
                        }
                    },
                };
                let sz_decimals = match state.meta.get_perp_sz_decimals_by_idx(asset_idx) {
                    Some(decimals) => decimals,
                    None => {
                        tracing::error!("perp sz decimals not found for asset_idx {asset_idx}");
                        continue;
                    }
                };
                let ntl = position.notional(perp_px);
                let ntl_abs = ntl.abs() as u128;
                let funding = position.funding.clone().and_then(|f| f.since_open);

                // collect user position by perp
                let perp_entry = user_positions_by_perp.entry(perp_name.to_owned()).or_default();
                perp_entry.insert(
                    user_addr.to_string(),
                    BriefPosition::new(
                        perp_name.to_owned(),
                        ntl_abs,
                        ntl > 0,
                        position.entry_amount as _,
                        position.size.abs() as _,
                        sz_decimals as _,
                        position.leverage.clone(),
                        funding,
                    ),
                );

                // collect users ntl by perp
                let user_entry = users_ntl_by_perp
                    .entry(perp_name.to_owned())
                    .or_default()
                    .entry(user_addr.to_string())
                    .or_default();
                *user_entry += ntl_abs;
            }
            let account_value =
                user_stat.account_value(&state.meta, &state.spot_meta, &state.spot_books);
            users_account_value.insert(user_addr.to_string(), account_value as u128);
        }

        // 2nd pass: filter top users
        let top_users_by_perp = users_ntl_by_perp
            .into_iter()
            .map(|(perp_name, users)| {
                let mut users: Vec<(String, u128)> =
                    users.into_iter().map(|(user_addr, ntl)| (user_addr, ntl)).collect();
                let index =
                    (users.len() as f64 * LEADERBOARD_USERS_PERCENT as f64 / 100.0) as usize;
                users.select_nth_unstable_by_key(index, |v| std::cmp::Reverse(v.1));
                users.truncate(index);
                (
                    perp_name,
                    users.into_iter().map(|(user_addr, _)| user_addr).collect::<HashSet<_>>(),
                )
            })
            .collect::<HashMap<_, _>>();

        // 3. filter out non-top users from user_positions_by_perp
        for (perp_name, user_positions) in user_positions_by_perp.iter_mut() {
            if let Some(top_users) = top_users_by_perp.get(perp_name) {
                user_positions.retain(|user_addr, _| top_users.contains(user_addr));
            }
        }

        // 4. collect account_value
        let mut res = HashMap::with_capacity(user_positions_by_perp.len());
        for (perp_name, user_positions) in user_positions_by_perp {
            let sz_decimals = state
                .meta
                .get_perp_idx(&perp_name)
                .and_then(|idx| state.meta.get_perp_sz_decimals_by_idx(idx))
                .unwrap_or(0);
            let perp_px = match perps_px.get(&perp_name) {
                Some(px) => crate::utils::convert_px(*px, sz_decimals as _, USDC_DECIMALS),
                None => {
                    tracing::error!("perp px not found for perp_name {perp_name}");
                    continue;
                }
            };

            // attach account_value to each user
            let collected_users = user_positions
                .into_iter()
                .map(|(user_addr, position)| {
                    // account_value or 0
                    let account_value = *users_account_value.get(&user_addr).unwrap_or(&0);
                    (user_addr, (account_value, position))
                })
                .collect::<HashMap<_, _>>();

            res.insert(perp_name, CollectedPerp { px: perp_px, users: collected_users });
        }

        res
    }

    fn aggregate_perps_with_top_users(
        top_users_by_perp: &HashMap<String, CollectedPerp>,
    ) -> HashMap<String, PerpBriefState> {
        let mut perps: HashMap<String, AggregatedPerpState> = HashMap::new();

        for (perp_name, CollectedPerp { users, .. }) in top_users_by_perp.iter() {
            let perp_entry =
                perps.entry(perp_name.to_owned()).or_insert_with(|| AggregatedPerpState::default());
            for (_, (_, position)) in users.iter() {
                // set sz_decimals for perp
                perp_entry.sz_decimals = position.sz_decimals;

                if position.is_long {
                    perp_entry.long_ntl += position.ntl;
                    perp_entry.long_traders += 1;
                    perp_entry.long_entry_amt_acc += position.entry_amt;
                    perp_entry.long_entry_sz_acc += position.entry_sz;
                    perp_entry.long_pnl_acc += position.ntl as i128 - position.entry_amt as i128 +
                        position.funding.unwrap_or(0) as i128;
                } else {
                    perp_entry.short_ntl += position.ntl;
                    perp_entry.short_traders += 1;
                    perp_entry.short_entry_amt_acc += position.entry_amt;
                    perp_entry.short_entry_sz_acc += position.entry_sz;
                    perp_entry.short_pnl_acc += position.entry_amt as i128 - position.ntl as i128 +
                        position.funding.unwrap_or(0) as i128;
                }
            }
        }

        perps
            .into_iter()
            .map(|(perp_id, state)| (perp_id.clone(), state.to_perp_brief_state(perp_id)))
            .collect()
    }

    #[allow(unused)]
    fn aggregate_accounts(state: &State) -> Vec<AccountStat> {
        let mut accounts = vec![];

        for (user_addr, user_stat) in state.user_states.iter() {
            let mut total_ntl = 0u128;
            let mut positions = vec![];

            for position in user_stat.positions.iter() {
                let perp_name = match state.meta.get_perp_name_by_idx(position.asset_idx) {
                    Some(name) => name,
                    None => {
                        tracing::error!("perp not found for asset_idx {}", position.asset_idx);
                        continue;
                    }
                };
                let perp_px = match state.meta.get_perp_px_by_idx(position.asset_idx) {
                    Some(px) => px,
                    None => {
                        tracing::error!("perp px not found for asset_idx {}", position.asset_idx);
                        continue;
                    }
                };
                let sz_decimals = match state.meta.get_perp_sz_decimals_by_idx(position.asset_idx) {
                    Some(decimals) => decimals,
                    None => {
                        tracing::error!(
                            "perp sz decimals not found for asset_idx {}",
                            position.asset_idx
                        );
                        continue;
                    }
                };

                let ntl = position.notional(perp_px);
                let ntl_abs = ntl.abs() as u128;
                total_ntl += ntl_abs;
                let funding = position.funding.clone().and_then(|f| f.since_open);

                positions.push(BriefPosition::new(
                    perp_name.to_string(),
                    ntl_abs,
                    ntl > 0,
                    position.entry_amount as _,
                    position.size.unsigned_abs() as _,
                    sz_decimals as _,
                    position.leverage.clone(),
                    funding,
                ));
            }

            let account_value =
                user_stat.account_value(&state.meta, &state.spot_meta, &state.spot_books);
            let perps_account_value = user_stat.perps_account_value(&state.meta);

            accounts.push(AccountStat {
                user_addr: user_addr.to_string(),
                account_value,
                perps_account_value,
                total_ntl,
                c: user_stat.c,
                p: user_stat.p,
                positions,
            });
        }

        // filter top users
        let index = (accounts.len() as f64 * LEADERBOARD_USERS_PERCENT as f64 / 100.0) as usize;
        accounts.select_nth_unstable_by_key(index, |v| std::cmp::Reverse(v.total_ntl));
        accounts.truncate(index);

        accounts
    }

    #[allow(unused)]
    fn aggregate_perps(accounts: &Vec<AccountStat>) -> HashMap<String, PerpBriefState> {
        let mut perps: HashMap<String, AggregatedPerpState> = HashMap::new();

        for account in accounts {
            for position in &account.positions {
                let perp_id = position.perp_name.clone();
                let entry = perps.entry(perp_id.clone()).or_insert_with(|| AggregatedPerpState {
                    sz_decimals: position.sz_decimals,
                    ..Default::default()
                });
                if position.is_long {
                    entry.long_ntl += position.ntl;
                    entry.long_traders += 1;
                    entry.long_entry_amt_acc += position.entry_amt;
                    entry.long_entry_sz_acc += position.entry_sz;
                } else {
                    entry.short_ntl += position.ntl;
                    entry.short_traders += 1;
                    entry.short_entry_amt_acc += position.entry_amt;
                    entry.short_entry_sz_acc += position.entry_sz;
                }
            }
        }

        perps
            .into_iter()
            .map(|(perp_id, state)| (perp_id.clone(), state.to_perp_brief_state(perp_id)))
            .collect()
    }

    async fn process(&mut self, rmp: &PathBuf, last_height: &mut u64) -> Result<()> {
        let rmp = rmp.clone();
        let lh = *last_height;
        let msg = tokio::task::spawn_blocking(move || {
            // 1. decode rmp
            tracing::info!("decoding {}", rmp.display());
            let now = std::time::Instant::now();
            let file = File::open(&rmp)?;
            let md = file.metadata()?;
            tracing::info!(
                "file last modify: {}",
                chrono::DateTime::<chrono::Utc>::from(md.modified()?)
            );
            let mut cursor = std::io::BufReader::new(file);
            let value: rmpv::Value = rmpv::decode::read_value(&mut cursor)?;
            tracing::info!("decoded in {:?}", now.elapsed());

            // 2. parse state
            tracing::info!("parsing {}", rmp.display());
            let now = std::time::Instant::now();
            let state = State::from_rmpv_value(&value)?;
            tracing::info!("parsed in {:?}", now.elapsed());

            let latest = state.context.height;

            if latest == lh {
                tracing::info!("height {} is the same as last height, skipping", latest);
                return Ok(None);
            }

            // 3. aggregate
            // let accts = Self::aggregate_accounts(&state);
            // let perps = Self::aggregate_perps(&accts);
            let accts_by_perp = Self::aggregate_accounts_by_perp(&state);
            let perps = Self::aggregate_perps_with_top_users(&accts_by_perp);
            let mut perp_tops = HashMap::with_capacity(accts_by_perp.len());
            for (perp_name, CollectedPerp { users, px }) in accts_by_perp {
                let mut vs = Vec::with_capacity(users.len());
                for (user_addr, (account_value, pos)) in users {
                    let entry_px = crate::utils::calc_entry_px(
                        pos.entry_amt,
                        pos.entry_sz,
                        pos.sz_decimals,
                        USDC_DECIMALS,
                    );
                    vs.push(PerpTopUserState {
                        perp_name: perp_name.clone(),
                        user_addr,
                        ntl: (pos.ntl as f64) / (10_u64.pow(USDC_DECIMALS) as f64),
                        is_long: pos.is_long,
                        entry_px,
                        liq_px: None, // call hl later in processor
                        size: (pos.entry_sz as f64) / (10_u64.pow(pos.sz_decimals) as f64),
                        funding: pos.funding.map(|f| f as f64 / (10_u64.pow(USDC_DECIMALS) as f64)),
                        account_value: (account_value as f64) / (10_u64.pow(USDC_DECIMALS) as f64),
                        timestamp_millis: 0, // modify when persist
                    });
                }

                perp_tops.insert(perp_name, ByPerpInfo { px, top_users: vs });
            }

            let state = StateMessage { perp_tops, perps };
            Ok::<_, anyhow::Error>(Some((state, latest)))
        })
        .await??;

        if let Some((msg, latest)) = msg {
            // 4. persist async
            let mut remove = vec![];
            for (name, tx) in self.state_ch_async.iter() {
                tracing::info!("notifying async consumer {}", name);
                let (ctx, crx) = oneshot::channel();
                if tx.send((msg.clone(), ctx)).await.is_err() {
                    tracing::error!("async consumer {} disconnected, removing", name);
                    remove.push(name as &'static str);
                }
                if crx.await.is_err() {
                    tracing::error!("async consumer {} (confirm) disconnected, removing", name);
                    remove.push(name as &'static str);
                }
            }
            self.state_ch_async.retain(|(name, _)| !remove.contains(name));

            *last_height = latest;
        }

        Ok(())
    }

    pub async fn run(mut self) {
        tracing::info!("state_parser start");
        let mut last_height = 0;
        loop {
            match self.abci_state_rx.recv().await {
                Ok(rmp) => {
                    tracing::info!("processing rmp {}", rmp.display());

                    if let Err(e) = self.process(&rmp, &mut last_height).await {
                        tracing::error!("error processing rmp: {e}");
                    } else {
                        tracing::info!("processed rmp");
                    }
                }
                Err(_) => {
                    tracing::error!("channel disconnected");
                    break;
                }
            }
        }
        tracing::info!("state_parser stopped");
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_aggregate_accounts() {
        superstack_data::utils::setup_tracing();

        let rmp = PathBuf::from("src/types/samples/abci_state.rmp");
        let file = File::open(&rmp).unwrap();
        let mut cursor = std::io::BufReader::new(file);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();
        let state = State::from_rmpv_value(&value).unwrap();
        let accounts = StateParser::aggregate_accounts(&state);

        let write_file = std::fs::File::create("src/types/samples/account_values.json").unwrap();
        serde_json::to_writer_pretty(write_file, &accounts).unwrap();
    }

    #[tokio::test]
    async fn test_aggregate_accounts_by_perp() {
        superstack_data::utils::setup_tracing();

        let rmp = PathBuf::from("../../abci_state.rmp");
        let file = File::open(&rmp).unwrap();
        let mut cursor = std::io::BufReader::new(file);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();
        let state = State::from_rmpv_value(&value).unwrap();

        let accts_by_perp = StateParser::aggregate_accounts_by_perp(&state);
        for (perp_name, CollectedPerp { px, users }) in accts_by_perp.iter() {
            tracing::info!("perp: {} has {} users", perp_name, users.len());
            for (user_addr, (_, v)) in users.iter() {
                if v.leverage.is_none() {
                    tracing::warn!("{}: {} has position no leverage", perp_name, user_addr,);
                }
            }
        }
        let perps = StateParser::aggregate_perps_with_top_users(&accts_by_perp);
        tracing::info!("done {} perps", perps.len());
        if let Some(perp) = perps.get("BTC") {
            tracing::info!("{perp:?}");
        }
    }
}
