use anyhow::Result;
use std::{
    os::unix::fs::PermissionsExt,
    path::{Path, PathBuf},
};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct HlNodeCmd {
    path: PathBuf,
}

impl HlNodeCmd {
    pub async fn new(path: PathBuf) -> Result<Self> {
        // Check if path exists
        let metadata = tokio::fs::metadata(&path)
            .await
            .map_err(|_| anyhow::anyhow!("Path does not exist: {}", path.display()))?;

        // Check if path is a file
        if !metadata.is_file() {
            return Err(anyhow::anyhow!("Path is not a file: {}", path.display()));
        }

        // Check if file is executable
        let permissions = metadata.permissions();

        // Check if the file has execute permission (for owner, group, or others)
        if permissions.mode() & 0o111 == 0 {
            return Err(anyhow::anyhow!("File is not executable: {}", path.display()));
        }

        Ok(Self { path })
    }

    /// hl-node --chain Mainnet compute-l4-snapshots $input_rmp $output_snap_json
    pub async fn compute_l4_snapshots(
        &self,
        input_rmp: &Path,
        output_snap_json: &Path,
    ) -> Result<PathBuf> {
        // Validate input file exists
        if !input_rmp.exists() {
            return Err(anyhow::anyhow!("Input RMP file does not exist: {}", input_rmp.display()));
        }

        // Validate input file has .rmp extension
        if input_rmp.extension().and_then(|ext| ext.to_str()) != Some("rmp") {
            return Err(anyhow::anyhow!(
                "Input file must have .rmp extension: {}",
                input_rmp.display()
            ));
        }

        // Create output directory if it doesn't exist
        if let Some(parent) = output_snap_json.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        // Execute hl-node command
        let now = std::time::Instant::now();
        let output = tokio::process::Command::new(&self.path)
            .arg("--chain")
            .arg("Mainnet")
            .arg("compute-l4-snapshots")
            .arg(input_rmp)
            .arg(output_snap_json)
            .output()
            .await?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            let stdout = String::from_utf8_lossy(&output.stdout);
            return Err(anyhow::anyhow!(
                "hl-node command failed with status: {}\nstdout: {}\nstderr: {}",
                output.status,
                stdout,
                stderr
            ));
        }

        // Verify output file was created
        if !output_snap_json.exists() {
            return Err(anyhow::anyhow!(
                "Output file was not created: {}",
                output_snap_json.display()
            ));
        }

        tracing::info!(
            "Successfully computed L4 snapshots in {:?} : {} -> {}",
            now.elapsed(),
            input_rmp.display(),
            output_snap_json.display()
        );

        Ok(output_snap_json.to_path_buf())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_compute_l4_snapshots() {
        let hl_node_cmd = HlNodeCmd::new(PathBuf::from("../../bin/hl-node")).await.unwrap();
        let input_rmp = PathBuf::from("../../hl/data/periodic_abci_states/20250710/657670000.rmp");
        let output_snap_json =
            PathBuf::from("../../hl/data/periodic_abci_states/20250710/657670000.snap.json");
        let result = hl_node_cmd.compute_l4_snapshots(&input_rmp, &output_snap_json).await.unwrap();
        assert_eq!(result, output_snap_json);
    }
}
