use std::path::PathBuf;

use superstack_data::{redis::RedisClient, utils::setup_tracing};
use superstack_hypurr::{
    config::Config,
    parsers::Parsers,
    processors::Processors,
    watcher::{<PERSON>r<PERSON><PERSON><PERSON>, FileCmp, FileWatcher, SearchPolicy, Upstream},
};
use tokio::signal;
use tokio_util::sync::CancellationToken;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    setup_tracing();

    let config = Config::get();
    tracing::info!("config: {:?}", config);

    let redis_client = RedisClient::new(&config.redis_url).await.expect("redis");
    if let Err(e) = redis_client.create_perp_top_user_index().await {
        tracing::error!("Failed to create perp top user index: {}", e);
    }

    // watcher
    let data_dir = PathBuf::from(&config.hl_dir).join("data");
    let hl_data_dir = PathBuf::from(&config.hl_dir).join("hyperliquid_data");
    let rmp_watcher = FileWatcher::new(
        hl_data_dir.join("abci_state.rmp"),
        config.watch_interval_secs,
        10, // channel_capacity
    );

    // Create order_status watcher for {day}/{hour} structure
    let order_status_watcher = DirWatcher::new(
        data_dir.join("node_order_statuses_by_block").join("hourly"),
        config.watch_interval_secs,
        SearchPolicy::NonRecursive(vec![FileCmp::ByNumLike, FileCmp::ByNumLike]), // day, hour
        "".to_string(),
        10,
    );

    let cancel = CancellationToken::new();

    // Create components
    let upstream = Upstream::new(rmp_watcher, order_status_watcher, cancel.clone());
    let mut parsers = Parsers::new(&upstream, cancel.clone());
    let processors = Processors::new(&mut parsers, redis_client, cancel.clone()).await;

    // Start All components
    let upstream_handle = tokio::spawn(upstream.run());
    let parser_handle = tokio::spawn(parsers.run());
    let processors_handle = tokio::spawn(processors.run());

    // ctrl-c
    tokio::select! {
        _ = signal::ctrl_c() => {
            tracing::info!("ctrlc received, cancelling");
            cancel.cancel();
        }
    }

    tokio::try_join!(upstream_handle, parser_handle, processors_handle).expect("tokio::try_join");
}
