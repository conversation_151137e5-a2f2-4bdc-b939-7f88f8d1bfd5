use crate::{
    parsers::Parsers,
    processors::{books::BooksProcessor, leaderboard::LeaderboardProcessor},
};
use superstack_data::redis::RedisClient;
use tokio_util::sync::CancellationToken;

pub mod books;
pub mod leaderboard;
pub mod liquidation;
pub mod portfolio;

pub struct Processors {
    pub leaderboard: LeaderboardProcessor,
    pub books: BooksProcessor,
    cancel: CancellationToken,
}

impl Processors {
    pub async fn new(
        parsers: &mut Parsers,
        redis_client: RedisClient,
        cancel: CancellationToken,
    ) -> Self {
        // Subscribe to parsers data
        let state_persist_rx = parsers.state.subscribe("leaderboard_processor");

        // Subscribe to node status and state for books processor
        let node_status_rx = parsers.node_status.subscribe("books_processor").await;
        let state_books_rx = parsers.state.subscribe("books_processor_state");

        // Create processors with their receivers
        let leaderboard = LeaderboardProcessor::new(redis_client.clone(), state_persist_rx)
            .await
            .expect("failed to create leaderboard processor");
        let books = BooksProcessor::new(node_status_rx, state_books_rx, redis_client);

        Self { leaderboard, books, cancel }
    }

    /// Run all processors concurrently with centralized cancellation management
    pub async fn run(self) {
        let leaderboard_task = tokio::spawn(async move {
            self.leaderboard.run().await;
        });

        let books_task = tokio::spawn(async move {
            self.books.run().await;
        });

        // Store abort handles
        let leaderboard_abort = leaderboard_task.abort_handle();
        let books_abort = books_task.abort_handle();

        // Use select! to handle cancellation - centralized cancellation management
        tokio::select! {
            _ = self.cancel.cancelled() => {
                tracing::info!("processors cancelled, aborting tasks");
            }
            leaderboard_result = leaderboard_task => {
                if let Err(e) = leaderboard_result {
                    tracing::error!("leaderboard processor task failed: {e}");
                }
            }
            books_result = books_task => {
                if let Err(e) = books_result {
                    tracing::error!("books processor task failed: {e}");
                }
            }
        }

        leaderboard_abort.abort();
        books_abort.abort();
        tracing::info!("processors stopped");
    }
}
