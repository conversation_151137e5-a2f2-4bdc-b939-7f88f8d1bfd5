use alloy::primitives::map::HashSet;
use anyhow::Error;
use hyperliquid_rust_sdk::{InfoRequest, UserStateResponse};
use moka::future::Cache;
use serde::Deserialize;
use std::{collections::HashMap, sync::Arc, time::Duration};
use superstack_data::{
    postgres::indexer::{perp_state::PerpBriefState, perp_top_user_state::PerpTopUserState},
    redis::RedisClient,
};
use tokio::sync::{mpsc::Receiver, oneshot};

use crate::types::state::{position::Leverage, State};

const USDC_DECIMALS: u32 = 6;
const LEADERBOARD_USERS_PERCENT: u32 = 1;

#[derive(<PERSON>lone, Debug)]
pub struct BriefPosition {
    pub perp_name: String,
    pub ntl: u128,
    pub is_long: bool,
    pub entry_amt: u128,
    pub entry_sz: u128,
    pub sz_decimals: u32,
    pub leverage: Option<Leverage>,
    pub funding: Option<i64>,
}

impl BriefPosition {
    pub fn new(
        perp_name: String,
        ntl: u128,
        is_long: bool,
        entry_amt: u128,
        entry_sz: u128,
        sz_decimals: u32,
        leverage: Option<Leverage>,
        funding: Option<i64>,
    ) -> Self {
        Self { perp_name, ntl, is_long, entry_amt, entry_sz, sz_decimals, leverage, funding }
    }
}

// user -> (account_value, position_of_perp)
struct CollectedPerp {
    px: f64,
    users: HashMap<String, (u128, BriefPosition)>, // user -> (account_value, pos)
}

#[derive(Debug, Default)]
struct AggregatedPerpState {
    long_ntl: u128,
    short_ntl: u128,
    long_traders: u64,
    short_traders: u64,
    long_entry_amt_acc: u128,
    long_entry_sz_acc: u128,
    short_entry_amt_acc: u128,
    short_entry_sz_acc: u128,
    long_pnl_acc: i128,
    short_pnl_acc: i128,
    sz_decimals: u32,
}

impl AggregatedPerpState {
    pub fn to_perp_brief_state(self, perp_id: String) -> PerpBriefState {
        PerpBriefState {
            perp_id,
            long_ntl: self.long_ntl,
            short_ntl: self.short_ntl,
            long_sz: self.long_entry_sz_acc as f64 / 10_u64.pow(self.sz_decimals) as f64,
            short_sz: self.short_entry_sz_acc as f64 / 10_u64.pow(self.sz_decimals) as f64,
            long_traders: self.long_traders,
            short_traders: self.short_traders,
            long_entry: crate::utils::calc_entry_px(
                self.long_entry_amt_acc,
                self.long_entry_sz_acc,
                self.sz_decimals,
                USDC_DECIMALS,
            ),
            short_entry: crate::utils::calc_entry_px(
                self.short_entry_amt_acc,
                self.short_entry_sz_acc,
                self.sz_decimals,
                USDC_DECIMALS,
            ),
            long_pnl: self.long_pnl_acc as f64 / 10_u64.pow(USDC_DECIMALS) as f64,
            short_pnl: self.short_pnl_acc as f64 / 10_u64.pow(USDC_DECIMALS) as f64,
            long_liq_dist: 0.,    // update during post processing
            short_liq_dist: 0.,   // update during post processing
            updated_at_millis: 0, // modify when persist
        }
    }
}

#[derive(Clone)]
struct HlStateInner {
    hl_info_cli: reqwest::Client,
    hl_user_cache: Cache<String, Arc<UserStateResponse>>,
}

impl HlStateInner {
    async fn parse_hl_response(
        response: reqwest::Response,
    ) -> Result<String, hyperliquid_rust_sdk::Error> {
        use hyperliquid_rust_sdk::Error;
        #[derive(Deserialize, Debug)]
        struct ErrorData {
            data: String,
            code: u16,
            msg: String,
        }

        let status_code = response.status().as_u16();
        let text = response.text().await.map_err(|e| Error::GenericRequest(e.to_string()))?;

        if status_code < 400 {
            return Ok(text);
        }
        let error_data = serde_json::from_str::<ErrorData>(&text);
        if (400..500).contains(&status_code) {
            let client_error = match error_data {
                Ok(error_data) => Error::ClientRequest {
                    status_code,
                    error_code: Some(error_data.code),
                    error_message: error_data.msg,
                    error_data: Some(error_data.data),
                },
                Err(err) => Error::ClientRequest {
                    status_code,
                    error_message: text,
                    error_code: None,
                    error_data: Some(err.to_string()),
                },
            };
            return Err(client_error);
        }

        Err(Error::ServerRequest { status_code, error_message: text })
    }

    async fn get_user_hl_state(&self, user_addr: &str) -> Option<Arc<UserStateResponse>> {
        let address = user_addr.parse().ok()?;
        match self
            .hl_user_cache
            .try_get_with(user_addr.to_owned(), async {
                let url = format!("{}/info", crate::config::Config::get().hl_api);
                let resp = self
                    .hl_info_cli
                    .post(url)
                    .json(&InfoRequest::UserState { user: address })
                    .send()
                    .await?;

                let json = Self::parse_hl_response(resp).await?;
                let user_state = serde_json::from_str::<UserStateResponse>(&json)?;
                Ok::<_, anyhow::Error>(Arc::new(user_state))
            })
            .await
        {
            Ok(user_state) => Some(user_state),
            Err(e) => {
                tracing::error!("error getting user state for {}: {}", user_addr, e);
                None
            }
        }
    }
}

#[derive(Debug, Default)]
struct CollectedWithApi {
    long_liq_dist_acc: f64,
    long_liq_dist_count: usize,
    short_liq_dist_acc: f64,
    short_liq_dist_count: usize,
}

pub struct LeaderboardProcessor {
    redis_client: RedisClient,
    rx: Receiver<(Arc<State>, oneshot::Sender<()>)>,
    inner: HlStateInner,
}

impl LeaderboardProcessor {
    pub async fn new(
        redis_client: RedisClient,
        rx: Receiver<(Arc<State>, oneshot::Sender<()>)>,
    ) -> Result<Self, Error> {
        // todo: support custom url
        let hl_info_cli = reqwest::Client::builder().timeout(Duration::from_secs(1)).build()?;
        let hl_user_cache =
            Cache::builder().max_capacity(2048).time_to_live(Duration::from_secs(60 * 3)).build();
        let inner = HlStateInner { hl_info_cli, hl_user_cache };
        Ok(Self { redis_client, rx, inner })
    }

    // return map(perp_name -> map(user -> (account_value, position))) where users are tops of each
    // perp
    fn aggregate_accounts_by_perp(state: &State) -> HashMap<String, CollectedPerp> {
        // 1st pass: collect positions & ntl
        // map(perp_name ->  map(user -> (position)))
        let mut user_positions_by_perp: HashMap<String, HashMap<String, BriefPosition>> =
            HashMap::new();
        // map(perp_name -> map(user -> total_ntl))
        let mut users_ntl_by_perp: HashMap<String, HashMap<String, u128>> = HashMap::new();
        let mut users_account_value: HashMap<String, u128> = HashMap::new();
        let mut perps_px: HashMap<String, u64> = HashMap::new();

        for (user_addr, user_stat) in state.user_states.iter() {
            for position in user_stat.positions.iter() {
                let asset_idx = position.asset_idx;
                let perp_name = match state.meta.get_perp_name_by_idx(asset_idx) {
                    Some(name) => name,
                    None => {
                        tracing::error!("perp not found for asset_idx {asset_idx}");
                        continue;
                    }
                };
                let perp_px = match perps_px.get(perp_name) {
                    Some(px) => *px,
                    _ => match state.meta.get_perp_px_by_idx(asset_idx) {
                        Some(px) => {
                            perps_px.insert(perp_name.to_owned(), px);
                            px
                        }
                        None => {
                            tracing::error!("perp px not found for asset_idx {asset_idx}");
                            continue;
                        }
                    },
                };
                let sz_decimals = match state.meta.get_perp_sz_decimals_by_idx(asset_idx) {
                    Some(decimals) => decimals,
                    None => {
                        tracing::error!("perp sz decimals not found for asset_idx {asset_idx}");
                        continue;
                    }
                };
                let ntl = position.notional(perp_px);
                let ntl_abs = ntl.unsigned_abs();
                let funding = position.funding.clone().and_then(|f| f.since_open);

                // collect user position by perp
                let perp_entry = user_positions_by_perp.entry(perp_name.to_owned()).or_default();
                perp_entry.insert(
                    user_addr.to_string(),
                    BriefPosition::new(
                        perp_name.to_owned(),
                        ntl_abs,
                        ntl > 0,
                        position.entry_amount as _,
                        position.size.unsigned_abs() as _,
                        sz_decimals as _,
                        position.leverage.clone(),
                        funding,
                    ),
                );

                // collect users ntl by perp
                let user_entry = users_ntl_by_perp
                    .entry(perp_name.to_owned())
                    .or_default()
                    .entry(user_addr.to_string())
                    .or_default();
                *user_entry += ntl_abs;
            }
            let account_value =
                user_stat.account_value(&state.meta, &state.spot_meta, &state.spot_books);
            users_account_value.insert(user_addr.to_string(), account_value);
        }

        // 2nd pass: filter top users
        let top_users_by_perp = users_ntl_by_perp
            .into_iter()
            .map(|(perp_name, users)| {
                let mut users: Vec<(String, u128)> = users.into_iter().collect();
                let index =
                    (users.len() as f64 * LEADERBOARD_USERS_PERCENT as f64 / 100.0) as usize;
                users.select_nth_unstable_by_key(index, |v| std::cmp::Reverse(v.1));
                users.truncate(index);
                (
                    perp_name,
                    users.into_iter().map(|(user_addr, _)| user_addr).collect::<HashSet<_>>(),
                )
            })
            .collect::<HashMap<_, _>>();

        // 3. filter out non-top users from user_positions_by_perp
        for (perp_name, user_positions) in user_positions_by_perp.iter_mut() {
            if let Some(top_users) = top_users_by_perp.get(perp_name) {
                user_positions.retain(|user_addr, _| top_users.contains(user_addr));
            }
        }

        // 4. collect account_value and calculate price
        let mut res = HashMap::with_capacity(user_positions_by_perp.len());
        for (perp_name, user_positions) in user_positions_by_perp {
            let sz_decimals = state
                .meta
                .get_perp_idx(&perp_name)
                .and_then(|idx| state.meta.get_perp_sz_decimals_by_idx(idx))
                .unwrap_or(0);
            let perp_px = match perps_px.get(&perp_name) {
                Some(px) => crate::utils::convert_px(*px, sz_decimals as _, USDC_DECIMALS),
                None => {
                    tracing::error!("perp px not found for perp_name {perp_name}");
                    continue;
                }
            };

            // attach account_value to each user
            let collected_users = user_positions
                .into_iter()
                .map(|(user_addr, position)| {
                    // account_value or 0
                    let account_value = *users_account_value.get(&user_addr).unwrap_or(&0);
                    (user_addr, (account_value, position))
                })
                .collect::<HashMap<_, _>>();

            res.insert(perp_name, CollectedPerp { px: perp_px, users: collected_users });
        }

        res
    }

    fn aggregate_perps_with_top_users(
        top_users_by_perp: &HashMap<String, CollectedPerp>,
    ) -> HashMap<String, PerpBriefState> {
        let mut perps: HashMap<String, AggregatedPerpState> = HashMap::new();

        for (perp_name, user_pos) in top_users_by_perp.iter() {
            let perp_entry = perps.entry(perp_name.to_owned()).or_default();
            for (_, (_, position)) in user_pos.users.iter() {
                // set sz_decimals for perp
                perp_entry.sz_decimals = position.sz_decimals;

                if position.is_long {
                    perp_entry.long_ntl += position.ntl;
                    perp_entry.long_traders += 1;
                    perp_entry.long_entry_amt_acc += position.entry_amt;
                    perp_entry.long_entry_sz_acc += position.entry_sz;
                    perp_entry.long_pnl_acc += position.ntl as i128 - position.entry_amt as i128 +
                        position.funding.unwrap_or(0) as i128;
                } else {
                    perp_entry.short_ntl += position.ntl;
                    perp_entry.short_traders += 1;
                    perp_entry.short_entry_amt_acc += position.entry_amt;
                    perp_entry.short_entry_sz_acc += position.entry_sz;
                    perp_entry.short_pnl_acc += position.entry_amt as i128 - position.ntl as i128 +
                        position.funding.unwrap_or(0) as i128;
                }
            }
        }

        perps
            .into_iter()
            .map(|(perp_id, state)| (perp_id.clone(), state.to_perp_brief_state(perp_id)))
            .collect()
    }

    async fn process_state(&self, state: Arc<State>) -> Result<(), anyhow::Error> {
        let now = chrono::Utc::now().timestamp_millis();

        // Aggregate data
        let accts_by_perp = Self::aggregate_accounts_by_perp(&state);
        let perps = Self::aggregate_perps_with_top_users(&accts_by_perp);

        // Convert to top user states with prices
        let mut perp_tops_with_px = HashMap::with_capacity(accts_by_perp.len());
        for (perp_name, CollectedPerp { px, users }) in accts_by_perp {
            let mut vs = Vec::with_capacity(users.len());
            for (user_addr, (account_value, pos)) in users {
                let entry_px = crate::utils::calc_entry_px(
                    pos.entry_amt,
                    pos.entry_sz,
                    pos.sz_decimals,
                    USDC_DECIMALS,
                );
                vs.push(PerpTopUserState {
                    perp_name: perp_name.clone(),
                    user_addr,
                    ntl: (pos.ntl as f64) / (10_u64.pow(USDC_DECIMALS) as f64),
                    is_long: pos.is_long,
                    entry_px,
                    liq_px: None, // Will be populated by Hyperliquid API call
                    size: (pos.entry_sz as f64) / (10_u64.pow(pos.sz_decimals) as f64),
                    funding: pos.funding.map(|f| f as f64 / (10_u64.pow(USDC_DECIMALS) as f64)),
                    account_value: (account_value as f64) / (10_u64.pow(USDC_DECIMALS) as f64),
                    timestamp_millis: state.context.time.and_utc().timestamp_millis(),
                });
            }
            perp_tops_with_px.insert(perp_name, (px, vs));
        }

        // top users
        let redis = self.redis_client.clone();
        let inner = self.inner.clone();
        let top_users_task = tokio::spawn(async move {
            // collect liquidation distance info needed by perp
            let mut need_by_perp = HashMap::<String, CollectedWithApi>::new();

            for (perp_name, (px, top_users)) in perp_tops_with_px {
                let mut top_users_ok = vec![];
                for mut user_state in top_users {
                    // try get liq px
                    if let Some(hl) = inner.get_user_hl_state(&user_state.user_addr).await {
                        let liq_px = hl
                            .asset_positions
                            .iter()
                            .find(|pos| pos.position.coin == perp_name)
                            .and_then(|pos| pos.position.liquidation_px.as_ref())
                            .and_then(|s| s.parse::<f64>().ok());

                        if let Some(liq_px) = &liq_px {
                            let entry = need_by_perp.entry(perp_name.clone()).or_default();
                            if user_state.is_long {
                                entry.long_liq_dist_acc += (px - liq_px).abs() / px;
                                entry.long_liq_dist_count += 1;
                            } else {
                                entry.short_liq_dist_acc += (px - liq_px).abs() / px;
                                entry.short_liq_dist_count += 1;
                            }
                        }

                        user_state.liq_px = liq_px;
                    };

                    user_state.timestamp_millis = now;
                    if let Err(e) = redis.set_perp_top_user(&user_state).await {
                        tracing::error!("Failed to write top users: {}", e);
                    } else {
                        top_users_ok.push(user_state.user_addr);
                    }
                }
                if let Err(e) = redis.set_perp_top_user_ids(&perp_name, &top_users_ok).await {
                    tracing::error!("Failed to write top users ids: {}", e);
                } else {
                    tracing::info!(
                        "Wrote {} top users for perp: {}",
                        top_users_ok.len(),
                        perp_name
                    );
                }
            }
            need_by_perp
        });
        let need_by_perp = top_users_task.await?;

        // perps
        let redis = self.redis_client.clone();
        let perps_task = tokio::spawn(async move {
            let mut perps_ok = 0;
            for mut perp in perps.into_values() {
                if let Some(v) = need_by_perp.get(&perp.perp_id) {
                    perp.long_liq_dist = v.long_liq_dist_acc / v.long_liq_dist_count as f64;
                    perp.short_liq_dist = v.short_liq_dist_acc / v.short_liq_dist_count as f64;
                }
                perp.updated_at_millis = now;
                if let Err(e) = redis.set_perp_state(&perp).await {
                    tracing::error!("Failed to write perp state: {}", e);
                } else {
                    tracing::info!("Wrote perp state for token: {}", perp.perp_id);
                    perps_ok += 1;
                }
            }
            tracing::info!("Wrote {} perp states", perps_ok);
        });
        perps_task.await?;

        Ok(())
    }

    pub async fn run(mut self) {
        tracing::info!("leaderboard processor started");
        loop {
            match self.rx.recv().await {
                Some((state, confirm)) => {
                    tracing::info!("processing state at height {}", state.context.height);

                    if let Err(e) = self.process_state(state).await {
                        tracing::error!("Error processing state: {}", e);
                    }

                    if confirm.send(()).is_err() {
                        tracing::error!("Failed to send confirmation");
                    }
                }
                None => break,
            }
        }
        tracing::info!("leaderboard processor stopped");
    }
}
