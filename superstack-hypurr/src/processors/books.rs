use std::{sync::Arc, time::Duration};

use superstack_data::{self, redis::RedisClient};
use tokio::sync::{oneshot, RwLock};

use crate::{
    config::Config,
    parsers::AsyncRxConfirm,
    types::{
        block::{order_status::OrderStatusEvent, HyperBlock},
        state::{books::Books, meta::Meta, State},
    },
};

#[derive(Clone)]
pub struct SharedState {
    perp_books: Arc<RwLock<Books>>,
    meta: Arc<RwLock<Meta>>,
    latest_block: Arc<RwLock<u64>>,
    latest_timestamp: Arc<RwLock<i64>>,
    latest_state_block: Arc<RwLock<u64>>,
}

impl SharedState {
    fn new() -> Self {
        Self {
            perp_books: Arc::new(RwLock::new(Books::default())),
            meta: Arc::new(RwLock::new(Meta::default())),
            latest_block: Arc::new(RwLock::new(0)),
            latest_timestamp: Arc::new(RwLock::new(0)),
            latest_state_block: Arc::new(RwLock::new(0)),
        }
    }
}
pub struct BooksProcessor {
    shared_state: SharedState,
    node_status_rx: AsyncRxConfirm<HyperBlock<OrderStatusEvent>>,
    state_rx: AsyncRxConfirm<Arc<State>>,
    redis_client: RedisClient,
}

impl BooksProcessor {
    pub fn new(
        node_status_rx: AsyncRxConfirm<HyperBlock<OrderStatusEvent>>,
        state_rx: AsyncRxConfirm<Arc<State>>,
        redis_client: RedisClient,
    ) -> Self {
        Self { shared_state: SharedState::new(), node_status_rx, state_rx, redis_client }
    }

    pub async fn init(&mut self) -> anyhow::Result<()> {
        tracing::info!("Initializing books processor, waiting for initial state...");

        // Wait for initial state to get meta
        match self.state_rx.recv().await {
            Some((state, confirm)) => {
                tracing::info!("Received initial state at height: {}", state.context.height);

                // Update shared state with initial values
                {
                    *self.shared_state.perp_books.write().await = state.books.clone();
                    *self.shared_state.meta.write().await = state.meta.clone();
                    *self.shared_state.latest_timestamp.write().await =
                        state.context.time.and_utc().timestamp_millis();
                    *self.shared_state.latest_block.write().await = state.context.height;
                    *self.shared_state.latest_state_block.write().await = state.context.height;
                }

                // Send confirmation
                if confirm.send(()).is_err() {
                    tracing::warn!("Failed to send initial state confirmation");
                }

                tracing::info!("Books processor initialized successfully");
                Ok(())
            }
            None => {
                tracing::error!("State channel closed before receiving initial state");
                Err(anyhow::anyhow!("State channel closed before receiving initial state"))
            }
        }
    }

    pub async fn run(mut self) {
        tracing::info!("Books processor starting with multi-threaded architecture...");

        // Initialize first
        if let Err(e) = self.init().await {
            tracing::error!("Failed to initialize books processor: {}", e);
            return;
        }

        // Spawn the three independent threads
        let state_handle = Self::spawn_state_updater(self.shared_state.clone(), self.state_rx);
        let block_handle =
            Self::spawn_block_updater(self.shared_state.clone(), self.node_status_rx);
        let persistence_handle =
            Self::spawn_persistence_manager(self.shared_state.clone(), self.redis_client);

        // Wait for all threads to complete
        let _ = tokio::try_join!(state_handle, block_handle, persistence_handle);

        tracing::info!("Books processor stopped");
    }

    /// Spawn state update thread to handle state channel updates
    fn spawn_state_updater(
        shared_state: SharedState,
        mut state_rx: AsyncRxConfirm<Arc<State>>,
    ) -> tokio::task::JoinHandle<()> {
        tokio::spawn(async move {
            tracing::info!("State updater thread starting...");

            // Continue processing state updates (init was already handled)
            loop {
                match state_rx.recv().await {
                    Some((state, confirm)) => {
                        if let Err(e) =
                            Self::update_books_from_state(&shared_state, state, confirm).await
                        {
                            tracing::error!("Error updating books from state: {}", e);
                        }
                    }
                    None => {
                        tracing::error!("State channel closed");
                        break;
                    }
                }
            }

            tracing::info!("State updater thread stopped");
        })
    }

    /// Spawn block update thread to handle order status updates
    fn spawn_block_updater(
        shared_state: SharedState,
        mut node_status_rx: AsyncRxConfirm<HyperBlock<OrderStatusEvent>>,
    ) -> tokio::task::JoinHandle<()> {
        tokio::spawn(async move {
            tracing::info!("Block updater thread starting...");

            loop {
                match node_status_rx.recv().await {
                    Some((order_status_block, confirm)) => {
                        let latest_block = *shared_state.latest_block.read().await;

                        if order_status_block.block_number <= latest_block {
                            tracing::warn!(
                                "Skipping block {} because it's older than latest block {}",
                                order_status_block.block_number,
                                latest_block
                            );
                            // Still send confirmation
                            if confirm.send(()).is_err() {
                                tracing::warn!("Failed to send node status confirmation");
                            }
                        } else {
                            if order_status_block.block_number > latest_block + 1 {
                                tracing::warn!(
                                    "exceeded block number by more than 1 {} > {}",
                                    order_status_block.block_number,
                                    latest_block
                                );
                            }
                            if let Err(e) = Self::update_books_from_block(
                                &shared_state,
                                &order_status_block,
                                confirm,
                            )
                            .await
                            {
                                tracing::error!("Error updating books from block: {}", e);
                            }
                        }
                    }
                    None => {
                        tracing::error!("Node status channel closed");
                        break;
                    }
                }
            }

            tracing::info!("Block updater thread stopped");
        })
    }

    /// Spawn persistence thread to periodically save books data
    fn spawn_persistence_manager(
        shared_state: SharedState,
        redis_client: RedisClient,
    ) -> tokio::task::JoinHandle<()> {
        tokio::spawn(async move {
            tracing::info!("Persistence manager thread starting...");

            let mut persist_interval = tokio::time::interval(Duration::from_secs(1));
            let mut persist_block_number = 0;

            loop {
                persist_interval.tick().await;

                let current_block = *shared_state.latest_block.read().await;

                if persist_block_number != current_block {
                    // Read all necessary data
                    let books = shared_state.perp_books.read().await.clone();
                    let meta = shared_state.meta.read().await;
                    let timestamp = *shared_state.latest_timestamp.read().await;

                    Self::handle_persist_books(
                        redis_client.clone(),
                        &books,
                        &meta,
                        timestamp,
                        current_block,
                    );
                    persist_block_number = current_block;
                }
            }
        })
    }

    /// Update books from state data
    async fn update_books_from_state(
        shared_state: &SharedState,
        state: Arc<State>,
        confirm: oneshot::Sender<()>,
    ) -> Result<(), anyhow::Error> {
        tracing::info!("Updating books from new state at height: {}", state.context.height);

        // Update state
        {
            {
                let old_books = shared_state.perp_books.read().await;
                let (not_equal_cnt, equal_cnt) = state.books.is_equal(&old_books)?;
                tracing::info!("Books equal: {}, not equal: {}", equal_cnt, not_equal_cnt);
            }
            *shared_state.perp_books.write().await = state.books.clone();
            *shared_state.meta.write().await = state.meta.clone();
            *shared_state.latest_timestamp.write().await =
                state.context.time.and_utc().timestamp_millis();
            *shared_state.latest_block.write().await = state.context.height;
            *shared_state.latest_state_block.write().await = state.context.height;
        }

        tracing::info!("updated state with height: {}", state.context.height);

        if confirm.send(()).is_err() {
            tracing::warn!("Failed to send state confirmation");
        }

        Ok(())
    }

    /// Update books from block order status events
    async fn update_books_from_block(
        shared_state: &SharedState,
        order_status_block: &HyperBlock<OrderStatusEvent>,
        confirm: oneshot::Sender<()>,
    ) -> Result<(), anyhow::Error> {
        tracing::debug!(
            "Received order status block: {} with {} events",
            order_status_block.block_number,
            order_status_block.events.len()
        );

        // Update latest block number
        {
            let mut block = shared_state.latest_block.write().await;
            *block = order_status_block.block_number;
        }

        // Apply order status events to update the books in real-time
        {
            let mut books = shared_state.perp_books.write().await;
            let meta = shared_state.meta.read().await;
            if let Err(e) = books.apply_order_status(order_status_block, &meta) {
                tracing::error!("Failed to apply order status: {}", e);
            }
        }

        let interval = Config::get().state_save_interval_blocks;

        // Send confirmation
        if order_status_block.block_number % interval == 0 {
            loop {
                {
                    let state_block = shared_state.latest_state_block.read().await;
                    if *state_block >= order_status_block.block_number {
                        break;
                    }
                    tracing::info!(
                        "Waiting for state to catch up to block {}",
                        order_status_block.block_number,
                    );
                }
                tokio::time::sleep(Duration::from_secs(10)).await;
            }
        }

        // Send confirmation
        if confirm.send(()).is_err() {
            tracing::warn!("Failed to send node status confirmation");
        }

        Ok(())
    }

    /// Spawn a task to persist books data
    fn handle_persist_books(
        redis_client: RedisClient,
        books: &Books,
        meta: &Meta,
        timestamp: i64,
        block_number: u64,
    ) {
        let converted_books = Books::convert_books_to_perp_books(books, timestamp, meta);
        tokio::spawn(async move {
            let books_count = converted_books.len();

            // // Write to file for debugging/backup
            // let file_path = format!("books_{}.json", block_number);
            // if let Ok(file) = std::fs::File::create(&file_path) {
            //     if let Err(e) = serde_json::to_writer_pretty(file, &converted_books) {
            //         tracing::error!("Failed to write books to file {}: {}", file_path, e);
            //     }
            // }

            // TODO: Uncomment when Redis persistence is needed
            for books in converted_books {
                if let Err(e) = redis_client.set_perp_books(&books).await {
                    tracing::error!(
                        "Failed to write perp books for asset {}: {}",
                        books.perp_id,
                        e
                    );
                }
            }

            tracing::info!("Persisted books for {} assets at block: {}", books_count, block_number);
        });
    }
}
