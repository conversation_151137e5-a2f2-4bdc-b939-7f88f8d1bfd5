use serde::{Deserialize, Serialize};

use crate::utils::find_value;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct Spot {
    pub token_id: u64,
    pub balance: u64,
    pub entry_ntl: Option<u64>,
}

impl Spot {
    pub fn notional(&self, oracle_px: u64, wei_decimal: u8, sz_decimal: u8) -> u128 {
        if self.token_id != 0 {
            let div = 10u128.pow(wei_decimal as u32) * 10u128.pow(8 - sz_decimal as u32);
            let mul = 10u128.pow(6);

            self.balance as u128 * oracle_px as u128 * mul / div
        } else {
            self.balance as u128 / 10u128.pow(2) // 8 -> 6
        }
    }

    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let item = value.as_array().ok_or(anyhow::anyhow!("invalid spot"))?;

        let token_id = item[0].as_u64().ok_or(anyhow::anyhow!("invalid token_id"))?;
        let balance = find_value(&item[1], "t").and_then(|v| v.as_u64()).unwrap_or(0);
        let entry_ntl = find_value(&item[1], "e").and_then(|v| v.as_u64());

        Ok(Self { token_id, balance, entry_ntl })
    }
}
