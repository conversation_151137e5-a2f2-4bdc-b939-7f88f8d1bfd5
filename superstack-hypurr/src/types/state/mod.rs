pub mod books;
pub mod meta;
pub mod position;
pub mod spot;
pub mod spot_books;
pub mod spot_meta;
pub mod user;

use serde::{Deserialize, Serialize};

use self::{
    books::Books, meta::Meta, spot_books::SpotBooks, spot_meta::SpotMeta, user::UserStates,
};
use crate::utils::find_value;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct State {
    pub context: StateCtx,

    // Perps
    pub meta: Meta,
    pub books: Books,

    // Spot
    pub spot_meta: SpotMeta,
    pub spot_books: SpotBooks,

    // User
    pub user_states: UserStates,
}

impl State {
    pub fn from_rmpv_value(abci_state_rmp: &rmpv::Value) -> anyhow::Result<Self> {
        let exchange =
            find_value(abci_state_rmp, "exchange").ok_or(anyhow::anyhow!("has no exchange"))?;

        // exchange level
        let context = find_value(exchange, "context")
            .and_then(|v| StateCtx::from_rmpv_value(v).ok())
            .ok_or(anyhow::anyhow!("invalid context"))?;
        let all_user_states =
            find_value(exchange, "user_states").ok_or(anyhow::anyhow!("has no user_states"))?;
        let spot_books =
            find_value(exchange, "spot_books").ok_or(anyhow::anyhow!("has no spot_books"))?;

        // spot level
        let spot_clearinghouse = find_value(exchange, "spot_clearinghouse")
            .ok_or(anyhow::anyhow!("has no spot_clearinghouse"))?;
        let spot_meta =
            find_value(spot_clearinghouse, "meta").ok_or(anyhow::anyhow!("has no spot_meta"))?;
        let spot_meta = SpotMeta::from_rmpv_value(spot_meta)?;
        let spot_books = SpotBooks::from_rmpv_value(spot_books)?;

        // perps level
        let perp_dexs = &find_value(exchange, "perp_dexs")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("has no perp_dexs"))?[0];
        let books = find_value(perp_dexs, "books")
            .and_then(|v| Books::from_rmpv_value(v).ok())
            .ok_or(anyhow::anyhow!("has no books"))?;

        // perps clearinghouse level
        let clearinghouse = find_value(perp_dexs, "clearinghouse")
            .ok_or(anyhow::anyhow!("has no clearinghouse"))?;
        let perps_user_states = find_value(clearinghouse, "user_states")
            .ok_or(anyhow::anyhow!("has no perps_user_states"))?;
        let perps_meta =
            find_value(clearinghouse, "meta").ok_or(anyhow::anyhow!("has no perps_meta"))?;
        let perps_oracle =
            find_value(clearinghouse, "oracle").ok_or(anyhow::anyhow!("has no perps_oracle"))?;

        let meta = Meta::from_rmpv_value(perps_meta, perps_oracle)?;
        let user_states =
            UserStates::from_rmpv_value(perps_user_states, spot_clearinghouse, all_user_states)?;

        Ok(Self { context, meta, user_states, books, spot_meta, spot_books })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateCtx {
    pub height: u64,
    pub initial_height: u64,
    pub time: chrono::NaiveDateTime,
}

impl StateCtx {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let height = find_value(value, "height")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid height"))?;

        let initial_height = find_value(value, "initial_height")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid initial_height"))?;

        let time_str = find_value(value, "time")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid time"))?;

        let naive_dt = chrono::NaiveDateTime::parse_from_str(time_str, "%Y-%m-%dT%H:%M:%S%.f")?;

        Ok(Self { height, initial_height, time: naive_dt })
    }
}

#[cfg(test)]
mod tests {
    use crate::{
        types::state::{State, StateCtx},
        utils::find_value,
    };
    use std::io::Read;

    #[test]
    fn test_state_ctx_from_rmpv_value() {
        let path = "src/types/samples/hyperliquid_data/abci_state.rmp";

        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let exchange = find_value(&value, "exchange").unwrap();

        let ctx_value = find_value(exchange, "context").unwrap();
        println!("{:#?}", ctx_value);

        let ctx = StateCtx::from_rmpv_value(ctx_value).unwrap();

        println!("{:?}", ctx);
    }

    #[test]
    fn test_state_from_rmpv_value() {
        let path = "src/types/samples/hyperliquid_data/abci_state.rmp";

        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let state = State::from_rmpv_value(&value).unwrap();

        println!("{}", state.user_states.len());
    }
}
