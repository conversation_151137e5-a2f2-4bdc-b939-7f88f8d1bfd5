use std::{collections::HashMap, str::FromStr};

use alloy::primitives::Address;
use bigdecimal::{BigDecimal, ToPrimitive};
use serde::{Deserialize, Serialize};
use superstack_data::postgres::{
    indexer::perp_books::{PerpB<PERSON>, PerpBooks},
    PerpExchange,
};

use crate::{
    types::{
        block::{order_status::OrderStatusEvent, HyperBlock},
        state::meta::Meta,
    },
    utils::find_value,
};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct Books(Vec<(u16, BookOrders)>);

impl Books {
    /// Convert Books to Vec<PerpBooks> for persistence
    pub fn convert_books_to_perp_books(
        books: &Books,
        timestamp: i64,
        meta: &Meta,
    ) -> Vec<PerpBooks> {
        let mut results = Vec::new();

        for (asset_idx, universe_item) in meta.universe.iter().enumerate() {
            if let Some(book_orders) = books.get_book_orders(asset_idx as u16) {
                let mut bid_buckets: HashMap<u64, (u64, u64)> = HashMap::new();
                let mut ask_buckets: HashMap<u64, (u64, u64)> = HashMap::new();
                let sz_decimals = universe_item.sz_decimals as u8;
                let mut total_sz = 0;

                // Aggregate bids by price level
                for entry in book_orders.bids.values() {
                    let bucket = bid_buckets.entry(entry.limit_px).or_insert((0, 0));
                    bucket.0 += entry.size;
                    bucket.1 += 1;
                    total_sz += entry.size;
                }

                // Aggregate asks by price level
                for entry in book_orders.asks.values() {
                    let bucket = ask_buckets.entry(entry.limit_px).or_insert((0, 0));
                    bucket.0 += entry.size;
                    bucket.1 += 1;
                    total_sz += entry.size;
                }

                // Convert to PerpBook format and sort
                let mut bid_books: Vec<PerpBook> = bid_buckets
                    .into_iter()
                    .map(|(limit_px, (total_sz, order_count))| PerpBook {
                        px: BookEntry::get_ui_price(limit_px, sz_decimals),
                        sz: BookEntry::get_ui_size(total_sz, sz_decimals),
                        n: order_count,
                    })
                    .collect();

                let mut ask_books: Vec<PerpBook> = ask_buckets
                    .into_iter()
                    .map(|(limit_px, (total_sz, order_count))| PerpBook {
                        px: BookEntry::get_ui_price(limit_px, sz_decimals),
                        sz: BookEntry::get_ui_size(total_sz, sz_decimals),
                        n: order_count,
                    })
                    .collect();

                // Sort by price (bids descending, asks ascending)
                bid_books
                    .sort_by(|a, b| b.px.partial_cmp(&a.px).unwrap_or(std::cmp::Ordering::Equal));
                ask_books
                    .sort_by(|a, b| a.px.partial_cmp(&b.px).unwrap_or(std::cmp::Ordering::Equal));

                let total_sz = BookEntry::get_ui_size(total_sz, sz_decimals);

                results.push(PerpBooks {
                    perp_id: universe_item.name.clone(),
                    perp_exchange: PerpExchange::Hyperliquid,
                    updated_at_millis: timestamp,
                    total_sz,
                    books: [bid_books, ask_books],
                });
            }
        }

        results
    }

    pub fn is_equal(&self, other: &Books) -> anyhow::Result<(u64, u64)> {
        let mut is_not_equal_cnt = 0;
        let mut equal_cnt = 0;
        for (idx, book_orders) in self.0.iter() {
            let other_book_orders = other
                .get_book_orders(*idx)
                .ok_or(anyhow::anyhow!("not same because of missing book_orders {idx}"))?;

            for (oid, entry) in book_orders.asks.iter() {
                match other_book_orders.asks.get(oid) {
                    Some(other_entry) => {
                        if entry != other_entry {
                            is_not_equal_cnt += 1;
                        } else {
                            equal_cnt += 1;
                        }
                    }
                    None => {
                        is_not_equal_cnt += 1;
                    }
                }
            }

            for (oid, entry) in book_orders.bids.iter() {
                match other_book_orders.bids.get(oid) {
                    Some(other_entry) => {
                        if entry != other_entry {
                            is_not_equal_cnt += 1;
                        } else {
                            equal_cnt += 1;
                        }
                    }
                    None => {
                        is_not_equal_cnt += 1;
                    }
                }
            }
        }

        Ok((is_not_equal_cnt, equal_cnt))
    }

    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let books_values = value.as_array().ok_or(anyhow::anyhow!("invalid books"))?;

        let mut books = Vec::new();
        for item in books_values {
            let asset_idx = find_value(item, "asset")
                .and_then(|v| v.as_u64())
                .ok_or(anyhow::anyhow!("invalid asset_idx"))? as u16;
            let book_orders_value =
                find_value(item, "book_orders").ok_or(anyhow::anyhow!("invalid book_orders"))?;
            let book_orders = BookOrders::from_rmpv_value(book_orders_value)?;

            books.push((asset_idx, book_orders));
        }

        Ok(Self(books))
    }

    pub fn get_book_orders(&self, asset_idx: u16) -> Option<&BookOrders> {
        self.0.iter().find(|(idx, _)| *idx == asset_idx).map(|(_, book_orders)| book_orders)
    }

    pub fn get_book_orders_mut(&mut self, asset_idx: u16) -> Option<&mut BookOrders> {
        self.0.iter_mut().find(|(idx, _)| *idx == asset_idx).map(|(_, book_orders)| book_orders)
    }

    pub fn apply_order_status(
        &mut self,
        order_status_block: &HyperBlock<OrderStatusEvent>,
        meta: &Meta,
    ) -> anyhow::Result<()> {
        for event in order_status_block.events.iter() {
            let order = &event.order;
            let asset_idx = match meta.get_perp_idx(&order.coin) {
                Some(idx) => idx as u16,
                None => continue,
            };
            let sz_decimal = match meta.get_perp_sz_decimals_by_idx(asset_idx as u64) {
                Some(sz_decimal) => sz_decimal,
                None => continue,
            };

            match event.status.as_str() {
                "filled" => {
                    if let Some(book_orders) = self.get_book_orders_mut(asset_idx) {
                        if order.sz == BigDecimal::from(0) {
                            book_orders.remove_book_entry(order.oid);
                        } else {
                            let orig_sz =
                                &order.orig_sz * BigDecimal::from(10u64.pow(sz_decimal as u32));
                            let new_sz = &order.sz * BigDecimal::from(10u64.pow(sz_decimal as u32));
                            let orig_sz =
                                orig_sz.to_u64().ok_or(anyhow::anyhow!("orig_sz overflow"))?;
                            let new_sz =
                                new_sz.to_u64().ok_or(anyhow::anyhow!("new_sz overflow"))?;
                            book_orders.update_book_entry(order.oid, orig_sz, new_sz);
                        }
                    }
                }
                "open" => {
                    if let Some(book_orders) = self.get_book_orders_mut(asset_idx) {
                        let book_entry = event.try_into_book_entry(meta)?;
                        book_orders.insert_book_entry(book_entry);
                    }
                }
                "canceled" => {
                    if let Some(book_orders) = self.get_book_orders_mut(asset_idx) {
                        book_orders.remove_book_entry(order.oid);
                    }
                }
                _ => {}
            }
        }
        Ok(())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BookEntry {
    /// order id
    pub oid: u64,
    /// size of the order
    pub size: u64,
    /// original size of the order, !!!if not filled, this will be the same as size
    pub orig_size: u64,
    /// limit price of the order
    pub limit_px: u64,
    /// user who placed the order
    pub user: Address,
    /// direction of the order
    pub dir: BookDirection,
    /// time in force of the order
    pub tif: Option<String>,
    /// timestamp of the order
    pub ts: u64,
    /// child order id
    pub child_oid: Option<String>,
}

impl PartialEq for BookEntry {
    fn eq(&self, other: &Self) -> bool {
        self.oid == other.oid &&
            self.size == other.size &&
            self.orig_size == other.orig_size &&
            self.limit_px == other.limit_px &&
            self.dir == other.dir &&
            self.ts == other.ts
    }
}

impl Eq for BookEntry {}

impl BookEntry {
    pub fn get_ui_price(limit_px: u64, sz_decimals: u8) -> f64 {
        let first = limit_px as f64;
        let div = 10u64.pow(6 - sz_decimals as u32) as f64;
        first / div
    }

    pub fn get_ui_size(size: u64, sz_decimals: u8) -> f64 {
        size as f64 / 10u64.pow(sz_decimals as u32) as f64
    }

    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let oid = find_value(value, "o")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid oid"))?;
        let size = find_value(value, "r")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid size"))?;
        let user = find_value(value, "u")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid user"))?;
        let user = Address::from_str(user).map_err(|e| anyhow::anyhow!("invalid user: {}", e))?;
        let tif = find_value(value, "tif").and_then(|v| v.as_str()).map(|v| v.to_string());

        let current = find_value(value, "c").ok_or(anyhow::anyhow!("invalid current"))?;

        let orig_size = find_value(current, "S")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid orig_size"))?;

        let limit_px = find_value(current, "l")
            .and_then(|v| v.as_array().and_then(|a| a.first().and_then(|v| v.as_u64())))
            .ok_or(anyhow::anyhow!("invalid limit"))?;

        let dir = find_value(current, "s")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid dir"))?;

        let dir = match dir {
            "A" => BookDirection::Ask,
            "B" => BookDirection::Bid,
            _ => anyhow::bail!("invalid dir: {}", dir),
        };

        let ts = find_value(current, "t")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid ts"))?;

        let child_oid = find_value(current, "c").and_then(|v| v.as_str()).map(|v| v.to_string());

        Ok(Self { oid, size, orig_size, limit_px, user, dir, tif, ts, child_oid })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BookOrders {
    /// oid -> entry
    pub asks: HashMap<u64, BookEntry>,
    /// oid -> entry
    pub bids: HashMap<u64, BookEntry>,
}

impl BookOrders {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let book_orders = value.as_map().ok_or(anyhow::anyhow!("invalid book_orders"))?;

        let mut asks = HashMap::new();
        let mut bids = HashMap::new();

        for (_, entry) in book_orders {
            let book_entry = BookEntry::from_rmpv_value(entry)?;
            let oid = book_entry.oid;
            let dir = book_entry.dir;

            match dir {
                BookDirection::Ask => {
                    asks.insert(oid, book_entry);
                }
                BookDirection::Bid => {
                    bids.insert(oid, book_entry);
                }
            }
        }

        Ok(Self { asks, bids })
    }

    pub fn get_book_entry(&self, oid: u64) -> Option<&BookEntry> {
        self.asks.get(&oid).or_else(|| self.bids.get(&oid))
    }

    pub fn get_book_entry_mut(&mut self, oid: u64) -> Option<&mut BookEntry> {
        self.asks.get_mut(&oid).or_else(|| self.bids.get_mut(&oid))
    }

    pub fn insert_book_entry(&mut self, book_entry: BookEntry) {
        match book_entry.dir {
            BookDirection::Ask => {
                self.asks.insert(book_entry.oid, book_entry);
            }
            BookDirection::Bid => {
                self.bids.insert(book_entry.oid, book_entry);
            }
        }
    }

    pub fn remove_book_entry(&mut self, oid: u64) {
        self.asks.remove(&oid);
        self.bids.remove(&oid);
    }

    pub fn update_book_entry(&mut self, oid: u64, orig_sz: u64, new_sz: u64) {
        if let Some(book_entry) = self.get_book_entry_mut(oid) {
            book_entry.orig_size = orig_sz;
            book_entry.size = new_sz;
        }
    }
}

#[derive(Debug, Copy, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum BookDirection {
    Ask,
    Bid,
}

#[cfg(test)]
mod tests {
    use std::io::Read;

    use super::*;

    #[test]
    fn test_books_from_rmpv_value() {
        let path = "examples/abci_state.2.rmp";
        //0xd2075830d96b6fe8d41d1620c38106c8c29e4b84
        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let exchange = find_value(&value, "exchange").unwrap();
        let perp_dexs = &find_value(exchange, "perp_dexs").and_then(|v| v.as_array()).unwrap()[0];

        let books = find_value(perp_dexs, "books").unwrap();
        let books = Books::from_rmpv_value(books).unwrap();

        let write_file = std::fs::File::create("src/types/samples/books.json").unwrap();
        serde_json::to_writer_pretty(write_file, &books).unwrap();
    }
}
