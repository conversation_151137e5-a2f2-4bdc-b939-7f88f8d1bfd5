use serde::{Deserialize, Serialize};

use crate::utils::find_value;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SpotBooks {
    /// (asset_idx, last_price)
    pub last_prices: Vec<(u64, u64)>,
}

impl SpotBooks {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let spots = value.as_array().ok_or(anyhow::anyhow!("spots is not an array"))?;

        let mut last_pxs = Vec::new();
        for spot in spots {
            let asset = find_value(spot, "asset")
                .and_then(|v| v.as_u64())
                .ok_or(anyhow::anyhow!("asset is not a number"))?;
            let last_price = find_value(spot, "last_trade_px")
                .and_then(|v| v.as_array().and_then(|a| a[0].as_u64()))
                .unwrap_or(0);

            last_pxs.push((asset, last_price));
        }

        Ok(Self { last_prices: last_pxs })
    }

    pub fn get_last_price(&self, spot_idx: u64) -> u64 {
        self.last_prices.iter().find(|(s, _)| *s == spot_idx).map(|(_, p)| *p).unwrap_or(0)
    }
}

#[cfg(test)]
mod tests {
    use crate::{types::state::spot_books::SpotBooks, utils::find_value};
    use std::io::Read;

    #[test]
    fn test_from_rmpv_value() {
        let path = "src/types/samples/abci_state.rmp";

        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let exchange = find_value(&value, "exchange").unwrap();
        let spot_books = find_value(exchange, "spot_books").unwrap();

        let spot_books = SpotBooks::from_rmpv_value(spot_books).unwrap();

        let write_file = std::fs::File::create("src/types/samples/spot_books.json").unwrap();
        serde_json::to_writer_pretty(write_file, &spot_books).unwrap();
    }
}
