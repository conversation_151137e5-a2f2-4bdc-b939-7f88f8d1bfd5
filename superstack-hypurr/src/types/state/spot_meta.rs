use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};

use crate::utils::find_value;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct SpotMeta {
    pub universe: Vec<SpotUniverse>,
    pub tokens: Vec<SpotToken>,
    pub quote_tokens: Vec<u64>,
}

impl SpotMeta {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let universes = find_value(value, "spot_infos")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid spot_infos"))?;
        let tokens = find_value(value, "token_infos")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid token_infos"))?;

        let quote_tokens = find_value(value, "quote_tokens")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid quote_tokens"))?;
        let quote_tokens = quote_tokens.iter().map(|v| v.as_u64().unwrap()).collect::<Vec<_>>();

        let mut us = vec![];
        for universe in universes {
            let universe = SpotUniverse::from_rmpv_value(universe)?;
            us.push(universe);
        }

        let mut ts = vec![];
        for token in tokens {
            let token = SpotToken::from_rmpv_value(token)?;
            ts.push(token);
        }

        Ok(Self { universe: us, tokens: ts, quote_tokens })
    }

    pub fn get_spot_idx_by_token_id(&self, token_idx: usize) -> u64 {
        let token = &self.tokens[token_idx];

        token.spots[0]
    }

    pub fn get_spot_sz_decimals_by_token_id(&self, token_idx: usize) -> u8 {
        let token = &self.tokens[token_idx];
        token.sz_decimals
    }

    pub fn get_spot_wei_decimals_by_token_id(&self, token_idx: usize) -> u8 {
        let token = &self.tokens[token_idx];
        token.wei_decimals
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpotUniverse {
    /// deploy time
    pub deploy_time: NaiveDateTime,
    /// Spot id, like "@188"
    pub name: String,
    /// token id pairs
    pub tokens: (u64, u64),
}

impl SpotUniverse {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let deploy_time = find_value(value, "deploy_time")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid deploy_time"))?;

        let deploy_time =
            chrono::NaiveDateTime::parse_from_str(deploy_time, "%Y-%m-%dT%H:%M:%S%.f")?;

        let name = find_value(value, "name")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid name"))?
            .to_string();

        let tokens = find_value(value, "tokens")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid tokens"))?;

        let tokens = tokens.iter().map(|v| v.as_u64().unwrap()).collect::<Vec<_>>();

        Ok(Self { deploy_time, name, tokens: (tokens[0], tokens[1]) })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpotToken {
    pub name: String,
    pub full_name: Option<String>,
    pub sz_decimals: u8,
    pub wei_decimals: u8,

    // Deploy
    pub deploy_time: Option<NaiveDateTime>,
    pub deployer: Option<String>,
    pub deployer_trading_fee_share: String,
    pub evm_contract: Option<EvmContract>,

    // Spots
    pub spots: Vec<u64>,
}

impl SpotToken {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let deploy_time = find_value(value, "deploy_time")
            .and_then(|v| v.as_str())
            .and_then(|v| chrono::NaiveDateTime::parse_from_str(v, "%Y-%m-%dT%H:%M:%S%.f").ok());
        let deployer =
            find_value(value, "deployer").and_then(|v| v.as_str()).map(|v| v.to_string());
        let deployer_trading_fee_share = find_value(value, "deployer_trading_fee_share")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid deployer_trading_fee_share"))?
            .to_string();

        let spots = find_value(value, "spots")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid spots"))?;
        let spots = spots.iter().map(|v| v.as_u64().unwrap()).collect::<Vec<_>>();

        let full_name =
            find_value(value, "full_name").and_then(|v| v.as_str()).map(|v| v.to_string());

        // spec
        let spec = find_value(value, "spec").ok_or(anyhow::anyhow!("invalid spec"))?;
        let name = find_value(spec, "name")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid name"))?
            .to_string();
        let sz_decimals = find_value(spec, "szDecimals")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid sz_decimals"))?;
        let wei_decimals = find_value(spec, "weiDecimals")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid wei_decimals"))?;

        // evm_contract
        let evm_contract = match find_value(value, "evm_contract") {
            Some(v) if !v.is_nil() => {
                let address = find_value(v, "address")
                    .and_then(|v| v.as_str())
                    .ok_or(anyhow::anyhow!("invalid address"))?
                    .to_string();
                let evm_extra_wei_decimals = find_value(v, "evm_extra_wei_decimals")
                    .and_then(|v| v.as_i64())
                    .ok_or(anyhow::anyhow!("invalid evm_extra_wei_decimals"))?;

                Some(EvmContract { address, evm_extra_wei_decimals: evm_extra_wei_decimals as i8 })
            }
            _ => None,
        };

        Ok(Self {
            name,
            full_name,
            sz_decimals: sz_decimals as u8,
            wei_decimals: wei_decimals as u8,
            deploy_time,
            deployer,
            deployer_trading_fee_share,
            evm_contract,
            spots,
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct EvmContract {
    pub address: String,
    pub evm_extra_wei_decimals: i8,
}

#[cfg(test)]
mod tests {
    use std::io::Read;

    use super::*;

    #[test]
    fn test_spot_meta_from_rmpv_value() {
        let path = "src/types/samples/state.rmp";

        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let exchange = find_value(&value, "exchange").unwrap();
        let spot_clearinghouse = find_value(exchange, "spot_clearinghouse").unwrap();
        let spot_meta = find_value(spot_clearinghouse, "meta").unwrap();
        let spot_meta = SpotMeta::from_rmpv_value(spot_meta).unwrap();

        let write_file = std::fs::File::create("src/types/samples/spot_meta.json").unwrap();
        serde_json::to_writer_pretty(write_file, &spot_meta).unwrap();
    }

    #[test]
    fn test_u64_max() {
        let max = u64::MAX;

        let in_million = max / 10u64.pow(6) / 1000000;

        println!("in_million: {}M", in_million);
    }
}
