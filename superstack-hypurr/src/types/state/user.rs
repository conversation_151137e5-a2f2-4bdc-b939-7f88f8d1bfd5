use std::{collections::HashMap, ops::Deref, str::FromStr};

use alloy::primitives::Address;
use serde::{Deserialize, Serialize};

use crate::{
    types::state::{
        meta::Meta,
        position::{Leverage, Position},
        spot::Spot,
        spot_books::SpotBooks,
        spot_meta::SpotMeta,
    },
    utils::find_value,
};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct UserState {
    /// positions
    pub positions: Vec<Position>,
    /// spot balance
    pub spots: Vec<Spot>,
    /// TODO(ethan): staking hype in wei, wei_decimals = 8
    pub staking: u64,
    /// all
    pub c: i64,
    /// perps, if not exist, it's 0
    pub p: i64,
    /// timestamp
    pub t: Option<chrono::NaiveDateTime>,
    /// total raw usd
    pub total_raw_usd: i64,
}

impl UserState {
    /// TODO(ethan): check is not correct
    /// perps account value = total_raw_usd - perps_notional
    pub fn perps_account_value(&self, meta: &Meta) -> u128 {
        let mut val = self.total_raw_usd as i128;
        for position in self.positions.iter() {
            let oracle_px = match meta.get_perp_px_by_idx(position.asset_idx) {
                Some(px) => px,
                _ => continue, // should not happen
            };
            let notional = position.notional(oracle_px);
            val += notional;
        }

        val as u128
    }

    /// account value = perps_account_value + spot_account_value
    pub fn account_value(&self, meta: &Meta, spot_meta: &SpotMeta, spot_book: &SpotBooks) -> u128 {
        let perps_account_value = self.perps_account_value(meta);
        let spot_ntls = self
            .spots
            .iter()
            .map(|spot| {
                // if token_id == 0, it's usdc
                let price = if spot.token_id == 0 {
                    1
                } else {
                    // price = real_price * 10^8 / 10^sz_decimals
                    spot_book
                        .get_last_price(spot_meta.get_spot_idx_by_token_id(spot.token_id as usize))
                };
                spot.notional(
                    price,
                    spot_meta.get_spot_wei_decimals_by_token_id(spot.token_id as usize),
                    spot_meta.get_spot_sz_decimals_by_token_id(spot.token_id as usize),
                )
            })
            .sum::<u128>();
        // TODO(ethan): need to add staking HYPE

        perps_account_value + spot_ntls
    }
}
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserStates(HashMap<Address, UserState>);

impl Deref for UserStates {
    type Target = HashMap<Address, UserState>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl UserStates {
    /// all: abci_state[exchange][perp_dexs][0][user_states]
    /// perps: abci_state[exchange][perp_dexs][0][clearinghouse][user_states]
    /// spots: abci_state[exchange][spot_clearinghouse][user_states]
    pub fn from_rmpv_value(
        perps: &rmpv::Value,
        spots: &rmpv::Value,
        all: &rmpv::Value,
    ) -> anyhow::Result<Self> {
        // 1. handle all
        let all_map = all.as_array().ok_or(anyhow::anyhow!("invalid all"))?;
        let mut user_states = HashMap::new();
        for item in all_map {
            let item = item.as_array().ok_or(anyhow::anyhow!("invalid item"))?;
            let address = item[0].as_str().ok_or(anyhow::anyhow!("invalid address"))?;
            let address = Address::from_str(address)?;

            let c = find_value(&item[1], "C").and_then(|v| v.as_i64()).unwrap_or(0);
            let p = find_value(&item[1], "P").and_then(|v| v.as_i64()).unwrap_or(0);
            let t = find_value(&item[1], "t").and_then(|v| v.as_str()).and_then(|t| {
                chrono::NaiveDateTime::parse_from_str(t, "%Y-%m-%dT%H:%M:%S%.f").ok()
            });
            let user_state = UserState {
                positions: vec![],
                c,
                p,
                t,
                total_raw_usd: 0,
                spots: vec![],
                staking: 0,
            };

            user_states.insert(address, user_state);
        }

        // 2. handle perps
        let user_to_states = find_value(perps, "user_to_state")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid user_to_states"))?;

        for item in user_to_states {
            let item = item.as_array().ok_or(anyhow::anyhow!("invalid item"))?;
            let address = item[0].as_str().ok_or(anyhow::anyhow!("invalid address"))?;

            let address = Address::from_str(address)?;

            let cross_total_raw_usd =
                find_value(&item[1], "u").and_then(|v| v.as_i64()).unwrap_or(0);
            let positions = find_value(&item[1], "p")
                .and_then(|p| find_value(p, "p"))
                .and_then(|p| p.as_array());

            if let Some(positions) = positions {
                for pos in positions {
                    let position = Position::from_rmpv_value(pos)?;
                    if let Some(position) = position {
                        // handle isolated total_raw_usd
                        if let Some(leverage) = &position.leverage {
                            if let Leverage::Isolated((_l, u)) = leverage {
                                user_states.get_mut(&address).unwrap().total_raw_usd += *u;
                            }
                        }
                        user_states.get_mut(&address).unwrap().positions.push(position);
                    }
                }
            }

            user_states.get_mut(&address).unwrap().total_raw_usd += cross_total_raw_usd;
        }

        // 3. handle spots
        let user_to_spots_state = find_value(spots, "user_states")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid user_to_spots_state"))?;

        for item in user_to_spots_state {
            let item = item.as_array().ok_or(anyhow::anyhow!("invalid item"))?;
            let address = item[0].as_str().ok_or(anyhow::anyhow!("invalid address"))?;
            let address = Address::from_str(address)?;

            let spots = find_value(&item[1], "b").and_then(|s| s.as_array());

            if let Some(spots) = spots {
                for spot in spots {
                    let spot = Spot::from_rmpv_value(spot)?;

                    user_states.entry(address).or_default().spots.push(spot);
                }
            }
        }

        Ok(Self(user_states))
    }
}

#[cfg(test)]
mod tests {
    use std::{io::Read, str::FromStr};

    use alloy::primitives::Address;

    use crate::{
        types::state::{State, UserStates},
        utils::find_value,
    };

    #[test]
    fn test_user_states_from_rmpv_value() {
        let path = "src/types/samples/hyperliquid_data/abci_state.rmp";
        //0xd2075830d96b6fe8d41d1620c38106c8c29e4b84
        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let exchange = find_value(&value, "exchange").unwrap();
        let perp_dexs = &find_value(exchange, "perp_dexs").and_then(|v| v.as_array()).unwrap()[0];
        let clearinghouse = find_value(perp_dexs, "clearinghouse").unwrap();
        let spot_clearinghouse = find_value(exchange, "spot_clearinghouse").unwrap();

        let all = find_value(exchange, "user_states").unwrap();
        let perps = find_value(clearinghouse, "user_states").unwrap();

        let user_states = UserStates::from_rmpv_value(perps, spot_clearinghouse, all).unwrap();

        let write_file = std::fs::File::create("src/types/samples/user_states.json").unwrap();
        serde_json::to_writer_pretty(write_file, &user_states).unwrap();
    }

    #[test]
    fn test_user_state_account_values() {
        let path = "src/types/samples/hyperliquid_data/abci_state.rmp";

        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let state = State::from_rmpv_value(&value).unwrap();
        let _user = Address::from_str("0xc1764d4d7dcf3fcf5799ee83eb78c56a73fd229e").unwrap();

        let mut account_values = Vec::new();

        for (address, user_state) in state.user_states.iter() {
            let account_value =
                user_state.account_value(&state.meta, &state.spot_meta, &state.spot_books);
            account_values.push((address, account_value));
        }

        // descending order
        account_values.sort_by(|a, b| b.1.cmp(&a.1));

        let write_file = std::fs::File::create("src/types/samples/account_values.json").unwrap();
        serde_json::to_writer_pretty(write_file, &account_values).unwrap();
    }

    #[test]
    fn test_user_state_perps_account_values() {
        let path = "src/types/samples/hyperliquid_data/abci_state.rmp";

        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let state = State::from_rmpv_value(&value).unwrap();

        let mut account_values = Vec::new();

        for (address, user_state) in state.user_states.iter() {
            let perps_account_value = user_state.perps_account_value(&state.meta);
            account_values.push((address, perps_account_value));
        }

        println!("account_values: {:?}", account_values.len());

        // descending order
        account_values.sort_by(|a, b| b.1.cmp(&a.1));

        let write_file =
            std::fs::File::create("src/types/samples/perps_account_values.json").unwrap();
        serde_json::to_writer_pretty(write_file, &account_values).unwrap();
    }

    #[test]
    fn test_user_state_account_value() {
        let path = "src/types/samples/hyperliquid_data/abci_state.rmp";

        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let state = State::from_rmpv_value(&value).unwrap();
        let user = Address::from_str("0xa2ce501d9c0c5e23d34272f84402cfb7835b3126").unwrap();

        let user_state = state.user_states.get(&user).unwrap();
        let perps_account_value = user_state.perps_account_value(&state.meta);
        let account_value =
            user_state.account_value(&state.meta, &state.spot_meta, &state.spot_books);
        println!("account_value: {}", account_value);
        println!("perps_account_value: {}", perps_account_value);
    }
}
