use std::str::FromStr;

use crate::types::{
    block::utils::deserialize_string_to_bigdecimal,
    state::{
        books::{BookDirection, BookEntry},
        meta::Meta,
    },
};

use alloy::primitives::Address;
use bigdecimal::{BigDecimal, ToPrimitive};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OrderStatusEvent {
    pub time: chrono::NaiveDateTime,
    pub hash: Option<String>,
    pub user: String,
    pub status: String,
    pub order: Order,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Order {
    pub coin: String,
    pub side: String,
    #[serde(deserialize_with = "deserialize_string_to_bigdecimal")]
    pub limit_px: BigDecimal,
    #[serde(deserialize_with = "deserialize_string_to_bigdecimal")]
    pub sz: BigDecimal,
    pub oid: u64,
    pub timestamp: u64,
    pub trigger_condition: Option<String>,
    pub is_trigger: bool,
    #[serde(deserialize_with = "deserialize_string_to_bigdecimal")]
    pub trigger_px: BigDecimal,
    pub children: Vec<Order>,
    pub is_position_tpsl: bool,
    pub reduce_only: bool,
    pub order_type: Option<String>,
    #[serde(deserialize_with = "deserialize_string_to_bigdecimal")]
    pub orig_sz: BigDecimal,
    pub tif: Option<String>,
    pub cloid: Option<String>,
}

impl OrderStatusEvent {
    pub fn try_into_book_entry(&self, meta: &Meta) -> anyhow::Result<BookEntry> {
        let order = &self.order;
        let asset_idx =
            meta.get_perp_idx(&self.order.coin).ok_or(anyhow::anyhow!("invalid asset_idx"))? as u16;
        let sz_decimal = meta
            .get_perp_sz_decimals_by_idx(asset_idx as u64)
            .ok_or(anyhow::anyhow!("invalid sz_decimal"))?;

        let size = &order.sz * BigDecimal::from(10u64.pow(sz_decimal as u32));
        let orig_size = &order.orig_sz * BigDecimal::from(10u64.pow(sz_decimal as u32));
        let limit_px = &order.limit_px * BigDecimal::from(10u64.pow(6)) /
            BigDecimal::from(10u64.pow(sz_decimal as u32));

        let book_entry = BookEntry {
            oid: order.oid,
            size: size.to_u64().ok_or(anyhow::anyhow!("size overflow"))?,
            orig_size: orig_size.to_u64().ok_or(anyhow::anyhow!("orig_size overflow"))?,
            limit_px: limit_px.to_u64().ok_or(anyhow::anyhow!("limit_px overflow"))?,
            user: Address::from_str(&self.user)
                .map_err(|e| anyhow::anyhow!("invalid user: {}", e))?,
            dir: match order.side.as_str() {
                "B" => BookDirection::Bid,
                "A" => BookDirection::Ask,
                _ => anyhow::bail!("invalid side: {}", order.side),
            },
            tif: order.tif.clone(),
            ts: order.timestamp,
            child_oid: order.cloid.clone(),
        };

        Ok(book_entry)
    }
}

#[cfg(test)]
mod tests {
    use std::io::Read;

    use crate::types::state::State;

    use super::*;

    #[test]
    fn test_try_into_book_entry() {
        let path = "examples/abci_state.2.rmp";
        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let state = State::from_rmpv_value(&value).unwrap();
        let json = r#"
                        {
            "time": "2025-07-25T02:27:44.712060669",
            "user": "******************************************",
            "hash": "0x195458f096727723e6cd04282d9c280183011082ddb37bbd5dabcd6c078b7fcf",
            "status": "open",
            "order": {
                "coin": "BTC",
                "side": "A",
                "limitPx": "117259.0",
                "sz": "0.01706",
                "oid": 120453232632,
                "timestamp": 1753410464712,
                "triggerCondition": "N/A",
                "isTrigger": false,
                "triggerPx": "0.0",
                "children": [],
                "isPositionTpsl": false,
                "reduceOnly": false,
                "orderType": "Limit",
                "origSz": "0.01706",
                "tif": "Alo",
                "cloid": "0xa8e45c48af0747f4bc532c8cb792c770"
            }
        }"#;

        let event: OrderStatusEvent = serde_json::from_str(json).unwrap();
        let book_entry = event.try_into_book_entry(&state.meta).unwrap();
        println!("{:?}", book_entry);
    }
}
