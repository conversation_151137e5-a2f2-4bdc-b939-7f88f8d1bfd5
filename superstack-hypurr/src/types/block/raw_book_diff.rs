use crate::types::block::utils::deserialize_string_to_bigdecimal;
use bigdecimal::BigDecimal;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RawBookDiffEvent {
    pub user: String,
    pub oid: u64,
    pub coin: String,
    pub px: String,
    pub raw_book_diff: RawBookDiff,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum RawBookDiff {
    #[serde(rename = "new")]
    New {
        #[serde(deserialize_with = "deserialize_string_to_bigdecimal")]
        sz: BigDecimal,
    },
    #[serde(rename = "update")]
    Update {
        #[serde(rename = "origSz")]
        #[serde(deserialize_with = "deserialize_string_to_bigdecimal")]
        orig_sz: BigDecimal,
        #[serde(rename = "newSz")]
        #[serde(deserialize_with = "deserialize_string_to_bigdecimal")]
        new_sz: BigDecimal,
    },
    #[serde(rename = "remove")]
    Remove,
}

// 120453245105 not equal
