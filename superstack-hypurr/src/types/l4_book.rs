use std::{collections::HashMap, fmt, str::FromStr};

use bigdecimal::{BigDecimal, Zero};
use serde::{
    de::{self, DeserializeSeed, Visitor},
    Deserialize,
};

use super::Side;

// snapshot as: [ ["token", [Bids], [Asks]] ]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct L4BookOrder {
    pub coin: String,
    pub side: Side,
    pub limit_px: String,
    pub sz: String,
    // pub oid: u64,
    // pub timestamp: u64,
    // pub trigger_condition: String,
    // pub is_trigger: bool,
    // pub trigger_px: String,
    // pub children: Vec<()>,
    // pub is_position_tpsl: bool,
    // pub reduce_only: bool,
    // pub order_type: String,
    // pub orig_sz: String,
    // pub tif: String,
    // pub cloid: Option<String>, // hash
}

#[derive(Debug, Clone)]
pub struct L4BookOrderStats {
    pub price_intervals: HashMap<String, (BigDecimal, u64)>, /* price interval -> (total_sz,
                                                              * order_count) */
    pub total_sz: BigDecimal, // sum of all sz
}

// expecting [orders]
pub struct L4BookOrderVisitor;
impl<'de> Visitor<'de> for L4BookOrderVisitor {
    type Value = L4BookOrderStats;

    fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("an array of orders")
    }

    fn visit_seq<A>(self, mut seq: A) -> Result<Self::Value, A::Error>
    where
        A: de::SeqAccess<'de>,
    {
        let mut stats =
            L4BookOrderStats { price_intervals: HashMap::new(), total_sz: BigDecimal::from(0) };

        while let Some(order) = seq.next_element::<L4BookOrder>()? {
            if order.coin.starts_with("@") {
                continue;
            }

            match BigDecimal::from_str(&order.sz) {
                Ok(sz) => {
                    stats.total_sz += &sz;
                    let entry = stats
                        .price_intervals
                        .entry(order.limit_px)
                        .or_insert((BigDecimal::from(0), 0));
                    entry.0 += &sz; // total_sz of px
                    entry.1 += 1; // order_count of px
                }
                _ => {
                    tracing::warn!("bigdecimal error: sz = {:?}", order.sz);
                }
            }
        }

        Ok(stats)
    }
}

impl<'de> DeserializeSeed<'de> for L4BookOrderVisitor {
    type Value = L4BookOrderStats;

    fn deserialize<D>(self, deserializer: D) -> Result<Self::Value, D::Error>
    where
        D: de::Deserializer<'de>,
    {
        deserializer.deserialize_seq(self)
    }
}

#[derive(Debug, Clone)]

pub struct L4BookTokenStats {
    pub token: String,
    pub bid_stats: L4BookOrderStats,
    pub ask_stats: L4BookOrderStats,
}

// expecting [ [Bids], [Asks] ]
struct PairVisitor;
impl<'de> Visitor<'de> for PairVisitor {
    type Value = (L4BookOrderStats, L4BookOrderStats);

    fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("an array of [Bids], [Asks]")
    }

    fn visit_seq<A>(self, mut seq: A) -> Result<Self::Value, A::Error>
    where
        A: de::SeqAccess<'de>,
    {
        let bid_stats = seq
            .next_element_seed(L4BookOrderVisitor)?
            .ok_or_else(|| de::Error::custom("expected bids"))?;
        let ask_stats = seq
            .next_element_seed(L4BookOrderVisitor)?
            .ok_or_else(|| de::Error::custom("expected asks"))?;
        Ok((bid_stats, ask_stats))
    }
}

impl<'de> DeserializeSeed<'de> for PairVisitor {
    type Value = (L4BookOrderStats, L4BookOrderStats);

    fn deserialize<D>(self, deserializer: D) -> Result<Self::Value, D::Error>
    where
        D: de::Deserializer<'de>,
    {
        deserializer.deserialize_seq(self)
    }
}

// expecting [ "token", [[Bids], [Asks]] ]
pub struct L4BookTokenVisitor;
impl<'de> Visitor<'de> for L4BookTokenVisitor {
    type Value = L4BookTokenStats;

    fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("an array of [token, [Bids], [Asks]]")
    }

    fn visit_seq<A>(self, mut seq: A) -> Result<Self::Value, A::Error>
    where
        A: de::SeqAccess<'de>,
    {
        let Some(token) = seq.next_element::<String>()? else {
            return Err(de::Error::custom("expected token name"));
        };

        let (bid_stats, ask_stats) = seq
            .next_element_seed(PairVisitor)?
            .ok_or_else(|| de::Error::custom("expected bids and asks"))?;

        Ok(L4BookTokenStats { token, bid_stats, ask_stats })
    }
}

impl<'de> DeserializeSeed<'de> for L4BookTokenVisitor {
    type Value = L4BookTokenStats;

    fn deserialize<D>(self, deserializer: D) -> Result<Self::Value, D::Error>
    where
        D: de::Deserializer<'de>,
    {
        deserializer.deserialize_seq(self)
    }
}

pub struct L4BookVisitor;
impl<'de> Visitor<'de> for L4BookVisitor {
    type Value = Vec<L4BookTokenStats>;

    fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("an array of [token with orders]")
    }

    fn visit_seq<A>(self, mut seq: A) -> Result<Self::Value, A::Error>
    where
        A: de::SeqAccess<'de>,
    {
        let mut tokens = Vec::new();
        while let Some(token_stats) = seq.next_element_seed(L4BookTokenVisitor)? {
            if !is_valid_token(&token_stats) {
                // not logging spot
                if !token_stats.token.starts_with("@") {
                    tracing::info!("skipping token: {}", token_stats.token);
                }
                continue;
            }
            tokens.push(token_stats);
        }

        Ok(tokens)
    }
}

fn is_valid_token(token_stats: &L4BookTokenStats) -> bool {
    !token_stats.token.starts_with("@") &&
        !token_stats.bid_stats.total_sz.is_zero() &&
        !token_stats.ask_stats.total_sz.is_zero()
}

pub type L4BookStates = Vec<L4BookTokenStats>;

#[cfg(test)]
mod tests {
    use serde::Deserializer;
    use std::{fs::File, io::BufReader};

    use super::*;

    #[test]
    fn test_deserialize_l4_book_entry() {
        superstack_data::utils::setup_tracing();
        let file = File::open("../../661370000.snap.json").unwrap();
        let reader = BufReader::new(file);
        let mut de = serde_json::Deserializer::from_reader(reader);
        let tokens: Vec<L4BookTokenStats> = de.deserialize_seq(L4BookVisitor).unwrap();
        for token in tokens.into_iter() {
            if token.token != "BTC" {
                continue;
            }
            let bid_min = token
                .bid_stats
                .price_intervals
                .keys()
                .map(|px| BigDecimal::from_str(px).unwrap())
                .min()
                .unwrap();
            let bid_max = token
                .bid_stats
                .price_intervals
                .keys()
                .map(|px| BigDecimal::from_str(px).unwrap())
                .max()
                .unwrap();
            let ask_min = token
                .ask_stats
                .price_intervals
                .keys()
                .map(|px| BigDecimal::from_str(px).unwrap())
                .min()
                .unwrap();
            let ask_max = token
                .ask_stats
                .price_intervals
                .keys()
                .map(|px| BigDecimal::from_str(px).unwrap())
                .max()
                .unwrap();
            let bos = token.bid_stats.price_intervals.values().map(|(_, n)| n).sum::<u64>();
            let aos = token.ask_stats.price_intervals.values().map(|(_, n)| n).sum::<u64>();
            println!(
                "{}: bid#={}/{bos} bid_min={} bid_max={} ask#={}/{aos} ask_min={} ask_max={}",
                token.token,
                token.bid_stats.price_intervals.len(),
                bid_min,
                bid_max,
                token.ask_stats.price_intervals.len(),
                ask_min,
                ask_max,
            );
        }
    }
}
