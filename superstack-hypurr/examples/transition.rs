use std::{
    io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>},
    str::FromStr,
};

use superstack_hypurr::types::{
    block::{order_status::OrderStatusEvent, HyperBlock},
    state::{books::Books, meta::Meta, State, StateCtx},
};

fn main() {
    let (first_ctx, mut first_books, first_meta) = get_books("examples/abci_state.1.rmp").unwrap();
    let (second_ctx, second_books, _second_meta) = get_books("examples/abci_state.2.rmp").unwrap();

    let path = "examples/order_status";
    let file = std::fs::File::open(path).unwrap();
    let reader = BufReader::new(file);

    let start_block = first_ctx.height + 1;
    let end_block = second_ctx.height;

    println!("start_block: {start_block}, end_block: {end_block}");

    for line in reader.lines() {
        let line = line.unwrap();
        match HyperBlock::<OrderStatusEvent>::from_str(&line) {
            Ok(block) => {
                if block.block_number >= start_block && block.block_number <= end_block {
                    if block.block_number == 674076834 {
                        println!("here");
                        if let Err(e) = first_books.apply_order_status(&block, &first_meta) {
                            println!("error: {:?}", e);
                        }
                    } else {
                        let _ = first_books.apply_order_status(&block, &first_meta);
                    }
                } else {
                    // println!("skip block: {}", block.block_number);
                }
            }
            Err(e) => {
                println!("invalid block: {:?} ,error: {:?}", &line[..20], e);
            }
        }
    }

    second_books.is_equal(&first_books).unwrap();
}

pub fn get_books(path: &str) -> anyhow::Result<(StateCtx, Books, Meta)> {
    let file = std::fs::File::open(path).unwrap();
    let mut reader = BufReader::new(file);

    let mut buffer = Vec::new();
    reader.read_to_end(&mut buffer).unwrap();

    let mut cursor = std::io::Cursor::new(&buffer);
    let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

    let state = State::from_rmpv_value(&value).unwrap();

    Ok((state.context, state.books, state.meta))
}
