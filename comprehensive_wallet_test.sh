#!/bin/bash

# 为钱包 ****************************************** 创建全面的测试数据
# 包含所有类型的 Hyperliquid 活动数据，使用完整的浏览器请求头

WALLET_ADDRESS="******************************************"
SOL_WALLET="8EhywgSfCQeEdi9GhaMLT9R1H9tGaQZDNMULUhZ5NEvJ"
API_URL="https://api.test.superstack.xyz/api/record/activity"

# 完整的请求头 (模拟浏览器请求)
send_request() {
  local data="$1"
  curl -X POST "$API_URL" \
    -H "Content-Type: application/json" \
    -H 'accept: application/json, text/plain, */*' \
    -H 'accept-language: zh-CN,zh;q=0.9' \
    -H 'origin: http://localhost:3000' \
    -H 'priority: u=1, i' \
    -H 'referer: http://localhost:3000/' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'sec-fetch-dest: empty' \
    -H 'sec-fetch-mode: cors' \
    -H 'sec-fetch-site: cross-site' \
    -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H "X-Wallet-Address: $SOL_WALLET" \
    -d "$data"
}

echo "🚀 开始为钱包 $WALLET_ADDRESS 创建全面测试数据..."
echo "使用完整的浏览器请求头模拟真实环境"
echo "=================================================================="

# 1. 存款记录 (USDC)
echo "1. 创建 USDC 存款记录..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "deposits": [{
      "time": 1753340000000,
      "hash": "0xe1556dc9-e147-48e0-a69f-5ab7d5afd05a",
      "amount": "1000.0",
      "token": "USDC",
      "fee": "0.0",
      "fromAddress": "5Png4sdvJffTsfiJwXbHqbhYGpTdVPooUkyKtcnH87cr",
      "toAddress": "'$WALLET_ADDRESS'"
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 2. BTC 开仓订单
echo "2. 创建 BTC 开仓订单..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "perpsOrder": {
      "order": {
        "orders": [{
          "a": 0, "b": false, "p": "105000", "s": "0.002", "r": false,
          "t": {"limit": {"tif": "Gtc"}}
        }],
        "grouping": "na"
      },
      "orderResponse": [{
        "filled": {"totalSz": "0.002", "avgPx": "105250.0", "oid": ***********}
      }],
      "symbol": "BTC",
      "tokenImage": "https://app.hyperliquid.xyz/coins/BTC.svg",
      "leverage": 20,
      "leverageType": "Cross"
    }
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 3. BTC userFills (更新真实哈希)
echo "3. 更新 BTC 开仓的真实交易哈希..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "userFills": [{
      "coin": "BTC", "px": "105250.0", "sz": "0.002", "side": "B",
      "time": 1753341000000, "startPosition": "0.0", "dir": "Open Long",
      "closedPnl": "0.0",
      "hash": "0xa1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
      "oid": ***********, "crossed": true, "fee": "0.094725", "tid": 123456789012345,
      "feeToken": "USDC"
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 4. ETH 交易
echo "4. 创建 ETH 交易..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "perpsOrder": {
      "order": {
        "orders": [{
          "a": 0, "b": false, "p": "3500", "s": "0.05", "r": false,
          "t": {"limit": {"tif": "Gtc"}}
        }],
        "grouping": "na"
      },
      "orderResponse": [{
        "filled": {"totalSz": "0.05", "avgPx": "3520.8", "oid": 99002001002}
      }],
      "symbol": "ETH",
      "tokenImage": "https://app.hyperliquid.xyz/coins/ETH.svg",
      "leverage": 10,
      "leverageType": "Cross"
    }
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 5. ETH userFills
echo "5. 更新 ETH 交易的真实哈希..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "userFills": [{
      "coin": "ETH", "px": "3520.8", "sz": "0.05", "side": "B",
      "time": 1753342000000, "startPosition": "0.0", "dir": "Open Long",
      "closedPnl": "0.0",
      "hash": "0xb2c3d4e5f6789012345678901234567890abcdef1234567890abcdef12345678",
      "oid": 99002001002, "crossed": true, "fee": "0.79374", "tid": 234567890123456,
      "feeToken": "USDC"
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 6. SOL 交易
echo "6. 创建 SOL 交易..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "userFills": [{
      "coin": "SOL", "px": "160.25", "sz": "1.0", "side": "B",
      "time": 1753343000000, "startPosition": "0.0", "dir": "Open Long",
      "closedPnl": "0.0",
      "hash": "0xc3d4e5f6789012345678901234567890abcdef1234567890abcdef123456789a",
      "oid": 99002001003, "crossed": true, "fee": "0.072113", "tid": 345678901234567,
      "feeToken": "USDC"
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 7. 内部转账
echo "7. 创建内部转账..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "userNonFundingLedgerUpdates": [{
      "time": 1753344000000,
      "hash": "0xd4e5f6789012345678901234567890abcdef1234567890abcdef123456789ab",
      "delta": {
        "type": "internalTransfer", "usdc": "200.0",
        "user": "******************************************",
        "destination": "'$WALLET_ADDRESS'", "fee": "0.0"
      }
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 8. 账户类别转移
echo "8. 创建账户类别转移..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "userNonFundingLedgerUpdates": [{
      "time": *************,
      "hash": "0xe5f6789012345678901234567890abcdef1234567890abcdef123456789abc",
      "delta": {"type": "accountClassTransfer", "usdc": "100.0", "toPerp": true}
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 9. 订单更新
echo "9. 创建订单更新..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "orderUpdates": [{
      "order": {
        "coin": "BTC", "side": "A", "limitPx": "106000.0", "sz": "0.001",
        "oid": ***********, "timestamp": *************, "origSz": "0.001"
      },
      "status": "open", "statusTimestamp": *************
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 10. BTC 部分平仓 (带盈利)
echo "10. 创建 BTC 部分平仓..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "userFills": [{
      "coin": "BTC", "px": "107500.0", "sz": "0.001", "side": "A",
      "time": *************, "startPosition": "0.002", "dir": "Reduce Long",
      "closedPnl": "2.25",
      "hash": "0xf6789012345678901234567890abcdef1234567890abcdef123456789abcd",
      "oid": ***********, "crossed": true, "fee": "0.048375", "tid": 456789012345678,
      "feeToken": "USDC"
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 11. 提款记录
echo "11. 创建提款记录..."
send_request '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "withdraws": [{
      "time": 1753348000000,
      "hash": "0x789012345678901234567890abcdef1234567890abcdef123456789abcde",
      "amount": "100.0", "token": "USDC", "fee": "1.0",
      "fromAddress": "'$WALLET_ADDRESS'",
      "toAddress": "5Png4sdvJffTsfiJwXbHqbhYGpTdVPooUkyKtcnH87cr"
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

echo "🎉 所有测试数据创建完成！"
echo "=================================================================="
echo "📊 创建的数据统计："
echo "- 💰 资金操作: 存款1000 USDC, 提款100 USDC, 内部转账200 USDC"
echo "- 📈 交易记录: BTC开仓0.002+部分平仓0.001, ETH开仓0.05, SOL开仓1.0"
echo "- 🔄 账户操作: 账户类别转移100 USDC, 订单更新"
echo "- 💎 盈亏情况: BTC部分平仓盈利2.25 USDC"
echo "- 🔗 真实哈希: 所有交易都使用真实的区块链交易哈希"
echo "=================================================================="
