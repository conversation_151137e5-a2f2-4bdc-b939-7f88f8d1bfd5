# clean superstack-api database
DROP TABLE IF EXISTS 
    _sqlx_migrations,
    closed_trades,
    daily_pnl,
    histories, 
    positions,
    transactions,
    wallets,
    token_price,
    token_metadata,
    token_info,
    open_trades,
    account_values,
    orders,
    trades,
    invite_codes,
    realized_pnl,
    wallet_activity,
    referral_codes,
    referral_rewards,
    referrals,
    settings,
    telegram_users, 
    watches CASCADE;


# clean superstack-data database
DROP TABLE IF EXISTS 
    _sqlx_migrations,
    dex_trades,
    pool_states,
    token_holders,
    token_metadata,
    makers, 
    latest_pool_states,
    statistics,
    holders,
    tokens, 
    trades,
    candles,
    aggregated_token_states,
    aggregated_token_statistics,
    pool_statistics,
    latest_pool_states,
    meme_statistics,
    pool_metadata,
    token_states,
    latest_token_states,
    token_statistics,
    sol_price,
    bot_makers,
    CASCADE;


TRUNCATE TABLE 
    dex_trades,
    pool_states,
    token_holders,
    token_metadata,
    makers, 
    latest_pool_states,
    candles,
    pool_statistics,
    latest_pool_states,
    meme_statistics,
    pool_metadata,
    token_states,
    latest_token_states,
    token_statistics,
    sol_price,
    bot_makers
CASCADE;