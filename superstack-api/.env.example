PORT=3000

DATABASE_URL=postgresql://admin:adminpassword@localhost:5433/superstack_api
MAX_DB_CONNECTIONS=30

JUPITER_TRIGGER_URL=https://lite-api.jup.ag/trigger/v1
JUPITER_PRICE_URL=https://lite-api.jup.ag/price/v2
JUPITER_SWAP_URL=https://lite-api.jup.ag/swap/v1

SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# HyperEVM Configuration (Simplified)
HYPEREVM_RPC_URL=https://rpc.hyperliquid-testnet.xyz/evm

MOONPAY_SECRET_KEY=your_moonpay_secrey_key

ENABLE_INVITE_CODE=false
INVITE_CODE_FILE_PATH=invite_codes.json
PERPS_CATEGORY_PATH=perps_category.json

# Referral System Configuration
REFERRAL_UNLIMITED_USERS=8d2HXxu5PCy21ZrPityWSKPsnSdACYoUnvZ4SfFmazZb,ABBUBJF7rDD3YwgVjT4frhE3X2576wjGqHozkoo4frR4
REFERRAL_TIER1_PERCENTAGE=0.20
REFERRAL_TIER2_PERCENTAGE=0.024
REFERRAL_TIER3_PERCENTAGE=0.016
REFERRAL_SIGNUP_REWARD_USD=5.0

TELEGRAM_BOT_USERNAME=SuperstackTestBot

# Image upload configuration
MAX_IMAGE_SIZE_MB=10
ALLOWED_IMAGE_FORMATS=jpg,jpeg,png,webp
GCS_BASE_URL=https://static.test.superstack.xyz/public
STORAGE_DIR=/static-public-bucket

# Dashboard
DASHBOARD_KEY=


POSTGRES_INDEXER_DATABASE_URL=postgresql://superstack-indexer:password@127.0.0.1:5432/superstack-indexer
POSTGRES_INDEXER_MAX_CONNECTIONS=30
POSTGRES_INDEXER_NEED_MIGRATE=false
STORE_PRICE_HISTORY=false

REDIS_URL=

KAFKA_BOOTSTRAP_SERVERS=
KAFKA_API_KEY=
KAFKA_API_SECRET=

COINGECKO_API_KEY=CG-T2Dk49gHP6uEZE1cotuJCezy
