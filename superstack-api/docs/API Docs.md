# Backend API Documentation

> last updated: 20250725

This document provides all API endpoints in the Superstack Backend.

## 🆕 New Features

### Version 2025-07-25: PnL Share Images Route Fix

**Fixed Issues**:
- **Route Matching**: Fixed DELETE endpoint for PnL share images to properly handle paths containing slashes
- **Path Parameters**: Updated route definition to use catch-all parameter (`{*image_path}`) for proper path matching
- **Deprecated Endpoints**: Removed deprecated select endpoint as frontend now handles image selection

**Technical Details**:
- Changed route from `/api/pnl/share/images/{image_path}` to `/api/pnl/share/images/{*image_path}`
- This allows proper matching of image paths like `pnl-share-images/uuid-filename.png`
- Removed `/api/pnl/share/images/{*image_path}/select` endpoint (deprecated)

### Version 2025-01-08: Perpetual Contracts (Perps) Category Support

**New Endpoints**:
- `/api/perps` - Enhanced with category filtering support
- `/api/perps/categories` - Get all available perp categories
- `/api/perps/categories/detail` - Get symbols for specific category

**New Features**:
- **Category Filtering**: Filter perps by category (`ai`, `layer1`, `layer2`, `defi`, `gaming`, `meme`)
- **Enhanced Query Parameters**: Added `category` parameter to existing perps endpoint
- **Category Management**: Get list of categories and their associated symbols

**Usage Examples**:
```bash
# Get all AI category perps
GET /api/perps?category=ai&sort_by=market_cap&sort_desc=true

# Get all available categories
GET /api/perps/categories

# Get symbols in DeFi category
GET /api/perps/categories/detail?category=defi
```

## Base URL

All API endpoints are prefixed with the base URL.

- Production: `https://api.superstack.xyz`
- Sit: `https://api.test.superstack.xyz`

## Authentication

**API endpoints are categorized into three authentication levels:**

### Authentication Levels:

1. **Public Endpoints** - No authentication required
2. **Wallet Authentication** - Requires `X-Wallet-Address` header
3. **Dashboard Authentication** - Requires `X-Dashboard-Key` header

### Public Endpoints (No Authentication Required):

**Market Data & System:**
- `/api/health`, `/health`, `/api/metrics` - System health and metrics
- `/api/sol-price` - SOL price information
- `/api/markets/tokens`, `/api/markets/search` - Market data and token search
- `/api/trench/tokens` - Trench token listings
- `/api/candle/snapshot` - Candlestick chart data
- `/api/perps`, `/api/perps/categories`, `/api/perps/categories/detail` - Perpetual contracts
- `/api/tokens/info` - Batch token information
- `/api/moonpay/sign` - Payment processing signatures

**Token Information:**
- `/api/token/info/{token_address}` - Individual token details
- `/api/token/details/{token_address}` - Token details (deprecated)
- `/api/token/meme-scan/{token_address}` - Meme token statistics
- `/api/token/trades/{token_address}` - Token trading history
- `/api/token/holders/{token_address}` - Token holder information
- `/api/token/top-traders/{token_address}` - Top traders for token

**Transaction Status & Charts:**
- `/api/transaction/status/{signature}` - Transaction status lookup
- `/api/hyperevm/transaction/status/{signature}` - HyperEVM transaction status
- `/api/depth/{pool_address}` - Liquidity depth charts

### Wallet Authentication (X-Wallet-Address Header Required):

**User Portfolio & Trading:**
- `/api/positions`, `/api/open-orders` - User positions and orders
- `/api/order-history`, `/api/trade-history`, `/api/position-history` - Trading history
- `/api/pnl/stats`, `/api/pnl/share` - Profit & Loss statistics
- `/api/activity`, `/api/activity/refresh`, `/api/activity/stats` - Wallet activity
- `/api/chart/user-indicators`, `/api/chart/trend-lines` - User trading indicators

**User Settings & Preferences:**
- `/api/watchlist` - User watchlists
- `/api/setting` - User settings and preferences

**Referral System:**
- `/api/referral/code`, `/api/referral/stats` - Referral codes and statistics
- `/api/referral/list`, `/api/referral/rewards` - Referral lists and rewards
- `/api/referral/rewards/claim`, `/api/referral/network` - Reward claiming and network

**Transaction Operations:**
- `/api/transaction/send` - Send transactions
- `/api/transaction/relay/quote`, `/api/transaction/relay/swap` - Transaction relay
- `/api/hyperevm/balance`, `/api/hyperevm/transaction/send` - HyperEVM operations

**Authentication Headers:**
```
X-Wallet-Address: your_wallet_address_here
```

### Dashboard Authentication (X-Dashboard-Key Header Required):

**Dashboard Statistics:**
- `/dashboard/code-stats` - Referral code statistics
- `/dashboard/transaction-stats` - Transaction statistics
- `/dashboard/funds-stats` - User funds statistics

**Authentication Headers:**
```
X-Dashboard-Key: your_dashboard_key_here
```

### New User Registration:

For wallet-authenticated endpoints, new users must bind a referral code:

1. **Bind Referral Code**: POST to `/api/invite` with wallet address and referral code
2. **Access API**: After successful binding, access all wallet-authenticated endpoints
3. **Legacy Users**: Existing users can access directly without referral codes

### Authentication Errors:

- `"Missing wallet address"` - No X-Wallet-Address header provided
- `"Missing Authorization"` - No X-Dashboard-Key header provided
- `"Wallet must use a referral code to access the system"` - New user needs referral code

### Example Requests:

**Public Endpoint (No Authentication):**
```bash
curl -X GET "https://api.test.superstack.xyz/api/sol-price" \
  -H "Content-Type: application/json"
```

**Wallet-Authenticated Endpoint:**
```bash
curl -X GET "https://api.test.superstack.xyz/api/positions" \
  -H "Content-Type: application/json" \
  -H "X-Wallet-Address: your_wallet_address_here"
```

**Dashboard Endpoint:**
```bash
curl -X GET "https://api.test.superstack.xyz/dashboard/code-stats" \
  -H "Content-Type: application/json" \
  -H "X-Dashboard-Key: your_dashboard_key_here"
```

## API Endpoints

### 1. Health Check

**Route**: `/api/health`

**Method**: GET
**Description**: Health check endpoint to verify API status
**Authentication**: None required

**Response**:

```json
{
  "status": "ok"
}

```

---

### 2. Transaction Management

### 2.1 Send Transaction

**Route**: `/api/transaction/send`

**Method**: POST
**Description**: Submit a signed transaction to the blockchain
**Authentication**: Required (`X-Wallet-Address` header)

**Request Body**:

```json
{
  "walletAddress": "string",
  "serializedTransaction": "string" // Base64 encoded signed transaction
}

```

**Success Response**:

```json
{
  "signature": "4bwJL6EUzK2KWhCpJUHC33WBN4T3o6fLHJpnDJosLXYB74rwt9tmmvtDGsCUMoUbR7BicumMk8Ck6Q2qZtRiFomS"
}

```

**Error Response**:

```json
{
  "error": "Invalid transaction encoding" // or other error messages
}

```

### 2.2 Get Transaction Status

**Route**: `/api/transaction/status/{signature}`

**Method**: GET

**Description**: Check the status of a submitted transaction

**Path Parameters**:

- `signature` (string, required): Transaction signature

**Success Response**:

```json
{
  "signature": "4bwJL6EUzK2KWhCpJUHC33WBN4T3o6fLHJpnDJosLXYB74rwt9tmmvtDGsCUMoUbR7BicumMk8Ck6Q2qZtRiFomS",
  "status": "Pending" // "Pending", "Success", "Failed", "Expired"
}

```

---

### 3. Referral System

### 3.1 Bind Referral Code / Check Authorization

**Route**: `/api/invite`

**Method**: POST

**Description**: Bind a referral code to a wallet address for authentication, or check if a wallet is already authorized (for seed users)

**Request Body**:

```json
{
  "invite_code": "string", // optional - required for new users, not needed for seed users
  "wallet_address": "string"
}

```

**Success Response**:

```json
{
  "status": "Authorized" // "Authorized", "Unauthorized", "BindSuccess", "BindFailed"
}

```

**Response Status Meanings**:
- `"Authorized"`: User is already authorized (seed user or previously bound)
- `"BindSuccess"`: New user successfully bound with referral code
- `"Unauthorized"`: New user without valid referral code
- `"BindFailed"`: Referral code binding failed

### 3.2 Get Referral Code

**Route**: `/api/referral/code`

**Method**: GET

**Description**: Get user's referral codes for inviting others

**Query Parameters**:

- `wallet_address` (string, required): Wallet address

**Success Response**:

```json
{
  "codes": [
    {
      "code": "3067TS",
      "status": "active",
      "created_at": **********,
      "used_by_wallet": null,
      "used_at": null
    }
  ]
}

```

### 3.3 Get Referral Statistics

**Route**: `/api/referral/stats`

**Method**: GET

**Description**: Get referral statistics for a user

**Query Parameters**:

- `wallet_address` (string, required): Wallet address

**Success Response**:

```json
{
  "total_invites": 5,
  "unclaimed_rewards_usd": "25.50",
  "claimed_rewards_usd": "10.00"
}

```

### 3.4 Get Referral List

**Route**: `/api/referral/list`

**Method**: GET

**Description**: Get list of users referred by this wallet

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `limit` (number, optional): Number of results to return
- `offset` (number, optional): Offset for pagination

**Success Response**:

```json
{
  "referrals": [
    {
      "referee_wallet": "ABBUBJF7rDD3YwgVjT4frhE3X2576wjGqHozkoo4frR4",
      "created_at": **********
    }
  ],
  "total_count": 5
}

```

### 3.5 Get Referral Rewards

**Route**: `/api/referral/rewards`

**Method**: GET

**Description**: Get referral rewards for a user

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `limit` (number, optional): Number of results to return
- `offset` (number, optional): Offset for pagination

**Success Response**:

```json
{
  "rewards": [
    {
      "id": 123,
      "referee_wallet": "ABBUBJF7rDD3YwgVjT4frhE3X2576wjGqHozkoo4frR4",
      "reward_type": "signup",
      "amount_usd": "5.00",
      "is_claimed": false,
      "created_at": **********,
      "claimed_at": null
    }
  ],
  "total_unclaimed_usd": "25.50",
  "total_claimed_usd": "10.00"
}

```

### 3.6 Claim Referral Rewards

**Route**: `/api/referral/rewards/claim`

**Method**: POST

**Description**: Claim pending referral rewards

**Request Body**:

```json
{
  "wallet_address": "string",
  "reward_ids": [123, 124, 125] // optional - if not provided, claims all unclaimed rewards
}

```

**Success Response**:

```json
{
  "claimed_count": 3,
  "total_claimed_usd": "15.00",
  "transaction_signature": "5J8c7AkiQbxVkwfXZcyqJ5K35moQCwtCFXJURZmnoHZ"
}

```

### 3.7 Get Referral Network

**Route**: `/api/referral/network`

**Method**: GET

**Description**: Get referral network visualization data

**Query Parameters**:

- `wallet_address` (string, required): Root wallet address
- `levels` (number, optional, default 3): Number of levels to retrieve (1-5)

**Success Response**:

```json
{
  "network": [
    {
      "parent_wallet": "5CYqL8B2Uk3XQCtc3z6T8sHFY59dYQYUvfUBzU7u7T3o",
      "child_wallet": "ABBUBJF7rDD3YwgVjT4frhE3X2576wjGqHozkoo4frR4",
      "level": 1,
      "created_at": **********
    }
  ],
  "total_network_size": 15
}

```

---

### 4. Portfolio Management

### 4.1 Get Positions

**Route**: `/api/positions`

**Method**: GET
**Description**: Get current open positions for a wallet

**Auto-Repair Feature**: This endpoint automatically detects and repairs position data inconsistencies when querying all positions for a wallet (without token_mint filter). If inconsistent data is detected, the system will automatically reprocess transactions to ensure accurate position data.

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `token_mint` (string, optional): Filter by specific token mint
- `limit` (number, optional): Limit number of results
- `offset` (number, optional): Offset for pagination
- `sort_by` (string, optional): Sort order
- `position_type` (string, optional): Filter by position type: `"spot"` or `"perps"`
- `position_status` (string, optional): Filter by position status: `"open"`, `"closed"`, `"active"` (deprecated), or `"sold"` (deprecated)



**Example Requests**:

```
GET /api/positions?wallet_address=DDGniHdkFbtuh5ufizmoNeePVeaszZBePjZzjV5pvMwb
GET /api/positions?wallet_address=DDGniHdkFbtuh5ufizmoNeePVeaszZBePjZzjV5pvMwb&token_mint=AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump
GET /api/positions?wallet_address=DDGniHdkFbtuh5ufizmoNeePVeaszZBePjZzjV5pvMwb&position_type=spot&position_status=open
GET /api/positions?wallet_address=DDGniHdkFbtuh5ufizmoNeePVeaszZBePjZzjV5pvMwb&position_type=perps

```

**New Fields Added**:
- `sell_initials_ui_amount` (number): Number of tokens needed to sell to recover initial cost (UI units)
- `sell_initials_amount` (number): Number of tokens needed to sell to recover initial cost (raw lamports)
- `sell_initial_usd` (number): USD value that would be recovered (historical cost)

**Success Response**:

```json
{
  "token_position_data": {
    "total_invested_usd": 100.0,
    "total_invested_amount": 1000000000,
    "total_invested_ui_amount": 1000.0,
    "total_sold_usd": 50.0,
    "total_sold_amount": 500000000,
    "total_sold_ui_amount": 500.0,
    "total_remaining_usd": 45.0,
    "total_remaining_amount": 500000000,
    "total_remaining_ui_amount": 500.0,
    "total_pnl_usd": -5.0,
    "total_pnl_percentage": -0.1
  },
  "positions": [
    {
      "wallet_address": "DDGniHdkFbtuh5ufizmoNeePVeaszZBePjZzjV5pvMwb",
      "token_mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
      "token_metadata": {
        "name": "Superstack",
        "symbol": "WORK",
        "decimals": 6,
        "image_url": "string",
        "chain": "Solana"
      },
      "bought_amount": 167000000000,
      "bought_ui_amount": 167000.0,
      "sold_amount": 0,
      "sold_ui_amount": 0.0,
      "cost_usd": 2.0,
      "earnings_usd": 0.0,
      "remaining_usd": 1.8,
      "current_holdings_cost_usd": 1.8,
      "sell_initials_ui_amount": 18000.0,
      "sell_initials_amount": 18000000000,
      "sell_initial_usd": 1.8,
      "pnl_usd": -0.2,
      "pnl_percentage": -0.1,
      "buy_operations": [
        {
          "is_buy": true,
          "token_amount": 100000000,
          "base_amount": 167000000000,
          "base_mint": "So11111111111111111111111111111111111111112",
          "base_decimals": 9,
          "tx_sig": "3LLRM3yTP...",
          "timestamp": **********
        }
      ],
      "sell_operations": [],
      "open_time": **********
    }
  ]
}

```

### 4.2 Get Open Orders

**Route**: `/api/open-orders`

**Method**: GET
**Description**: Get current open orders for a wallet

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `token_mint` (string, optional): Filter by specific token mint
- `limit` (number, optional): Limit number of results
- `offset` (number, optional): Offset for pagination
- `order_type` (string, optional): Filter by order type: `"spot"` or `"perps"`
- `order_status` (string, optional): Filter by order status: `"open"` or `"closed"`

**Example Requests**:

```
GET /api/open-orders?wallet_address=FN4...pump
GET /api/open-orders?wallet_address=FN4...pump&token_mint=xxxxxxx
GET /api/open-orders?wallet_address=FN4...pump&order_type=spot&order_status=open
GET /api/open-orders?wallet_address=FN4...pump&order_type=perps

```

**Success Response**:

```json
[
  {
    "order_id": "order_123",
    "wallet_address": "FN4...pump",
    "token_mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
    "base_mint": "So11111111111111111111111111111111111111112",
    "timestamp": 1213413212,
    "pair_label": "WORK/SOL",
    "order_type": "Limit",
    "side": "Buy",
    "price": 0.0002,
    "token_amount": 313322000000,
    "token_ui_amount": 313322.0,
    "base_amount": 4300000000,
    "base_ui_amount": 0.43,
    "filled": 0.0,
    "token_metadata": {
      "name": "Superstack",
      "symbol": "WORK",
      "decimals": 6,
      "image_url": "https://static.jup.ag/jup/icon.png",
      "chain": "Solana"
    }
  }
]

```

### 4.3 Get Order History

**Route**: `/api/order-history`

**Method**: GET
**Description**: Get historical orders and trades for a wallet (includes both limit orders and market orders)

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `token_mint` (string, optional): Filter by specific token mint
- `limit` (number, optional, default 30): Number of records to return
- `offset` (number, optional, default 0): Number of records to skip
- `order_status` (string, optional): Filter by order status: `"closed"`

**Example Requests**:

```
GET /api/order-history?wallet_address=FN4...pump
GET /api/order-history?wallet_address=FN4...pump&token_mint=xxxxxxx&limit=30&offset=0
GET /api/order-history?wallet_address=FN4...pump&order_status=closed

```

**Success Response**:

```json
[
  {
    "order_id": "order_123",
    "wallet_address": "FN4...pump",
    "token_mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
    "base_mint": "So11111111111111111111111111111111111111112",
    "timestamp": 1213413212,
    "pair_label": "WORK/SOL",
    "order_type": "Limit", // "Limit" for limit orders, "Market" for market orders
    "side": "Buy",
    "price": 0.0002,
    "token_amount": 313322000000,
    "token_ui_amount": 313322.0,
    "base_amount": 4300000000,
    "base_ui_amount": 0.43,
    "filled": 1.0,
    "status": "Filled", // "Filled", "PartiallyFilled", "Cancelled", "Expired", "Failed"
    "token_metadata": {
      "name": "Superstack",
      "symbol": "WORK",
      "decimals": 6,
      "image_url": "https://static.jup.ag/jup/icon.png",
      "chain": "Solana"
    }
  }
]

```

### 4.4 Get Trade History

**Route**: `/api/trade-history`

**Method**: GET
**Description**: Get historical trades for a wallet

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `token_mint` (string, optional): Filter by specific token mint
- `limit` (number, optional, default 30): Number of records to return
- `offset` (number, optional, default 0): Number of records to skip

**Example Requests**:

```
GET /api/trade-history?wallet_address=FN4...pump
GET /api/trade-history?wallet_address=FN4...pump&token_mint=xxxxxxx&limit=30&offset=0

```

**Success Response**:

```json
[
  {
    "wallet_address": "FN4...pump",
    "token_mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
    "base_mint": "So11111111111111111111111111111111111111112",
    "timestamp": 1213413212,
    "pair_label": "WORK/SOL",
    "trade_type": "Market", // "Market", "Limit"
    "side": "Buy", // "Buy", "Sell"
    "price": 0.0002,
    "token_amount": 313322000000,
    "token_ui_amount": 313322.0,
    "base_amount": 4300000000,
    "base_ui_amount": 0.43,
    "fee_ui_amount": 0.001,
    "token_metadata": {
      "name": "Superstack",
      "symbol": "WORK",
      "decimals": 6,
      "image_url": "https://static.jup.ag/jup/icon.png",
      "chain": "Solana"
    }
  }
]

```

### 4.5 Get Position History

**Route**: `/api/position-history`

**Method**: GET
**Description**: Get historical closed positions for a wallet

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `limit` (number, optional, default 30): Number of records to return
- `offset` (number, optional, default 0): Number of records to skip
- `sort_by` (string, optional, default "close_time"): Sort field ("open_time", "close_time", "pnl", "pnl_percentage")

**Example Requests**:

```
GET /api/position-history?wallet_address=FN4...pump
GET /api/position-history?wallet_address=FN4...pump&limit=30&offset=0&sort_by=pnl

```

**Success Response**:

```json
[
  {
    "wallet_address": "DDGniHdkFbtuh5ufizmoNeePVeaszZBePjZzjV5pvMwb",
    "token_mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
    "token_metadata": {
      "name": "Superstack",
      "symbol": "WORK",
      "decimals": 6,
      "image_url": "string",
      "chain": "Solana"
    },
    "bought_amount": 167000000000,
    "bought_ui_amount": 167000.0,
    "sold_amount": 167000000000,
    "sold_ui_amount": 167000.0,
    "cost_usd": 2.0,
    "earnings_usd": 1.8,
    "pnl_usd": -0.2,
    "pnl_percentage": -0.1,
    "buy_operations": [
      {
        "is_buy": true,
        "token_amount": 100000000,
        "base_amount": 167000000000,
        "base_mint": "So11111111111111111111111111111111111111112",
        "base_decimals": 9,
        "tx_sig": "3LLRM3yTP...",
        "timestamp": **********
      }
    ],
    "sell_operations": [
      {
        "is_buy": false,
        "token_amount": 100000000,
        "base_amount": 167000000000,
        "base_mint": "So11111111111111111111111111111111111111112",
        "base_decimals": 9,
        "tx_sig": "3LLRM3yTP...",
        "timestamp": **********
      }
    ],
    "open_time": **********,
    "close_time": **********
  }
]

```

### 4.6 Get PnL Statistics

**Route**: `/api/pnl/stats`

**Method**: GET
**Description**: Get profit and loss statistics for a wallet over a specified time range

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `range` (string, required): Time range ("5m", "1h", "6h", "24h", "3d", "7d", "30d", "1y", "All")

**Example Requests**:

```
GET /api/pnl/stats?wallet_address=FN4...pump&range=7d

```

**Success Response**:

```json
{
  "accountValue": 5.0,
  "realizedPnl": 2.0,
  "realizedPnlPercentage": 0.2,
  "unrealizedPnl": 1.0,
  "unrealizedPnlPercentage": -0.4,
  "revenue": 12.0,
  "spent": 9.0,
  "totalBought": 15.0,
  "totalSold": 10.0,
  "lastTxnDate": "2025-05-09",
  "accountValues": [
    {
      "timestamp": **********,
      "value": 5.0,
      "valuePercentage": 0.1
    }
  ],
  "pnlViews": [
    {
      "timestamp": **********,
      "pnl": 0.3,
      "pnlPercentage": 0.1
    }
  ]
}

```

### 4.7 Get PnL Share

**Route**: `/api/pnl/share`

**Method**: GET
**Description**: Get PnL data for sharing purposes for a specific token, including the user's selected background image
**Authentication**: Wallet Authentication Required

**Query Parameters**:

- `wallet_address` (string, required): Wallet address
- `token_mint` (string, required): Token mint address

**Success Response**:

```json
{
  "tokenMint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
  "name": "Superstack",
  "symbol": "WORK",
  "imageUrl": "string",
  "pnlUsd": 0.3,
  "pnlPercentage": 0.1,
  "backgroundImageUrl": "https://static.test.superstack.xyz/public/pnl-share-images/default-blue.jpg"
}

```

**Response Fields**:

- `tokenMint`: Token mint address
- `name`: Token name
- `symbol`: Token symbol
- `imageUrl`: Token logo image URL
- `pnlUsd`: Profit/Loss in USD
- `pnlPercentage`: Profit/Loss percentage
- `backgroundImageUrl`: User's selected background image URL for PnL sharing (null if no image selected)

### 4.8 Get Wallet Activity

**Route**: `/api/activity`

**Method**: GET
**Description**: Get wallet activity history including swaps, limit orders, transfers, and other transactions
**Authentication**: Required (`X-Wallet-Address` header)

**Activity Types**:
- `swap`: Token exchanges through DEXs (Jupiter, Raydium, etc.) - includes both token and base fields
- `limit_order`: Limit order trades - includes both token and base fields
- `send`: Outgoing token transfers - only includes token fields, base fields are null
- `receive`: Incoming token transfers - only includes token fields, base fields are null
- `perp_trade`: Generic perpetual trading activity
- `open_position`: Opening a perpetual position (when reduceOnly=false)
- `close_position`: Closing a perpetual position (when reduceOnly=true)
- `deposit`: Deposit activity (funds added to account)
- `withdraw`: Withdrawal activity (funds removed from account)
- `transfer`: Transfer activity (funds moved between accounts)
- `liquidation`: Liquidation activity (forced position closure)
- `other`: Other blockchain activities - field population varies

**Query Parameters**:

- `wallet_address` (string, required): Comma-separated list of wallet addresses to query (supports both Solana and EVM addresses)
- `limit` (number, optional, default 50): Number of activities to return (max 200)
- `before_timestamp` (number, optional): Return activities before this timestamp (for pagination)
- `include_other` (boolean, optional, default false): Whether to include "other" type activities in results

**Example Requests**:

```
# Single address query
GET /api/activity?wallet_address=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM

# Multiple addresses query (cross-chain support)
GET /api/activity?wallet_address=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM,******************************************

# EVM address only
GET /api/activity?wallet_address=******************************************

# With pagination and filters
GET /api/activity?wallet_address=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM&limit=50&before_timestamp=**********&include_other=true
```

**Success Response**:

```json
{
  "data": [
    {
      "tx_signature": "4bwJL6EUzK2KWhCpJUHC33WBN4T3o6fLHJpnDJosLXYB74rwt9tmmvtDGsCUMoUbR7BicumMk8Ck6Q2qZtRiFomS",
      "activity_type": "swap",
      "timestamp": 1703980800,
      "block_time": 1703980800,
      "slot": 12345,
      "chain": 0,
      "usd_value": 100.0,
      "category": "Trading",
      "token": "USDC",
      "amount": "1.0",
      "token_name": "USD Coin",
      "token_symbol": "USDC",
      "token_image": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png",
      "token_ui_amount": 1.0,
      "base_name": "Wrapped SOL",
      "base_symbol": "SOL",
      "base_image": null,
      "base_ui_amount": 1.0,
      "from_address": null,
      "to_address": null,
      "sell_token": "USDC",
      "sell_amount": "1.0",
      "buy_token": "SOL",
      "buy_amount": "1.0",
      "price": "1.0",
      "trading_pair": "USDC/SOL",
      "order_type": "Market",
      "side": "Sell",
      "fee": "0.005",
      "fee_token": "SOL",
      "token_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
      "token_decimals": 6,
      "token_amount": 1000000,
      "base_mint": "So11111111111111111111111111111111111111112",
      "base_decimals": 9,
      "base_amount": 1000000000,
      "metadata": {
        "fee": 5000,
        "event_type": "aggregator_v6_swap",
        "amm": "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"
      }
    },
    {
      "tx_signature": "33tT6S6xHKhCGC7o3oNdVvjgEftY4a6ihUtf67sF6UBVtHyYvScxnjjoNdAaq8ct8p9ByZTFDW8CiJnzWvGMEme8",
      "activity_type": "send",
      "timestamp": 1751868785,
      "block_time": 1751868785,
      "slot": 351661764,
      "chain": 0,
      "usd_value": null,
      "category": "Transfer",
      "exchange": null,
      "token": "SOL",
      "amount": "0.006361821",
      "token_name": "Wrapped SOL",
      "token_symbol": "SOL",
      "token_image": null,
      "token_ui_amount": 0.006361821,
      "base_name": null,
      "base_symbol": null,
      "base_image": null,
      "base_ui_amount": null,
      "from_address": "7AvkHFPsiD1ZDL4sEV5cBYpQjiT8JpfHrWRS9vFtKX3D",
      "to_address": "34HsSsEJCwVVuWL8wnkSnuSWpYet1szBF9RwdKHUeRqj",
      "sell_token": null,
      "sell_amount": null,
      "buy_token": null,
      "buy_amount": null,
      "price": null,
      "trading_pair": null,
      "order_type": null,
      "side": null,
      "fee": "0.000006519",
      "fee_token": "SOL",
      "token_mint": "So11111111111111111111111111111111111111112",
      "token_decimals": 9,
      "token_amount": 6361821,
      "base_mint": null,
      "base_decimals": null,
      "base_amount": null,
      "metadata": {
        "direction": "out",
        "enhanced_parsing": true,
        "event_type": "token_transfer",
        "fee": 6519,
        "mint_to_address": "34HsSsEJCwVVuWL8wnkSnuSWpYet1szBF9RwdKHUeRqj",
        "receive_from_address": "7AvkHFPsiD1ZDL4sEV5cBYpQjiT8JpfHrWRS9vFtKX3D",
        "send_to_address": "34HsSsEJCwVVuWL8wnkSnuSWpYet1szBF9RwdKHUeRqj",
        "transfer_type": "transfer_checked",
        "ui_amount": 0.006361821
      }
    }
  ],
  "has_more": false,
  "limit": 50,
  "next_timestamp": null
}
```

**Response Fields**:

**Core Fields**:
- `tx_signature` (string): Transaction signature/hash
- `activity_type` (string): Type of activity ("swap", "limit_order", "send", "receive", "perp_trade", "open_position", "close_position", "deposit", "withdraw", "transfer", "liquidation", "other")
- `timestamp` (number): Unix timestamp of the transaction
- `block_time` (number, optional): Block timestamp
- `slot` (number, optional): Blockchain slot number
- `chain` (number): Chain identifier (0=Solana, 1=Hypercore, 2=HyperEVM)
- `usd_value` (number, optional): USD value of the transaction

**Display Fields**:
- `category` (string): Enhanced activity category for UI display:
  - For perp trades: `"open_position"` (reduceOnly=false), `"close_position"` (reduceOnly=true), or `"perp_trade"` (generic)
  - For other types: `"spot_trade"`, `"deposit"`, `"withdraw"`, `"transfer"`, `"liquidation"`, `"other"`
- `exchange` (string, optional): Exchange name (e.g., "Jupiter", "Raydium")
- `token` (string): Primary token symbol for display
- `amount` (string): Formatted amount for display

**Token Information**:
- `token_name` (string, optional): Token name
- `token_symbol` (string, optional): Token symbol
- `token_image` (string, optional): Token image URL
- `token_ui_amount` (number, optional): Human-readable token amount
- `base_name` (string, optional): **Base token name - only for swap/limit_order**
- `base_symbol` (string, optional): **Base token symbol - only for swap/limit_order**
- `base_image` (string, optional): **Base token image - only for swap/limit_order**
- `base_ui_amount` (number, optional): **Human-readable base amount - only for swap/limit_order**

**Address Information**:
- `from_address` (string, optional): Source address for transfers
- `to_address` (string, optional): Destination address for transfers

**Trading Information**:
- `sell_token` (string, optional): Token being sold (for trades)
- `sell_amount` (string, optional): Amount being sold (for trades)
- `buy_token` (string, optional): Token being bought (for trades)
- `buy_amount` (string, optional): Amount being bought (for trades)
- `price` (string, optional): **Execution price** - actual trade price when order was filled
- `trading_pair` (string, optional): Trading pair (e.g., "USDC/SOL")
- `order_type` (string, optional): Order type ("Market", "Limit")
- `side` (string, optional): Trade side ("Buy", "Sell")
- `leverage` (string, optional): Leverage multiplier for perp trades (e.g., "10x")
- `size` (string, optional): Contract size for perp trades (e.g., "0.1") - extracted from `order_parameters.orders[0].s` or `order_response[0].totalSz`
- `limit_price` (string, optional): **Limit price** - price set by user when placing limit order (e.g., "50000.0") - extracted from `limit_price` or `order_parameters.orders[0].p`

**Fee Information**:
- `fee` (string, optional): Transaction fee amount
- `fee_token` (string, optional): Fee token symbol

**Legacy Fields** (for backward compatibility):
- `token_mint` (string, optional): Token mint address
- `token_decimals` (number, optional): Token decimal places (SOL: 9, USDC: 6, most EVM tokens: 18)
- `token_amount` (number, optional): Raw token amount (in smallest unit based on token_decimals)
- `base_mint` (string, optional): Base token mint address
- `base_decimals` (number, optional): Base token decimal places
- `base_amount` (number, optional): Raw base amount
- `metadata` (object, optional): Additional transaction metadata

**Pagination Fields**:
- `has_more` (boolean): Whether more activities are available
- `limit` (number): Number of activities returned
- `next_timestamp` (number, optional): Timestamp for next page

**Example Response for Perp Trade (Market Order)**:
```json
{
  "data": [
    {
      "tx_signature": "0x123abc...",
      "activity_type": "perp_trade",
      "category": "open_position",
      "timestamp": **********000,
      "amount": "-4.2 USDC",
      "leverage": "10x",
      "size": "0.1",
      "trading_pair": "BTC/USDC",
      "order_type": "Market",
      "side": "Buy",
      "price": "50000.0",
      "limit_price": null,
      "token_symbol": "USDC",
      "token_ui_amount": 4.2,
      "usd_value": 4.2,
      "chain": 1
    }
  ],
  "has_more": false,
  "limit": 50,
  "next_timestamp": null
}
```

**Example Response for Perp Trade (Limit Order)**:
```json
{
  "data": [
    {
      "tx_signature": "0x456def...",
      "activity_type": "perp_trade",
      "category": "open_position",
      "timestamp": 1640995300000,
      "amount": "-5.0 USDC",
      "leverage": "5x",
      "size": "0.1",
      "trading_pair": "SOL/USDC",
      "order_type": "Limit",
      "side": "Buy",
      "price": "180.0",
      "limit_price": "175.0",
      "token_symbol": "SOL",
      "token_decimals": 9,
      "token_ui_amount": 0.1,
      "usd_value": 18.0,
      "chain": 1
    }
  ],
  "has_more": false,
  "limit": 50,
  "next_timestamp": null
}
```

**Important Notes about Trading Fields**:

**Price vs Limit Price**:
- `price`: The actual execution price when a trade was filled (market price at execution time)
- `limit_price`: The price set by the user when placing a limit order (desired price)
- For market orders: only `price` is populated, `limit_price` is `null`
- For limit orders: both `price` (execution) and `limit_price` (user-set) may be populated

**Token Decimals**:
- `token_decimals` represents the number of decimal places for the token
- Common values: SOL (9 decimals), USDC/USDT (6 decimals), most EVM tokens (18 decimals)
- `token_amount` is the raw amount in the smallest unit (e.g., lamports for SOL, wei for ETH)
- To get the UI amount: `ui_amount = token_amount / (10 ^ token_decimals)`

**Base Fields**:
- **Base fields (`base_mint`, `base_name`, `base_symbol`, `base_image`, etc.) are only populated for swap and limit_order activities**
- **For send/receive activities, base fields will be `null` as these are single-token transfers, not token exchanges**
- Base fields represent the "quote" token in a trading pair (e.g., SOL or USDC in a TOKEN/SOL or TOKEN/USDC pair)
- This design correctly reflects the semantic difference between token swaps (which involve two tokens) and transfers (which involve only one token)

### 4.9 Get Wallet Activity Statistics

**Route**: `/api/activity/stats`

**Method**: GET
**Description**: Get aggregated statistics for wallet activity
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:

- `wallet_address` (string, required): Comma-separated list of wallet addresses for statistics (supports both Solana and EVM addresses)

**Example Requests**:

```
# Single address statistics
GET /api/activity/stats?wallet_address=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM

# Multiple addresses statistics (cross-chain support)
GET /api/activity/stats?wallet_address=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM,******************************************

# EVM address only statistics
GET /api/activity/stats?wallet_address=******************************************
```

**Success Response**:

```json
{
  "total_activities": 150,
  "swap_count": 120,
  "limit_order_count": 25,
  "transfer_count": 3,
  "other_count": 2,
  "total_usd_volume": 15000.0,
  "first_activity_timestamp": 1703980800,
  "last_activity_timestamp": 1704067200
}
```

**Response Fields**:

- `total_activities` (number): Total number of activities
- `swap_count` (number): Number of swap transactions
- `limit_order_count` (number): Number of limit order transactions
- `receive_count` (number): Number of receive transactions
- `send_count` (number): Number of send transactions
- `other_count` (number): Number of other transactions
- `total_usd_volume` (number): Total USD volume across all activities
- `first_activity_timestamp` (number, optional): Timestamp of first activity
- `last_activity_timestamp` (number, optional): Timestamp of most recent activity

### 4.10 Update Wallet Activity

**Route**: `/api/activity/update`

**Method**: POST
**Description**: Trigger an update of wallet activity data from blockchain
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:

- `wallet_address` (string, required): Wallet address to update

**Example Request**:

```
POST /api/activity/update?wallet_address=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
```

**Success Response**:

```json
{
  "message": "Activity update completed",
  "new_activities": 5,
  "total_activities": 155
}
```

**Response Fields**:

- `message` (string): Status message
- `new_activities` (number): Number of new activities found and added
- `total_activities` (number): Total activities after update

**Notes**:

- The system automatically filters out spam/phishing transactions (very small amounts < 1e-6)
- Activity data is updated incrementally based on the last known timestamp
- The update process waits up to 10 seconds for new data before responding
- Duplicate transactions are automatically handled using the unique constraint on (wallet_address, tx_signature, chain)
- **Background Updates**: Only Solana addresses trigger background activity updates; EVM addresses do not require real-time refresh
- **Multi-Address Queries**: When querying multiple addresses, only the first valid Solana address will trigger a background update to avoid duplicate work

### 4.11 Record HyperEVM Activity

**Route**: `/api/record/activity`

**Method**: POST
**Description**: Record HyperEVM wallet activity data including perpetual orders, spot orders, deposits, and withdrawals
**Authentication**: Required (`X-Wallet-Address` header for referral rewards)

**Headers**:
- `Content-Type: application/json` (required)
- `X-Wallet-Address: <SOL_WALLET_ADDRESS>` (optional, used for referral rewards tracking)

**Request Body Structure**:

```json
{
  "wallet_address": "******************************************",
  "activities": {
    "userFills": null,
    "userNonFundingLedgerUpdates": null,
    "perpOrders": [...],
    "spotOrders": [...],
    "deposits": [...],
    "withdraws": [...]
  }
}
```

**Activity Types**:

#### Perpetual Orders (`perpOrders`)

```json
{
  "time": 1703980800000,
  "hash": "0xa95ca3c75201de2891f80416af61da0110004a5e8453a577a3de34354530b617",
  "order": {
    "type": "normal",
    "orderType": "Market",
    "asset": "BTC",
    "isBuy": true,
    "sz": "0.1",
    "limitPx": "50000.0",
    "reduceOnly": false,
    "timeInForce": "Gtc",
    "postOnly": false,
    "cloid": "client_order_123",
    "twap": {
      "m": 10,
      "t": 60000
    }
  },
  "symbol": "BTC/USDC",
  "tokenImage": "https://example.com/btc.png",
  "leverage": 10,
  "leverageType": "Cross",
  "isMainnet": true,
  "notionalValue": "5000.0",
  "fee": "2.5",
  "feeToken": "USDC"
}
```

#### Spot Orders (`spotOrders`)

```json
{
  "time": 1703980900000,
  "hash": "0xb95ca3c75201de2891f80416af61da0110004a5e8453a577a3de34354530b618",
  "order": {
    "type": "normal",
    "orderType": "Limit",
    "asset": "ETH",
    "isBuy": false,
    "sz": "1.0",
    "limitPx": "3000.0",
    "timeInForce": "Gtc",
    "postOnly": true,
    "cloid": "spot_order_456"
  },
  "symbol": "ETH/USDC",
  "tokenImage": "https://example.com/eth.png",
  "notionalValue": "3000.0",
  "fee": "1.5",
  "feeToken": "USDC"
}
```

#### Deposits (`deposits`)

```json
{
  "time": 1703981000000,
  "hash": "0xc95ca3c75201de2891f80416af61da0110004a5e8453a577a3de34354530b619",
  "amount": "1000.0",
  "token": "USDC",
  "tokenImage": "https://example.com/usdc.png",
  "toAddress": "******************************************",
  "fromAddress": null,
  "fee": "0.0",
  "feeToken": "USDC",
  "depositType": "direct",
  "source": "binance",
  "network": "ethereum",
  "confirmations": 12,
  "blockNumber": 12345678,
  "gasUsed": "21000",
  "gasPrice": "20000000000",
  "metadata": {}
}
```

#### Withdrawals (`withdraws`)

```json
{
  "time": 1703981100000,
  "hash": "0xd95ca3c75201de2891f80416af61da0110004a5e8453a577a3de34354530b620",
  "amount": "500.0",
  "token": "USDC",
  "tokenImage": "https://example.com/usdc.png",
  "fromAddress": "******************************************",
  "toAddress": "******************************************",
  "fee": "2.0",
  "feeToken": "USDC",
  "nonce": 12345,
  "withdrawType": "external",
  "destination": "coinbase",
  "network": "ethereum",
  "estimatedArrival": 1703982000,
  "status": "pending",
  "confirmations": 15,
  "blockNumber": 12345679,
  "gasUsed": "35000",
  "gasPrice": "25000000000",
  "metadata": {}
}
```

**Example Request**:

```bash
curl -X POST https://api.superstack.xyz/api/record/activity \
  -H "Content-Type: application/json" \
  -H "X-Wallet-Address: 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM" \
  -d '{
    "wallet_address": "******************************************",
    "activities": {
      "userFills": null,
      "userNonFundingLedgerUpdates": null,
      "perpOrders": [
        {
          "time": 1703980800000,
          "hash": "0xa95ca3c75201de2891f80416af61da0110004a5e8453a577a3de34354530b617",
          "order": {
            "type": "normal",
            "orderType": "Market",
            "asset": "BTC",
            "isBuy": true,
            "sz": "0.1"
          },
          "symbol": "BTC/USDC",
          "leverage": 10,
          "leverageType": "Cross",
          "isMainnet": true,
          "notionalValue": "5000.0"
        }
      ],
      "spotOrders": null,
      "deposits": [
        {
          "time": 1703981000000,
          "hash": "0xc95ca3c75201de2891f80416af61da0110004a5e8453a577a3de34354530b619",
          "amount": "1000.0",
          "token": "USDC",
          "toAddress": "******************************************"
        }
      ],
      "withdraws": null
    }
  }'
```

**Success Response**:

```json
{
  "success": true,
  "message": "Activities recorded successfully",
  "processed_count": 2,
  "failed_count": 0,
  "errors": null,
  "referral_rewards_created": 1
}
```

**Error Response**:

```json
{
  "success": false,
  "message": "Validation failed",
  "processed_count": 0,
  "failed_count": 2,
  "errors": [
    "PerpOrder[0]: hash cannot be empty",
    "Deposit[0]: invalid amount format"
  ]
}
```

**Field Specifications**:

**Required Fields**:
- `time`: Timestamp in milliseconds
- `hash`: Transaction hash (must be unique)
- `wallet_address`: HyperEVM wallet address

**Order Fields**:
- `order.type`: "normal" | "twap"
- `order.orderType`: "Market" | "Limit" | "Stop" | "StopLimit"
- `order.asset`: Asset symbol (e.g., "BTC", "ETH")
- `order.isBuy`: Boolean indicating buy/sell direction
- `order.sz`: Order size as string
- `leverageType`: "Cross" | "Isolated" (for perp orders)

**Deposit/Withdraw Fields**:
- `amount`: Amount as string
- `token`: Token symbol
- `toAddress`/`fromAddress`: Ethereum addresses
- `fee`: Fee amount as string (required for withdraws)
- `nonce`: Withdrawal nonce (required for withdraws)

**Notes**:

- **Referral Integration**: If `X-Wallet-Address` header is provided with a valid SOL address, trading activities (perp_trade, spot_trade) will automatically create referral reward records
- **Data Validation**: All numeric fields must be provided as strings to ensure precision
- **Duplicate Handling**: Activities with duplicate transaction hashes are automatically filtered out
- **Batch Processing**: Multiple activity types can be submitted in a single request
- **Error Handling**: Partial success is supported - some activities may succeed while others fail
- **Chain Support**: Currently supports HyperEVM (chain ID: 2) activities only

---

### 5. Price and Market Data

### 5.1 Get SOL Price

**Route**: `/api/sol-price`

**Method**: GET
**Description**: Get current SOL price in USD

**Success Response**:

```json
{
  "token_mint": "So11111111111111111111111111111111111111112",
  "usd_price": 132.623080305
}

```

### 5.2 Get Market Tokens

**Route**: `/api/markets/tokens`

**Method**: GET
**Description**: Get market tokens with filtering and pagination

**Query Parameters**:

- `limit` (number, optional): Number of tokens to return
- `offset` (number, optional): Number of tokens to skip
- `time` (string, optional): Time period for statistics ("5m", "1h", "6h", "24h", "3d", "7d")
- `trend` (string, optional): Trend filter ("Trending", "New", "Popular", "Gainers", "Losers", "Top")
- `chain` (string, optional): Blockchain filter ("solana", "hypercore", "hyperevm"). Defaults to "solana"
- `has_social` (boolean, optional): Filter tokens with social media links
- `has_twitter` (boolean, optional): Filter tokens with Twitter
- `has_telegram` (boolean, optional): Filter tokens with Telegram
- `has_website` (boolean, optional): Filter tokens with website

**Success Response**:

```json
[
  {
    "tokenMint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
    "name": "Superstack",
    "symbol": "WORK",
    "decimals": 6,
    "imageUrl": "string",
    "chain": "solana",
    "dex": "unknown",
    "poolAddress": "61DtTvjeTuwcAdtGsiQLvBRqrJ56FdiNdhNcEun9c1ci",
    "poolDex": "meteora",
    "twitter": "string",
    "telegram": "string",
    "website": "string",
    "priceUsd": 0.0001,
    "marketCapUsd": 100000.0,
    "fdvUsd": 100000.0,
    "volumeUsd": 50000.0,
    "liquidityUsd": 25000.0,
    "priceChange": 0.15,
    "candles": {..}
  }
]

```

**Response Fields**:
- `tokenMint` (string): Token mint address
- `name` (string): Token name
- `symbol` (string): Token symbol
- `decimals` (number): Token decimal places
- `imageUrl` (string, optional): Token image URL
- `chain` (string): Blockchain network ("solana", "hypercore", "hyperevm")
- `dex` (string): Token creation DEX (for backward compatibility)
- `poolAddress` (string, optional): Default pool address for trading
- `poolDex` (string, optional): Default pool's actual DEX for trading
- `twitter` (string, optional): Twitter URL
- `telegram` (string, optional): Telegram URL
- `website` (string, optional): Website URL
- `priceUsd` (number): Current USD price
- `marketCapUsd` (number): Market capitalization in USD
- `fdvUsd` (number): Fully diluted valuation in USD
- `volumeUsd` (number): Trading volume in USD (based on time parameter)
- `liquidityUsd` (number): Liquidity in USD
- `priceChange` (number): Price change percentage (based on time parameter)

**Notes**:
- refer `candles` field to `/api/candle/snapshot`
- default to now-24h to now, interval = 1h
- `poolAddress` and `poolDex` provide the default trading pool information
- Use `poolDex` for accurate trading DEX info, `dex` shows token creation source
- `poolAddress` can be used for WebSocket subscriptions and trading operations

### 5.3 Search Tokens

**Route**: `/api/markets/search`

**Method**: GET
**Description**: Search for tokens by keyword or get trending tokens

**Query Parameters**:

- `context` (string, optional): Search keyword (if empty, returns trending tokens)

**Success Response**:

```json
{
  "tokens": [
    {
      "tokenMint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
      "name": "Superstack",
      "symbol": "WORK",
      "imageUrl": "string",
      "chain": 0,
      "twitter": "string",
      "telegram": "string",
      "website": "string",
      "price": 0.0001,
      "marketCap": 100000.0,
      "liquidity": 25000.0,
      "priceChange24h": 0.15,
      "volume24h": 50000.0
    }
  ],
  "pools": [
    {
      "tokenMint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
      "address": "pool_address_here",
      "name": "Superstack",
      "symbol": "WORK",
      "imageUrl": "string",
      "chain": 0,
      "baseSymbol": "SOL",
      "pairLabel": "WORK/SOL",
      "dex": "Raydium",
      "poolType": "CLMM",
      "creationTime": **********,
      "price": 0.0001,
      "priceChange24h": 0.15,
      "volume24h": 50000.0,
      "marketCap": 100000.0,
      "liquidity": 25000.0
    }
  ]
}

```

### 5.4 Get Market Depth Chart

**Route**: `/api/depth/{pool_address}`

**Method**: GET
**Description**: Get market depth chart data for a trading pool
**Authentication**: None required (Public API)

**Path Parameters**:

- `pool_address` (string, required): Pool address for the trading pair

**Example Request**:

```
GET /api/depth/61DtTvjeTuwcAdtGsiQLvBRqrJ56FdiNdhNcEun9c1ci
```

**Success Response**:

```json
{
  "data": [
    {
      "priceUsd": 0.001234,
      "priceNative": 0.000567,
      "marketCapUsd": 1234567.89,
      "marketCapNative": 567890.12,
      "liquidityUsd": 10000.0
    },
    {
      "priceUsd": 0.001250,
      "priceNative": 0.000575,
      "marketCapUsd": 1250000.0,
      "marketCapNative": 575000.0,
      "liquidityUsd": 8500.0
    }
  ]
}
```

**Response Fields**:

- `priceUsd` (number): Price level in USD
- `priceNative` (number): Price level in native token (SOL)
- `marketCapUsd` (number): Market cap at this price level in USD
- `marketCapNative` (number): Market cap at this price level in native token
- `liquidityUsd` (number): Available liquidity at this price level in USD

**Notes**:

- Returns depth data for ±40 price levels around current price (2% steps)
- Data is based on real order book when available, otherwise uses liquidity distribution
- All returned values are guaranteed to be positive (> 0)
- Data is sorted by price in ascending order

**Error Responses**:

```json
{
  "error": "Pool not found"
}
```

```json
{
  "error": "Failed to get pool metadata"
}
```

### 5.5 Get Trench Tokens

**Route**: `/api/trench/tokens`

**Method**: GET
**Description**: Get tokens from Trench (new/trending tokens)

**Query Parameters**: Similar to market tokens with additional filters:

- `bonding_curve_min` (number, optional): Minimum bonding curve progress
- `bonding_curve_max` (number, optional): Maximum bonding curve progress
- `creation_time_min` (number, optional): Minimum creation timestamp
- `creation_time_max` (number, optional): Maximum creation timestamp
- `dev_holding_min` (number, optional): Minimum dev holding percentage
- `dev_holding_max` (number, optional): Maximum dev holding percentage
- `holders_min` (number, optional): Minimum number of holders
- `holders_max` (number, optional): Maximum number of holders
- `snipers_min` (number, optional): Minimum number of snipers
- `snipers_max` (number, optional): Maximum number of snipers

**Success Response**:

```json
[
  {
    "tokenMint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
    "name": "Superstack",
    "symbol": "WORK",
    "imageUrl": "string",
    "chain": "Solana",
    "dex": "Raydium",
    "dexUrl": "<https://raydium.io>",
    "bondingCurveProgress": 0.85,
    "twitter": "string",
    "telegram": "string",
    "website": "string",
    "marketCapUsd": 100000.0,
    "volumeUsd": 50000.0,
    "top10Holding": 0.3,
    "devHolding": 0.1,
    "devSold": 0.05,
    "sniperHolding": 0.2,
    "holders": 1500,
    "snipers": 50,
    "creationTime": **********
  }
]

```

---

### 6. Token Information

### 6.1 Get Token Info

**Route**: `/api/token/info/{token_address}`

**Method**: GET
**Description**: Get detailed token information

**Path Parameters**:

- `token_address` (string, required): Token mint address

**Query Parameters**:

- `pool_address` (string, optional): Specific pool address

**Success Response**:

```json
{
  "tokenMint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
  "name": "Superstack",
  "symbol": "WORK",
  "description": "AI-powered work automation token",
  "imageUrl": "string",
  "twitter": "string",
  "telegram": "string",
  "website": "string",
  "launchDex": "Raydium",
  "launchDexUrl": "<https://raydium.io>",
  "decimals": 6,
  "totalSupply": 1000.0,
  "circulatingSupply": 800.0,
  "chain": 0,
  "isTrenchToken": true,
  "defaultPoolIndex": 0,
  "currentPoolIndex": 0,
  "isUserSelected": false,
  "pools": [
    {
      "address": "pool_address_here",
      "pairLabel": "WORK/SOL",
      "token1Mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
      "token2Mint": "So11111111111111111111111111111111111111112",
      "creationTime": **********,
      "dex": "Raydium",
      "poolType": "CLMM",
      "priceUsd": 0.0001,
      "priceNative": 0.001,
      "priceChange": 0.15,
      "marketCapUsd": 100000.0,
      "fdvUsd": 100000.0,
      "volumeUsd": 50000.0,
      "liquidityUsd": 25000.0,
      "isDefault": true
    },
    {
      "address": "another_pool_address",
      "pairLabel": "WORK/USDC",
      "token1Mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
      "token2Mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
      "creationTime": 1744642300,
      "dex": "Orca",
      "poolType": "Whirlpool",
      "priceUsd": 0.0001,
      "priceNative": 0.0001,
      "priceChange": 0.12,
      "marketCapUsd": 100000.0,
      "fdvUsd": 100000.0,
      "volumeUsd": 30000.0,
      "liquidityUsd": 15000.0,
      "isDefault": false
    }
  ],
  "currentPoolStatistic": {
    "priceUsd": 0.0001,
    "marketCapUsd": 100000.0,
    "liquidityUsd": 25000.0,
    "volumeUsd": 50000.0,
    "priceChange": 0.15
  }
}

```

**Response Fields**:
- `tokenMint` (string): Token mint address
- `name` (string): Token name
- `symbol` (string): Token symbol
- `description` (string, optional): Token description
- `imageUrl` (string, optional): Token image URL
- `twitter` (string, optional): Twitter URL
- `telegram` (string, optional): Telegram URL
- `website` (string, optional): Website URL
- `launchDex` (string, optional): DEX where token was launched
- `launchDexUrl` (string, optional): URL to the launch DEX
- `decimals` (number): Token decimal places
- `totalSupply` (number): **Total token supply in UI amount** (normalized by dividing raw supply by 10^decimals)
- `circulatingSupply` (number): **Circulating token supply in UI amount** (normalized by dividing raw supply by 10^decimals)
- `chain` (string): Blockchain network
- `isTrenchToken` (boolean): Whether this is a trench token
- `defaultPoolIndex` (number): Index of the default pool
- `currentPoolIndex` (number): Index of the currently selected pool
- `isUserSelected` (boolean): Whether pool was user-selected
- `pools` (array): Array of available pools for this token
- `currentPoolStatistic` (object): Statistics for the current pool

**⚠️ Breaking Change Notice**:
As of the latest version, `totalSupply` and `circulatingSupply` fields now return **normalized decimal values** (UI amounts) instead of raw integer values. For example, a token with 6 decimals and raw supply of 1,000,000,000 will now return `1000.0` instead of `1000000000`.

### 6.2 Get Token Details

**Route**: `/api/token/details/{token_address}`

**Method**: GET
**Description**: Get basic token details (supply, market cap, etc.)

**Path Parameters**:

- `token_address` (string, required): Token mint address

**Success Response**:

```json
{
  "mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
  "name": "Superstack",
  "symbol": "WORK",
  "marketCapUsd": 100000.0,
  "fdvUsd": 100000.0,
  "circulatingSupply": 800.0,
  "totalSupply": 1000.0
}

```

**Response Fields**:
- `mint` (string): Token mint address
- `name` (string): Token name
- `symbol` (string): Token symbol
- `marketCapUsd` (number): Market capitalization in USD
- `fdvUsd` (number): Fully diluted valuation in USD
- `circulatingSupply` (number): **Circulating supply in UI amount** (normalized by decimals)
- `totalSupply` (number): **Total supply in UI amount** (normalized by decimals)

**Note**: This endpoint is deprecated and returns HTTP 410 Gone.

### 6.3 Get Token Meme Statistics

**Route**: `/api/token/meme-scan/{token_address}`

**Method**: GET
**Description**: Get meme token security and holder statistics

**Path Parameters**:

- `token_address` (string, required): Token mint address

**Note**: This endpoint may not be available for all tokens. Returns 404 if meme statistics are not found.

**Success Response**:

```json
{
  "tokenMint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
  "lpBurned": 1.0,
  "isMintable": false,
  "isFreezable": false,
  "isMutable": false,
  "holders": 1500,
  "insiders": 10,
  "snipers": 50,
  "bot": 5,
  "devHolding": 0.1,
  "top10Holding": 0.3,
  "insiderHolding": 0.05,
  "sniperHolding": 0.2
}

```

**Error Response**:

```json
{
  "error": "Meme statistics not found"
}

```

### 6.4 Get Token Trades

**Route**: `/api/token/trades/{token_address}`

**Method**: GET
**Description**: Get recent trades for a token

**Path Parameters**:

- `token_address` (string, required): Token mint address

**Query Parameters**:

- `pool_address` (string, required): Pool address
- `limit` (number, optional, default 100): Number of trades to return
- `maker_type` (string, optional): Filter by maker type ("all", "dev", "snipers")
- `min_usd_amount` (number, optional): Minimum trade amount in USD
- `max_usd_amount` (number, optional): Maximum trade amount in USD
- `maker_address` (string, optional): Filter by specific maker address

**Success Response**:

```json
[
  {
    "txSig": "3LLRM3yTP...",
    "maker": "wallet_address_here",
    "makerType": "Whale", // "Plankton", "Fish", "Shrimp", "Dolphin", "Whale"
    "isBuy": true,
    "tokenUiAmount": 1000.0,
    "baseUiAmount": 0.1,
    "usd": 13.26,
    "solAmount": 0.078, // Equivalent SOL amount regardless of base token
    "baseTokenSymbol": "SOL", // Base token symbol: "SOL", "USDC", "USDT", or "UNKNOWN"
    "priceUsd": 0.0001326,
    "priceNative": 0.001,
    "marketCapUsd": 132600.0,
    "timestamp": **********
  }
]

```

### 6.5 Get Token Holders

**Route**: `/api/token/holders/{token_address}`

**Method**: GET
**Description**: Get token holders information

**Path Parameters**:

- `token_address` (string, required): Token mint address

**Query Parameters**:

- `limit` (number, optional, default 20): Number of holders to return
- `offset` (number, optional, default 0): Number of holders to skip

**Success Response**:

```json
{
  "holders": [
    {
      "maker": "wallet_address_here",
      "makerType": "Whale",
      "baseBalance": 10.5,
      "boughtUiAmount": 5000.0,
      "boughtUsd": 663.0,
      "boughtTxCount": 3,
      "soldUiAmount": 1000.0,
      "soldUsd": 150.0,
      "soldTxCount": 1,
      "remainingUiAmount": 4000.0,
      "remainingUsd": 530.4,
      "remainingPercentage": 0.004,
      "pnlUsd": 17.4
    }
  ],
  "totalHolders": 1500
}

```

### 6.6 Get Token Top Traders

**Route**: `/api/token/top-traders/{token_address}`

**Method**: GET
**Description**: Get top traders for a token

**Path Parameters**:

- `token_address` (string, required): Token mint address

**Query Parameters**:

- `limit` (number, optional, default 20): Number of traders to return
- `offset` (number, optional, default 0): Number of traders to skip

**Success Response**:

```json
[
  {
    "maker": "wallet_address_here",
    "makerType": "Whale",
    "baseBalance": 10.5,
    "boughtUiAmount": 5000.0,
    "boughtUsd": 663.0,
    "boughtTxCount": 3,
    "soldUiAmount": 1000.0,
    "soldUsd": 150.0,
    "soldTxCount": 1,
    "remainingUiAmount": 4000.0,
    "remainingUsd": 530.4,
    "remainingPercentage": 0.004,
    "pnlUsd": 17.4
  }
]

```

### 6.7 Get Tokens Info (Batch)

**Route**: `/api/tokens/info`

**Method**: GET
**Description**: Get basic information for multiple tokens in batch.


**Query Parameters**:

- `tokens` (string, required): Comma-separated list of token mint addresses

**Example Request**:

```
GET /api/tokens/info?tokens=token1,token2,token3

```

**Success Response**:

```json
[
  {
    "mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
    "name": "Superstack",
    "symbol": "WORK",
    "image": "https://example.com/token-image.png",
    "description": "AI-powered work automation token",
    "usd_price": 0.0001234
  },
  {
    "mint": "So11111111111111111111111111111111111111112",
    "name": "Wrapped SOL",
    "symbol": "SOL",
    "image": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",
    "description": "Wrapped Solana",
    "usd_price": 132.45
  }
]

```

**Response Fields**:

- `mint` (string): Token mint address
- `name` (string): Full name of the token
- `symbol` (string): Token symbol/ticker
- `image` (string, optional): URL to the token's logo/image
- `description` (string, optional): Token description
- `usd_price` (number): Current price in USD

**Error Responses**:

```json
{
  "error": "Invalid token address format"
}
```

```json
{
  "error": "No tokens provided"
}
```

---

### 7. Chart and Trading Indicators

### 7.1 Get User Trading Indicators (Pills + Trend Lines)

**Route**: `/api/chart/user-indicators`

**Method**: GET
**Description**: Get user trading indicators for displaying buy/sell pills and trend lines on price charts within a specific time range
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:

- `wallet_address` (string, required): User's wallet address
- `token_mint` (string, required): Token mint address
- `start_time` (number, required): Start timestamp in seconds
- `end_time` (number, required): End timestamp in seconds
- `pool_address` (string, optional): Specific pool address to filter by
- `chain` (string, optional): Chain specification (`solana`, `hypercore`, `hyperevm`)

### 7.2 Get Trend Lines Only

**Route**: `/api/chart/trend-lines`

**Method**: GET
**Description**: Get trend lines for a token only if the user has an open position (bought more than sold). Returns null if no open position.
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:

- `wallet_address` (string, required): User's wallet address
- `token_mint` (string, required): Token mint address
- `pool_address` (string, optional): Specific pool address to filter by
- `chain` (string, optional): Chain specification (`solana`, `hypercore`, `hyperevm`)

**Example Request**:

```
GET /api/chart/trend-lines?wallet_address=********************************************&token_mint=So11111111111111111111111111111111111111112&chain=solana
```

**Success Response (with open position)**:

```json
{
  "trend_lines": {
    "average_buy_price": 150.25,
    "average_sell_price": 155.50,
    "buy_volume_weighted_price": 150.25,
    "sell_volume_weighted_price": 155.50,
    "total_buy_volume_usd": 1000.0,
    "total_sell_volume_usd": 500.0,
    "buy_trade_count": 2,
    "sell_trade_count": 1
  }
}
```

**Success Response (no open position)**:

```json
{
  "trend_lines": null
}
```

**Response Fields**:

**Trend Lines Object** (null if no open position):
- `average_buy_price` (number): Simple average of all buy prices (USD)
- `average_sell_price` (number): Simple average of all sell prices (USD)
- `buy_volume_weighted_price` (number): Volume-weighted average buy price (USD)
- `sell_volume_weighted_price` (number): Volume-weighted average sell price (USD)
- `total_buy_volume_usd` (number): Total buy volume in USD
- `total_sell_volume_usd` (number): Total sell volume in USD
- `buy_trade_count` (number): Number of buy trades
- `sell_trade_count` (number): Number of sell trades

---

### 7.1 Get User Trading Indicators (Pills + Trend Lines) - Legacy

**Example Request**:

```
GET /api/chart/user-indicators?wallet_address=********************************************&token_mint=So11111111111111111111111111111111111111112&start_time=**********&end_time=**********&chain=solana
```

**Success Response**:

```json
{
  "pills": [
    {
      "timestamp": **********,
      "price_usd": 150.25,
      "amount_usd": 1000.0,
      "trade_type": "buy",
      "tx_signature": "5J8c7AkiQbxVkwfXZcyqJ5K35moQCwtCFXJURZmnoHZ"
    },
    {
      "timestamp": 1640995800,
      "price_usd": 155.50,
      "amount_usd": 500.0,
      "trade_type": "sell",
      "tx_signature": "*******************************************"
    }
  ],
  "trend_lines": {
    "average_buy_price": 150.25,
    "average_sell_price": 155.50,
    "buy_volume_weighted_price": 150.25,
    "sell_volume_weighted_price": 155.50,
    "total_buy_volume_usd": 1000.0,
    "total_sell_volume_usd": 500.0,
    "buy_trade_count": 1,
    "sell_trade_count": 1
  },
  "metadata": {
    "total_trades": 2
  }
}
```

**Response Fields**:

**Pills Array** - Individual trade points for chart display:
- `timestamp` (number): Trade timestamp in seconds
- `price_usd` (number): Trade price in USD
- `amount_usd` (number): Trade amount in USD
- `trade_type` (string): "buy" or "sell"
- `tx_signature` (string): Transaction signature for linking to explorer

**Trend Lines Object** - Calculated trend line data:
- `average_buy_price` (number): Simple average of all buy prices (USD)
- `average_sell_price` (number): Simple average of all sell prices (USD)
- `buy_volume_weighted_price` (number): Volume-weighted average buy price (USD)
- `sell_volume_weighted_price` (number): Volume-weighted average sell price (USD)
- `total_buy_volume_usd` (number): Total buy volume in USD
- `total_sell_volume_usd` (number): Total sell volume in USD
- `buy_trade_count` (number): Number of buy trades
- `sell_trade_count` (number): Number of sell trades

**Metadata Object**:
- `total_trades` (number): Total number of trades found in the time range


**Frontend Integration**:
- Plot `pills` as colored dots on price chart (green for buy, red for sell)
- Draw horizontal trend lines using `average_buy_price` and `average_sell_price`
- Use `tx_signature` for linking to blockchain explorer
- Scale pill size based on `amount_usd` for visual impact

**Error Responses**:

```json
{
  "error": "Invalid parameters: start_time must be less than end_time"
}
```

```json
{
  "error": "Invalid wallet address: address format not recognized"
}
```



---

### 8. Candle Data

### 7.1 Get Candle Snapshot

**Route**: `/api/candle/snapshot`

**Method**: GET
**Description**: Get candlestick data for a trading pair

**Query Parameters**:

- `pair_address` (string, required): Pool address or token address. If a token address is provided, the API will automatically use the default pool (highest volume pool for that token)
- `interval` (string, required): Candle interval ("1s", "5s", "15s", "30s", "1m", "5m", "15m", "30m", "1h", "4h", "8h", "12h", "24h", "3d", "7d", "30d")
- `start_time` (number, required): Start timestamp (seconds or milliseconds)
- `end_time` (number, required): End timestamp (seconds or milliseconds)
- `chain` (string, optional): Blockchain chain ("solana", "hypercore", "hyperevm"). If not specified, the API will auto-detect the chain

**Enhanced Features**:
- **Smart Address Resolution**: The API now supports both pool addresses and token addresses. When a token address is provided, it automatically selects the pool with the highest 24h volume as the default pool.
- **Auto Chain Detection**: If no chain is specified, the API will search across all supported chains to find the address.
- **Backward Compatibility**: Existing integrations using pool addresses will continue to work without any changes.

**Note**: Some interval values may return "Invalid interval" error. Test with common intervals like "5m", "15m", "1h" first.

**Success Response**:

```json
{
  "data": [
    {
      "pool_address": "pool_address_here",
      "open": **********,
      "close": 1744642340,
      "interval": "M1",
      "native_token_price": 132.62,
      "open_price": 0.0001,
      "close_price": 0.00011,
      "high_price": 0.00012,
      "low_price": 0.00009,
      "open_market_cap": 100000.0,
      "close_market_cap": 110000.0,
      "high_market_cap": 120000.0,
      "low_market_cap": 90000.0,
      "volume": 50000.0,
      "trades": 150
    }
  ],
  "hasMore": true,
  "defaultPool": "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2",
  "chain": "solana"
}
```

**Response Fields**:
- `data`: Array of candle data objects
- `hasMore`: Boolean indicating if more historical data is available
- `defaultPool`: The standardized pool address used for the query (useful for WebSocket subscriptions)
- `chain`: The detected or specified blockchain chain

---

### 9. Watchlist Management

### 9.1 Get Watchlist

**Route**: `/api/watchlist`

**Method**: GET
**Description**: Get user's watchlist

**Query Parameters**:

- `wallet_address` (string, required): Wallet address

**Success Response**:

```json
{
  "tokens": [
    {
      "add_time": **********,
      "address": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
      "name": "Superstack",
      "symbol": "WORK",
      "image_url": "string",
      "chain": "Solana",
      "twitter": "string",
      "telegram": "string",
      "website": "string",
      "price": 0.0001,
      "price_change_24h": 0.15,
      "volume_24h": 50000.0,
      "market_cap": 100000.0,
      "liquidity": 25000.0
    }
  ],
  "pools": [
    {
      "add_time": **********,
      "token_mint": "AiYvvEZWqhNSqVuxDdWHoPeMoTiB2BbMNF6Xj9ZApump",
      "address": "pool_address_here",
      "name": "Superstack",
      "symbol": "WORK",
      "image_url": "string",
      "chain": "Solana",
      "base_symbol": "SOL",
      "dex": "Raydium",
      "pool_type": "CLMM",
      "price": 0.0001,
      "price_change_24h": 0.15,
      "volume_24h": 50000.0,
      "market_cap": 100000.0,
      "liquidity": 25000.0
    }
  ],
  "perps": [
    {
      "add_time": **********,
      "coin": "BTC"
    }
  ]
}

```

### 9.2 Change Watchlist

**Route**: `/api/watchlist`

**Method**: POST
**Description**: Add or remove items from watchlist

**Request Body**:

```json
{
  "wallet_address": "string",
  "is_add": true, // true for add, false for remove
  "watch_type": "SolanaToken", // "SolanaToken", "SolanaPool", "HyperliquidPerps"
  "watch_id": "token_or_pool_address_or_coin_symbol"
}

```

**Success Response**: HTTP 200 (no body)

---

### 10. PnL Share Images

### 10.1 Get PnL Share Images

**Route**: `/api/pnl/share/images`

**Method**: GET
**Description**: Get all PnL share background images for a user
**Authentication**: Wallet Authentication Required

**Query Parameters**:

- `wallet_address` (string, required): User's wallet address

**Success Response**:

```json
{
  "images": [
    {
      "imagePath": "pnl-share-images/default-blue.jpg",
      "imageUrl": "https://static.test.superstack.xyz/public/pnl-share-images/default-blue.jpg",
      "imageType": "default",
      "isSelected": true,
      "createdAt": *************
    },
    {
      "imagePath": "pnl-share-images/custom-abc123.jpg",
      "imageUrl": "https://static.test.superstack.xyz/public/pnl-share-images/custom-abc123.jpg",
      "imageType": "custom",
      "isSelected": false,
      "createdAt": 1704067300000
    }
  ],
  "selectedImagePath": "pnl-share-images/default-blue.jpg"
}
```

### 10.2 Upload PnL Share Image

**Route**: `/api/pnl/share/images/upload`

**Method**: POST
**Description**: Upload a new custom background image for PnL sharing
**Authentication**: Wallet Authentication Required
**Content-Type**: multipart/form-data

**Query Parameters**:

- `wallet_address` (string, required): User's wallet address

**Form Data**:

- `image` (file, required): Image file (JPG, PNG, WebP, max 10MB)

**Success Response**:

```json
{
  "imagePath": "pnl-share-images/uuid-generated-filename.jpg",
  "imageUrl": "https://static.test.superstack.xyz/public/pnl-share-images/uuid-generated-filename.jpg",
  "message": "Image uploaded successfully"
}
```

### 10.3 Select PnL Share Image (DEPRECATED)

**Route**: `/api/pnl/share/images/{image_path}/select`

**Method**: POST
**Description**: ⚠️ **DEPRECATED** - This endpoint has been removed. Frontend now handles image selection locally.
**Authentication**: Wallet Authentication Required

**Deprecation Notice**:
- This endpoint was removed in version 2025-01-25
- Image selection is now handled entirely by the frontend
- No server-side selection state is maintained
- This endpoint returns HTTP 200 for backward compatibility but performs no action

**Path Parameters**:

- `image_path` (string, required): Path of the image to select (URL encoded)

**Query Parameters**:

- `wallet_address` (string, required): User's wallet address

**Success Response**: HTTP 200 (no body)

### 10.4 Delete PnL Share Image

**Route**: `/api/pnl/share/images/{*image_path}`

**Method**: DELETE
**Description**: Delete a custom background image (default images cannot be deleted)
**Authentication**: Wallet Authentication Required

**Route Details**:
- Uses catch-all parameter (`{*image_path}`) to properly handle paths containing slashes
- Supports image paths like `pnl-share-images/uuid-filename.png`
- Fixed in version 2025-01-25 to resolve route matching issues

**Path Parameters**:

- `image_path` (string, required): Full path of the image to delete (including subdirectories)
  - Example: `pnl-share-images/ce4c0db6-dc93-4c08-bc1a-f40eb2a96cb0.png`
  - No URL encoding required for the path parameter

**Query Parameters**:

- `wallet_address` (string, required): User's wallet address

**Example Request**:

```bash
curl -X DELETE 'https://api.test.superstack.xyz/api/pnl/share/images/pnl-share-images/ce4c0db6-dc93-4c08-bc1a-f40eb2a96cb0.png?wallet_address=CRofTuikUG3H67eufZFjCNMEUEzJMy9evwoqhMfhL2d' \
  -H 'X-Wallet-Address: CRofTuikUG3H67eufZFjCNMEUEzJMy9evwoqhMfhL2d' \
  -H 'Accept: application/json'
```

**Success Response**:

```json
{
  "success": true,
  "message": "Image deleted successfully"
}
```

**Error Responses**:

**Image not found**:
```json
{
  "error": "Image not found"
}
```

**Invalid wallet address**:
```json
{
  "error": "Invalid wallet address format"
}
```

**Server error**:
```json
{
  "error": "Failed to delete image"
}
```

**Notes**:
- Only custom images can be deleted (images uploaded by users)
- Default system images cannot be deleted
- The image file is also removed from disk storage
- If file deletion fails, the database record is still removed

---

### 11. User Settings

### 11.1 Get Settings

**Route**: `/api/setting`

**Method**: GET
**Description**: Get user settings

**Query Parameters**:

- `wallet_address` (string, required): Wallet address

**Success Response**:

```json
{
  "quickTrade": {
    "enabled": true,
    "default_amount": 0.1
  },
  "walletSwapSlippage": {
    "percentage": 0.5
  },
  "spotPresets": {
    "amounts": [0.1, 0.5, 1.0, 5.0],
    "slippage": 0.5
  },
  "perpsPresets": {
    "leverage": 10,
    "amounts": [10, 50, 100, 500]
  }
}

```

### 10.2 Change Settings

**Route**: `/api/setting`

**Method**: POST
**Description**: Update user settings

**Request Body**:

```json
{
  "wallet_address": "string",
  "setting": {
    "quickTrade": {
      "enabled": true,
      "default_amount": 0.1
    },
    "walletSwapSlippage": {
      "percentage": 0.5
    },
    "spotPresets": {
      "amounts": [0.1, 0.5, 1.0, 5.0],
      "slippage": 0.5
    },
    "perpsPresets": {
      "leverage": 10,
      "amounts": [10, 50, 100, 500]
    }
  }
}

```

**Success Response**: HTTP 200 (no body)

---

### 11. Payment Integration

### 11.1 Sign MoonPay Request

**Route**: `/api/moonpay/sign`

**Method**: POST
**Description**: Sign MoonPay URL for secure payment processing

**Request Body**:

```json
{
  "url": "<https://moonpay.com/buy?apiKey=xxx&currencyCode=sol&walletAddress=xxx>"
}

```

**Success Response**:

```json
{
  "signature": "base64_encoded_signature"
}

```

---

### 12. Dashboard Analytics (Protected)

These endpoints require dashboard authentication.

### 12.1 Get Code Statistics

**Route**: `/dashboard/code-stats`

**Method**: GET
**Description**: Get referral code usage statistics

**Success Response**:

```json
{
  "total_codes": 1000,
  "active_codes": 750,
  "used_codes": 250,
  "usage_rate": 0.25
}

```

### 12.2 Get Transaction Statistics

**Route**: `/dashboard/transaction-stats`

**Method**: GET
**Description**: Get transaction statistics

**Success Response**:

```json
{
  "total_transactions": 50000,
  "successful_transactions": 48500,
  "failed_transactions": 1500,
  "success_rate": 0.97,
  "daily_volume": 1000000.0
}

```

### 12.3 Get User Funds Statistics

**Route**: `/dashboard/funds-stats`

**Method**: GET
**Description**: Get user funds and portfolio statistics

**Success Response**:

```json
{
  "total_users": 5000,
  "total_portfolio_value": 10000000.0,
  "average_portfolio_value": 2000.0,
  "total_pnl": 500000.0,
  "profitable_users": 3000,
  "profitable_rate": 0.6
}

```

---

### 13. Perpetual Contracts (Perps)

### 13.1 Get Perp List

**Route**: `/api/perps`

**Method**: GET
**Description**: Get list of perpetual contracts with filtering, sorting, and pagination
**Authentication**: None required (Public API)

**Query Parameters**:

- `limit` (number, optional, default 300): Number of perps to return (max 1000)
- `offset` (number, optional, default 0): Number of records to skip for pagination
- `volume_min` (number, optional): Minimum 24h volume filter
- `volume_max` (number, optional): Maximum 24h volume filter
- `funding_min` (number, optional): Minimum funding rate filter
- `funding_max` (number, optional): Maximum funding rate filter
- `open_interest_min` (number, optional): Minimum open interest filter
- `open_interest_max` (number, optional): Maximum open interest filter
- `category` (string, optional): Filter by category (`ai`, `layer1`, `layer2`, `defi`, `gaming`, `meme`)
- `sort_by` (string, optional): Sort field (`24h_volume`, `funding`, `open_interest`, `mark_px`, `market_cap`)
- `sort_desc` (boolean, optional, default true): Sort in descending order

**Example Requests**:

```
GET /api/perps
GET /api/perps?category=ai&sort_by=market_cap&sort_desc=true&limit=50
GET /api/perps?category=defi&volume_min=10000000&sort_by=24h_volume&limit=20
GET /api/perps?funding_min=-0.01&funding_max=0.01&sort_by=funding&sort_desc=false
```

**Success Response**:

```json
{
  "perps": [
    {
      "info": {
        "perp_exchange": "Hyperliquid",
        "perp_id": "BTC",
        "is_native_token": false,
        "network": "ethereum",
        "address": "0x...",
        "name": "Bitcoin",
        "symbol": "BTC",
        "socials": {
          "website": "https://bitcoin.org",
          "x": "https://twitter.com/bitcoin",
          "telegram": "https://t.me/bitcoin"
        },
        "total_supply": "21000000",
        "circulating_supply": "19800000",
        "update_timestamp_millis": *************
      },
      "state": {
        "perp_exchange": "Hyperliquid",
        "perp_id": "BTC",
        "funding": -0.0001,
        "open_interest": 5000000000.0,
        "premium": "0.001",
        "oracle_px": 45000.0,
        "impact_pxs": [44950.0, 45050.0],
        "day_base_vlm": 100000.0,
        "day_ntl_vlm": 4500000000.0,
        "mark_px": 45000.5,
        "mid_px": 45000.25,
        "market_cap": ************.0,
        "liquidity": 10000000.0,
        "fdv": ************.0,
        "long_ntl": 1918781094.730095,
        "short_ntl": 1918781094.730095,
        "long_traders": 9263,
        "short_traders": 5296,
        "long_entry": 115098.85682236843,
        "short_entry": 111816.53373948975,
        "updated_at_millis": *************
      },
      "state_series": {
        "1h": {
          "mark_px": 44000.5,
          "market_cap": ************.0,
          "funding": -0.0001,
          "open_interest": 5000000000.0,
          "long_ntl": 1918781094.730095,
          "short_ntl": 1918781094.730095,
          "long_traders": 9263,
          "short_traders": 5296,
        },
        "6h": { .. },
        "24h": { .. },
        "7d": { .. }
      }
    }
  ],
  "total": 1
}
```

**Response Fields**:

**Info Object** (Contract Information):
- `perp_exchange` (string): Exchange name (e.g., "Hyperliquid")
- `perp_id` (string): Unique identifier for the perpetual contract
- `is_native_token` (boolean): Whether this is a native blockchain token
- `network` (string, optional): Network/blockchain where the token exists
- `address` (string, optional): Token contract address
- `name` (string): Full name of the underlying asset
- `symbol` (string): Symbol of the underlying asset
- `socials` (object): Social media links and additional information
  - `website` (string, optional): Official website URL
  - `x` (string, optional): X (Twitter) profile URL
  - `telegram` (string, optional): Telegram group/channel URL
- `total_supply` (string): Total token supply (decimal string)
- `circulating_supply` (string): Circulating token supply (decimal string)
- `update_timestamp_millis` (number): Last update timestamp in milliseconds

**State Object** (Current Market Data):
- `perp_exchange` (string): Exchange name
- `perp_id` (string): Contract identifier
- `funding` (number): Current funding rate
- `open_interest` (number): Total open interest
- `premium` (string, optional): Premium over oracle price
- `oracle_px` (number): Oracle price
- `impact_pxs` (array, optional): Impact prices for different sizes
- `day_base_vlm` (number): 24h base volume
- `day_ntl_vlm` (number): 24h notional volume
- `mark_px` (number): Mark price
- `mid_px` (number, optional): Mid price
- `market_cap` (number): Market capitalization
- `liquidity` (number): Available liquidity
- `fdv` (number): Fully diluted valuation
- `long_ntl` (number, optional): Long notional
- `short_ntl` (number, optional): Short notional
- `long_sz` (number, optional): Long size
- `short_sz` (number, optional): Short size
- `long_traders` (number, optional): Long side traders
- `short_traders` (number, optional): Short side traders
- `long_entry` (number, optional): Long side entry px
- `short_entry` (number, optional): Short side entry px
- `long_pnl` (number, optional): Long side total pnl
- `short_pnl` (number, optional): Short side total pnl
- `long_liq_dist` (number, optional): Long side average liqudation distance
- `short_liq_dist` (number, optional): Short side average liqudation distance
- `updated_at_millis` (number): Last update timestamp in milliseconds

**State Series Object** (Historical Market Data):
(optional coz history data may not avaiable)
- `mark_px` (number): Mark price
- `market_cap` (number): Market capitalization
- `funding` (number): Funding rate
- `open_interest` (number): Total open interest
- `long_ntl` (number) Long notional
- `short_ntl` (number) Short notional
- `long_traders` (number) Short notional
- `short_traders` (number) Short notional

**Error Responses**:

```json
{
  "error": "Limit must be between 1 and 1000"
}
```

```json
{
  "error": "Invalid category. Valid values: ai, layer1, layer2, defi, gaming, meme"
}
```

```json
{
  "error": "Invalid sort_by field. Valid values: 24h_volume, funding, open_interest, mark_px, market_cap"
}
```

### 13.2 Get Perp Categories

**Route**: `/api/perps/categories`

**Method**: GET
**Description**: Get list of all available perp categories
**Authentication**: None required (Public API)

**Example Request**:

```
GET /api/perps/categories
```

**Success Response**:

```json
{
  "categories": [
    "ai",
    "layer1",
    "layer2",
    "defi",
    "gaming",
    "meme"
  ]
}
```

**Response Fields**:
- `categories` (array): List of available category names

### 13.3 Get Perp Category Detail

**Route**: `/api/perps/categories/detail`

**Method**: GET
**Description**: Get detailed information for a specific perp category including all symbols
**Authentication**: None required (Public API)

**Query Parameters**:

- `category` (string, required): Category name (`ai`, `layer1`, `layer2`, `defi`, `gaming`, `meme`)

**Example Requests**:

```
GET /api/perps/categories/detail?category=ai
GET /api/perps/categories/detail?category=defi
GET /api/perps/categories/detail?category=layer1
```

**Success Response**:

```json
{
  "category": "ai",
  "symbols": [
    "FET",
    "RNDR",
    "TAO",
    "NEAR",
    "WLD",
    "IO",
    "RENDER",
    "GRASS",
    "VIRTUAL",
    "AIXBT",
    "AI16Z",
    "ZEREBRO",
    "GRIFFAIN",
    "VVV",
    "KAITO",
    "PROMPT"
  ]
}
```

**Response Fields**:
- `category` (string): The requested category name
- `symbols` (array): List of symbols/coins belonging to this category

**Error Response**:

```json
{
  "error": "Category not found"
}
```

**Category Descriptions**:

- `ai` - Artificial Intelligence and machine learning tokens
- `layer1` - Layer 1 blockchain protocols and native tokens
- `layer2` - Layer 2 scaling solutions and tokens
- `defi` - Decentralized Finance protocols and tokens
- `gaming` - Gaming and metaverse related tokens
- `meme` - Meme coins and community-driven tokens

**Usage Examples**:

Get all AI category perps sorted by market cap:
```
GET /api/perps?category=ai&sort_by=market_cap&sort_desc=true
```

Get DeFi perps with high volume (> $10M):
```
GET /api/perps?category=defi&volume_min=10000000&sort_by=24h_volume
```

Get Layer 1 tokens with positive funding rates:
```
GET /api/perps?category=layer1&funding_min=0&sort_by=funding&sort_desc=true
```

### 13.4 Get Perp Books

**Route**: `/api/perps/books/{perp_id}`

**Method**: GET
**Description**: Get perp books given `perp_id` for depth chart

**Success Response**:

```json
{
  "perp_id": "kDOGS",
  "perp_exchange": "Hyperliquid",
  "updated_at_millis": *************,
  "total_sz": 983951.0,
  "books": [
    [bid_orders],
    [ask_orders]
  ]
}
```

**Response Fields**:
bid/ask_order (object):
- `px`: 0.14955 // price
- `sz`: 9696.0  // size
- `n`: 2        // number of orders

### 13.5 Get Perp Portfolio

**Route**: `/api/perps/portfolio`

**Method**: GET
**Description**: Get perpetual contracts portfolio data including account value history and PnL history for a specific wallet and time range
**Authentication**: Required (`X-Wallet-Address` header)

**Features**:
- **Database-Level Sampling**: Efficient data retrieval with intelligent sampling intervals based on time range
- **Zero-Value Baseline**: Automatically inserts zero-value starting point if no data exists at range start
- **Multi-Timeframe Support**: Different sampling intervals optimized for each time range
- **CDN Caching**: 3-minute cache TTL for improved performance

**Sampling Intervals by Time Range**:
- **1h**: Every 15 seconds (0.25 minutes)
- **24h**: Every 5 minutes
- **7d**: Every 30 minutes
- **30d**: Every 2 hours
- **1y**: Every 1 day
- **All**: Every 2 days

**Query Parameters**:

- `wallet_address` (string, required): User's wallet address
- `range` (string, required): Time range for portfolio data ("1h", "24h", "7d", "30d", "1y", "All")

**Example Requests**:

```
GET /api/perps/portfolio?wallet_address=8d2HXxu5PC...&range=7d
GET /api/perps/portfolio?wallet_address=8d2HXxu5PC...&range=1y
GET /api/perps/portfolio?wallet_address=8d2HXxu5PC...&range=All
```

**Success Response**:

```json
{
  "accountValueHistory": [
    {
      "timestamp": *************,
      "account_value": 0.0
    },
    {
      "timestamp": *************,
      "account_value": 1250.50
    },
    {
      "timestamp": *************,
      "account_value": 1180.25
    }
  ],
  "pnlHistory": [
    {
      "timestamp": *************,
      "pnl": 0.0
    },
    {
      "timestamp": *************,
      "pnl": 1250.50
    },
    {
      "timestamp": *************,
      "pnl": 1180.25
    }
  ]
}
```

**Response Fields**:

**PerpsPortfolio Object**:
- `accountValueHistory` (array): Historical account values over the requested time range
- `pnlHistory` (array): Profit/Loss history calculated from account value changes

**AccountValueHistory Object**:
- `timestamp` (number): Unix timestamp in milliseconds
- `account_value` (number): Account value in USD at this timestamp (from `hyperliquid_acc_perps`)

**PnlHistory Object**:
- `timestamp` (number): Unix timestamp in milliseconds
- `pnl` (number): Profit/Loss in USD relative to the first value in the time range

**Data Processing Logic**:

1. **Time Range Calculation**: Converts the selected range to start/end timestamps
2. **Database Sampling**: Queries sampled data using optimized intervals to reduce data transfer
3. **Zero-Value Insertion**: If no data exists at range start, inserts a zero-value entry at the start timestamp
4. **PnL Calculation**: Calculates relative PnL using the first account value as baseline (initial_value = 0 if zero-inserted)

**Performance Optimizations**:

- **Efficient Sampling**: Database-level time bucketing reduces data volume by up to 99% for large time ranges
- **Smart Baseline**: Zero-value insertion ensures consistent chart display for new users
- **CDN Caching**: 3-minute cache TTL reduces server load and improves response times

**Cache Headers**:

The endpoint returns the following cache headers:
```
Cache-Control: public, max-age=180
```

**Error Responses**:

```json
{
  "error": "Failed to get perp user states"
}
```

```json
{
  "error": "Missing wallet address"
}
```

**Usage Examples**:

**Weekly Portfolio View**:
```bash
curl -X GET "https://api.superstack.xyz/api/perps/portfolio?wallet_address=8d2HXxu5PC...&range=7d" \
  -H "X-Wallet-Address: 8d2HXxu5PC..."
```

**All-Time Portfolio History**:
```bash
curl -X GET "https://api.superstack.xyz/api/perps/portfolio?wallet_address=8d2HXxu5PC...&range=All" \
  -H "X-Wallet-Address: 8d2HXxu5PC..."
```

**Frontend Integration Notes**:

- Use `accountValueHistory` for rendering account value charts over time
- Use `pnlHistory` for displaying profit/loss trends
- The data is pre-sampled and ready for direct chart rendering without additional processing
- Zero-value baseline ensures consistent chart display for users without historical data
- Timestamps are in milliseconds for JavaScript Date compatibility

### 13.6 Get Perp Leaderboard

**Route**: `/api/perps/leaderboard/{perp_id}`

**Method**: GET
**Description**: Get perpetual trading leaderboard showing top traders and their positions
**Authentication**: Required (`X-Wallet-Address` header)

**Example Requests**:

```
GET /api/perps/leaderboard
```

**Success Response**:

```json
[
  {
    "perp_name": "BTC",
    "user_addr": "0x..",
    "ntl": 3918754.41354,
    "is_long": true,
    "entry_px": 116250.0,
    "liq_px": 125890.**********,
    "size": 33.77334,
    "funding": 43130.796519,
    "account_value": 801896.56434,
    "timestamp_millis": *************
  }
]
```

**Response Fields**:

**Leaderboard Entry Object**:
- `perp_name` (string): Perp name
- `user_addr` (string): User address
- `ntl` (number): Notional value of the position in USD
- `is_long` (boolean): Position direction (true for long, false for short)
- `entry_px` (number): Entry price of the position
- `size` (number): Position size in contract units
- `liq_px` (number, optional): Liquidition price
- `funding` (number, optional): Funding amount in USD
- `account_value` (number): Total account value in USD
- `timestamp_millis` (number): Updated timestamp


**Error Responses**:

```json
```

**Notes**:
- The leaderboard shows real-time position data from active perpetual traders
- Notional value (`ntl`) represents the USD value of the position (size × price)
- Account value includes all positions and available balance
- Leverage types: "isolated" (position-specific margin) or "cross" (shared margin)
- Funding represents the funding payments/receipts for the position

---

### 14. Referral System

The referral system allows users to generate referral codes, track referral relationships, and earn rewards from multi-tier referrals.

### 14.1 Get Referral Code

**Route**: `/api/referral/code`

**Method**: GET
**Description**: Get user's referral code
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:
- `wallet_address` (string, required): User's wallet address

**Success Response**:

```json
{
  "code": "ABC123",
  "created_at": **********,
  "updated_at": **********
}
```

**Response when no code exists**:

```json
null
```

### 14.2 Create/Update Referral Code (DEPRECATED)

**Note**: This API endpoint has been deprecated. Use `GET /api/referral/code` instead, which automatically generates codes as needed.

~~**Route**: `/api/referral/code`~~

~~**Method**: POST~~
~~**Description**: Create a new referral code or update existing one with custom code~~
~~**Authentication**: Required (`X-Wallet-Address` header)~~

~~**Request Body**:~~

~~```json
{
  "wallet_address": "8d2HXxu5PC...",
  "custom_code": "MYCUSTOMCODE" // Optional: if not provided, generates default code
}
```~~

**Success Response**:

```json
{
  "code": "MYCUSTOMCODE",
  "created_at": **********,
  "updated_at": **********
}
```

**Error Response**:

```json
{
  "error": "This referral code is already taken"
}
```

### 14.3 Bind Referral Relationship

**Route**: `/api/referral/bind`

**Method**: POST
**Description**: Bind a referral relationship between referrer and referee
**Authentication**: Required (`X-Wallet-Address` header)

**Request Body**:

```json
{
  "wallet_address": "referee_wallet_address",
  "referral_code": "ABC123"
}
```

**Success Response**:

```json
{
  "success": true,
  "message": "Successfully bound referral relationship"
}
```

**Error Response**:

```json
{
  "success": false,
  "message": "You have already been referred by someone"
}
```

### 14.4 Get Referral Statistics

**Route**: `/api/referral/stats`

**Method**: GET
**Description**: Get referral statistics for a user
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:
- `wallet_address` (string, required): User's wallet address

**Success Response**:

```json
{
  "total_invites": 15,
  "unclaimed_rewards_usd": "123.45",
  "claimed_rewards_usd": "67.89"
}
```

### 14.5 Get Referral List

**Route**: `/api/referral/list`

**Method**: GET
**Description**: Get list of users referred by the current user
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:
- `wallet_address` (string, required): User's wallet address
- `limit` (number, optional): Number of results per page (default: 20, max: 100)
- `offset` (number, optional): Offset for pagination (default: 0)

**Success Response**:

```json
{
  "referrals": [
    {
      "referrer_wallet": "8d2HXxu5PC...",
      "referee_wallet": "9e3IYyv6QD...",
      "created_at": **********
    }
  ],
  "total": 15
}
```

### 14.6 Get Reward List

**Route**: `/api/referral/rewards`

**Method**: GET
**Description**: Get list of referral rewards for a user
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:
- `wallet_address` (string, required): User's wallet address
- `limit` (number, optional): Number of results per page (default: 20, max: 100)
- `offset` (number, optional): Offset for pagination (default: 0)

**Success Response**:

```json
{
  "rewards": [
    {
      "id": 123,
      "referrer_wallet": "8d2HXxu5PC...",
      "referee_wallet": "9e3IYyv6QD...",
      "reward_type": "trading_tier1",
      "amount_usd": "12.34",
      "is_claimed": false,
      "created_at": **********,
      "claimed_at": null
    }
  ],
  "total": 25
}
```

**Reward Types**:
- `signup`: One-time signup reward when referee completes first trade
- `trading_tier1`: 20% of trading fees from direct referrals (Tier 1)
- `trading_tier2`: 2.4% of trading fees from second-level referrals (Tier 2)
- `trading_tier3`: 1.6% of trading fees from third-level referrals (Tier 3)
- `milestone`: Milestone rewards for reaching referral count targets

### 14.7 Claim Reward

**Route**: `/api/referral/rewards/claim`

**Method**: POST
**Description**: Claim a specific referral reward
**Authentication**: Required (`X-Wallet-Address` header)

**Request Body**:

```json
{
  "wallet_address": "8d2HXxu5PC...",
  "reward_id": 123
}
```

**Success Response**:

```json
{
  "success": true,
  "message": "Reward claimed successfully"
}
```

**Error Response**:

```json
{
  "success": false,
  "message": "Reward not found or already claimed"
}
```

### 14.8 Get Referral Network Visualization

**Route**: `/api/referral/network`

**Method**: GET
**Description**: Get referral network data for visualization (3 levels deep)
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:
- `wallet_address` (string, required): Root wallet address for the network

**Success Response**:

```json
{
  "root": "8d2HXxu5PC...",
  "relationships": [
    ["8d2HXxu5PC...", "9e3IYyv6QD..."],
    ["8d2HXxu5PC...", "7f4JZzw8RE..."],
    ["9e3IYyv6QD...", "6g5KAax9SF..."]
  ],
  "nodes": {
    "8d2HXxu5PC...": {
      "wallet": "8d2HXxu5PC...",
      "level": 0,
      "referral_count": 5,
      "total_rewards_usd": "123.45",
      "trading_volume_usd": "2500.00"
    },
    "9e3IYyv6QD...": {
      "wallet": "9e3IYyv6QD...",
      "level": 1,
      "referral_count": 2,
      "total_rewards_usd": "45.67",
      "trading_volume_usd": "750.00"
    }
  }
}
```

**Response Fields**:
- `root` (string): The root wallet address
- `relationships` (array): Array of [parent, child] wallet address pairs
- `nodes` (object): Map of wallet addresses to node information
  - `wallet` (string): Wallet address
  - `level` (number): Level in the referral tree (0=root, 1=direct referral, etc.)
  - `referral_count` (number): Number of people this wallet has referred
  - `total_rewards_usd` (string): Total rewards earned by this wallet from referrals
  - `trading_volume_usd` (string): Total trading volume of this wallet (from referral_rewards table, deduplicated by tx_signature)

**Notes**:
- The network query goes up to 3 levels deep from the root wallet
- Relationships are returned as parent-child pairs for easy tree/graph construction
- All monetary amounts are returned as strings to preserve precision

---

### 15. HyperEVM Integration (Simplified)

**Overview**:
HyperEVM integration provides basic blockchain query functionality, similar to Jupiter's approach. It focuses on essential read-only operations rather than complex trading functionality.

**Available Features**:
- Wallet balance queries (native HYPE tokens + ERC20 tokens)
- Transaction status monitoring
- Simple, reliable blockchain data access

**Note**: For actual trading/swapping functionality, use dedicated DEX interfaces or front-end implementations.

### 15.1 Get Wallet Balance

**Route**: `/api/hyperevm/balance`

**Method**: GET
**Description**: Get wallet balance information for HyperEVM tokens
**Authentication**: Required (`X-Wallet-Address` header)

**Query Parameters**:

- `wallet_address` (string, required): Wallet address to query
- `token_addresses` (string, optional): Comma-separated list of token contract addresses

**Example Request**:

```
GET /api/hyperevm/balance?wallet_address=0x1234...&token_addresses=0x5555...,0x6666...
```

**Success Response**:

```json
{
  "success": true,
  "data": {
    "address": "******************************************",
    "native_balance": "1500000000000000000",
    "native_ui_amount": 1.5,
    "token_balances": [
      {
        "token_address": "******************************************",
        "balance": "1000000000000000000",
        "ui_amount": 1.0,
        "decimals": 18
      },
      {
        "token_address": "******************************************",
        "balance": "500000000",
        "ui_amount": 500.0,
        "decimals": 6
      }
    ]
  },
  "error": null
}
```

**Response Fields**:
- `address` (string): Wallet address
- `native_balance` (string): Native HYPE token balance in wei
- `native_ui_amount` (number): Native balance in human-readable format
- `token_balances` (array): Array of ERC20 token balances
  - `token_address` (string): Token contract address
  - `balance` (string): Token balance in smallest unit
  - `ui_amount` (number): Balance in human-readable format
  - `decimals` (number): Token decimal places

### 15.2 Send HyperEVM Transaction

**Route**: `/api/hyperevm/transaction/send`

**Method**: POST
**Description**: Send a signed HyperEVM transaction (similar to Jupiter pattern)
**Authentication**: Required (`X-Wallet-Address` header)

**Request Body**:

```json
{
  "walletAddress": "******************************************",
  "serializedTransaction": "0x02f8b2..."
}
```

**Fields**:
- `walletAddress` (string, required): Wallet address sending the transaction
- `serializedTransaction` (string, required): Hex-encoded signed transaction

**Success Response**:

```json
{
  "tx_hash": "0xabcdef123456789012345678901234567890abcdef123456789012345678901234"
}
```

**Error Response**:

```json
{
  "error": "Send transaction failed"
}
```

### 15.3 Check Transaction Status

**Route**: `/api/hyperevm/transaction/status/{signature}`

**Method**: GET
**Description**: Check the status of a HyperEVM transaction (similar to Jupiter pattern)
**Authentication**: None required (Public API)

**Path Parameters**:

- `signature` (string, required): Transaction hash to check (using consistent naming with Solana)

**Example Request**:

```
GET /api/hyperevm/transaction/status/0xabcdef123456789012345678901234567890abcdef123456789012345678901234
```

**Success Response**:

```json
{
  "tx_hash": "0xabcdef123456789012345678901234567890abcdef123456789012345678901234",
  "status": "Success",
  "block_number": 12345678,
  "confirmations": 15,
  "gas_used": "180000"
}
```

**Response Fields**:
- `tx_hash` (string): Transaction hash
- `status` (string): Transaction status ("Pending", "Success", "Failed")
- `block_number` (number, optional): Block number if confirmed
- `confirmations` (number, optional): Number of confirmations
- `gas_used` (string, optional): Actual gas used

**Error Responses**:

All HyperEVM endpoints return error responses in this format:

```json
{
  "success": false,
  "data": null,
  "error": "Error message describing what went wrong"
}
```

Common error messages:
- `"Failed to connect to HyperEVM node"` - Network connection issue
- `"Invalid wallet address format"` - Malformed wallet address
- `"Failed to get wallet balance"` - Network or contract error
- `"Invalid transaction hash"` - Malformed transaction hash

---

## Data Types Reference

### Enums

### Chain

Chain values are represented as numbers in API responses:

- `0` - Solana
- `1` - Hypercore
- `2` - HyperEvm

For API requests, you can use string values:
- `"solana"` - Solana
- `"hypercore"` - Hypercore
- `"hyperevm"` - HyperEvm

### MakerType

- `"Plankton"` - < $10 trade
- `"Fish"` - $10-$250 trade
- `"Shrimp"` - $250-$1,000 trade
- `"Dolphin"` - $1,000-$10,000 trade
- `"Whale"` - > $10,000 trade

### Side

- `"Buy"`
- `"Sell"`

### OrderType

- `"Limit"`

### TradeType

- `"Market"`
- `"Limit"`

### OrderStatus

- `"Filled"`
- `"Expired"`
- `"Cancelled"`
- `"Failed"`

### TransactionStatus

- `"Pending"`
- `"Success"`
- `"Failed"`
- `"Expired"`

### PortfolioRange

- `"5m"` - 5 minutes
- `"1h"` - 1 hour
- `"6h"` - 6 hours
- `"24h"` - 24 hours
- `"3d"` - 3 days
- `"7d"` - 7 days
- `"30d"` - 30 days
- `"1y"` - 1 year
- `"All"` - All time

### CandleInterval

- `"S1"`, `"S5"`, `"S15"`, `"S30"` - Seconds
- `"M1"`, `"M5"`, `"M15"`, `"M30"` - Minutes
- `"H1"`, `"H4"`, `"H8"`, `"H12"`, `"H24"` - Hours

### WatchType

- `"SolanaToken"` - Solana token
- `"SolanaPool"` - Solana pool
- `"HyperliquidPerps"` - Hyperliquid perpetuals

### ActivityType

- `"swap"` - Token swap transactions
- `"limit_order"` - Limit order transactions (create, trade, cancel)
- `"transfer"` - Token transfer transactions
- `"perp_trade"` - Perpetual trading activities (HyperEVM)
- `"spot_trade"` - Spot trading activities (HyperEVM)
- `"deposit"` - Deposit transactions (HyperEVM)
- `"withdraw"` - Withdrawal transactions (HyperEVM)
- `"other"` - Other types of transactions

### PositionType

- `"spot"` - Spot positions/orders
- `"perps"` - Perpetual positions/orders (not implemented yet)

### PositionStatus

- `"active"` - Active positions (bought_amount > sold_amount) [deprecated, use "open"]
- `"sold"` - Sold/closed positions (bought_amount <= sold_amount) [deprecated, use "closed"]
- `"open"` - Open/active positions (bought_amount > sold_amount)
- `"closed"` - Closed/sold positions (bought_amount <= sold_amount)

### PositionOrderType

- `"spot"` - Spot orders
- `"perps"` - Perpetual orders (not implemented yet)

### PositionOrderStatus

- `"open"` - Open/active orders (not cancelled or completed)
- `"closed"` - Closed orders (cancelled or completed)

### Common Fields

### TokenMetadata

```json
{
  "name": "string",
  "symbol": "string",
  "decimals": 6,
  "image_url": "string",
  "chain": 0
}
```

**Description**: Token metadata information included in trade history, order history, and open orders responses.

**Fields**:
- `name` (string): Full name of the token (e.g., "Jupiter")
- `symbol` (string): Token symbol (e.g., "JUP")
- `decimals` (number): Number of decimal places for the token
- `image_url` (string, optional): URL to the token's image/logo
- `chain` (number): Blockchain network (0 = Solana, 1 = Hypercore, 2 = HyperEvm)

### Operation (Buy/Sell)

```json
{
  "is_buy": true,
  "token_amount": 100000000,
  "base_amount": 167000000000,
  "base_mint": "So11111111111111111111111111111111111111112",
  "base_decimals": 9,
  "tx_sig": "3LLRM3yTP...",
  "timestamp": **********
}
```

### Position Sell Initials Fields

**Description**: New fields added to Position responses to help calculate sell amounts for recovering initial costs.

**Fields**:
- `sell_initials_ui_amount` (number): Number of tokens needed to sell to recover initial investment cost in UI units (human-readable format)
- `sell_initials_amount` (number): Number of tokens needed to sell to recover initial investment cost in raw lamports (internal format)
- `sell_initial_usd` (number): USD value that would be recovered, representing the historical cost of current holdings

**Calculation Logic**:
- `sell_initials_ui_amount = current_holdings_cost_usd ÷ current_token_price_usd`
- `sell_initials_amount = sell_initials_ui_amount × 10^decimals`
- `sell_initial_usd = current_holdings_cost_usd` (historical cost)

**Use Case**: These fields help users determine exactly how many tokens to sell to break even on their investment.

## Enhanced Filtering for Positions and Orders

### Position Filtering

The `/api/positions` endpoint now supports enhanced filtering:

**Query Parameters**:
- `wallet_address` (required) - Wallet address
- `token_mint` (optional) - Filter by specific token
- `limit` (optional) - Limit number of results
- `offset` (optional) - Offset for pagination
- `sort_by` (optional) - Sort order
- `position_type` (optional) - Filter by position type: `"spot"` or `"perps"`
- `position_status` (optional) - Filter by position status: `"open"`, `"closed"`, `"active"` (deprecated), or `"sold"` (deprecated)

**Examples**:

Get all open spot positions:
```
GET /api/positions?wallet_address=WALLET&position_type=spot&position_status=open
```

Get all closed positions:
```
GET /api/positions?wallet_address=WALLET&position_status=closed
```

### Order Filtering

The `/api/open-orders` and `/api/order-history` endpoints now support enhanced filtering:

**Query Parameters**:
- `wallet_address` (required) - Wallet address
- `token_mint` (optional) - Filter by specific token
- `limit` (optional) - Limit number of results
- `offset` (optional) - Offset for pagination
- `order_type` (optional) - Filter by order type: `"spot"` or `"perps"`
- `order_status` (optional) - Filter by order status: `"open"` or `"closed"`

**Examples**:

Get all open spot orders:
```
GET /api/open-orders?wallet_address=WALLET&order_type=spot&order_status=open
```

Get all closed orders:
```
GET /api/order-history?wallet_address=WALLET&order_status=closed
```

**Note**: Perpetual contracts (perps) filtering is supported in the API but the underlying functionality is not yet implemented. Requests with `position_type=perps` or `order_type=perps` will return empty results.

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (missing/invalid authentication)
- `404` - Not Found (resource doesn't exist)
- `500` - Internal Server Error

Error responses include a descriptive error message in the `error` field.

### HyperEVM Data Types

### HyperEvmApiResponse

Generic response wrapper for all HyperEVM endpoints:

```json
{
  "success": boolean,
  "data": object | null,
  "error": string | null
}
```

### TokenBalance

Token balance information for HyperEVM:

```json
{
  "token_address": "string",
  "balance": "string",
  "ui_amount": number,
  "decimals": number
}
```

### HyperEvmWalletInfo

Complete wallet information for HyperEVM:

```json
{
  "address": "string",
  "native_balance": "string",
  "native_ui_amount": number,
  "token_balances": [TokenBalance]
}
```
