# 推荐系统设计文档

## 概述

简化的推荐系统，支持推荐码生成、关系追踪和多级奖励分配。

## 核心功能

1. **推荐码管理**：自动生成6位推荐码
2. **推荐关系追踪**：记录推荐关系网络
3. **多级奖励系统**：3级推荐奖励分配
4. **统计展示**：邀请数量和奖励统计
5. **网络可视化**：推荐关系图展示

## 奖励机制

### 三级奖励比例
- Tier 1: 20%
- Tier 2: 2.4%
- Tier 3: 1.6%

## API接口

### 获取推荐码
```
GET /api/referral/code?wallet_address={wallet}
```

### 绑定推荐关系（新用户注册）
```
POST /api/invite
{
  "wallet_address": "...",
  "invite_code": "ABC123"
}
```

### 获取推荐统计
```
GET /api/referral/stats?wallet_address={wallet}
```

### 获取推荐网络
```
GET /api/referral/network?wallet_address={wallet}
```

## 环境变量配置

```bash
# 推荐系统配置
REFERRAL_UNLIMITED_USERS=wallet1,wallet2  # 无限制用户列表（逗号分隔）
REFERRAL_TIER1_PERCENTAGE=0.20             # 一级推荐奖励比例 (20%)
REFERRAL_TIER2_PERCENTAGE=0.024            # 二级推荐奖励比例 (2.4%)
REFERRAL_TIER3_PERCENTAGE=0.016            # 三级推荐奖励比例 (1.6%)
REFERRAL_SIGNUP_REWARD_USD=5.0             # 注册奖励金额 ($5)

# 邀请码功能开关
ENABLE_INVITE_CODE=true                    # 是否启用邀请码验证
```

## 奖励类型说明

系统支持以下奖励类型：
- `signup`: 新用户完成首次交易的一次性奖励
- `trading_tier1`: 一级推荐交易奖励（直接推荐人获得）
- `trading_tier2`: 二级推荐交易奖励（间接推荐人获得）
- `trading_tier3`: 三级推荐交易奖励（三级推荐人获得）
