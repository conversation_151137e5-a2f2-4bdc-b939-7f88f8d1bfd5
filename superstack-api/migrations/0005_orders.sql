-- Add migration script here

CREATE TABLE orders (
    order_id TEXT PRIMARY KEY,

    tx_sig VARCHAR(88) NOT NULL,
    wallet_address VARCHAR(44) NOT NULL,
    
    token_mint VARCHAR(44) NOT NULL,
    token_decimals SMALLINT NOT NULL,
    base_mint VARCHAR(44) NOT NULL,
    base_decimals SMALLINT NOT NULL,
    
    trade_type TEXT NOT NULL,
    
    token_amount BIGINT NOT NULL,
    remaining_token_amount BIGINT NOT NULL,
    base_amount BIGINT NOT NULL,
    remaining_base_amount BIGINT NOT NULL,
    
    timestamp BIGINT NOT NULL,
    slot BIGINT NOT NULL,
    fee BIGINT NOT NULL,

    is_cancelled BOOLEAN NOT NULL,
    is_completed BOOLEAN NOT NULL,
    fee_bps SMALLINT NOT NULL
);

CREATE INDEX idx_orders_tx_sig ON orders (tx_sig);
CREATE INDEX idx_orders_wallet_address ON orders (wallet_address);
CREATE INDEX idx_orders_token_mint ON orders (token_mint);
CREATE INDEX idx_orders_trade_type ON orders (trade_type);
CREATE INDEX idx_orders_timestamp ON orders (timestamp);
CREATE INDEX idx_orders_timestamp_desc ON orders (timestamp DESC);
CREATE INDEX idx_orders_is_cancelled ON orders (is_cancelled);
CREATE INDEX idx_orders_is_completed ON orders (is_completed);
