-- Prevent duplicate referral rewards for the same transaction
-- This migration adds constraints to ensure each transaction can only generate rewards once

-- Add unique constraint to prevent duplicate rewards
-- This constraint ensures that for any given transaction (tx_signature),
-- each referrer can only receive one reward of each type from each referee
ALTER TABLE referral_rewards
ADD CONSTRAINT unique_trading_reward
UNIQUE (referrer_wallet, referee_wallet, reward_type, tx_signature);

-- Create partial index for better performance on trading rewards
-- This index specifically targets trading rewards with tx_signature
CREATE INDEX IF NOT EXISTS idx_referral_rewards_trading_lookup
ON referral_rewards (referrer_wallet, referee_wallet, reward_type, tx_signature)
WHERE tx_signature IS NOT NULL;
