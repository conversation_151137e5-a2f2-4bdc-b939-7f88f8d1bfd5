-- PnL Share Images table for managing user background images

CREATE TABLE IF NOT EXISTS pnl_share_images (
    wallet_address VARCHAR(44) NOT NULL,
    image_path VARCHAR NOT NULL,
    image_type VARCHAR(20) NOT NULL DEFAULT 'custom', -- 'default' or 'custom'
    is_selected BOOLEAN NOT NULL DEFAULT FALSE,
    created_at BIGINT NOT NULL,

    -- Composite primary key
    PRIMARY KEY (wallet_address, image_path),

    -- Foreign key constraint
    FOREIGN KEY (wallet_address) REFERENCES wallets(wallet_address) ON DELETE CASCADE
);

-- Index for faster queries on selected images
CREATE INDEX IF NOT EXISTS idx_pnl_share_images_selected ON pnl_share_images (wallet_address, is_selected) WHERE is_selected = true;

-- Constraint to ensure only one selected image per user
-- Note: PostgreSQL doesn't support conditional unique constraints directly, 
-- so we'll handle this in the application logic
