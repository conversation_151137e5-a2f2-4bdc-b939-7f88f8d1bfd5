-- Simplify PnL Share Images: Remove default images, backend only handles custom images
-- Frontend will handle default images display and selection

-- Step 1: Remove all default images from the database
DELETE FROM pnl_share_images WHERE image_type = 'default';

-- Step 2: Remove columns that are no longer needed
ALTER TABLE pnl_share_images DROP COLUMN IF EXISTS is_selected;
ALTER TABLE pnl_share_images DROP COLUMN IF EXISTS image_type;

-- Step 3: Add comment to document the change
COMMENT ON TABLE pnl_share_images IS 'Stores only custom user-uploaded images. Default images are handled by frontend.';

-- Migration completed
-- Backend now only handles custom user images
-- Frontend manages default images and selection logic
