-- Add migration script here

CREATE TABLE IF NOT EXISTS token_info (
    mint VARCHAR(44) PRIMARY KEY,

    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    image TEXT,
    description TEXT,
    
    usd_price DOUBLE PRECISION NOT NULL,
    updated_at BIGINT NOT NULL
);


CREATE INDEX IF NOT EXISTS idx_token_info_name ON token_info (name);
CREATE INDEX IF NOT EXISTS idx_token_info_symbol ON token_info (symbol);
CREATE INDEX IF NOT EXISTS idx_token_info_updated_at ON token_info (updated_at);

