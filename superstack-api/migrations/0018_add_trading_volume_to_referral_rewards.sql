-- Add trading volume and tx_signature to referral_rewards table
-- Remove amount_usd as it will be calculated dynamically

-- Add new columns
ALTER TABLE referral_rewards ADD COLUMN IF NOT EXISTS trading_volume_usd DECIMAL(20,8);
ALTER TABLE referral_rewards ADD COLUMN IF NOT EXISTS tx_signature VARCHAR(88);

-- Create index for tx_signature lookups
CREATE INDEX IF NOT EXISTS idx_referral_rewards_tx_signature ON referral_rewards (tx_signature);

ALTER TABLE referral_rewards DROP COLUMN IF EXISTS amount_usd;
