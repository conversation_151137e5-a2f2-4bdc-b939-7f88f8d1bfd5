-- Add migration script here

CREATE TABLE histories (
    id BIGSERIAL PRIMARY KEY,

    wallet_address VARCHAR(44) NOT NULL,
    token_mint VARCHAR(44) NOT NULL,
    token_decimals SMALLINT NOT NULL,
    
    bought_amount BIGINT NOT NULL,
    sold_amount BIGINT NOT NULL,

    native_decimals SMALLINT NOT NULL,
    cost_native_amount BIGINT NOT NULL,
    earnings_native_amount BIGINT NOT NULL,
    cost_usd DOUBLE PRECISION NOT NULL,
    earnings_usd DOUBLE PRECISION NOT NULL,

    pnl_usd DOUBLE PRECISION NOT NULL,
    pnl_percentage DOUBLE PRECISION NOT NULL,

    operations JSONB NOT NULL,

    open_time BIGINT NOT NULL,
    close_time BIGINT NOT NULL
);

CREATE INDEX idx_histories_wallet_address ON histories (wallet_address);
CREATE INDEX idx_histories_token_mint ON histories (token_mint);
CREATE INDEX idx_histories_open_time ON histories (open_time DESC);
CREATE INDEX idx_histories_close_time ON histories (close_time DESC);
CREATE INDEX idx_histories_pnl_usd ON histories (pnl_usd DESC);
CREATE INDEX idx_histories_pnl_percentage ON histories (pnl_percentage DESC);
