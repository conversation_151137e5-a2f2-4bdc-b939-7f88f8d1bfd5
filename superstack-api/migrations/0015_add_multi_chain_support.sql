-- Add multi-chain support to core tables
-- Chain enum: 0=Solana, 1=Hypercore, 2=HyperEvm

-- Add chain field as nullable first
ALTER TABLE positions ADD COLUMN IF NOT EXISTS chain SMALLINT;
ALTER TABLE wallets ADD COLUMN IF NOT EXISTS chain SMALLINT;
ALTER TABLE trades ADD COLUMN IF NOT EXISTS chain SMALLINT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS chain SMALLINT;
ALTER TABLE histories ADD COLUMN IF NOT EXISTS chain SMALLINT;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS chain SMALLINT;
ALTER TABLE account_values ADD COLUMN IF NOT EXISTS chain SMALLINT;

-- Update existing data to use Solana (chain = 0) as default
UPDATE positions SET chain = 0 WHERE chain IS NULL;
UPDATE wallets SET chain = 0 WHERE chain IS NULL;
UPDATE trades SET chain = 0 WHERE chain IS NULL;
UPDATE orders SET chain = 0 WHERE chain IS NULL;
UPDATE histories SET chain = 0 WHERE chain IS NULL;
UPDATE transactions SET chain = 0 WHERE chain IS NULL;
UPDATE account_values SET chain = 0 WHERE chain IS NULL;

-- Now make chain field NOT NULL with default value
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'positions' AND column_name = 'chain' 
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE positions ALTER COLUMN chain SET NOT NULL;
        ALTER TABLE positions ALTER COLUMN chain SET DEFAULT 0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'wallets' AND column_name = 'chain' 
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE wallets ALTER COLUMN chain SET NOT NULL;
        ALTER TABLE wallets ALTER COLUMN chain SET DEFAULT 0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trades' AND column_name = 'chain' 
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE trades ALTER COLUMN chain SET NOT NULL;
        ALTER TABLE trades ALTER COLUMN chain SET DEFAULT 0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'orders' AND column_name = 'chain' 
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE orders ALTER COLUMN chain SET NOT NULL;
        ALTER TABLE orders ALTER COLUMN chain SET DEFAULT 0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'histories' AND column_name = 'chain' 
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE histories ALTER COLUMN chain SET NOT NULL;
        ALTER TABLE histories ALTER COLUMN chain SET DEFAULT 0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'chain' 
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE transactions ALTER COLUMN chain SET NOT NULL;
        ALTER TABLE transactions ALTER COLUMN chain SET DEFAULT 0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'account_values' AND column_name = 'chain' 
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE account_values ALTER COLUMN chain SET NOT NULL;
        ALTER TABLE account_values ALTER COLUMN chain SET DEFAULT 0;
    END IF;
END $$;

-- Update primary key constraints to include chain field
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'positions' AND constraint_name = 'positions_pkey'
        AND constraint_type = 'PRIMARY KEY'
    ) THEN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.key_column_usage 
            WHERE table_name = 'positions' AND constraint_name = 'positions_pkey'
            AND column_name = 'chain'
        ) THEN
            ALTER TABLE positions DROP CONSTRAINT positions_pkey;
            ALTER TABLE positions ADD CONSTRAINT positions_pkey PRIMARY KEY (wallet_address, token_mint, chain);
        END IF;
    END IF;
END $$;

DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'account_values' AND constraint_name = 'account_values_pkey'
        AND constraint_type = 'PRIMARY KEY'
    ) THEN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.key_column_usage 
            WHERE table_name = 'account_values' AND constraint_name = 'account_values_pkey'
            AND column_name = 'chain'
        ) THEN
            ALTER TABLE account_values DROP CONSTRAINT account_values_pkey;
            ALTER TABLE account_values ADD CONSTRAINT account_values_pkey PRIMARY KEY (wallet_address, timestamp, chain);
        END IF;
    END IF;
END $$;

-- Create indexes for chain field
CREATE INDEX IF NOT EXISTS idx_positions_chain ON positions (chain);
CREATE INDEX IF NOT EXISTS idx_wallets_chain ON wallets (chain);
CREATE INDEX IF NOT EXISTS idx_trades_chain ON trades (chain);
CREATE INDEX IF NOT EXISTS idx_orders_chain ON orders (chain);
CREATE INDEX IF NOT EXISTS idx_histories_chain ON histories (chain);
CREATE INDEX IF NOT EXISTS idx_transactions_chain ON transactions (chain);
CREATE INDEX IF NOT EXISTS idx_account_values_chain ON account_values (chain);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_positions_wallet_chain ON positions (wallet_address, chain);
CREATE INDEX IF NOT EXISTS idx_trades_wallet_chain ON trades (wallet_address, chain);
CREATE INDEX IF NOT EXISTS idx_orders_wallet_chain ON orders (wallet_address, chain);
CREATE INDEX IF NOT EXISTS idx_histories_wallet_chain ON histories (wallet_address, chain);
CREATE INDEX IF NOT EXISTS idx_transactions_wallet_chain ON transactions (wallet_address, chain);
CREATE INDEX IF NOT EXISTS idx_account_values_wallet_chain ON account_values (wallet_address, chain); 