-- Add migration script here

CREATE TABLE positions (
    wallet_address VARCHAR(44) NOT NULL,
    token_mint VARCHAR(44) NOT NULL,
    token_decimals SMALLINT NOT NULL,

    bought_amount BIGINT NOT NULL,
    sold_amount BIGINT NOT NULL,

    native_decimals SMALLINT NOT NULL,
    cost_native_amount BIGINT NOT NULL,
    earnings_native_amount BIGINT NOT NULL,

    -- other costs except for the native token will be converted to USD using the price at the time of the trade
    -- for example, if the cost is 100 USDC, $100 will be stored in cost_usd
    cost_usd DOUBLE PRECISION NOT NULL,
    -- other earnings except for the native token will be converted to USD using the price at the time of the trade
    -- for example, if the earnings are 100 USDC, $100 will be stored in earnings_usd
    earnings_usd DOUBLE PRECISION NOT NULL,

    operations JSONB NOT NULL,

    open_time BIGINT NOT NULL,
    
    PRIMARY KEY (wallet_address, token_mint)
);

CREATE INDEX idx_positions_token_mint ON positions (token_mint);
CREATE INDEX idx_positions_open_time ON positions (open_time);
