-- Telegram tables

-- Table for storing telegram users
CREATE TABLE IF NOT EXISTS telegram_users (
    -- Telegram user info
    id BIGINT PRIMARY KEY,
    username <PERSON><PERSON><PERSON><PERSON>,
    first_name VA<PERSON>HAR NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>,
    is_bot BO<PERSON><PERSON>N NOT NULL,
    is_premium BOOLEAN NOT NULL,
    -- Inner fields
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    bind_account VARCHAR,
    bind_at BIGINT,
    auth_code VARCHAR,
    auth_code_created_at BIGINT
);

CREATE INDEX IF NOT EXISTS idx_telegram_users_bind_account ON telegram_users (bind_account);
