-- Add migration script here

CREATE TABLE wallet_activity (
    wallet_address VARCHAR(44) NOT NULL,
    tx_signature VARCHAR(88) NOT NULL,

    activity_type TEXT NOT NULL, -- 'swap', 'limit_order', 'transfer', 'other'

    token_mint VARCHAR(44),
    token_decimals SMALLINT,
    token_amount BIGINT,

    base_mint VARCHAR(44),
    base_decimals SMALLINT,
    base_amount BIGINT,

    usd_value DOUBLE PRECISION,

    timestamp BIGINT NOT NULL,
    block_time BIGINT,
    slot BIGINT,

    chain SMALLINT NOT NULL DEFAULT 0, -- 0: <PERSON><PERSON>, 1: Hypercore, 2: HyperEVM

    -- Additional metadata as JSON
    metadata JSONB,

    created_at BIGINT NOT NULL DEFAULT extract(epoch from now()),

    PRIMARY KEY(wallet_address, tx_signature, chain)
);

-- Create indexes for efficient querying
CREATE INDEX idx_wallet_activity_wallet_address ON wallet_activity (wallet_address);
CREATE INDEX idx_wallet_activity_timestamp ON wallet_activity (timestamp DESC);
CREATE INDEX idx_wallet_activity_wallet_timestamp ON wallet_activity (wallet_address, timestamp DESC);
CREATE INDEX idx_wallet_activity_chain ON wallet_activity (chain);
CREATE INDEX idx_wallet_activity_activity_type ON wallet_activity (activity_type);
CREATE INDEX idx_wallet_activity_token_mint ON wallet_activity (token_mint);
