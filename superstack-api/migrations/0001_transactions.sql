-- Add migration script here

CREATE TABLE transactions (
    signature VARCHAR(88) PRIMARY KEY,

    wallet_address VARCHAR(44) NOT NULL,
    status TEXT NOT NULL,
    is_processed BOOLEAN NOT NULL,

    created_at BIGINT NOT NULL
);

CREATE INDEX idx_transactions_wallet_address ON transactions (wallet_address);
CREATE INDEX idx_transactions_status ON transactions (status);
CREATE INDEX idx_transactions_is_processed ON transactions (is_processed);
CREATE INDEX idx_transactions_created_at ON transactions (created_at DESC);
