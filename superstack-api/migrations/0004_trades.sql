-- Add migration script here

CREATE TABLE trades (
    tx_sig VARCHAR(88) PRIMARY KEY,

    wallet_address VARCHAR(44) NOT NULL,

    token_mint VARCHAR(44) NOT NULL,
    token_decimals SMALLINT NOT NULL,
    base_mint VARCHAR(44) NOT NULL,
    base_decimals SMALLINT NOT NULL,

    trade_type TEXT NOT NULL,

    token_amount BIGINT NOT NULL,
    base_amount BIGINT NOT NULL,
    
    timestamp BIGINT NOT NULL,
    slot BIGINT NOT NULL,
    fee BIGINT NOT NULL,

    order_id TEXT,
    remaining_token_amount BIGINT,
    remaining_base_amount BIGINT,
    token_fee_amount BIGINT NOT NULL,
    base_fee_amount BIGINT NOT NULL
);

CREATE INDEX idx_trades_wallet_address ON trades (wallet_address);
CREATE INDEX idx_trades_token_mint ON trades (token_mint);
CREATE INDEX idx_trades_trade_type ON trades (trade_type);
CREATE INDEX idx_trades_timestamp ON trades (timestamp DESC);
CREATE INDEX idx_trades_order_id ON trades (order_id);