-- Referral system tables

-- Table for storing referral codes
CREATE TABLE IF NOT EXISTS referral_codes (
    id BIGSERIAL PRIMARY KEY,
    owner_wallet VARCHAR(44) NOT NULL,
    code VARCHAR(6) UNIQUE NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    used_by_wallet VARCHAR(44),
    used_at BIGINT,
    created_at BIGINT NOT NULL
);

-- Table for storing referral relationships
-- referrer_wallet can be NULL for seed users (users without referrers)
CREATE TABLE IF NOT EXISTS referrals (
    referrer_wallet VARCHAR(44),  -- NULL for seed users
    referee_wallet VARCHAR(44) NOT NULL,
    created_at BIGINT NOT NULL,
    PRIMARY KEY (referee_wallet)  -- Each user can only be referred once
);

-- Table for storing referral rewards
-- Note: referrer_wallet is NOT NULL because only users with referrers can generate rewards
-- Seed users (referrer_wallet IS NULL in referrals table) will not have reward records
CREATE TABLE IF NOT EXISTS referral_rewards (
    id BIGSERIAL PRIMARY KEY,
    referrer_wallet VARCHAR(44) NOT NULL,  -- Only users with referrers generate rewards
    referee_wallet VARCHAR(44) NOT NULL,
    reward_type VARCHAR(20) NOT NULL,
    amount_usd DECIMAL(20,8) NOT NULL,
    is_claimed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at BIGINT NOT NULL,
    claimed_at BIGINT
);

-- Essential indexes for core queries
CREATE INDEX IF NOT EXISTS idx_referral_codes_code ON referral_codes (code);
CREATE INDEX IF NOT EXISTS idx_referral_codes_owner_wallet ON referral_codes (owner_wallet);
CREATE INDEX IF NOT EXISTS idx_referral_codes_status ON referral_codes (status);
CREATE INDEX IF NOT EXISTS idx_referral_codes_used_by ON referral_codes (used_by_wallet);

-- Referrals table indexes
CREATE INDEX IF NOT EXISTS idx_referrals_referrer ON referrals (referrer_wallet);
-- Note: No need for idx_referrals_referee as referee_wallet is the primary key (auto-indexed)
CREATE INDEX IF NOT EXISTS idx_referrals_seed_users ON referrals (referee_wallet) WHERE referrer_wallet IS NULL;

-- Referral rewards table indexes
CREATE INDEX IF NOT EXISTS idx_referral_rewards_referrer ON referral_rewards (referrer_wallet);
CREATE INDEX IF NOT EXISTS idx_referral_rewards_claimed ON referral_rewards (is_claimed);

-- Status constraint (only add if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'check_referral_codes_status'
        AND table_name = 'referral_codes'
    ) THEN
        ALTER TABLE referral_codes
        ADD CONSTRAINT check_referral_codes_status
        CHECK (status IN ('active', 'used'));
    END IF;
END $$;
