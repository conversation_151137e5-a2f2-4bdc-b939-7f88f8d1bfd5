{"db_name": "PostgreSQL", "query": "\n            INSERT INTO referral_rewards (referrer_wallet, referee_wallet, reward_type, trading_volume_usd, tx_signature, is_claimed, created_at)\n            VALUES ($1, $2, $3, $4, $5, $6, $7)\n            ON CONFLICT (referrer_wallet, referee_wallet, reward_type, tx_signature) DO NOTHING\n            RETURNING id\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int8"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Numeric", "<PERSON><PERSON><PERSON><PERSON>", "Bool", "Int8"]}, "nullable": [false]}, "hash": "3088e121aae2d43bd72e0a34b6baf799896290854e9e8972fb1a782005e7da31"}