{"db_name": "PostgreSQL", "query": "\n            INSERT INTO histories (\n                wallet_address, token_mint, token_decimals,\n                bought_amount, sold_amount,\n                native_decimals, cost_native_amount, earnings_native_amount,\n                cost_usd, earnings_usd,\n                pnl_usd, pnl_percentage,\n                operations,\n                open_time, close_time, chain\n            )\n            VALUES (\n                $1, $2, $3,\n                $4, $5,\n                $6, $7, $8,\n                $9, $10,\n                $11, $12,\n                $13,\n                $14, $15, $16)\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8", "Int8", "Int2", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Jsonb", "Int8", "Int8", "Int2"]}, "nullable": []}, "hash": "525fba71c6788975508dc2eb312ce47bf865207c270f2e6a5d0662fcc368e0d8"}