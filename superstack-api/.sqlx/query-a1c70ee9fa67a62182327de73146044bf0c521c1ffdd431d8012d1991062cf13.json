{"db_name": "PostgreSQL", "query": "\n            SELECT id, owner_wallet, code, status, used_by_wallet, used_at, created_at\n            FROM referral_codes\n            WHERE owner_wallet = $1\n            ORDER BY created_at DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int8"}, {"ordinal": 1, "name": "owner_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "status", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "used_by_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "used_at", "type_info": "Int8"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false, true, true, false]}, "hash": "a1c70ee9fa67a62182327de73146044bf0c521c1ffdd431d8012d1991062cf13"}