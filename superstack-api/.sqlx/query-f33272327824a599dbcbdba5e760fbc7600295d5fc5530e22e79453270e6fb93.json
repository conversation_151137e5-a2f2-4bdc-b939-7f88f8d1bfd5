{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM settings WHERE wallet_address = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "json_settings", "type_info": "Jsonb"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false]}, "hash": "f33272327824a599dbcbdba5e760fbc7600295d5fc5530e22e79453270e6fb93"}