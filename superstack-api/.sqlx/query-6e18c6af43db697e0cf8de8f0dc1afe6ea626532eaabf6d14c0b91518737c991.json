{"db_name": "PostgreSQL", "query": "\n            INSERT INTO account_values (\n                wallet_address, timestamp, value, chain\n            )\n            VALUES ($1, $2, $3, $4)\n            ON CONFLICT (wallet_address, timestamp, chain) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "Int8", "Float8", "Int2"]}, "nullable": []}, "hash": "6e18c6af43db697e0cf8de8f0dc1afe6ea626532eaabf6d14c0b91518737c991"}