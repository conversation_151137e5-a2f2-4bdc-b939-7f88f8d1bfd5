{"db_name": "PostgreSQL", "query": "\n            SELECT reward_type, trading_volume_usd, is_claimed\n            FROM referral_rewards\n            WHERE referrer_wallet = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "reward_type", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "trading_volume_usd", "type_info": "Numeric"}, {"ordinal": 2, "name": "is_claimed", "type_info": "Bool"}], "parameters": {"Left": ["Text"]}, "nullable": [false, true, false]}, "hash": "629950528dfe58ea7c49c6b23e889714e2936b01e5696f6b576e670462bf2404"}