{"db_name": "PostgreSQL", "query": "\n                SELECT metadata FROM wallet_activity\n                WHERE wallet_address = $1\n                  AND chain = $2\n                  AND metadata->>'oid' = $3::text\n                LIMIT 1\n                ", "describe": {"columns": [{"ordinal": 0, "name": "metadata", "type_info": "Jsonb"}], "parameters": {"Left": ["Text", "Int2", "Text"]}, "nullable": [true]}, "hash": "ca76ed5150b05f933f94414af5ffb39be8fcb95a23bf4aae2f2cd98f21ee658c"}