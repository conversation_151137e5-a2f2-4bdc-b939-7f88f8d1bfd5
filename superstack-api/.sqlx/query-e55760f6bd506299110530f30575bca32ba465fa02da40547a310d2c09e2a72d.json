{"db_name": "PostgreSQL", "query": "\n                UPDATE wallet_activity SET\n                    tx_signature = $2,\n                    activity_type = $3,\n                    token_mint = $4,\n                    token_decimals = $5,\n                    token_amount = $6,\n                    base_mint = $7,\n                    base_decimals = $8,\n                    base_amount = $9,\n                    usd_value = $10,\n                    timestamp = $11,\n                    block_time = $12,\n                    slot = $13,\n                    metadata = $14\n                WHERE wallet_address = $1\n                  AND chain = $15\n                  AND (metadata->>'oid' = $16::text OR metadata->>'order_id' = $16::text)\n                ", "describe": {"columns": [], "parameters": {"Left": ["Text", "<PERSON><PERSON><PERSON><PERSON>", "Text", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8", "Float8", "Int8", "Int8", "Int8", "Jsonb", "Int2", "Text"]}, "nullable": []}, "hash": "e55760f6bd506299110530f30575bca32ba465fa02da40547a310d2c09e2a72d"}