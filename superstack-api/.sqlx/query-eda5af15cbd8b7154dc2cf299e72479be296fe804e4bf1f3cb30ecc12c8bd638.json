{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM token_info WHERE mint = ANY($1)\n            ", "describe": {"columns": [{"ordinal": 0, "name": "mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "Text"}, {"ordinal": 2, "name": "symbol", "type_info": "Text"}, {"ordinal": 3, "name": "image", "type_info": "Text"}, {"ordinal": 4, "name": "description", "type_info": "Text"}, {"ordinal": 5, "name": "usd_price", "type_info": "Float8"}, {"ordinal": 6, "name": "updated_at", "type_info": "Int8"}], "parameters": {"Left": ["TextArray"]}, "nullable": [false, false, false, true, true, false, false]}, "hash": "eda5af15cbd8b7154dc2cf299e72479be296fe804e4bf1f3cb30ecc12c8bd638"}