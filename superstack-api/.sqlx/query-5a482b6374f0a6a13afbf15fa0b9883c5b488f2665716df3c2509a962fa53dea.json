{"db_name": "PostgreSQL", "query": "\n            INSERT INTO wallet_activity (\n                wallet_address, tx_signature, activity_type,\n                token_mint, token_decimals, token_amount,\n                base_mint, base_decimals, base_amount,\n                usd_value, timestamp, block_time, slot, chain, metadata, created_at\n            )\n            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)\n            ON CONFLICT (wallet_address, tx_signature, chain) DO UPDATE SET\n                activity_type = $3,\n                token_mint = $4,\n                token_decimals = $5,\n                token_amount = $6,\n                base_mint = $7,\n                base_decimals = $8,\n                base_amount = $9,\n                usd_value = $10,\n                timestamp = $11,\n                block_time = $12,\n                slot = $13,\n                metadata = $15,\n                created_at = $16\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Text", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8", "Float8", "Int8", "Int8", "Int8", "Int2", "Jsonb", "Int8"]}, "nullable": []}, "hash": "5a482b6374f0a6a13afbf15fa0b9883c5b488f2665716df3c2509a962fa53dea"}