{"db_name": "PostgreSQL", "query": "\n            SELECT tx_signature\n            FROM wallet_activity\n            WHERE wallet_address = $1\n              AND chain = $2\n              AND (metadata->>'oid' = $3::text OR metadata->>'order_id' = $3::text)\n            ", "describe": {"columns": [{"ordinal": 0, "name": "tx_signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text", "Int2", "Text"]}, "nullable": [false]}, "hash": "369dbca552b474f3cb382bc20ec6aa59381b87d93f7c345654870fd85171ae4e"}