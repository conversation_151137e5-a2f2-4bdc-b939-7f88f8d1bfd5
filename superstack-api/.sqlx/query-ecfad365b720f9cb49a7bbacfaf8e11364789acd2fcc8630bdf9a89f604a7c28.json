{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, token_mint, token_decimals,\n                   bought_amount, sold_amount,\n                   native_decimals, cost_native_amount, earnings_native_amount,\n                   cost_usd, earnings_usd,\n                   operations,\n                   open_time, chain\n            FROM positions WHERE wallet_address = $1 AND token_mint = $2 AND chain = $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "token_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "token_decimals", "type_info": "Int2"}, {"ordinal": 3, "name": "bought_amount", "type_info": "Int8"}, {"ordinal": 4, "name": "sold_amount", "type_info": "Int8"}, {"ordinal": 5, "name": "native_decimals", "type_info": "Int2"}, {"ordinal": 6, "name": "cost_native_amount", "type_info": "Int8"}, {"ordinal": 7, "name": "earnings_native_amount", "type_info": "Int8"}, {"ordinal": 8, "name": "cost_usd", "type_info": "Float8"}, {"ordinal": 9, "name": "earnings_usd", "type_info": "Float8"}, {"ordinal": 10, "name": "operations", "type_info": "Jsonb"}, {"ordinal": 11, "name": "open_time", "type_info": "Int8"}, {"ordinal": 12, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Text", "Int2"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "ecfad365b720f9cb49a7bbacfaf8e11364789acd2fcc8630bdf9a89f604a7c28"}