{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM transactions WHERE status = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "status", "type_info": "Text"}, {"ordinal": 3, "name": "is_processed", "type_info": "Bool"}, {"ordinal": 4, "name": "created_at", "type_info": "Int8"}, {"ordinal": 5, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false, false, false]}, "hash": "6b0c3ca47f142436280bd8164dab8711ad2714437e589f06bb2875638511194d"}