{"db_name": "PostgreSQL", "query": "\n            SELECT COALESCE(SUM(DISTINCT_VOLUME.trading_volume_usd), 0.0) as total_volume\n            FROM (\n                SELECT DISTINCT tx_signature, trading_volume_usd\n                FROM referral_rewards\n                WHERE referee_wallet = $1\n                  AND tx_signature IS NOT NULL\n                  AND trading_volume_usd IS NOT NULL\n            ) AS DISTINCT_VOLUME\n            ", "describe": {"columns": [{"ordinal": 0, "name": "total_volume", "type_info": "Numeric"}], "parameters": {"Left": ["Text"]}, "nullable": [null]}, "hash": "664e0ee605d05e89be2f915253596e965748d1f65d984b608e8c6f95a3ebc34e"}