{"db_name": "PostgreSQL", "query": "\n            SELECT \n                COUNT(*) FILTER (WHERE status = $2) as pending_count,\n                COUNT(*) FILTER (WHERE status = $3 AND is_processed = false) as unprocessed_success_count\n            FROM transactions\n            WHERE wallet_address = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "pending_count", "type_info": "Int8"}, {"ordinal": 1, "name": "unprocessed_success_count", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Text", "Text"]}, "nullable": [null, null]}, "hash": "59d82a34ace8594c6ada1b45f582997f69d0c1e25788504ef57cb07b45a2782b"}