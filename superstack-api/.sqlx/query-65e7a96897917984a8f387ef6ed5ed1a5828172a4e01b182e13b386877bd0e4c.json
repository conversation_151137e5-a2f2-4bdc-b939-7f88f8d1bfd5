{"db_name": "PostgreSQL", "query": "\n            WITH latest_values AS (\n                SELECT DISTINCT ON (wallet_address) wallet_address, timestamp, value, chain\n                FROM account_values\n                ORDER BY wallet_address, timestamp DESC\n            )\n            SELECT wallet_address, timestamp, value, chain FROM latest_values\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 2, "name": "value", "type_info": "Float8"}, {"ordinal": 3, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": []}, "nullable": [false, false, false, false]}, "hash": "65e7a96897917984a8f387ef6ed5ed1a5828172a4e01b182e13b386877bd0e4c"}