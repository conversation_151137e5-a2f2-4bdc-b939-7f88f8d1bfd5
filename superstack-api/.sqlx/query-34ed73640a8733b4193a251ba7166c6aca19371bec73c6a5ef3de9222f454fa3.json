{"db_name": "PostgreSQL", "query": "\n                        SELECT wallet_address, tx_signature, activity_type,\n                               token_mint, token_decimals, token_amount,\n                               base_mint, base_decimals, base_amount,\n                               usd_value, timestamp, block_time, slot, chain, metadata, created_at\n                        FROM wallet_activity\n                        WHERE wallet_address = $1 AND chain = $2 AND tx_signature = $3\n                        ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "tx_signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "activity_type", "type_info": "Text"}, {"ordinal": 3, "name": "token_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "token_decimals", "type_info": "Int2"}, {"ordinal": 5, "name": "token_amount", "type_info": "Int8"}, {"ordinal": 6, "name": "base_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 7, "name": "base_decimals", "type_info": "Int2"}, {"ordinal": 8, "name": "base_amount", "type_info": "Int8"}, {"ordinal": 9, "name": "usd_value", "type_info": "Float8"}, {"ordinal": 10, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 11, "name": "block_time", "type_info": "Int8"}, {"ordinal": 12, "name": "slot", "type_info": "Int8"}, {"ordinal": 13, "name": "chain", "type_info": "Int2"}, {"ordinal": 14, "name": "metadata", "type_info": "Jsonb"}, {"ordinal": 15, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Int2", "Text"]}, "nullable": [false, false, false, true, true, true, true, true, true, true, false, true, true, false, true, false]}, "hash": "34ed73640a8733b4193a251ba7166c6aca19371bec73c6a5ef3de9222f454fa3"}