{"db_name": "PostgreSQL", "query": "\n            SELECT referrer_wallet, referee_wallet, created_at\n            FROM referrals\n            WHERE referee_wallet = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referrer_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "referee_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [true, false, false]}, "hash": "9f7c43cda75b75238dca35d2e892771b5b3e0fbeb01ef9248296684d9f44e838"}