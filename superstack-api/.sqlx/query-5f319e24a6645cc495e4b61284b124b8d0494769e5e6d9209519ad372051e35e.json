{"db_name": "PostgreSQL", "query": "\n            INSERT INTO referral_codes (owner_wallet, code, status, created_at)\n            VALUES ($1, $2, 'active', $3)\n            RETURNING id, owner_wallet, code, status, used_by_wallet, used_at, created_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int8"}, {"ordinal": 1, "name": "owner_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "status", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "used_by_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "used_at", "type_info": "Int8"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8"]}, "nullable": [false, false, false, false, true, true, false]}, "hash": "5f319e24a6645cc495e4b61284b124b8d0494769e5e6d9209519ad372051e35e"}