{"db_name": "PostgreSQL", "query": "\n            SELECT referrer_wallet, referee_wallet, created_at\n            FROM referrals\n            WHERE referrer_wallet = $1\n            ORDER BY created_at DESC\n            LIMIT $2 OFFSET $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referrer_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "referee_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Int8", "Int8"]}, "nullable": [true, false, false]}, "hash": "efba2a76ce357c2d34fa0db4cd60d9d373163016e8ccd25f2dce2b3d974a9958"}