{"db_name": "PostgreSQL", "query": "\n            SELECT\n                referee_wallet,\n                COALESCE(SUM(DISTINCT_VOLUME.trading_volume_usd), 0.0) as total_volume\n            FROM (\n                SELECT DISTINCT referee_wallet, tx_signature, trading_volume_usd\n                FROM referral_rewards\n                WHERE referee_wallet = ANY($1)\n                  AND tx_signature IS NOT NULL\n                  AND trading_volume_usd IS NOT NULL\n            ) AS DISTINCT_VOLUME\n            GROUP BY referee_wallet\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referee_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "total_volume", "type_info": "Numeric"}], "parameters": {"Left": ["TextArray"]}, "nullable": [false, null]}, "hash": "16f4b7f89ee3d49abb77fd4a98c54279f9a7b238e96d0ef119cf7ab111e4671e"}