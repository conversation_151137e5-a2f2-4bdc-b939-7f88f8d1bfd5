{"db_name": "PostgreSQL", "query": "\n            SELECT id, owner_wallet, code, status, used_by_wallet, used_at, created_at\n            FROM referral_codes\n            WHERE code = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int8"}, {"ordinal": 1, "name": "owner_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "status", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "used_by_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "used_at", "type_info": "Int8"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false, true, true, false]}, "hash": "9423ea28f28eae9f0920a8e6550fe03e79d0ca99e22e110c03fecc79fa1f60a3"}