{"db_name": "PostgreSQL", "query": "\n            SELECT\n                COUNT(*) as total_activities,\n                COUNT(CASE WHEN activity_type = 'spot_trade' THEN 1 END) as swap_count,\n                COUNT(CASE WHEN activity_type IN ('spot_trade', 'perp_trade') THEN 1 END) as limit_order_count,\n                COUNT(CASE WHEN activity_type = 'deposit' THEN 1 END) as receive_count,\n                COUNT(CASE WHEN activity_type = 'withdraw' THEN 1 END) as send_count,\n                COUNT(CASE WHEN activity_type = 'transfer' THEN 1 END) as transfer_count,\n                COUNT(CASE WHEN activity_type IN ('other', 'liquidation') THEN 1 END) as other_count,\n                COALESCE(SUM(usd_value), 0.0) as total_usd_volume,\n                MIN(timestamp) as first_activity_timestamp,\n                MAX(timestamp) as last_activity_timestamp\n            FROM wallet_activity\n            WHERE wallet_address = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "total_activities", "type_info": "Int8"}, {"ordinal": 1, "name": "swap_count", "type_info": "Int8"}, {"ordinal": 2, "name": "limit_order_count", "type_info": "Int8"}, {"ordinal": 3, "name": "receive_count", "type_info": "Int8"}, {"ordinal": 4, "name": "send_count", "type_info": "Int8"}, {"ordinal": 5, "name": "transfer_count", "type_info": "Int8"}, {"ordinal": 6, "name": "other_count", "type_info": "Int8"}, {"ordinal": 7, "name": "total_usd_volume", "type_info": "Float8"}, {"ordinal": 8, "name": "first_activity_timestamp", "type_info": "Int8"}, {"ordinal": 9, "name": "last_activity_timestamp", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [null, null, null, null, null, null, null, null, null, null]}, "hash": "f12c1e70545761d0a1f8f077e1d984090eb9cef27d59fb60f436267515702912"}