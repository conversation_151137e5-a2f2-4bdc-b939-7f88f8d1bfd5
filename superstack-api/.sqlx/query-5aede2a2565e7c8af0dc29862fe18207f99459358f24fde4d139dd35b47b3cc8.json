{"db_name": "PostgreSQL", "query": "\n            INSERT INTO realized_pnl (\n                wallet_address, timestamp, pnl_usd\n            )\n            VALUES ($1, $2, $3) \n            ON CONFLICT (wallet_address, timestamp) DO UPDATE SET \n                pnl_usd = $3 + realized_pnl.pnl_usd\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "Int8", "Float8"]}, "nullable": []}, "hash": "5aede2a2565e7c8af0dc29862fe18207f99459358f24fde4d139dd35b47b3cc8"}