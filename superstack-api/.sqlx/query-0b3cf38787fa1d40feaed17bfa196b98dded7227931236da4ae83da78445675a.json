{"db_name": "PostgreSQL", "query": "\n            INSERT INTO positions (\n                wallet_address, token_mint, token_decimals,\n                bought_amount, sold_amount,\n                native_decimals, cost_native_amount, earnings_native_amount,\n                cost_usd, earnings_usd,\n                operations,\n                open_time, chain\n            )\n            VALUES (\n                $1, $2, $3,\n                $4, $5,\n                $6, $7, $8,\n                $9, $10,\n                $11,\n                $12, $13\n            )\n            ON CONFLICT (wallet_address, token_mint, chain) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8", "Int8", "Int2", "Int8", "Int8", "Float8", "Float8", "Jsonb", "Int8", "Int2"]}, "nullable": []}, "hash": "0b3cf38787fa1d40feaed17bfa196b98dded7227931236da4ae83da78445675a"}