{"db_name": "PostgreSQL", "query": "\n            SELECT referrer_wallet, referee_wallet, created_at\n            FROM referrals\n            ORDER BY created_at DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referrer_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "referee_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": []}, "nullable": [true, false, false]}, "hash": "250da21bcd97b2716e05b9efc5b6be44ccae37cd33ac745f7e5005f41cc0e630"}