{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM transactions\n            WHERE wallet_address = $1 AND status = $2\n            ORDER BY created_at ASC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "status", "type_info": "Text"}, {"ordinal": 3, "name": "is_processed", "type_info": "Bool"}, {"ordinal": 4, "name": "created_at", "type_info": "Int8"}, {"ordinal": 5, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Text"]}, "nullable": [false, false, false, false, false, false]}, "hash": "19d1cd7a89847c17c4030ad26a363395be529b1b44dd421a25882143abfdd76d"}