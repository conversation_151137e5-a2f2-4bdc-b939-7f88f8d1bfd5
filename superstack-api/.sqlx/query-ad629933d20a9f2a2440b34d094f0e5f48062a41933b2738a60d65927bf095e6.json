{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM watches WHERE wallet_address = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "add_time", "type_info": "Int8"}, {"ordinal": 2, "name": "watch_type", "type_info": "Text"}, {"ordinal": 3, "name": "watch_id", "type_info": "Text"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false]}, "hash": "ad629933d20a9f2a2440b34d094f0e5f48062a41933b2738a60d65927bf095e6"}