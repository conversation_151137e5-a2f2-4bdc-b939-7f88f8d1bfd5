{"db_name": "PostgreSQL", "query": "\n            SELECT \n                COUNT(CASE WHEN status = $1 THEN 1 END) as total_success_count,\n                COUNT(CASE WHEN status = $2 THEN 1 END) as total_pending_count,\n                COUNT(CASE WHEN status = $3 THEN 1 END) as total_failed_count,\n                COUNT(CASE WHEN status = $4 THEN 1 END) as total_expired_count,\n                COUNT(CASE WHEN status = $1 AND created_at >= $5 THEN 1 END) as success_count_24h,\n                COUNT(CASE WHEN status = $2 AND created_at >= $5 THEN 1 END) as pending_count_24h,\n                COUNT(CASE WHEN status = $3 AND created_at >= $5 THEN 1 END) as failed_count_24h,\n                COUNT(CASE WHEN status = $4 AND created_at >= $5 THEN 1 END) as expired_count_24h\n            FROM transactions\n            ", "describe": {"columns": [{"ordinal": 0, "name": "total_success_count", "type_info": "Int8"}, {"ordinal": 1, "name": "total_pending_count", "type_info": "Int8"}, {"ordinal": 2, "name": "total_failed_count", "type_info": "Int8"}, {"ordinal": 3, "name": "total_expired_count", "type_info": "Int8"}, {"ordinal": 4, "name": "success_count_24h", "type_info": "Int8"}, {"ordinal": 5, "name": "pending_count_24h", "type_info": "Int8"}, {"ordinal": 6, "name": "failed_count_24h", "type_info": "Int8"}, {"ordinal": 7, "name": "expired_count_24h", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Text", "Text", "Text", "Int8"]}, "nullable": [null, null, null, null, null, null, null, null]}, "hash": "63e35c198fbb67c78aa0670367bd238b6aa22c079b9fd988a76d0a6b131f9f06"}