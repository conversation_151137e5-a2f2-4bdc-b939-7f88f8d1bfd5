{"db_name": "PostgreSQL", "query": "\n            SELECT timestamp FROM wallet_activity\n            WHERE wallet_address = $1 AND chain = $2\n            ORDER BY timestamp DESC\n            LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "timestamp", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Int2"]}, "nullable": [false]}, "hash": "0b84e5213fd1d7f3b4c0c11cf49d0985be7a5038857b4bea13be89e8fb4cec56"}