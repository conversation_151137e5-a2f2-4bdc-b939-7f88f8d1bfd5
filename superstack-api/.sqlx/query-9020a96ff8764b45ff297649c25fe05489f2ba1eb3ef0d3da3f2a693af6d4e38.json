{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM transactions WHERE status = $1 AND chain = $2\n            ", "describe": {"columns": [{"ordinal": 0, "name": "signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "status", "type_info": "Text"}, {"ordinal": 3, "name": "is_processed", "type_info": "Bool"}, {"ordinal": 4, "name": "created_at", "type_info": "Int8"}, {"ordinal": 5, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Int2"]}, "nullable": [false, false, false, false, false, false]}, "hash": "9020a96ff8764b45ff297649c25fe05489f2ba1eb3ef0d3da3f2a693af6d4e38"}