{"db_name": "PostgreSQL", "query": "\n            INSERT INTO token_info (\n                mint, name, symbol, image, description, usd_price, updated_at\n            ) VALUES (\n                $1, $2, $3, $4, $5, $6, $7\n            ) ON CONFLICT (mint) DO UPDATE SET\n                name = $2,\n                symbol = $3,\n                image = $4,\n                description = $5,\n                usd_price = $6,\n                updated_at = $7\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "Text", "Text", "Text", "Text", "Float8", "Int8"]}, "nullable": []}, "hash": "9e4784872d4dbf78b0ff406ea2b35326f215fe160358c9dec82321b466b12396"}