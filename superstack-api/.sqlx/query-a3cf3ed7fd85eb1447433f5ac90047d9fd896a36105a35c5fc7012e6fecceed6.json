{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM realized_pnl WHERE wallet_address = $1 AND timestamp >= $2 AND timestamp <= $3\n            ORDER BY timestamp DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 2, "name": "pnl_usd", "type_info": "Float8"}], "parameters": {"Left": ["Text", "Int8", "Int8"]}, "nullable": [false, false, false]}, "hash": "a3cf3ed7fd85eb1447433f5ac90047d9fd896a36105a35c5fc7012e6fecceed6"}