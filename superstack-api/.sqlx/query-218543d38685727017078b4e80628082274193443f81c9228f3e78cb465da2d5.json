{"db_name": "PostgreSQL", "query": "\n            INSERT INTO trades (\n                tx_sig, wallet_address,\n                token_mint, token_decimals, base_mint, base_decimals,\n                trade_type,\n                token_amount, base_amount,\n                timestamp, slot, fee,\n                token_fee_amount, base_fee_amount,\n                order_id, remaining_token_amount, remaining_base_amount, chain)\n            VALUES (\n                $1, $2,\n                $3, $4, $5, $6,\n                $7,\n                $8, $9,\n                $10, $11, $12,\n                $13, $14,\n                $15, $16, $17, $18)\n            ON CONFLICT (tx_sig) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Text", "Int8", "Int8", "Int8", "Int8", "Int8", "Int8", "Int8", "Text", "Int8", "Int8", "Int2"]}, "nullable": []}, "hash": "218543d38685727017078b4e80628082274193443f81c9228f3e78cb465da2d5"}