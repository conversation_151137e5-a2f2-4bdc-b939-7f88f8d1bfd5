{"db_name": "PostgreSQL", "query": "\n            INSERT INTO telegram_users (\n                id, username, first_name, last_name, is_bot, is_premium,\n                created_at, updated_at,\n                bind_account, bind_at,\n                auth_code, auth_code_created_at\n            )\n            VALUES (\n                $1, $2, $3, $4, $5, $6,\n                $7, $8,\n                $9, $10,\n                $11, $12\n            )\n            ON CONFLICT (id) DO UPDATE SET\n                username = EXCLUDED.username,\n                first_name = EXCLUDED.first_name,\n                last_name = EXCLUDED.last_name,\n                is_bot = EXCLUDED.is_bot,\n                is_premium = EXCLUDED.is_premium,\n                updated_at = EXCLUDED.updated_at,\n                auth_code = EXCLUDED.auth_code,\n                auth_code_created_at = EXCLUDED.auth_code_created_at\n            RETURNING bind_account\n            ", "describe": {"columns": [{"ordinal": 0, "name": "bind_account", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Int8", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bool", "Bool", "Int8", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int8"]}, "nullable": [true]}, "hash": "2f902f882b4e7fa6410a7d5caaee269953e0a6a74b694bee3ff0bfc3aaaadaa0"}