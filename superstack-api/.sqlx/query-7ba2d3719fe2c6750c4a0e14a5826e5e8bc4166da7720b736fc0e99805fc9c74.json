{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM orders\n            WHERE wallet_address = $1 and is_cancelled = false and is_completed = false\n            ORDER BY timestamp ASC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "order_id", "type_info": "Text"}, {"ordinal": 1, "name": "tx_sig", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "token_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "token_decimals", "type_info": "Int2"}, {"ordinal": 5, "name": "base_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "base_decimals", "type_info": "Int2"}, {"ordinal": 7, "name": "trade_type", "type_info": "Text"}, {"ordinal": 8, "name": "token_amount", "type_info": "Int8"}, {"ordinal": 9, "name": "remaining_token_amount", "type_info": "Int8"}, {"ordinal": 10, "name": "base_amount", "type_info": "Int8"}, {"ordinal": 11, "name": "remaining_base_amount", "type_info": "Int8"}, {"ordinal": 12, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 13, "name": "slot", "type_info": "Int8"}, {"ordinal": 14, "name": "fee", "type_info": "Int8"}, {"ordinal": 15, "name": "is_cancelled", "type_info": "Bool"}, {"ordinal": 16, "name": "is_completed", "type_info": "Bool"}, {"ordinal": 17, "name": "fee_bps", "type_info": "Int2"}, {"ordinal": 18, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "7ba2d3719fe2c6750c4a0e14a5826e5e8bc4166da7720b736fc0e99805fc9c74"}