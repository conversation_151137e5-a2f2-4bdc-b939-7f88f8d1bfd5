{"db_name": "PostgreSQL", "query": "\n            INSERT INTO settings (\n                wallet_address, json_settings\n            )\n            VALUES ($1, $2)\n            ON CONFLICT (wallet_address) DO UPDATE SET json_settings = $2\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "Jsonb"]}, "nullable": []}, "hash": "0a8fd627ec1e157fbb820755e759ea161bfc966c6e7092a15b0420b5e4de4f20"}