{"db_name": "PostgreSQL", "query": "\n            WITH RECURSIVE referral_tree AS (\n                -- Level 1: Direct referrals\n                SELECT referrer_wallet, referee_wallet, 1 as level\n                FROM referrals\n                WHERE referrer_wallet = $1\n\n                UNION ALL\n\n                -- Levels 2-3: Recursive referrals\n                SELECT r.referrer_wallet, r.referee_wallet, rt.level + 1\n                FROM referrals r\n                INNER JOIN referral_tree rt ON r.referrer_wallet = rt.referee_wallet\n                WHERE rt.level < 3\n            )\n            SELECT referrer_wallet, referee_wallet, level FROM referral_tree\n            ORDER BY level, referrer_wallet, referee_wallet\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referrer_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "referee_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "level", "type_info": "Int4"}], "parameters": {"Left": ["Text"]}, "nullable": [null, null, null]}, "hash": "607c115403aafca64aef6f8a82ab6a01a472ce4367420dcdda50a9341df26241"}