{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM histories\n            WHERE wallet_address = $1\n            AND close_time >= $2\n            AND close_time <= $3\n            ORDER BY close_time DESC\n            LIMIT $4\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int8"}, {"ordinal": 1, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "token_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "token_decimals", "type_info": "Int2"}, {"ordinal": 4, "name": "bought_amount", "type_info": "Int8"}, {"ordinal": 5, "name": "sold_amount", "type_info": "Int8"}, {"ordinal": 6, "name": "native_decimals", "type_info": "Int2"}, {"ordinal": 7, "name": "cost_native_amount", "type_info": "Int8"}, {"ordinal": 8, "name": "earnings_native_amount", "type_info": "Int8"}, {"ordinal": 9, "name": "cost_usd", "type_info": "Float8"}, {"ordinal": 10, "name": "earnings_usd", "type_info": "Float8"}, {"ordinal": 11, "name": "pnl_usd", "type_info": "Float8"}, {"ordinal": 12, "name": "pnl_percentage", "type_info": "Float8"}, {"ordinal": 13, "name": "operations", "type_info": "Jsonb"}, {"ordinal": 14, "name": "open_time", "type_info": "Int8"}, {"ordinal": 15, "name": "close_time", "type_info": "Int8"}, {"ordinal": 16, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Int8", "Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "846b0bae783d7f897590867dff6ca3f81cba85e76fa96b0eed91a9e30e525a47"}