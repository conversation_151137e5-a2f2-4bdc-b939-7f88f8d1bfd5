{"db_name": "PostgreSQL", "query": "\n            DELETE FROM pnl_share_images\n            WHERE wallet_address = $1 AND image_path = $2\n            RETURNING wallet_address, image_path, created_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "image_path", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Text"]}, "nullable": [false, false, false]}, "hash": "78d28d9ac44fadc0cc306a8faac74dceed2bb0fd1d0497cf583a1805062bbcf6"}