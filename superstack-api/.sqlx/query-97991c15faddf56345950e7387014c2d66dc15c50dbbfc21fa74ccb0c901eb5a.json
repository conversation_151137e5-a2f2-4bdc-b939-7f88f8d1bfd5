{"db_name": "PostgreSQL", "query": "\n            SELECT\n                id, username, first_name, last_name, is_bot, is_premium,\n                created_at, updated_at,\n                bind_account, bind_at,\n                auth_code, auth_code_created_at\n            FROM telegram_users WHERE bind_account = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int8"}, {"ordinal": 1, "name": "username", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "first_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "last_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "is_bot", "type_info": "Bool"}, {"ordinal": 5, "name": "is_premium", "type_info": "Bool"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}, {"ordinal": 7, "name": "updated_at", "type_info": "Int8"}, {"ordinal": 8, "name": "bind_account", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "bind_at", "type_info": "Int8"}, {"ordinal": 10, "name": "auth_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "auth_code_created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [false, true, false, true, false, false, false, false, true, true, true, true]}, "hash": "97991c15faddf56345950e7387014c2d66dc15c50dbbfc21fa74ccb0c901eb5a"}