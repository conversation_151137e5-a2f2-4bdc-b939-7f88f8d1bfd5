{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, timestamp, value, chain FROM account_values\n            WHERE wallet_address = $1 AND timestamp >= $2 AND value > 0\n            ORDER BY timestamp ASC LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 2, "name": "value", "type_info": "Float8"}, {"ordinal": 3, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Int8"]}, "nullable": [false, false, false, false]}, "hash": "e6654d1fe66139813c2f7ef3cc91de060ff4609c9d03760c2385f603da0f7049"}