{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, tx_signature, activity_type,\n                   token_mint, token_decimals, token_amount,\n                   base_mint, base_decimals, base_amount,\n                   usd_value, timestamp, block_time, slot, chain, metadata, created_at\n            FROM wallet_activity\n            WHERE wallet_address = $1\n              AND ($2::bigint IS NULL OR timestamp < $2)\n              AND ($4 OR activity_type != 'other')\n              AND (\n                -- Include all activity types except 'other' unless explicitly requested\n                activity_type IN ('spot_trade', 'perp_trade', 'deposit', 'withdraw', 'transfer', 'liquidation')\n                OR (metadata IS NOT NULL AND metadata != '{}')\n              )\n              AND (\n                -- Mainnet filter: if is_mainnet is specified, filter HyperCore activities by metadata\n                $5::boolean IS NULL\n                OR chain != 1  -- Not HyperCore chain\n                OR (\n                  metadata IS NOT NULL\n                  AND (metadata->>'is_hyperliquid_mainnet')::boolean = $5\n                )\n              )\n            ORDER BY timestamp DESC, tx_signature DESC\n            LIMIT $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "tx_signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "activity_type", "type_info": "Text"}, {"ordinal": 3, "name": "token_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "token_decimals", "type_info": "Int2"}, {"ordinal": 5, "name": "token_amount", "type_info": "Int8"}, {"ordinal": 6, "name": "base_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 7, "name": "base_decimals", "type_info": "Int2"}, {"ordinal": 8, "name": "base_amount", "type_info": "Int8"}, {"ordinal": 9, "name": "usd_value", "type_info": "Float8"}, {"ordinal": 10, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 11, "name": "block_time", "type_info": "Int8"}, {"ordinal": 12, "name": "slot", "type_info": "Int8"}, {"ordinal": 13, "name": "chain", "type_info": "Int2"}, {"ordinal": 14, "name": "metadata", "type_info": "Jsonb"}, {"ordinal": 15, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Int8", "Int8", "Bool", "Bool"]}, "nullable": [false, false, false, true, true, true, true, true, true, true, false, true, true, false, true, false]}, "hash": "e288446e2360eea26a8daf893cd691d2f8df9bc410e04043df0345692882151e"}