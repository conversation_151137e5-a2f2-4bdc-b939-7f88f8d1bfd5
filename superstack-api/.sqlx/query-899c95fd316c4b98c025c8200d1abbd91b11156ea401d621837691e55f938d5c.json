{"db_name": "PostgreSQL", "query": "\n            INSERT INTO wallets (\n                wallet_address, latest_tx_signature, latest_tx_slot, updated_at\n            )\n            VALUES ($1, $2, $3, $4)\n            ON CONFLICT (wallet_address) DO UPDATE SET\n                latest_tx_signature = $2,\n                latest_tx_slot = $3,\n                updated_at = $4\n            WHERE wallets.latest_tx_slot is null OR wallets.latest_tx_slot < $3\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8"]}, "nullable": []}, "hash": "899c95fd316c4b98c025c8200d1abbd91b11156ea401d621837691e55f938d5c"}