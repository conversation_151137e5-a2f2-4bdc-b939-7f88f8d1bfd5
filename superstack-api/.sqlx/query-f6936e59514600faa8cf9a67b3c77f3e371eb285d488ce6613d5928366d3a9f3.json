{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, timestamp, value, chain FROM account_values\n            WHERE wallet_address = $1 AND timestamp <= $2\n            ORDER BY timestamp DESC LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 2, "name": "value", "type_info": "Float8"}, {"ordinal": 3, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Int8"]}, "nullable": [false, false, false, false]}, "hash": "f6936e59514600faa8cf9a67b3c77f3e371eb285d488ce6613d5928366d3a9f3"}