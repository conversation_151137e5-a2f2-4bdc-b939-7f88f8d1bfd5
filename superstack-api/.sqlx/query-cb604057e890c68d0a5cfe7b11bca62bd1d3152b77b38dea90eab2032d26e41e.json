{"db_name": "PostgreSQL", "query": "\n                UPDATE wallet_activity SET\n                    activity_type = $2,\n                    token_mint = $3,\n                    token_decimals = $4,\n                    token_amount = $5,\n                    base_mint = $6,\n                    base_decimals = $7,\n                    base_amount = $8,\n                    usd_value = $9,\n                    timestamp = $10,\n                    block_time = $11,\n                    slot = $12,\n                    metadata = $13\n                WHERE wallet_address = $1\n                  AND chain = $14\n                  AND (metadata->>'oid' = $15::text OR metadata->>'order_id' = $15::text)\n                ", "describe": {"columns": [], "parameters": {"Left": ["Text", "Text", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8", "Float8", "Int8", "Int8", "Int8", "Jsonb", "Int2", "Text"]}, "nullable": []}, "hash": "cb604057e890c68d0a5cfe7b11bca62bd1d3152b77b38dea90eab2032d26e41e"}