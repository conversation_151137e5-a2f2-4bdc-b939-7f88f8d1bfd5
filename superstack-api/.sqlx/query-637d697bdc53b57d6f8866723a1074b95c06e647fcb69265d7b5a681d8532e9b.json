{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, timestamp, value, chain FROM account_values WHERE wallet_address = $1 ORDER BY timestamp DESC LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 2, "name": "value", "type_info": "Float8"}, {"ordinal": 3, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false]}, "hash": "637d697bdc53b57d6f8866723a1074b95c06e647fcb69265d7b5a681d8532e9b"}