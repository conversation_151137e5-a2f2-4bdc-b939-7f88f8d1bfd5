{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, latest_tx_signature, latest_tx_slot, updated_at\n            FROM wallets WHERE wallet_address = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "latest_tx_signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "latest_tx_slot", "type_info": "Int8"}, {"ordinal": 3, "name": "updated_at", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [false, true, true, true]}, "hash": "f283f34d4119bb2655f070f9d375453cac73f5b15a6947ef5dabe725df580a40"}