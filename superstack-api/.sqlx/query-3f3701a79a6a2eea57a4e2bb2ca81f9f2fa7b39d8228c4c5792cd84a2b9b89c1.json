{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM transactions\n            WHERE wallet_address = $1 AND status = $2 AND is_processed = false AND chain = $3\n            ORDER BY created_at ASC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "status", "type_info": "Text"}, {"ordinal": 3, "name": "is_processed", "type_info": "Bool"}, {"ordinal": 4, "name": "created_at", "type_info": "Int8"}, {"ordinal": 5, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Text", "Int2"]}, "nullable": [false, false, false, false, false, false]}, "hash": "3f3701a79a6a2eea57a4e2bb2ca81f9f2fa7b39d8228c4c5792cd84a2b9b89c1"}