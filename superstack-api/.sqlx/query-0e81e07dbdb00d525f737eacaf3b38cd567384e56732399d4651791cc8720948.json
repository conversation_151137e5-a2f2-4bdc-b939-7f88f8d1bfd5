{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, timestamp, value, chain FROM account_values WHERE wallet_address = $1 AND timestamp >= $2 AND timestamp <= $3\n            ORDER BY timestamp DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 2, "name": "value", "type_info": "Float8"}, {"ordinal": 3, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Int8", "Int8"]}, "nullable": [false, false, false, false]}, "hash": "0e81e07dbdb00d525f737eacaf3b38cd567384e56732399d4651791cc8720948"}