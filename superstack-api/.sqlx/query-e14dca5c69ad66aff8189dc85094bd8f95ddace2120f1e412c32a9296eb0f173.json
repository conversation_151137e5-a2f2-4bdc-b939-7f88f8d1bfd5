{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, image_path, created_at\n            FROM pnl_share_images\n            WHERE wallet_address = $1\n            ORDER BY created_at DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "image_path", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false]}, "hash": "e14dca5c69ad66aff8189dc85094bd8f95ddace2120f1e412c32a9296eb0f173"}