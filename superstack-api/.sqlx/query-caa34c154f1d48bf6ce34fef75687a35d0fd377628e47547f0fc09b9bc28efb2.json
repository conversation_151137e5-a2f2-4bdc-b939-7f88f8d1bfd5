{"db_name": "PostgreSQL", "query": "\n            WITH RECURSIVE referral_chain AS (\n                -- Base case: direct referrer\n                SELECT referrer_wallet, referee_wallet, 1 as level\n                FROM referrals\n                WHERE referee_wallet = $1\n                AND referrer_wallet IS NOT NULL\n                AND referrer_wallet != ''\n\n                UNION ALL\n\n                -- Recursive case: up to 3 levels\n                SELECT r.referrer_wallet, r.referee_wallet, rc.level + 1\n                FROM referrals r\n                INNER JOIN referral_chain rc ON r.referee_wallet = rc.referrer_wallet\n                WHERE rc.level < 3\n                AND r.referrer_wallet IS NOT NULL\n                AND r.referrer_wallet != ''\n            )\n            SELECT referrer_wallet, level FROM referral_chain\n            ORDER BY level\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referrer_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "level", "type_info": "Int4"}], "parameters": {"Left": ["Text"]}, "nullable": [null, null]}, "hash": "caa34c154f1d48bf6ce34fef75687a35d0fd377628e47547f0fc09b9bc28efb2"}