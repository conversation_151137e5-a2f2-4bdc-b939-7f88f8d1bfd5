{"db_name": "PostgreSQL", "query": "\n            INSERT INTO transactions (\n                signature, wallet_address, status, is_processed, created_at, chain\n            ) VALUES ($1, $2, $3, $4, $5, $6)\n            ON CONFLICT (signature) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Text", "Bool", "Int8", "Int2"]}, "nullable": []}, "hash": "beda1c865ad70c789c0f008d99f817ca85e9d0b4c605c95b53521f70eb55c9f4"}