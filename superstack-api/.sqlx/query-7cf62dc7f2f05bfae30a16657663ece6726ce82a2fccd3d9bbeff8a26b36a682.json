{"db_name": "PostgreSQL", "query": "\n            INSERT INTO orders (\n                order_id,\n                tx_sig, wallet_address, \n                token_mint, token_decimals, \n                base_mint, base_decimals, \n                trade_type, \n                token_amount, remaining_token_amount,\n                base_amount, remaining_base_amount,\n                timestamp, slot, fee,\n                is_cancelled, is_completed,\n                fee_bps, chain\n            )\n            VALUES (\n                $1, \n                $2, $3, \n                $4, $5, \n                $6, $7, \n                $8,\n                $9, $10, \n                $11, $12, \n                $13, $14, $15,\n                $16, $17, \n                $18, $19\n            )\n            ON CONFLICT (order_id) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": ["Text", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Text", "Int8", "Int8", "Int8", "Int8", "Int8", "Int8", "Int8", "Bool", "Bool", "Int2", "Int2"]}, "nullable": []}, "hash": "7cf62dc7f2f05bfae30a16657663ece6726ce82a2fccd3d9bbeff8a26b36a682"}