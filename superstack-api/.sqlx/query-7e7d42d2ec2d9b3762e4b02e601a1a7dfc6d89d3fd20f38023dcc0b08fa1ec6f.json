{"db_name": "PostgreSQL", "query": "\n            SELECT id, referrer_wallet, referee_wallet, reward_type, trading_volume_usd, tx_signature, is_claimed, created_at, claimed_at\n            FROM referral_rewards\n            WHERE referrer_wallet = $1\n            ORDER BY created_at DESC\n            LIMIT $2 OFFSET $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int8"}, {"ordinal": 1, "name": "referrer_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "referee_wallet", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "reward_type", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "trading_volume_usd", "type_info": "Numeric"}, {"ordinal": 5, "name": "tx_signature", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "is_claimed", "type_info": "Bool"}, {"ordinal": 7, "name": "created_at", "type_info": "Int8"}, {"ordinal": 8, "name": "claimed_at", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Int8", "Int8"]}, "nullable": [false, false, false, false, true, true, false, false, true]}, "hash": "7e7d42d2ec2d9b3762e4b02e601a1a7dfc6d89d3fd20f38023dcc0b08fa1ec6f"}