{"db_name": "PostgreSQL", "query": "\n            SELECT * FROM trades WHERE wallet_address = $1 AND chain = $2\n            ORDER BY timestamp DESC\n            LIMIT $3\n            OFFSET $4\n            ", "describe": {"columns": [{"ordinal": 0, "name": "tx_sig", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "token_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "token_decimals", "type_info": "Int2"}, {"ordinal": 4, "name": "base_mint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "base_decimals", "type_info": "Int2"}, {"ordinal": 6, "name": "trade_type", "type_info": "Text"}, {"ordinal": 7, "name": "token_amount", "type_info": "Int8"}, {"ordinal": 8, "name": "base_amount", "type_info": "Int8"}, {"ordinal": 9, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 10, "name": "slot", "type_info": "Int8"}, {"ordinal": 11, "name": "fee", "type_info": "Int8"}, {"ordinal": 12, "name": "order_id", "type_info": "Text"}, {"ordinal": 13, "name": "remaining_token_amount", "type_info": "Int8"}, {"ordinal": 14, "name": "remaining_base_amount", "type_info": "Int8"}, {"ordinal": 15, "name": "token_fee_amount", "type_info": "Int8"}, {"ordinal": 16, "name": "base_fee_amount", "type_info": "Int8"}, {"ordinal": 17, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Int2", "Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, false, false, false]}, "hash": "241d07f33a1eee2cbd7ece2ef2070f11dd222db55baa16e28185891cc50a6480"}