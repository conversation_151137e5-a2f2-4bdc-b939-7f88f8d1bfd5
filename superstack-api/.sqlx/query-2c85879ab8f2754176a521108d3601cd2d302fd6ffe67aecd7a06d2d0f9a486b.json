{"db_name": "PostgreSQL", "query": "\n            INSERT INTO pnl_share_images (wallet_address, image_path, created_at)\n            VALUES ($1, $2, $3)\n            RETURNING wallet_address, image_path, created_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "image_path", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8"]}, "nullable": [false, false, false]}, "hash": "2c85879ab8f2754176a521108d3601cd2d302fd6ffe67aecd7a06d2d0f9a486b"}