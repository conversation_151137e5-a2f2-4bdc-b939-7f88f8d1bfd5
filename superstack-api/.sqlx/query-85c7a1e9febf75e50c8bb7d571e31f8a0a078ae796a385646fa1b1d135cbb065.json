{"db_name": "PostgreSQL", "query": "\n            SELECT wallet_address, timestamp, value, chain FROM account_values\n            WHERE wallet_address = $1 AND value > 0\n            ORDER BY timestamp ASC LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "wallet_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "timestamp", "type_info": "Int8"}, {"ordinal": 2, "name": "value", "type_info": "Float8"}, {"ordinal": 3, "name": "chain", "type_info": "Int2"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false]}, "hash": "85c7a1e9febf75e50c8bb7d571e31f8a0a078ae796a385646fa1b1d135cbb065"}