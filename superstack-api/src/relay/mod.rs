use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::{config::Config, utils::get_reqwest_client};

#[derive(Debug, Clone)]
pub struct CliqueRelayService;

impl CliqueRelayService {
    pub async fn get_quote(params: &GetQuoteParams) -> anyhow::Result<Value> {
        let client = get_reqwest_client();
        let api_base = &Config::get().clique_relay_api_base;
        let url = format!("{}/api/v1/quote", api_base);
        let response = client.post(url).json(params).send().await?;
        let quote = response.json::<Value>().await?;

        Ok(quote)
    }

    pub async fn get_swap(params: &GetSwapParams) -> anyhow::Result<Value> {
        let client = get_reqwest_client();
        let api_base = &Config::get().clique_relay_api_base;
        let url = format!("{}/api/v1/swap", api_base);
        let response = client.post(url).json(params).send().await?;

        let swap = response.json::<Value>().await?;

        Ok(swap)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetQuoteParams {
    pub from_token: String,
    pub to_token: String,
    pub amount: String,
    pub slippage: u64,
    pub protocols: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuoteResponse {
    pub request_id: String,
    pub amount_in: String,
    pub total_estimated_amount_out: String,
    pub min_amount_out_with_slippage: String,
    pub effective_rate: String,
    pub pool_breakdown: Vec<Value>,
    pub in_decimal: u64,
    pub out_decimal: u64,
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetSwapParams {
    pub request_id: String,
    pub user_wallet_address: String,
    pub return_transaction: bool,
    pub max_priority_lamports: u64,
    pub max_gas_lamports: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapResponse {
    pub success: bool,
    pub swap_transaction: Option<String>,
    pub request_id: String,
    pub timestamp: u64,
    pub worker_id: u64,
    pub error: Option<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_quote() {
        dotenv::dotenv().ok();
        let params = GetQuoteParams {
            from_token: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(),
            to_token: "So11111111111111111111111111111111111111112".to_string(),
            amount: "1.0".to_string(),
            slippage: 500,
            protocols: "meteora_dlmm".to_string(),
        };

        let quote = CliqueRelayService::get_quote(&params).await.unwrap();

        println!("{:?}", quote);

        let swap_params = GetSwapParams {
            request_id: quote["requestId"].as_str().unwrap().to_string(),
            user_wallet_address: "CY4ScQyddbUJy53JU32X2UZbMbeXmmxNFd22rDtqeFsW".to_string(),
            return_transaction: true,
            max_priority_lamports: 1000000,
            max_gas_lamports: 1000000,
        };

        let swap = CliqueRelayService::get_swap(&swap_params).await.unwrap();

        println!("{:?}", swap);
    }
}
