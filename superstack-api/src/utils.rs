#[allow(unused)]
use std::{
    str::FromStr,
    sync::OnceLock,
    time::{Duration, Instant},
};

use alloy::providers::{DynProvider, Provider, ProviderBuilder};
use anyhow::Result;
use reqwest::Client;
use sha3::{Digest, Keccak256};
use solana_client::{nonblocking::rpc_client::RpcClient, rpc_request::TokenAccountsFilter};
use solana_sdk::{
    commitment_config::CommitmentConfig,
    native_token::{lamports_to_sol, sol_to_lamports},
    pubkey::Pubkey,
};
use superstack_data::postgres::enums::Chain;

use tokio::sync::{Mutex, OnceCell, RwLock};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::config::Config;

pub mod image;
pub mod price_calculator;

pub const CACHE_TIMEOUT: Duration = Duration::from_secs(5);
pub const DEFAULT_SOL_PRICE: f64 = 170.0;

pub fn setup_tracing() {
    let _ = tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
        ))
        .with(tracing_subscriber::fmt::layer())
        .try_init();
}

pub fn get_reqwest_client() -> &'static Client {
    static CLIENT: OnceLock<Client> = OnceLock::new();
    CLIENT.get_or_init(Client::new)
}

pub fn get_rpc_client() -> &'static RpcClient {
    static RPC_CLIENT: OnceLock<RpcClient> = OnceLock::new();
    RPC_CLIENT.get_or_init(|| {
        let config = Config::get();
        RpcClient::new_with_timeout_and_commitment(
            config.solana_rpc_url.clone(),
            Duration::from_secs(10),
            CommitmentConfig::confirmed(),
        )
    })
}

pub fn get_provider() -> &'static DynProvider {
    static PROVIDER: OnceLock<DynProvider> = OnceLock::new();

    PROVIDER.get_or_init(|| {
        let config = Config::get();
        let url = config.hyperevm_rpc_url.clone();
        let p = ProviderBuilder::new().connect_http(url.parse().unwrap());
        p.erased()
    })
}

pub fn lamports_to_token(lamports: u64, decimals: u8) -> f64 {
    lamports as f64 / (10_u64.pow(decimals as _) as f64)
}

pub fn token_to_lamports(token: f64, decimals: u8) -> u64 {
    (token * (10_u64.pow(decimals as _) as f64)) as u64
}

pub async fn get_sol_price() -> f64 {
    get_sol_price_with_cache().await
}

pub fn usd_to_sol_amount(usd: f64, sol_price: f64) -> u64 {
    let ui_amount = usd / sol_price;
    sol_to_lamports(ui_amount)
}

/// Timestamp utility functions for handling both millisecond and second timestamps
pub mod timestamp {
    /// Convert timestamp to seconds if it appears to be in milliseconds
    /// This handles both millisecond and second timestamps automatically
    pub fn normalize_to_seconds(timestamp: u64) -> i64 {
        // If timestamp is greater than 1e12 (year 2001 in milliseconds),
        // it's likely in milliseconds and should be converted to seconds
        if timestamp > 1_000_000_000_000 {
            (timestamp / 1000) as i64
        } else {
            timestamp as i64
        }
    }

    /// Validate that the timestamp is within a reasonable range
    pub fn validate_range(timestamp_seconds: i64) -> bool {
        // Allow timestamps from 2000 onwards (reasonable range for trading data)
        let min_timestamp = 946684800; // 2000-01-01 00:00:00 UTC
        timestamp_seconds >= min_timestamp
    }

    /// Normalize and validate timestamp in one step, returning i64 seconds
    pub fn normalize_and_validate(timestamp: u64) -> Result<i64, &'static str> {
        let normalized = normalize_to_seconds(timestamp);
        Ok(normalized)
        // TODO: Re-enable timestamp validation
        // if validate_range(normalized) {
        //     Ok(normalized)
        // } else {
        //     Err("Timestamp out of valid range")
        // }
    }

    /// Normalize and validate optional timestamp
    pub fn normalize_and_validate_optional(
        timestamp: Option<u64>,
    ) -> Result<Option<i64>, &'static str> {
        match timestamp {
            Some(ts) => normalize_and_validate(ts).map(Some),
            None => Ok(None),
        }
    }
}

pub async fn get_sol_balance(wallet_address: Pubkey) -> Result<f64> {
    let rpc_client = get_rpc_client();
    let balance = rpc_client.get_balance(&wallet_address).await?;
    Ok(lamports_to_sol(balance))
}

async fn fetch_sol_price_with_fallback() -> Result<f64> {
    if let Ok(price) = crate::jupiter::price::get_sol_price().await {
        return Ok(price);
    } else {
        tracing::warn!("Failed to fetch SOL price from Jupiter, trying Pump.fun...");
    }

    if let Ok(price) = crate::pump::crawler::PumpCrawler::get().fetch_sol_price().await {
        return Ok(price);
    } else {
        tracing::warn!("Failed to fetch SOL price from Pump.fun");
    }

    Err(anyhow::anyhow!("Failed to fetch SOL price from Jupiter or Pump.fun."))
}

async fn get_sol_price_with_cache() -> f64 {
    static SOL_PRICE: OnceCell<RwLock<(f64, Instant)>> = OnceCell::const_new();
    static UPDATE_LOCK: OnceCell<Mutex<()>> = OnceCell::const_new();

    let cache = SOL_PRICE
        .get_or_init(|| async {
            let sol_price = fetch_sol_price_with_fallback().await.unwrap_or(DEFAULT_SOL_PRICE);
            RwLock::new((sol_price, Instant::now()))
        })
        .await;

    let update_lock = UPDATE_LOCK.get_or_init(|| async { tokio::sync::Mutex::new(()) }).await;

    // Check if cache is valid
    let old_price = {
        let cached = cache.read().await;
        if cached.1.elapsed() < CACHE_TIMEOUT {
            return cached.0;
        } else {
            cached.0
        }
    };

    // Try to acquire update lock - non-blocking
    if let Ok(_update_guard) = update_lock.try_lock() {
        {
            // Check if cache is updated by another thread
            let cached = cache.read().await;
            if cached.1.elapsed() < CACHE_TIMEOUT {
                return cached.0;
            }
        }

        tracing::info!("Updating SOL price cache...");

        // Got the update lock, fetch new price
        let price_result = fetch_sol_price_with_fallback().await;

        // Update cache with result
        let now = Instant::now();
        let mut cache_write = cache.write().await;
        match price_result {
            Ok(price) => {
                *cache_write = (price, now);
                return price;
            }
            Err(e) => {
                tracing::warn!("Failed to update SOL price cache: {}, using stale price", e);
                // Trigger cache update again 3 seconds later
                cache_write.1 = now - CACHE_TIMEOUT + Duration::from_secs(3);
                return cache_write.0;
            }
        }
        // Update lock is released when update_guard goes out of scope
    }

    // Couldn't get update lock, use current value
    old_price
}

/// Convert Solana public key to EVM address
/// This uses the same derivation method as used in HyperEVM
pub fn solana_pubkey_to_evm_address(pubkey: &Pubkey) -> String {
    // Get the 32-byte public key
    let pubkey_bytes = pubkey.to_bytes();

    // Hash with Keccak256
    let mut hasher = Keccak256::new();
    hasher.update(&pubkey_bytes);
    let hash = hasher.finalize();

    // Take the last 20 bytes and format as hex with 0x prefix
    let address_bytes = &hash[12..32];
    format!("0x{}", hex::encode(address_bytes))
}

/// Try to parse address as both Solana and EVM format
pub fn parse_wallet_address(address: &str) -> anyhow::Result<(Option<Pubkey>, Option<String>)> {
    let mut solana_pubkey = None;
    let mut evm_address = None;

    // Try to parse as Solana pubkey
    if let Ok(pubkey) = Pubkey::from_str(address) {
        solana_pubkey = Some(pubkey);
        // Also derive the corresponding EVM address
        evm_address = Some(solana_pubkey_to_evm_address(&pubkey));
    }

    // Try to parse as EVM address
    if address.starts_with("0x") && address.len() == 42 {
        evm_address = Some(address.to_lowercase());
    }

    Ok((solana_pubkey, evm_address))
}

pub async fn get_wallet_token_balance(wallet_address: Pubkey, token_mint: Pubkey) -> Result<u64> {
    let rpc_client = get_rpc_client();

    // Derive the ATA address
    let ata =
        spl_associated_token_account::get_associated_token_address(&wallet_address, &token_mint);

    // Try to get the balance directly from the ATA
    match rpc_client.get_token_account_balance(&ata).await {
        Ok(balance) => Ok(balance.amount.parse::<u64>()?),
        Err(_) => {
            // ATA doesn't exist or has issues, fall back to checking all accounts
            let token_accounts = rpc_client
                .get_token_accounts_by_owner(&wallet_address, TokenAccountsFilter::Mint(token_mint))
                .await?;

            if let Some(token_account) = token_accounts.first() {
                let pubkey = Pubkey::from_str(&token_account.pubkey)?;
                let token_account = rpc_client.get_token_account_balance(&pubkey).await?;
                Ok(token_account.amount.parse::<u64>()?)
            } else {
                // No token accounts found
                Ok(0) // Return 0 instead of error for better UX
            }
        }
    }
}

/// Multi-chain address parsing and validation
pub mod address {
    use super::*;

    /// Represents a parsed address that can be used across different chains
    #[derive(Debug, Clone)]
    pub struct ParsedAddress {
        pub original: String,
        pub chain: Chain,
        pub solana_pubkey: Option<Pubkey>,
        pub evm_address: Option<String>,
    }

    impl ParsedAddress {
        /// Get the address as a string for the specified chain
        pub fn as_string_for_chain(&self, chain: Chain) -> Option<String> {
            match chain {
                Chain::Solana => self.solana_pubkey.map(|pk| pk.to_string()),
                Chain::HyperEvm => self.evm_address.clone(),
                Chain::Hypercore => Some(self.original.clone()), // For now, use original string
            }
        }
    }

    /// Parse an address string and determine its format and supported chains
    pub fn parse_address(address: &str) -> Result<ParsedAddress, String> {
        let address = address.trim();

        // Try to detect chain based on address format
        if address.starts_with("0x") && address.len() == 42 {
            // EVM-style address
            let evm_address = alloy::primitives::Address::from_str(address)
                .map_err(|e| format!("Invalid EVM address: {}", e))?;

            Ok(ParsedAddress {
                original: address.to_string(),
                chain: Chain::HyperEvm,
                solana_pubkey: None,
                evm_address: Some(evm_address.to_string()),
            })
        } else {
            // Try to parse as Solana Base58 address
            match Pubkey::from_str(address) {
                Ok(pubkey) => Ok(ParsedAddress {
                    original: address.to_string(),
                    chain: Chain::Solana,
                    solana_pubkey: Some(pubkey),
                    evm_address: Some(solana_pubkey_to_evm_address(&pubkey)),
                }),
                Err(_) => {
                    // If it's not a valid Solana address or EVM address, assume it's Hypercore
                    if address.is_empty() {
                        return Err("Address cannot be empty".to_string());
                    }

                    Ok(ParsedAddress {
                        original: address.to_string(),
                        chain: Chain::Hypercore,
                        solana_pubkey: None,
                        evm_address: None,
                    })
                }
            }
        }
    }

    /// Parse and validate an address for a specific chain
    pub fn parse_address_for_chain(address: &str, chain: Chain) -> Result<String, String> {
        match chain {
            Chain::Solana => match Pubkey::from_str(address) {
                Ok(pubkey) => Ok(pubkey.to_string()),
                Err(_) => Err("Invalid Solana address format (Base58 expected)".to_string()),
            },
            Chain::HyperEvm => {
                if !address.starts_with("0x") {
                    return Err("EVM address must start with 0x".to_string());
                }
                if address.len() != 42 {
                    return Err("EVM address must be 42 characters long".to_string());
                }
                if !address[2..].chars().all(|c| c.is_ascii_hexdigit()) {
                    return Err("EVM address contains invalid characters".to_string());
                }
                Ok(address.to_lowercase())
            }
            Chain::Hypercore => {
                if address.is_empty() {
                    Err("Hypercore address cannot be empty".to_string())
                } else {
                    Ok(address.to_string())
                }
            }
        }
    }

    /// Auto-detect chain from address format and validate
    pub fn parse_and_detect_chain(address: &str) -> Result<(Chain, String), String> {
        let parsed = parse_address(address)?;
        Ok((parsed.chain, parsed.as_string_for_chain(parsed.chain).unwrap()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_sol_price_with_cache() {
        crate::utils::setup_tracing();

        let mut handles = vec![];

        // Spawn 5 tasks that poll the price cache
        for task_id in 0..5 {
            let handle = tokio::spawn(async move {
                let start = Instant::now();
                let mut count = 0;
                while start.elapsed() < CACHE_TIMEOUT + Duration::from_secs(10) {
                    let price = get_sol_price_with_cache().await;
                    tracing::debug!("Task {} - SOL price: {}", task_id, price);
                    count += 1;
                }
                tracing::info!("Task {} - Count: {}", task_id, count);
            });
            handles.push(handle);
        }

        // Wait for all tasks to complete
        for handle in handles {
            handle.await.unwrap();
        }
    }

    #[tokio::test]
    async fn test_get_wallet_token_balance() {
        crate::utils::setup_tracing();

        let wallet_address =
            Pubkey::from_str("********************************************").unwrap();
        let token_mint = Pubkey::from_str("J8XdCvx7dhkXRPkcuZLmLT5gCGab1tMxvFFyJvaEDJJj").unwrap();
        let balance = get_wallet_token_balance(wallet_address, token_mint).await.unwrap();
        tracing::info!("Balance: {}", balance);
    }

    #[test]
    fn test_address_parsing() {
        use address::*;

        // Test Solana address
        let solana_addr = "3B4575E689DEd21CAAD31d64C4df1f10F3B2CedF";
        let result = parse_address(solana_addr);
        assert!(result.is_ok());
        let parsed = result.unwrap();
        assert_eq!(parsed.chain, Chain::Solana);
        assert!(parsed.solana_pubkey.is_some());

        // Test EVM address
        let evm_addr = "******************************************";
        let result = parse_address(evm_addr);
        assert!(result.is_ok());
        let parsed = result.unwrap();
        assert_eq!(parsed.chain, Chain::HyperEvm);
        assert!(parsed.evm_address.is_some());

        // Test chain-specific parsing
        let result = parse_address_for_chain(evm_addr, Chain::HyperEvm);
        assert!(result.is_ok());

        let result = parse_address_for_chain(evm_addr, Chain::Solana);
        assert!(result.is_err());

        // Test auto-detection
        let (chain, _) = parse_and_detect_chain(evm_addr).unwrap();
        assert_eq!(chain, Chain::HyperEvm);
    }
}

/// Get the appropriate base symbol for display purposes across different chains
pub fn get_base_symbol_for_chain(chain: Chain, base_address: &str) -> anyhow::Result<&'static str> {
    match chain {
        Chain::Solana => match base_address {
            crate::constant::SOL_MINT => Ok("SOL"),
            crate::constant::USDC_MINT => Ok("USDC"),
            crate::constant::USDT_MINT => Ok("USDT"),
            _ => {
                anyhow::bail!("Unsupported Solana base mint: {}", base_address)
            }
        },
        Chain::Hypercore => {
            // For Hypercore, all base tokens are USD-based (USDC or similar)
            // We can safely return "USDC" as the symbol for display purposes
            Ok("USDC")
        }
        Chain::HyperEvm => {
            // For HyperEVM, check if it's the native HYPE token or other tokens
            if base_address == crate::constant::HYPE_NATIVE_TOKEN {
                Ok("HYPE")
            } else {
                // For other EVM tokens, we could look up the symbol, but for now use a generic
                // label In the future, this could be enhanced to query the actual token symbol
                Ok("USD")
            }
        }
    }
}
