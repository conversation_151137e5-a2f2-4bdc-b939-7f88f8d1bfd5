{"address": "j1o2qRpjcyUwEvwtcfhEQefh773ZgjxcVRry7LDqg5X", "metadata": {"name": "limit_order_2", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "cancel_dust_order", "discriminator": [197, 112, 189, 164, 79, 48, 23, 246], "accounts": [{"name": "signer", "signer": true}, {"name": "maker", "writable": true, "relations": ["order"]}, {"name": "order", "writable": true}, {"name": "input_mint_reserve", "writable": true}, {"name": "maker_input_mint_account", "writable": true}, {"name": "input_mint"}, {"name": "input_token_program"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "CancelDustOrderParams"}}}]}, {"name": "cancel_order", "discriminator": [95, 129, 237, 240, 8, 49, 223, 132], "accounts": [{"name": "signer", "signer": true}, {"name": "maker", "writable": true, "relations": ["order"]}, {"name": "order", "writable": true}, {"name": "input_mint_reserve", "writable": true}, {"name": "maker_input_mint_account", "writable": true, "optional": true}, {"name": "input_mint"}, {"name": "input_token_program"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "fill_order", "discriminator": [232, 122, 115, 25, 199, 143, 136, 162], "accounts": [{"name": "taker", "writable": true, "signer": true}, {"name": "maker", "writable": true, "relations": ["order"]}, {"name": "order", "writable": true}, {"name": "taker_input_mint_account", "writable": true}, {"name": "taker_output_mint_account", "writable": true}, {"name": "maker_output_mint_account", "writable": true, "optional": true}, {"name": "fee_account", "writable": true}, {"name": "order_input_mint_account", "writable": true}, {"name": "input_mint"}, {"name": "input_token_program"}, {"name": "output_mint"}, {"name": "output_token_program"}, {"name": "jupiter_program", "address": "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "FillOrderParams"}}}]}, {"name": "flash_fill_order", "discriminator": [252, 104, 18, 134, 164, 78, 18, 140], "accounts": [{"name": "taker", "writable": true, "signer": true}, {"name": "maker", "writable": true, "relations": ["order"]}, {"name": "order", "writable": true}, {"name": "input_mint_reserve", "writable": true}, {"name": "maker_output_mint_account", "writable": true, "optional": true}, {"name": "taker_output_mint_account", "writable": true, "optional": true}, {"name": "fee_account", "writable": true}, {"name": "input_token_program"}, {"name": "output_mint"}, {"name": "output_token_program"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "FlashFillOrderParams"}}}]}, {"name": "initialize_order", "discriminator": [133, 110, 74, 175, 112, 159, 245, 159], "accounts": [{"name": "payer", "writable": true, "signer": true}, {"name": "maker", "signer": true}, {"name": "order", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [111, 114, 100, 101, 114]}, {"kind": "account", "path": "maker"}, {"kind": "arg", "path": "unique_id"}]}}, {"name": "input_mint_reserve", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "order"}, {"kind": "account", "path": "input_token_program"}, {"kind": "account", "path": "input_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "maker_input_mint_account", "writable": true}, {"name": "fee", "pda": {"seeds": [{"kind": "const", "value": [102, 101, 101]}]}}, {"name": "referral", "optional": true}, {"name": "input_mint"}, {"name": "output_mint"}, {"name": "input_token_program"}, {"name": "output_token_program"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "InitializeOrderParams"}}}]}, {"name": "pre_flash_fill_order", "discriminator": [240, 47, 153, 68, 13, 190, 225, 42], "accounts": [{"name": "taker", "signer": true}, {"name": "order", "writable": true}, {"name": "input_mint_reserve", "writable": true}, {"name": "taker_input_mint_account", "writable": true}, {"name": "input_mint"}, {"name": "input_token_program"}, {"name": "instruction", "address": "Sysvar1nstructions1111111111111111111111111"}], "args": [{"name": "params", "type": {"defined": {"name": "PreFlashFillOrderParams"}}}]}, {"name": "update_fee", "discriminator": [232, 253, 195, 247, 148, 212, 73, 222], "accounts": [{"name": "admin", "writable": true, "signer": true}, {"name": "fee_authority", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [102, 101, 101]}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "params", "type": {"defined": {"name": "UpdateFeeParams"}}}]}, {"name": "withdraw_fee", "discriminator": [14, 122, 231, 218, 31, 238, 223, 150], "accounts": [{"name": "admin", "writable": true, "signer": true}, {"name": "fee_authority", "docs": ["CHECK"], "pda": {"seeds": [{"kind": "const", "value": [102, 101, 101]}]}}, {"name": "program_fee_account", "writable": true}, {"name": "destination_token_account", "writable": true}, {"name": "token_program"}, {"name": "mint"}], "args": []}], "accounts": [{"name": "Fee", "discriminator": [24, 55, 150, 250, 168, 27, 101, 178]}, {"name": "Order", "discriminator": [134, 173, 223, 185, 77, 86, 28, 51]}], "events": [{"name": "CancelDustOrderEvent", "discriminator": [124, 190, 74, 28, 177, 40, 200, 220]}, {"name": "CancelOrderEvent", "discriminator": [174, 66, 141, 17, 4, 224, 162, 77]}, {"name": "CreateOrderEvent", "discriminator": [49, 142, 72, 166, 230, 29, 84, 84]}, {"name": "TradeEvent", "discriminator": [189, 219, 127, 211, 78, 230, 97, 238]}], "errors": [{"code": 6000, "name": "InvalidMakingAmount"}, {"code": 6001, "name": "InvalidTakingAmount"}, {"code": 6002, "name": "InvalidMaxTakingAmount"}, {"code": 6003, "name": "InvalidInputAccount"}, {"code": 6004, "name": "InvalidMakerOutputAccount"}, {"code": 6005, "name": "InvalidTakerOutputAccount"}, {"code": 6006, "name": "InvalidPair"}, {"code": 6007, "name": "OrderExpired"}, {"code": 6008, "name": "OrderNotExpired"}, {"code": 6009, "name": "InvalidAdmin"}, {"code": 6010, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 6011, "name": "MathOverflow"}, {"code": 6012, "name": "ProgramMismatch"}, {"code": 6013, "name": "UnknownInstruction"}, {"code": 6014, "name": "MissingRepayInstructions"}, {"code": 6015, "name": "InvalidOrder"}, {"code": 6016, "name": "InvalidBorrowMakingAmount"}, {"code": 6017, "name": "InvalidExpiredDate"}, {"code": 6018, "name": "FeeBpsTooLow"}, {"code": 6019, "name": "FeeBpsTooHigh"}, {"code": 6020, "name": "TransferTaxMint"}, {"code": 6021, "name": "InvalidOutputMint"}, {"code": 6022, "name": "InvalidMinTakingAmount"}, {"code": 6023, "name": "InvalidSwapData"}, {"code": 6024, "name": "InvalidSwapInputAmount"}, {"code": 6025, "name": "InvalidSwappedAmount"}, {"code": 6026, "name": "InvalidDustOrder"}, {"code": 6027, "name": "UnsupportedMint"}], "types": [{"name": "CancelDustOrderEvent", "type": {"kind": "struct", "fields": [{"name": "order_key", "type": "pubkey"}, {"name": "one_usd_input_mint_amount", "type": "u64"}]}}, {"name": "CancelDustOrderParams", "type": {"kind": "struct", "fields": [{"name": "one_usd_input_mint_amount", "type": "u64"}]}}, {"name": "CancelOrderEvent", "type": {"kind": "struct", "fields": [{"name": "order_key", "type": "pubkey"}]}}, {"name": "CreateOrderEvent", "type": {"kind": "struct", "fields": [{"name": "order_key", "type": "pubkey"}, {"name": "maker", "type": "pubkey"}, {"name": "input_mint", "type": "pubkey"}, {"name": "output_mint", "type": "pubkey"}, {"name": "input_token_program", "type": "pubkey"}, {"name": "output_token_program", "type": "pubkey"}, {"name": "making_amount", "type": "u64"}, {"name": "taking_amount", "type": "u64"}, {"name": "expired_at", "type": {"option": "i64"}}, {"name": "fee_bps", "type": "u16"}, {"name": "fee_account", "type": "pubkey"}, {"name": "slippage_bps", "type": "u16"}]}}, {"name": "Fee", "type": {"kind": "struct", "fields": [{"name": "normal_fee_bps", "type": "u16"}, {"name": "stable_fee_bps", "type": "u16"}]}}, {"name": "FillOrderParams", "type": {"kind": "struct", "fields": [{"name": "input_amount", "type": "u64"}, {"name": "swap_data", "type": "bytes"}]}}, {"name": "FlashFillOrderParams", "type": {"kind": "struct", "fields": [{"name": "max_taking_amount", "type": "u64"}]}}, {"name": "InitializeOrderParams", "type": {"kind": "struct", "fields": [{"name": "unique_id", "type": "u64"}, {"name": "making_amount", "type": "u64"}, {"name": "taking_amount", "type": "u64"}, {"name": "expired_at", "type": {"option": "i64"}}, {"name": "fee_bps", "type": {"option": "u16"}}, {"name": "slippage_bps", "type": {"option": "u16"}}]}}, {"name": "Order", "type": {"kind": "struct", "fields": [{"name": "maker", "type": "pubkey"}, {"name": "input_mint", "type": "pubkey"}, {"name": "output_mint", "type": "pubkey"}, {"name": "input_token_program", "type": "pubkey"}, {"name": "output_token_program", "type": "pubkey"}, {"name": "input_mint_reserve", "type": "pubkey"}, {"name": "unique_id", "type": "u64"}, {"name": "ori_making_amount", "type": "u64"}, {"name": "ori_taking_amount", "type": "u64"}, {"name": "making_amount", "type": "u64"}, {"name": "taking_amount", "type": "u64"}, {"name": "borrow_making_amount", "type": "u64"}, {"name": "expired_at", "type": {"option": "i64"}}, {"name": "fee_bps", "type": "u16"}, {"name": "fee_account", "type": "pubkey"}, {"name": "created_at", "type": "i64"}, {"name": "updated_at", "type": "i64"}, {"name": "bump", "type": "u8"}, {"name": "slippage_bps", "type": "u16"}]}}, {"name": "PreFlashFillOrderParams", "type": {"kind": "struct", "fields": [{"name": "making_amount", "type": "u64"}]}}, {"name": "TradeEvent", "type": {"kind": "struct", "fields": [{"name": "order_key", "type": "pubkey"}, {"name": "taker", "type": "pubkey"}, {"name": "remaining_making_amount", "type": "u64"}, {"name": "remaining_taking_amount", "type": "u64"}, {"name": "making_amount", "type": "u64"}, {"name": "taking_amount", "type": "u64"}]}}, {"name": "UpdateFeeParams", "type": {"kind": "struct", "fields": [{"name": "normal_fee_bps", "type": "u16"}, {"name": "stable_fee_bps", "type": "u16"}]}}]}