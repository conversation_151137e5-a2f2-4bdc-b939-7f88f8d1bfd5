{"address": "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4", "metadata": {"name": "jupiter", "version": "0.1.0", "spec": "0.1.0", "description": "Jupiter aggregator program"}, "instructions": [{"name": "claim", "discriminator": [62, 198, 214, 193, 213, 159, 108, 210], "accounts": [{"name": "wallet", "writable": true, "address": "J434EKW6KDmnJHxVty1axHT6kjszKKFEyesKqxdQ7y64"}, {"name": "program_authority", "writable": true}, {"name": "system_program", "address": "********************************"}], "args": [{"name": "id", "type": "u8"}], "returns": "u64"}, {"name": "claim_token", "discriminator": [116, 206, 27, 191, 166, 19, 0, 73], "accounts": [{"name": "payer", "writable": true, "signer": true}, {"name": "wallet", "address": "J434EKW6KDmnJHxVty1axHT6kjszKKFEyesKqxdQ7y64"}, {"name": "program_authority"}, {"name": "program_token_account", "writable": true}, {"name": "destination_token_account", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "wallet"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "mint"}, {"name": "token_program"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "system_program", "address": "********************************"}], "args": [{"name": "id", "type": "u8"}], "returns": "u64"}, {"name": "close_token", "discriminator": [26, 74, 236, 151, 104, 64, 183, 249], "accounts": [{"name": "operator", "signer": true}, {"name": "wallet", "writable": true, "address": "J434EKW6KDmnJHxVty1axHT6kjszKKFEyesKqxdQ7y64"}, {"name": "program_authority"}, {"name": "program_token_account", "writable": true}, {"name": "mint", "writable": true}, {"name": "token_program"}], "args": [{"name": "id", "type": "u8"}, {"name": "burn_all", "type": "bool"}]}, {"name": "create_open_orders", "discriminator": [229, 194, 212, 172, 8, 10, 134, 147], "accounts": [{"name": "open_orders", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [111, 112, 101, 110, 95, 111, 114, 100, 101, 114, 115]}, {"kind": "account", "path": "market"}, {"kind": "account", "path": "payer"}]}}, {"name": "payer", "writable": true, "signer": true}, {"name": "dex_program"}, {"name": "system_program", "address": "********************************"}, {"name": "rent", "address": "SysvarRent********************************1"}, {"name": "market"}], "args": []}, {"name": "create_program_open_orders", "discriminator": [28, 226, 32, 148, 188, 136, 113, 171], "accounts": [{"name": "open_orders", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [111, 112, 101, 110, 95, 111, 114, 100, 101, 114, 115]}, {"kind": "account", "path": "market"}, {"kind": "account", "path": "program_authority"}]}}, {"name": "payer", "writable": true, "signer": true}, {"name": "program_authority"}, {"name": "dex_program"}, {"name": "system_program", "address": "********************************"}, {"name": "rent", "address": "SysvarRent********************************1"}, {"name": "market"}], "args": [{"name": "id", "type": "u8"}]}, {"name": "create_token_ledger", "discriminator": [232, 242, 197, 253, 240, 143, 129, 52], "accounts": [{"name": "token_ledger", "writable": true, "signer": true}, {"name": "payer", "writable": true, "signer": true}, {"name": "system_program", "address": "********************************"}], "args": []}, {"name": "create_token_account", "discriminator": [147, 241, 123, 100, 244, 132, 174, 118], "accounts": [{"name": "token_account", "writable": true}, {"name": "user", "writable": true, "signer": true}, {"name": "mint"}, {"name": "token_program"}, {"name": "system_program", "address": "********************************"}], "args": [{"name": "bump", "type": "u8"}]}, {"name": "exact_out_route", "discriminator": [208, 51, 239, 151, 123, 43, 237, 92], "accounts": [{"name": "token_program"}, {"name": "user_transfer_authority", "signer": true}, {"name": "user_source_token_account", "writable": true}, {"name": "user_destination_token_account", "writable": true}, {"name": "destination_token_account", "writable": true, "optional": true}, {"name": "source_mint"}, {"name": "destination_mint"}, {"name": "platform_fee_account", "writable": true, "optional": true}, {"name": "token_2022_program", "optional": true}, {"name": "event_authority", "address": "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf"}, {"name": "program"}], "args": [{"name": "route_plan", "type": {"vec": {"defined": {"name": "RoutePlanStep"}}}}, {"name": "out_amount", "type": "u64"}, {"name": "quoted_in_amount", "type": "u64"}, {"name": "slippage_bps", "type": "u16"}, {"name": "platform_fee_bps", "type": "u8"}], "returns": "u64"}, {"name": "route", "docs": ["route_plan Topologically sorted trade DAG"], "discriminator": [229, 23, 203, 151, 122, 227, 173, 42], "accounts": [{"name": "token_program"}, {"name": "user_transfer_authority", "signer": true}, {"name": "user_source_token_account", "writable": true}, {"name": "user_destination_token_account", "writable": true}, {"name": "destination_token_account", "writable": true, "optional": true}, {"name": "destination_mint"}, {"name": "platform_fee_account", "writable": true, "optional": true}, {"name": "event_authority", "address": "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf"}, {"name": "program"}], "args": [{"name": "route_plan", "type": {"vec": {"defined": {"name": "RoutePlanStep"}}}}, {"name": "in_amount", "type": "u64"}, {"name": "quoted_out_amount", "type": "u64"}, {"name": "slippage_bps", "type": "u16"}, {"name": "platform_fee_bps", "type": "u8"}], "returns": "u64"}, {"name": "route_with_token_ledger", "discriminator": [150, 86, 71, 116, 167, 93, 14, 104], "accounts": [{"name": "token_program"}, {"name": "user_transfer_authority", "signer": true}, {"name": "user_source_token_account", "writable": true}, {"name": "user_destination_token_account", "writable": true}, {"name": "destination_token_account", "writable": true, "optional": true}, {"name": "destination_mint"}, {"name": "platform_fee_account", "writable": true, "optional": true}, {"name": "token_ledger"}, {"name": "event_authority", "address": "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf"}, {"name": "program"}], "args": [{"name": "route_plan", "type": {"vec": {"defined": {"name": "RoutePlanStep"}}}}, {"name": "quoted_out_amount", "type": "u64"}, {"name": "slippage_bps", "type": "u16"}, {"name": "platform_fee_bps", "type": "u8"}], "returns": "u64"}, {"name": "set_token_ledger", "discriminator": [228, 85, 185, 112, 78, 79, 77, 2], "accounts": [{"name": "token_ledger", "writable": true}, {"name": "token_account"}], "args": []}, {"name": "shared_accounts_exact_out_route", "docs": ["Route by using program owned token accounts and open orders accounts."], "discriminator": [176, 209, 105, 168, 154, 125, 69, 62], "accounts": [{"name": "token_program"}, {"name": "program_authority"}, {"name": "user_transfer_authority", "signer": true}, {"name": "source_token_account", "writable": true}, {"name": "program_source_token_account", "writable": true}, {"name": "program_destination_token_account", "writable": true}, {"name": "destination_token_account", "writable": true}, {"name": "source_mint"}, {"name": "destination_mint"}, {"name": "platform_fee_account", "writable": true, "optional": true}, {"name": "token_2022_program", "optional": true}, {"name": "event_authority", "address": "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf"}, {"name": "program"}], "args": [{"name": "id", "type": "u8"}, {"name": "route_plan", "type": {"vec": {"defined": {"name": "RoutePlanStep"}}}}, {"name": "out_amount", "type": "u64"}, {"name": "quoted_in_amount", "type": "u64"}, {"name": "slippage_bps", "type": "u16"}, {"name": "platform_fee_bps", "type": "u8"}], "returns": "u64"}, {"name": "shared_accounts_route", "docs": ["Route by using program owned token accounts and open orders accounts."], "discriminator": [193, 32, 155, 51, 65, 214, 156, 129], "accounts": [{"name": "token_program"}, {"name": "program_authority"}, {"name": "user_transfer_authority", "signer": true}, {"name": "source_token_account", "writable": true}, {"name": "program_source_token_account", "writable": true}, {"name": "program_destination_token_account", "writable": true}, {"name": "destination_token_account", "writable": true}, {"name": "source_mint"}, {"name": "destination_mint"}, {"name": "platform_fee_account", "writable": true, "optional": true}, {"name": "token_2022_program", "optional": true}, {"name": "event_authority", "address": "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf"}, {"name": "program"}], "args": [{"name": "id", "type": "u8"}, {"name": "route_plan", "type": {"vec": {"defined": {"name": "RoutePlanStep"}}}}, {"name": "in_amount", "type": "u64"}, {"name": "quoted_out_amount", "type": "u64"}, {"name": "slippage_bps", "type": "u16"}, {"name": "platform_fee_bps", "type": "u8"}], "returns": "u64"}, {"name": "shared_accounts_route_with_token_ledger", "discriminator": [230, 121, 143, 80, 119, 159, 106, 170], "accounts": [{"name": "token_program"}, {"name": "program_authority"}, {"name": "user_transfer_authority", "signer": true}, {"name": "source_token_account", "writable": true}, {"name": "program_source_token_account", "writable": true}, {"name": "program_destination_token_account", "writable": true}, {"name": "destination_token_account", "writable": true}, {"name": "source_mint"}, {"name": "destination_mint"}, {"name": "platform_fee_account", "writable": true, "optional": true}, {"name": "token_2022_program", "optional": true}, {"name": "token_ledger"}, {"name": "event_authority", "address": "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf"}, {"name": "program"}], "args": [{"name": "id", "type": "u8"}, {"name": "route_plan", "type": {"vec": {"defined": {"name": "RoutePlanStep"}}}}, {"name": "quoted_out_amount", "type": "u64"}, {"name": "slippage_bps", "type": "u16"}, {"name": "platform_fee_bps", "type": "u8"}], "returns": "u64"}], "accounts": [{"name": "TokenLedger", "discriminator": [156, 247, 9, 188, 54, 108, 85, 77]}], "events": [{"name": "FeeEvent", "discriminator": [73, 79, 78, 127, 184, 213, 13, 220]}, {"name": "SwapEvent", "discriminator": [64, 198, 205, 232, 38, 8, 113, 226]}], "errors": [{"code": 6000, "name": "EmptyRoute", "msg": "Empty route"}, {"code": 6001, "name": "SlippageToleranceExceeded", "msg": "Slippage tolerance exceeded"}, {"code": 6002, "name": "InvalidCalculation", "msg": "Invalid calculation"}, {"code": 6003, "name": "MissingPlatformFeeAccount", "msg": "Missing platform fee account"}, {"code": 6004, "name": "InvalidSlippage", "msg": "Invalid slippage"}, {"code": 6005, "name": "NotEnoughPercent", "msg": "Not enough percent to 100"}, {"code": 6006, "name": "InvalidInputIndex", "msg": "Token input index is invalid"}, {"code": 6007, "name": "InvalidOutputIndex", "msg": "Token output index is invalid"}, {"code": 6008, "name": "NotEnoughAccountKeys", "msg": "Not Enough Account keys"}, {"code": 6009, "name": "NonZeroMinimumOutAmountNotSupported", "msg": "Non zero minimum out amount not supported"}, {"code": 6010, "name": "InvalidRoutePlan", "msg": "Invalid route plan"}, {"code": 6011, "name": "InvalidReferralAuthority", "msg": "Invalid referral authority"}, {"code": 6012, "name": "LedgerTokenAccountDoesNotMatch", "msg": "Token account doesn't match the ledger"}, {"code": 6013, "name": "InvalidTokenLedger", "msg": "Invalid token ledger"}, {"code": 6014, "name": "IncorrectTokenProgramID", "msg": "Token program ID is invalid"}, {"code": 6015, "name": "TokenProgramNotProvided", "msg": "Token program not provided"}, {"code": 6016, "name": "SwapNotSupported", "msg": "Swap not supported"}, {"code": 6017, "name": "ExactOutAmountNotMatched", "msg": "Exact out amount doesn't match"}, {"code": 6018, "name": "SourceAndDestinationMintCannotBeTheSame", "msg": "Source mint and destination mint cannot the same"}], "types": [{"name": "AccountsType", "type": {"kind": "enum", "variants": [{"name": "TransferHookA"}, {"name": "TransferHookB"}, {"name": "TransferHookReward"}, {"name": "TransferHookInput"}, {"name": "TransferHookIntermediate"}, {"name": "TransferHookOutput"}, {"name": "SupplementalTickArrays"}, {"name": "SupplementalTickArraysOne"}, {"name": "SupplementalTickArraysTwo"}]}}, {"name": "FeeEvent", "type": {"kind": "struct", "fields": [{"name": "account", "type": "pubkey"}, {"name": "mint", "type": "pubkey"}, {"name": "amount", "type": "u64"}]}}, {"name": "RemainingAccountsInfo", "type": {"kind": "struct", "fields": [{"name": "slices", "type": {"vec": {"defined": {"name": "RemainingAccountsSlice"}}}}]}}, {"name": "RemainingAccountsSlice", "type": {"kind": "struct", "fields": [{"name": "accounts_type", "type": {"defined": {"name": "AccountsType"}}}, {"name": "length", "type": "u8"}]}}, {"name": "RoutePlanStep", "type": {"kind": "struct", "fields": [{"name": "swap", "type": {"defined": {"name": "<PERSON><PERSON><PERSON>"}}}, {"name": "percent", "type": "u8"}, {"name": "input_index", "type": "u8"}, {"name": "output_index", "type": "u8"}]}}, {"name": "Side", "type": {"kind": "enum", "variants": [{"name": "Bid"}, {"name": "Ask"}]}}, {"name": "<PERSON><PERSON><PERSON>", "type": {"kind": "enum", "variants": [{"name": "<PERSON><PERSON>"}, {"name": "SaberAddDecimalsDeposit"}, {"name": "SaberAddDecimalsWithdraw"}, {"name": "TokenSwap"}, {"name": "<PERSON><PERSON>"}, {"name": "Step"}, {"name": "C<PERSON>per"}, {"name": "Raydium"}, {"name": "Crema", "fields": [{"name": "a_to_b", "type": "bool"}]}, {"name": "Lifinity"}, {"name": "Mercurial"}, {"name": "Cykura"}, {"name": "Serum", "fields": [{"name": "side", "type": {"defined": {"name": "Side"}}}]}, {"name": "MarinadeDeposit"}, {"name": "MarinadeUnstake"}, {"name": "Aldrin", "fields": [{"name": "side", "type": {"defined": {"name": "Side"}}}]}, {"name": "AldrinV2", "fields": [{"name": "side", "type": {"defined": {"name": "Side"}}}]}, {"name": "Whirlpool", "fields": [{"name": "a_to_b", "type": "bool"}]}, {"name": "Invariant", "fields": [{"name": "x_to_y", "type": "bool"}]}, {"name": "Meteora"}, {"name": "GooseFX"}, {"name": "DeltaFi", "fields": [{"name": "stable", "type": "bool"}]}, {"name": "Balansol"}, {"name": "<PERSON><PERSON><PERSON>", "fields": [{"name": "x_to_y", "type": "bool"}]}, {"name": "Dradex", "fields": [{"name": "side", "type": {"defined": {"name": "Side"}}}]}, {"name": "LifinityV2"}, {"name": "RaydiumClmm"}, {"name": "Openbook", "fields": [{"name": "side", "type": {"defined": {"name": "Side"}}}]}, {"name": "Phoenix", "fields": [{"name": "side", "type": {"defined": {"name": "Side"}}}]}, {"name": "Symmetry", "fields": [{"name": "from_token_id", "type": "u64"}, {"name": "to_token_id", "type": "u64"}]}, {"name": "TokenSwapV2"}, {"name": "HeliumTreasuryManagementRedeemV0"}, {"name": "StakeDexStakeWrappedSol"}, {"name": "StakeDexSwapViaStake", "fields": [{"name": "bridge_stake_seed", "type": "u32"}]}, {"name": "GooseFXV2"}, {"name": "Perps"}, {"name": "PerpsAddLiquidity"}, {"name": "PerpsRemoveLiquidity"}, {"name": "MeteoraDlmm"}, {"name": "OpenBookV2", "fields": [{"name": "side", "type": {"defined": {"name": "Side"}}}]}, {"name": "RaydiumClmmV2"}, {"name": "StakeDexPrefundWithdrawStakeAndDepositStake", "fields": [{"name": "bridge_stake_seed", "type": "u32"}]}, {"name": "<PERSON><PERSON>", "fields": [{"name": "pool_index", "type": "u8"}, {"name": "quantity_is_input", "type": "bool"}, {"name": "quantity_is_collateral", "type": "bool"}]}, {"name": "SanctumS", "fields": [{"name": "src_lst_value_calc_accs", "type": "u8"}, {"name": "dst_lst_value_calc_accs", "type": "u8"}, {"name": "src_lst_index", "type": "u32"}, {"name": "dst_lst_index", "type": "u32"}]}, {"name": "SanctumSAddLiquidity", "fields": [{"name": "lst_value_calc_accs", "type": "u8"}, {"name": "lst_index", "type": "u32"}]}, {"name": "SanctumSRemoveLiquidity", "fields": [{"name": "lst_value_calc_accs", "type": "u8"}, {"name": "lst_index", "type": "u32"}]}, {"name": "RaydiumCP"}, {"name": "WhirlpoolSwapV2", "fields": [{"name": "a_to_b", "type": "bool"}, {"name": "remaining_accounts_info", "type": {"option": {"defined": {"name": "RemainingAccountsInfo"}}}}]}, {"name": "OneIntro"}, {"name": "PumpdotfunWrappedBuy"}, {"name": "PumpdotfunWrappedSell"}, {"name": "PerpsV2"}, {"name": "PerpsV2AddLiquidity"}, {"name": "PerpsV2RemoveLiquidity"}, {"name": "MoonshotWrappedBuy"}, {"name": "MoonshotWrappedSell"}, {"name": "StabbleStableSwap"}, {"name": "StabbleWeightedSwap"}, {"name": "Obric", "fields": [{"name": "x_to_y", "type": "bool"}]}, {"name": "FoxBuyFromEstimatedCost"}, {"name": "FoxClaimPartial", "fields": [{"name": "is_y", "type": "bool"}]}, {"name": "SolFi", "fields": [{"name": "is_quote_to_base", "type": "bool"}]}, {"name": "SolayerDelegateNoInit"}, {"name": "SolayerUndelegateNoInit"}, {"name": "TokenMill", "fields": [{"name": "side", "type": {"defined": {"name": "Side"}}}]}, {"name": "Daos<PERSON>unBuy"}, {"name": "DaosFunSell"}, {"name": "ZeroFi"}, {"name": "StakeDexWithdrawWrappedSol"}, {"name": "VirtualsBuy"}, {"name": "VirtualsSell"}, {"name": "<PERSON><PERSON>", "fields": [{"name": "in_index", "type": "u8"}, {"name": "out_index", "type": "u8"}]}, {"name": "PumpdotfunAmmBuy"}, {"name": "PumpdotfunAmmSell"}, {"name": "Gamma"}, {"name": "MeteoraDlmmSwapV2", "fields": [{"name": "remaining_accounts_info", "type": {"defined": {"name": "RemainingAccountsInfo"}}}]}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "MeteoraDammV2"}, {"name": "MeteoraDynamicBondingCurveSwap"}, {"name": "StabbleStableSwapV2"}, {"name": "StabbleWeightedSwapV2"}, {"name": "RaydiumLaunchlabBuy", "fields": [{"name": "share_fee_rate", "type": "u64"}]}, {"name": "RaydiumLaunchlabSell", "fields": [{"name": "share_fee_rate", "type": "u64"}]}]}}, {"name": "SwapEvent", "type": {"kind": "struct", "fields": [{"name": "amm", "type": "pubkey"}, {"name": "input_mint", "type": "pubkey"}, {"name": "input_amount", "type": "u64"}, {"name": "output_mint", "type": "pubkey"}, {"name": "output_amount", "type": "u64"}]}}, {"name": "TokenLedger", "type": {"kind": "struct", "fields": [{"name": "token_account", "type": "pubkey"}, {"name": "amount", "type": "u64"}]}}]}