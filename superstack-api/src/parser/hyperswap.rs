use alloy::rpc::types::{Log, TransactionReceipt};

use crate::parser::hyperswap::HyperSwapV3::Swap;

pub fn parse_hyperswap_transaction(receipt: &TransactionReceipt) -> anyhow::Result<Vec<Log<Swap>>> {
    let logs = receipt.logs();

    let mut swaps = vec![];

    for log in logs {
        if let Ok(swap) = log.log_decode::<HyperSwapV3::Swap>() {
            swaps.push(swap);
        }
    }

    Ok(swaps)
}

alloy::sol!(
    interface HyperSwapV3 {
        #[derive(Debug)]
        event Swap(
            address indexed sender,
            address indexed recipient,
            int256 amount0,
            int256 amount1,
            uint160 sqrtPriceX96, // limit price
            uint128 liquidity,
            int24 tick
        );
    }
);
