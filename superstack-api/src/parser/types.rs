use solana_sdk::{pubkey::Pubkey, signature::Signature, transaction::TransactionError};

use super::{
    aggregator_v6::AggregatorV6Event, limit_order_v2::LimitOrderV2Event,
    token_program::TokenProgramEvent,
};

#[derive(Debug, Clone)]
pub enum ParsedEvent {
    AggregatorV6(AggregatorV6Event),
    LimitOrderV2(LimitOrderV2Event),
    TokenProgram(TokenProgramEvent),
}

impl ParsedEvent {
    pub fn is_aggregator_v6(&self) -> bool {
        matches!(self, ParsedEvent::AggregatorV6(_))
    }

    pub fn is_limit_order_v2(&self) -> bool {
        matches!(self, ParsedEvent::LimitOrderV2(_))
    }

    pub fn is_token_program(&self) -> bool {
        matches!(self, ParsedEvent::TokenProgram(_))
    }

    pub fn as_aggregator_v6(&self) -> Option<&AggregatorV6Event> {
        if let ParsedEvent::AggregatorV6(event) = self {
            Some(event)
        } else {
            None
        }
    }

    pub fn as_limit_order_v2(&self) -> Option<&LimitOrderV2Event> {
        if let ParsedEvent::LimitOrderV2(event) = self {
            Some(event)
        } else {
            None
        }
    }

    pub fn as_token_program(&self) -> Option<&TokenProgramEvent> {
        if let ParsedEvent::TokenProgram(event) = self {
            Some(event)
        } else {
            None
        }
    }
}

/// Token balance information from Solana transaction
#[derive(Debug, Clone)]
pub struct TokenBalance {
    pub account_index: usize,
    pub mint: String,
    pub owner: Option<String>,
    pub program_id: Option<String>,
    pub amount: String,
    pub decimals: u8,
    pub ui_amount: Option<f64>,
    pub ui_amount_string: String,
}

impl From<solana_transaction_status::UiTransactionTokenBalance> for TokenBalance {
    fn from(balance: solana_transaction_status::UiTransactionTokenBalance) -> Self {
        TokenBalance {
            account_index: balance.account_index as usize,
            mint: balance.mint,
            owner: balance.owner.into(),
            program_id: balance.program_id.into(),
            amount: balance.ui_token_amount.amount,
            decimals: balance.ui_token_amount.decimals,
            ui_amount: balance.ui_token_amount.ui_amount,
            ui_amount_string: balance.ui_token_amount.ui_amount_string,
        }
    }
}

#[derive(Debug, Clone)]
pub struct TransactionWithEvent {
    pub tx_sig: Signature,
    pub signers: Vec<Pubkey>,
    pub accounts: Vec<Pubkey>,
    pub fee: u64,
    pub slot: u64,
    pub block_time: Option<i64>,
    pub events: Option<Vec<ParsedEvent>>,
    pub err: Option<TransactionError>,
    pub pre_token_balances: Option<Vec<TokenBalance>>,
    pub post_token_balances: Option<Vec<TokenBalance>>,
}
