pub mod fee_event;
pub mod swap_event;

pub mod discriminator {
    pub const SWAP_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 64, 198, 205, 232, 38, 8, 113, 226];
    pub const FEE_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 73, 79, 78, 127, 184, 213, 13, 220];
}

use borsh::BorshDeserialize;

pub use fee_event::FeeEvent;
pub use swap_event::SwapEvent;

#[derive(Debug, Clone)]
pub enum AggregatorV6Event {
    Swap(SwapEvent),
    Fee(FeeEvent),
}

impl AggregatorV6Event {
    pub fn from_data(data: &[u8]) -> anyhow::Result<Self> {
        match data {
            x if x.starts_with(&discriminator::SWAP_EVENT) => {
                if let Ok(event) = SwapEvent::try_from_slice(&x[16..]) {
                    Ok(Self::Swap(event))
                } else {
                    // LimitOrderV2 and AggregatorV6 has the same event discriminator
                    Err(anyhow::anyhow!("Failed to parse SwapEvent"))
                }
            }
            x if x.starts_with(&discriminator::FEE_EVENT) => {
                if let Ok(event) = FeeEvent::try_from_slice(&x[16..]) {
                    Ok(Self::Fee(event))
                } else {
                    tracing::error!("Failed to parse FeeEvent, idl might changed");
                    Err(anyhow::anyhow!("Failed to parse FeeEvent"))
                }
            }
            _ => Err(anyhow::anyhow!("Unknown event {:?}", data)),
        }
    }

    pub fn is_swap(&self) -> bool {
        matches!(self, Self::Swap(_))
    }

    pub fn is_fee(&self) -> bool {
        matches!(self, Self::Fee(_))
    }

    pub fn as_swap(&self) -> Option<&SwapEvent> {
        if let Self::Swap(event) = self {
            Some(event)
        } else {
            None
        }
    }

    pub fn as_fee(&self) -> Option<&FeeEvent> {
        if let Self::Fee(event) = self {
            Some(event)
        } else {
            None
        }
    }
}
