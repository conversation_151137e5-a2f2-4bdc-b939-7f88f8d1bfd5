pub mod aggregator_v6;
pub mod hyperswap;
pub mod limit_order_v2;
pub mod token_program;
pub mod types;

use anyhow::Result;

use solana_client::rpc_config::RpcTransactionConfig;
use solana_sdk::{bs58, commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use solana_transaction_status::{
    option_serializer::OptionSerializer, UiInstruction, UiTransactionEncoding,
};

use self::{
    aggregator_v6::AggregatorV6Event, limit_order_v2::LimitOrderV2Event,
    token_program::TokenProgramEvent, types::*,
};

pub struct TransactionParser {}

impl TransactionParser {
    /// Parse a transaction with wallet-specific analysis for enhanced direction detection
    pub async fn parse_transaction_with_wallet(
        tx_sig: &Signature,
        wallet_address: Option<&str>,
    ) -> Result<TransactionWithEvent> {
        let rpc_client = crate::utils::get_rpc_client();
        let tx = rpc_client
            .get_transaction_with_config(
                tx_sig,
                RpcTransactionConfig {
                    max_supported_transaction_version: Some(0),
                    encoding: Some(UiTransactionEncoding::Base58),
                    commitment: Some(CommitmentConfig::confirmed()),
                },
            )
            .await?;

        match (&tx.transaction.meta, tx.transaction.transaction.decode()) {
            (Some(meta), Some(decoded)) => {
                let slot = tx.slot;
                let block_time = tx.block_time;

                let accounts = decoded.message.static_account_keys().to_vec();
                let signers = decoded.message.header().num_required_signatures as usize;

                let mut tx_with_event = TransactionWithEvent {
                    tx_sig: *tx_sig,
                    signers: decoded
                        .message
                        .static_account_keys()
                        .iter()
                        .take(signers)
                        .cloned()
                        .collect(),
                    accounts: accounts.clone(),
                    fee: meta.fee,
                    slot,
                    block_time,
                    events: None,
                    err: meta.err.clone(),
                    pre_token_balances: meta.pre_token_balances.as_ref().map(|balances| {
                        balances.iter().map(|b| TokenBalance::from(b.clone())).collect()
                    }),
                    post_token_balances: meta.post_token_balances.as_ref().map(|balances| {
                        balances.iter().map(|b| TokenBalance::from(b.clone())).collect()
                    }),
                };

                let mut events = vec![];

                // Parse SPL token transfers with wallet-specific analysis
                if accounts.contains(&crate::constant::token_program_pubkey()) {
                    let token_results = TokenProgramEvent::from_token_balances_with_wallet(
                        &tx_with_event,
                        wallet_address,
                    );

                    if !token_results.is_empty() {
                        for result in token_results {
                            events.push(ParsedEvent::TokenProgram(result.event));
                        }
                        tracing::debug!(
                            "Wallet-aware token parsing completed for {}: {} transfers found",
                            tx_sig,
                            events.len()
                        );
                    } else {
                        // Fall back to instruction-based parsing
                        events.extend(
                            Self::parse_token_instructions(&decoded, &accounts, meta).await,
                        );
                    }
                }

                // Check for SOL transfers (System Program)
                if accounts.contains(&crate::constant::system_program_pubkey()) {
                    if let Some(sol_transfer) = TokenProgramEvent::from_sol_transfer(&tx_with_event)
                    {
                        events.push(ParsedEvent::TokenProgram(sol_transfer));
                        tracing::debug!("SOL transfer detected for {}", tx_sig);
                    }
                }

                // Parse other event types (aggregator, limit orders, etc.)
                events.extend(Self::parse_other_events(meta, &decoded, &accounts).await);

                tx_with_event.events = Some(events);
                Ok(tx_with_event)
            }
            _ => {
                anyhow::bail!("Invalid transaction format");
            }
        }
    }

    /// Parse a transaction and extract events (legacy method for backward compatibility)
    pub async fn parse_transaction(tx_sig: &Signature) -> Result<TransactionWithEvent> {
        let rpc_client = crate::utils::get_rpc_client();
        let tx = rpc_client
            .get_transaction_with_config(
                tx_sig,
                RpcTransactionConfig {
                    max_supported_transaction_version: Some(0),
                    encoding: Some(UiTransactionEncoding::Base58),
                    commitment: Some(CommitmentConfig::confirmed()),
                },
            )
            .await?;

        match (&tx.transaction.meta, tx.transaction.transaction.decode()) {
            (Some(meta), Some(decoded)) => {
                let slot = tx.slot;
                let block_time = tx.block_time;

                let fee = meta.fee;

                let mut signers = vec![];
                let accounts = decoded.message.static_account_keys();

                for (idx, &account) in accounts.iter().enumerate() {
                    if decoded.message.is_signer(idx) {
                        signers.push(account);
                    }
                }

                // Extract token balances from transaction metadata
                let pre_token_balances =
                    Self::parse_token_balances_simple(&meta.pre_token_balances);
                let post_token_balances =
                    Self::parse_token_balances_simple(&meta.post_token_balances);

                if !Self::has_supported_programs(accounts) || meta.err.is_some() {
                    return Ok(TransactionWithEvent {
                        tx_sig: *tx_sig,
                        signers,
                        accounts: accounts.to_vec(),
                        fee,
                        slot,
                        block_time,
                        err: meta.err.clone(),
                        events: None,
                        pre_token_balances,
                        post_token_balances,
                    });
                }

                let mut events = vec![];

                // Enhanced token transfer parsing
                let mut tx_with_event = TransactionWithEvent {
                    tx_sig: *tx_sig,
                    signers: signers.clone(),
                    accounts: accounts.to_vec(),
                    fee,
                    slot,
                    block_time,
                    err: meta.err.clone(),
                    events: None,
                    pre_token_balances: pre_token_balances.clone(),
                    post_token_balances: post_token_balances.clone(),
                };

                // Parse SPL token transfers using enhanced token_program module
                if accounts.contains(&crate::constant::token_program_pubkey()) {
                    // Try enhanced token balance parsing first (without wallet context for now)
                    let token_events = TokenProgramEvent::from_token_balances(&tx_with_event);
                    if !token_events.is_empty() {
                        for token_event in token_events {
                            events.push(ParsedEvent::TokenProgram(token_event));
                        }
                        tracing::debug!(
                            "Token balance parsing completed for {}: {} transfers found",
                            tx_sig,
                            events.len()
                        );
                    } else {
                        // Fall back to instruction-based parsing
                        events
                            .extend(Self::parse_token_instructions(&decoded, accounts, meta).await);
                    }
                }

                // Check for SOL transfers (System Program)
                if accounts.contains(&crate::constant::system_program_pubkey()) {
                    if let Some(sol_transfer) = TokenProgramEvent::from_sol_transfer(&tx_with_event)
                    {
                        events.push(ParsedEvent::TokenProgram(sol_transfer));
                        tracing::debug!("SOL transfer detected for {}", tx_sig);
                    }
                }

                // Parse other event types (aggregator, limit orders, etc.)
                events.extend(Self::parse_other_events(meta, &decoded, accounts).await);

                tx_with_event.events = Some(events);
                Ok(tx_with_event)
            }
            _ => {
                anyhow::bail!("Invalid transaction format");
            }
        }
    }

    /// Parse token balances from Solana transaction metadata
    fn parse_token_balances_simple(
        token_balances: &OptionSerializer<
            Vec<solana_transaction_status::UiTransactionTokenBalance>,
        >,
    ) -> Option<Vec<types::TokenBalance>> {
        match token_balances {
            OptionSerializer::Some(balances) => {
                let parsed_balances: Vec<types::TokenBalance> = balances
                    .iter()
                    .map(|balance| types::TokenBalance {
                        account_index: balance.account_index as usize,
                        mint: balance.mint.clone(),
                        owner: balance.owner.clone().into(),
                        program_id: balance.program_id.clone().into(),
                        amount: balance.ui_token_amount.amount.clone(),
                        decimals: balance.ui_token_amount.decimals as u8,
                        ui_amount: balance.ui_token_amount.ui_amount,
                        ui_amount_string: balance.ui_token_amount.ui_amount_string.clone(),
                    })
                    .collect();

                if parsed_balances.is_empty() {
                    None
                } else {
                    Some(parsed_balances)
                }
            }
            OptionSerializer::None | OptionSerializer::Skip => None,
        }
    }

    fn supported_programs() -> Vec<Pubkey> {
        vec![
            self::aggregator_v6::ID,
            self::limit_order_v2::ID,
            crate::constant::token_program_pubkey(),
            crate::constant::system_program_pubkey(),
            crate::constant::associated_token_program_pubkey(),
        ]
    }

    fn has_supported_programs(accounts: &[Pubkey]) -> bool {
        let supported = Self::supported_programs();
        for program in &supported {
            if accounts.contains(program) {
                return true;
            }
        }
        false
    }

    /// Parse token instructions from transaction data (fallback method)
    async fn parse_token_instructions(
        decoded: &solana_sdk::transaction::VersionedTransaction,
        accounts: &[Pubkey],
        meta: &solana_transaction_status::UiTransactionStatusMeta,
    ) -> Vec<ParsedEvent> {
        let mut events = vec![];

        // Parse inner instructions
        if let OptionSerializer::Some(ixs) = &meta.inner_instructions {
            let ixs = ixs.iter().flat_map(|ix| ix.instructions.iter());
            for ix in ixs {
                if let UiInstruction::Compiled(ix) = ix {
                    let data = match bs58::decode(ix.data.clone()).into_vec() {
                        Ok(data) => data,
                        Err(_) => continue,
                    };

                    if let Ok(event) = Self::parse_event_data(&data, accounts) {
                        events.push(event);
                    }
                }
            }
        }

        // Parse main instructions
        for ix in decoded.message.instructions().iter() {
            if let Ok(event) = Self::parse_event_data(&ix.data, accounts) {
                events.push(event);
            }
        }

        // Direct token program instruction parsing
        if events.is_empty() && accounts.contains(&crate::constant::token_program_pubkey()) {
            for ix in decoded.message.instructions().iter() {
                let program_id_index = ix.program_id_index as usize;
                if program_id_index < accounts.len() &&
                    accounts[program_id_index] == crate::constant::token_program_pubkey()
                {
                    if let Ok(token_event) = TokenProgramEvent::from_data(&ix.data) {
                        events.push(ParsedEvent::TokenProgram(token_event));
                    }
                }
            }
        }

        events
    }

    /// Parse non-token events (aggregator, limit orders, etc.)
    async fn parse_other_events(
        meta: &solana_transaction_status::UiTransactionStatusMeta,
        decoded: &solana_sdk::transaction::VersionedTransaction,
        accounts: &[Pubkey],
    ) -> Vec<ParsedEvent> {
        let mut events = vec![];

        // Parse inner instructions for non-token events
        if let OptionSerializer::Some(ixs) = &meta.inner_instructions {
            let ixs = ixs.iter().flat_map(|ix| ix.instructions.iter());
            for ix in ixs {
                if let UiInstruction::Compiled(ix) = ix {
                    let data = match bs58::decode(ix.data.clone()).into_vec() {
                        Ok(data) => data,
                        Err(_) => continue,
                    };

                    // Only parse non-token events here
                    if let Ok(event) = Self::parse_non_token_event_data(&data, accounts) {
                        events.push(event);
                    }
                }
            }
        }

        // Parse main instructions for non-token events
        for ix in decoded.message.instructions().iter() {
            if let Ok(event) = Self::parse_non_token_event_data(&ix.data, accounts) {
                events.push(event);
            }
        }

        events
    }

    /// Parse non-token event data (aggregator, limit orders, etc.)
    fn parse_non_token_event_data(data: &[u8], program_range: &[Pubkey]) -> Result<ParsedEvent> {
        // Try aggregator v6 first
        if program_range.contains(&self::aggregator_v6::ID) {
            if let Ok(event) = AggregatorV6Event::from_data(data) {
                return Ok(ParsedEvent::AggregatorV6(event));
            }
        }

        // Try limit order v2
        if program_range.contains(&self::limit_order_v2::ID) {
            if let Ok(event) = LimitOrderV2Event::from_data(data) {
                return Ok(ParsedEvent::LimitOrderV2(event));
            }
        }

        anyhow::bail!("Unknown non-token event data");
    }

    fn parse_event_data(data: &[u8], program_range: &[Pubkey]) -> Result<ParsedEvent> {
        // Try aggregator v6 first
        if program_range.contains(&self::aggregator_v6::ID) {
            if let Ok(event) = AggregatorV6Event::from_data(data) {
                return Ok(ParsedEvent::AggregatorV6(event));
            }
        }

        // Try limit order v2
        if program_range.contains(&self::limit_order_v2::ID) {
            if let Ok(event) = LimitOrderV2Event::from_data(data) {
                return Ok(ParsedEvent::LimitOrderV2(event));
            }
        }

        // Try token program - be more permissive for basic transfers
        if program_range.contains(&crate::constant::token_program_pubkey()) {
            if let Ok(event) = TokenProgramEvent::from_data(data) {
                return Ok(ParsedEvent::TokenProgram(event));
            }
        }

        // Fallback: try to parse as token program event even if not in program_range
        // This helps catch basic token transfers that might not be properly detected
        if let Ok(event) = TokenProgramEvent::from_data(data) {
            return Ok(ParsedEvent::TokenProgram(event));
        }

        anyhow::bail!("Unknown event data");
    }
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use super::*;

    #[tokio::test]
    async fn test_tx_parser() -> Result<()> {
        crate::utils::setup_tracing();

        // swap
        let tx_sig = Signature::from_str("49sgnipE9oysfStERut4SByJkQjLPAiDWfhzFoe6zyC9s7PJMvBiMZPqbsDf7j4v5Py3kyzZPs2srjSR1sMw7QVj")
        .unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        // swap
        let tx_sig = Signature::from_str("5QpUuWyGm5TEFNn1ggXhVd2YG941g5XZmpjgfV5MnkTQBrWGKuTBzh3yYFHdxYXtDzGR59rnLr12EdUogW3pum4M")
        .unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        // swap
        let tx_sig = Signature::from_str("4u9Jg5RNRYVeEkdoMsgyPz4GbK6tJ4ptPDnvu5YyeUALmSdJZLtgSYks71TRS45esZf22jtdvs9k6mkrRxQhr3wv")
        .unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        // failed swap
        let tx_sig = Signature::from_str("5YiqGaHzBSGa5fp4WcRGEDSfhPQWRraaVTf7fsPRRhhrakcNF87uTbxuxxXHa9nDCJyXt44D6yCAc7nJYiyvC1hu")
        .unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        // swap with fee
        let tx_sig = Signature::from_str("4bwJL6EUzK2KWhCpJUHC33WBN4T3o6fLHJpnDJosLXYB74rwt9tmmvtDGsCUMoUbR7BicumMk8Ck6Q2qZtRiFomS")
        .unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        // limit trade
        let tx_sig = Signature::from_str("5pxzrmRMNF3EKbQbq7eB7iJryVwQ4mMJ2fLim5oZxPJUn2eWkPd84Fe4kzMh6kmmGBWUfYZuHTKpU81xLnp1Mpvn")
        .unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        // limit open
        let tx_sig = Signature::from_str("26G5cuX2d162KvBSYSDqWkfrroyQpfUdgGY9xgdGV9Jv1S4xqyETQ1eZjQvDS4jFHg2r8WdighxdeTu1XwYsYoHX").unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        // limit open
        let tx_sig = Signature::from_str("3sKQvZSGRNM9zSUyMgrg5iLFcCjXUPLosawAbM8poFLHvVnHzMXARyry8anzQUVj1TTetdJRKa4JH4zfqqTagYQC").unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        // limit cancel
        let tx_sig = Signature::from_str("HvS5j1v4yARutZiGwAztKpspRofGUuRLD7GGYi7TPh8M9UytK3EiMV8n7LRpXzaisvZUCmy7WXkLR5k4dM3mVEb")
        .unwrap();
        let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;
        tracing::info!("{:?}", parsed_tx);

        Ok(())
    }

    #[tokio::test]
    async fn test_sol_transfer_parsing() -> Result<()> {
        crate::utils::setup_tracing();

        // Test SOL transfer transactions from the user's example
        let sol_transfer_sigs = vec![
            "5xuw1WRDQY6k6pocfR1zHt4FqXZfy2nseqNfohCbHdnsgRsmE6vX6Zbd4HYjDZkQk78mmmgDTzQoY5cmr1qo32F5",
            "5NaMGu49JYM4Hr9W2mVAHBvoUXC3nS3aibELDr3zs6AxNTRV9v7gNak1KvYRFt63cmWoJ2pMNmUcBLKpMKDu6FJm",
        ];

        for sig_str in sol_transfer_sigs {
            let tx_sig = Signature::from_str(sig_str).unwrap();
            let parsed_tx = TransactionParser::parse_transaction(&tx_sig).await?;

            tracing::info!("SOL Transfer Transaction: {}", sig_str);
            tracing::info!("Events found: {:?}", parsed_tx.events);

            // Check if we detected SOL transfer events
            if let Some(events) = &parsed_tx.events {
                for event in events {
                    if let Some(token_event) = event.as_token_program() {
                        tracing::info!("Token event detected: {:?}", token_event);
                        if let Some(amount) = token_event.get_transfer_amount() {
                            tracing::info!(
                                "Transfer amount: {} lamports ({} SOL)",
                                amount,
                                amount as f64 / 1_000_000_000.0
                            );
                        }
                    }
                }
            }
        }

        Ok(())
    }
}
