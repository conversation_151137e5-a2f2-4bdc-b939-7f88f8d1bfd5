use anyhow::Result;
use solana_sdk::pubkey::Pubkey;

use crate::parser::types::{TokenBalance, TransactionWithEvent};

/// Transfer direction from wallet perspective
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum TransferDirection {
    Incoming, // Receiving tokens
    Outgoing, // Sending tokens
}

/// Complete token transfer result with direction analysis
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct TokenTransferResult {
    pub event: TokenProgramEvent,
    pub mint: String,
    pub decimals: u8,
    pub direction: Option<TransferDirection>,
    pub ui_amount: f64,
    pub amount: u64,
}

/// Address information extracted from transaction
#[derive(Debug, <PERSON>lone)]
pub struct TransactionAddresses {
    pub send_to_address: Option<String>,
    pub receive_from_address: Option<String>,
    pub mint_to_address: Option<String>,
}

/// Token Program transfer instruction types
#[derive(Debug, <PERSON>lone)]
pub enum TokenProgramEvent {
    Transfer { amount: u64 },
    TransferChecked { amount: u64, decimals: u8 },
}

impl TokenProgramEvent {
    /// Parse Token Program instruction data
    pub fn from_data(data: &[u8]) -> Result<Self> {
        if data.is_empty() {
            anyhow::bail!("Empty instruction data");
        }

        let instruction_type = data[0];

        match instruction_type {
            3 => {
                // Transfer instruction
                if data.len() < 9 {
                    anyhow::bail!("Invalid Transfer instruction data length: expected at least 9 bytes, got {}", data.len());
                }

                let amount = u64::from_le_bytes([
                    data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8],
                ]);

                Ok(TokenProgramEvent::Transfer { amount })
            }
            12 => {
                // TransferChecked instruction
                if data.len() < 10 {
                    anyhow::bail!("Invalid TransferChecked instruction data length: expected at least 10 bytes, got {}", data.len());
                }

                let amount = u64::from_le_bytes([
                    data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8],
                ]);
                let decimals = data[9];

                Ok(TokenProgramEvent::TransferChecked { amount, decimals })
            }

            _ => {
                anyhow::bail!(
                    "Unsupported Token Program instruction type: {} (data length: {})",
                    instruction_type,
                    data.len()
                );
            }
        }
    }

    /// Check if this event represents a transfer operation (always true now)
    pub fn is_transfer(&self) -> bool {
        true
    }

    /// Get the transfer amount
    pub fn get_transfer_amount(&self) -> Option<u64> {
        match self {
            TokenProgramEvent::Transfer { amount } => Some(*amount),
            TokenProgramEvent::TransferChecked { amount, .. } => Some(*amount),
        }
    }

    /// Get the decimals if available (only for TransferChecked)
    pub fn get_decimals(&self) -> Option<u8> {
        match self {
            TokenProgramEvent::TransferChecked { decimals, .. } => Some(*decimals),
            _ => None,
        }
    }

    /// Extract addresses from transaction for activity metadata
    pub fn extract_transaction_addresses(
        tx: &TransactionWithEvent,
        wallet_address: &str,
    ) -> TransactionAddresses {
        let mut addresses = TransactionAddresses {
            send_to_address: None,
            receive_from_address: None,
            mint_to_address: None,
        };

        // Parse wallet address
        let wallet_pubkey = match wallet_address.parse::<Pubkey>() {
            Ok(pubkey) => pubkey,
            Err(_) => {
                tracing::warn!("Failed to parse wallet address: {}", wallet_address);
                return addresses;
            }
        };

        // Extract addresses from token balances
        if let (Some(pre_balances), Some(post_balances)) =
            (&tx.pre_token_balances, &tx.post_token_balances)
        {
            Self::extract_addresses_from_balances(
                pre_balances,
                post_balances,
                &wallet_pubkey,
                &tx.accounts,
                &mut addresses,
            );
        }

        // For SOL transfers, extract from accounts directly
        if Self::is_sol_transfer_tx(tx) {
            Self::extract_sol_transfer_addresses(tx, &wallet_pubkey, &mut addresses);
        }

        addresses
    }

    /// Extract addresses from token balance changes
    fn extract_addresses_from_balances(
        pre_balances: &[TokenBalance],
        post_balances: &[TokenBalance],
        wallet_pubkey: &Pubkey,
        accounts: &[Pubkey],
        addresses: &mut TransactionAddresses,
    ) {
        // Create maps for efficient lookup
        let mut pre_map = std::collections::HashMap::new();
        let mut post_map = std::collections::HashMap::new();

        for balance in pre_balances {
            pre_map.insert((balance.account_index, &balance.mint), balance);
        }

        for balance in post_balances {
            post_map.insert((balance.account_index, &balance.mint), balance);
        }

        // Find balance changes to determine transfer direction and addresses
        for ((account_index, mint), post_balance) in &post_map {
            if let Some(pre_balance) = pre_map.get(&(*account_index, mint)) {
                let pre_amount: u64 = pre_balance.amount.parse().unwrap_or(0);
                let post_amount: u64 = post_balance.amount.parse().unwrap_or(0);

                if pre_amount != post_amount && *account_index < accounts.len() {
                    let account_pubkey = &accounts[*account_index];
                    let account_address = account_pubkey.to_string();

                    // Determine if this is the wallet's account or another account
                    if account_pubkey == wallet_pubkey {
                        // This is the wallet's account
                        if post_amount > pre_amount {
                            // Wallet received tokens - the sender is unknown from balances
                            // We could try to find the sender from other balance decreases
                            if let Some(sender) = Self::find_sender_address(
                                pre_balances,
                                post_balances,
                                mint,
                                accounts,
                            ) {
                                addresses.receive_from_address = Some(sender);
                            }
                        } else {
                            // Wallet sent tokens - find the recipient
                            if let Some(recipient) = Self::find_recipient_address(
                                pre_balances,
                                post_balances,
                                mint,
                                accounts,
                            ) {
                                addresses.send_to_address = Some(recipient);
                            }
                        }
                    } else {
                        // This is another account
                        if post_amount > pre_amount {
                            // This account received tokens
                            addresses.send_to_address = Some(account_address.clone());
                            addresses.mint_to_address = Some(account_address);
                        } else {
                            // This account sent tokens
                            addresses.receive_from_address = Some(account_address);
                        }
                    }
                }
            }
        }
    }

    /// Find the recipient address in a token transfer
    fn find_recipient_address(
        pre_balances: &[TokenBalance],
        post_balances: &[TokenBalance],
        mint: &str,
        accounts: &[Pubkey],
    ) -> Option<String> {
        let mut pre_map = std::collections::HashMap::new();
        for balance in pre_balances {
            if balance.mint == mint {
                pre_map.insert(balance.account_index, balance.amount.parse::<u64>().unwrap_or(0));
            }
        }

        for balance in post_balances {
            if balance.mint == mint {
                let post_amount = balance.amount.parse::<u64>().unwrap_or(0);
                let pre_amount = pre_map.get(&balance.account_index).copied().unwrap_or(0);

                // If this account's balance increased, it's likely the recipient
                if post_amount > pre_amount && balance.account_index < accounts.len() {
                    return Some(accounts[balance.account_index].to_string());
                }
            }
        }
        None
    }

    /// Find the sender address in a token transfer
    fn find_sender_address(
        pre_balances: &[TokenBalance],
        post_balances: &[TokenBalance],
        mint: &str,
        accounts: &[Pubkey],
    ) -> Option<String> {
        let mut post_map = std::collections::HashMap::new();
        for balance in post_balances {
            if balance.mint == mint {
                post_map.insert(balance.account_index, balance.amount.parse::<u64>().unwrap_or(0));
            }
        }

        for balance in pre_balances {
            if balance.mint == mint {
                let pre_amount = balance.amount.parse::<u64>().unwrap_or(0);
                let post_amount = post_map.get(&balance.account_index).copied().unwrap_or(0);

                // If this account's balance decreased, it's likely the sender
                if pre_amount > post_amount && balance.account_index < accounts.len() {
                    return Some(accounts[balance.account_index].to_string());
                }
            }
        }
        None
    }

    /// Extract addresses for SOL transfers
    fn extract_sol_transfer_addresses(
        tx: &TransactionWithEvent,
        wallet_pubkey: &Pubkey,
        addresses: &mut TransactionAddresses,
    ) {
        // For SOL transfers, we can analyze the signers and accounts
        // The first signer is usually the sender
        if let Some(first_signer) = tx.signers.first() {
            if first_signer == wallet_pubkey {
                // Wallet is sending SOL - find the recipient
                // Usually the recipient is one of the non-signer accounts
                for account in &tx.accounts {
                    if account != wallet_pubkey && !tx.signers.contains(account) {
                        addresses.send_to_address = Some(account.to_string());
                        break;
                    }
                }
            } else {
                // Someone else is sending SOL to the wallet
                addresses.receive_from_address = Some(first_signer.to_string());
            }
        }
    }

    /// Check if this is a SOL transfer transaction
    fn is_sol_transfer_tx(tx: &TransactionWithEvent) -> bool {
        tx.accounts.contains(&crate::constant::system_program_pubkey()) &&
            !Self::has_valid_token_balances(tx)
    }

    /// Parse token transfers from transaction token balances with wallet-specific analysis
    pub fn from_token_balances_with_wallet(
        tx: &TransactionWithEvent,
        wallet_address: Option<&str>,
    ) -> Vec<TokenTransferResult> {
        let mut results = Vec::new();

        let pre_balances = match &tx.pre_token_balances {
            Some(balances) => balances,
            None => return results,
        };

        let post_balances = match &tx.post_token_balances {
            Some(balances) => balances,
            None => return results,
        };

        // Create maps for efficient lookup
        let mut pre_map = std::collections::HashMap::new();
        let mut post_map = std::collections::HashMap::new();

        for balance in pre_balances {
            pre_map.insert((balance.account_index, &balance.mint), balance);
        }

        for balance in post_balances {
            post_map.insert((balance.account_index, &balance.mint), balance);
        }

        // Find balance changes and create events with direction analysis
        for ((account_index, mint), post_balance) in &post_map {
            let pre_amount = pre_map
                .get(&(*account_index, mint))
                .and_then(|b| b.amount.parse::<u64>().ok())
                .unwrap_or(0);

            let post_amount = post_balance.amount.parse::<u64>().ok().unwrap_or(0);

            if pre_amount != post_amount {
                let amount_change = if post_amount > pre_amount {
                    post_amount - pre_amount
                } else {
                    pre_amount - post_amount
                };

                // Create appropriate event based on available decimals
                let event = if post_balance.decimals > 0 {
                    TokenProgramEvent::TransferChecked {
                        amount: amount_change,
                        decimals: post_balance.decimals,
                    }
                } else {
                    TokenProgramEvent::Transfer { amount: amount_change }
                };

                // Determine transfer direction based on balance change and wallet
                let direction = Self::determine_transfer_direction_from_balances(
                    pre_amount,
                    post_amount,
                    wallet_address,
                    &tx.accounts,
                    *account_index,
                    post_balance.decimals,
                );

                // Calculate UI amount
                let ui_amount = if post_balance.decimals > 0 {
                    amount_change as f64 / 10_u64.pow(post_balance.decimals as u32) as f64
                } else {
                    amount_change as f64
                };

                results.push(TokenTransferResult {
                    event,
                    mint: mint.to_string(),
                    decimals: post_balance.decimals,
                    direction,
                    ui_amount,
                    amount: amount_change,
                });
            }
        }

        results
    }

    /// Legacy method for backward compatibility
    pub fn from_token_balances(tx: &TransactionWithEvent) -> Vec<Self> {
        Self::from_token_balances_with_wallet(tx, None)
            .into_iter()
            .map(|result| result.event)
            .collect()
    }

    /// Parse SOL transfer from System Program transaction
    pub fn from_sol_transfer(tx: &TransactionWithEvent) -> Option<Self> {
        // Check if this transaction involves System Program
        if !tx.accounts.contains(&crate::constant::system_program_pubkey()) {
            return None;
        }

        // Check if we have valid SPL token balances (if so, it's not a pure SOL transfer)
        if Self::has_valid_token_balances(tx) {
            return None;
        }

        // Try to extract SOL amount from events if available
        if let Some(events) = &tx.events {
            for event in events {
                if let Some(token_event) = event.as_token_program() {
                    if let Some(amount) = token_event.get_transfer_amount() {
                        // Return as SOL transfer (9 decimals)
                        return Some(TokenProgramEvent::TransferChecked {
                            amount,
                            decimals: crate::constant::SOL_DECIMALS,
                        });
                    }
                }
            }
        }

        None
    }

    /// Check if transaction has valid SPL token balances
    fn has_valid_token_balances(tx: &TransactionWithEvent) -> bool {
        if let Some(balances) = &tx.post_token_balances {
            for balance in balances {
                if !balance.mint.is_empty() && balance.decimals > 0 {
                    return true;
                }
            }
        }

        if let Some(balances) = &tx.pre_token_balances {
            for balance in balances {
                if !balance.mint.is_empty() && balance.decimals > 0 {
                    return true;
                }
            }
        }

        false
    }

    /// Extract mint and decimals from token balances
    pub fn extract_mint_and_decimals(tx: &TransactionWithEvent) -> Option<(String, u8)> {
        // Check post token balances first
        if let Some(balances) = &tx.post_token_balances {
            for balance in balances {
                if !balance.mint.is_empty() && balance.decimals > 0 {
                    return Some((balance.mint.clone(), balance.decimals));
                }
            }
        }

        // Check pre token balances as fallback
        if let Some(balances) = &tx.pre_token_balances {
            for balance in balances {
                if !balance.mint.is_empty() && balance.decimals > 0 {
                    return Some((balance.mint.clone(), balance.decimals));
                }
            }
        }

        None
    }

    /// Determine transfer direction from balance changes (replicates original logic)
    fn determine_transfer_direction_from_balances(
        pre_amount: u64,
        post_amount: u64,
        wallet_address: Option<&str>,
        accounts: &[Pubkey],
        account_index: usize,
        decimals: u8,
    ) -> Option<TransferDirection> {
        // If no wallet address provided, use amount-based heuristics
        if wallet_address.is_none() {
            return Self::determine_direction_by_amount(pre_amount, post_amount, decimals);
        }

        let wallet_pubkey = match wallet_address.and_then(|addr| addr.parse::<Pubkey>().ok()) {
            Some(pubkey) => pubkey,
            None => return Self::determine_direction_by_amount(pre_amount, post_amount, decimals),
        };

        // Check if the account at this index belongs to the wallet
        if account_index < accounts.len() && accounts[account_index] == wallet_pubkey {
            // This is the wallet's account
            if post_amount > pre_amount {
                Some(TransferDirection::Incoming)
            } else {
                Some(TransferDirection::Outgoing)
            }
        } else {
            // Not the wallet's account, use amount-based heuristics
            Self::determine_direction_by_amount(pre_amount, post_amount, decimals)
        }
    }

    /// Determine direction based on amount and decimals (original heuristic logic)
    fn determine_direction_by_amount(
        pre_amount: u64,
        post_amount: u64,
        decimals: u8,
    ) -> Option<TransferDirection> {
        let amount_change = if post_amount > pre_amount {
            post_amount - pre_amount
        } else {
            pre_amount - post_amount
        };

        // Use threshold-based logic similar to original implementation
        // For tokens with high decimals, large amounts are likely sends
        let threshold = if decimals >= 6 {
            10_u64.pow((decimals as u32).saturating_sub(3)) // e.g., 1000 for 6 decimals
        } else {
            1000 // Default threshold for low decimal tokens
        };

        if amount_change > threshold {
            // Large amounts are typically sends (outgoing)
            if post_amount > pre_amount {
                Some(TransferDirection::Incoming)
            } else {
                Some(TransferDirection::Outgoing)
            }
        } else {
            // Small amounts direction is harder to determine
            if post_amount > pre_amount {
                Some(TransferDirection::Incoming)
            } else {
                Some(TransferDirection::Outgoing)
            }
        }
    }
}

/// Token Program ID
pub fn id() -> Pubkey {
    crate::constant::token_program_pubkey()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::parser::types::{TokenBalance, TransactionWithEvent};
    use solana_sdk::{pubkey::Pubkey, signature::Signature};

    #[test]
    fn test_parse_transfer_instruction() {
        // Transfer instruction: [3, amount (8 bytes)]
        let data = vec![3, 0x40, 0x42, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00]; // 1000000 in little endian

        let event = TokenProgramEvent::from_data(&data).unwrap();

        match event {
            TokenProgramEvent::Transfer { amount } => {
                assert_eq!(amount, 1000000);
            }
            _ => panic!("Expected Transfer event"),
        }

        assert!(event.is_transfer());
        assert_eq!(event.get_transfer_amount(), Some(1000000));
    }

    #[test]
    fn test_parse_transfer_checked_instruction() {
        // TransferChecked instruction: [12, amount (8 bytes), decimals (1 byte)]
        let data = vec![12, 0x40, 0x42, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 6]; // 1000000, 6 decimals

        let event = TokenProgramEvent::from_data(&data).unwrap();

        match event {
            TokenProgramEvent::TransferChecked { amount, decimals } => {
                assert_eq!(amount, 1000000);
                assert_eq!(decimals, 6);
            }
            _ => panic!("Expected TransferChecked event"),
        }

        assert!(event.is_transfer());
        assert_eq!(event.get_transfer_amount(), Some(1000000));
        assert_eq!(event.get_decimals(), Some(6));
    }

    #[test]
    fn test_parse_unsupported_instruction() {
        let data = vec![99]; // Unsupported instruction type

        let result = TokenProgramEvent::from_data(&data);
        assert!(result.is_err());
    }

    #[test]
    fn test_transfer_direction_logic() {
        // Test the fixed transfer direction logic
        // This test verifies that large amounts from signers are considered sends

        // Create a Transfer event with a large amount
        let data = vec![3, 0x00, 0x10, 0x96, 0x49, 0x00, 0x00, 0x00, 0x00]; // ********** in little endian
        let event = TokenProgramEvent::from_data(&data).unwrap();

        // Verify the amount is parsed correctly
        assert_eq!(event.get_transfer_amount(), Some(**********));

        // For a token with 9 decimals, threshold should be 10^6 = 1,000,000
        // Amount ********** > 1,000,000, so this should be considered a send (not receive)
        let decimals = 9;
        let threshold = 10_u64.pow((decimals as u32).saturating_sub(3));
        assert_eq!(threshold, 1_000_000);
        assert!(event.get_transfer_amount().unwrap() > threshold);
    }

    #[test]
    fn test_extract_mint_and_decimals() {
        use crate::parser::types::{TokenBalance, TransactionWithEvent};
        use solana_sdk::{pubkey::Pubkey, signature::Signature};

        // Create mock token balances
        let token_balance = TokenBalance {
            account_index: 0,
            mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(), // USDC
            owner: Some("wallet123".to_string()),
            program_id: Some("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA".to_string()),
            amount: "1000000".to_string(),
            decimals: 6,
            ui_amount: Some(1.0),
            ui_amount_string: "1.0".to_string(),
        };

        let tx = TransactionWithEvent {
            tx_sig: Signature::default(),
            signers: vec![],
            accounts: vec![],
            fee: 5000,
            slot: 12345,
            block_time: Some(**********),
            events: None,
            err: None,
            pre_token_balances: None,
            post_token_balances: Some(vec![token_balance]),
        };

        let result = TokenProgramEvent::extract_mint_and_decimals(&tx);
        assert!(result.is_some());
        let (mint, decimals) = result.unwrap();
        assert_eq!(mint, "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
        assert_eq!(decimals, 6);
    }

    #[test]
    fn test_extract_transaction_addresses() {
        // Create mock wallet address
        let wallet_address = "********************************************";
        let wallet_pubkey = Pubkey::try_from(wallet_address).unwrap();

        // Create mock recipient address
        let recipient_address = "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM";
        let recipient_pubkey = Pubkey::try_from(recipient_address).unwrap();

        // Create mock transaction with token balances showing a send transaction
        let pre_balances = vec![
            TokenBalance {
                account_index: 0,
                mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(), // USDC
                owner: Some(wallet_address.to_string()),
                program_id: Some("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA".to_string()),
                amount: "1000000".to_string(), // 1 USDC
                decimals: 6,
                ui_amount: Some(1.0),
                ui_amount_string: "1.0".to_string(),
            },
            TokenBalance {
                account_index: 1,
                mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(), // USDC
                owner: Some(recipient_address.to_string()),
                program_id: Some("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA".to_string()),
                amount: "0".to_string(), // 0 USDC
                decimals: 6,
                ui_amount: Some(0.0),
                ui_amount_string: "0.0".to_string(),
            },
        ];

        let post_balances = vec![
            TokenBalance {
                account_index: 0,
                mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(), // USDC
                owner: Some(wallet_address.to_string()),
                program_id: Some("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA".to_string()),
                amount: "500000".to_string(), // 0.5 USDC (sent 0.5)
                decimals: 6,
                ui_amount: Some(0.5),
                ui_amount_string: "0.5".to_string(),
            },
            TokenBalance {
                account_index: 1,
                mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(), // USDC
                owner: Some(recipient_address.to_string()),
                program_id: Some("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA".to_string()),
                amount: "500000".to_string(), // 0.5 USDC (received 0.5)
                decimals: 6,
                ui_amount: Some(0.5),
                ui_amount_string: "0.5".to_string(),
            },
        ];

        let tx = TransactionWithEvent {
            tx_sig: Signature::default(),
            signers: vec![wallet_pubkey],
            accounts: vec![wallet_pubkey, recipient_pubkey],
            fee: 5000,
            slot: 12345,
            block_time: Some(**********),
            events: None,
            err: None,
            pre_token_balances: Some(pre_balances),
            post_token_balances: Some(post_balances),
        };

        // Test address extraction
        let addresses = TokenProgramEvent::extract_transaction_addresses(&tx, wallet_address);

        // Verify that send_to_address is extracted correctly
        assert_eq!(addresses.send_to_address, Some(recipient_address.to_string()));

        // For this test case, receive_from_address should be None since wallet is sending
        assert_eq!(addresses.receive_from_address, None);

        // mint_to_address should be the recipient in this case
        assert_eq!(addresses.mint_to_address, Some(recipient_address.to_string()));
    }
}
