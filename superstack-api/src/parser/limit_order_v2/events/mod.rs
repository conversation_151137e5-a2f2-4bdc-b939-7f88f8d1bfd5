pub mod cancel_order_event;
pub mod create_order_event;
pub mod trade_event;

pub mod discriminator {
    pub const CANCEL_ORDER_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 174, 66, 141, 17, 4, 224, 162, 77];
    pub const CREATE_ORDER_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 49, 142, 72, 166, 230, 29, 84, 84];
    pub const TRADE_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 189, 219, 127, 211, 78, 230, 97, 238];
}

use borsh::BorshDeserialize;

pub use cancel_order_event::CancelOrderEvent;
pub use create_order_event::CreateOrderEvent;
pub use trade_event::TradeEvent;

#[derive(Debug, Clone)]
pub enum LimitOrderV2Event {
    CreateOrder(CreateOrderEvent),
    CancelOrder(CancelOrderEvent),
    Trade(TradeEvent),
}

impl LimitOrderV2Event {
    pub fn from_data(data: &[u8]) -> anyhow::Result<Self> {
        match data {
            x if x.starts_with(&discriminator::CREATE_ORDER_EVENT) => {
                if let Ok(event) = CreateOrderEvent::try_from_slice(&x[16..]) {
                    Ok(Self::CreateOrder(event))
                } else {
                    let event = match CreateOrderEvent::deserialize(&mut &x[16..]) {
                        Ok(event) => event,
                        Err(e) => {
                            tracing::error!(
                                "Failed to parse CreateOrderEvent, idl might changed: {:?}",
                                e
                            );
                            return Err(anyhow::anyhow!("Failed to parse CreateOrderEvent"));
                        }
                    };
                    Ok(Self::CreateOrder(event))
                }
            }
            x if x.starts_with(&discriminator::CANCEL_ORDER_EVENT) => {
                if let Ok(event) = CancelOrderEvent::try_from_slice(&x[16..]) {
                    Ok(Self::CancelOrder(event))
                } else {
                    tracing::error!("Failed to parse CancelOrderEvent, idl might changed");
                    Err(anyhow::anyhow!("Failed to parse CancelOrderEvent"))
                }
            }
            x if x.starts_with(&discriminator::TRADE_EVENT) => {
                if let Ok(event) = TradeEvent::try_from_slice(&x[16..]) {
                    Ok(Self::Trade(event))
                } else {
                    // LimitOrderV2 and AggregatorV6 has the same event discriminator
                    Err(anyhow::anyhow!("Failed to parse TradeEvent"))
                }
            }
            _ => Err(anyhow::anyhow!("Unknown event {:?}", data)),
        }
    }

    pub fn is_trade_event(&self) -> bool {
        matches!(self, Self::Trade(_))
    }

    pub fn is_cancel_order_event(&self) -> bool {
        matches!(self, Self::CancelOrder(_))
    }

    pub fn is_create_order_event(&self) -> bool {
        matches!(self, Self::CreateOrder(_))
    }

    pub fn as_trade_event(&self) -> Option<&TradeEvent> {
        if let Self::Trade(event) = self {
            Some(event)
        } else {
            None
        }
    }

    pub fn as_cancel_order_event(&self) -> Option<&CancelOrderEvent> {
        if let Self::CancelOrder(event) = self {
            Some(event)
        } else {
            None
        }
    }

    pub fn as_create_order_event(&self) -> Option<&CreateOrderEvent> {
        if let Self::CreateOrder(event) = self {
            Some(event)
        } else {
            None
        }
    }
}
