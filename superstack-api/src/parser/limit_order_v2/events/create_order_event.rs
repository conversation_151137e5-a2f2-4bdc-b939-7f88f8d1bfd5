use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

#[derive(Debug, <PERSON><PERSON>, BorshSerialize, BorshDeserialize)]
pub struct CreateOrderEvent {
    pub order_key: Pubkey,
    pub maker: Pubkey,
    pub input_mint: Pubkey,
    pub output_mint: Pubkey,
    pub input_token_program: Pubkey,
    pub output_token_program: Pubkey,
    pub making_amount: u64,
    pub taking_amount: u64,
    pub expired_at: Option<i64>,
    pub fee_bps: u16,
    pub fee_account: Pubkey,
    // pub slippage_bps: u16,
}
