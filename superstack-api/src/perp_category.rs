use std::{
    collections::{HashMap, HashSet},
    path::Path,
    sync::OnceLock,
};

use serde::{Deserialize, Serialize};
use tracing::{error, info};

use crate::config::Config;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerpCategories {
    #[serde(flatten)]
    pub categories: HashMap<String, Vec<String>>,
}

pub struct PerpCategoryManager {
    categories: PerpCategories,
    symbol_to_categories: HashMap<String, HashSet<String>>,
}

impl PerpCategoryManager {
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self, Box<dyn std::error::Error>> {
        let content = std::fs::read_to_string(path.as_ref())?;
        let categories: PerpCategories = serde_json::from_str(&content)?;

        let mut symbol_to_categories: HashMap<String, HashSet<String>> = HashMap::new();

        // Build the reverse mapping: symbol -> set of categories
        for (category_name, symbols) in &categories.categories {
            for symbol in symbols {
                symbol_to_categories
                    .entry(symbol.clone())
                    .or_insert_with(HashSet::new)
                    .insert(category_name.clone());
            }
        }

        let total_symbols = symbol_to_categories.len();
        let total_categories = categories.categories.len();

        info!("Loaded {} perp categories with {} total symbols", total_categories, total_symbols);

        Ok(Self { categories, symbol_to_categories })
    }

    pub fn get_all_categories(&self) -> Vec<String> {
        self.categories.categories.keys().cloned().collect()
    }

    pub fn get_symbols_by_category(&self, category: &str) -> Option<&Vec<String>> {
        self.categories.categories.get(category)
    }

    pub fn get_categories_by_symbol(&self, symbol: &str) -> Option<&HashSet<String>> {
        self.symbol_to_categories.get(symbol)
    }

    pub fn is_symbol_in_category(&self, symbol: &str, category: &str) -> bool {
        self.get_categories_by_symbol(symbol)
            .map(|categories| categories.contains(category))
            .unwrap_or(false)
    }
}

static PERP_CATEGORY_MANAGER: OnceLock<PerpCategoryManager> = OnceLock::new();

pub fn init_perp_category_manager() -> Result<(), Box<dyn std::error::Error>> {
    let manager = PerpCategoryManager::load_from_file(&Config::get().perps_category_file_path)?;
    PERP_CATEGORY_MANAGER.set(manager).map_err(|_| "Failed to set global perp category manager")?;
    Ok(())
}

pub fn ensure_perp_category_manager() -> &'static PerpCategoryManager {
    PERP_CATEGORY_MANAGER.get_or_init(|| {
        match PerpCategoryManager::load_from_file(&Config::get().perps_category_file_path) {
            Ok(manager) => {
                info!("Successfully loaded perp category manager");
                manager
            }
            Err(e) => {
                error!("Failed to load perp category manager: {}", e);
                PerpCategoryManager {
                    categories: PerpCategories { categories: HashMap::new() },
                    symbol_to_categories: HashMap::new(),
                }
            }
        }
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_one_to_many_category_mapping() {
        // Create test data with symbols appearing in multiple categories
        let mut categories = HashMap::new();
        categories.insert("ai".to_string(), vec!["NEAR".to_string(), "FET".to_string()]);
        categories.insert(
            "layer1".to_string(),
            vec!["NEAR".to_string(), "INJ".to_string(), "BTC".to_string()],
        );
        categories.insert("defi".to_string(), vec!["INJ".to_string(), "UNI".to_string()]);

        let perp_categories = PerpCategories { categories };
        let manager = PerpCategoryManager {
            categories: perp_categories.clone(),
            symbol_to_categories: {
                let mut symbol_to_categories = HashMap::new();
                for (category_name, symbols) in &perp_categories.categories {
                    for symbol in symbols {
                        symbol_to_categories
                            .entry(symbol.clone())
                            .or_insert_with(HashSet::new)
                            .insert(category_name.clone());
                    }
                }
                symbol_to_categories
            },
        };

        // Test NEAR appears in both ai and layer1
        let near_categories = manager.get_categories_by_symbol("NEAR").unwrap();
        assert_eq!(near_categories.len(), 2);
        assert!(near_categories.contains("ai"));
        assert!(near_categories.contains("layer1"));

        // Test INJ appears in both layer1 and defi
        let inj_categories = manager.get_categories_by_symbol("INJ").unwrap();
        assert_eq!(inj_categories.len(), 2);
        assert!(inj_categories.contains("layer1"));
        assert!(inj_categories.contains("defi"));

        // Test BTC appears only in layer1
        let btc_categories = manager.get_categories_by_symbol("BTC").unwrap();
        assert_eq!(btc_categories.len(), 1);
        assert!(btc_categories.contains("layer1"));

        // Test symbol not in any category
        assert!(manager.get_categories_by_symbol("NONEXISTENT").is_none());

        // Test category checks work correctly
        assert!(manager.is_symbol_in_category("NEAR", "ai"));
        assert!(manager.is_symbol_in_category("NEAR", "layer1"));
        assert!(!manager.is_symbol_in_category("NEAR", "defi"));

        assert!(manager.is_symbol_in_category("INJ", "layer1"));
        assert!(manager.is_symbol_in_category("INJ", "defi"));
        assert!(!manager.is_symbol_in_category("INJ", "ai"));
    }

    #[test]
    fn test_load_from_file() {
        let manager = PerpCategoryManager::load_from_file("perps_category.json").unwrap();
        println!("{:?}", manager.get_all_categories());
    }
}
