use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;

pub const SOL_MINT: &str = "So11111111111111111111111111111111111111112";
pub const SOL_DECIMALS: u8 = 9;

pub const USDC_MINT: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
pub const USDC_DECIMALS: u8 = 6;

pub const USDT_MINT: &str = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
pub const USDT_DECIMALS: u8 = 6;

pub fn sol_mint_pubkey() -> Pubkey {
    Pubkey::from_str(SOL_MINT).unwrap()
}

// Chain constants
pub const CHAIN_SOLANA: i16 = 0;
pub const CHAIN_HYPERCORE: i16 = 1;
pub const CHAIN_HYPEREVM: i16 = 2;
// Alias for Hyperliquid (same as HyperEVM)
pub const CHAIN_HYPERLIQUID: i16 = 2;

// HyperEVM constants
pub const HYPE_NATIVE_TOKEN: &str = "0x0000000000000000000000000000000000000000"; // Native HYPE token
pub const HYPE_DECIMALS: u8 = 18;

// Solana Program IDs
pub const SYSTEM_PROGRAM_ID: &str = "11111111111111111111111111111111";
pub const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
pub const ASSOCIATED_TOKEN_PROGRAM_ID: &str = "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL";
pub const COMPUTE_BUDGET_PROGRAM_ID: &str = "ComputeBudget111111111111111111111111111111";
pub const JUPITER_V6_PROGRAM_ID: &str = "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4";
pub const LIMIT_ORDER_V2_PROGRAM_ID: &str = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo";

// Known program accounts for filtering
pub const KNOWN_PROGRAM_ACCOUNTS: &[&str] = &[
    SYSTEM_PROGRAM_ID,
    TOKEN_PROGRAM_ID,
    ASSOCIATED_TOKEN_PROGRAM_ID,
    COMPUTE_BUDGET_PROGRAM_ID,
    JUPITER_V6_PROGRAM_ID,
    LIMIT_ORDER_V2_PROGRAM_ID,
];

pub fn system_program_pubkey() -> Pubkey {
    Pubkey::from_str(SYSTEM_PROGRAM_ID).unwrap()
}

pub fn token_program_pubkey() -> Pubkey {
    Pubkey::from_str(TOKEN_PROGRAM_ID).unwrap()
}

pub fn associated_token_program_pubkey() -> Pubkey {
    Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID).unwrap()
}
