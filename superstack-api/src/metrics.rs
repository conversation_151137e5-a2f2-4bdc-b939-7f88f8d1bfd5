use serde::{Deserialize, Serialize};
use std::sync::{
    atomic::{AtomicU64, Ordering},
    Arc,
};

#[derive(Debug, <PERSON><PERSON>)]
pub struct DatabaseMetrics {
    // PostgreSQL metrics
    pub postgres_writes: Arc<AtomicU64>,
    pub postgres_reads: Arc<AtomicU64>,
    pub postgres_errors: Arc<AtomicU64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MetricsSnapshot {
    pub postgres_writes: u64,
    pub postgres_reads: u64,
    pub postgres_errors: u64,
    pub timestamp: u64,
}

impl Default for DatabaseMetrics {
    fn default() -> Self {
        Self {
            postgres_writes: Arc::new(AtomicU64::new(0)),
            postgres_reads: Arc::new(AtomicU64::new(0)),
            postgres_errors: Arc::new(AtomicU64::new(0)),
        }
    }
}

impl DatabaseMetrics {
    pub fn increment_postgres_writes(&self) {
        self.postgres_writes.fetch_add(1, Ordering::Relaxed);
    }

    pub fn increment_postgres_reads(&self) {
        self.postgres_reads.fetch_add(1, Ordering::Relaxed);
    }

    pub fn increment_postgres_errors(&self) {
        self.postgres_errors.fetch_add(1, Ordering::Relaxed);
    }

    pub fn snapshot(&self) -> MetricsSnapshot {
        MetricsSnapshot {
            postgres_writes: self.postgres_writes.load(Ordering::Relaxed),
            postgres_reads: self.postgres_reads.load(Ordering::Relaxed),
            postgres_errors: self.postgres_errors.load(Ordering::Relaxed),
            timestamp: chrono::Utc::now().timestamp() as u64,
        }
    }

    pub fn reset(&self) {
        self.postgres_writes.store(0, Ordering::Relaxed);
        self.postgres_reads.store(0, Ordering::Relaxed);
        self.postgres_errors.store(0, Ordering::Relaxed);
    }
}

// Global metrics instance
static METRICS: std::sync::OnceLock<DatabaseMetrics> = std::sync::OnceLock::new();

pub fn get_metrics() -> &'static DatabaseMetrics {
    METRICS.get_or_init(DatabaseMetrics::default)
}

// Helper macros for metrics
#[macro_export]
macro_rules! track_postgres_write {
    ($operation:expr) => {{
        let result = $operation.await;
        if result.is_ok() {
            $crate::metrics::get_metrics().increment_postgres_writes();
        } else {
            $crate::metrics::get_metrics().increment_postgres_errors();
        }
        result
    }};
}

#[macro_export]
macro_rules! track_postgres_read {
    ($operation:expr) => {{
        let result = $operation.await;
        if result.is_ok() {
            $crate::metrics::get_metrics().increment_postgres_reads();
        } else {
            $crate::metrics::get_metrics().increment_postgres_errors();
        }
        result
    }};
}
