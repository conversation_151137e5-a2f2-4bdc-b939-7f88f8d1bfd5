#[allow(unused)]
use std::{collections::HashMap, str::FromStr};

use anyhow::Result;
use chrono::Utc;
use solana_account_decoder_client_types::UiAccountData;
use solana_client::rpc_request::TokenAccountsFilter;
use solana_sdk::{native_token::lamports_to_sol, pubkey::Pubkey};

use crate::{
    models::{DbAccountValue, StorageState},
    utils::get_rpc_client,
};

#[derive(Debug, Clone)]
pub struct TokenBalance {
    pub mint: Pubkey,
    pub decimals: u8,
    pub amount: u64,
    pub ui_amount: f64,
}

#[derive(Debug, Clone)]
pub struct TokenAmount {
    pub mint: Pubkey,
    pub amount: u64,
    pub ui_amount: f64,
}

pub struct SolanaWalletManager {}

impl SolanaWalletManager {
    pub async fn get_sol_balance(wallet_address: &Pubkey) -> Result<f64> {
        let rpc_client = get_rpc_client();
        let balance = rpc_client.get_balance(wallet_address).await?;
        let ui_amount = lamports_to_sol(balance);
        Ok(ui_amount)
    }

    pub async fn get_token_balance(wallet_address: &Pubkey, mint: &Pubkey) -> Result<TokenAmount> {
        let token_balances = SolanaWalletManager::get_token_balances(wallet_address).await?;
        match token_balances.iter().find(|b| b.mint == *mint) {
            Some(token_balance) => Ok(TokenAmount {
                mint: token_balance.mint,
                amount: token_balance.amount,
                ui_amount: token_balance.ui_amount,
            }),
            None => Ok(TokenAmount { mint: *mint, amount: 0, ui_amount: 0.0 }),
        }
    }

    pub async fn get_token_balances(wallet_address: &Pubkey) -> Result<Vec<TokenBalance>> {
        let rpc_client = get_rpc_client();
        let token_accounts = rpc_client
            .get_token_accounts_by_owner(
                wallet_address,
                TokenAccountsFilter::ProgramId(spl_token::id()),
            )
            .await?;

        let mut mint_to_balance: HashMap<Pubkey, TokenBalance> = HashMap::new();
        for token_account in token_accounts {
            if let UiAccountData::Json(parsed) = &token_account.account.data {
                let parsed_data = parsed.parsed.as_object().ok_or_else(|| {
                    anyhow::anyhow!(
                        "Failed to parse parsed_data in token account data: {:?}",
                        token_account.account.data
                    )
                })?;
                let info =
                    parsed_data.get("info").and_then(|v| v.as_object()).ok_or_else(|| {
                        anyhow::anyhow!(
                            "Failed to parse info in token account data: {:?}",
                            token_account.account.data
                        )
                    })?;

                let mint = info.get("mint").and_then(|v| v.as_str()).ok_or_else(|| {
                    anyhow::anyhow!(
                        "Failed to parse mint in token account data: {:?}",
                        token_account.account.data
                    )
                })?;
                let mint = Pubkey::from_str(mint)?;

                let token_amount =
                    info.get("tokenAmount").and_then(|v| v.as_object()).ok_or_else(|| {
                        anyhow::anyhow!(
                            "Failed to parse tokenAmount in token account data: {:?}",
                            token_account.account.data
                        )
                    })?;
                let amount =
                    token_amount.get("amount").and_then(|v| v.as_str()).ok_or_else(|| {
                        anyhow::anyhow!(
                            "Failed to parse amount in token account data: {:?}",
                            token_account.account.data
                        )
                    })?;
                let amount = amount.parse::<u64>()?;
                let ui_amount =
                    token_amount.get("uiAmount").and_then(|v| v.as_f64()).ok_or_else(|| {
                        anyhow::anyhow!(
                            "Failed to parse uiAmount in token account data: {:?}",
                            token_account.account.data
                        )
                    })?;
                let decimals =
                    token_amount.get("decimals").and_then(|v| v.as_u64()).ok_or_else(|| {
                        anyhow::anyhow!(
                            "Failed to parse decimals in token account data: {:?}",
                            token_account.account.data
                        )
                    })? as u8;

                if let Some(existing_balance) = mint_to_balance.get_mut(&mint) {
                    existing_balance.amount += amount;
                    existing_balance.ui_amount += ui_amount;
                } else {
                    let token_balance = TokenBalance { mint, decimals, amount, ui_amount };
                    mint_to_balance.insert(mint, token_balance);
                }
            } else {
                tracing::error!(
                    "Failed to parse token account data: {:?}",
                    token_account.account.data
                );
            }
        }

        tracing::debug!("Token balances for {:?}: {:?}", wallet_address, mint_to_balance);

        Ok(mint_to_balance.values().cloned().collect())
    }

    pub async fn calculate_account_value(
        storage_state: &StorageState,
        wallet_address: &Pubkey,
    ) -> Result<f64> {
        let token_balances = SolanaWalletManager::get_token_balances(wallet_address).await?;
        let token_mints = token_balances.iter().map(|b| b.mint).collect::<Vec<_>>();
        let token_infos = crate::token::info::get_tokens_info(storage_state, &token_mints).await?;

        let sol_balance = SolanaWalletManager::get_sol_balance(wallet_address).await?;
        let sol_price = crate::utils::get_sol_price().await;

        let mut total_value = sol_balance * sol_price;

        for token_balance in token_balances {
            if let Some(token_info) = token_infos.get(&token_balance.mint) {
                total_value += token_balance.ui_amount * token_info.usd_price;
            }
        }

        Ok(total_value)
    }

    pub async fn get_account_value(
        storage_state: &StorageState,
        wallet_address: &Pubkey,
    ) -> Result<f64> {
        let now = Utc::now().timestamp();
        let db_account_value =
            storage_state.get_latest_account_value(&wallet_address.to_string()).await?;
        if let Some(db_account_value) = db_account_value {
            if db_account_value.timestamp > now - 5 {
                return Ok(db_account_value.value);
            }
        }

        let value = Self::update_account_value(storage_state, wallet_address, now).await?;
        Ok(value)
    }

    pub async fn update_account_value(
        storage_state: &StorageState,
        wallet_address: &Pubkey,
        timestamp: i64,
    ) -> Result<f64> {
        let value =
            SolanaWalletManager::calculate_account_value(storage_state, wallet_address).await?;
        let account_value = DbAccountValue {
            wallet_address: wallet_address.to_string(),
            value,
            timestamp,
            chain: 0,
        };
        storage_state.insert_account_value(account_value).await?;
        Ok(value)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_token_balances() {
        crate::utils::setup_tracing();

        let wallet_address = "2K9mq3S26ZTg8sxBdHdYEa2bcV2tXANgN7CtcCi9gUa2";
        let wallet_address = Pubkey::from_str(wallet_address).unwrap();
        let token_balances =
            SolanaWalletManager::get_token_balances(&wallet_address).await.unwrap();
        for token_balance in token_balances {
            tracing::info!("{:?}", token_balance);
        }
    }

    #[tokio::test]
    async fn test_get_single_token_balance() {
        crate::utils::setup_tracing();

        let wallet_address = "2K9mq3S26ZTg8sxBdHdYEa2bcV2tXANgN7CtcCi9gUa2";
        let wallet_address = Pubkey::from_str(wallet_address).unwrap();
        let token_mint = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap();
        let token_balance =
            SolanaWalletManager::get_token_balance(&wallet_address, &token_mint).await.unwrap();
        tracing::info!("token balance: {:?}", token_balance);

        let token_mint = Pubkey::from_str("26yrmkmjpLZddngnA2q3BjntTWGBojfzzTyVbbMbpump").unwrap();
        let token_balance =
            SolanaWalletManager::get_token_balance(&wallet_address, &token_mint).await.unwrap();
        tracing::info!("token balance: {:?}", token_balance);
    }

    // #[tokio::test]
    // async fn test_get_account_value() {
    //     crate::utils::setup_tracing();
    //     let storage_state = crate::utils::get_storage_state().await;

    //     let wallet_address = "2K9mq3S26ZTg8sxBdHdYEa2bcV2tXANgN7CtcCi9gUa2";
    //     let wallet_address = Pubkey::from_str(wallet_address).unwrap();
    //     let value =
    //         SolanaWalletManager::get_account_value(&storage_state,
    // &wallet_address).await.unwrap();     tracing::info!("account value: {}", value);
    // }
}
