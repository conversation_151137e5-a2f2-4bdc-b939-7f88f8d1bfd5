use std::{sync::OnceLock, time::Duration};

use anyhow::Result;
use reqwest::header::{HeaderMap, HeaderValue};
use serde::{Deserialize, Serialize};

use super::{<PERSON><PERSON><PERSON><PERSON>lestick, Pump<PERSON>oin};

#[allow(dead_code)]
const SINGLE_REQUEST_TIME_INTERVAL: Duration = Duration::from_millis(1000);

#[allow(dead_code)]
const FETCH_ALL_TIME_INTERVAL: Duration = Duration::from_secs(2);

#[allow(dead_code)]
const ERROR_SLEEP_TIME: Duration = Duration::from_secs(1);

#[allow(dead_code)]
const SORT_BY_CREATION_TIME: &str = "creationTime";

#[allow(dead_code)]
const SORT_BY_MARKET_CAP: &str = "marketCap";

#[allow(dead_code)]
const SORT_BY_VOLUME: &str = "volume";

#[allow(dead_code)]
const SORT_BY_NUM_HOLDERS: &str = "numHolders";

#[allow(dead_code)]
const LAST_SCORE_LOW: u64 = 0;

#[allow(dead_code)]
const LAST_SCORE_MEDIUM: u64 = 30;

#[allow(dead_code)]
const LAST_SCORE_HIGH: u64 = 180;

#[allow(dead_code)]
const ADVANCED_API_BASE_URL: &str = "https://advanced-api-v2.pump.fun";

const FRONTEND_API_BASE_URL: &str = "https://frontend-api-v3.pump.fun";

// Define the response structures
#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PumpApiResponse {
    pub coins: Vec<PumpCoin>,
    pub pagination: PumpPagination,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PumpPagination {
    pub last_score: Option<u64>,
    pub has_more: bool,
}

pub struct PumpCrawler {
    client: reqwest::Client,
}

impl PumpCrawler {
    pub fn new() -> Self {
        let mut headers = HeaderMap::new();
        headers.insert("accept", HeaderValue::from_static("*/*"));
        headers.insert(
            "accept-language",
            HeaderValue::from_static("en,zh-CN;q=0.9,zh;q=0.8,en-US;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"),
        );
        headers.insert("Referer", HeaderValue::from_static("https://pump.fun/"));

        let client = reqwest::Client::builder()
            .default_headers(headers)
            .user_agent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36")
            .build()
            .expect("Failed to build client");

        Self { client }
    }

    pub fn get() -> &'static PumpCrawler {
        static INSTANCE: OnceLock<PumpCrawler> = OnceLock::new();
        INSTANCE.get_or_init(Self::new)
    }

    #[allow(dead_code)]
    async fn fetch_coins(&self, url: &str, last_score: u64) -> Result<PumpApiResponse> {
        let url = format!("{}&lastScore={}", url, last_score);
        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch data from {}: HTTP {}",
                url,
                response.status()
            ));
        }

        let data: PumpApiResponse = response.json().await?;

        tracing::info!("Fetched {} coins from Pump.fun {}", data.coins.len(), url);

        Ok(data)
    }

    #[allow(dead_code)]
    async fn fetch_all_coins(
        &self,
        url: &str,
        fetch_all: bool,
        time_interval: Duration,
        min_last_score: u64,
        max_last_score: u64,
    ) -> Result<Vec<PumpCoin>> {
        let mut pump_api_response = self.fetch_coins(url, min_last_score).await?;

        let mut coins = vec![];
        coins.extend(pump_api_response.coins);
        while fetch_all &&
            pump_api_response.pagination.has_more &&
            pump_api_response.pagination.last_score.is_some() &&
            pump_api_response.pagination.last_score.unwrap() < max_last_score
        {
            pump_api_response =
                self.fetch_coins(url, pump_api_response.pagination.last_score.unwrap()).await?;
            coins.extend(pump_api_response.coins);
            tokio::time::sleep(time_interval).await;
        }

        Ok(coins)
    }

    #[allow(dead_code)]
    async fn fetch_new_created_coins(
        &self,
        fetch_all: bool,
        min_last_score: u64,
        max_last_score: u64,
    ) -> Result<Vec<PumpCoin>> {
        let url = format!("{}/coins/list?sortBy={}", ADVANCED_API_BASE_URL, SORT_BY_CREATION_TIME);
        self.fetch_all_coins(
            &url,
            fetch_all,
            SINGLE_REQUEST_TIME_INTERVAL,
            min_last_score,
            max_last_score,
        )
        .await
    }

    #[allow(dead_code)]
    async fn fetch_about_to_graduate_coins(
        &self,
        fetch_all: bool,
        min_last_score: u64,
        max_last_score: u64,
    ) -> Result<Vec<PumpCoin>> {
        let url = format!("{}/coins/list?sortBy={}", ADVANCED_API_BASE_URL, SORT_BY_MARKET_CAP);
        self.fetch_all_coins(
            &url,
            fetch_all,
            SINGLE_REQUEST_TIME_INTERVAL,
            min_last_score,
            max_last_score,
        )
        .await
    }

    #[allow(dead_code)]
    async fn fetch_graduated_coins(
        &self,
        sort_by: &str,
        fetch_all: bool,
        min_last_score: u64,
        max_last_score: u64,
    ) -> Result<Vec<PumpCoin>> {
        let url = format!("{}/coins/graduated?sortBy={}", ADVANCED_API_BASE_URL, sort_by);
        self.fetch_all_coins(
            &url,
            fetch_all,
            SINGLE_REQUEST_TIME_INTERVAL,
            min_last_score,
            max_last_score,
        )
        .await
    }

    #[allow(dead_code)]
    async fn fetch_candlesticks(
        &self,
        coin_address: &str,
        offset: Option<u64>,
        limit: Option<u64>,
        timeframe: Option<u32>,
    ) -> Result<Vec<PumpCandlestick>> {
        let offset = offset.unwrap_or(0);
        let limit = limit.unwrap_or(1000);
        let timeframe = timeframe.unwrap_or(5);

        // Fetch the data from Pump.fun API
        let url = format!(
            "{}/candlesticks/{}?offset={}&limit={}&timeframe={}",
            FRONTEND_API_BASE_URL, coin_address, offset, limit, timeframe
        );

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch data from {}: HTTP {}",
                url,
                response.status()
            ));
        }

        let data: Vec<PumpCandlestick> = response.json().await?;

        tracing::info!("Fetched {} candlesticks from Pump.fun {}", data.len(), url);

        Ok(data)
    }

    pub async fn fetch_sol_price(&self) -> Result<f64> {
        let url = format!("{}/sol-price", FRONTEND_API_BASE_URL);
        let response = self.client.get(&url).send().await?;

        let data: serde_json::Value = response.json().await?;

        let sol_price = data
            .get("solPrice")
            .and_then(|v| v.as_f64())
            .ok_or(anyhow::anyhow!("Failed to get sol price"))?;

        Ok(sol_price)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_fetch_coins() {
        crate::utils::setup_tracing();

        let crawler = PumpCrawler::get();

        crawler.fetch_new_created_coins(true, LAST_SCORE_LOW, LAST_SCORE_HIGH).await.unwrap();

        crawler.fetch_about_to_graduate_coins(true, LAST_SCORE_LOW, LAST_SCORE_HIGH).await.unwrap();

        for sort_by in
            [SORT_BY_CREATION_TIME, SORT_BY_MARKET_CAP, SORT_BY_VOLUME, SORT_BY_NUM_HOLDERS]
        {
            crawler
                .fetch_graduated_coins(sort_by, true, LAST_SCORE_LOW, LAST_SCORE_HIGH)
                .await
                .unwrap();
        }
    }

    #[tokio::test]
    async fn test_fetch_candlesticks() {
        crate::utils::setup_tracing();

        let crawler = PumpCrawler::get();

        let result = crawler
            .fetch_candlesticks("4d2QrnYfV3rWAbNfwcju7kQ1kQmsEUB6GneXy4hxpump", None, None, None)
            .await
            .unwrap();

        assert!(!result.is_empty());

        tracing::info!("candlesticks: {:?}", result[0]);
    }

    #[tokio::test]
    async fn test_fetch_sol_price() {
        crate::utils::setup_tracing();

        let crawler = PumpCrawler::get();
        let sol_price = crawler.fetch_sol_price().await.unwrap();
        tracing::info!("sol price: {}", sol_price);
    }
}
