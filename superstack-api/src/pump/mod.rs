pub mod crawler;

use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PumpCoin {
    pub coin_mint: String,
    pub dev: String,
    pub name: String,
    pub ticker: String,
    pub image_url: String,
    pub creation_time: u64,
    pub num_holders: u64,
    pub market_cap: f64,
    pub volume: f64,
    pub current_market_price: f64,
    pub bonding_curve_progress: f64,
    pub sniper_count: u64,
    pub graduation_date: Option<u64>,
    pub holders: Vec<PumpHolder>,
    pub all_time_high_market_cap: f64,
    pub pool_address: Option<String>,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PumpHolder {
    pub holder_id: String,
    pub owned_percentage: f64,
    #[serde(rename = "realizedPnL")]
    pub realized_pnl: f64,
    pub total_cost_of_tokens_held: f64,
    pub total_token_amount_held: f64,
    pub is_sniper: bool,
    #[serde(rename = "unrealizedPnL")]
    pub unrealized_pnl: f64,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct PumpCandlestick {
    pub mint: String,
    pub timestamp: u64,
    pub open: f64,
    pub high: f64,
    pub low: f64,
    pub close: f64,
    pub volume: u64,
    pub slot: u64,
    pub is_5_min: bool,
    pub is_1_min: bool,
}
