use std::collections::HashMap;

use anyhow::Result;
use serde::Deserialize;

use crate::{config::Config, constant::SOL_MINT};

#[derive(Debug, Clone, Deserialize)]
pub struct Price {
    pub id: String,
    pub price: f64,
}

#[derive(Debug, Clone, Deserialize)]
pub struct SinglePriceResponse {
    pub id: String,
    pub price: String,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct PriceResponse {
    pub data: HashMap<String, Option<SinglePriceResponse>>,
}

pub async fn get_price(ids: &[&str]) -> Result<Vec<Price>> {
    if ids.is_empty() {
        return Err(anyhow::anyhow!("No ids provided"));
    }

    let config = Config::get();
    let url = format!("{}?ids={}", config.jupiter_price_url, ids.join(","));
    let client = crate::utils::get_reqwest_client();
    let response =
        client.get(url).header(reqwest::header::ACCEPT, "application/json").send().await?;

    if !response.status().is_success() {
        return Err(anyhow::anyhow!(
            "Failed to get price with status: {}, body: {}",
            response.status(),
            response.text().await.unwrap_or("Unknown body".to_string())
        ));
    }

    let price_response: PriceResponse = response.json().await?;

    let mut prices = Vec::new();
    for (id, price) in price_response.data {
        if let Some(price) = price {
            prices.push(Price { id: id.to_string(), price: price.price.parse::<f64>()? });
        } else {
            tracing::warn!("No price found for token: {}", id);
        }
    }

    Ok(prices)
}

pub async fn get_sol_price() -> Result<f64> {
    let prices = get_price(&[SOL_MINT]).await?;
    if prices.len() != 1 {
        return Err(anyhow::anyhow!("Invalid number of prices found"));
    }
    if prices[0].id != SOL_MINT {
        return Err(anyhow::anyhow!("Invalid SOL price found"));
    }

    let price = prices[0].price;
    Ok(price)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_sol_price() {
        crate::utils::setup_tracing();

        let price = get_sol_price().await.unwrap();
        tracing::info!("SOL price: {}", price);
    }

    #[tokio::test]
    async fn test_get_price() {
        crate::utils::setup_tracing();

        let prices = get_price(&["6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN"]).await.unwrap();
        tracing::info!("Prices: {:?}", prices);

        let prices = get_price(&["38PgzpJYu2HkiYvV8qePFakB8tuobPdGm2FFEn7Dpump"]).await.unwrap();
        tracing::info!("Prices: {:?}", prices);
    }
}
