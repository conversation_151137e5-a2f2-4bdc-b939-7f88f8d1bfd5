use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::Deserialize;

use crate::{config::Config, utils::get_reqwest_client};

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub struct TriggerOrderResponse {
    pub user: String,
    pub order_status: String,
    pub orders: Vec<Order>,
    pub total_pages: u32,
    pub page: u32,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub struct Order {
    pub user_pubkey: String,
    pub order_key: String,
    pub input_mint: String,
    pub output_mint: String,
    pub making_amount: String,
    pub taking_amount: String,
    pub remaining_making_amount: String,
    pub remaining_taking_amount: String,
    pub raw_making_amount: String,
    pub raw_taking_amount: String,
    pub raw_remaining_making_amount: String,
    pub raw_remaining_taking_amount: String,
    pub slippage_bps: String,
    pub expired_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub status: String,
    pub open_tx: String,
    pub close_tx: String,
    pub program_version: String,
    pub trades: Vec<Trade>,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub struct Trade {
    pub order_key: String,
    pub keeper: String,
    pub input_mint: String,
    pub output_mint: String,
    pub input_amount: String,
    pub output_amount: String,
    pub raw_input_amount: String,
    pub raw_output_amount: String,
    pub fee_mint: String,
    pub fee_amount: String,
    pub raw_fee_amount: String,
    pub tx_id: String,
    pub confirmed_at: DateTime<Utc>,
    pub action: String,
}

pub async fn get_trigger_orders(
    user: &str,
    get_active: bool,
    input_mint: Option<String>,
    output_mint: Option<String>,
) -> Result<TriggerOrderResponse> {
    let config = Config::get();
    let trigger_base_url = &config.jupiter_trigger_url;
    let order_status = if get_active { "active" } else { "history" };
    let input_mint_param =
        input_mint.map(|mint| format!("&inputMint={}", mint)).unwrap_or_default();
    let output_mint_param =
        output_mint.map(|mint| format!("&outputMint={}", mint)).unwrap_or_default();
    let url = format!(
        "{trigger_base_url}/getTriggerOrders?user={}&orderStatus={}{}{}",
        user, order_status, input_mint_param, output_mint_param
    );

    let client = get_reqwest_client();

    let response = client.get(&url).send().await?;

    if !response.status().is_success() {
        tracing::error!("Failed to get trigger orders: {:?}", response.text().await?);
        return Err(anyhow::anyhow!("Failed to get trigger orders"));
    }

    // let orders: serde_json::Value = response.json().await?;
    // tracing::info!("Orders: {:?}", orders);
    // todo!();
    // let orders = vec![];
    let orders: TriggerOrderResponse = response.json().await?;

    Ok(orders)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_trigger_orders() {
        crate::utils::setup_tracing();

        let user = "3QL8c7AkiQbxVkwfXZcyqJ5K35moQCwtCFXJURZmnoHZ";
        let get_active = true;
        let input_mint = None;
        let output_mint = None;
        let orders = get_trigger_orders(user, get_active, input_mint, output_mint).await.unwrap();
        tracing::info!("Orders: {:?}", orders);
    }
}
