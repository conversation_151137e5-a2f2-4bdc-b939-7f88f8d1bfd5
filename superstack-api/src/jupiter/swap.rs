use anyhow::Result;
use base64::{engine::general_purpose, Engine as _};
use serde::Serialize;
use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_sdk::{
    commitment_config::CommitmentConfig,
    pubkey::Pubkey,
    signature::Signature,
    signer::{keypair::Keypair, Signer},
    transaction::VersionedTransaction,
};

use crate::config::Config;

// allow camelCase in struct fields
// Doc: https://serde.rs/field-attrs.html

#[allow(dead_code)]
#[derive(Serialize)]
struct SwapRequest<'a> {
    #[serde(rename = "quoteResponse")]
    quote_response: &'a serde_json::Value,
    #[serde(rename = "userPublicKey")]
    user_public_key: String,
    #[serde(rename = "wrapAndUnwrapSol")]
    wrap_and_unwrap_sol: bool,
    #[serde(rename = "prioritizationFeeLamports")]
    prioritization_fee_lamports: u64,
    #[serde(rename = "dynamicComputeUnitLimit")]
    dynamic_compute_unit_limit: bool,
    #[serde(rename = "dynamicSlippage")]
    dynamic_slippage: bool,
}

pub async fn get_unsigned_swap_transaction(
    input_mint: &str,
    output_mint: &str,
    amount: u64,
    signer: Pubkey,
) -> Result<String> {
    let config = Config::get();

    // Define the quote API endpoint and parameters
    let quote_url = format!("{}/quote", config.jupiter_swap_url);
    let amount_str = amount.to_string();
    let params = [
        ("inputMint", input_mint),
        ("outputMint", output_mint),
        ("amount", &amount_str),
        ("slippageBps", "50"), // 0.5% slippage
    ];

    let http_client = crate::utils::get_reqwest_client();

    // Make GET request to fetch the quote
    // Doc: https://station.jup.ag/api-v6/get-quote
    let quote_response: serde_json::Value =
        http_client.get(quote_url).query(&params).send().await?.json().await?;

    let estimated_out_amount = quote_response
        .get("outAmount")
        .and_then(|v| v.as_str())
        .and_then(|v| v.parse::<u64>().ok())
        .ok_or(anyhow::anyhow!("Failed to parse outAmount from the response"))?;

    tracing::info!(
        "Get Quote: inAmount {}, estimated outAmount {}, inputMint {}, outputMint {}",
        amount,
        estimated_out_amount,
        input_mint,
        output_mint
    );

    let swap_url = format!("{}/swap", config.jupiter_swap_url);
    let swap_request = SwapRequest {
        quote_response: &quote_response,
        user_public_key: signer.to_string(),
        wrap_and_unwrap_sol: true,
        prioritization_fee_lamports: 200000,
        dynamic_compute_unit_limit: true,
        dynamic_slippage: true,
    };

    // Make POST request to fetch the swap transaction
    // Doc: https://station.jup.ag/api-v6/post-swap
    let swap_response: serde_json::Value =
        http_client.post(swap_url).json(&swap_request).send().await?.json().await?;

    tracing::info!("Swap response: {:?}", swap_response);

    // Extract the swapTransaction field from the response
    let swap_transaction_b64 = swap_response
        .get("swapTransaction")
        .and_then(|v| v.as_str())
        .ok_or(anyhow::anyhow!("swapTransaction not found in the response"))?;

    Ok(swap_transaction_b64.to_string())
}

pub async fn swap(
    input_mint: &str,
    output_mint: &str,
    amount: u64,
    wallet: Keypair,
) -> Result<Signature> {
    let swap_transaction_b64 =
        get_unsigned_swap_transaction(input_mint, output_mint, amount, wallet.pubkey()).await?;

    // Decode the Base64-encoded transaction
    let swap_transaction_bytes = general_purpose::STANDARD
        .decode(swap_transaction_b64)
        .map_err(|_| anyhow::anyhow!("Failed to decode swapTransaction from Base64"))?;

    // Deserialize into a VersionedTransaction
    let versioned_tx: VersionedTransaction = bincode::deserialize(&swap_transaction_bytes)
        .map_err(|_| anyhow::anyhow!("Failed to deserialize swapTransaction"))?;

    let signed_versioned_tx =
        VersionedTransaction::try_new(versioned_tx.message, &[wallet]).unwrap();

    let rpc_client = crate::utils::get_rpc_client();

    // Send the transaction to the Solana network
    let txid = rpc_client
        .send_and_confirm_transaction_with_spinner_and_config(
            &signed_versioned_tx,
            CommitmentConfig::confirmed(),
            RpcSendTransactionConfig {
                skip_preflight: false,
                max_retries: Some(10),
                ..RpcSendTransactionConfig::default()
            },
        )
        .await?;

    Ok(txid)
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::native_token::sol_to_lamports;

    #[tokio::test]
    async fn test_get_unsigned_swap_transaction() {
        crate::utils::setup_tracing();

        let wallet = Keypair::new();
        let input_mint = "So11111111111111111111111111111111111111112";
        let output_mint = "********************************************";
        let amount = sol_to_lamports(0.01);
        let swap_transaction_b64 =
            get_unsigned_swap_transaction(input_mint, output_mint, amount, wallet.pubkey())
                .await
                .unwrap();
        tracing::info!("swap_transaction_b64: {}", swap_transaction_b64);
    }
}
