use anyhow::Result;
use chrono::Utc;
use rust_decimal::prelude::FromPrimitive;
use solana_client::{
    rpc_client::GetConfirmedSignaturesForAddress2Config,
    rpc_response::RpcConfirmedTransactionStatusWithSignature,
};
use solana_sdk::{
    commitment_config::CommitmentConfig, native_token::LAMPORTS_PER_SOL, pubkey::Pubkey,
    signature::Signature,
};
use spl_token::{solana_program::program_pack::Pack, state::Mint};
use std::{collections::HashMap, str::FromStr};
use superstack_data::postgres::{
    enums::{Chain, Dex, DexPaid},
    indexer::TokenMetadata,
};

use crate::{
    config::Config,
    constant::{sol_mint_pubkey, SOL_DECIMALS, SOL_MINT, USDC_MINT},
    models::*,
    parser::{types::TransactionWithEvent, TransactionParser},
    wallet::SolanaWalletManager,
};

// Constants for activity processing
const DEFAULT_SIGNATURE_LIMIT: usize = 1000;
const INCREMENTAL_SIGNATURE_LIMIT: usize = 2000;
const MIN_SPAM_AMOUNT_THRESHOLD: f64 = 1e-5;

pub struct PositionManager {}

impl PositionManager {
    // TODO: handle failed cases
    // TODO: use database transaction
    pub async fn update_positions(state: &StorageState, wallet_address: &str) -> Result<()> {
        Self::update_positions_internal(state, wallet_address, false).await
    }

    /// Force reprocess all transactions for a wallet, ignoring is_processed status
    pub async fn force_update_positions(state: &StorageState, wallet_address: &str) -> Result<()> {
        Self::update_positions_internal(state, wallet_address, true).await
    }

    /// Simple validation to check if position data needs reprocessing
    /// Returns true if position data appears inconsistent and needs force update
    pub async fn needs_position_update(state: &StorageState, wallet_address: &str) -> Result<bool> {
        // Simple check: compare transaction count vs unprocessed successful transactions
        let (pending_count, unprocessed_success_count) =
            state.get_transaction_counts(wallet_address).await.unwrap_or((0, 0));

        // If there are many unprocessed successful transactions, position likely needs update
        Ok(unprocessed_success_count > 5) // Allow some tolerance
    }

    async fn update_positions_internal(
        state: &StorageState,
        wallet_address: &str,
        force_reprocess: bool,
    ) -> Result<()> {
        // Check if this is a Solana address before proceeding
        let wallet_pubkey = match Pubkey::from_str(wallet_address) {
            Ok(pubkey) => pubkey,
            Err(_) => {
                // Not a valid Solana address (likely EVM), skip Solana-specific position updates
                tracing::debug!(
                    "Skipping Solana position updates for non-Solana address: {}",
                    wallet_address
                );
                return Ok(());
            }
        };

        let transactions = if force_reprocess {
            // Get all successful transactions, regardless of is_processed status
            state.get_all_successful_transactions(wallet_address).await?
        } else {
            // Normal behavior: only get unprocessed transactions
            state.get_unprocessed_successful_transactions(wallet_address).await?
        };

        tracing::info!(
            "Processing {} transactions for wallet {} (force_reprocess: {})",
            transactions.len(),
            wallet_address,
            force_reprocess
        );

        for db_transaction in transactions {
            if let Err(e) = Self::handle_transaction(state, db_transaction, &wallet_pubkey).await {
                tracing::error!(
                    "Error handling transaction for wallet {}: {:?}",
                    wallet_address,
                    e
                );
            }
        }

        Self::track_wallet_limit_order_trade_transaction(state, &wallet_pubkey).await?;
        Self::sync_wallet_assets(state, &wallet_pubkey).await?;

        Ok(())
    }

    pub async fn sync_wallet_assets(state: &StorageState, wallet_address: &Pubkey) -> Result<()> {
        let (pending_count, unprocessed_success_count) =
            state.get_transaction_counts(&wallet_address.to_string()).await?;
        if pending_count > 0 || unprocessed_success_count > 0 {
            tracing::info!(
                "Pending or unprocessed successful transactions found, skipping asset sync: {}, {}",
                pending_count,
                unprocessed_success_count
            );
            return Ok(());
        }

        let positions = state.get_positions(&wallet_address.to_string()).await?;
        let token_balances = SolanaWalletManager::get_token_balances(wallet_address).await?;

        for token_balance in token_balances.iter() {
            if token_balance.amount == 0 {
                continue;
            }

            let token_mint = token_balance.mint;
            let token_metadata =
                state.indexer_db.get_token_metadata(Chain::Solana, &token_mint.to_string()).await?;
            if let Some(token_metadata) = token_metadata {
                let position = positions.iter().find(|p| p.token_mint == token_mint.to_string());
                if position.is_none() {
                    let position = DbPosition {
                        wallet_address: wallet_address.to_string(),
                        token_mint: token_mint.to_string(),
                        token_decimals: token_metadata.decimals as i16,
                        bought_amount: token_balance.amount as i64,
                        sold_amount: 0,
                        native_decimals: 9,
                        cost_native_amount: 0,
                        earnings_native_amount: 0,
                        cost_usd: 0.0,
                        earnings_usd: 0.0,
                        operations: vec![],
                        open_time: Utc::now().timestamp(),
                        chain: 0, // Solana chain
                    };

                    tracing::info!("Synced position: {:?}", position);
                    state.insert_position(position).await?;
                }
            }
        }

        for mut position in positions {
            let token_mint = Pubkey::from_str(&position.token_mint)?;
            let token_balance = token_balances
                .iter()
                .find(|b| b.mint == token_mint)
                .map(|b| b.amount as i64)
                .unwrap_or(0);

            let position_remaining_amount = position.bought_amount - position.sold_amount;
            position.bought_amount = token_balance + position.sold_amount;

            if token_balance == 0 {
                Self::close_position(state, position, Utc::now().timestamp()).await?;
            } else if position_remaining_amount != token_balance {
                state.update_position(position).await?;
            }
        }

        Ok(())
    }

    async fn handle_transaction(
        state: &StorageState,
        db_transaction: DbTransaction,
        wallet_pubkey: &Pubkey,
    ) -> Result<()> {
        let signature = db_transaction.signature.parse::<Signature>()?;
        let fallback_timestamp = db_transaction.created_at;
        Self::handle_signature(state, &signature, wallet_pubkey, fallback_timestamp, false)
            .await
            .inspect_err(|e| {
            tracing::error!("Error handling signature: {:?}", e);
        })?;
        state.update_transaction_processed(db_transaction.signature.as_str()).await?;

        Ok(())
    }

    async fn handle_signature(
        state: &StorageState,
        signature: &Signature,
        wallet_pubkey: &Pubkey,
        fallback_timestamp: i64,
        track_limit_order_trade_only: bool,
    ) -> Result<()> {
        let tx = TransactionParser::parse_transaction(signature).await?;

        if tx.err.is_some() {
            tracing::debug!("Transaction status mismatch: {:?}", tx);
            return Ok(());
        }

        if !tx.accounts.contains(wallet_pubkey) {
            tracing::error!("Transaction account mismatch: {:?}, {:?}", tx, wallet_pubkey);
            return Ok(());
        }

        if let Some(events) = &tx.events {
            let has_limit_order_v2 = events.iter().any(|event| event.is_limit_order_v2());

            if track_limit_order_trade_only && has_limit_order_v2 {
                Self::handle_limit_order_v2(
                    state,
                    &tx,
                    wallet_pubkey,
                    fallback_timestamp,
                    track_limit_order_trade_only,
                )
                .await?;
                return Ok(());
            }

            let has_aggregator_v6 = events.iter().any(|event| event.is_aggregator_v6());

            if has_limit_order_v2 {
                Self::handle_limit_order_v2(
                    state,
                    &tx,
                    wallet_pubkey,
                    fallback_timestamp,
                    track_limit_order_trade_only,
                )
                .await?;
            } else if has_aggregator_v6 {
                Self::handle_aggregator_v6(state, &tx, wallet_pubkey, fallback_timestamp).await?;
            } else {
                tracing::error!("No valid events found: {:?} events: {:?}", tx, events);
                return Ok(());
            }
        } else {
            tracing::debug!("Transaction events not found: {:?}", tx);
            return Ok(());
        }

        Ok(())
    }

    async fn handle_limit_order_v2(
        state: &StorageState,
        tx: &TransactionWithEvent,
        wallet_pubkey: &Pubkey,
        fallback_timestamp: i64,
        track_limit_order_trade_only: bool,
    ) -> Result<()> {
        if tx.events.is_none() {
            tracing::error!("Transaction events not found: {:?}", tx);
            return Ok(());
        }

        let events = tx.events.as_ref().unwrap();
        let trade_events = events
            .iter()
            .filter_map(|event| event.as_limit_order_v2().and_then(|event| event.as_trade_event()))
            .collect::<Vec<_>>();

        // Only track cancel order events if not tracking limit order trade only
        let cancel_order_events = if !track_limit_order_trade_only {
            events
                .iter()
                .filter_map(|event| {
                    event.as_limit_order_v2().and_then(|event| event.as_cancel_order_event())
                })
                .collect::<Vec<_>>()
        } else {
            vec![]
        };

        let create_order_events = if !track_limit_order_trade_only {
            events
                .iter()
                .filter_map(|event| {
                    event.as_limit_order_v2().and_then(|event| event.as_create_order_event())
                })
                .collect::<Vec<_>>()
        } else {
            vec![]
        };

        for create_order_event in create_order_events {
            let (token_mint, trade_type, token_amount, base_amount) =
                if create_order_event.input_mint == sol_mint_pubkey() {
                    (
                        create_order_event.output_mint,
                        types::OrderType::LimitBuy,
                        create_order_event.taking_amount,
                        create_order_event.making_amount,
                    )
                } else if create_order_event.output_mint == sol_mint_pubkey() {
                    (
                        create_order_event.input_mint,
                        types::OrderType::LimitSell,
                        create_order_event.making_amount,
                        create_order_event.taking_amount,
                    )
                } else {
                    tracing::error!("Invalid create order event: {:?}", create_order_event);
                    return Ok(());
                };

            let token_metadata = state
                .indexer_db
                .get_token_metadata(Chain::Solana, &token_mint.to_string())
                .await?
                .ok_or(anyhow::anyhow!("Token not found: {:?}", token_mint))?;

            let db_order = DbOrder {
                order_id: create_order_event.order_key.to_string(),
                tx_sig: tx.tx_sig.to_string(),
                wallet_address: wallet_pubkey.to_string(),
                token_mint: token_mint.to_string(),
                token_decimals: token_metadata.decimals as i16,
                base_mint: SOL_MINT.to_string(),
                base_decimals: SOL_DECIMALS as i16,
                trade_type,
                token_amount: token_amount as i64,
                remaining_token_amount: token_amount as i64,
                base_amount: base_amount as i64,
                remaining_base_amount: base_amount as i64,
                timestamp: tx.block_time.unwrap_or(fallback_timestamp),
                slot: tx.slot as i64,
                fee: tx.fee as i64,
                is_cancelled: false,
                is_completed: false,
                fee_bps: create_order_event.fee_bps as i16,
                chain: 0, // Solana chain
            };

            tracing::info!("Inserted order: {:?}", db_order);
            state.insert_order(db_order).await?;
        }

        for trade_event in trade_events {
            let order = state
                .get_order(&trade_event.order_key.to_string())
                .await?
                .ok_or(anyhow::anyhow!("Order not found: {:?}", trade_event.order_key))?;

            if order.is_completed {
                tracing::info!("Order is completed, skipping: {:?}", order);
                continue;
            }

            let (
                token_mint,
                trade_type,
                token_amount,
                base_amount,
                remaining_token_amount,
                remaining_base_amount,
                token_fee_amount,
                base_fee_amount,
            ) = if order.trade_type.is_buy() {
                (
                    order.token_mint,
                    TradeType::LimitBuy,
                    trade_event.taking_amount,
                    trade_event.making_amount,
                    trade_event.remaining_taking_amount,
                    trade_event.remaining_making_amount,
                    order.fee_bps as u64 * trade_event.taking_amount / 10000,
                    0,
                )
            } else {
                (
                    order.token_mint,
                    TradeType::LimitSell,
                    trade_event.making_amount,
                    trade_event.taking_amount,
                    trade_event.remaining_making_amount,
                    trade_event.remaining_taking_amount,
                    0,
                    order.fee_bps as u64 * trade_event.taking_amount / 10000,
                )
            };

            let trade = DbTrade {
                tx_sig: tx.tx_sig.to_string(),
                wallet_address: wallet_pubkey.to_string(),
                token_mint: token_mint.to_string(),
                token_decimals: order.token_decimals,
                base_mint: SOL_MINT.to_string(),
                base_decimals: SOL_DECIMALS as i16,
                trade_type,
                token_amount: token_amount as i64,
                base_amount: base_amount as i64,
                token_fee_amount: token_fee_amount as i64,
                base_fee_amount: base_fee_amount as i64,
                timestamp: tx.block_time.unwrap_or(fallback_timestamp),
                slot: tx.slot as i64,
                fee: tx.fee as i64,
                order_id: Some(order.order_id.clone()),
                remaining_token_amount: Some(remaining_token_amount as i64),
                remaining_base_amount: Some(remaining_base_amount as i64),
                chain: order.chain, // Use the order's actual chain
            };

            Self::handle_position_change(state, trade).await?;

            tracing::info!(
                "Updated order remaining amounts: {:?}, {:?}, {:?}",
                order.order_id,
                remaining_token_amount as i64,
                remaining_base_amount as i64
            );
            state
                .update_order_remaining_amounts(
                    &order.order_id,
                    remaining_token_amount as i64,
                    remaining_base_amount as i64,
                )
                .await?;
        }

        for cancel_order_event in cancel_order_events {
            state.cancel_order(&cancel_order_event.order_key.to_string()).await?;
        }

        Ok(())
    }

    async fn handle_aggregator_v6(
        state: &StorageState,
        tx: &TransactionWithEvent,
        wallet_pubkey: &Pubkey,
        fallback_timestamp: i64,
    ) -> Result<()> {
        if tx.events.is_none() {
            tracing::error!("Transaction events not found: {:?}", tx);
            return Ok(());
        }

        let events = tx.events.as_ref().unwrap();

        let swap_events = events
            .iter()
            .filter_map(|event| event.as_aggregator_v6().and_then(|event| event.as_swap()))
            .collect::<Vec<_>>();

        let fee_events = events
            .iter()
            .filter_map(|event| event.as_aggregator_v6().and_then(|event| event.as_fee()))
            .collect::<Vec<_>>();

        if swap_events.is_empty() {
            tracing::error!("No swap events found: {:?}", tx);
            return Ok(());
        }

        let mut in_mint_to_in_amount = HashMap::new();
        let mut out_mint_to_out_amount = HashMap::new();
        for swap_event in &swap_events {
            in_mint_to_in_amount.insert(swap_event.input_mint, swap_event.input_amount);
            out_mint_to_out_amount.insert(swap_event.output_mint, swap_event.output_amount);
        }

        let in_mint =
            in_mint_to_in_amount.keys().find(|mint| !out_mint_to_out_amount.contains_key(mint));
        let out_mint =
            out_mint_to_out_amount.keys().find(|mint| !in_mint_to_in_amount.contains_key(mint));

        let (in_mint, in_amount) = if let Some(in_mint) = in_mint {
            (
                in_mint,
                in_mint_to_in_amount
                    .get(in_mint)
                    .expect("SAFE: in_mint should be in in_mint_to_in_amount"),
            )
        } else {
            tracing::error!("Invalid swap events: {:?}, {:?}", swap_events, tx.tx_sig);
            return Ok(());
        };
        let (out_mint, out_amount) = if let Some(out_mint) = out_mint {
            (
                out_mint,
                out_mint_to_out_amount
                    .get(out_mint)
                    .expect("SAFE: out_mint should be in out_mint_to_out_amount"),
            )
        } else {
            tracing::error!("Invalid swap events: {:?}, {:?}", swap_events, tx.tx_sig);
            return Ok(());
        };

        let mut in_fee_amount = 0;
        let mut out_fee_amount = 0;
        for fee_event in &fee_events {
            if fee_event.mint == *in_mint {
                in_fee_amount += fee_event.amount;
            } else if fee_event.mint == *out_mint {
                out_fee_amount += fee_event.amount;
            }
        }

        let (token_mint, trade_type, token_amount, base_amount, token_fee_amount, base_fee_amount) =
            if *in_mint == sol_mint_pubkey() {
                (
                    *out_mint,
                    TradeType::MarketBuy,
                    *out_amount,
                    *in_amount,
                    out_fee_amount,
                    in_fee_amount,
                )
            } else if *out_mint == sol_mint_pubkey() {
                (
                    *in_mint,
                    TradeType::MarketSell,
                    *in_amount,
                    *out_amount,
                    in_fee_amount,
                    out_fee_amount,
                )
            } else {
                tracing::error!(
                    "Invalid swap event, Only support SOL as input or output: {:?}",
                    swap_events
                );
                return Ok(());
            };

        let token_mint_address = token_mint.to_string();

        let token_metadata =
            match state.indexer_db.get_token_metadata(Chain::Solana, &token_mint_address).await? {
                Some(token_metadata) => token_metadata,
                None => {
                    if token_mint_address == USDC_MINT {
                        tracing::info!("Found USDC transaction when updating position, ignore it");
                        return Ok(());
                    } else {
                        tracing::warn!(
                            "Token metadata not found for {}, creating fallback metadata",
                            token_mint_address
                        );

                        let fallback_metadata =
                            match Self::create_fallback_token_metadata(&token_mint_address).await {
                                Ok(metadata) => metadata,
                                Err(e) => {
                                    tracing::error!(
                                        "Failed to create fallback metadata for token {}: {:?}",
                                        token_mint_address,
                                        e
                                    );
                                    Self::create_minimal_fallback_metadata(&token_mint_address)
                                }
                            };

                        fallback_metadata
                    }
                }
            };

        let trade = DbTrade {
            tx_sig: tx.tx_sig.to_string(),
            wallet_address: wallet_pubkey.to_string(),
            token_mint: token_mint_address.clone(),
            token_decimals: token_metadata.decimals as i16,
            base_mint: SOL_MINT.to_string(),
            base_decimals: SOL_DECIMALS as i16,
            trade_type,
            token_amount: token_amount as i64,
            base_amount: base_amount as i64,
            token_fee_amount: token_fee_amount as i64,
            base_fee_amount: base_fee_amount as i64,
            timestamp: tx.block_time.unwrap_or(fallback_timestamp),
            slot: tx.slot as i64,
            fee: tx.fee as i64,
            order_id: None,
            remaining_token_amount: None,
            remaining_base_amount: None,
            chain: 0, // Solana chain
        };

        Self::handle_position_change(state, trade.clone()).await?;

        // Auto-trigger referral rewards for trading (non-blocking)
        if let Err(e) = handle_referral_rewards(state, &trade).await {
            tracing::error!("Failed to process referral rewards for trade {}: {}", trade.tx_sig, e);
            // Don't fail the entire trade processing if referral rewards fail
        }

        Ok(())
    }

    async fn handle_position_change(state: &StorageState, trade: DbTrade) -> Result<()> {
        let cur_position =
            state.get_position(&trade.wallet_address, &trade.token_mint, trade.chain).await?;
        tracing::info!("Current position: {:?}", cur_position);

        let position_change = DbPositionChange {
            is_buy: trade.trade_type.is_buy(),
            token_amount: trade.token_amount,
            base_amount: trade.base_amount,
            base_mint: trade.base_mint.clone(),
            base_decimals: trade.base_decimals,
            tx_sig: trade.tx_sig.clone(),
            timestamp: trade.timestamp,
            chain: trade.chain, // Use the trade's actual chain
        };

        // let cur_token_balance = SolanaWalletManager::get_token_balance(
        //     &trade.wallet_address.parse::<Pubkey>()?,
        //     &trade.token_mint.parse::<Pubkey>()?,
        // )
        // .await?
        // .amount as i64;

        if trade.trade_type.is_buy() {
            // TODO: update db using Database transaction
            tracing::info!("Inserted buy trade: {:?}", trade);
            state.insert_trade(&trade).await?;

            if let Some(mut position) = cur_position {
                if position.operations.iter().any(|op| op.tx_sig == trade.tx_sig) {
                    tracing::info!("Trade ({}) already exists, ignoring", trade.tx_sig);
                    return Ok(());
                }

                position.bought_amount += trade.token_amount - trade.token_fee_amount;
                // TODO: handle other base cases
                position.cost_native_amount += trade.base_amount + trade.base_fee_amount;
                position.operations.push(position_change);

                // if position.bought_amount - position.sold_amount != cur_token_balance {
                //     tracing::error!(
                //         "Token balance mismatch! {:?}, {}",
                //         position,
                //         cur_token_balance
                //     );
                //     // Ensure the remaining token amount is matched
                //     // Which means: position.bought_amount - position.sold_amount =
                //     // cur_token_balance
                //     position.bought_amount = cur_token_balance + position.sold_amount;
                // }

                tracing::info!("Updated position: {:?}", position);
                state.update_position(position).await?;
            } else {
                let position = DbPosition {
                    wallet_address: trade.wallet_address.clone(),
                    token_mint: trade.token_mint.clone(),
                    token_decimals: trade.token_decimals,
                    bought_amount: trade.token_amount - trade.token_fee_amount,
                    sold_amount: 0,
                    native_decimals: 9,
                    cost_native_amount: trade.base_amount + trade.base_fee_amount,
                    earnings_native_amount: 0,
                    cost_usd: 0.0,
                    earnings_usd: 0.0,
                    operations: vec![position_change],
                    open_time: trade.timestamp,
                    chain: trade.chain, // Use the trade's actual chain
                };

                // if position.bought_amount != cur_token_balance {
                //     tracing::error!(
                //         "Token balance mismatch! {:?}, {}",
                //         position,
                //         cur_token_balance
                //     );
                //     position.bought_amount = cur_token_balance;
                // }

                tracing::info!("Inserted position: {:?}", position);
                state.insert_position(position).await?;
            }
        } else {
            tracing::info!("Inserted sell trade: {:?}", trade);
            state.insert_trade(&trade).await?;

            if let Some(mut position) = cur_position {
                if position.operations.iter().any(|op| op.tx_sig == trade.tx_sig) {
                    tracing::info!("Trade ({}) already exists, ignoring", trade.tx_sig);
                    return Ok(());
                }

                position.sold_amount += trade.token_amount;
                // TODO: handle other base cases
                position.earnings_native_amount += trade.base_amount - trade.base_fee_amount;
                position.operations.push(position_change);

                // if position.bought_amount == position.sold_amount || cur_token_balance == 0 {
                if position.bought_amount == position.sold_amount {
                    Self::close_position(state, position, trade.timestamp).await?;
                } else {
                    tracing::info!("Updated position: {:?}", position);
                    state.update_position(position).await?;
                }
            } else {
                tracing::error!(
                    "Not found position when updating position for sell trade: {:?}",
                    trade
                );
                return Ok(());
            }
        }

        Ok(())
    }

    async fn close_position(
        state: &StorageState,
        position: DbPosition,
        close_time: i64,
    ) -> Result<()> {
        // Only support Solana Chain for now
        let sol_price = crate::utils::get_sol_price().await;
        let total_cost_usd = position.cost_native_amount as f64 / LAMPORTS_PER_SOL as f64 *
            sol_price +
            position.cost_usd;
        let total_earnings_usd = position.earnings_native_amount as f64 / LAMPORTS_PER_SOL as f64 *
            sol_price +
            position.earnings_usd;
        let pnl_usd = total_earnings_usd - total_cost_usd;
        let pnl_percentage = if total_cost_usd > 0.0 {
            pnl_usd / total_cost_usd
        } else {
            // Handle the case where the cost is 0, which means the tokens are coming
            // from transfer instead of buying FIXME: Maybe we
            // should use infinity to represent this case
            0.0
        };

        let history = DbHistory {
            id: None,
            wallet_address: position.wallet_address.clone(),
            token_mint: position.token_mint.clone(),
            token_decimals: position.token_decimals,
            bought_amount: position.bought_amount,
            sold_amount: position.bought_amount, // use bought amount as sold amount
            native_decimals: 9,
            cost_native_amount: position.cost_native_amount,
            earnings_native_amount: position.earnings_native_amount,
            cost_usd: position.cost_usd,
            earnings_usd: position.earnings_usd,
            pnl_usd,
            pnl_percentage,
            operations: position.operations,
            open_time: position.open_time,
            close_time,
            chain: position.chain, // Use the position's actual chain
        };
        tracing::info!("Inserted history: {:?}", history);
        state.close_position(history).await?;

        let realized_pnl = DbRealizedPnl {
            wallet_address: position.wallet_address.clone(),
            timestamp: close_time,
            pnl_usd,
        };
        SolanaWalletManager::update_account_value(
            state,
            &position.wallet_address.parse::<Pubkey>()?,
            close_time,
        )
        .await?;
        state.insert_or_update_realized_pnl(realized_pnl).await?;

        Ok(())
    }

    async fn track_wallet_limit_order_trade_transaction(
        state: &StorageState,
        wallet_pubkey: &Pubkey,
    ) -> Result<()> {
        let active_orders = state.get_active_orders_for_wallet(&wallet_pubkey.to_string()).await?;

        if active_orders.is_empty() {
            return Ok(());
        }

        let first_order_signature = active_orders.first().unwrap().tx_sig.parse::<Signature>()?;
        let signatures =
            Self::get_signatures_until(wallet_pubkey, None, Some(first_order_signature), None)
                .await?;

        if signatures.is_empty() {
            return Ok(());
        }

        let first_signature = signatures.first().unwrap();

        let wallet = state.get_wallet(&wallet_pubkey.to_string()).await?;

        let candidate_signatures = if let Some(wallet) = wallet {
            match (wallet.latest_tx_signature, wallet.latest_tx_slot) {
                (Some(latest_tx_signature), Some(latest_tx_slot)) => {
                    if latest_tx_slot <= first_signature.slot as i64 {
                        signatures
                    } else {
                        Self::get_signatures_until(
                            wallet_pubkey,
                            None,
                            Some(latest_tx_signature.parse::<Signature>()?),
                            None,
                        )
                        .await?
                    }
                }
                _ => signatures,
            }
        } else {
            signatures
        };

        if candidate_signatures.is_empty() {
            return Ok(());
        }

        let track_limit_order_trade_only = true;
        for signature in candidate_signatures.iter() {
            let fallback_timestamp = signature.block_time.unwrap_or(Utc::now().timestamp());
            match Self::handle_signature(
                state,
                &signature.signature.parse::<Signature>()?,
                wallet_pubkey,
                fallback_timestamp,
                track_limit_order_trade_only,
            )
            .await
            {
                Ok(_) => {
                    state
                        .insert_or_update_wallet(DbWallet {
                            wallet_address: wallet_pubkey.to_string(),
                            latest_tx_signature: Some(signature.signature.to_string()),
                            latest_tx_slot: Some(signature.slot as i64),
                            updated_at: None, // Will be set by the database
                        })
                        .await?;
                }
                Err(e) => {
                    tracing::error!(
                        "Error handling signature when tracking wallet transaction: {:?}",
                        e
                    );

                    anyhow::bail!(
                        "Error handling signature when tracking wallet transaction: {:?}",
                        e
                    );
                }
            }
        }

        Ok(())
    }

    async fn get_signatures_until(
        wallet_pubkey: &Pubkey,
        before: Option<Signature>,
        until: Option<Signature>,
        limit: Option<usize>,
    ) -> Result<Vec<RpcConfirmedTransactionStatusWithSignature>> {
        let config = GetConfirmedSignaturesForAddress2Config {
            before,
            until,
            limit,
            commitment: Some(CommitmentConfig::confirmed()),
        };

        let rpc_client = crate::utils::get_rpc_client();
        let mut signatures =
            rpc_client.get_signatures_for_address_with_config(wallet_pubkey, config).await?;
        signatures.reverse();
        Ok(signatures)
    }

    /// Get signatures newer than the given timestamp
    async fn get_signatures_since_timestamp(
        wallet_pubkey: &Pubkey,
        since_timestamp: i64,
        max_limit: Option<usize>,
    ) -> Result<Vec<RpcConfirmedTransactionStatusWithSignature>> {
        let mut all_signatures = Vec::new();
        let mut before_signature: Option<Signature> = None;
        let batch_size = 1000; // Maximum allowed by Solana RPC
        let max_total = max_limit.unwrap_or(5000); // Reasonable default limit

        loop {
            let config = GetConfirmedSignaturesForAddress2Config {
                before: before_signature,
                until: None,
                limit: Some(batch_size),
                commitment: Some(CommitmentConfig::confirmed()),
            };

            let rpc_client = crate::utils::get_rpc_client();
            let batch_signatures =
                rpc_client.get_signatures_for_address_with_config(wallet_pubkey, config).await?;

            if batch_signatures.is_empty() {
                break; // No more signatures
            }

            let mut found_old_signature = false;
            let batch_len = batch_signatures.len();
            for sig_info in &batch_signatures {
                // Check if this signature is older than our timestamp
                if let Some(block_time) = sig_info.block_time {
                    if block_time <= since_timestamp {
                        found_old_signature = true;
                        break; // Stop processing this batch
                    }
                }

                all_signatures.push(sig_info.clone());

                // Update before_signature for next batch
                if let Ok(signature) = sig_info.signature.parse::<Signature>() {
                    before_signature = Some(signature);
                }

                // Check if we've reached our limit
                if all_signatures.len() >= max_total {
                    found_old_signature = true;
                    break;
                }
            }

            // If we found an old signature or reached limit, stop fetching
            if found_old_signature || all_signatures.len() >= max_total {
                break;
            }

            // If we got less than batch_size, we've reached the end
            if batch_len < batch_size {
                break;
            }
        }

        // Reverse to get chronological order (oldest first)
        all_signatures.reverse();
        Ok(all_signatures)
    }

    /// Update wallet activity by fetching new signatures and parsing them
    pub async fn update_wallet_activity(state: &StorageState, wallet_address: &str) -> Result<()> {
        let wallet_pubkey = Pubkey::from_str(wallet_address)?;

        // Get the latest activity timestamp from database
        let latest_timestamp = state
            .get_latest_wallet_activity_timestamp(wallet_address, 0) // Solana chain
            .await?;

        // Get wallet info to determine the starting point for signature fetching
        let wallet = state.get_wallet(wallet_address).await?;
        let until_signature = if let Some(wallet) = wallet {
            wallet.latest_tx_signature.and_then(|sig| sig.parse::<Signature>().ok())
        } else {
            None
        };

        // Use timestamp-based fetching if we have a latest timestamp
        let signatures = if let Some(latest_ts) = latest_timestamp {
            // Fetch only signatures newer than our latest timestamp
            tracing::debug!(
                "Fetching signatures since timestamp {} for wallet {}",
                latest_ts,
                wallet_address
            );
            Self::get_signatures_since_timestamp(&wallet_pubkey, latest_ts, Some(2000)).await?
        } else {
            // First time fetching, get recent signatures
            tracing::debug!("First time fetching signatures for wallet {}", wallet_address);
            Self::get_signatures_until(&wallet_pubkey, None, until_signature, Some(1000)).await?
        };

        // Quick check: if no new signatures, return early
        if signatures.is_empty() {
            tracing::debug!("No new signatures found for wallet {}", wallet_address);
            return Ok(());
        }

        tracing::info!(
            "Processing {} new signatures for wallet {}",
            signatures.len(),
            wallet_address
        );

        let mut activities = Vec::new();
        let current_timestamp = chrono::Utc::now().timestamp();

        for signature_info in &signatures {
            let signature = signature_info.signature.parse::<Signature>()?;

            // Parse the transaction
            match TransactionParser::parse_transaction(&signature).await {
                Ok(tx) => {
                    if tx.err.is_some() {
                        continue; // Skip failed transactions
                    }

                    if !tx.accounts.contains(&wallet_pubkey) {
                        continue; // Skip transactions not involving this wallet
                    }

                    // Convert parsed transaction to activity records
                    let parsed_activities = Self::convert_transaction_to_activities(
                        &tx,
                        wallet_address,
                        signature_info.block_time.unwrap_or(current_timestamp),
                        signature_info.slot as i64,
                    )
                    .await?;

                    activities.extend(parsed_activities);
                }
                Err(e) => {
                    tracing::warn!("Failed to parse transaction {}: {}", signature, e);
                    continue;
                }
            }
        }

        // Filter out spam/phishing transactions
        let filtered_activities = Self::filter_spam_activities(activities);

        // Batch insert activities
        if !filtered_activities.is_empty() {
            state.batch_insert_wallet_activities(filtered_activities).await?;
        }

        // Update wallet's latest signature
        if let Some(latest_sig) = signatures.last() {
            let wallet_update = DbWallet {
                wallet_address: wallet_address.to_string(),
                latest_tx_signature: Some(latest_sig.signature.clone()),
                latest_tx_slot: Some(latest_sig.slot as i64),
                updated_at: Some(current_timestamp),
            };
            state.insert_or_update_wallet(wallet_update).await?;
        }

        Ok(())
    }

    /// Convert parsed transaction to activity records
    async fn convert_transaction_to_activities(
        tx: &TransactionWithEvent,
        wallet_address: &str,
        block_time: i64,
        slot: i64,
    ) -> Result<Vec<DbWalletActivity>> {
        let mut activities = Vec::new();
        let current_timestamp = chrono::Utc::now().timestamp();

        // Extract transaction addresses for metadata
        let transaction_addresses =
            crate::parser::token_program::TokenProgramEvent::extract_transaction_addresses(
                tx,
                wallet_address,
            );

        if let Some(events) = &tx.events {
            for event in events {
                match event {
                    crate::parser::types::ParsedEvent::AggregatorV6(agg_event) => {
                        match agg_event {
                            crate::parser::aggregator_v6::AggregatorV6Event::Swap(swap_event) => {
                                // Handle aggregator swap events
                                let mut activity = Self::create_base_activity(
                                    wallet_address,
                                    tx,
                                    block_time,
                                    current_timestamp,
                                );
                                activity.activity_type = ActivityType::SpotTrade;
                                activity.token_mint = Some(swap_event.input_mint.to_string());
                                activity.token_amount = Some(swap_event.input_amount as i64);
                                activity.base_mint = Some(swap_event.output_mint.to_string());
                                activity.base_amount = Some(swap_event.output_amount as i64);
                                // Create unified metadata for swap
                                let trading_pair = Self::create_trading_pair(
                                    &swap_event.input_mint.to_string(),
                                    &swap_event.output_mint.to_string(),
                                );
                                let price = Self::calculate_price(
                                    swap_event.input_amount,
                                    swap_event.output_amount,
                                );

                                activity.metadata = Some(Self::create_unified_metadata(
                                    Some(&transaction_addresses),
                                    Some(trading_pair),
                                    Some("Market".to_string()),
                                    price,
                                ));
                                activities.push(activity);
                            }
                            crate::parser::aggregator_v6::AggregatorV6Event::Fee(_fee_event) => {
                                // Handle fee events - we can skip these or create minimal records
                                // For now, we'll skip fee-only events
                            }
                        }
                    }
                    crate::parser::types::ParsedEvent::LimitOrderV2(limit_event) => {
                        match limit_event {
                            crate::parser::limit_order_v2::LimitOrderV2Event::Trade(
                                trade_event,
                            ) => {
                                // Handle limit order trade events
                                let (token_mint, base_mint) =
                                    Self::extract_mints_from_transaction(tx);

                                let mut activity = Self::create_base_activity(
                                    wallet_address,
                                    tx,
                                    block_time,
                                    current_timestamp,
                                );
                                activity.activity_type = ActivityType::SpotTrade;
                                activity.token_mint = token_mint;
                                activity.token_amount = Some(trade_event.making_amount as i64);
                                activity.base_mint = base_mint;
                                activity.base_amount = Some(trade_event.taking_amount as i64);
                                // Create unified metadata for limit order trade
                                let trading_pair = match (&activity.token_mint, &activity.base_mint)
                                {
                                    (Some(token), Some(base)) => {
                                        Some(Self::create_trading_pair(token, base))
                                    }
                                    _ => None,
                                };
                                let price = Self::calculate_price(
                                    trade_event.taking_amount,
                                    trade_event.making_amount,
                                );

                                activity.metadata = Some(Self::create_unified_metadata(
                                    Some(&transaction_addresses),
                                    trading_pair,
                                    Some("Limit".to_string()),
                                    price,
                                ));
                                activities.push(activity);
                            }
                            crate::parser::limit_order_v2::LimitOrderV2Event::CreateOrder(
                                create_event,
                            ) => {
                                // Handle order creation
                                let activity = DbWalletActivity {
                                    wallet_address: wallet_address.to_string(),
                                    tx_signature: tx.tx_sig.to_string(),
                                    activity_type: ActivityType::SpotTrade,
                                    token_mint: Some(create_event.input_mint.to_string()),
                                    token_decimals: None,
                                    token_amount: Some(create_event.making_amount as i64),
                                    base_mint: Some(create_event.output_mint.to_string()),
                                    base_decimals: None,
                                    base_amount: Some(create_event.taking_amount as i64),
                                    usd_value: None,
                                    timestamp: block_time,
                                    block_time: Some(block_time),
                                    slot: Some(slot),
                                    chain: 0, // Solana
                                    metadata: Some({
                                        let trading_pair = Self::create_trading_pair(
                                            &create_event.input_mint.to_string(),
                                            &create_event.output_mint.to_string(),
                                        );
                                        let price = Self::calculate_price(
                                            create_event.taking_amount,
                                            create_event.making_amount,
                                        );

                                        Self::create_unified_metadata(
                                            Some(&transaction_addresses),
                                            Some(trading_pair),
                                            Some("Limit".to_string()),
                                            price,
                                        )
                                    }),
                                    created_at: current_timestamp,
                                };
                                activities.push(activity);
                            }
                            crate::parser::limit_order_v2::LimitOrderV2Event::CancelOrder(
                                cancel_event,
                            ) => {
                                // Handle order cancellation
                                let activity = DbWalletActivity {
                                    wallet_address: wallet_address.to_string(),
                                    tx_signature: tx.tx_sig.to_string(),
                                    activity_type: ActivityType::SpotTrade,
                                    token_mint: None,
                                    token_decimals: None,
                                    token_amount: None,
                                    base_mint: None,
                                    base_decimals: None,
                                    base_amount: None,
                                    usd_value: None,
                                    timestamp: block_time,
                                    block_time: Some(block_time),
                                    slot: Some(slot),
                                    chain: 0, // Solana
                                    metadata: Some(serde_json::json!({
                                        "fee": tx.fee,
                                        "event_type": "limit_order_v2_cancel",
                                        "order_key": cancel_event.order_key.to_string()
                                    })),
                                    created_at: current_timestamp,
                                };
                                activities.push(activity);
                            }
                        }
                    }
                    crate::parser::types::ParsedEvent::TokenProgram(token_event) => {
                        // Handle Token Program events with simplified parsing
                        if token_event.is_transfer() {
                            let activity = Self::create_token_activity(
                                tx,
                                wallet_address,
                                token_event,
                                block_time,
                                current_timestamp,
                            )
                            .await;
                            activities.push(activity);
                        }
                    }
                }
            }
        } else {
            // Fallback: try to parse basic token transfers from transaction instructions
            let fallback_activities =
                Self::parse_fallback_activities(tx, wallet_address, block_time, current_timestamp)
                    .await;

            if !fallback_activities.is_empty() {
                activities.extend(fallback_activities);
            } else {
                // Only create "other" activity if it has some value
                if Self::should_store_other_activity(tx) {
                    let mut activity = Self::create_base_activity(
                        wallet_address,
                        tx,
                        block_time,
                        current_timestamp,
                    );
                    activity.metadata = Some(serde_json::json!({
                        "fee": tx.fee,
                        "event_type": "other",
                        "send_to_address": transaction_addresses.send_to_address,
                        "receive_from_address": transaction_addresses.receive_from_address,
                        "mint_to_address": transaction_addresses.mint_to_address
                    }));
                    activities.push(activity);
                }
                // If no value, simply don't store the activity
            }
        }

        Ok(activities)
    }

    /// Parse fallback activities from transaction instructions when no events are found
    async fn parse_fallback_activities(
        tx: &TransactionWithEvent,
        wallet_address: &str,
        block_time: i64,
        current_timestamp: i64,
    ) -> Vec<DbWalletActivity> {
        let mut activities = Vec::new();

        // Extract transaction addresses for metadata
        let transaction_addresses =
            crate::parser::token_program::TokenProgramEvent::extract_transaction_addresses(
                tx,
                wallet_address,
            );

        // For now, we'll create a simple fallback activity that indicates a transfer occurred
        // but we couldn't parse the specific details. This is better than marking everything as
        // "other"

        // Check if the transaction involves token program
        if tx.accounts.contains(&crate::constant::token_program_pubkey()) {
            // Try to determine if this is likely a token transfer
            let wallet_pubkey = match wallet_address.parse::<solana_sdk::pubkey::Pubkey>() {
                Ok(pubkey) => pubkey,
                Err(_) => return activities,
            };

            // Check if the wallet is involved in the transaction
            if tx.accounts.contains(&wallet_pubkey) || tx.signers.contains(&wallet_pubkey) {
                // Create a transfer activity with unknown direction
                let mut activity =
                    Self::create_base_activity(wallet_address, tx, block_time, current_timestamp);

                // Default to receive for now - this could be improved with more sophisticated
                // analysis
                activity.activity_type = ActivityType::Transfer;
                activity.metadata = Some(serde_json::json!({
                    "fee": tx.fee,
                    "event_type": "token_transfer_fallback",
                    "direction": "unknown",
                    "note": "Transfer detected but direction could not be determined",
                    "send_to_address": transaction_addresses.send_to_address,
                    "receive_from_address": transaction_addresses.receive_from_address,
                    "mint_to_address": transaction_addresses.mint_to_address
                }));

                activities.push(activity);
            }
        }

        activities
    }

    /// Extract mint information from transaction accounts
    /// This is a heuristic approach to identify token mints from the transaction
    fn extract_mints_from_transaction(
        tx: &TransactionWithEvent,
    ) -> (Option<String>, Option<String>) {
        use crate::constant::SOL_MINT;

        // Look for known token mints in the transaction accounts
        let mut token_mints = Vec::new();

        for account in &tx.accounts {
            let account_str = account.to_string();

            // Skip common program accounts and system accounts
            if Self::is_program_account(&account_str) {
                continue;
            }

            // Check if this looks like a token mint (not SOL)
            if account_str != SOL_MINT && Self::is_likely_token_mint(&account_str) {
                token_mints.push(account_str);
            }
        }

        // Heuristic: first non-SOL mint is token, SOL is base
        // This is not perfect but covers most common cases
        match token_mints.len() {
            0 => (None, Some(SOL_MINT.to_string())), // No token found, assume SOL base
            1 => (Some(token_mints[0].clone()), Some(SOL_MINT.to_string())), // One token, SOL base
            _ => {
                // Multiple tokens, try to pick the most likely pair
                // For now, just take the first two
                (Some(token_mints[0].clone()), Some(token_mints[1].clone()))
            }
        }
    }

    /// Check if an "other" type activity should be stored
    /// Only store if it has some potential value or significance
    fn should_store_other_activity(tx: &TransactionWithEvent) -> bool {
        // Store if transaction has high fee (might be significant)
        const HIGH_FEE_THRESHOLD: u64 = 50_000; // 0.05 SOL in lamports
        if tx.fee > HIGH_FEE_THRESHOLD {
            return true;
        }

        // Store if transaction involves many accounts (might be complex operation)
        const MANY_ACCOUNTS_THRESHOLD: usize = 10;
        if tx.accounts.len() > MANY_ACCOUNTS_THRESHOLD {
            return true;
        }

        // Store if transaction has multiple signers (might be multi-sig or complex)
        if tx.signers.len() > 1 {
            return true;
        }

        // Store if transaction failed (might be worth tracking for debugging)
        if tx.err.is_some() {
            return true;
        }

        // Otherwise, don't store low-value "other" activities
        false
    }

    /// Check if an account is a known program account
    fn is_program_account(account: &str) -> bool {
        use crate::constant::KNOWN_PROGRAM_ACCOUNTS;
        KNOWN_PROGRAM_ACCOUNTS.contains(&account)
    }

    /// Heuristic to determine if an account is likely a token mint
    fn is_likely_token_mint(account: &str) -> bool {
        // This is a simple heuristic - in practice, you might want to:
        // 1. Check if the account is a valid mint by querying the RPC
        // 2. Maintain a whitelist of known token mints
        // 3. Use more sophisticated pattern matching

        // For now, just check that it's a valid base58 string of the right length
        account.len() == 44 && account.chars().all(|c| c.is_ascii_alphanumeric())
    }

    /// Get mint address from a token account by querying RPC
    async fn get_mint_from_token_account(
        token_account: &solana_sdk::pubkey::Pubkey,
    ) -> Option<String> {
        use spl_token::state::Account as TokenAccount;

        let rpc_client = crate::utils::get_rpc_client();
        match rpc_client.get_account(token_account).await {
            Ok(account) => {
                // Check if this is a valid SPL Token account
                let spl_token_program_id = spl_token::id();

                if account.owner == spl_token_program_id {
                    // Try to unpack as SPL Token account
                    match TokenAccount::unpack(&account.data) {
                        Ok(token_account_data) => {
                            let mint = token_account_data.mint.to_string();
                            tracing::debug!(
                                "Extracted mint {} from token account {}",
                                mint,
                                token_account
                            );
                            Some(mint)
                        }
                        Err(e) => {
                            tracing::debug!(
                                "Failed to unpack token account {}: {}",
                                token_account,
                                e
                            );
                            None
                        }
                    }
                } else {
                    tracing::debug!(
                        "Account {} is not owned by SPL Token program (owner: {})",
                        token_account,
                        account.owner
                    );
                    None
                }
            }
            Err(e) => {
                tracing::debug!("Failed to get token account {}: {}", token_account, e);
                None
            }
        }
    }

    /// Fallback method to extract mint from Transfer instruction
    async fn extract_mint_from_transfer_fallback(
        tx: &crate::parser::types::TransactionWithEvent,
    ) -> Option<String> {
        // For Transfer instructions where we can't query the source account,
        // we can try several fallback strategies:

        // Strategy 1: Look for other token accounts in the transaction
        if tx.accounts.len() > 1 {
            let dest_token_account = tx.accounts[1];
            tracing::debug!(
                "Trying to extract mint from destination token account: {}",
                dest_token_account
            );

            if let Some(mint) = Self::get_mint_from_token_account(&dest_token_account).await {
                return Some(mint);
            }
        }

        // Strategy 2: Check if there are any other accounts that might be mints
        for (i, account) in tx.accounts.iter().enumerate() {
            if i < 2 {
                continue; // Skip source and dest token accounts
            }

            let account_str = account.to_string();

            // Skip known program accounts
            if Self::is_program_account(&account_str) {
                continue;
            }

            // Check if this looks like a valid mint by trying to query it as a mint
            if let Some(mint) = Self::try_account_as_mint(account).await {
                tracing::debug!("Found mint {} from account {} at index {}", mint, account, i);
                return Some(mint);
            }
        }

        tracing::debug!("All fallback strategies failed for Transfer instruction");
        None
    }

    /// Try to treat an account as a mint and verify it
    async fn try_account_as_mint(account: &solana_sdk::pubkey::Pubkey) -> Option<String> {
        use spl_token::state::Mint;

        let rpc_client = crate::utils::get_rpc_client();
        match rpc_client.get_account(account).await {
            Ok(account_data) => {
                let spl_token_program_id = spl_token::id();

                if account_data.owner == spl_token_program_id {
                    // Try to unpack as SPL Token mint
                    match Mint::unpack(&account_data.data) {
                        Ok(_mint_data) => {
                            let mint_str = account.to_string();
                            tracing::debug!("Successfully verified {} as a mint", mint_str);
                            Some(mint_str)
                        }
                        Err(_) => None,
                    }
                } else {
                    None
                }
            }
            Err(_) => None,
        }
    }

    /// Check if an account string looks like a valid mint address
    fn is_valid_mint_format(account: &str) -> bool {
        // Check that it's a valid base58 string of the right length (44 characters)
        // and contains only valid base58 characters
        if account.len() != 44 {
            return false;
        }

        // Check for valid base58 characters (excluding confusing ones)
        if !account.chars().all(|c| c.is_ascii_alphanumeric() && !"0OIl".contains(c)) {
            return false;
        }

        // Additional checks to exclude known non-mint addresses
        // System Program ID
        if account == "********************************" {
            return false;
        }

        // Token Program ID
        if account == "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" {
            return false;
        }

        // Associated Token Program ID
        if account == "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL" {
            return false;
        }

        true
    }

    /// Get token decimals from database
    async fn get_token_decimals_from_db(token_mint: &str) -> Option<i16> {
        use superstack_data::postgres::{enums::Chain, PostgresDatabase};

        let db = PostgresDatabase::get_indexer_db().await;
        match db.get_token_metadata(Chain::Solana, token_mint).await {
            Ok(Some(metadata)) => Some(metadata.decimals as i16),
            Ok(None) => {
                tracing::debug!("Token metadata not found for mint: {}", token_mint);
                None
            }
            Err(e) => {
                tracing::warn!("Failed to get token metadata for {}: {}", token_mint, e);
                None
            }
        }
    }

    /// Get token decimals from Solana RPC by querying mint account
    async fn get_token_decimals_from_rpc(token_mint: &str) -> Option<i16> {
        // Pre-validate the mint address format
        if !Self::is_valid_mint_format(token_mint) {
            tracing::debug!("Invalid mint format, skipping RPC query: {}", token_mint);
            return None;
        }

        let mint_pubkey = match Pubkey::from_str(token_mint) {
            Ok(pubkey) => pubkey,
            Err(e) => {
                tracing::warn!("Invalid mint pubkey {}: {}", token_mint, e);
                return None;
            }
        };

        let rpc_client = crate::utils::get_rpc_client();
        match rpc_client.get_account(&mint_pubkey).await {
            Ok(account) => {
                // Check if this is a valid SPL Token mint account
                let spl_token_program_id = spl_token::id();

                if account.owner == spl_token_program_id {
                    // Standard SPL Token
                    match Mint::unpack(&account.data) {
                        Ok(mint) => {
                            tracing::debug!(
                                "Got decimals {} for SPL token {} from RPC",
                                mint.decimals,
                                token_mint
                            );
                            Some(mint.decimals as i16)
                        }
                        Err(e) => {
                            tracing::debug!(
                                "Failed to unpack SPL mint account for {} (likely not a valid mint): {}",
                                token_mint,
                                e
                            );
                            None
                        }
                    }
                } else {
                    tracing::debug!(
                        "Account {} is not an SPL Token mint (owner: {}), skipping",
                        token_mint,
                        account.owner
                    );
                    None
                }
            }
            Err(e) => {
                tracing::debug!("Account not found or inaccessible for {}: {}", token_mint, e);
                None
            }
        }
    }

    /// Create token activity with enhanced direction detection using wallet-aware parsing
    async fn create_token_activity(
        tx: &crate::parser::types::TransactionWithEvent,
        wallet_address: &str,
        token_event: &crate::parser::token_program::TokenProgramEvent,
        block_time: i64,
        current_timestamp: i64,
    ) -> DbWalletActivity {
        let mut activity =
            Self::create_base_activity(wallet_address, tx, block_time, current_timestamp);

        // Extract transaction addresses for metadata
        let transaction_addresses =
            crate::parser::token_program::TokenProgramEvent::extract_transaction_addresses(
                tx,
                wallet_address,
            );

        // Use enhanced parsing with wallet context for better direction detection
        let token_results =
            crate::parser::token_program::TokenProgramEvent::from_token_balances_with_wallet(
                tx,
                Some(wallet_address),
            );

        // If we have enhanced results, use them for better accuracy
        if let Some(result) = token_results.first() {
            // Use enhanced parsing results with direction analysis
            activity.activity_type = ActivityType::Transfer;

            activity.token_mint = Some(result.mint.clone());
            activity.token_decimals = Some(result.decimals as i16);
            activity.token_amount = Some(result.amount as i64);

            let is_sol_transfer = Self::is_sol_transfer(tx);

            activity.metadata = Some(Self::create_unified_metadata(
                Some(&transaction_addresses),
                None, // no trading pair for transfers
                None, // no order type for transfers
                None, // no price for transfers
            ));

            return activity;
        }

        // Fallback to original logic if enhanced parsing didn't work
        let token_mint_info = if Self::is_sol_transfer(tx) {
            // For SOL transfers, use SOL mint and decimals
            Some((SOL_MINT.to_string(), SOL_DECIMALS))
        } else {
            crate::parser::token_program::TokenProgramEvent::extract_mint_and_decimals(tx)
        };

        // Determine transfer direction using original logic
        let is_receive = Self::determine_transfer_direction(
            wallet_address,
            tx,
            token_event,
            token_mint_info.as_ref().map(|(mint, _)| mint.as_str()),
        )
        .await;

        activity.activity_type =
            if is_receive { ActivityType::Deposit } else { ActivityType::Withdraw };

        // Set token information
        if let Some((mint, decimals)) = token_mint_info {
            activity.token_mint = Some(mint);
            activity.token_decimals = Some(decimals as i16);
        }

        activity.token_amount = token_event.get_transfer_amount().map(|a| a as i64);

        // Calculate UI amount for better display
        let ui_amount = if let (Some(amount), Some(decimals)) =
            (token_event.get_transfer_amount(), activity.token_decimals.map(|d| d as u8))
        {
            Some(amount as f64 / 10_u64.pow(decimals as u32) as f64)
        } else {
            None
        };

        let is_sol_transfer = Self::is_sol_transfer(tx);
        activity.metadata = Some(serde_json::json!({
            "fee": tx.fee,
            "event_type": if is_sol_transfer { "sol_transfer" } else { "token_transfer" },
            "direction": if is_receive { "in" } else { "out" },
            "transfer_type": match token_event {
                crate::parser::token_program::TokenProgramEvent::Transfer { .. } => "transfer",
                crate::parser::token_program::TokenProgramEvent::TransferChecked { .. } => "transfer_checked",
            },
            "ui_amount": ui_amount,
            "enhanced_parsing": false,
            "send_to_address": transaction_addresses.send_to_address,
            "receive_from_address": transaction_addresses.receive_from_address,
            "mint_to_address": transaction_addresses.mint_to_address
        }));

        activity
    }

    /// Check if this transaction is a SOL transfer
    fn is_sol_transfer(tx: &crate::parser::types::TransactionWithEvent) -> bool {
        // SOL transfers use System Program
        let has_system_program = tx.accounts.contains(&crate::constant::system_program_pubkey());

        // Check if we have valid SPL token balances
        let has_valid_token_balances =
            crate::parser::token_program::TokenProgramEvent::extract_mint_and_decimals(tx)
                .is_some();

        // If it has system program but no valid SPL token balances, it's likely a SOL transfer
        has_system_program && !has_valid_token_balances
    }

    /// Create a base wallet activity with common fields
    fn create_base_activity(
        wallet_address: &str,
        tx: &crate::parser::types::TransactionWithEvent,
        block_time: i64,
        current_timestamp: i64,
    ) -> DbWalletActivity {
        DbWalletActivity {
            wallet_address: wallet_address.to_string(),
            tx_signature: tx.tx_sig.to_string(),
            activity_type: ActivityType::Other, // Will be overridden
            token_mint: None,
            token_decimals: None,
            token_amount: None,
            base_mint: None,
            base_decimals: None,
            base_amount: None,
            usd_value: None,
            timestamp: block_time,
            block_time: Some(block_time),
            slot: Some(tx.slot as i64),
            chain: 0, // Solana
            metadata: Some(serde_json::json!({})), /* Will be populated by specific activity
                       * creators */
            created_at: current_timestamp,
        }
    }

    /// Create unified metadata for display consistency across chains
    fn create_unified_metadata(
        addresses: Option<&crate::parser::token_program::TransactionAddresses>,
        trading_pair: Option<String>,
        order_type: Option<String>,
        price: Option<String>,
    ) -> serde_json::Value {
        let mut metadata = serde_json::json!({});

        // Add address information for transfers/deposits/withdrawals
        if let Some(addr) = addresses {
            if let Some(from) = &addr.receive_from_address {
                metadata["from_address"] = serde_json::Value::String(from.clone());
            }
            if let Some(to) = &addr.send_to_address {
                metadata["to_address"] = serde_json::Value::String(to.clone());
            }
        }

        // Add trading information for swaps/trades
        if let Some(pair) = trading_pair {
            metadata["trading_pair"] = serde_json::Value::String(pair);
        }
        if let Some(ot) = order_type {
            metadata["order_type"] = serde_json::Value::String(ot);
        }
        if let Some(p) = price {
            metadata["price"] = serde_json::Value::String(p);
        }

        metadata
    }

    /// Determine DEX name from program ID
    fn determine_dex_name(program_id: &solana_sdk::pubkey::Pubkey) -> &'static str {
        match program_id.to_string().as_str() {
            "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4" => "Jupiter",
            "j1o2qRpjcyUwEvwtcfhEQefh773ZgjxcVRry7LDqg5X" => "Jupiter Limit",
            "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM" => "Raydium",
            "DjVE6JNiYqPL2QXyCUUh8rNjHrbz9hXHNYt99MQ59qw1" => "Orca",
            "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" => "Meteora",
            _ => "Unknown DEX",
        }
    }

    /// Create trading pair string (unified function)
    fn create_trading_pair(input_mint: &str, output_mint: &str) -> String {
        let input_symbol = Self::get_token_symbol(input_mint);
        let output_symbol = Self::get_token_symbol(output_mint);
        format!("{}/{}", input_symbol, output_symbol)
    }

    /// Get token symbol from mint (unified with activity.rs logic)
    fn get_token_symbol(mint: &str) -> String {
        crate::api::handlers::activity::Activity::get_common_token_symbol(mint, 0) // Solana chain
    }

    /// Calculate price from two amounts (unified function)
    fn calculate_price(numerator: u64, denominator: u64) -> Option<String> {
        if denominator > 0 {
            let price = numerator as f64 / denominator as f64;
            Some(format!("{:.6}", price))
        } else {
            None
        }
    }

    /// Determine transfer direction based on wallet address and transaction accounts
    /// Returns true for receive, false for send
    async fn determine_transfer_direction(
        wallet_address: &str,
        tx: &crate::parser::types::TransactionWithEvent,
        token_event: &crate::parser::token_program::TokenProgramEvent,
        token_mint: Option<&str>,
    ) -> bool {
        use solana_sdk::pubkey::Pubkey;
        use std::str::FromStr;

        // Parse wallet address
        let wallet_pubkey = match Pubkey::from_str(wallet_address) {
            Ok(pubkey) => pubkey,
            Err(_) => {
                tracing::warn!("Failed to parse wallet address: {}", wallet_address);
                return true; // Default to receive if parsing fails
            }
        };

        // Check if wallet is in signers (usually means sending)
        if tx.signers.contains(&wallet_pubkey) {
            // If wallet signed the transaction, it's likely sending
            // Use dynamic threshold based on token decimals to distinguish between
            // actual transfers and dust/change amounts
            let amount = token_event.get_transfer_amount().unwrap_or(0);

            // Get decimals from database first, then fallback to token event, then default
            let decimals = if let Some(mint) = token_mint {
                Self::get_token_decimals_from_db(mint)
                    .await
                    .map(|d| d as u8)
                    .or_else(|| token_event.get_decimals())
                    .unwrap_or(9) // Default to SOL decimals
            } else {
                token_event.get_decimals().unwrap_or(9)
            };

            // Calculate threshold: 0.001 tokens (adjusted for decimals)
            // This helps distinguish between actual transfers and dust/change
            let threshold = 10_u64.pow((decimals as u32).saturating_sub(3));

            // If amount is above threshold, consider it a send operation
            // Fixed logic: large amounts from signers are sends, small amounts might be change/dust
            let is_send = amount > threshold;

            tracing::debug!(
                "Transfer direction analysis: wallet={}, amount={}, decimals={}, threshold={}, is_send={}",
                wallet_address, amount, decimals, threshold, is_send
            );

            !is_send // Return false for send, true for receive
        } else {
            // If wallet didn't sign, it's likely receiving
            true
        }
    }

    /// Filter out spam and phishing activities
    fn filter_spam_activities(activities: Vec<DbWalletActivity>) -> Vec<DbWalletActivity> {
        activities
            .into_iter()
            .filter(|activity| {
                // Filter out very small Solana transactions (< MIN_SPAM_AMOUNT_THRESHOLD)
                if activity.chain == 0 {
                    // Solana
                    if let (Some(token_amount), Some(token_decimals)) =
                        (activity.token_amount, activity.token_decimals)
                    {
                        let ui_amount = token_amount as f64 / 10_f64.powi(token_decimals as i32);
                        if ui_amount < MIN_SPAM_AMOUNT_THRESHOLD {
                            return false;
                        }
                    }

                    if let (Some(base_amount), Some(base_decimals)) =
                        (activity.base_amount, activity.base_decimals)
                    {
                        let ui_amount = base_amount as f64 / 10_f64.powi(base_decimals as i32);
                        if ui_amount < MIN_SPAM_AMOUNT_THRESHOLD {
                            return false;
                        }
                    }

                    // Filter out suspicious token mints (common spam patterns)
                    if let Some(token_mint) = &activity.token_mint {
                        // Filter out known spam token patterns
                        if Self::is_suspicious_token_mint(token_mint) {
                            return false;
                        }
                    }

                    if let Some(base_mint) = &activity.base_mint {
                        if Self::is_suspicious_token_mint(base_mint) {
                            return false;
                        }
                    }
                }

                true
            })
            .collect()
    }

    /// Check if a token mint address looks suspicious (spam/phishing)
    fn is_suspicious_token_mint(mint: &str) -> bool {
        // Known spam token patterns - these are common patterns used by spammers
        let suspicious_patterns = [
            // Common spam token addresses (these would be updated based on real data)
            "********************************", // System program (not a token)
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", // Token program (not a token)
        ];

        for pattern in &suspicious_patterns {
            if mint == *pattern {
                return true;
            }
        }

        // Additional heuristics for suspicious tokens:
        // 1. Tokens with all same characters (like 1111111... or aaaaaaa...)
        if mint.len() >= 10 {
            let first_char = mint.chars().next().unwrap();
            if mint.chars().all(|c| c == first_char) {
                return true;
            }
        }

        // 2. Very short mint addresses (invalid)
        if mint.len() < 32 {
            return true;
        }

        false
    }

    async fn create_fallback_token_metadata(token_mint: &str) -> Result<TokenMetadata> {
        let token_pubkey = Pubkey::from_str(token_mint)?;
        let rpc_client = crate::utils::get_rpc_client();

        let mint_account = rpc_client.get_account(&token_pubkey).await?;
        let mint_data = Mint::unpack(&mint_account.data)?;

        let short_name = if token_mint.len() >= 10 {
            format!("{}...{}", &token_mint[0..6], &token_mint[token_mint.len() - 4..])
        } else {
            token_mint.to_string()
        };

        let current_time = Utc::now().timestamp_millis();

        Ok(TokenMetadata {
            chain: Chain::Solana,
            address: token_mint.to_string(),
            name: format!("Token {}", short_name),
            symbol: short_name.clone(),
            decimals: mint_data.decimals,
            supply: mint_data.supply.into(),
            description: Some("Auto-generated fallback metadata".to_string()),
            image: None,
            website: None,
            twitter: None,
            telegram: None,
            dex_paid: DexPaid::Unknown,
            is_trench_token: false,
            create_dex: Dex::Unknown,
            create_block_number: None,
            create_tx_hash: None,
            create_bonding_curve: None,
            create_dev: None,
            create_timestamp_millis: current_time,
            migration_pool_address: None,
            migration_timestamp_millis: 0,
            update_timestamp_millis: current_time,
            uri: None,
            seller_fee_basis_points: None,
            creators: None,
            primary_sale_happened: None,
            is_mutable: None,
            update_authority: mint_data.mint_authority.map(|p| p.to_string()).into(),
            mint_authority: mint_data.mint_authority.map(|p| p.to_string()).into(),
            freeze_authority: mint_data.freeze_authority.map(|p| p.to_string()).into(),
            is_active: true,
            image_path: None,
        })
    }

    fn create_minimal_fallback_metadata(token_mint: &str) -> TokenMetadata {
        let short_name = if token_mint.len() >= 10 {
            format!("{}...{}", &token_mint[0..6], &token_mint[token_mint.len() - 4..])
        } else {
            token_mint.to_string()
        };

        let current_time = Utc::now().timestamp_millis();

        TokenMetadata {
            chain: Chain::Solana,
            address: token_mint.to_string(),
            name: format!("Unknown Token {}", short_name),
            symbol: short_name,
            decimals: 9,
            supply: 0.into(),
            description: Some("Minimal fallback metadata - metadata fetch failed".to_string()),
            image: None,
            website: None,
            twitter: None,
            telegram: None,
            dex_paid: DexPaid::Unknown,
            is_trench_token: false,
            create_dex: Dex::Unknown,
            create_block_number: None,
            create_tx_hash: None,
            create_bonding_curve: None,
            create_dev: None,
            create_timestamp_millis: current_time,
            migration_pool_address: None,
            migration_timestamp_millis: 0,
            update_timestamp_millis: current_time,
            uri: None,
            seller_fee_basis_points: None,
            creators: None,
            primary_sale_happened: None,
            is_mutable: None,
            update_authority: None,
            mint_authority: None,
            freeze_authority: None,
            is_active: true,
            image_path: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_filter_spam_activities() {
        let current_timestamp = chrono::Utc::now().timestamp();

        // Create test activities
        let activities = vec![
            // Valid activity
            DbWalletActivity {
                wallet_address: "test_wallet".to_string(),
                tx_signature: "test_sig_1".to_string(),
                activity_type: ActivityType::SpotTrade,
                token_mint: Some("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string()), /* USDC */
                token_decimals: Some(6),
                token_amount: Some(1_000_000), // 1 USDC
                base_mint: Some("So********************************111111112".to_string()), // SOL
                base_decimals: Some(9),
                base_amount: Some(1_000_000_000), // 1 SOL
                usd_value: Some(100.0),
                timestamp: current_timestamp,
                block_time: Some(current_timestamp),
                slot: Some(12345),
                chain: 0,
                metadata: None,
                created_at: current_timestamp,
            },
            // Spam activity (very small amount)
            DbWalletActivity {
                wallet_address: "test_wallet".to_string(),
                tx_signature: "test_sig_2".to_string(),
                activity_type: ActivityType::SpotTrade,
                token_mint: Some("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string()),
                token_decimals: Some(6),
                token_amount: Some(1), // 0.000001 USDC (very small)
                base_mint: Some("So********************************111111112".to_string()),
                base_decimals: Some(9),
                base_amount: Some(1000), // 0.000001 SOL (very small)
                usd_value: Some(0.0001),
                timestamp: current_timestamp,
                block_time: Some(current_timestamp),
                slot: Some(12346),
                chain: 0,
                metadata: None,
                created_at: current_timestamp,
            },
            // Suspicious token mint
            DbWalletActivity {
                wallet_address: "test_wallet".to_string(),
                tx_signature: "test_sig_3".to_string(),
                activity_type: ActivityType::SpotTrade,
                token_mint: Some("********************************".to_string()), /* Suspicious
                                                                                   * pattern */
                token_decimals: Some(6),
                token_amount: Some(1_000_000),
                base_mint: Some("So********************************111111112".to_string()),
                base_decimals: Some(9),
                base_amount: Some(1_000_000_000),
                usd_value: Some(100.0),
                timestamp: current_timestamp,
                block_time: Some(current_timestamp),
                slot: Some(12347),
                chain: 0,
                metadata: None,
                created_at: current_timestamp,
            },
        ];

        let filtered = PositionManager::filter_spam_activities(activities);

        // Should only have 1 valid activity (the first one)
        assert_eq!(filtered.len(), 1);
        // assert_eq!(filtered[0].id, Some(1));
    }

    #[test]
    fn test_is_suspicious_token_mint() {
        // Test known suspicious patterns
        assert!(PositionManager::is_suspicious_token_mint("********************************"));
        assert!(PositionManager::is_suspicious_token_mint(
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
        ));

        // Test short addresses
        assert!(PositionManager::is_suspicious_token_mint("short"));

        // Test valid addresses
        assert!(!PositionManager::is_suspicious_token_mint(
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        )); // USDC
        assert!(!PositionManager::is_suspicious_token_mint(
            "So********************************111111112"
        )); // SOL
    }

    #[tokio::test]
    #[ignore] // Ignore by default since it requires RPC connection
    async fn test_get_signatures_since_timestamp() {
        use std::str::FromStr;

        // Test with a known wallet (you can replace with any wallet for testing)
        let wallet_pubkey =
            Pubkey::from_str("9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM").unwrap();

        // Get signatures from last 24 hours
        let yesterday_timestamp = chrono::Utc::now().timestamp() - 24 * 60 * 60;

        match PositionManager::get_signatures_since_timestamp(
            &wallet_pubkey,
            yesterday_timestamp,
            Some(100),
        )
        .await
        {
            Ok(signatures) => {
                println!("Found {} signatures since yesterday", signatures.len());

                // Verify all signatures are newer than timestamp
                for sig in &signatures {
                    if let Some(block_time) = sig.block_time {
                        assert!(
                            block_time > yesterday_timestamp,
                            "Signature {} has block_time {} which is not newer than {}",
                            sig.signature,
                            block_time,
                            yesterday_timestamp
                        );
                    }
                }

                // Verify chronological order (oldest first)
                for i in 1..signatures.len() {
                    if let (Some(prev_time), Some(curr_time)) =
                        (signatures[i - 1].block_time, signatures[i].block_time)
                    {
                        assert!(
                            prev_time <= curr_time,
                            "Signatures not in chronological order: {} > {}",
                            prev_time,
                            curr_time
                        );
                    }
                }
            }
            Err(e) => {
                println!("RPC call failed (expected in test environment): {}", e);
            }
        }
    }

    // #[tokio::test]
    // async fn test_handle_signature() {
    //     crate::utils::setup_tracing();

    //     let storage_state = crate::utils::get_storage_state().await;
    //     let wallet_pubkey =
    //         Pubkey::from_str("2K9mq3S26ZTg8sxBdHdYEa2bcV2tXANgN7CtcCi9gUa2").unwrap();
    //     let signature =
    // Signature::from_str("
    // 2p5JA1RaaxFNz9SjhmzKAbihie45jdPJ4nY7t3uDBHMWWj1BDG2cwyW8QtB43phzSCeQqqR1tc3JongfCs6rnfVc").
    // unwrap();     let fallback_timestamp = 1747318517;
    //     PositionManager::handle_signature(
    //         &storage_state,
    //         &signature,
    //         &wallet_pubkey,
    //         fallback_timestamp,
    //         false,
    //     )
    //     .await
    //     .unwrap();
    // }

    // #[tokio::test]
    // async fn test_handle_limit_order1() {
    //     crate::utils::setup_tracing();

    //     let storage_state = crate::utils::get_storage_state().await;
    //     let wallet_pubkey =
    //         Pubkey::from_str("2K9mq3S26ZTg8sxBdHdYEa2bcV2tXANgN7CtcCi9gUa2").unwrap();

    //     // create a limit order
    //     let signature =
    // Signature::from_str("
    // 45fPvBQg6jf4Lv3ZSQ4g9kcAzdrWk82VgHAqzp7JVKPPLE34N3TSJQ5AeV8jLSbVaWANfBZPsumV8fuX5aNxRFaB").
    // unwrap();     let fallback_timestamp = 1747318968;
    //     PositionManager::handle_signature(
    //         &storage_state,
    //         &signature,
    //         &wallet_pubkey,
    //         fallback_timestamp,
    //         false,
    //     )
    //     .await
    //     .unwrap();

    //     // fill the limit order
    //     let signature =
    // Signature::from_str("
    // 49nTdA6PVUiqRZz93AUA8FPNV66MYvseNbTcq5t1dG6rkv8YTSBfGB3t3jd8BLLrwbr4Vet74T9P6Gh78SyxRQX9").
    // unwrap();     let fallback_timestamp = 1747318991;
    //     PositionManager::handle_signature(
    //         &storage_state,
    //         &signature,
    //         &wallet_pubkey,
    //         fallback_timestamp,
    //         false,
    //     )
    //     .await
    //     .unwrap();
    // }

    // #[tokio::test]
    // async fn test_handle_multiple_route_aggregator_v6() {
    //     crate::utils::setup_tracing();

    //     let storage_state = crate::utils::get_storage_state().await;
    //     let wallet_pubkey =
    //         Pubkey::from_str("8gzkWPhiJHM3gkcoR4nu8V9JBjWfogcz1oV6vSN9LQUA").unwrap();
    //     let signature =
    // Signature::from_str("
    // 5AG1p8iGwtaxjT9Cz3kHk4N5VCfywKDtDnZ2Qukrj9TkFLRQBBg7GNMYCucS3KnhEYD3bvBKjXv1GhN6yDQUqjP").
    // unwrap();     let fallback_timestamp = 1747318517;
    //     PositionManager::handle_signature(
    //         &storage_state,
    //         &signature,
    //         &wallet_pubkey,
    //         fallback_timestamp,
    //         false,
    //     )
    //     .await
    //     .unwrap();
    // }

    // #[tokio::test]
    // async fn test_handle_limit_order2() {
    //     crate::utils::setup_tracing();

    //     let storage_state = crate::utils::get_storage_state().await;
    //     let wallet_pubkey =
    //         Pubkey::from_str("2K9mq3S26ZTg8sxBdHdYEa2bcV2tXANgN7CtcCi9gUa2").unwrap();

    //     // create a limit order
    //     let signature =
    // Signature::from_str("
    // NBVTXRCwCTeSSq1Ym7KoVvadFhXnGm3YMAZEqe49xK7AEq7Uw8x9ywf6uP3p1pg65fv4MbqDW85CDjbNPzJpvRX").
    // unwrap();     let fallback_timestamp = 1747318968;
    //     PositionManager::handle_signature(
    //         &storage_state,
    //         &signature,
    //         &wallet_pubkey,
    //         fallback_timestamp,
    //         false,
    //     )
    //     .await
    //     .unwrap();
    // }

    #[tokio::test]
    async fn test_get_signatures_until() {
        crate::utils::setup_tracing();

        let wallet_pubkey =
            Pubkey::from_str("2K9mq3S26ZTg8sxBdHdYEa2bcV2tXANgN7CtcCi9gUa2").unwrap();
        let signature = Signature::from_str("5c3z6o2fiVPgAxNqBeooZ9sGgmcn3whDN9rSeJaSM4ZtdeWhhPpvrfwaryTQ7tR7VM3986wUeboYmQaaUuEreEHS").unwrap();
        let _signatures =
            PositionManager::get_signatures_until(&wallet_pubkey, None, Some(signature), None)
                .await
                .unwrap();

        // for signature in signatures.iter() {
        //     tracing::info!("Signature: {:?}", signature);
        // }

        let signature = Signature::from_str("tzMQnTLWd6Hcp63k5xTTyGShRPCcHXzY9ShgqxcXARXTxipLANCMmY4LfDoLtncfw95mF25Baqkj83zkd39nwrH").unwrap();
        let signatures =
            PositionManager::get_signatures_until(&wallet_pubkey, None, Some(signature), None)
                .await
                .unwrap();

        for signature in signatures.iter() {
            tracing::info!("Signature: {:?}", signature.signature);
        }
    }

    /// Simple validation to check if position data needs reprocessing
    /// Returns true if position data appears inconsistent and needs force update
    pub async fn needs_position_update(state: &StorageState, wallet_address: &str) -> Result<bool> {
        // Simple check: get recent transactions and see if there are unprocessed ones
        let unprocessed_transactions =
            state.get_unprocessed_successful_transactions(wallet_address).await?;

        // If there are more than a few unprocessed transactions, likely needs update
        Ok(unprocessed_transactions.len() > 3)
    }
}

/// Auto-trigger referral rewards for trading
async fn handle_referral_rewards(state: &StorageState, trade: &DbTrade) -> Result<()> {
    // Calculate trading volume in USD (base amount * base token price)
    let base_amount_ui = rust_decimal::Decimal::from(trade.base_amount) /
        rust_decimal::Decimal::from(10_u64.pow(trade.base_decimals as u32));

    let sol_price = crate::utils::get_sol_price().await;
    let trading_volume_usd = match trade.base_mint.as_str() {
        crate::constant::SOL_MINT => {
            base_amount_ui *
                rust_decimal::Decimal::from_f64(sol_price).unwrap_or(rust_decimal::Decimal::ZERO)
        }
        crate::constant::USDC_MINT | crate::constant::USDT_MINT => {
            base_amount_ui // Already in USD
        }
        _ => {
            // For other tokens, try to get price or fallback to SOL price
            base_amount_ui *
                rust_decimal::Decimal::from_f64(sol_price).unwrap_or(rust_decimal::Decimal::ZERO)
        }
    };

    if trading_volume_usd > rust_decimal::Decimal::ZERO {
        let config = Config::get();

        // Check if this is the first trade
        let is_first_trade =
            state.api_db.is_first_trade(&trade.wallet_address).await.unwrap_or(false);

        // Process signup reward for first trade
        if is_first_trade {
            if let Err(e) = state.api_db.process_signup_reward(&trade.wallet_address).await {
                tracing::error!(
                    "Failed to process signup reward for {}: {}",
                    trade.wallet_address,
                    e
                );
            }
        }

        // Process multi-tier trading rewards based on volume
        if let Err(e) = state
            .api_db
            .process_multi_tier_trading_rewards(
                &trade.wallet_address,
                trading_volume_usd,
                &trade.tx_sig,
                config.referral_tier1_percentage,
                config.referral_tier2_percentage,
                config.referral_tier3_percentage,
            )
            .await
        {
            tracing::error!(
                "Failed to process trading rewards for {}: {}",
                trade.wallet_address,
                e
            );
        } else {
            tracing::debug!(
                "Processed referral rewards for trade: wallet={}, volume_usd={}, tx={}",
                trade.wallet_address,
                trading_volume_usd,
                trade.tx_sig
            );
        }
    }

    Ok(())
}

#[cfg(test)]
mod position_tests {
    use super::*;

    #[tokio::test]
    async fn test_get_token_decimals_from_rpc() {
        crate::utils::setup_tracing();

        // Test with a known token mint
        let token_mint = "8d2HXxu5PCy21ZrPityWSKPsnSdACYoUnvZ4SfFmazZb";
        let decimals = PositionManager::get_token_decimals_from_rpc(token_mint).await;

        tracing::info!("Token {} decimals from RPC: {:?}", token_mint, decimals);

        // Test with USDC (known to have 6 decimals)
        let usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
        let usdc_decimals = PositionManager::get_token_decimals_from_rpc(usdc_mint).await;

        tracing::info!("USDC decimals from RPC: {:?}", usdc_decimals);
        assert_eq!(usdc_decimals, Some(6));
    }
}
