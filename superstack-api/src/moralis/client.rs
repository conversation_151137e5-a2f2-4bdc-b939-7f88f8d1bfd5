#[allow(dead_code)]
use std::sync::OnceLock;

use anyhow::Result;
use reqwest::{
    header::{HeaderMap, HeaderValue},
    Client,
};

use super::*;

pub struct MoralisClient {
    client: Client,
}

impl MoralisClient {
    pub fn get() -> &'static MoralisClient {
        static CLIENT: OnceLock<MoralisClient> = OnceLock::new();

        CLIENT.get_or_init(|| {
            let config = crate::config::Config::get();
            let api_key = &config.moralis_api_key;

            let mut headers = HeaderMap::new();
            headers.insert("accept", HeaderValue::from_static("application/json"));
            headers.insert("X-API-Key", HeaderValue::from_str(api_key).unwrap());

            let client = reqwest::Client::builder()
                .default_headers(headers)
                .build()
                .expect("Failed to build client");

            Self { client }
        })
    }

    // Get Pump.fun token price
    pub async fn get_pump_token_price(&self, token_address: &str) -> Result<TokenPrice> {
        let url =
            format!("https://solana-gateway.moralis.io/token/mainnet/{}/price", token_address);

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch token price for {}: HTTP {}",
                token_address,
                response.status()
            ));
        }

        let token_price = response.json::<TokenPrice>().await?;
        Ok(token_price)
    }

    // Get Pump.fun token metadata
    pub async fn get_pump_token_metadata(&self, token_address: &str) -> Result<TokenMetadata> {
        let url =
            format!("https://solana-gateway.moralis.io/token/mainnet/{}/metadata", token_address);

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch token metadata: HTTP {}",
                response.status()
            ));
        }

        let token_metadata = response.json::<TokenMetadata>().await?;
        Ok(token_metadata)
    }

    // Get Pump.fun token swaps
    #[allow(dead_code)]
    pub async fn get_pump_token_swaps(
        &self,
        token_address: &str,
        is_desc: bool,
        cursor: Option<&str>,
        limit: Option<usize>,
        transaction_types: Option<&str>,
    ) -> Result<SwapResponse> {
        let mut url =
            format!("https://solana-gateway.moralis.io/token/mainnet/{}/swaps", token_address);

        // Build query parameters
        let mut query_params = Vec::new();

        if let Some(cursor_val) = cursor {
            if !cursor_val.is_empty() {
                query_params.push(format!("cursor={}", cursor_val));
            }
        }

        if let Some(limit_val) = limit {
            query_params.push(format!("limit={}", limit_val));
        }

        if is_desc {
            query_params.push("order=DESC".to_string());
        } else {
            query_params.push("order=ASC".to_string());
        }

        if let Some(types) = transaction_types {
            query_params.push(format!("transactionTypes={}", types));
        }

        if !query_params.is_empty() {
            url = format!("{}?{}", url, query_params.join("&"));
        }

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch token swaps for {}: HTTP {}",
                token_address,
                response.status()
            ));
        }

        let swaps = response.json::<SwapResponse>().await?;
        Ok(swaps)
    }

    #[allow(dead_code)]
    pub async fn get_pump_token_swaps_by_wallet(
        &self,
        wallet_address: &str,
        token_address: Option<&str>,
        is_desc: bool,
        cursor: Option<&str>,
        limit: Option<usize>,
        transaction_types: Option<&str>,
    ) -> Result<SwapResponse> {
        let mut url =
            format!("https://solana-gateway.moralis.io/account/mainnet/{}/swaps", wallet_address);

        // Build query parameters
        let mut query_params = Vec::new();

        if let Some(token_address_val) = token_address {
            query_params.push(format!("tokenAddress={}", token_address_val));
        }

        if let Some(cursor_val) = cursor {
            if !cursor_val.is_empty() {
                query_params.push(format!("cursor={}", cursor_val));
            }
        }

        if let Some(limit_val) = limit {
            query_params.push(format!("limit={}", limit_val));
        }

        if is_desc {
            query_params.push("order=DESC".to_string());
        } else {
            query_params.push("order=ASC".to_string());
        }

        if let Some(types) = transaction_types {
            query_params.push(format!("transactionTypes={}", types));
        }

        if !query_params.is_empty() {
            url = format!("{}?{}", url, query_params.join("&"));
        }

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch token swaps for wallet {}: HTTP {}",
                wallet_address,
                response.status()
            ));
        }

        let swaps = response.json::<SwapResponse>().await?;
        Ok(swaps)
    }

    // Get Pump.fun token pairs
    #[allow(dead_code)]
    pub async fn get_pump_token_pairs(&self, token_address: &str) -> Result<TokenPairsResponse> {
        let url =
            format!("https://solana-gateway.moralis.io/token/mainnet/{}/pairs", token_address);

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch token pairs for {}: HTTP {}",
                token_address,
                response.status()
            ));
        }

        let pairs = response.json::<TokenPairsResponse>().await?;
        Ok(pairs)
    }

    // Get new Pump.fun tokens
    #[allow(dead_code)]
    pub async fn get_new_pump_tokens(
        &self,
        limit: Option<usize>,
        cursor: Option<&str>,
    ) -> Result<TokensResponse> {
        let mut url =
            "https://solana-gateway.moralis.io/token/mainnet/exchange/pumpfun/new".to_string();

        // Build query parameters
        let mut query_params = Vec::new();

        if let Some(limit_val) = limit {
            query_params.push(format!("limit={}", limit_val));
        } else {
            // Default limit if none provided
            query_params.push("limit=100".to_string());
        }

        if let Some(cursor_val) = cursor {
            if !cursor_val.is_empty() {
                query_params.push(format!("cursor={}", cursor_val));
            }
        }

        if !query_params.is_empty() {
            url = format!("{}?{}", url, query_params.join("&"));
        }

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch new Pump.fun tokens: HTTP {}",
                response.status()
            ));
        }

        let tokens = response.json::<TokensResponse>().await?;
        Ok(tokens)
    }

    // Get bonding Pump.fun tokens
    #[allow(dead_code)]
    pub async fn get_bonding_pump_tokens(
        &self,
        limit: Option<usize>,
        cursor: Option<&str>,
    ) -> Result<TokensResponse> {
        let mut url =
            "https://solana-gateway.moralis.io/token/mainnet/exchange/pumpfun/bonding".to_string();

        // Build query parameters
        let mut query_params = Vec::new();

        if let Some(limit_val) = limit {
            query_params.push(format!("limit={}", limit_val));
        } else {
            // Default limit if none provided
            query_params.push("limit=100".to_string());
        }

        if let Some(cursor_val) = cursor {
            if !cursor_val.is_empty() {
                query_params.push(format!("cursor={}", cursor_val));
            }
        }

        if !query_params.is_empty() {
            url = format!("{}?{}", url, query_params.join("&"));
        }

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch new Pump.fun tokens: HTTP {}",
                response.status()
            ));
        }

        let tokens = response.json::<TokensResponse>().await?;
        Ok(tokens)
    }

    // Get graduated Pump.fun tokens
    #[allow(dead_code)]
    pub async fn get_graduated_pump_tokens(
        &self,
        limit: Option<usize>,
        cursor: Option<&str>,
    ) -> Result<TokensResponse> {
        let mut url = "https://solana-gateway.moralis.io/token/mainnet/exchange/pumpfun/graduated"
            .to_string();

        // Build query parameters
        let mut query_params = Vec::new();

        if let Some(limit_val) = limit {
            query_params.push(format!("limit={}", limit_val));
        } else {
            // Default limit if none provided
            query_params.push("limit=100".to_string());
        }

        if let Some(cursor_val) = cursor {
            if !cursor_val.is_empty() {
                query_params.push(format!("cursor={}", cursor_val));
            }
        }

        if !query_params.is_empty() {
            url = format!("{}?{}", url, query_params.join("&"));
        }

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch new Pump.fun tokens: HTTP {}",
                response.status()
            ));
        }

        let tokens = response.json::<TokensResponse>().await?;
        Ok(tokens)
    }

    // Get Pump.fun token bonding status
    #[allow(dead_code)]
    pub async fn get_pump_token_bonding_status(
        &self,
        token_address: &str,
    ) -> Result<TokenBondingStatus> {
        let url = format!(
            "https://solana-gateway.moralis.io/token/mainnet/{}/bonding-status",
            token_address
        );

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch token bonding status for {}: HTTP {}",
                token_address,
                response.status()
            ));
        }

        let bonding_status = response.json::<TokenBondingStatus>().await?;
        Ok(bonding_status)
    }

    // Get token pair stats
    #[allow(dead_code)]
    pub async fn get_token_pair_stats(&self, pair_address: &str) -> Result<TokenPairStats> {
        let url =
            format!("https://solana-gateway.moralis.io/token/mainnet/pairs/{}/stats", pair_address);

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch token pair stats for {}: HTTP {}",
                pair_address,
                response.status()
            ));
        }

        let pair_stats = response.json::<TokenPairStats>().await?;
        Ok(pair_stats)
    }

    pub async fn get_metaplex_file(&self, url: &str) -> Result<MetaplexJson> {
        let response = self.client.get(url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to fetch metaplex file for {}: HTTP {}",
                url,
                response.status()
            ));
        }

        let metaplex_json = response.json::<MetaplexJson>().await?;
        Ok(metaplex_json)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    const PUMP_TOKEN: &str = "BQQzEvYT4knThhkSPBvSKBLg1LEczisWLhx5ydJipump";

    // Real-world integration test - needs valid API key in env
    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_pump_token_price() {
        crate::utils::setup_tracing();

        // Use a known Pump.fun token
        let pump_token = PUMP_TOKEN;

        // Get the provider that uses the actual API key
        let provider = MoralisClient::get();

        // Call the actual API
        let result = provider.get_pump_token_price(pump_token).await.unwrap();

        tracing::info!("price: {:?}", result);

        // Basic validation
        assert_eq!(result.token_address, pump_token);
        assert!(result.usd_price > 0.0, "Price should be greater than zero");
        assert!(!result.exchange_name.is_empty(), "Exchange name should not be empty");
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_pump_token_metadata() {
        crate::utils::setup_tracing();

        // Use a known Pump.fun token
        let pump_token = PUMP_TOKEN;

        // Get the provider that uses the actual API key
        let provider = MoralisClient::get();

        // Call the actual API
        let result = provider.get_pump_token_metadata(pump_token).await.unwrap();

        tracing::info!("metadata: {:?}", result);

        // Basic validation
        assert_eq!(result.mint, pump_token);
        assert!(!result.name.is_empty(), "Name should not be empty");
        assert!(!result.symbol.is_empty(), "Symbol should not be empty");
        assert!(!result.total_supply.is_empty(), "Total supply should not be empty");
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_pump_token_swaps() {
        crate::utils::setup_tracing();

        let provider = MoralisClient::get();

        let result =
            provider.get_pump_token_swaps(PUMP_TOKEN, true, None, Some(2), None).await.unwrap();

        tracing::info!("swaps: {:?}", result);

        let cursor = result.cursor;
        let wallet_address = result.result[0].wallet_address.clone();

        let result = provider
            .get_pump_token_swaps(PUMP_TOKEN, true, Some(&cursor), Some(2), Some("buy"))
            .await
            .unwrap();

        tracing::info!("swaps: {:?}", result);

        let result = provider
            .get_pump_token_swaps_by_wallet(
                &wallet_address,
                Some(PUMP_TOKEN),
                true,
                None,
                Some(1),
                Some("buy"),
            )
            .await
            .unwrap();

        tracing::info!("swaps by wallet: {:?}", result);
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_pump_token_pairs() {
        crate::utils::setup_tracing();

        // Use a known Pump.fun token
        let pump_token = PUMP_TOKEN;

        // Get the provider that uses the actual API key
        let provider = MoralisClient::get();

        // Call the actual API
        let result = provider.get_pump_token_pairs(pump_token).await.unwrap();

        tracing::info!("token pairs: {:?}", result);

        // Basic validation
        assert!(!result.pairs.is_empty(), "Should have at least one pair");

        if let Some(pair) = result.pairs.first() {
            assert!(!pair.exchange_name.is_empty(), "Exchange name should not be empty");
            assert!(!pair.pair_address.is_empty(), "Pair address should not be empty");
            assert!(!pair.pair_label.is_empty(), "Pair label should not be empty");
            assert!(pair.usd_price > 0.0, "USD price should be greater than zero");
            assert!(pair.liquidity_usd > 0.0, "Liquidity USD should be greater than zero");

            // Validate pair tokens
            assert_eq!(pair.pair.len(), 2, "Should have exactly two tokens in the pair");
            assert!(!pair.pair[0].token_address.is_empty(), "Token address should not be empty");
            assert!(!pair.pair[1].token_address.is_empty(), "Token address should not be empty");
        }
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_pump_token_bonding_status() {
        crate::utils::setup_tracing();

        // Use a known Pump.fun token
        let pump_token = PUMP_TOKEN;

        // Get the provider that uses the actual API key
        let provider = MoralisClient::get();

        // Call the actual API
        let result = provider.get_pump_token_bonding_status(pump_token).await.unwrap();

        tracing::info!("bonding status: {:?}", result);

        // Basic validation
        assert_eq!(result.mint, pump_token);
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_new_pump_tokens() {
        crate::utils::setup_tracing();

        // Get the provider that uses the actual API key
        let provider = MoralisClient::get();

        // Call the actual API with a limit of 2 tokens
        let result = provider.get_new_pump_tokens(Some(2), None).await.unwrap();

        tracing::info!("new tokens: {:?}", result);

        // Basic validation
        assert!(!result.result.is_empty(), "Should have at least one new token");

        if let Some(token) = result.result.first() {
            assert!(!token.token_address.is_empty(), "Token address should not be empty");

            // Try to parse prices to ensure they're valid numbers
            let price_native = token.price_native.parse::<f64>().unwrap();
            let price_usd = token.price_usd.parse::<f64>().unwrap();

            assert!(price_native > 0.0, "Native price should be greater than zero");
            assert!(price_usd > 0.0, "USD price should be greater than zero");
        }

        // Test pagination if there's a cursor
        if let Some(cursor) = &result.cursor {
            tracing::info!("Testing pagination with cursor: {}", cursor);

            let next_page = provider.get_new_pump_tokens(Some(2), Some(cursor)).await.unwrap();

            tracing::info!("next page tokens: {:?}", next_page);
            assert!(!next_page.result.is_empty(), "Should have tokens in the next page");
        }
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_bonding_pump_tokens() {
        crate::utils::setup_tracing();

        // Get the provider that uses the actual API key
        let provider = MoralisClient::get();

        // Call the actual API with a limit of 2 tokens
        let result = provider.get_bonding_pump_tokens(Some(2), None).await.unwrap();

        tracing::info!("bonding tokens: {:?}", result);

        // Basic validation
        assert!(!result.result.is_empty(), "Should have at least one bonding token");

        if let Some(token) = result.result.first() {
            assert!(!token.token_address.is_empty(), "Token address should not be empty");

            // Try to parse prices to ensure they're valid numbers
            let price_native = token.price_native.parse::<f64>().unwrap();
            let price_usd = token.price_usd.parse::<f64>().unwrap();

            assert!(price_native > 0.0, "Native price should be greater than zero");
            assert!(price_usd > 0.0, "USD price should be greater than zero");
        }

        // Test pagination if there's a cursor
        if let Some(cursor) = &result.cursor {
            tracing::info!("Testing pagination with cursor: {}", cursor);

            let next_page = provider.get_bonding_pump_tokens(Some(2), Some(cursor)).await.unwrap();

            tracing::info!("next page tokens: {:?}", next_page);
            assert!(!next_page.result.is_empty(), "Should have tokens in the next page");
        }
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_graduated_pump_tokens() {
        crate::utils::setup_tracing();

        // Get the provider that uses the actual API key
        let provider = MoralisClient::get();

        // Call the actual API with a limit of 2 tokens
        let result = provider.get_graduated_pump_tokens(Some(2), None).await.unwrap();

        tracing::info!("graduated tokens: {:?}", result);

        // Basic validation
        assert!(!result.result.is_empty(), "Should have at least one graduated token");

        if let Some(token) = result.result.first() {
            assert!(!token.token_address.is_empty(), "Token address should not be empty");

            // Try to parse prices to ensure they're valid numbers
            let price_native = token.price_native.parse::<f64>().unwrap();
            let price_usd = token.price_usd.parse::<f64>().unwrap();

            assert!(price_native > 0.0, "Native price should be greater than zero");
            assert!(price_usd > 0.0, "USD price should be greater than zero");
        }

        // Test pagination if there's a cursor
        if let Some(cursor) = &result.cursor {
            tracing::info!("Testing pagination with cursor: {}", cursor);

            let next_page =
                provider.get_graduated_pump_tokens(Some(2), Some(cursor)).await.unwrap();

            tracing::info!("next page tokens: {:?}", next_page);
            assert!(!next_page.result.is_empty(), "Should have tokens in the next page");
        }
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_token_pair_stats() {
        crate::utils::setup_tracing();

        // Get the provider that uses the actual API key
        let provider = MoralisClient::get();

        // Get token pairs first to find a valid pair address
        let pairs_result = provider.get_pump_token_pairs(PUMP_TOKEN).await.unwrap();

        // Ensure we have at least one pair
        assert!(!pairs_result.pairs.is_empty(), "Should have at least one pair");

        // Use the first pair's address
        let pair_address = &pairs_result.pairs[0].pair_address;
        tracing::info!("Using pair address: {}", pair_address);

        // Call the actual API to get pair stats
        let result = provider.get_token_pair_stats(pair_address).await.unwrap();

        tracing::info!("pair stats: {:?}", result);

        // Basic validation
        assert_eq!(result.pair_address, *pair_address);
        assert!(!result.token_name.is_empty(), "Token name should not be empty");
        assert!(!result.token_symbol.is_empty(), "Token symbol should not be empty");
        assert!(!result.pair_label.is_empty(), "Pair label should not be empty");
        assert!(!result.exchange.is_empty(), "Exchange name should not be empty");
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_metaplex_file() {
        crate::utils::setup_tracing();

        let provider = MoralisClient::get();

        let url = "https://ipfs.io/ipfs/QmPCg62SmjZpxzR6SJ5xMcH6nLnLTjwBTRoVcgQPVbf7iq";
        let result = provider.get_metaplex_file(url).await.unwrap();

        tracing::info!("metaplex file: {:?}", result);
    }
}
