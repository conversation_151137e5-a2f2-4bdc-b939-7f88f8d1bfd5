use std::{
    collections::HashMap,
    sync::{Arc, OnceLock},
};

use anyhow::Result;
use tokio::sync::RwLock;

use crate::constant::SOL_MINT;

use super::{client::MoralisClient, *};

#[allow(dead_code)]
pub struct MoralisProvider {
    metadatas: Arc<RwLock<HashMap<String, AggregatedTokenMetadata>>>,
    pairs: Arc<RwLock<HashMap<String, Vec<TokenPair>>>>,
    statss: Arc<RwLock<HashMap<String, LatestTokenPairStats>>>,
    prices: Arc<RwLock<HashMap<String, LatestTokenPrice>>>,
}

impl MoralisProvider {
    pub fn get() -> &'static MoralisProvider {
        static PROVIDER: OnceLock<MoralisProvider> = OnceLock::new();

        PROVIDER.get_or_init(|| {
            let metadatas = Arc::new(RwLock::new(HashMap::new()));
            let pairs = Arc::new(RwLock::new(HashMap::new()));
            let statss = Arc::new(RwLock::new(HashMap::new()));
            let prices = Arc::new(RwLock::new(HashMap::new()));

            MoralisProvider { metadatas, pairs, statss, prices }
        })
    }

    pub async fn get_metadata(&self, token_address: &str) -> Result<AggregatedTokenMetadata> {
        {
            let metadatas = self.metadatas.read().await;
            if let Some(metadata) = metadatas.get(token_address) {
                return Ok(metadata.clone());
            }
        }

        let client = MoralisClient::get();
        let metadata = client.get_pump_token_metadata(token_address).await?;

        tracing::info!("metadata: {:?}", metadata);

        let metaplex_json = client.get_metaplex_file(&metadata.metaplex.metadata_uri).await?;

        let mut metadatas = self.metadatas.write().await;
        let aggregated_metadata = AggregatedTokenMetadata { metadata, json: metaplex_json };
        metadatas.insert(token_address.to_string(), aggregated_metadata.clone());

        Ok(aggregated_metadata)
    }

    #[allow(dead_code)]
    pub async fn get_pair(&self, token_address: &str, exchange_name: &str) -> Result<TokenPair> {
        {
            let pairs = self.pairs.read().await;
            if let Some(pairs) = pairs.get(token_address) {
                for pair in pairs {
                    if pair.exchange_name == exchange_name && !pair.inactive_pair {
                        return Ok(pair.clone());
                    }
                }
            }
        }

        let client = MoralisClient::get();
        let pair = client.get_pump_token_pairs(token_address).await?;
        if pair.pairs.is_empty() {
            return Err(anyhow::anyhow!("No pairs found for token address: {}", token_address));
        }

        let mut pairs = self.pairs.write().await;
        pairs.insert(token_address.to_string(), pair.pairs.clone());

        for pair in pair.pairs {
            if pair.exchange_name == exchange_name && !pair.inactive_pair {
                return Ok(pair.clone());
            }
        }

        Err(anyhow::anyhow!("No pairs found for token address: {}", token_address))
    }

    #[allow(dead_code)]
    pub async fn get_stats(&self, pair_address: &str) -> Result<TokenPairStats> {
        {
            let statss = self.statss.read().await;
            if let Some(stats) = statss.get(pair_address) {
                if stats.is_up_to_date() {
                    return Ok(stats.data.clone());
                }
            }
        }

        let client = MoralisClient::get();
        let stats = client.get_token_pair_stats(pair_address).await?;

        let latest_stats = LatestTokenPairStats::new(stats.clone());
        let mut statss = self.statss.write().await;
        statss.insert(pair_address.to_string(), latest_stats);

        Ok(stats)
    }

    pub async fn get_price(&self, token_address: &str) -> Result<TokenPrice> {
        {
            let prices = self.prices.read().await;
            if let Some(price) = prices.get(token_address) {
                if price.is_up_to_date() {
                    return Ok(price.data.clone());
                }
            }
        }

        let client = MoralisClient::get();
        let price = client.get_pump_token_price(token_address).await?;

        let latest_price = LatestTokenPrice::new(price.clone());
        let mut prices = self.prices.write().await;
        prices.insert(token_address.to_string(), latest_price);

        Ok(price)
    }

    #[allow(dead_code)]
    pub async fn get_sol_price(&self) -> Result<f64> {
        let price = self.get_price(SOL_MINT).await?;
        Ok(price.usd_price)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    const PUMP_TOKEN: &str = "BQQzEvYT4knThhkSPBvSKBLg1LEczisWLhx5ydJipump";

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_metadata() {
        crate::utils::setup_tracing();

        let provider = MoralisProvider::get();
        let metadata = provider.get_metadata(PUMP_TOKEN).await.unwrap();
        tracing::info!("{:?}", metadata);
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_pairs() {
        crate::utils::setup_tracing();

        let provider = MoralisProvider::get();
        let pair = provider.get_pair(PUMP_TOKEN, GRADUATED_TOKEN_PUMP_SWAP_PAIR).await.unwrap();
        tracing::info!("{:?}", pair);
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_stats() {
        crate::utils::setup_tracing();

        let provider = MoralisProvider::get();
        let pair = provider.get_pair(PUMP_TOKEN, GRADUATED_TOKEN_PUMP_SWAP_PAIR).await.unwrap();
        let stats = provider.get_stats(&pair.pair_address).await.unwrap();
        tracing::info!("{:?}", stats);
    }

    #[tokio::test]
    // #[ignore] // Run with: cargo test -- --ignored
    async fn test_get_price() {
        crate::utils::setup_tracing();

        let provider = MoralisProvider::get();
        let price = provider.get_price(PUMP_TOKEN).await.unwrap();
        tracing::info!("{:?}", price);
    }
}
