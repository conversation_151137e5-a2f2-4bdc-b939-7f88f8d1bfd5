pub mod client;
pub mod provider;

use std::time::{SystemTime, UNIX_EPOCH};

use serde::{Deserialize, Serialize};

// For new tokens
#[allow(dead_code)]
pub const NEW_TOKEN_PUMP_FUN_PAIR: &str = "Pump.Fun";
// For graduated tokens
#[allow(dead_code)]
pub const GRADUATED_TOKEN_PUMP_SWAP_PAIR: &str = "PumpSwap";

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NativePrice {
    pub value: String,
    pub symbol: String,
    pub name: String,
    pub decimals: u8,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenPrice {
    pub token_address: String,
    pub pair_address: String,
    pub exchange_name: String,
    pub exchange_address: String,
    pub native_price: NativePrice,
    pub usd_price: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Metaplex {
    pub metadata_uri: String,
    pub master_edition: bool,
    pub is_mutable: bool,
    pub seller_fee_basis_points: u64,
    pub update_authority: String,
    pub primary_sale_happened: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct MetaplexJson {
    pub name: String,
    pub symbol: String,
    pub description: String,
    pub image: Option<String>,
    pub show_name: Option<bool>,
    pub created_on: Option<String>,
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub website: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct AggregatedTokenMetadata {
    pub metadata: TokenMetadata,
    pub json: MetaplexJson,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenMetadata {
    pub mint: String,
    pub standard: String,
    pub name: String,
    pub symbol: String,
    pub logo: Option<String>,
    pub decimals: String,
    pub metaplex: Metaplex,
    pub fully_diluted_value: String,
    pub total_supply: String,
    pub total_supply_formatted: String,
    pub links: Option<serde_json::Value>,
    pub description: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SwapResponse {
    pub cursor: String,
    pub page: i32,
    pub page_size: i32,
    pub result: Vec<TokenSwap>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenSwap {
    pub transaction_hash: String,
    pub transaction_type: String,
    pub transaction_index: i32,
    pub sub_category: Option<String>,
    pub block_timestamp: String,
    pub block_number: i64,
    pub wallet_address: String,
    pub pair_address: String,
    pub pair_label: String,
    pub exchange_address: String,
    pub exchange_name: String,
    pub exchange_logo: Option<String>,
    pub base_token: String,
    pub quote_token: String,
    pub bought: SwapTokenDetails,
    pub sold: SwapTokenDetails,
    pub base_quote_price: String,
    pub total_value_usd: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SwapTokenDetails {
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub logo: Option<String>,
    pub amount: String,
    pub usd_price: f64,
    pub usd_amount: f64,
    pub token_type: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokenPairsResponse {
    pub pairs: Vec<TokenPair>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenPair {
    pub exchange_address: String,
    pub exchange_name: String,
    pub exchange_logo: Option<String>,
    pub pair_address: String,
    pub pair_label: String,
    pub usd_price: f64,
    pub usd_price_24hr_percent_change: f64,
    pub usd_price_24hr_usd_change: f64,
    pub volume_24hr_native: f64,
    pub volume_24hr_usd: f64,
    pub liquidity_usd: f64,
    pub base_token: String,
    pub quote_token: String,
    pub inactive_pair: bool,
    pub pair: Vec<PairToken>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct PairToken {
    pub token_address: String,
    pub token_name: String,
    pub token_symbol: String,
    pub token_logo: Option<String>,
    pub token_decimals: String,
    pub pair_token_type: String,
    pub liquidity_usd: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenBondingStatus {
    pub mint: String,
    pub bonding_progress: Option<f64>,
    pub graduated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokensResponse {
    pub result: Vec<Token>,
    pub cursor: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Token {
    pub token_address: String,
    pub name: Option<String>,
    pub symbol: Option<String>,
    pub logo: Option<String>,
    pub decimals: Option<String>,
    pub price_native: String,
    pub price_usd: String,
    pub liquidity: String,
    pub fully_diluted_valuation: Option<String>,
    pub created_at: Option<String>,
    pub bonding_curve_progress: Option<f64>,
    pub graduated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenPairStats {
    pub token_address: String,
    pub token_name: String,
    pub token_symbol: String,
    pub token_logo: Option<String>,
    pub pair_created: Option<String>,
    pub pair_label: String,
    pub pair_address: String,
    pub exchange: String,
    pub exchange_address: String,
    pub exchange_logo: Option<String>,
    pub exchange_url: Option<String>,
    pub current_usd_price: String,
    pub current_native_price: String,
    pub total_liquidity_usd: String,
    pub price_percent_change: PercentChange,
    pub liquidity_percent_change: PercentChange,
    pub buys: TimeU64Metrics,
    pub sells: TimeU64Metrics,
    pub total_volume: TimeF64Metrics,
    pub buy_volume: TimeF64Metrics,
    pub sell_volume: TimeF64Metrics,
    pub buyers: TimeU64Metrics,
    pub sellers: TimeU64Metrics,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PercentChange {
    #[serde(rename = "5min")]
    pub five_min: f64,
    #[serde(rename = "1h")]
    pub one_hour: f64,
    #[serde(rename = "4h")]
    pub four_hour: f64,
    #[serde(rename = "24h")]
    pub twenty_four_hour: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TimeF64Metrics {
    #[serde(rename = "5min")]
    pub five_min: f64,
    #[serde(rename = "1h")]
    pub one_hour: f64,
    #[serde(rename = "4h")]
    pub four_hour: f64,
    #[serde(rename = "24h")]
    pub twenty_four_hour: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TimeU64Metrics {
    #[serde(rename = "5min")]
    pub five_min: u64,
    #[serde(rename = "1h")]
    pub one_hour: u64,
    #[serde(rename = "4h")]
    pub four_hour: u64,
    #[serde(rename = "24h")]
    pub twenty_four_hour: u64,
}

// Define a trait for the TTL (Time To Live)
pub trait HasTTL {
    const TTL: u64; // Time in seconds
}

// Implement the trait for your types
impl HasTTL for TokenPairStats {
    const TTL: u64 = 60; // 1 minute
}

impl HasTTL for TokenPrice {
    const TTL: u64 = 10; // 10 seconds
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LatestData<T: HasTTL> {
    pub data: T,
    pub updated_at: u64,
}

impl<T: HasTTL> LatestData<T> {
    pub fn new(data: T) -> Self {
        let updated_at = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
        Self { data, updated_at }
    }

    pub fn is_up_to_date(&self) -> bool {
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
        now - self.updated_at < T::TTL
    }
}

pub type LatestTokenPairStats = LatestData<TokenPairStats>;
pub type LatestTokenPrice = LatestData<TokenPrice>;
