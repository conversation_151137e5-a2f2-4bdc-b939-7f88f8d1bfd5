mod api;
mod config;
mod constant;
mod dexscreener;
mod health;
mod hyperevm;
mod hyperliquid_parser;

mod jupiter;
mod metrics;
mod models;
mod moralis;
mod parser;
mod perp_category;
mod portfolio;
mod pump;
mod relay;
mod token;
mod transaction;
mod utils;
mod wallet;

use std::sync::Arc;

use models::{ApiDatabase, RedisClient, StorageState};
use superstack_data::{data_provider::IndexerDataProvider, postgres::PostgresDatabase};

#[tokio::main]
async fn main() {
    crate::utils::setup_tracing();

    let _ = rustls::crypto::aws_lc_rs::default_provider().install_default();

    let config = crate::config::Config::get();
    let indexer_config = superstack_data::config::Config::get();

    // Initialize perp category manager
    if let Err(e) = crate::perp_category::init_perp_category_manager() {
        tracing::warn!("Failed to initialize perp category manager: {}", e);
    }

    let api_db = Arc::new(
        ApiDatabase::new(&config.api_database_url, config.max_db_connections, true).await.unwrap(),
    );

    let indexer_db = Arc::new(
        PostgresDatabase::new(
            &indexer_config.postgres_indexer_database_url,
            indexer_config.postgres_indexer_max_connections,
            indexer_config.postgres_indexer_need_migrate,
        )
        .await
        .unwrap(),
    );

    let indexer_data_provider = Arc::new(IndexerDataProvider::new().await);

    let storage_state = StorageState {
        api_db,
        indexer_db,
        indexer_data_provider,
        redis_client: RedisClient::new(&config.redis_url).await.unwrap(),
    };

    let server_handle = crate::api::spawn_server(storage_state.clone(), config.port);

    let transaction_tracker_handle =
        crate::transaction::tracker::TransactionTracker::run(storage_state.clone());

    tokio::select! {
        _ = server_handle => {
            tracing::info!("Server handle finished");
        }
        _ = transaction_tracker_handle => {
            tracing::info!("Transaction tracker handle finished");
        }
        // _ = tokio::signal::ctrl_c() => {
        //     tracing::info!("Ctrl+C received, shutting down");
        // }
    }
}
