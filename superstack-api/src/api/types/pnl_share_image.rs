use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PnlShareImageResponse {
    pub image_path: String,
    pub image_url: String,
    pub image_type: String, // 'default' or 'custom'
    pub is_selected: bool,
    pub created_at: i64,
}

#[derive(<PERSON>lone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PnlShareImagesListResponse {
    pub images: Vec<PnlShareImageResponse>,
    pub selected_image_path: Option<String>,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SelectImageRequest {
    pub image_path: String,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct UploadImageResponse {
    pub image_path: String,
    pub image_url: String,
    pub message: String,
}

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeleteImageResponse {
    pub success: bool,
    pub message: String,
}

impl PnlShareImageResponse {
    pub fn from_db_model(image: crate::models::DbPnlShareImage, gcs_base_url: &str) -> Self {
        Self {
            image_path: image.image_path.clone(),
            image_url: format!("{}/{}", gcs_base_url, image.image_path),
            image_type: "custom".to_string(),
            is_selected: false, // Frontend handles selection
            created_at: image.created_at,
        }
    }
}
