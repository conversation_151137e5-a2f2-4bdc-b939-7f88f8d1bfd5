use anyhow::Result;
use serde::{Deserialize, Serialize};

use superstack_data::postgres::indexer::token_metadata::TokenMetadata;
use time::OffsetDateTime;

use super::{common::Chain, position::ApiTokenMetadata};
use crate::{models::*, utils::lamports_to_token};

/// Context for PnL calculations to ensure consistent data interpretation
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct PnlCalculationContext {
    /// Current SOL price in USD
    pub sol_price_usd: f64,
    /// Timestamp for the calculation
    pub calculation_timestamp: i64,
    /// Chain being calculated
    pub chain: i16,
}

/// Represents the financial breakdown of a position or history
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct FinancialBreakdown {
    /// Total cost in USD (includes both native token cost converted to USD and direct USD cost)
    pub total_cost_usd: f64,
    /// Total earnings in USD (includes both native token earnings converted to USD and direct USD
    /// earnings)
    pub total_earnings_usd: f64,
    /// Net PnL in USD (earnings - cost)
    pub pnl_usd: f64,
    /// PnL percentage based on cost
    pub pnl_percentage: f64,
}

impl FinancialBreakdown {
    /// Create a new financial breakdown from raw position data
    pub fn from_position_data(
        cost_native_amount: i64,
        cost_usd: f64,
        earnings_native_amount: i64,
        earnings_usd: f64,
        context: &PnlCalculationContext,
    ) -> Self {
        let native_cost_usd = (cost_native_amount as f64 / 1_000_000_000.0) * context.sol_price_usd;
        let native_earnings_usd =
            (earnings_native_amount as f64 / 1_000_000_000.0) * context.sol_price_usd;

        let total_cost_usd = native_cost_usd + cost_usd;
        let total_earnings_usd = native_earnings_usd + earnings_usd;
        let pnl_usd = total_earnings_usd - total_cost_usd;
        let pnl_percentage = if total_cost_usd > 0.0 { pnl_usd / total_cost_usd } else { 0.0 };

        Self { total_cost_usd, total_earnings_usd, pnl_usd, pnl_percentage }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Eq, PartialEq)]
pub enum PortfolioRange {
    #[serde(rename = "5m")]
    Min5,
    #[serde(rename = "1h")]
    Hour1,
    #[serde(rename = "6h")]
    Hour6,
    #[serde(rename = "24h")]
    Hour24,
    #[serde(rename = "3d")]
    Day3,
    #[serde(rename = "7d")]
    Day7,
    #[serde(rename = "30d")]
    Day30,
    #[serde(rename = "1y")]
    Year1,
    #[serde(rename = "All")]
    All,
}

impl PortfolioRange {
    pub fn get_start_time(&self, end_time: OffsetDateTime) -> i64 {
        let start = match self {
            PortfolioRange::Min5 => end_time.saturating_sub(time::Duration::minutes(5)),
            PortfolioRange::Hour1 => end_time.saturating_sub(time::Duration::hours(1)),
            PortfolioRange::Hour6 => end_time.saturating_sub(time::Duration::hours(6)),
            PortfolioRange::Hour24 => end_time.saturating_sub(time::Duration::hours(24)),
            PortfolioRange::Day3 => end_time.saturating_sub(time::Duration::days(3)),
            PortfolioRange::Day7 => end_time.saturating_sub(time::Duration::days(7)),
            PortfolioRange::Day30 => end_time.saturating_sub(time::Duration::days(30)),
            PortfolioRange::Year1 => end_time.saturating_sub(time::Duration::days(365)),
            PortfolioRange::All => OffsetDateTime::UNIX_EPOCH,
        };

        start.unix_timestamp()
    }

    /// Get optimal aggregation level based on time range
    pub fn get_optimal_aggregation(&self) -> Option<AggregationLevel> {
        match self {
            // Short ranges: return raw data
            PortfolioRange::Min5 | PortfolioRange::Hour1 => None,
            // Medium ranges: hourly aggregation
            PortfolioRange::Hour6 | PortfolioRange::Hour24 => Some(AggregationLevel::Hourly),
            // Long ranges: daily aggregation
            PortfolioRange::Day3 | PortfolioRange::Day7 => Some(AggregationLevel::Daily),
            // Very long ranges: weekly aggregation
            PortfolioRange::Day30 | PortfolioRange::Year1 | PortfolioRange::All => {
                Some(AggregationLevel::Weekly)
            }
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub enum OrderType {
    Limit,
    Market,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub enum TradeType {
    Market,
    Limit,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub enum OrderStatus {
    Filled,
    PartiallyFilled,
    Expired,
    Cancelled,
    Failed,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub enum Side {
    Buy,
    Sell,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct OpenOrder {
    pub order_id: String,
    pub wallet_address: String,
    pub token_mint: String,
    pub base_mint: String,
    pub timestamp: u64,
    pub pair_label: String,
    pub order_type: OrderType,
    pub side: Side,
    pub price: f64,
    pub token_amount: i64,
    pub token_ui_amount: f64,
    pub base_amount: i64,
    pub base_ui_amount: f64,
    pub filled: f64,
    pub token_metadata: ApiTokenMetadata,
}

impl OpenOrder {
    pub async fn construct(
        order: DbOrder,
        pair_label: String,
        sol_price: f64,
        token_metadata: superstack_data::postgres::indexer::token_metadata::TokenMetadata,
    ) -> Result<Self> {
        if order.is_cancelled || order.is_completed {
            return Err(anyhow::anyhow!("Order is cancelled or completed"));
        }

        let side = if order.trade_type.is_buy() { Side::Buy } else { Side::Sell };

        let token_ui_amount = lamports_to_token(order.token_amount as _, order.token_decimals as _);
        let base_ui_amount = lamports_to_token(order.base_amount as _, order.base_decimals as _);
        let price = (base_ui_amount / token_ui_amount) * sol_price;
        let filled =
            (order.base_amount - order.remaining_base_amount) as f64 / order.base_amount as f64;

        let token_metadata = ApiTokenMetadata {
            name: token_metadata.name,
            symbol: token_metadata.symbol,
            decimals: token_metadata.decimals,
            image_url: token_metadata.image, // todo: translate url with path
            chain: Chain::Solana,
        };

        let open_order = OpenOrder {
            order_id: order.order_id,
            wallet_address: order.wallet_address,
            token_mint: order.token_mint,
            base_mint: order.base_mint,
            timestamp: order.timestamp as _,
            pair_label,
            order_type: OrderType::Limit,
            side,
            price,
            token_amount: order.token_amount,
            token_ui_amount,
            base_amount: order.base_amount,
            base_ui_amount,
            filled,
            token_metadata,
        };

        Ok(open_order)
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct OrderHistory {
    pub order_id: String,
    pub wallet_address: String,
    pub token_mint: String,
    pub base_mint: String,
    pub timestamp: u64,
    pub pair_label: String,
    pub order_type: OrderType,
    pub side: Side,
    pub price: f64,
    pub token_amount: i64,
    pub token_ui_amount: f64,
    pub base_amount: i64,
    pub base_ui_amount: f64,
    pub filled: f64,
    pub status: OrderStatus,
    pub token_metadata: ApiTokenMetadata,
}

impl OrderHistory {
    pub async fn construct(
        order: DbOrder,
        pair_label: String,
        sol_price: f64,
        token_metadata: superstack_data::postgres::indexer::token_metadata::TokenMetadata,
    ) -> Result<Self> {
        let status = if order.is_cancelled {
            OrderStatus::Cancelled
        } else if order.is_completed {
            OrderStatus::Filled
        } else {
            return Err(anyhow::anyhow!("Order is not cancelled or completed"));
        };

        let side = if order.trade_type.is_buy() { Side::Buy } else { Side::Sell };

        let token_ui_amount = lamports_to_token(order.token_amount as _, order.token_decimals as _);
        let base_ui_amount = lamports_to_token(order.base_amount as _, order.base_decimals as _);
        let price = (base_ui_amount / token_ui_amount) * sol_price;
        let filled =
            (order.base_amount - order.remaining_base_amount) as f64 / order.base_amount as f64;

        let token_metadata = ApiTokenMetadata {
            name: token_metadata.name,
            symbol: token_metadata.symbol,
            decimals: token_metadata.decimals,
            image_url: token_metadata.image, // todo: translate url with path
            chain: Chain::Solana,
        };

        let order_history = OrderHistory {
            order_id: order.order_id,
            wallet_address: order.wallet_address,
            token_mint: order.token_mint,
            base_mint: order.base_mint,
            timestamp: order.timestamp as _,
            pair_label,
            order_type: OrderType::Limit,
            side,
            price,
            token_amount: order.token_amount,
            token_ui_amount,
            base_amount: order.base_amount,
            base_ui_amount,
            filled,
            status,
            token_metadata,
        };

        Ok(order_history)
    }

    pub async fn construct_from_trade(
        trade: crate::models::trade::DbTrade,
        order_info: Option<crate::models::order::DbOrder>,
        pair_label: String,
        sol_price: f64,
        token_metadata: superstack_data::postgres::indexer::token_metadata::TokenMetadata,
    ) -> Result<Self> {
        let side = if trade.trade_type.is_buy() { Side::Buy } else { Side::Sell };

        let token_ui_amount = lamports_to_token(trade.token_amount as _, trade.token_decimals as _);
        let base_ui_amount = lamports_to_token(trade.base_amount as _, trade.base_decimals as _);
        let price = (base_ui_amount / token_ui_amount) * sol_price;

        // Determine order type and status based on whether there's order info
        let (order_type, status, order_id, filled) = if let Some(order) = order_info {
            // This is a limit order with order record
            let status = if order.is_cancelled {
                OrderStatus::Cancelled
            } else if order.is_completed {
                OrderStatus::Filled
            } else {
                OrderStatus::PartiallyFilled
            };
            let filled =
                (order.base_amount - order.remaining_base_amount) as f64 / order.base_amount as f64;
            (OrderType::Limit, status, order.order_id, filled)
        } else {
            // This is a market order (no order record, executed immediately)
            (OrderType::Market, OrderStatus::Filled, trade.tx_sig.clone(), 1.0)
        };

        let token_metadata = ApiTokenMetadata {
            name: token_metadata.name,
            symbol: token_metadata.symbol,
            decimals: token_metadata.decimals,
            image_url: token_metadata.image, // todo: translate url with path
            chain: Chain::Solana,
        };

        let order_history = OrderHistory {
            order_id,
            wallet_address: trade.wallet_address,
            token_mint: trade.token_mint,
            base_mint: trade.base_mint,
            timestamp: trade.timestamp as _,
            pair_label,
            order_type,
            side,
            price,
            token_amount: trade.token_amount,
            token_ui_amount,
            base_amount: trade.base_amount,
            base_ui_amount,
            filled,
            status,
            token_metadata,
        };

        Ok(order_history)
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct TradeHistory {
    pub wallet_address: String,
    pub token_mint: String,
    pub base_mint: String,
    pub timestamp: u64,
    pub pair_label: String,
    pub trade_type: TradeType,
    pub side: Side,
    pub price: f64,
    pub token_amount: i64,
    pub token_ui_amount: f64,
    pub base_amount: i64,
    pub base_ui_amount: f64,
    pub fee_ui_amount: f64,
    pub token_metadata: ApiTokenMetadata,
}

impl TradeHistory {
    pub async fn construct(
        trade: DbTrade,
        pair_label: String,
        sol_price: f64,
        token_metadata: TokenMetadata,
    ) -> Result<Self> {
        let trade_type =
            if trade.trade_type.is_market_order() { TradeType::Market } else { TradeType::Limit };

        let side = if trade.trade_type.is_buy() { Side::Buy } else { Side::Sell };

        let token_ui_amount = lamports_to_token(trade.token_amount as _, trade.token_decimals as _);
        let base_ui_amount = lamports_to_token(trade.base_amount as _, trade.base_decimals as _);
        let price = (base_ui_amount / token_ui_amount) * sol_price;
        let fee_ui_amount = lamports_to_token(trade.fee as _, trade.base_decimals as _);

        let token_metadata = ApiTokenMetadata {
            name: token_metadata.name,
            symbol: token_metadata.symbol,
            decimals: token_metadata.decimals,
            image_url: token_metadata.image, // todo: translate url with path
            chain: Chain::Solana,
        };

        let trade_history = TradeHistory {
            wallet_address: trade.wallet_address,
            token_mint: trade.token_mint,
            base_mint: trade.base_mint,
            timestamp: trade.timestamp as _,
            pair_label,
            trade_type,
            side,
            price,
            token_amount: trade.token_amount,
            token_ui_amount,
            base_amount: trade.base_amount,
            base_ui_amount,
            fee_ui_amount,
            token_metadata,
        };

        Ok(trade_history)
    }
}

/// Enhanced PnL statistics with clear semantic definitions
#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PnlStats {
    /// Current total account value in USD
    pub account_value: f64,

    /// Realized PnL in USD (profit/loss from closed positions)
    pub realized_pnl: f64,
    /// Realized PnL percentage based on total invested amount
    pub realized_pnl_percentage: f64,

    /// Unrealized PnL in USD (profit/loss from current positions)
    pub unrealized_pnl: f64,
    /// Unrealized PnL percentage based on current holdings cost
    pub unrealized_pnl_percentage: f64,

    /// Total revenue in USD (total earnings from all sales)
    pub revenue: f64,
    /// Total spent in USD (total cost of all purchases) - should be negative
    pub spent: f64,

    /// Total amount bought in USD (sum of all purchase costs)
    pub total_bought: f64,
    /// Total amount sold in USD (sum of all sale proceeds)
    pub total_sold: f64,

    /// Date of last transaction
    pub last_txn_date: String,

    // Chart data
    pub account_values: Vec<AccountValuePoint>,
    pub pnl_views: Vec<PnlView>,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct PnlView {
    pub timestamp: i64,
    pub pnl: f64,
    pub pnl_percentage: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct ValueView {
    pub timestamp: i64,
    pub value: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct Price {
    pub token_mint: String,
    pub usd_price: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PnlShare {
    pub token_mint: String,
    pub name: String,
    pub symbol: String,
    pub image_url: Option<String>,
    pub pnl_usd: f64,
    pub pnl_percentage: f64,
    pub background_image_url: Option<String>,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct AccountValuePoint {
    pub timestamp: i64,
    pub value: f64,
    pub value_percentage: f64,
}

#[derive(Debug, Clone)]
pub enum AggregationLevel {
    Hourly,
    Daily,
    Weekly,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_data_format() {
        crate::utils::setup_tracing();

        let date_time = time::OffsetDateTime::from_unix_timestamp(**********).unwrap();
        let date = date_time.date();

        assert_eq!(date.to_string(), "2024-04-16");
    }

    #[test]
    fn test_account_value_point_serialization() {
        let point =
            AccountValuePoint { timestamp: **********, value: 1000.0, value_percentage: 0.05 };
        let json = serde_json::to_string(&point).unwrap();
        assert!(json.contains("**********"));
        assert!(json.contains("1000"));
        assert!(json.contains("valuePercentage"));
        assert!(json.contains("0.05"));
    }

    #[test]
    fn test_token_metadata_serialization() {
        let token_metadata = ApiTokenMetadata {
            name: "Jupiter".to_string(),
            symbol: "JUP".to_string(),
            decimals: 6,
            image_url: Some("https://static.jup.ag/jup/icon.png".to_string()),
            chain: Chain::Solana,
        };

        let json = serde_json::to_string(&token_metadata).unwrap();
        assert!(json.contains("Jupiter"));
        assert!(json.contains("JUP"));
        assert!(json.contains("6"));
        assert!(json.contains("https://static.jup.ag/jup/icon.png"));
        assert!(json.contains("Solana"));
    }

    #[test]
    fn test_trade_history_with_token_metadata() {
        let token_metadata = ApiTokenMetadata {
            name: "Jupiter".to_string(),
            symbol: "JUP".to_string(),
            decimals: 6,
            image_url: Some("https://static.jup.ag/jup/icon.png".to_string()),
            chain: Chain::Solana,
        };

        let trade_history = TradeHistory {
            wallet_address: "test_wallet".to_string(),
            token_mint: "test_mint".to_string(),
            base_mint: "base_mint".to_string(),
            timestamp: **********,
            pair_label: "JUP/SOL".to_string(),
            trade_type: TradeType::Market,
            side: Side::Buy,
            price: 1.5,
            token_amount: 1000000,
            token_ui_amount: 1.0,
            base_amount: 1500000,
            base_ui_amount: 1.5,
            fee_ui_amount: 0.001,
            token_metadata,
        };

        let json = serde_json::to_string(&trade_history).unwrap();
        assert!(json.contains("token_metadata"));
        assert!(json.contains("Jupiter"));
        assert!(json.contains("JUP"));
    }
}
