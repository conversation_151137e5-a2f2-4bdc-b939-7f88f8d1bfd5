use serde::{Deserialize, Serialize};
use superstack_data::postgres::{enhanced_candle::EnhancedCandle, CandleInterval, Chain};

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct CandleResponse {
    pub chain: Chain,
    pub pool_address: String,
    pub open_timestamp_seconds: i64,
    pub close_timestamp_seconds: i64,
    pub interval: CandleInterval,
    pub native_token_usd_price: f64,
    pub usd_open_price: f64,
    pub usd_close_price: f64,
    pub usd_high_price: f64,
    pub usd_low_price: f64,
    pub usd_open_market_cap: f64,
    pub usd_close_market_cap: f64,
    pub usd_high_market_cap: f64,
    pub usd_low_market_cap: f64,
    pub usd_volume: f64,
    pub txns: u64,
}

impl From<EnhancedCandle> for CandleResponse {
    fn from(c: EnhancedCandle) -> Self {
        Self {
            chain: c.chain,
            pool_address: c.pool_address,
            open_timestamp_seconds: c.open_timestamp_seconds,
            close_timestamp_seconds: c.close_timestamp_seconds,
            interval: c.interval,
            native_token_usd_price: c.native_token_usd_price,
            usd_open_price: c.usd_open_price,
            usd_close_price: c.usd_close_price,
            usd_high_price: c.usd_high_price,
            usd_low_price: c.usd_low_price,
            usd_open_market_cap: c.usd_open_market_cap,
            usd_close_market_cap: c.usd_close_market_cap,
            usd_high_market_cap: c.usd_high_market_cap,
            usd_low_market_cap: c.usd_low_market_cap,
            usd_volume: c.usd_volume,
            txns: c.txns,
        }
    }
}
