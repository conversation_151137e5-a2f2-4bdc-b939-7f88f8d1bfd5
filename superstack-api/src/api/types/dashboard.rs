use serde::Serialize;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
#[serde(rename_all = "camelCase")]
pub struct BindedUser {
    pub wallet_address: String,
    pub code: String,
    pub created_at: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct InvideCodeStats {
    pub used_codes: Vec<BindedUser>,
    pub unused_codes: Vec<String>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct UserFundsStats {
    pub users: Vec<SingleUserFunds>,
    pub total_account_value: f64,
}

#[derive(Debug, <PERSON>lone, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SingleUserFunds {
    pub user: String,
    pub funds: Vec<SingleWalletFunds>,
}

#[derive(Debug, <PERSON>lone, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SingleWalletFunds {
    pub wallet_address: String,
    pub account_values: f64,
    pub funds: Vec<FundInfo>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct FundInfo {
    pub mint: String,
    pub decimals: u8,
    pub symbol: String,
    pub name: String,
    pub image: Option<String>,
    pub ui_amount: f64,
    pub usd_value: f64,
}

#[derive(Debug, Clone, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TransactionStats {
    pub total_success_transactions: u64,
    pub total_pending_transactions: u64,
    pub total_failed_transactions: u64,
    pub total_expired_transactions: u64,
    pub success_transactions_24h: u64,
    pub pending_transactions_24h: u64,
    pub failed_transactions_24h: u64,
    pub expired_transactions_24h: u64,
}
