use serde::{Deserialize, Serialize};
use superstack_data::postgres::aggregator::PoolStatistic;

use super::aggregator::CandleResponse;

#[derive(Clone, Debug, Deserialize, Serialize)]
pub enum MakerType {
    Plankton,
    Fish,
    Shrimp,
    Dolphin,
    Whale,
}

impl MakerType {
    pub fn new(ui_amount: f64) -> Self {
        match ui_amount {
            ui_amount if ui_amount < 10.0 => Self::Plankton,
            ui_amount if ui_amount < 250.0 => Self::Fish,
            ui_amount if ui_amount < 1000.0 => Self::Shrimp,
            ui_amount if ui_amount < 10000.0 => Self::Dolphin,
            _ => Self::Whale,
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct MarketTokenResponse {
    pub token_mint: String,
    pub name: String,
    pub symbol: String,
    pub decimals: u8,
    pub image_url: Option<String>,
    pub chain: String,
    pub dex: String,                  // Keep for backward compatibility (create_dex)
    pub pool_address: Option<String>, // Default pool address for trading
    pub pool_dex: Option<String>,     // Default pool's actual DEX for trading
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub website: Option<String>,
    pub price_usd: f64,
    pub market_cap_usd: f64,
    pub fdv_usd: f64,
    pub volume_usd: f64,
    pub liquidity_usd: f64,
    pub price_change: f64,
    pub candles: Vec<CandleResponse>,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TrenchTokenResponse {
    pub token_mint: String,
    pub name: String,
    pub symbol: String,
    pub image_url: Option<String>,
    pub chain: String,
    pub dex: Option<String>,
    pub dex_url: Option<String>,
    pub bonding_curve_progress: Option<f64>,
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub website: Option<String>,
    pub market_cap_usd: f64,
    pub volume_usd: f64,
    pub top10_holding: f64,
    pub dev_holding: f64,
    pub dev_sold: f64,
    pub sniper_holding: f64,
    pub holders: u64,
    pub snipers: u64,
    pub creation_time: u64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct DetailedTokenResponse {
    pub token_mint: String,
    pub name: String,
    pub symbol: String,
    pub description: Option<String>,
    pub image_url: Option<String>,

    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub website: Option<String>,
    pub launch_dex: Option<String>,
    pub launch_dex_url: Option<String>,

    pub decimals: u8,
    pub total_supply: f64,
    pub circulating_supply: f64,

    pub chain: String,

    pub is_trench_token: bool,

    pub default_pool_index: u64,
    pub current_pool_index: u64,
    pub is_user_selected: bool,
    pub pools: Vec<PoolInfo>,

    pub current_pool_statistic: PoolStatisticResponse,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PoolInfo {
    pub address: String,
    pub pair_label: String,
    pub token1_mint: String,
    pub token2_mint: String,
    pub creation_time: i64,
    pub dex: String,
    pub pool_type: Option<String>,
    pub price_usd: f64,
    pub price_native: f64,
    pub price_change: f64,
    pub market_cap_usd: f64,
    pub fdv_usd: f64,
    pub volume_usd: f64,
    pub liquidity_usd: f64,
    pub is_default: bool,
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct PoolStatisticResponse {
    pub data5m: TimeStatistic,
    pub data1h: TimeStatistic,
    pub data6h: TimeStatistic,
    pub data24h: TimeStatistic,
}

impl PoolStatisticResponse {
    pub fn from_pool_statistics(
        statistic: &PoolStatistic,
        _use_native_price: bool,
        _sol_price: f64,
    ) -> Self {
        Self {
            data5m: TimeStatistic {
                price_change: statistic.price_change_5m,
                txns: statistic.txns_5m as i64,
                buys: statistic.buy_txns_5m as i64,
                sells: statistic.sell_txns_5m as i64,
                volume_usd: statistic.usd_volume_5m,
                buy_volume_usd: statistic.usd_buy_volume_5m,
                sell_volume_usd: statistic.usd_sell_volume_5m,
                makers: statistic.makers_5m as i64,
                buyers: statistic.buyers_5m as i64,
                sellers: statistic.sellers_5m as i64,
            },
            data1h: TimeStatistic {
                price_change: statistic.price_change_1h,
                txns: statistic.txns_1h as i64,
                buys: statistic.buy_txns_1h as i64,
                sells: statistic.sell_txns_1h as i64,
                volume_usd: statistic.usd_volume_1h,
                buy_volume_usd: statistic.usd_buy_volume_1h,
                sell_volume_usd: statistic.usd_sell_volume_1h,
                makers: statistic.makers_1h as i64,
                buyers: statistic.buyers_1h as i64,
                sellers: statistic.sellers_1h as i64,
            },
            data6h: TimeStatistic {
                price_change: statistic.price_change_6h,
                txns: statistic.txns_6h as i64,
                buys: statistic.buy_txns_6h as i64,
                sells: statistic.sell_txns_6h as i64,
                volume_usd: statistic.usd_volume_6h,
                buy_volume_usd: statistic.usd_buy_volume_6h,
                sell_volume_usd: statistic.usd_sell_volume_6h,
                makers: statistic.makers_6h as i64,
                buyers: statistic.buyers_6h as i64,
                sellers: statistic.sellers_6h as i64,
            },
            data24h: TimeStatistic {
                price_change: statistic.price_change_24h,
                txns: statistic.txns_24h as i64,
                buys: statistic.buy_txns_24h as i64,
                sells: statistic.sell_txns_24h as i64,
                volume_usd: statistic.usd_volume_24h,
                buy_volume_usd: statistic.usd_buy_volume_24h,
                sell_volume_usd: statistic.usd_sell_volume_24h,
                makers: statistic.makers_24h as i64,
                buyers: statistic.buyers_24h as i64,
                sellers: statistic.sellers_24h as i64,
            },
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct TimeStatistic {
    pub price_change: f64,
    pub txns: i64,
    pub buys: i64,
    pub sells: i64,
    pub volume_usd: f64,
    pub buy_volume_usd: f64,
    pub sell_volume_usd: f64,
    pub makers: i64,
    pub buyers: i64,
    pub sellers: i64,
}

impl From<PoolStatistic> for TimeStatistic {
    fn from(statistic: PoolStatistic) -> Self {
        Self {
            price_change: statistic.price_change_24h, // Default to 24h data
            txns: statistic.txns_24h as i64,
            buys: statistic.buy_txns_24h as i64,
            sells: statistic.sell_txns_24h as i64,
            volume_usd: statistic.usd_volume_24h,
            buy_volume_usd: statistic.usd_buy_volume_24h,
            sell_volume_usd: statistic.usd_sell_volume_24h,
            makers: statistic.makers_24h as i64,
            buyers: statistic.buyers_24h as i64,
            sellers: statistic.sellers_24h as i64,
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenMemeStatisticResponse {
    pub token_mint: String,

    pub lp_burned: f64,
    pub is_mintable: bool,
    pub is_freezable: bool,
    pub is_mutable: bool,

    pub holders: i64,
    pub insiders: i64,
    pub snipers: i64,
    pub bot: i64,

    pub dev_holding: f64,
    pub top10_holding: f64,
    pub insider_holding: f64,
    pub sniper_holding: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct Holder {
    pub maker: String,
    pub maker_type: MakerType,
    pub base_balance: f64,
    pub bought_ui_amount: f64,
    pub bought_usd: f64,
    pub bought_tx_count: u64,
    pub sold_ui_amount: f64,
    pub sold_usd: f64,
    pub sold_tx_count: u64,
    pub remaining_ui_amount: f64,
    pub remaining_usd: f64,
    pub remaining_percentage: f64,
    pub pnl_usd: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct Holders {
    pub holders: Vec<Holder>,
    pub total_holders: u64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct Trader {
    pub wallet_address: String,
    pub sol_balance: f64,
    pub bought_ui_amount: f64,
    pub bought_usd: f64,
    pub bought_tx_count: u64,
    pub sold_ui_amount: f64,
    pub sold_usd: f64,
    pub sold_tx_count: u64,
    pub remaining_ui_amount: f64,
    pub remaining_usd: f64,
    pub pnl_usd: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct Trade {
    pub tx_sig: String,
    pub maker: String,
    pub maker_type: MakerType,
    pub is_buy: bool,
    pub token_ui_amount: f64,
    pub base_ui_amount: f64,
    pub usd: f64,
    pub sol_amount: f64,
    pub base_token_symbol: String,
    pub price_usd: f64,
    pub price_native: f64,
    pub market_cap_usd: f64,
    pub timestamp: u64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenDetails {
    pub mint: String,
    pub name: String,
    pub symbol: String,
    pub market_cap_usd: f64,
    pub fdv_usd: f64,
    pub circulating_supply: f64,
    pub total_supply: f64,
}
