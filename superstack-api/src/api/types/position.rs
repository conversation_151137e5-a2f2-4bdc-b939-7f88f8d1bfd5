use anyhow::Result;
use serde::{Deserialize, Serialize};
use solana_sdk::native_token::lamports_to_sol;

use super::Chain;
use crate::{constant::SOL_MINT, models::*, utils::lamports_to_token};

#[derive(<PERSON>lone, Debug, Deserialize, Serialize)]
pub struct ApiTokenMetadata {
    pub name: String,
    pub symbol: String,
    pub decimals: u8,
    pub image_url: Option<String>,
    pub chain: Chain,
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
pub struct TokenPositionData {
    pub total_invested_usd: f64,
    pub total_invested_amount: i64,
    pub total_invested_ui_amount: f64,
    pub total_sold_usd: f64,
    pub total_sold_amount: i64,
    pub total_sold_ui_amount: f64,
    pub total_remaining_usd: f64,
    pub total_remaining_amount: i64,
    pub total_remaining_ui_amount: f64,
    pub total_pnl_usd: f64,
    pub total_pnl_percentage: f64,
}

#[derive(<PERSON><PERSON>, Debug, Deserialize, Serialize)]
pub struct Positions {
    pub token_position_data: Option<TokenPositionData>,
    pub positions: Vec<Position>,
}

#[derive(<PERSON><PERSON>, <PERSON>bug, Deserialize, Serialize)]
pub struct Position {
    pub wallet_address: String,
    pub token_mint: String,
    pub token_metadata: ApiTokenMetadata,

    // bought tokens
    pub bought_amount: i64,
    pub bought_ui_amount: f64,
    // sold tokens
    pub sold_amount: i64,
    pub sold_ui_amount: f64,

    pub cost_usd: f64,
    pub earnings_usd: f64,
    pub remaining_usd: f64,

    // Total value of current holdings calculated using USD price at time of each purchase
    pub current_holdings_cost_usd: f64,

    // Sell initials - amount needed to sell to recover initial cost
    pub sell_initials_ui_amount: f64, // UI amount needed to sell to recover initial cost
    pub sell_initials_amount: i64,    /* Raw amount (lamports) needed to sell to recover initial
                                       * cost */
    pub sell_initial_usd: f64, // USD value that would be recovered

    pub pnl_usd: f64,
    pub pnl_percentage: f64,

    pub buy_operations: Vec<DbPositionChange>,
    pub sell_operations: Vec<DbPositionChange>,

    pub open_time: u64,
}

impl Position {
    pub async fn construct(state: &StorageState, position: DbPosition) -> Result<Self> {
        // Determine chain from position
        let chain = match position.chain {
            0 => Chain::Solana,
            1 => Chain::Hypercore,
            2 => Chain::HyperEvm,
            _ => Chain::Solana, // Default fallback
        };

        let sol_usd_price = crate::utils::get_sol_price().await;

        // Try to find token across all chains
        let mut token_found = None;
        for search_chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
            // todo: stat from redis
            if let Ok(Some(token)) =
                state.indexer_data_provider.get_token(search_chain, &position.token_mint).await
            {
                token_found = Some(token);
                break;
            }
        }

        let token_statistic = match token_found {
            Some(token) => token,
            None => {
                tracing::warn!(
                    "Token not found for position: wallet={}, token={}, chain={}",
                    position.wallet_address,
                    position.token_mint,
                    position.chain
                );
                return Err(anyhow::anyhow!("Token not found"));
            }
        };

        let token_metadata = ApiTokenMetadata {
            name: token_statistic.name,
            symbol: token_statistic.symbol,
            decimals: token_statistic.decimals as _,
            image_url: token_statistic.image, // todo: translate url with path
            chain,
        };

        let token_usd_price = token_statistic.usd_price;

        let remaining_amount = position.bought_amount - position.sold_amount;

        // Use appropriate unit conversion based on chain
        let remaining_ui_amount = match chain {
            Chain::Solana => lamports_to_token(remaining_amount as _, token_metadata.decimals),
            Chain::HyperEvm | Chain::Hypercore => {
                remaining_amount as f64 / 10_f64.powi(token_metadata.decimals as i32)
            }
        };

        let bought_ui_amount = match chain {
            Chain::Solana => {
                lamports_to_token(position.bought_amount as _, token_metadata.decimals)
            }
            Chain::HyperEvm | Chain::Hypercore => {
                position.bought_amount as f64 / 10_f64.powi(token_metadata.decimals as i32)
            }
        };

        let sold_ui_amount = match chain {
            Chain::Solana => lamports_to_token(position.sold_amount as _, token_metadata.decimals),
            Chain::HyperEvm | Chain::Hypercore => {
                position.sold_amount as f64 / 10_f64.powi(token_metadata.decimals as i32)
            }
        };

        // Calculate costs based on chain
        let cost_usd = match chain {
            Chain::Solana => {
                lamports_to_sol(position.cost_native_amount as _) * sol_usd_price +
                    position.cost_usd
            }
            Chain::HyperEvm | Chain::Hypercore => {
                // For EVM chains, native_amount is already in proper decimals
                let native_ui_amount = position.cost_native_amount as f64 /
                    10_f64.powi(position.native_decimals as i32);
                native_ui_amount * sol_usd_price + position.cost_usd // Assuming native token price
                                                                     // similar to SOL for now
            }
        };

        let earnings_usd = match chain {
            Chain::Solana => {
                lamports_to_sol(position.earnings_native_amount as _) * sol_usd_price +
                    position.earnings_usd
            }
            Chain::HyperEvm | Chain::Hypercore => {
                let native_ui_amount = position.earnings_native_amount as f64 /
                    10_f64.powi(position.native_decimals as i32);
                native_ui_amount * sol_usd_price + position.earnings_usd
            }
        };

        // Get active limit sell orders for this wallet and token to include their expected value
        let mut pending_sell_orders_usd = 0.0;
        if let Ok(active_orders) = state
            .get_active_orders_for_wallet_and_token(&position.wallet_address, &position.token_mint)
            .await
        {
            for order in active_orders {
                // Only consider sell orders (where we're selling the token)
                if order.trade_type.is_sell() && !order.is_cancelled && !order.is_completed {
                    // Calculate expected value from remaining order amount
                    let _remaining_token_ui_amount = match chain {
                        Chain::Solana => lamports_to_token(
                            order.remaining_token_amount as u64,
                            token_metadata.decimals,
                        ),
                        Chain::HyperEvm | Chain::Hypercore => {
                            order.remaining_token_amount as f64 /
                                10_f64.powi(token_metadata.decimals as i32)
                        }
                    };

                    let remaining_base_ui_amount = match chain {
                        Chain::Solana => lamports_to_token(
                            order.remaining_base_amount as u64,
                            order.base_decimals as u8,
                        ),
                        Chain::HyperEvm | Chain::Hypercore => {
                            order.remaining_base_amount as f64 /
                                10_f64.powi(order.base_decimals as i32)
                        }
                    };

                    // Calculate expected USD value from the order
                    let order_expected_usd = if order.base_mint == SOL_MINT {
                        remaining_base_ui_amount * sol_usd_price
                    } else {
                        // Assume other base tokens (like USDC) are already in USD equivalent
                        remaining_base_ui_amount
                    };

                    pending_sell_orders_usd += order_expected_usd;
                }
            }
        }

        let remaining_usd = remaining_ui_amount * token_usd_price + pending_sell_orders_usd;
        let pnl_usd = remaining_usd + earnings_usd - cost_usd;
        let pnl_percentage = if cost_usd > 0.0 { pnl_usd / cost_usd } else { 0.0 };

        let buy_operations: Vec<DbPositionChange> =
            position.operations.iter().filter(|x| x.is_buy).cloned().collect();
        let sell_operations = position.operations.iter().filter(|x| !x.is_buy).cloned().collect();

        // Calculate current holdings cost based on purchase prices at time of each buy operation
        let mut current_holdings_cost_usd = 0.0;
        let mut remaining_tokens = remaining_amount;

        // Collect unique timestamps for batch price fetching
        let mut unique_timestamps: std::collections::HashSet<i64> =
            std::collections::HashSet::new();
        for buy_op in &buy_operations {
            unique_timestamps.insert(buy_op.timestamp);
        }

        // Batch fetch historical SOL prices to improve performance
        let mut historical_prices = std::collections::HashMap::new();
        for timestamp in unique_timestamps {
            // TODO: Implement SOL price lookup using existing methods
            let price = match Ok::<f64, anyhow::Error>(100.0f64) {
                // Temporary default price
                Ok(price) if price > 0.0 => price,
                _ => sol_usd_price, // Fallback to current SOL price
            };
            historical_prices.insert(timestamp, price);
        }

        // Process buy operations in reverse order (LIFO - Last In First Out)
        // This assumes we sell the most recently bought tokens first
        for buy_op in buy_operations.iter().rev() {
            if remaining_tokens <= 0 {
                break;
            }

            let tokens_from_this_buy = std::cmp::min(remaining_tokens, buy_op.token_amount);

            // Calculate USD cost for this portion of tokens
            let base_ui_amount =
                lamports_to_token(buy_op.base_amount as _, buy_op.base_decimals as _);
            let token_ui_amount =
                lamports_to_token(tokens_from_this_buy as _, token_metadata.decimals);
            let total_buy_ui_amount =
                lamports_to_token(buy_op.token_amount as _, token_metadata.decimals);

            // Calculate proportional cost for the tokens we're keeping
            let cost_for_this_portion = if total_buy_ui_amount > 0.0 {
                let proportion = token_ui_amount / total_buy_ui_amount;

                // Handle different base mints properly
                let base_cost_usd = if buy_op.base_mint == SOL_MINT {
                    // For SOL, use historical SOL price
                    let historical_sol_price =
                        historical_prices.get(&buy_op.timestamp).unwrap_or(&sol_usd_price);
                    base_ui_amount * historical_sol_price
                } else {
                    // For other tokens (like USDC), assume the base_amount is already in USD
                    // equivalent This is a simplification - in a more robust
                    // system, fetch historical prices for other tokens too
                    base_ui_amount
                };

                base_cost_usd * proportion
            } else {
                0.0
            };

            current_holdings_cost_usd += cost_for_this_portion;
            remaining_tokens -= tokens_from_this_buy;
        }

        // Calculate sell_initials_ui_amount, sell_initials_amount, and sell_initial_usd
        let sell_initials_ui_amount =
            if token_usd_price > 0.0 { current_holdings_cost_usd / token_usd_price } else { 0.0 };

        // Convert UI amount to lamports
        let sell_initials_amount =
            (sell_initials_ui_amount * 10_f64.powi(token_metadata.decimals as i32)) as i64;

        // The USD value would be exactly the historical cost
        let sell_initial_usd = current_holdings_cost_usd;

        Ok(Position {
            wallet_address: position.wallet_address,
            token_mint: position.token_mint,
            token_metadata,
            bought_amount: position.bought_amount as _,
            bought_ui_amount,
            sold_amount: position.sold_amount as _,
            sold_ui_amount,
            cost_usd,
            earnings_usd,
            remaining_usd,
            current_holdings_cost_usd,
            sell_initials_ui_amount,
            sell_initials_amount,
            sell_initial_usd,
            pnl_usd,
            pnl_percentage,
            buy_operations,
            sell_operations,
            open_time: position.open_time as _,
        })
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct PositionHistory {
    pub wallet_address: String,
    pub token_mint: String,
    pub token_metadata: ApiTokenMetadata,

    // bought tokens
    pub bought_amount: i64,
    pub bought_ui_amount: f64,
    // sold tokens
    pub sold_amount: i64,
    pub sold_ui_amount: f64,

    pub cost_usd: f64,
    pub earnings_usd: f64,

    pub pnl_usd: f64,
    pub pnl_percentage: f64,

    pub buy_operations: Vec<DbPositionChange>,
    pub sell_operations: Vec<DbPositionChange>,

    pub open_time: u64,
    pub close_time: u64,
}

impl PositionHistory {
    pub async fn construct(state: &StorageState, history: DbHistory) -> Result<Self> {
        let token_metadata = state
            .indexer_db
            .get_token_metadata(Chain::Solana, &history.token_mint)
            .await?
            .ok_or(anyhow::anyhow!("Token not found"))?;

        let token_metadata = ApiTokenMetadata {
            name: token_metadata.name,
            symbol: token_metadata.symbol,
            decimals: token_metadata.decimals as _,
            image_url: token_metadata.image, // todo: translate url with path
            chain: Chain::Solana,
        };

        let bought_ui_amount =
            lamports_to_token(history.bought_amount as _, token_metadata.decimals);
        let sold_ui_amount = lamports_to_token(history.sold_amount as _, token_metadata.decimals);

        let buy_operations = history.operations.iter().filter(|x| x.is_buy).cloned().collect();
        let sell_operations = history.operations.iter().filter(|x| !x.is_buy).cloned().collect();

        let position_history = PositionHistory {
            wallet_address: history.wallet_address,
            token_mint: history.token_mint,
            token_metadata,
            bought_amount: history.bought_amount as _,
            bought_ui_amount,
            sold_amount: history.sold_amount as _,
            sold_ui_amount,
            cost_usd: history.cost_usd,
            earnings_usd: history.earnings_usd,
            pnl_usd: history.pnl_usd,
            pnl_percentage: history.pnl_percentage,
            buy_operations,
            sell_operations,
            open_time: history.open_time as _,
            close_time: history.close_time as _,
        };

        Ok(position_history)
    }
}
