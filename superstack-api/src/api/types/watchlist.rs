use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Deserialize, Serial<PERSON>)]
pub struct Watchlists {
    pub tokens: Vec<WatchedToken>,
    pub pools: Vec<WatchedPool>,
    pub perps: Vec<WatchedPerp>,
}

#[derive(<PERSON>lone, Debug, Deserialize, Serialize)]
pub struct WatchedToken {
    pub add_time: i64,
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub image_url: Option<String>,
    pub chain: String,
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub website: Option<String>,
    pub price: f64,
    pub price_change_24h: f64,
    pub volume_24h: f64,
    pub market_cap: f64,
    pub liquidity: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct WatchedPool {
    pub add_time: i64,
    pub token_mint: String,
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub image_url: Option<String>,
    pub chain: String,
    pub base_symbol: String,
    pub dex: String,
    pub pool_type: Option<String>,
    pub price: f64,
    pub price_change_24h: f64,
    pub volume_24h: f64,
    pub market_cap: f64,
    pub liquidity: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct WatchedPerp {
    pub add_time: i64,
    pub coin: String,
}
