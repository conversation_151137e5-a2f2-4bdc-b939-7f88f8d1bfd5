use serde::{Deserialize, Serialize};

/// Request parameters for user trading indicators
#[derive(Debug, Deserialize)]
pub struct UserIndicatorsParams {
    /// User wallet address
    pub wallet_address: String,
    /// Token mint address
    pub token_mint: String,
    /// Optional pool address for specific pool filtering
    pub pool_address: Option<String>,
    /// Start timestamp (seconds)
    pub start_time: u64,
    /// End timestamp (seconds)
    pub end_time: u64,
    /// Optional chain specification (solana, hypercore, hyperevm)
    pub chain: Option<String>,
}

/// Response containing user trading indicators data
#[derive(Debug, Serialize)]
pub struct UserIndicatorsResponse {
    /// Individual trade pills for chart display
    pub pills: Vec<TradePill>,
    /// Trend lines data (average prices)
    pub trend_lines: TrendLines,
    /// Metadata about the indicators
    pub metadata: IndicatorMetadata,
}

/// Individual trade point for chart display (pill)
#[derive(Debug, Serialize)]
pub struct TradePill {
    /// Trade timestamp (seconds)
    pub timestamp: i64,
    /// Trade price in USD
    pub price_usd: f64,
    /// Trade price in SOL
    pub price_sol: f64,
    /// Trade amount in USD
    pub amount_usd: f64,
    /// Market cap in USD at time of trade
    pub market_cap_usd: f64,
    /// Market cap in SOL at time of trade
    pub market_cap_sol: f64,
    /// Trade type: "buy" or "sell"
    pub trade_type: String,
    /// Transaction signature
    pub tx_signature: String,
}

/// Trend lines data for average buy/sell prices
#[derive(Debug, Serialize)]
pub struct TrendLines {
    /// Simple average buy price (USD)
    pub average_buy_price: f64,
    /// Simple average sell price (USD)
    pub average_sell_price: f64,
    /// Simple average buy price (SOL)
    pub average_buy_price_sol: f64,
    /// Simple average sell price (SOL)
    pub average_sell_price_sol: f64,
    /// Volume-weighted average buy price (USD)
    pub buy_volume_weighted_price: f64,
    /// Volume-weighted average sell price (USD)
    pub sell_volume_weighted_price: f64,
    /// Volume-weighted average buy price (SOL)
    pub buy_volume_weighted_price_sol: f64,
    /// Volume-weighted average sell price (SOL)
    pub sell_volume_weighted_price_sol: f64,
    /// Total buy volume (USD)
    pub total_buy_volume_usd: f64,
    /// Total sell volume (USD)
    pub total_sell_volume_usd: f64,
    /// Current market cap (USD)
    pub market_cap_usd: f64,
    /// Current market cap (SOL)
    pub market_cap_sol: f64,
    /// Number of buy trades
    pub buy_trade_count: u32,
    /// Number of sell trades
    pub sell_trade_count: u32,
}

/// Metadata about the indicators response
#[derive(Debug, Serialize)]
pub struct IndicatorMetadata {
    /// Total number of trades found
    pub total_trades: u32,
}

/// Request parameters for trend lines only (no time range needed)
#[derive(Debug, Deserialize)]
pub struct TrendLinesParams {
    /// User wallet address
    pub wallet_address: String,
    /// Token mint address
    pub token_mint: String,
    /// Optional pool address for specific pool filtering
    pub pool_address: Option<String>,
    /// Optional chain specification (solana, hypercore, hyperevm)
    pub chain: Option<String>,
}

/// Response containing only trend lines data
#[derive(Debug, Serialize)]
pub struct TrendLinesResponse {
    /// Trend lines data (null if no open position)
    pub trend_lines: Option<TrendLines>,
}

impl Default for TrendLines {
    fn default() -> Self {
        Self {
            average_buy_price: 0.0,
            average_sell_price: 0.0,
            average_buy_price_sol: 0.0,
            average_sell_price_sol: 0.0,
            buy_volume_weighted_price: 0.0,
            sell_volume_weighted_price: 0.0,
            buy_volume_weighted_price_sol: 0.0,
            sell_volume_weighted_price_sol: 0.0,
            total_buy_volume_usd: 0.0,
            total_sell_volume_usd: 0.0,
            market_cap_usd: 0.0,
            market_cap_sol: 0.0,
            buy_trade_count: 0,
            sell_trade_count: 0,
        }
    }
}

impl TrendLinesParams {
    /// Validate the request parameters
    pub fn validate(&self) -> Result<(), String> {
        if self.wallet_address.is_empty() {
            return Err("wallet_address cannot be empty".to_string());
        }
        if self.token_mint.is_empty() {
            return Err("token_mint cannot be empty".to_string());
        }
        if let Some(ref chain) = self.chain {
            match chain.to_lowercase().as_str() {
                "solana" | "hypercore" | "hyperevm" => {}
                _ => {
                    return Err(
                        "invalid chain, must be one of: solana, hypercore, hyperevm".to_string()
                    )
                }
            }
        }
        Ok(())
    }

    /// Get the chain enum value, defaulting to auto-detection if not specified
    pub fn get_chain(&self) -> Option<superstack_data::postgres::enums::Chain> {
        self.chain.as_ref().and_then(|chain| match chain.to_lowercase().as_str() {
            "solana" => Some(superstack_data::postgres::enums::Chain::Solana),
            "hypercore" => Some(superstack_data::postgres::enums::Chain::Hypercore),
            "hyperevm" => Some(superstack_data::postgres::enums::Chain::HyperEvm),
            _ => None,
        })
    }
}

impl UserIndicatorsParams {
    /// Validate the request parameters
    pub fn validate(&self) -> Result<(), String> {
        if self.wallet_address.is_empty() {
            return Err("wallet_address cannot be empty".to_string());
        }
        if self.token_mint.is_empty() {
            return Err("token_mint cannot be empty".to_string());
        }

        // Validate time range
        if self.start_time >= self.end_time {
            return Err("start_time must be less than end_time".to_string());
        }

        if let Some(ref chain) = self.chain {
            match chain.to_lowercase().as_str() {
                "solana" | "hypercore" | "hyperevm" => {}
                _ => {
                    return Err(
                        "invalid chain, must be one of: solana, hypercore, hyperevm".to_string()
                    )
                }
            }
        }
        Ok(())
    }

    /// Get the chain enum value, defaulting to auto-detection if not specified
    pub fn get_chain(&self) -> Option<superstack_data::postgres::enums::Chain> {
        self.chain.as_ref().and_then(|chain| match chain.to_lowercase().as_str() {
            "solana" => Some(superstack_data::postgres::enums::Chain::Solana),
            "hypercore" => Some(superstack_data::postgres::enums::Chain::Hypercore),
            "hyperevm" => Some(superstack_data::postgres::enums::Chain::HyperEvm),
            _ => None,
        })
    }
}
