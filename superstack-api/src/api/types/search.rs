use anyhow::Result;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use superstack_data::postgres::{
    aggregator::{PoolStatistic, TokenStatistic},
    indexer::PoolMetadata,
};

use super::Chain;

// Temporary types until proper search implementation
#[derive(Clone, Debug)]
pub struct SearchedToken {
    pub mint: Pubkey,
    pub name: String,
    pub symbol: String,
    pub image_url: Option<String>,
    pub chain: Chain,
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub website: Option<String>,
    pub price: f64,
    pub market_cap: f64,
    pub liquidity: f64,
    pub price_change_24h: f64,
    pub volume_24h: f64,
}

#[derive(Clone, Debug)]
pub struct SearchedPool {
    pub pool_metadata: PoolMetadata,
    pub price: f64,
    pub price_change_24h: f64,
    pub volume_24h: f64,
    pub market_cap: f64,
    pub liquidity: f64,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchResponse {
    pub tokens: Vec<SearchTokenResponse>,
    pub pools: Vec<SearchPoolResponse>,
}

impl SearchResponse {
    pub fn new(tokens: Vec<SearchTokenResponse>, pools: Vec<SearchPoolResponse>) -> Self {
        Self { tokens, pools }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchTokenResponse {
    pub token_mint: String,
    pub name: String,
    pub symbol: String,
    pub image_url: Option<String>,
    pub chain: String,
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub website: Option<String>,
    pub price: f64,
    pub market_cap: f64,
    pub liquidity: f64,
    pub price_change_24h: f64,
    pub volume_24h: f64,
}

impl From<SearchedToken> for SearchTokenResponse {
    fn from(token: SearchedToken) -> Self {
        Self {
            token_mint: token.mint.to_string(),
            name: token.name,
            symbol: token.symbol,
            image_url: token.image_url,
            chain: token.chain.to_string(),
            twitter: token.twitter,
            telegram: token.telegram,
            website: token.website,
            price: token.price,
            market_cap: token.market_cap,
            liquidity: token.liquidity,
            price_change_24h: token.price_change_24h,
            volume_24h: token.volume_24h,
        }
    }
}

impl From<TokenStatistic> for SearchTokenResponse {
    fn from(token: TokenStatistic) -> Self {
        Self {
            token_mint: token.token_address,
            name: token.name,
            symbol: token.symbol,
            image_url: token.image,
            chain: token.chain.to_string(),
            twitter: token.twitter,
            telegram: token.telegram,
            website: token.website,
            price: token.usd_price,
            market_cap: token.usd_market_cap,
            liquidity: token.usd_liquidity,
            price_change_24h: token.price_change_24h,
            volume_24h: token.usd_volume_24h,
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchPoolResponse {
    pub token_mint: String,
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub image_url: Option<String>,
    pub chain: String,
    pub base_symbol: String,
    pub pair_label: String,
    pub dex: String,
    pub pool_type: Option<String>,
    pub creation_time: i64,
    pub price: f64,
    pub price_change_24h: f64,
    pub volume_24h: f64,
    pub market_cap: f64,
    pub liquidity: f64,
}

impl SearchPoolResponse {
    /// Convert timestamp from milliseconds to seconds, handling edge cases
    fn normalize_timestamp(timestamp_millis: i64) -> i64 {
        if timestamp_millis < 0 {
            0 // Use 0 for invalid negative timestamps
        } else if timestamp_millis > 1_000_000_000_000 {
            // If timestamp is in milliseconds (> year 2001 in seconds), convert to seconds
            timestamp_millis / 1000
        } else {
            // Already in seconds
            timestamp_millis
        }
    }
    pub fn new(
        pool: SearchedPool,
        token_mint: String,
        token_name: String,
        token_symbol: String,
        token_image: Option<String>,
    ) -> Result<Self> {
        let pool_metadata = &pool.pool_metadata;
        let base_symbol = crate::utils::get_base_symbol_for_chain(
            pool_metadata.chain,
            &pool_metadata.base_address,
        )?;

        Ok(Self {
            token_mint,
            address: pool_metadata.pool_address.clone(),
            name: token_name,
            symbol: token_symbol,
            image_url: token_image,
            chain: pool_metadata.chain.to_string(),
            base_symbol: base_symbol.to_string(),
            pair_label: pool_metadata.pair_label.clone(),
            dex: pool_metadata.dex.to_string(),
            pool_type: Some(pool_metadata.pool_type.to_string()),
            creation_time: Self::normalize_timestamp(pool_metadata.create_timestamp_millis),
            price: pool.price,
            price_change_24h: pool.price_change_24h,
            volume_24h: pool.volume_24h,
            market_cap: pool.market_cap,
            liquidity: pool.liquidity,
        })
    }

    pub fn from_pool_statistics(
        pool: PoolStatistic,
        token_mint: String,
        token_name: String,
        token_symbol: String,
        token_image: Option<String>,
    ) -> Result<Self> {
        let base_symbol = crate::utils::get_base_symbol_for_chain(pool.chain, &pool.base_address)?;

        Ok(Self {
            token_mint,
            address: pool.pool_address.clone(),
            name: token_name,
            symbol: token_symbol,
            image_url: token_image,
            chain: pool.chain.to_string(),
            base_symbol: base_symbol.to_string(),
            pair_label: pool.pair_label.clone(),
            dex: pool.dex.to_string(),
            pool_type: Some(pool.pool_type.to_string()),
            creation_time: Self::normalize_timestamp(pool.create_timestamp_millis),
            price: pool.usd_price,
            price_change_24h: pool.price_change_24h,
            volume_24h: pool.usd_volume_24h,
            market_cap: pool.usd_market_cap,
            liquidity: pool.usd_liquidity,
        })
    }
}
