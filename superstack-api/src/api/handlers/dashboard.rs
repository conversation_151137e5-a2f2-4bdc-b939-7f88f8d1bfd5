use std::collections::{HashMap, HashSet};

use axum::{extract::State, http::StatusCode, Json};
use chrono::Utc;
use sqlx::types::BigDecimal;
use superstack_data::postgres::enums::Chain;

use crate::models::TokenMetadataWithPrice;

use super::*;
use crate::{
    api::{types::*, StorageState},
    utils::lamports_to_token,
    wallet::SolanaWalletManager,
};

pub async fn get_code_stats(
    State(state): State<StorageState>,
) -> Result<Json<InvideCodeStats>, (StatusCode, Json<ErrorResponse>)> {
    // Get all used referral codes from referrals table
    let referrals = state.get_all_referrals().await.map_err(|e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
    })?;

    let mut used_codes_set = HashSet::new();
    let mut binded_users = Vec::new();

    for referral in referrals {
        // For each referral, we need to find the code that was used
        // Since we don't store the code directly in referrals table,
        // we'll use the referee wallet to find the used code
        if let Ok(Some(used_code)) =
            state.get_used_referral_code_by_wallet(&referral.referee_wallet).await
        {
            used_codes_set.insert(used_code.code.clone());

            let binded_user = BindedUser {
                code: used_code.code,
                wallet_address: referral.referee_wallet,
                created_at: referral.created_at,
            };
            binded_users.push(binded_user);
        }
    }

    // Note: With unified referral code system, we no longer have a predefined list of codes
    // All codes are generated dynamically, so "unused codes" concept doesn't apply
    let unused_codes: Vec<String> = Vec::new();

    let stats = InvideCodeStats { used_codes: binded_users, unused_codes };

    Ok(Json(stats))
}

pub async fn get_transaction_stats(
    State(state): State<StorageState>,
) -> Result<Json<TransactionStats>, (StatusCode, Json<ErrorResponse>)> {
    let stats = state.get_transaction_stats().await.map_err(|e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
    })?;

    let stats = TransactionStats {
        total_success_transactions: stats.success_count as u64,
        total_pending_transactions: stats.pending_count as u64,
        total_failed_transactions: stats.failed_count as u64,
        total_expired_transactions: stats.expired_count as u64,
        success_transactions_24h: stats.success_count_24h as u64,
        pending_transactions_24h: stats.pending_count_24h as u64,
        failed_transactions_24h: stats.failed_count_24h as u64,
        expired_transactions_24h: stats.expired_count_24h as u64,
    };

    Ok(Json(stats))
}

pub async fn get_user_funds_stats(
    State(state): State<StorageState>,
) -> Result<Json<UserFundsStats>, (StatusCode, Json<ErrorResponse>)> {
    let referrals = state.get_all_referrals().await.map_err(|e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
    })?;

    let mut users = HashSet::new();
    for referral in referrals {
        users.insert(referral.referee_wallet);
    }

    // TODO: Handle multiple wallets
    let mut user_funds_map = HashMap::new();
    let mut wallet_funds_map = HashMap::new();
    for user in users {
        let single_wallet_funds =
            SingleWalletFunds { wallet_address: user.clone(), account_values: 0.0, funds: vec![] };
        let user_funds = SingleUserFunds { user: user.clone(), funds: vec![] };
        wallet_funds_map.insert(user.clone(), single_wallet_funds);
        user_funds_map.insert(user.clone(), user_funds);
    }

    let account_values = state.get_all_latest_account_values().await.map_err(|e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
    })?;

    let mut total_account_values = 0.0;
    let now = Utc::now().timestamp();
    let one_day_ago = now - 24 * 60 * 60;
    for account_value in account_values {
        let user = account_value.wallet_address;
        let user_pubkey = match Pubkey::from_str(&user) {
            Ok(pubkey) => pubkey,
            Err(_) => {
                tracing::error!("Failed to parse wallet address: {:?}", user);
                continue;
            }
        };
        let mut value = account_value.value;

        if let Some(single_wallet_funds) = wallet_funds_map.get_mut(&user) {
            if account_value.timestamp < one_day_ago {
                let latest_account_value =
                    SolanaWalletManager::get_account_value(&state, &user_pubkey)
                        .await
                        .map_err(|e| {
                            (
                                StatusCode::INTERNAL_SERVER_ERROR,
                                Json(ErrorResponse::new(e.to_string())),
                            )
                        })
                        .unwrap_or(value);
                value = latest_account_value;
            }

            single_wallet_funds.account_values = value;
            total_account_values += value;
        }
    }

    let positions = state.get_all_positions().await.map_err(|e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
    })?;

    let mut token_info_map: HashMap<String, TokenMetadataWithPrice> = HashMap::new();
    let sol_usd_price = crate::utils::get_sol_price().await;
    for position in positions {
        let wallet_address = position.wallet_address;
        let token_mint = position.token_mint;

        let token_info = if let Some(token_info) = token_info_map.get(&token_mint) {
            token_info.clone()
        } else {
            // Try to find token across all chains
            let mut token_found = None;
            for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
                if let Ok(Some(token)) =
                    state.indexer_data_provider.get_token(chain, &token_mint).await
                {
                    token_found = Some(token);
                    break;
                }
            }

            if let Some(token_statistic) = token_found {
                // Create a TokenMetadataWithPrice-like structure from TokenStatistic
                let token_metadata_with_price =
                    superstack_data::data_provider::TokenMetadataWithPrice {
                        token_metadata:
                            superstack_data::postgres::indexer::token_metadata::TokenMetadata {
                                chain: token_statistic.chain,
                                address: token_statistic.token_address.clone(),
                                name: token_statistic.name.clone(),
                                symbol: token_statistic.symbol.clone(),
                                decimals: token_statistic.decimals,
                                image: token_statistic.image.clone(),
                                description: token_statistic.description.clone(),
                                twitter: token_statistic.twitter.clone(),
                                telegram: token_statistic.telegram.clone(),
                                website: token_statistic.website.clone(),
                                create_dex: token_statistic.create_dex,
                                supply: token_statistic
                                    .supply
                                    .parse()
                                    .unwrap_or_else(|_| BigDecimal::from(0)),
                                mint_authority: token_statistic.mint_authority.clone(),
                                freeze_authority: token_statistic.freeze_authority.clone(),
                                is_mutable: token_statistic.is_mutable,
                                is_trench_token: token_statistic.is_trench_token,
                                migration_pool_address: None,
                                create_timestamp_millis: token_statistic.create_timestamp_millis,
                                update_timestamp_millis: token_statistic.update_timestamp_millis,
                                dex_paid: token_statistic.dex_paid,
                                create_block_number: token_statistic.create_block_number,
                                create_tx_hash: token_statistic.create_tx_hash,
                                create_bonding_curve: token_statistic.create_bonding_curve,
                                create_dev: token_statistic.create_dev,
                                migration_timestamp_millis: token_statistic
                                    .migration_timestamp_millis,
                                uri: token_statistic.website,
                                seller_fee_basis_points: None,
                                creators: None,
                                primary_sale_happened: None,
                                update_authority: token_statistic.update_authority,
                                is_active: token_statistic.is_active,
                                image_path: token_statistic.image_path,
                            },
                        price: token_statistic.usd_price,
                    };
                token_info_map.insert(token_mint.clone(), token_metadata_with_price.clone());
                token_metadata_with_price
            } else {
                continue;
            }
        };

        if let Some(single_wallet_funds) = wallet_funds_map.get_mut(&wallet_address) {
            let amount = position.bought_amount - position.sold_amount;
            let ui_amount = lamports_to_token(amount as _, token_info.token_metadata.decimals as _);
            let usd_value = ui_amount * token_info.price;

            let fund = FundInfo {
                mint: token_mint.clone(),
                decimals: token_info.token_metadata.decimals as _,
                symbol: token_info.token_metadata.symbol,
                name: token_info.token_metadata.name,
                image: token_info.token_metadata.image,
                ui_amount,
                usd_value,
            };
            single_wallet_funds.funds.push(fund);
        }
    }

    for (user, user_funds) in user_funds_map.iter_mut() {
        let wallet_address = user;
        if let Some(single_wallet_funds) = wallet_funds_map.get(wallet_address) {
            user_funds.funds.push(single_wallet_funds.clone());
        } else {
            user_funds.funds.push(SingleWalletFunds {
                wallet_address: wallet_address.clone(),
                account_values: 0.0,
                funds: vec![],
            });
        }
    }

    Ok(Json(UserFundsStats {
        users: user_funds_map.values().cloned().collect(),
        total_account_value: total_account_values,
    }))
}
