use axum::{extract::State, http::StatusCode, Json};
use serde::Deserialize;

use crate::{
    api::{types::Price, StorageState},
    constant::SOL_MINT,
};

#[derive(Deserialize)]
pub struct PriceParams {
    pub token_mint: Option<String>,
    pub wallet_address: Option<String>,
}

pub async fn get_sol_price(State(_state): State<StorageState>) -> Result<Json<Price>, StatusCode> {
    let sol_price = crate::utils::get_sol_price().await;
    Ok(Json(Price { token_mint: SOL_MINT.to_string(), usd_price: sol_price }))
}

// pub async fn get_price(
//     State(state): State<StorageState>,
//     Query(query): Query<PriceParams>,
// ) -> Result<Json<Vec<Price>>, StatusCode> {
//     let provider = MoralisProvider::get();

//     if let Some(token_mint_str) = query.token_mint {
//         let tokens = token_mint_str.split(',').collect::<Vec<&str>>();
//         let mut prices = vec![];
//         for token in tokens {
//             let price = match provider.get_price(token).await {
//                 Ok(price) => price,
//                 Err(e) => {
//                     tracing::error!("Failed to get price for token ({}): {}", token, e);
//                     return Err(StatusCode::INTERNAL_SERVER_ERROR);
//                 }
//             };
//             tracing::info!("Price for token ({}): {:?}", token, price);
//             prices.push(Price { token_mint: token.to_string(), usd_price: price.usd_price });
//         }
//         return Ok(Json(prices));
//     }

//     if let Some(wallet_address) = query.wallet_address {
//         let positions = state.api_db.get_positions(&wallet_address).await.unwrap();
//         let mut prices = vec![];
//         for position in positions {
//             let price = match provider.get_price(&position.token_mint).await {
//                 Ok(price) => price,
//                 Err(e) => {
//                     tracing::error!(
//                         "Failed to get price for token ({}): {}",
//                         position.token_mint,
//                         e
//                     );
//                     return Err(StatusCode::INTERNAL_SERVER_ERROR);
//                 }
//             };
//             prices.push(Price {
//                 token_mint: position.token_mint.to_string(),
//                 usd_price: price.usd_price,
//             });
//         }
//         return Ok(Json(prices));
//     }

//     Err(StatusCode::BAD_REQUEST)
// }
