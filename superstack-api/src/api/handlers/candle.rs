use std::str::FromStr;

use alloy::primitives::Address as EthAddress;
use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use serde::Deserialize;
use serde_json::{json, Value};
use solana_sdk::pubkey::Pubkey;
use superstack_data::postgres::{
    aggregator::smart_cache::*,
    enums::{CandleInterval, Chain},
};

use super::*;
use crate::{
    api::{types::CandleResponse, StorageState},
    utils::timestamp,
};

#[derive(Debug, Deserialize)]
pub struct CandleParams {
    pub pair_address: String,
    pub interval: String,
    pub start_time: u64,
    pub end_time: u64,
    pub chain: Option<String>,
}

/// Validate pair address based on the blockchain chain
fn validate_pair_address(address: &str, chain: Chain) -> Result<String, String> {
    match chain {
        Chain::Solana => {
            // Validate Solana address (Base58 format)
            match Pubkey::from_str(address) {
                Ok(addr) => Ok(addr.to_string()),
                Err(_) => Err("Invalid Solana address format".to_string()),
            }
        }
        Chain::HyperEvm => {
            // Validate Ethereum-style address (0x format)
            match EthAddress::from_str(address) {
                Ok(addr) => Ok(addr.to_string()),
                Err(_) => Err("Invalid Ethereum address format".to_string()),
            }
        }
        Chain::Hypercore => {
            // For Hypercore, use simple string validation for now
            // Could be enhanced with specific Hypercore address format validation
            if address.is_empty() {
                Err("Address cannot be empty".to_string())
            } else {
                Ok(address.to_string())
            }
        }
    }
}

/// Resolve pool address from either pool address or token address
/// First tries to find as pool address, then as token address to get default pool
async fn resolve_pool_address(
    state: &StorageState,
    address: &str,
    chain: Option<Chain>,
) -> Result<(String, Chain), String> {
    // If chain is specified, try that chain first
    if let Some(specified_chain) = chain {
        if let Ok(pool_address) = try_resolve_for_chain(state, address, specified_chain).await {
            return Ok((pool_address, specified_chain));
        }
        return Err(format!("Address not found as pool or token on chain {:?}", specified_chain));
    }

    // If no chain specified, try all chains
    for try_chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
        if let Ok(pool_address) = try_resolve_for_chain(state, address, try_chain).await {
            return Ok((pool_address, try_chain));
        }
    }

    Err("Address not found as pool or token on any supported chain".to_string())
}

/// Try to resolve address for a specific chain
async fn try_resolve_for_chain(
    state: &StorageState,
    address: &str,
    chain: Chain,
) -> Result<String, String> {
    // First, try to get pool metadata directly (address is pool address)
    match state.indexer_db.get_pool_metadata(chain, address).await {
        Ok(Some(_pool_metadata)) => {
            tracing::info!("Address {} resolved as pool address on chain {:?}", address, chain);
            return Ok(address.to_string());
        }
        Ok(None) => {
            tracing::debug!(
                "Address {} not found as pool on chain {:?}, trying as token",
                address,
                chain
            );
        }
        Err(e) => {
            tracing::warn!(
                "Error checking pool metadata for {} on chain {:?}: {}",
                address,
                chain,
                e
            );
        }
    }

    // If not found as pool, try as token address and get default pool
    match state.indexer_data_provider.get_pools_by_token_address(chain, address).await {
        Ok(pools) => {
            if pools.is_empty() {
                return Err(format!("No pools found for token {} on chain {:?}", address, chain));
            }

            // Sort pools by volume (descending) to get the default pool
            let mut sorted_pools = pools;
            sorted_pools.sort_by(|a, b| {
                b.usd_volume_24h.partial_cmp(&a.usd_volume_24h).unwrap_or(std::cmp::Ordering::Equal)
            });

            let default_pool = &sorted_pools[0];
            tracing::info!(
                "Address {} resolved as token address on chain {:?}, using default pool {} (volume: {})",
                address, chain, default_pool.pool_address, default_pool.usd_volume_24h
            );
            Ok(default_pool.pool_address.clone())
        }
        Err(e) => {
            tracing::debug!(
                "Error getting pools for token {} on chain {:?}: {}",
                address,
                chain,
                e
            );
            Err(format!("Address {} not found as token on chain {:?}", address, chain))
        }
    }
}

/// Enhanced candle snapshot endpoint with intelligent caching (v1 compatible)
pub async fn get_candle_snapshot(
    State(state): State<StorageState>,
    Query(params): Query<CandleParams>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    // Parse chain parameter, default to None for auto-detection
    let specified_chain = match params.chain.as_deref() {
        Some("solana") => Some(Chain::Solana),
        Some("hypercore") => Some(Chain::Hypercore),
        Some("hyperevm") => Some(Chain::HyperEvm),
        None => None, // Auto-detect chain
        Some(invalid) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!(
                    "Invalid chain: {}. Supported chains: solana, hypercore, hyperevm",
                    invalid
                ))),
            ));
        }
    };

    // Resolve pool address (supports both pool and token addresses)
    let (pool_address, detected_chain) =
        match resolve_pool_address(&state, &params.pair_address, specified_chain).await {
            Ok((addr, chain)) => (addr, chain),
            Err(error_msg) => {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new(&format!("Failed to resolve address: {}", error_msg))),
                ));
            }
        };

    // Validate the resolved pool address format for the detected chain
    let validated_pool_address = match validate_pair_address(&pool_address, detected_chain) {
        Ok(addr) => addr,
        Err(error_msg) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!("Invalid resolved pool address: {}", error_msg))),
            ));
        }
    };

    // Validate and parse interval
    let interval = match params.interval.as_str() {
        "1s" => CandleInterval::S1,
        "5s" => CandleInterval::S5,
        "15s" => CandleInterval::S15,
        "30s" => CandleInterval::S30,
        "1m" => CandleInterval::M1,
        "5m" => CandleInterval::M5,
        "15m" => CandleInterval::M15,
        "30m" => CandleInterval::M30,
        "1h" => CandleInterval::H1,
        "4h" => CandleInterval::H4,
        "8h" => CandleInterval::H8,
        "12h" => CandleInterval::H12,
        "24h" => CandleInterval::H24,
        "3d" => CandleInterval::D3,
        "7d" => CandleInterval::D7,
        "30d" => CandleInterval::D30,
        _ => {
            return Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid interval"))));
        }
    };

    // Normalize timestamps
    let start_time_seconds = match timestamp::normalize_and_validate(params.start_time) {
        Ok(ts) => ts,
        Err(e) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!("Invalid start timestamp: {}", e))),
            ));
        }
    };

    let end_time_seconds = match timestamp::normalize_and_validate(params.end_time) {
        Ok(ts) => ts,
        Err(e) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!("Invalid end timestamp: {}", e))),
            ));
        }
    };

    // Validate time range
    if end_time_seconds <= start_time_seconds {
        return Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid time range"))));
    }

    tracing::info!(
        "Getting candles for resolved pool {} (from input: {}) with interval {} from {} to {} (normalized from {} to {})",
        validated_pool_address,
        params.pair_address,
        params.interval,
        start_time_seconds,
        end_time_seconds,
        params.start_time,
        params.end_time
    );

    // Create cache manager with default config
    let cache_config = SmartCacheConfig::default();
    let cache_manager = SmartCacheManager::new(state.redis_client.clone(), cache_config);

    // Get candles using intelligent caching
    match cache_manager
        .get_candles(
            detected_chain,
            &validated_pool_address,
            interval,
            start_time_seconds,
            end_time_seconds,
        )
        .await
    {
        Ok(candles) => {
            tracing::info!(
                "Retrieved {} candles for pool {} with interval {} in time range {} to {}",
                candles.len(),
                validated_pool_address,
                params.interval,
                start_time_seconds,
                end_time_seconds
            );

            // Convert to response format (remove metadata fields for v1 compatibility)
            let response_candles: Vec<_> = candles
                .into_iter()
                .map(|mut c| {
                    // Increment access count for analytics
                    c.increment_access();

                    // Convert to compatible format
                    CandleResponse::from(c)
                })
                .collect();

            // Check if there might be more data available
            let has_more = check_has_more_candles(
                &state.indexer_db,
                &validated_pool_address,
                detected_chain,
                start_time_seconds,
            )
            .await
            .unwrap_or(false);

            // Log first few candles for debugging
            if !response_candles.is_empty() {
                tracing::debug!(
                    "Sample candle data: first candle timestamp={}, price={}, volume={}",
                    response_candles[0].open_timestamp_seconds,
                    response_candles[0].usd_open_price,
                    response_candles[0].usd_volume
                );
            }

            Ok(Json(json!({
                "data": response_candles,
                "hasMore": has_more,
                "defaultPool": validated_pool_address,
                "chain": detected_chain.to_string().to_lowercase(),
            })))
        }
        Err(e) => {
            tracing::error!("Failed to get candles for pool {}: {}", validated_pool_address, e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to get candle snapshot")),
            ))
        }
    }
}

/// Check if there are more candles available before the given timestamp
async fn check_has_more_candles(
    db: &superstack_data::postgres::PostgresDatabase,
    pool_address: &str,
    chain: Chain,
    before_timestamp: i64,
) -> Result<bool, superstack_data::Error> {
    // This is a simplified implementation
    // In practice, you might want to check the database for earlier data
    let candles = db
        .get_candles(
            chain,
            pool_address,
            CandleInterval::S1,
            before_timestamp - 86400, // Check 24 hours before
            before_timestamp,
        )
        .await?;

    Ok(!candles.is_empty())
}
