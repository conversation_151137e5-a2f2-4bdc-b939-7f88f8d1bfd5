use axum::{
    extract::{Query, State},
    http::{header, HeaderMap, HeaderValue, StatusCode},
    Json,
};
use serde::{Deserialize, Serialize};
use superstack_data::postgres::PerpExchange;
use time::OffsetDateTime;

use crate::{
    api::{
        handlers::{ErrorResponse, PortfolioParams},
        types::portfolio::PortfolioRange,
    },
    models::StorageState,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct PerpsPortfolio {
    #[serde(rename = "accountValueHistory")]
    pub account_value_history: Vec<AccountValueHistory>,
    #[serde(rename = "pnlHistory")]
    pub pnl_history: Vec<PnlHistory>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PnlHistory {
    pub timestamp: i64,
    pub pnl: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AccountValueHistory {
    pub timestamp: i64,
    pub account_value: f64,
}

/// Get sampling interval in seconds based on portfolio range
fn get_sampling_interval(range: &PortfolioRange) -> i64 {
    match range {
        PortfolioRange::Hour1 => 15,    // 0.25 min = 15 seconds
        PortfolioRange::Hour24 => 300,  // 5 mins = 300 seconds
        PortfolioRange::Day7 => 1800,   // 30 mins = 1800 seconds
        PortfolioRange::Day30 => 7200,  // 2 hours = 7200 seconds
        PortfolioRange::Year1 => 86400, // 1 day = 86400 seconds
        PortfolioRange::All => 172800,  // 2 days = 172800 seconds
        // For other ranges, use reasonable defaults
        PortfolioRange::Min5 => 15,   // 15 seconds
        PortfolioRange::Hour6 => 300, // 5 mins
        PortfolioRange::Day3 => 1800, // 30 mins
    }
}

/// Generate PnL history from account value history
fn accumulate_pnl_history(sorted_account_values: &[AccountValueHistory]) -> Vec<PnlHistory> {
    if sorted_account_values.is_empty() {
        return Vec::new();
    }

    let initial_value = sorted_account_values[0].account_value;

    sorted_account_values
        .iter()
        .map(|value| PnlHistory {
            timestamp: value.timestamp,
            pnl: value.account_value - initial_value,
        })
        .collect()
}

pub async fn get_perp_portfolio(
    State(state): State<StorageState>,
    Query(query): Query<PortfolioParams>,
) -> Result<(HeaderMap, Json<PerpsPortfolio>), (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = &query.wallet_address;
    let range = &query.range;

    let now = OffsetDateTime::now_utc();
    let end_timestamp = now.unix_timestamp() * 1000; // Convert to milliseconds
    let start_timestamp = range.get_start_time(now) * 1000; // Convert to milliseconds

    // Get sampling interval based on range
    let sampling_interval = get_sampling_interval(range);

    // Query perp user states with database-level sampling for better performance
    let user_states = state
        .indexer_db
        .get_perp_user_states_sampled(
            PerpExchange::Hyperliquid,
            wallet_address,
            start_timestamp,
            end_timestamp,
            sampling_interval,
        )
        .await
        .map_err(|e| {
            tracing::error!("Failed to get sampled perp user states: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to get perp user states")),
            )
        })?;

    // Convert to AccountValueHistory directly
    let mut account_value_history: Vec<AccountValueHistory> = user_states
        .into_iter()
        .map(|state| AccountValueHistory {
            timestamp: state.timestamp_millis,
            account_value: state.hyperliquid_acc_perps,
        })
        .collect();

    // Check if we need to add a zero-value entry at range start
    let needs_start_point =
        account_value_history.is_empty() || account_value_history[0].timestamp > start_timestamp;

    if needs_start_point {
        // Insert zero-value entry at the beginning for range start
        account_value_history
            .insert(0, AccountValueHistory { timestamp: start_timestamp, account_value: 0.0 });
    }

    // Generate PnL history from account value history
    let pnl_history = accumulate_pnl_history(&account_value_history);

    let portfolio = PerpsPortfolio { account_value_history, pnl_history };

    // Set CDN cache headers - 3 minutes TTL
    let mut headers = HeaderMap::new();
    headers.insert(header::CACHE_CONTROL, HeaderValue::from_static("public, max-age=180"));

    Ok((headers, Json(portfolio)))
}
