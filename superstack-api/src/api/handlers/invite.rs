use std::str::FromStr;

use axum::{
    extract::{Json, State},
    http::StatusCode,
};
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

use super::ErrorResponse;
use crate::{config::Config, models::StorageState};

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct InviteCodeRequest {
    pub invite_code: Option<String>,
    pub wallet_address: String,
}

#[derive(Debug, <PERSON>lone, Serialize)]
pub enum InvideCodeStatus {
    Authorized,
    Unauthorized,
    BindSuccess,
    BindFailed,
}

#[derive(Debug, Clone, Serialize)]
pub struct InviteCodeResponse {
    pub status: InvideCodeStatus,
}

// Handler to bind an invite code to a wallet
pub async fn handle_invite_code(
    State(state): State<StorageState>,
    Json(request): Json<InviteCodeRequest>,
) -> Result<Json<InviteCodeResponse>, (StatusC<PERSON>, Json<ErrorResponse>)> {
    // Check if invite code feature is enabled
    let config = Config::get();
    if !config.enable_invite_code {
        // If invite code is disabled, always return Authorized
        return Ok(Json(InviteCodeResponse { status: InvideCodeStatus::Authorized }));
    }

    // Validate wallet address (basic check)
    let _wallet_pubkey = Pubkey::from_str(&request.wallet_address).map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new(format!("Invalid wallet address format: {}", e))),
        )
    })?;

    // Check if wallet already exists in the system (legacy users)
    // For existing users, we don't require invite codes and don't allow rebinding
    if let Ok(Some(_wallet)) = state.get_wallet(&request.wallet_address).await {
        return Ok(Json(InviteCodeResponse { status: InvideCodeStatus::Authorized }));
    }

    // Check if this wallet has already been referred
    let existing_referral =
        state.get_referral_by_referee(&request.wallet_address).await.map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new(format!("Failed to get referral status: {}", e))),
            )
        })?;

    if let Some(_referral) = existing_referral {
        // User already has been referred, authorize them
        return Ok(Json(InviteCodeResponse { status: InvideCodeStatus::Authorized }));
    }

    // For new users, invite code is required
    if let Some(invite_code) = request.invite_code {
        // Validate the invite code format (6 characters, letters and numbers)
        if invite_code.len() == 6 && invite_code.chars().all(|c| c.is_alphanumeric()) {
            // Check if the invite code exists and get its owner
            let referral_code_info =
                state.get_referral_code_by_code(&invite_code).await.map_err(|e| {
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse::new(format!("Failed to find referral code: {}", e))),
                    )
                })?;

            match referral_code_info {
                Some(code_info) => {
                    // Check if code is still active
                    if code_info.status != "active" {
                        return Ok(Json(InviteCodeResponse {
                            status: InvideCodeStatus::BindFailed,
                        }));
                    }

                    // Prevent self-referral
                    if code_info.owner_wallet == request.wallet_address {
                        return Ok(Json(InviteCodeResponse {
                            status: InvideCodeStatus::BindFailed,
                        }));
                    }

                    // Bind the invite code
                    let bind_success = state
                        .bind_invite_code(&invite_code, &request.wallet_address)
                        .await
                        .map_err(|e| {
                            (
                                StatusCode::INTERNAL_SERVER_ERROR,
                                Json(ErrorResponse::new(format!(
                                    "Failed to bind invite code: {}",
                                    e
                                ))),
                            )
                        })?;

                    if bind_success {
                        // Successfully bound the invite code, user is now authorized
                        return Ok(Json(InviteCodeResponse {
                            status: InvideCodeStatus::BindSuccess,
                        }));
                    }
                }
                None => {
                    // Invite code doesn't exist
                    return Ok(Json(InviteCodeResponse { status: InvideCodeStatus::BindFailed }));
                }
            }
        }
        // Invalid code format or binding failed
        return Ok(Json(InviteCodeResponse { status: InvideCodeStatus::BindFailed }));
    }

    // New user without invite code - unauthorized
    Ok(Json(InviteCodeResponse { status: InvideCodeStatus::Unauthorized }))
}
