use axum::{
    extract::{<PERSON><PERSON>, Query, State},
    http::StatusCode,
};
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;

use super::ErrorResponse;
use crate::models::{
    Referral, ReferralCode, ReferralNetwork, ReferralReward, ReferralStats, StorageState,
};

/// Helper function to validate wallet address format
fn validate_wallet_address(wallet_address: &str) -> Result<(), (StatusCode, Json<ErrorResponse>)> {
    if Pubkey::from_str(wallet_address).is_err() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new("Invalid wallet address format")),
        ));
    }
    Ok(())
}

#[derive(Debug, Deserialize)]
pub struct GetReferralCodeQuery {
    pub wallet_address: String,
}

#[derive(Debug, Deserialize)]
pub struct PaginationQuery {
    pub wallet_address: String,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Deserialize)]
pub struct ClaimRewardRequest {
    pub wallet_address: String,
    pub reward_id: i64,
}

#[derive(Debug, Serialize)]
pub struct ReferralCodeResponse {
    pub code: String,
    pub status: String,
    pub created_at: i64,
    pub used_at: Option<i64>,
}

#[derive(Debug, Serialize)]
pub struct ReferralListResponse {
    pub referrals: Vec<ReferralInfo>,
    pub total: i64,
}

#[derive(Debug, Serialize)]
pub struct ReferralInfo {
    pub referee_wallet: String,
    pub created_at: i64,
}

#[derive(Debug, Serialize)]
pub struct RewardListResponse {
    pub rewards: Vec<RewardInfo>,
    pub total: i64,
}

#[derive(Debug, Serialize)]
pub struct RewardInfo {
    pub id: i64,
    pub referee_wallet: String,
    pub reward_type: String,
    pub amount_usd: rust_decimal::Decimal,
    pub is_claimed: bool,
    pub created_at: i64,
    pub claimed_at: Option<i64>,
}

#[derive(Debug, Serialize)]
pub struct ClaimRewardResponse {
    pub success: bool,
    pub message: String,
}

impl From<ReferralCode> for ReferralCodeResponse {
    fn from(rc: ReferralCode) -> Self {
        Self { code: rc.code, status: rc.status, created_at: rc.created_at, used_at: rc.used_at }
    }
}

impl From<Referral> for ReferralInfo {
    fn from(r: Referral) -> Self {
        Self { referee_wallet: r.referee_wallet, created_at: r.created_at }
    }
}

impl From<ReferralReward> for RewardInfo {
    fn from(r: ReferralReward) -> Self {
        let amount_usd = r.calculate_amount_usd();
        Self {
            id: r.id,
            referee_wallet: r.referee_wallet,
            reward_type: r.reward_type,
            amount_usd,
            is_claimed: r.is_claimed,
            created_at: r.created_at,
            claimed_at: r.claimed_at,
        }
    }
}

/// Get user's referral codes with automatic generation
///
/// This API automatically ensures the user has sufficient referral codes:
/// - Regular users: 12 total codes (active + used), creates missing codes if needed
/// - Unlimited users: 12 active (unused) codes, creates missing codes if needed
/// - Returns all codes (both active and used) for the user
///
/// Frontend only needs to call this API once to get all codes.
pub async fn get_referral_code(
    State(state): State<StorageState>,
    Query(query): Query<GetReferralCodeQuery>,
) -> Result<Json<Vec<ReferralCodeResponse>>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet address
    validate_wallet_address(&query.wallet_address)?;

    // Get user's referral codes (automatically ensures sufficient codes)
    let referral_codes =
        state.get_user_referral_codes(&query.wallet_address).await.map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new(&format!("Failed to get referral codes: {}", e))),
            )
        })?;

    let response: Vec<ReferralCodeResponse> =
        referral_codes.into_iter().map(ReferralCodeResponse::from).collect();

    Ok(Json(response))
}

/// Get referral statistics
pub async fn get_referral_stats(
    State(state): State<StorageState>,
    Query(query): Query<GetReferralCodeQuery>,
) -> Result<Json<ReferralStats>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet address
    validate_wallet_address(&query.wallet_address)?;

    let stats = state.get_referral_stats(&query.wallet_address).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse::new(&format!("Failed to get referral stats: {}", e))),
        )
    })?;

    Ok(Json(stats))
}

/// Get user's referral list
pub async fn get_referral_list(
    State(state): State<StorageState>,
    Query(query): Query<PaginationQuery>,
) -> Result<Json<ReferralListResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet address
    validate_wallet_address(&query.wallet_address)?;

    let limit = query.limit.unwrap_or(20).min(100); // Max 100 per page
    let offset = query.offset.unwrap_or(0);

    let referrals = state
        .get_referrals_by_referrer(&query.wallet_address, limit, offset)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new(&format!("Failed to get referrals: {}", e))),
            )
        })?;

    // Get total count for pagination - use optimized method
    let total = state.get_referral_count(&query.wallet_address).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse::new(&format!("Failed to get referral count: {}", e))),
        )
    })?;

    Ok(Json(ReferralListResponse {
        referrals: referrals.into_iter().map(ReferralInfo::from).collect(),
        total,
    }))
}

/// Get user's reward list
pub async fn get_reward_list(
    State(state): State<StorageState>,
    Query(query): Query<PaginationQuery>,
) -> Result<Json<RewardListResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet address
    validate_wallet_address(&query.wallet_address)?;

    let limit = query.limit.unwrap_or(20).min(100); // Max 100 per page
    let offset = query.offset.unwrap_or(0);

    let rewards =
        state.get_referral_rewards(&query.wallet_address, limit, offset).await.map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new(&format!("Failed to get rewards: {}", e))),
            )
        })?;

    // Get accurate total count
    let total = state.get_referral_rewards_count(&query.wallet_address).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse::new(&format!("Failed to get rewards count: {}", e))),
        )
    })?;

    Ok(Json(RewardListResponse {
        rewards: rewards.into_iter().map(RewardInfo::from).collect(),
        total,
    }))
}

/// Claim a reward
pub async fn claim_reward(
    State(state): State<StorageState>,
    Json(request): Json<ClaimRewardRequest>,
) -> Result<Json<ClaimRewardResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet address
    validate_wallet_address(&request.wallet_address)?;

    let success = state
        .claim_referral_reward(request.reward_id, &request.wallet_address)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new(&format!("Failed to claim reward: {}", e))),
            )
        })?;

    if success {
        Ok(Json(ClaimRewardResponse {
            success: true,
            message: "Reward claimed successfully".to_string(),
        }))
    } else {
        Ok(Json(ClaimRewardResponse {
            success: false,
            message: "Reward not found or already claimed".to_string(),
        }))
    }
}

/// Get referral network visualization data
pub async fn get_referral_network(
    State(state): State<StorageState>,
    Query(query): Query<GetReferralCodeQuery>,
) -> Result<Json<ReferralNetwork>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet address
    validate_wallet_address(&query.wallet_address)?;

    let network = state.get_referral_network(&query.wallet_address).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse::new(&format!("Failed to get referral network: {}", e))),
        )
    })?;

    Ok(Json(network))
}
