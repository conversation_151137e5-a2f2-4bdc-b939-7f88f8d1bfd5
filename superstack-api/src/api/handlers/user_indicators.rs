use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};

use super::*;
use crate::{
    api::{types::*, StorageState},
    utils::{address, price_calculator::PriceCalculator},
};

/// Get user trading indicators for a specific token
pub async fn get_user_indicators(
    State(state): State<StorageState>,
    Query(params): Query<UserIndicatorsParams>,
) -> Result<Json<UserIndicatorsResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate request parameters
    if let Err(e) = params.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new(&format!("Invalid parameters: {}", e))),
        ));
    }

    tracing::info!(
        "Getting user indicators for wallet: {}, token: {}, time range: {} to {}",
        params.wallet_address,
        params.token_mint,
        params.start_time,
        params.end_time
    );

    // Parse and validate wallet address
    let (detected_chain, normalized_wallet_address) =
        match address::parse_and_detect_chain(&params.wallet_address) {
            Ok((chain, addr)) => (chain, addr),
            Err(e) => {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new(&format!("Invalid wallet address: {}", e))),
                ));
            }
        };

    // Use specified chain or detected chain
    let query_chain = params.get_chain().unwrap_or(detected_chain);

    // Validate token mint address for the detected chain
    let normalized_token_mint =
        match address::parse_address_for_chain(&params.token_mint, query_chain) {
            Ok(addr) => addr,
            Err(e) => {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new(&format!(
                        "Invalid token mint for chain {}: {}",
                        query_chain.to_string().to_lowercase(),
                        e
                    ))),
                ));
            }
        };

    // Convert timestamps to seconds
    let start_time = params.start_time as i64;
    let end_time = params.end_time as i64;

    // Query user trades within the time range
    let trades = match state
        .get_trades_by_time_range(
            &normalized_wallet_address,
            &normalized_token_mint,
            start_time,
            end_time,
            Some(query_chain),
        )
        .await
    {
        Ok(trades) => trades,
        Err(e) => {
            tracing::error!("Failed to get trades for user indicators: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to retrieve trading data")),
            ));
        }
    };

    tracing::info!("Found {} trades for user indicators", trades.len());

    // Convert trades to pills with USD and SOL prices
    let pills =
        match PriceCalculator::trades_to_pills(trades, &state, &normalized_token_mint, query_chain)
            .await
        {
            Ok(pills) => pills,
            Err(e) => {
                tracing::error!("Failed to convert trades to pills: {}", e);
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to process trading data")),
                ));
            }
        };

    // Calculate trend lines
    let trend_lines = PriceCalculator::calculate_trend_lines(&pills);

    // Create metadata
    let metadata = IndicatorMetadata { total_trades: pills.len() as u32 };

    let response = UserIndicatorsResponse { pills, trend_lines, metadata };

    tracing::info!(
        "Returning user indicators: {} pills, {} buy trades, {} sell trades",
        response.metadata.total_trades,
        response.trend_lines.buy_trade_count,
        response.trend_lines.sell_trade_count
    );

    Ok(Json(response))
}

/// Get trend lines for a specific token (only if user has open position)
pub async fn get_trend_lines(
    State(state): State<StorageState>,
    Query(params): Query<TrendLinesParams>,
) -> Result<Json<TrendLinesResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate request parameters
    if let Err(e) = params.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new(&format!("Invalid parameters: {}", e))),
        ));
    }

    tracing::info!(
        "Getting trend lines for wallet: {}, token: {}",
        params.wallet_address,
        params.token_mint
    );

    // Parse and validate wallet address
    let (detected_chain, normalized_wallet_address) =
        match address::parse_and_detect_chain(&params.wallet_address) {
            Ok((chain, addr)) => (chain, addr),
            Err(e) => {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new(&format!("Invalid wallet address: {}", e))),
                ));
            }
        };

    // Use specified chain or detected chain
    let query_chain = params.get_chain().unwrap_or(detected_chain);

    // Validate token mint address for the detected chain
    let normalized_token_mint =
        match address::parse_address_for_chain(&params.token_mint, query_chain) {
            Ok(addr) => addr,
            Err(e) => {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new(&format!(
                        "Invalid token mint for chain {}: {}",
                        query_chain.to_string().to_lowercase(),
                        e
                    ))),
                ));
            }
        };

    // Get all trades for this wallet and token to calculate trend lines
    let trades = match state
        .get_all_trades_for_position_check(
            &normalized_wallet_address,
            &normalized_token_mint,
            Some(query_chain),
        )
        .await
    {
        Ok(trades) => trades,
        Err(e) => {
            tracing::error!("Failed to get trades for trend lines: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to retrieve trading data")),
            ));
        }
    };

    tracing::info!("Found {} total trades for trend lines calculation", trades.len());

    // Convert trades to pills and calculate trend lines (regardless of position status)
    let trend_lines = match PriceCalculator::trades_to_pills(
        trades.clone(),
        &state,
        &normalized_token_mint,
        query_chain,
    )
    .await
    {
        Ok(pills) => {
            let trend_lines = PriceCalculator::calculate_trend_lines(&pills);
            Some(trend_lines)
        }
        Err(e) => {
            tracing::error!("Failed to convert trades to pills for trend lines: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to process trading data")),
            ));
        }
    };

    let response = TrendLinesResponse { trend_lines };

    tracing::info!("Returning trend lines: {} trades processed", trades.len());

    Ok(Json(response))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        api::types::{TradePill, TrendLines},
        utils::price_calculator::PriceCalculator,
    };

    #[test]
    fn test_user_indicators_params_validation() {
        // Test valid parameters
        let valid_params = UserIndicatorsParams {
            wallet_address: "********************************".to_string(),
            token_mint: "So111111111********************************".to_string(),
            pool_address: None,
            start_time: 1640995200, // 2022-01-01
            end_time: 1640995260,   // 2022-01-01 + 1 minute
            chain: Some("solana".to_string()),
        };
        assert!(valid_params.validate().is_ok());

        // Test invalid time range
        let invalid_time_params = UserIndicatorsParams {
            wallet_address: "********************************".to_string(),
            token_mint: "So111111111********************************".to_string(),
            pool_address: None,
            start_time: 1640995260,
            end_time: 1640995200, // end before start
            chain: Some("solana".to_string()),
        };
        assert!(invalid_time_params.validate().is_err());

        // Test empty wallet address
        let empty_wallet_params = UserIndicatorsParams {
            wallet_address: "".to_string(),
            token_mint: "So111111111********************************".to_string(),
            pool_address: None,
            start_time: 1640995200,
            end_time: 1640995260,
            chain: Some("solana".to_string()),
        };
        assert!(empty_wallet_params.validate().is_err());

        // Test invalid chain
        let invalid_chain_params = UserIndicatorsParams {
            wallet_address: "********************************".to_string(),
            token_mint: "So111111111********************************".to_string(),
            pool_address: None,
            start_time: 1640995200,
            end_time: 1640995260,
            chain: Some("invalid_chain".to_string()),
        };
        assert!(invalid_chain_params.validate().is_err());
    }

    #[test]
    fn test_trend_lines_calculation() {
        let pills = vec![
            TradePill {
                timestamp: 1640995200,
                price_usd: 100.0,
                price_sol: 1.0,
                amount_usd: 1000.0,
                market_cap_usd: 100000.0,
                market_cap_sol: 1000.0,
                trade_type: "buy".to_string(),
                tx_signature: "test1".to_string(),
            },
            TradePill {
                timestamp: 1640995220,
                price_usd: 200.0,
                price_sol: 2.0,
                amount_usd: 2000.0,
                market_cap_usd: 200000.0,
                market_cap_sol: 2000.0,
                trade_type: "buy".to_string(),
                tx_signature: "test2".to_string(),
            },
            TradePill {
                timestamp: 1640995240,
                price_usd: 150.0,
                price_sol: 1.5,
                amount_usd: 1500.0,
                market_cap_usd: 150000.0,
                market_cap_sol: 1500.0,
                trade_type: "sell".to_string(),
                tx_signature: "test3".to_string(),
            },
        ];

        let trend_lines = PriceCalculator::calculate_trend_lines(&pills);

        // Check basic counts
        assert_eq!(trend_lines.buy_trade_count, 2);
        assert_eq!(trend_lines.sell_trade_count, 1);

        // Check volumes
        assert_eq!(trend_lines.total_buy_volume_usd, 3000.0);
        assert_eq!(trend_lines.total_sell_volume_usd, 1500.0);

        // Check average prices
        assert_eq!(trend_lines.average_buy_price, 150.0); // (100 + 200) / 2
        assert_eq!(trend_lines.average_sell_price, 150.0);

        // Check volume-weighted prices
        // Buy VWAP: (100*1000 + 200*2000) / (1000+2000) = 500000/3000 = 166.67
        assert!((trend_lines.buy_volume_weighted_price - 166.666666666666667).abs() < 0.001);
        assert_eq!(trend_lines.sell_volume_weighted_price, 150.0);
    }

    #[test]
    fn test_user_indicators_params_no_time_limit() {
        // Test that time range validation no longer has 30-day limit
        let large_time_range_params = UserIndicatorsParams {
            wallet_address: "********************************".to_string(),
            token_mint: "So111111111********************************".to_string(),
            pool_address: None,
            start_time: 1640995200,
            end_time: 1640995200 + (365 * 24 * 60 * 60), // 1 year range
            chain: Some("solana".to_string()),
        };
        assert!(large_time_range_params.validate().is_ok());
    }
}
