use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use serde::Deserialize;

use super::*;
use crate::{api::StorageState, token::info::TokenInfo};

#[derive(Debug, Deserialize)]
pub struct TokenInfoParams {
    pub tokens: String,
}

pub async fn get_tokens_info(
    State(state): State<StorageState>,
    Query(query): Query<TokenInfoParams>,
) -> Result<Json<Vec<TokenInfo>>, (StatusCode, Json<ErrorResponse>)> {
    // TODO: impl batch
    // TODO: update token info in background

    let tokens = query.tokens.split(",").map(|s| s.to_string()).collect::<Vec<String>>();
    let mut token_mints = Vec::new();
    for token in tokens {
        let token_mint = Pubkey::from_str(&token)
            .map_err(|e| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new(e.to_string()))))?;
        token_mints.push(token_mint);
    }

    let token_infos =
        crate::token::info::get_tokens_info(&state, &token_mints).await.map_err(|e| {
            (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
        })?;

    Ok(Json(token_infos.values().cloned().collect::<Vec<_>>()))
}
