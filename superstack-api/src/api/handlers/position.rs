use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use serde::Deserialize;

use super::*;
use crate::{
    api::{types::*, StorageState},
    portfolio::position::PositionManager,
    utils::{get_sol_price, lamports_to_token},
    wallet::SolanaWalletManager,
};

#[derive(Debug, Deserialize)]
pub struct PositionParams {
    pub wallet_address: String,
    pub token_mint: Option<String>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
    pub sort_by: Option<SortBy>,
    pub position_type: Option<PositionType>,
    pub position_status: Option<PositionStatus>,
    pub order_type: Option<PositionOrderType>,
    pub order_status: Option<PositionOrderStatus>,
}

pub async fn get_positions(
    State(state): State<StorageState>,
    Query(query): Query<PositionParams>,
) -> Result<Json<Positions>, (<PERSON>C<PERSON>, <PERSON><PERSON><ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let token_mint = query.token_mint;
    let position_type = query.position_type;
    let position_status = query.position_status;

    // First try normal position update
    if let Err(e) = PositionManager::update_positions(&state, &wallet_address).await {
        tracing::error!("Failed to update positions for wallet({:?}): {:?}", wallet_address, e);
    }

    // Check if position data looks inconsistent and auto-repair if needed
    // Only check for auto-repair if this is a direct position query (not filtered by token)
    if token_mint.is_none() {
        match PositionManager::needs_position_update(&state, &wallet_address).await {
            Ok(true) => {
                tracing::info!(
                    "Position data appears inconsistent for wallet {}, attempting auto-repair",
                    wallet_address
                );

                // Use a timeout to prevent hanging the API response
                let repair_result = tokio::time::timeout(
                    std::time::Duration::from_secs(30), // 30 second timeout
                    PositionManager::force_update_positions(&state, &wallet_address),
                )
                .await;

                match repair_result {
                    Ok(Ok(())) => {
                        tracing::info!(
                            "Successfully auto-repaired positions for wallet {}",
                            wallet_address
                        );
                    }
                    Ok(Err(e)) => {
                        tracing::error!(
                            "Auto-repair failed for wallet {}: {:?}",
                            wallet_address,
                            e
                        );
                    }
                    Err(_) => {
                        tracing::warn!(
                            "Auto-repair timed out for wallet {}, continuing with existing data",
                            wallet_address
                        );
                    }
                }
            }
            Ok(false) => {
                // Position data looks consistent, no action needed
                tracing::debug!("Position data appears consistent for wallet {}", wallet_address);
            }
            Err(e) => {
                tracing::error!(
                    "Failed to check position consistency for wallet {}: {:?}",
                    wallet_address,
                    e
                );
            }
        }
    }

    // Try to parse as Solana address for account value calculation
    // If it's an EVM address, skip the account value calculation
    let _latest_account_value = if let Ok(wallet_pubkey) = Pubkey::from_str(&wallet_address) {
        SolanaWalletManager::get_account_value(&state, &wallet_pubkey)
            .await
            .map_err(|e| {
                tracing::warn!(
                    "Failed to get account value for Solana wallet {}: {}",
                    wallet_address,
                    e
                );
                // Don't fail the request, just log the warning
            })
            .unwrap_or_default()
    } else {
        // For EVM addresses, we don't calculate Solana account value
        tracing::debug!(
            "Skipping Solana account value calculation for non-Solana address: {}",
            wallet_address
        );
        0.0
    };

    if let Some(token_mint) = token_mint {
        // Keep token_mint in original format for Solana addresses
        // Only normalize EVM addresses to lowercase if needed
        let token_mint = if token_mint.starts_with("0x") {
            token_mint.to_lowercase()
        } else {
            token_mint // Keep Solana Base58 addresses as-is
        };
        let sol_price = get_sol_price().await;

        let histories = match state.get_histories_by_token(&wallet_address, &token_mint).await {
            Ok(histories) => histories,
            Err(e) => {
                tracing::error!(
                    "Failed to get histories for wallet({:?}) and token({:?}): {:?}",
                    wallet_address,
                    token_mint,
                    e
                );
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new("Failed to get histories")),
                ));
            }
        };
        let mut token_position_data = TokenPositionData::default();
        for history in histories {
            // Only support SOL for now
            token_position_data.total_invested_usd += history.cost_usd +
                lamports_to_token(
                    history.cost_native_amount as u64,
                    history.native_decimals as u8,
                ) * sol_price;
            token_position_data.total_invested_amount += history.bought_amount;
            token_position_data.total_invested_ui_amount +=
                lamports_to_token(history.bought_amount as u64, history.token_decimals as u8);
            token_position_data.total_sold_usd += history.earnings_usd +
                lamports_to_token(
                    history.earnings_native_amount as u64,
                    history.native_decimals as u8,
                ) * sol_price;
            token_position_data.total_sold_amount += history.sold_amount;
            token_position_data.total_sold_ui_amount +=
                lamports_to_token(history.sold_amount as u64, history.token_decimals as u8);
            token_position_data.total_pnl_usd += history.pnl_usd;
        }

        // Calculate remaining amounts for closed positions based on history data
        token_position_data.total_remaining_amount =
            token_position_data.total_invested_amount - token_position_data.total_sold_amount;
        token_position_data.total_remaining_ui_amount =
            token_position_data.total_invested_ui_amount - token_position_data.total_sold_ui_amount;

        // For closed positions, if remaining amount <= 0, set remaining_usd to 0
        // Otherwise calculate USD value using current token price
        if token_position_data.total_remaining_amount <= 0 {
            token_position_data.total_remaining_usd = 0.0;
            token_position_data.total_remaining_amount = 0;
            token_position_data.total_remaining_ui_amount = 0.0;
        } else {
            // Get current token price to calculate remaining USD value
            let token_usd_price =
                match state.indexer_data_provider.get_token(Chain::Solana, &token_mint).await {
                    Ok(Some(token_statistic)) => token_statistic.usd_price,
                    _ => {
                        tracing::warn!(
                            "Failed to get token price for remaining USD calculation: {}",
                            token_mint
                        );
                        0.0
                    }
                };
            token_position_data.total_remaining_usd =
                token_position_data.total_remaining_ui_amount * token_usd_price;
        }

        // Calculate total PnL percentage
        token_position_data.total_pnl_percentage = if token_position_data.total_invested_usd > 0.0 {
            (token_position_data.total_sold_usd + token_position_data.total_remaining_usd -
                token_position_data.total_invested_usd) /
                token_position_data.total_invested_usd
        } else {
            0.0
        };

        // Try to find position across all chains for the specified token
        let mut position_found = None;

        // Parse wallet address and get potential address formats
        let (solana_pubkey, evm_address) = match crate::utils::parse_wallet_address(&wallet_address)
        {
            Ok(addresses) => {
                tracing::info!(
                    "Address parsing result for {}: solana_pubkey={:?}, evm_address={:?}",
                    wallet_address,
                    addresses.0.map(|p| p.to_string()),
                    addresses.1
                );
                addresses
            }
            Err(e) => {
                tracing::error!("Failed to parse wallet address {}: {}", wallet_address, e);
                (None, None)
            }
        };

        for chain in [0, 1, 2] {
            // Solana, Hypercore, HyperEVM
            // For each chain, try different address formats
            let addresses_to_try = match chain {
                0 | 1 => vec![wallet_address.clone()], // Solana/Hypercore: use original address
                2 => {
                    // HyperEVM: try both original address and converted EVM address
                    let mut addresses = vec![wallet_address.clone()];
                    if let Some(ref evm_addr) = evm_address {
                        addresses.push(evm_addr.clone());
                    }
                    addresses
                }
                _ => vec![wallet_address.clone()],
            };

            for addr in addresses_to_try {
                if let Ok(Some(db_position)) = state.get_position(&addr, &token_mint, chain).await {
                    position_found = Some(db_position);
                    break;
                }
            }

            if position_found.is_some() {
                break;
            }
        }

        // If still no position found, try a more comprehensive search
        // This handles cases where address conversion doesn't work as expected
        if position_found.is_none() {
            tracing::debug!("No position found with standard address formats, trying comprehensive search for wallet: {} and token: {}", wallet_address, token_mint);

            // Try to find any position with this token across all chains and addresses
            for chain in [0, 1, 2] {
                // Get all positions for this chain and filter by token
                if let Ok(all_positions) =
                    state.get_positions_by_chain(&wallet_address, chain).await
                {
                    for pos in all_positions {
                        if pos.token_mint == token_mint {
                            tracing::debug!("Found matching position via comprehensive search: wallet={}, token={}, chain={}", pos.wallet_address, pos.token_mint, pos.chain);
                            position_found = Some(pos);
                            break;
                        }
                    }
                }

                // Also try with EVM address if available
                if position_found.is_none() {
                    if let Some(ref evm_addr) = evm_address {
                        if evm_addr != &wallet_address {
                            if let Ok(all_positions) =
                                state.get_positions_by_chain(evm_addr, chain).await
                            {
                                for pos in all_positions {
                                    if pos.token_mint == token_mint {
                                        tracing::debug!("Found matching position via EVM address search: wallet={}, token={}, chain={}", pos.wallet_address, pos.token_mint, pos.chain);
                                        position_found = Some(pos);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }

                if position_found.is_some() {
                    break;
                }
            }
        }

        match position_found {
            Some(db_position) => {
                let position = match Position::construct(&state, db_position.clone()).await {
                    Ok(position) => position,
                    Err(e) if e.to_string().contains("Token not found") => {
                        tracing::warn!("Token metadata not found for position, creating fallback position: wallet={}, token={}, chain={}",
                            db_position.wallet_address, db_position.token_mint, db_position.chain);

                        // Create a fallback position with basic information when token metadata is
                        // missing
                        let short_symbol = if db_position.token_mint.len() >= 10 {
                            format!(
                                "{}...{}",
                                &db_position.token_mint[0..6],
                                &db_position.token_mint[db_position.token_mint.len() - 4..]
                            )
                        } else {
                            db_position.token_mint.clone()
                        };

                        let chain = match db_position.chain {
                            0 => Chain::Solana,
                            1 => Chain::Hypercore,
                            2 => Chain::HyperEvm,
                            _ => Chain::Solana,
                        };

                        Position {
                            wallet_address: db_position.wallet_address,
                            token_mint: db_position.token_mint,
                            token_metadata: ApiTokenMetadata {
                                name: format!("Unknown Token ({})", short_symbol),
                                symbol: short_symbol,
                                decimals: 18, // Default decimals
                                image_url: None,
                                chain,
                            },
                            bought_amount: db_position.bought_amount,
                            bought_ui_amount: lamports_to_token(
                                db_position.bought_amount as u64,
                                18,
                            ),
                            sold_amount: db_position.sold_amount,
                            sold_ui_amount: lamports_to_token(db_position.sold_amount as u64, 18),
                            cost_usd: db_position.cost_usd,
                            earnings_usd: db_position.earnings_usd,
                            remaining_usd: 0.0, // Cannot calculate without token price
                            current_holdings_cost_usd: db_position.cost_usd,
                            sell_initials_ui_amount: 0.0,
                            sell_initials_amount: 0,
                            sell_initial_usd: 0.0,
                            pnl_usd: db_position.earnings_usd - db_position.cost_usd,
                            pnl_percentage: if db_position.cost_usd > 0.0 {
                                (db_position.earnings_usd - db_position.cost_usd) /
                                    db_position.cost_usd
                            } else {
                                0.0
                            },
                            buy_operations: vec![], // Cannot construct without token metadata
                            sell_operations: vec![], // Cannot construct without token metadata
                            open_time: db_position.open_time as u64,
                        }
                    }
                    Err(e) => {
                        tracing::error!("Failed to construct position for token {}: {:?}, returning empty result", token_mint, e);
                        // Return empty result for other errors
                        let response = Positions {
                            token_position_data: Some(token_position_data),
                            positions: vec![],
                        };
                        return Ok(Json(response));
                    }
                };

                token_position_data.total_invested_usd += position.cost_usd;
                token_position_data.total_invested_amount += position.bought_amount;
                token_position_data.total_invested_ui_amount += position.bought_ui_amount;
                token_position_data.total_sold_usd += position.earnings_usd;
                token_position_data.total_sold_amount += position.sold_amount;
                token_position_data.total_sold_ui_amount += position.sold_ui_amount;
                token_position_data.total_remaining_usd = position.remaining_usd;
                token_position_data.total_remaining_amount =
                    position.bought_amount - position.sold_amount;
                token_position_data.total_remaining_ui_amount =
                    position.bought_ui_amount - position.sold_ui_amount;
                token_position_data.total_pnl_usd += position.pnl_usd;
                token_position_data.total_pnl_percentage = if token_position_data.total_invested_usd >
                    0.0
                {
                    (token_position_data.total_sold_usd + token_position_data.total_remaining_usd -
                        token_position_data.total_invested_usd) /
                        token_position_data.total_invested_usd
                } else {
                    0.0
                };

                let response = Positions {
                    token_position_data: Some(token_position_data),
                    positions: vec![position],
                };
                Ok(Json(response))
            }
            None => {
                let response =
                    Positions { token_position_data: Some(token_position_data), positions: vec![] };
                Ok(Json(response))
            }
        }
    } else {
        // Get positions from all chains (Solana, Hypercore, HyperEVM)
        // Parse wallet address and get potential address formats
        let (solana_pubkey, evm_address) = match crate::utils::parse_wallet_address(&wallet_address)
        {
            Ok(addresses) => addresses,
            Err(e) => {
                tracing::error!("Failed to parse wallet address {}: {}", wallet_address, e);
                (None, None)
            }
        };

        // Collect positions from all address formats
        let mut all_positions = Vec::new();

        // Query with original address
        if let Ok(positions) = state.get_positions(&wallet_address).await {
            all_positions.extend(positions);
        }

        // Query with EVM address if available and different from original
        if let Some(ref evm_addr) = evm_address {
            if evm_addr != &wallet_address {
                if let Ok(positions) = state.get_positions(evm_addr).await {
                    all_positions.extend(positions);
                }
            }
        }

        // Additional cross-chain address queries for better compatibility
        // If we still don't have positions, try querying all chains with different address formats
        if all_positions.is_empty() {
            tracing::debug!("No positions found with direct address queries, trying cross-chain lookup for wallet: {}", wallet_address);

            for chain in [0, 1, 2] {
                // Solana, Hypercore, HyperEVM
                // Try querying all positions for this chain and filter by potential address matches
                if let Ok(chain_positions) =
                    state.get_positions_by_chain(&wallet_address, chain).await
                {
                    tracing::debug!(
                        "Found {} positions for wallet {} on chain {}",
                        chain_positions.len(),
                        wallet_address,
                        chain
                    );
                    all_positions.extend(chain_positions);
                }

                // If we have an EVM address, also try it for this chain
                if let Some(ref evm_addr) = evm_address {
                    if evm_addr != &wallet_address {
                        if let Ok(chain_positions) =
                            state.get_positions_by_chain(evm_addr, chain).await
                        {
                            tracing::debug!(
                                "Found {} positions for EVM address {} on chain {}",
                                chain_positions.len(),
                                evm_addr,
                                chain
                            );
                            all_positions.extend(chain_positions);
                        }
                    }
                }
            }
        }

        match all_positions.is_empty() {
            false => {
                let mut positions = Vec::new();
                for db_position in all_positions {
                    let position = match Position::construct(&state, db_position).await {
                        Ok(position) => position,
                        Err(e) => {
                            tracing::error!(
                                "Failed to construct position for wallet({:?}): {:?}",
                                wallet_address,
                                e
                            );
                            continue;
                        }
                    };

                    // Apply position status filter
                    if let Some(status) = &position_status {
                        let is_active = position.bought_amount > position.sold_amount;
                        match status {
                            PositionStatus::Active | PositionStatus::Open => {
                                if !is_active {
                                    continue;
                                }
                            }
                            PositionStatus::Sold | PositionStatus::Closed => {
                                if is_active {
                                    continue;
                                }
                            }
                        }
                    }

                    // Apply position type filter (currently only Spot is supported)
                    if let Some(pos_type) = &position_type {
                        match pos_type {
                            PositionType::Spot => {
                                // All current positions are spot positions
                            }
                            PositionType::Perps => {
                                // Skip all positions since perps are not implemented yet
                                continue;
                            }
                        }
                    }

                    positions.push(position);
                }
                let response = Positions { token_position_data: None, positions };
                Ok(Json(response))
            }
            true => {
                // No positions found
                let response = Positions { token_position_data: None, positions: Vec::new() };
                Ok(Json(response))
            }
        }
    }
}

// Note: force_update_positions and check_position_status handlers removed
// as auto-repair is now integrated into the main /api/positions endpoint.
// The underlying PositionManager methods are still available for internal use.

pub async fn get_open_orders(
    State(state): State<StorageState>,
    Query(query): Query<PositionParams>,
) -> Result<Json<Vec<OpenOrder>>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let token_mint = query.token_mint;
    let order_type = query.order_type;
    let order_status = query.order_status;

    let open_orders = match token_mint {
        Some(token_mint) => {
            // Keep token_mint in original format for Solana addresses
            // Only normalize EVM addresses to lowercase if needed
            let token_mint = if token_mint.starts_with("0x") {
                token_mint.to_lowercase()
            } else {
                token_mint // Keep Solana Base58 addresses as-is
            };
            state
                .get_active_orders_for_wallet_and_token(&wallet_address, &token_mint)
                .await
                .map_err(|e| {
                    tracing::error!(
                        "Failed to get active orders for wallet({:?}) and token({:?}): {:?}",
                        wallet_address,
                        token_mint,
                        e
                    );
                    (
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse::new("Failed to get active orders")),
                    )
                })?
        }
        None => state.get_active_orders_for_wallet(&wallet_address).await.map_err(|e| {
            tracing::error!(
                "Failed to get active orders for wallet({:?}): {:?}",
                wallet_address,
                e
            );
            (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to get active orders")))
        })?,
    };

    let sol_price = get_sol_price().await;

    let mut result = Vec::new();
    for order in open_orders {
        // Get token metadata first to check if it's a trench token
        let token1 =
            match state.indexer_db.get_token_metadata(Chain::Solana, &order.token_mint).await {
                Ok(Some(token)) => token,
                Ok(None) => {
                    tracing::error!("Failed to get token for order({:?})", order);
                    continue;
                }
                Err(e) => {
                    tracing::error!("Failed to get token for order({:?}): {:?}", order, e);
                    continue;
                }
            };

        // Apply order type filter based on token type
        if let Some(ord_type) = &order_type {
            match ord_type {
                PositionOrderType::Spot => {
                    // Only include non-trench tokens (spot trading)
                    if token1.is_trench_token {
                        continue;
                    }
                }
                PositionOrderType::Perps => {
                    // Only include trench tokens (perpetual contracts)
                    if !token1.is_trench_token {
                        continue;
                    }
                }
            }
        }

        // Apply order status filter
        if let Some(status) = &order_status {
            match status {
                PositionOrderStatus::Open => {
                    // Only include active orders (not cancelled or completed)
                    if order.is_cancelled || order.is_completed {
                        continue;
                    }
                }
                PositionOrderStatus::Closed => {
                    // Only include cancelled or completed orders
                    if !order.is_cancelled && !order.is_completed {
                        continue;
                    }
                }
            }
        }
        let token2 =
            match state.indexer_db.get_token_metadata(Chain::Solana, &order.base_mint).await {
                Ok(Some(token)) => token,
                Ok(None) => {
                    tracing::error!("Failed to get token for order({:?})", order);
                    continue;
                }
                Err(e) => {
                    tracing::error!("Failed to get token for order({:?}): {:?}", order, e);
                    continue;
                }
            };
        let pair_label = format!("{}/{}", token1.symbol, token2.symbol);

        match OpenOrder::construct(order.clone(), pair_label, sol_price, token1).await {
            Ok(open_order) => result.push(open_order),
            Err(e) => {
                tracing::error!("Failed to construct open order for order({:?}): {:?}", order, e);
                continue;
            }
        }
    }

    Ok(Json(result))
}

pub async fn get_order_histories(
    State(state): State<StorageState>,
    Query(query): Query<PositionParams>,
) -> Result<Json<Vec<OrderHistory>>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let token_mint = query.token_mint;
    let limit = query.limit.unwrap_or(30);
    let offset = query.offset.unwrap_or(0);
    let order_type = query.order_type;
    let order_status = query.order_status;

    // Get all trades for the wallet (includes both limit orders and market orders)
    let trades = match token_mint {
        Some(token_mint) => {
            // Keep token_mint in original format for Solana addresses
            // Only normalize EVM addresses to lowercase if needed
            let token_mint = if token_mint.starts_with("0x") {
                token_mint.to_lowercase()
            } else {
                token_mint // Keep Solana Base58 addresses as-is
            };
            state
                .get_trades_for_wallet_and_token(
                    &wallet_address,
                    &token_mint,
                    limit as i64,
                    offset as i64,
                )
                .await
                .map_err(|e| {
                    tracing::error!(
                        "Failed to get trades for wallet({:?}) and token({:?}): {:?}",
                        wallet_address,
                        token_mint,
                        e
                    );
                    (
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse::new("Failed to get trades for order history")),
                    )
                })?
        }
        None => {
            state.get_trades(&wallet_address, limit as i64, offset as i64).await.map_err(|e| {
                tracing::error!("Failed to get trades for wallet({:?}): {:?}", wallet_address, e);
                (
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new("Failed to get trades for order history")),
                )
            })?
        }
    };

    let sol_price = get_sol_price().await;

    let mut result = Vec::new();
    for trade in trades {
        // Check if this trade has an associated order
        let order_info = if let Some(order_id) = &trade.order_id {
            state.get_order(order_id).await.ok().flatten()
        } else {
            None
        };

        // Get token metadata first to check if it's a trench token
        let token1 =
            match state.indexer_db.get_token_metadata(Chain::Solana, &trade.token_mint).await {
                Ok(Some(token)) => token,
                Ok(None) => {
                    tracing::error!("Failed to get token for trade({:?})", trade);
                    continue;
                }
                Err(e) => {
                    tracing::error!("Failed to get token for trade({:?}): {:?}", trade, e);
                    continue;
                }
            };

        // Apply order type filter based on token type
        if let Some(ord_type) = &order_type {
            match ord_type {
                PositionOrderType::Spot => {
                    // Only include non-trench tokens (spot trading)
                    if token1.is_trench_token {
                        continue;
                    }
                }
                PositionOrderType::Perps => {
                    // Only include trench tokens (perpetual contracts)
                    if !token1.is_trench_token {
                        continue;
                    }
                }
            }
        }

        // Apply order status filter (only applies to limit orders with order records)
        if let Some(status) = &order_status {
            if let Some(ref order) = order_info {
                match status {
                    PositionOrderStatus::Open => {
                        // Only include active orders (not cancelled or completed)
                        if order.is_cancelled || order.is_completed {
                            continue;
                        }
                    }
                    PositionOrderStatus::Closed => {
                        // Only include cancelled or completed orders
                        if !order.is_cancelled && !order.is_completed {
                            continue;
                        }
                    }
                }
            } else {
                // For market orders (no order record), they are always "closed" (filled
                // immediately)
                match status {
                    PositionOrderStatus::Open => {
                        // Market orders are never "open", skip them
                        continue;
                    }
                    PositionOrderStatus::Closed => {
                        // Market orders are always "closed", include them
                    }
                }
            }
        }

        let token2 =
            match state.indexer_db.get_token_metadata(Chain::Solana, &trade.base_mint).await {
                Ok(Some(token)) => token,
                Ok(None) => {
                    tracing::error!("Failed to get base token for trade({:?})", trade);
                    continue;
                }
                Err(e) => {
                    tracing::error!("Failed to get base token for trade({:?}): {:?}", trade, e);
                    continue;
                }
            };

        let pair_label = format!("{}/{}", token1.symbol, token2.symbol);

        // Construct OrderHistory from trade data (with optional order info)
        match OrderHistory::construct_from_trade(trade, order_info, pair_label, sol_price, token1)
            .await
        {
            Ok(order_history) => result.push(order_history),
            Err(e) => {
                tracing::error!("Failed to construct order history from trade: {:?}", e);
                continue;
            }
        }
    }

    Ok(Json(result))
}

pub async fn get_position_history(
    State(state): State<StorageState>,
    Query(query): Query<PositionParams>,
) -> Result<Json<Vec<PositionHistory>>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let limit = query.limit.unwrap_or(30);
    let offset = query.offset.unwrap_or(0);
    let sort_by = query.sort_by.unwrap_or(SortBy::CloseTime);

    let histories = match sort_by {
        SortBy::OpenTime => state
            .get_histories_ordered_by_open_time(
                &wallet_address.to_string(),
                limit as i64,
                offset as i64,
            )
            .await
            .map_err(|e| {
                tracing::error!("Failed to get history for wallet({:?}) : {:?}", wallet_address, e);
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to get history")))
            })?,
        SortBy::CloseTime => state
            .get_histories_ordered_by_close_time(
                &wallet_address.to_string(),
                limit as i64,
                offset as i64,
            )
            .await
            .map_err(|e| {
                tracing::error!("Failed to get history for wallet({:?}) : {:?}", wallet_address, e);
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to get history")))
            })?,
        SortBy::Pnl => state
            .get_histories_ordered_by_pnl(&wallet_address.to_string(), limit as i64, offset as i64)
            .await
            .map_err(|e| {
                tracing::error!("Failed to get history for wallet({:?}) : {:?}", wallet_address, e);
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to get history")))
            })?,
        SortBy::PnlPercentage => state
            .get_histories_ordered_by_pnl_percentage(
                &wallet_address.to_string(),
                limit as i64,
                offset as i64,
            )
            .await
            .map_err(|e| {
                tracing::error!("Failed to get history for wallet({:?}) : {:?}", wallet_address, e);
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to get history")))
            })?,
    };

    let mut result = Vec::new();
    for db_history in histories {
        let history = match PositionHistory::construct(&state, db_history).await {
            Ok(history) => history,
            Err(e) => {
                tracing::error!("Failed to construct history: {:?}", e);
                continue;
            }
        };
        result.push(history);
    }

    Ok(Json(result))
}

pub async fn get_trade_histories(
    State(state): State<StorageState>,
    Query(query): Query<PositionParams>,
) -> Result<Json<Vec<TradeHistory>>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let token_mint = query.token_mint;
    let limit = query.limit.unwrap_or(30);
    let offset = query.offset.unwrap_or(0);
    let order_type = query.order_type;

    let trades = match token_mint {
        Some(token_mint) => state
            .get_trades_for_wallet_and_token(
                &wallet_address.to_string(),
                &token_mint,
                limit as i64,
                offset as i64,
            )
            .await
            .map_err(|e| {
                tracing::error!(
                    "Failed to get trade histories for wallet({:?}) and token({:?}): {:?}",
                    wallet_address,
                    token_mint,
                    e
                );
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to get trade histories")))
            })?,
        None => state
            .get_trades(&wallet_address.to_string(), limit as i64, offset as i64)
            .await
            .map_err(|e| {
                tracing::error!(
                    "Failed to get trade histories for wallet({:?}): {:?}",
                    wallet_address,
                    e
                );
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to get trade histories")))
            })?,
    };

    let sol_price = get_sol_price().await;

    let mut result = Vec::new();
    for trade in trades {
        // TODO: cache token
        // Convert trade.chain to Chain enum
        let trade_chain = match trade.chain {
            0 => Chain::Solana,
            1 => Chain::Hypercore,
            2 => Chain::HyperEvm,
            _ => Chain::Solana, // Default to Solana for unknown chains
        };

        let token1 = match state.indexer_db.get_token_metadata(trade_chain, &trade.token_mint).await
        {
            Ok(Some(token)) => token,
            Ok(None) => {
                tracing::error!("Failed to get token for trade({:?})", trade);
                continue;
            }
            Err(e) => {
                tracing::error!("Failed to get token for trade({:?}): {:?}", trade, e);
                continue;
            }
        };

        // Apply order type filter based on token type
        if let Some(ord_type) = &order_type {
            match ord_type {
                PositionOrderType::Spot => {
                    // Only include non-trench tokens (spot trading)
                    if token1.is_trench_token {
                        continue;
                    }
                }
                PositionOrderType::Perps => {
                    // Only include trench tokens (perpetual contracts)
                    if !token1.is_trench_token {
                        continue;
                    }
                }
            }
        }
        let token2 =
            match state.indexer_db.get_token_metadata(Chain::Solana, &trade.base_mint).await {
                Ok(Some(token)) => token,
                Ok(None) => {
                    tracing::error!("Failed to get token for trade({:?})", trade);
                    continue;
                }
                Err(e) => {
                    tracing::error!("Failed to get token for trade({:?}): {:?}", trade, e);
                    continue;
                }
            };
        let pair_label = format!("{}/{}", token1.symbol, token2.symbol);

        match TradeHistory::construct(trade.clone(), pair_label, sol_price, token1).await {
            Ok(trade_history) => result.push(trade_history),
            Err(e) => {
                tracing::error!("Failed to construct trade history: {:?}", e);
                continue;
            }
        }
    }

    Ok(Json(result))
}

#[derive(Debug, Deserialize)]
pub struct PnlShareParams {
    pub wallet_address: String,
    pub token_mint: String,
}

pub async fn get_pnl_share(
    State(state): State<StorageState>,
    Query(query): Query<PnlShareParams>,
) -> Result<Json<PnlShare>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = Pubkey::from_str(&query.wallet_address).map_err(|e| {
        tracing::error!("Failed to parse wallet address: {:?}", e);
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to parse wallet address")))
    })?;

    let sol_price = get_sol_price().await;

    // Background image selection is now handled by frontend
    let background_image_url: Option<String> = None;

    // Use cross-chain method to find the token and get its chain
    // todo: stat from redis
    let mut token_found = None;
    for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
        if let Ok(Some(token)) =
            state.indexer_data_provider.get_token(chain, &query.token_mint).await
        {
            token_found = Some(token);
            break;
        }
    }

    let token_statistic = match token_found {
        Some(token) => token,
        None => {
            tracing::error!("Failed to get token metadata for token({})", query.token_mint);
            return Err((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Token not found"))));
        }
    };

    if let Ok(Some(db_position)) =
        state.get_solana_position(&wallet_address.to_string(), &query.token_mint).await
    {
        let position = match Position::construct(&state, db_position).await {
            Ok(position) => position,
            Err(e) => {
                tracing::error!("Failed to construct position: {:?}", e);
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to construct position")),
                ));
            }
        };
        let pnl_share = PnlShare {
            token_mint: query.token_mint.clone(),
            name: token_statistic.name.clone(),
            symbol: token_statistic.symbol.clone(),
            image_url: token_statistic.image.clone(), // todo: translate url with path
            pnl_usd: position.pnl_usd,
            pnl_percentage: position.pnl_percentage,
            background_image_url: background_image_url.clone(),
        };
        return Ok(Json(pnl_share));
    }

    let history = state
        .get_latest_history(&wallet_address.to_string(), &query.token_mint)
        .await
        .map_err(|e| {
            tracing::error!("Failed to get pnl share for token({}): {:?}", query.token_mint, e);
            (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Failed to get pnl share")))
        })?;

    if let Some(history) = history {
        let pnl_share = PnlShare {
            token_mint: query.token_mint.clone(),
            name: token_statistic.name.clone(),
            symbol: token_statistic.symbol.clone(),
            image_url: token_statistic.image.clone(), // todo: translate url with path
            pnl_usd: history.pnl_usd,
            pnl_percentage: history.pnl_percentage,
            background_image_url: background_image_url.clone(),
        };
        Ok(Json(pnl_share))
    } else {
        let pnl_share = PnlShare {
            token_mint: query.token_mint,
            name: token_statistic.name,
            symbol: token_statistic.symbol,
            image_url: token_statistic.image, // todo: translate url with path
            pnl_usd: 0.0,
            pnl_percentage: 0.0,
            background_image_url,
        };
        Ok(Json(pnl_share))
    }
}
