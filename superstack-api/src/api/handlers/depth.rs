use axum::{
    extract::{Path, Query, State},
    http::<PERSON><PERSON><PERSON>,
    J<PERSON>,
};
use serde::{Deserialize, Serialize};
use std::collections::BTreeMap;

use crate::{
    api::{handlers::ErrorResponse, StorageState},
    constant::{SOL_MINT, USDC_MINT},
    utils::{get_sol_price, lamports_to_token},
};
use superstack_data::postgres::enums::Chain;

// Constants for depth chart generation
const PRICE_LEVELS_PER_SIDE: i32 = 20;
const PRICE_STEP_PERCENTAGE: f64 = 0.02; // 2%
const LIQUIDITY_BASE_RATIO: f64 = 0.1; // 10% of total liquidity
const DISTANCE_DECAY_FACTOR: f64 = 0.5;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DepthEntry {
    pub price_usd: f64,
    pub price_native: f64,
    pub market_cap_usd: f64,
    pub market_cap_native: f64,
    pub liquidity_usd: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DepthChartResponse {
    pub data: Vec<DepthEntry>,
}

async fn generate_depth_from_trading_data(
    state: &StorageState,
    pool_metadata: &superstack_data::postgres::indexer::pool_metadata::PoolMetadata,
    current_price_usd: f64,
    current_price_native: f64,
    current_market_cap_usd: f64,
    current_market_cap_native: f64,
    sol_price: f64,
    liquidity: f64,
) -> Vec<DepthEntry> {
    let mut depth_data = Vec::new();

    // Try to get real order book data first
    let mut has_real_data = false;
    if let Ok(token_mint) = pool_metadata.token_address.parse::<solana_sdk::pubkey::Pubkey>() {
        if let Ok(orders) = state.get_active_orders_for_token(&token_mint.to_string()).await {
            if !orders.is_empty() {
                // Generate depth from real orders
                depth_data = generate_depth_from_orders(
                    orders,
                    current_price_usd,
                    current_price_native,
                    current_market_cap_usd,
                    current_market_cap_native,
                    sol_price,
                );
                has_real_data = true;
            }
        }
    }

    // If no real order data, use trading volume distribution
    if !has_real_data {
        depth_data = generate_depth_from_liquidity(
            current_price_usd,
            current_price_native,
            current_market_cap_usd,
            current_market_cap_native,
            liquidity,
        );
    }

    depth_data
}

#[derive(Debug, Deserialize)]
pub struct DepthQuery {
    pub chain: Option<String>,
}

pub async fn get_depth_chart(
    State(state): State<StorageState>,
    Path(pool_address): Path<String>,
    Query(query): Query<DepthQuery>,
) -> Result<Json<DepthChartResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Parse chain parameter, default to Solana
    let chain = match query.chain.as_deref() {
        Some("solana") | None => Chain::Solana,
        Some("hypercore") => Chain::Hypercore,
        Some("hyperevm") => Chain::HyperEvm,
        Some(invalid) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!(
                    "Invalid chain: {}. Supported chains: solana, hypercore, hyperevm",
                    invalid
                ))),
            ));
        }
    };

    let sol_price = get_sol_price().await;

    // Get pool metadata and statistics separately using existing methods
    let pool_metadata = state
        .indexer_db
        .get_pool_metadata(chain, &pool_address)
        .await
        .map_err(|_e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to get pool metadata")),
            )
        })?
        .ok_or((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Pool metadata not found"))))?;

    let pool_statistics = state
        .indexer_data_provider
        .get_pool(chain, &pool_address)
        .await
        .map_err(|_e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to get pool statistics")),
            )
        })?
        .ok_or((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Pool statistics not found"))))?;

    let pool_data = (pool_metadata, pool_statistics);

    // Get current price and market cap from pool statistics (already in USD)
    let current_price_usd = pool_data.1.usd_price;
    let current_price_native = current_price_usd / sol_price;
    let current_market_cap_usd = pool_data.1.usd_market_cap;
    let current_market_cap_native = current_market_cap_usd / sol_price;

    // Generate depth data from real trading activity
    let depth_data = generate_depth_from_trading_data(
        &state,
        &pool_data.0,
        current_price_usd,
        current_price_native,
        current_market_cap_usd,
        current_market_cap_native,
        sol_price,
        pool_data.1.usd_liquidity,
    )
    .await;

    Ok(Json(DepthChartResponse { data: depth_data }))
}

fn create_depth_entry(
    level: i32,
    liquidity_usd: f64,
    current_price_usd: f64,
    current_price_native: f64,
    current_market_cap_usd: f64,
    current_market_cap_native: f64,
) -> Option<DepthEntry> {
    if liquidity_usd <= 0.0 {
        return None;
    }

    let price_multiplier = 1.0 + (level as f64 * PRICE_STEP_PERCENTAGE);

    let level_price_usd = current_price_usd * price_multiplier;
    let level_price_native = current_price_native * price_multiplier;
    let level_market_cap_usd = current_market_cap_usd * price_multiplier;
    let level_market_cap_native = current_market_cap_native * price_multiplier;

    // Skip levels with non-positive values
    if level_price_usd <= 0.0 ||
        level_price_native <= 0.0 ||
        level_market_cap_usd <= 0.0 ||
        level_market_cap_native <= 0.0
    {
        return None;
    }

    Some(DepthEntry {
        price_usd: level_price_usd,
        price_native: level_price_native,
        market_cap_usd: level_market_cap_usd,
        market_cap_native: level_market_cap_native,
        liquidity_usd,
    })
}

fn generate_depth_from_orders(
    orders: Vec<crate::models::DbOrder>,
    current_price_usd: f64,
    current_price_native: f64,
    current_market_cap_usd: f64,
    current_market_cap_native: f64,
    sol_price: f64,
) -> Vec<DepthEntry> {
    let mut depth_levels = BTreeMap::new();

    for order in orders {
        let order_price_usd = calculate_order_price_usd(&order, sol_price);
        if order_price_usd <= 0.0 || current_price_usd <= 0.0 {
            continue;
        }

        let price_level =
            ((order_price_usd / current_price_usd - 1.0) / PRICE_STEP_PERCENTAGE).round() as i32;

        // Skip orders too far from current price
        if price_level.abs() > PRICE_LEVELS_PER_SIDE {
            continue;
        }

        let remaining_liquidity_usd = calculate_remaining_order_size_usd(&order, sol_price);
        if remaining_liquidity_usd > 0.0 {
            *depth_levels.entry(price_level).or_insert(0.0) += remaining_liquidity_usd;
        }
    }

    // Convert to DepthEntry format
    depth_levels
        .into_iter()
        .filter_map(|(level, liquidity_usd)| {
            create_depth_entry(
                level,
                liquidity_usd,
                current_price_usd,
                current_price_native,
                current_market_cap_usd,
                current_market_cap_native,
            )
        })
        .collect()
}

fn generate_depth_from_liquidity(
    current_price_usd: f64,
    current_price_native: f64,
    current_market_cap_usd: f64,
    current_market_cap_native: f64,
    liquidity: f64,
) -> Vec<DepthEntry> {
    let mut depth_data = Vec::new();

    // Generate realistic depth distribution based on liquidity
    for i in -PRICE_LEVELS_PER_SIDE..PRICE_LEVELS_PER_SIDE {
        let distance_from_current = (i as f64).abs();

        // More realistic liquidity distribution:
        // - Higher liquidity near current price
        // - Exponential decay with distance
        // - Some randomness for realism
        let base_liquidity = liquidity * LIQUIDITY_BASE_RATIO;
        let decay_factor = (-distance_from_current * DISTANCE_DECAY_FACTOR).exp();
        let liquidity_usd = base_liquidity * decay_factor;

        if let Some(entry) = create_depth_entry(
            i,
            liquidity_usd,
            current_price_usd,
            current_price_native,
            current_market_cap_usd,
            current_market_cap_native,
        ) {
            depth_data.push(entry);
        }
    }

    depth_data
}

// Helper functions for real data processing
fn calculate_order_price_usd(order: &crate::models::DbOrder, sol_price: f64) -> f64 {
    // Calculate price from order amounts
    let token_amount = lamports_to_token(order.token_amount as u64, order.token_decimals as u8);
    let base_amount = lamports_to_token(order.base_amount as u64, order.base_decimals as u8);

    if token_amount == 0.0 {
        return 0.0;
    }

    let price_in_base = base_amount / token_amount;

    // Convert to USD based on base token
    match order.base_mint.as_str() {
        SOL_MINT => price_in_base * sol_price, // SOL
        USDC_MINT => price_in_base,            // USDC
        _ => price_in_base,                    // Assume USD for other tokens
    }
}

fn calculate_remaining_order_size_usd(order: &crate::models::DbOrder, sol_price: f64) -> f64 {
    let remaining_base_amount =
        lamports_to_token(order.remaining_base_amount as u64, order.base_decimals as u8);

    match order.base_mint.as_str() {
        SOL_MINT => remaining_base_amount * sol_price, // SOL
        USDC_MINT => remaining_base_amount,            // USDC
        _ => remaining_base_amount,                    // Assume USD for other tokens
    }
}
