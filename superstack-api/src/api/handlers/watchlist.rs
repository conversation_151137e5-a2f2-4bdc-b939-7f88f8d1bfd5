use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use chrono::Utc;
use serde::Deserialize;

use super::*;
use crate::{
    api::{
        types::{WatchedPerp, WatchedPool, WatchedToken, Watchlists},
        StorageState,
    },
    models::watch::{DbWatch, WatchType},
};

#[derive(Debug, Deserialize)]
pub struct GetWatchlistParams {
    pub wallet_address: String,
}

pub async fn get_watchlist(
    State(state): State<StorageState>,
    Query(query): Query<GetWatchlistParams>,
) -> Result<Json<Watchlists>, (StatusC<PERSON>, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let _wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_e| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    let watches = state.get_watches(&wallet_address).await.map_err(|_e| {
        (StatusCode::INTERNAL_SERVER_ERROR, <PERSON><PERSON>(ErrorResponse::new("Failed to get watches")))
    })?;

    let mut watchlists = Watchlists { tokens: Vec::new(), pools: Vec::new(), perps: Vec::new() };
    let sol_price = crate::utils::get_sol_price().await;
    for watch in watches {
        match watch.watch_type {
            WatchType::SolanaToken => {
                let token_mint = Pubkey::from_str(&watch.watch_id).map_err(|_e| {
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse::new("Invalid token address format")),
                    )
                })?;

                // Try to find token across all chains
                let mut token_found = None;
                for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
                    // todo: md from db
                    if let Ok(Some(token_metadata)) =
                        state.indexer_db.get_token_metadata(chain, &token_mint.to_string()).await
                    {
                        if let Ok(Some(token_statistics)) = state
                            .indexer_data_provider
                            .get_token(chain, &token_mint.to_string())
                            .await
                        {
                            token_found = Some((token_metadata, token_statistics));
                            break;
                        }
                    }
                }

                if let Some(token_info) = token_found {
                    watchlists.tokens.push(WatchedToken {
                        add_time: watch.add_time,
                        address: token_mint.to_string(),
                        name: token_info.0.name,
                        symbol: token_info.0.symbol,
                        chain: token_info.0.chain.to_string(),
                        image_url: token_info.0.image, // todo: translate url with path
                        price: token_info.1.usd_price,
                        price_change_24h: token_info.1.price_change_24h,
                        volume_24h: token_info.1.usd_volume_24h,
                        market_cap: token_info.1.usd_market_cap,
                        liquidity: token_info.1.usd_liquidity,
                        twitter: token_info.0.twitter,
                        telegram: token_info.0.telegram,
                        website: token_info.0.website,
                    });
                }
            }
            WatchType::SolanaPool => {
                let pool_address = Pubkey::from_str(&watch.watch_id).map_err(|_e| {
                    (
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse::new("Invalid pool address format")),
                    )
                })?;

                // Try to find pool across all chains
                let mut pool_found = None;
                for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
                    if let Ok(Some(pool_metadata)) =
                        state.indexer_db.get_pool_metadata(chain, &pool_address.to_string()).await
                    {
                        if let Ok(Some(pool_statistics)) = state
                            .indexer_data_provider
                            .get_pool(chain, &pool_address.to_string())
                            .await
                        {
                            pool_found = Some((pool_metadata, pool_statistics));
                            break;
                        }
                    }
                }

                if let Some(pool_metadata_and_stats) = pool_found {
                    let token_mint = pool_metadata_and_stats.0.token_address.clone();
                    let base_mint = pool_metadata_and_stats.0.base_address.clone();
                    let chain = pool_metadata_and_stats.0.chain;

                    let token_metadata =
                        state.indexer_db.get_token_metadata(chain, &token_mint).await.map_err(
                            |_e| {
                                (
                                    StatusCode::INTERNAL_SERVER_ERROR,
                                    Json(ErrorResponse::new("Failed to get token metadata")),
                                )
                            },
                        )?;

                    if let Some(token_metadata) = token_metadata {
                        let base_symbol =
                            match crate::utils::get_base_symbol_for_chain(chain, &base_mint) {
                                Ok(symbol) => symbol,
                                Err(e) => {
                                    tracing::error!(
                                    "Failed to get base symbol for chain {:?}, base_mint {}: {}",
                                    chain,
                                    base_mint,
                                    e
                                );
                                    continue;
                                }
                            };
                        watchlists.pools.push(WatchedPool {
                            add_time: watch.add_time,
                            token_mint: token_mint.clone(),
                            address: pool_address.to_string(),
                            name: token_metadata.name,
                            symbol: token_metadata.symbol,
                            image_url: token_metadata.image,
                            chain: pool_metadata_and_stats.0.chain.to_string(),
                            base_symbol: base_symbol.to_string(),
                            dex: pool_metadata_and_stats.0.dex.to_string(),
                            pool_type: Some(pool_metadata_and_stats.0.pool_type.to_string()),
                            price: pool_metadata_and_stats.1.usd_price,
                            price_change_24h: pool_metadata_and_stats.1.price_change_24h,
                            volume_24h: pool_metadata_and_stats.1.usd_volume_24h,
                            market_cap: pool_metadata_and_stats.1.usd_market_cap,
                            liquidity: pool_metadata_and_stats.1.usd_liquidity,
                        });
                    }
                }
            }
            WatchType::HyperliquidPerps => {
                watchlists
                    .perps
                    .push(WatchedPerp { add_time: watch.add_time, coin: watch.watch_id });
            }
        }
    }

    Ok(Json(watchlists))
}

#[derive(Debug, Deserialize)]
pub struct ChangeWatchlist {
    pub is_add: bool,
    pub wallet_address: String,
    pub watch_type: WatchType,
    pub watch_id: String,
}

pub async fn change_watchlist(
    State(state): State<StorageState>,
    Json(query): Json<ChangeWatchlist>,
) -> Result<StatusCode, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_e| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    // Check if wallet is authorized (legacy user or has been referred)
    let is_legacy_user = state.get_wallet(&wallet_address).await.is_ok();
    let has_referral = state
        .get_referral_by_referee(&wallet_address)
        .await
        .map_err(|_e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Error checking authorization")),
            )
        })?
        .is_some();

    if !is_legacy_user && !has_referral {
        return Err((StatusCode::UNAUTHORIZED, Json(ErrorResponse::new("Unauthorized"))));
    }

    if !query.is_add {
        state.delete_watch(&wallet_address, &query.watch_type, &query.watch_id).await.map_err(
            |_e| {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to delete watch")),
                )
            },
        )?;
        return Ok(StatusCode::OK);
    }

    let watches_number = state.get_watches_number(&wallet_address).await.map_err(|_e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse::new("Failed to get watches number")),
        )
    })?;

    if watches_number >= 50 {
        return Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Watchlist is full"))));
    }

    let watch = DbWatch {
        wallet_address: wallet_pubkey,
        add_time: Utc::now().timestamp(),
        watch_type: query.watch_type.clone(),
        watch_id: query.watch_id.clone(),
    };

    match query.watch_type {
        WatchType::SolanaToken => {
            let token_mint = Pubkey::from_str(&query.watch_id).map_err(|_e| {
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid token address format")))
            })?;

            // Try to find token across all chains
            let mut token_found = false;
            for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
                if let Ok(Some(_)) =
                    state.indexer_db.get_token_metadata(chain, &token_mint.to_string()).await
                {
                    token_found = true;
                    break;
                }
            }

            if !token_found {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new("Unsupported token")),
                ));
            }

            state.insert_watch(watch).await.map_err(|_e| {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to insert spot watchlist")),
                )
            })?;
        }
        WatchType::SolanaPool => {
            let pool_address = Pubkey::from_str(&query.watch_id).map_err(|_e| {
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid pool address format")))
            })?;

            // Try to find pool across all chains
            let mut pool_found = false;
            for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
                if let Ok(Some(_)) =
                    state.indexer_db.get_pool_metadata(chain, &pool_address.to_string()).await
                {
                    pool_found = true;
                    break;
                }
            }

            if !pool_found {
                return Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Unsupported pool"))));
            }

            state.insert_watch(watch).await.map_err(|_e| {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to insert spot watchlist")),
                )
            })?;
        }
        WatchType::HyperliquidPerps => {
            state.insert_watch(watch).await.map_err(|_e| {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to insert spot watchlist")),
                )
            })?;
        }
    }

    Ok(StatusCode::OK)
}
