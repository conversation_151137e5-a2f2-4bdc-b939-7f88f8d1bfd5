use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};

use crate::utils::get_sol_price;

use super::*;
use crate::api::{types::*, StorageState};

pub async fn get_markets_tokens(
    State(state): State<StorageState>,
    Query(query): Query<PaginationParams>,
) -> Result<Json<Vec<MarketTokenResponse>>, (StatusCode, Json<ErrorResponse>)> {
    let sol_price = get_sol_price().await;

    let checked_params = query.try_into_checked_params(sol_price)?;

    let tokens_query = checked_params.construct_market_tokens_query();
    // todo: stat from redis
    let tokens = match state.indexer_data_provider.get_market_tokens(&tokens_query).await {
        Ok(tokens) => {
            tracing::info!("Retrieved {} market tokens", tokens.len());
            if tokens.is_empty() {
                tracing::warn!("No market tokens found for query: {:?}", tokens_query);
            } else {
                // Log sample token for debugging
                if let Some(first_token) = tokens.first() {
                    tracing::debug!(
                        "Sample token: {} ({}) - Price: ${:.6}, Market Cap: ${:.2}",
                        first_token.name,
                        first_token.symbol,
                        first_token.usd_price,
                        first_token.usd_market_cap
                    );
                }
            }
            tokens
        }
        Err(e) => {
            tracing::error!("Failed to get market tokens: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new(e.to_string())),
            ));
        }
    };

    let mut market_tokens = Vec::new();

    for token in tokens {
        let volume_usd = match tokens_query.time {
            Time::Minute5 => token.usd_volume_5m,
            Time::Hour1 => token.usd_volume_1h,
            Time::Hour6 => token.usd_volume_6h,
            Time::Hour24 => token.usd_volume_24h,
            Time::Day3 => token.usd_volume_3d,
            Time::Day7 => token.usd_volume_7d,
            _ => token.usd_total_volume,
        };
        let price_change = match tokens_query.time {
            Time::Minute5 => token.price_change_5m,
            Time::Hour1 => token.price_change_1h,
            Time::Hour6 => token.price_change_6h,
            Time::Hour24 => token.price_change_24h,
            Time::Day3 => token.price_change_3d,
            Time::Day7 => token.price_change_7d,
            _ => token.total_price_change,
        };
        let price_usd = token.usd_price;
        let market_cap_usd = token.usd_market_cap;
        let liquidity_usd = token.usd_liquidity;

        let best_pool_dex = if token.best_pool_dex == Dex::Unknown {
            match state
                .indexer_data_provider
                .get_pool_metadata(token.chain, &token.best_pool_address)
                .await
            {
                Ok(Some(pool_metadata)) => pool_metadata.dex,
                Ok(None) => token.best_pool_dex,
                Err(e) => {
                    tracing::error!("Failed to get pool metadata: {}", e);
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse::new(
                            "Failed to get pool metadata for pool dex".to_string(),
                        )),
                    ));
                }
            }
        } else {
            token.best_pool_dex
        };

        let market_token = MarketTokenResponse {
            token_mint: token.token_address,
            name: token.name,
            symbol: token.symbol,
            decimals: token.decimals,
            image_url: token.image, // todo: translate url with path
            chain: token.chain.to_string(),
            dex: token.create_dex.to_string(),
            pool_address: Some(token.best_pool_address),
            pool_dex: Some(best_pool_dex.to_string()),
            twitter: token.twitter,
            telegram: token.telegram,
            website: token.website,
            price_usd,
            market_cap_usd,
            fdv_usd: market_cap_usd,
            volume_usd,
            liquidity_usd,
            price_change,
            candles: vec![],
        };
        market_tokens.push(market_token);
    }

    Ok(Json(market_tokens))
}
