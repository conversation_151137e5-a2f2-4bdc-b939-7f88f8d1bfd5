use axum::{
    extract::{Query, State},
    http::<PERSON>C<PERSON>,
    Json,
};
use serde::Deserialize;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use time::OffsetDateTime;

use super::*;
use crate::{
    api::{types::*, StorageState},
    models::{DbHistory, DbPosition},
    utils::get_sol_price,
};

// Constants
const DEFAULT_HISTORY_LIMIT: i64 = 1000;
const LAMPORTS_PER_SOL: f64 = 1_000_000_000.0;

// Use Rust's built-in checked arithmetic for safe percentage calculation
fn safe_percentage(value: f64, base: f64) -> f64 {
    // For f64, we can't use checked_div directly, but we can check for valid values
    if base != 0.0 && base.is_finite() && value.is_finite() {
        value / base
    } else {
        0.0
    }
}

fn get_interval_seconds(aggregation: &AggregationLevel) -> i64 {
    match aggregation {
        AggregationLevel::Hourly => 3600,
        AggregationLevel::Daily => 86400,
        AggregationLevel::Weekly => 604800,
    }
}

fn aggregate_account_values(
    mut values: Vec<AccountValuePoint>,
    aggregation: &AggregationLevel,
    init_value: f64,
) -> Vec<AccountValuePoint> {
    if values.is_empty() {
        return values;
    }

    // Ensure data is sorted by timestamp
    values.sort_by_key(|v| v.timestamp);

    let interval = get_interval_seconds(aggregation);
    let mut result = Vec::new();
    let mut bucket_start = (values[0].timestamp / interval) * interval;
    let mut bucket_values = Vec::new();

    for value in values {
        let current_bucket = (value.timestamp / interval) * interval;

        if current_bucket != bucket_start {
            if !bucket_values.is_empty() {
                let avg_value =
                    bucket_values.iter().map(|v: &AccountValuePoint| v.value).sum::<f64>() /
                        bucket_values.len() as f64;
                let value_percentage = safe_percentage(avg_value - init_value, init_value);
                result.push(AccountValuePoint {
                    timestamp: bucket_start,
                    value: avg_value,
                    value_percentage,
                });
                bucket_values.clear();
            }
            bucket_start = current_bucket;
        }
        bucket_values.push(value);
    }

    if !bucket_values.is_empty() {
        let avg_value = bucket_values.iter().map(|v: &AccountValuePoint| v.value).sum::<f64>() /
            bucket_values.len() as f64;
        let value_percentage = safe_percentage(avg_value - init_value, init_value);
        result.push(AccountValuePoint {
            timestamp: bucket_start,
            value: avg_value,
            value_percentage,
        });
    }
    result
}

#[derive(Debug, Deserialize)]
pub struct PortfolioParams {
    pub wallet_address: String,
    pub range: PortfolioRange,
}

pub async fn get_pnl_stats(
    State(state): State<StorageState>,
    Query(query): Query<PortfolioParams>,
) -> Result<Json<PnlStats>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let range = query.range;

    let now = OffsetDateTime::now_utc();
    let end_time = now.unix_timestamp();
    let start_time = range.get_start_time(now);

    // Get optimal aggregation level based on time range
    let aggregation = range.get_optimal_aggregation();

    // Get chart data for the response (needed for visualization)
    let account_values =
        state.get_account_values(&wallet_address, start_time, end_time).await.map_err(|e| {
            (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
        })?;

    // Get last transaction date for the response
    let latest_trade = state.get_latest_trade_for_wallet(&wallet_address).await.map_err(|e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
    })?;
    let last_txn_date = match latest_trade {
        Some(trade) => {
            OffsetDateTime::from_unix_timestamp(trade.timestamp).unwrap().date().to_string()
        }
        None => "".to_string(),
    };

    // Transform account values for chart
    let mut account_value_points: Vec<AccountValuePoint> = account_values
        .into_iter()
        .map(|av| {
            AccountValuePoint {
                timestamp: av.timestamp,
                value: av.value,
                value_percentage: 0.0, // Will be calculated in the service
            }
        })
        .collect();

    // Apply data aggregation if needed
    if let Some(agg) = aggregation {
        // Get initial account value for proper percentage calculation
        let initial_account_value = if let Some(first_point) = account_value_points.first() {
            first_point.value
        } else {
            0.0
        };
        account_value_points =
            aggregate_account_values(account_value_points, &agg, initial_account_value);
    }

    // Use the internal PnL calculation function
    let pnl_stats = calculate_pnl_stats_internal(
        &state,
        &wallet_address,
        start_time,
        end_time,
        account_value_points,
        last_txn_date,
    )
    .await
    .map_err(|e| {
        tracing::error!("Failed to calculate PnL for wallet {}: {}", wallet_address, e);
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
    })?;

    Ok(Json(pnl_stats))
}

/// Calculate PnL statistics directly, returning the final PnlStats
async fn calculate_pnl_stats_internal(
    state: &StorageState,
    wallet_address: &str,
    start_time: i64,
    end_time: i64,
    account_values: Vec<AccountValuePoint>,
    last_txn_date: String,
) -> Result<PnlStats, anyhow::Error> {
    let sol_price = get_sol_price().await;

    // Get all required data
    let histories = state
        .api_db
        .get_histories_by_time_range(wallet_address, start_time, end_time, DEFAULT_HISTORY_LIMIT)
        .await?;
    // Note: We don't query realized_pnls to avoid double counting with histories
    // let realized_pnls = state.get_realized_pnls(wallet_address, start_time, end_time).await?;
    let positions = state.get_positions(wallet_address).await?;

    // Get current account value using wallet manager
    let wallet_pubkey = Pubkey::from_str(wallet_address)?;
    let current_account_value =
        crate::wallet::SolanaWalletManager::get_account_value(state, &wallet_pubkey).await?;

    // Calculate components directly
    let (total_realized_pnl, total_costs_usd, total_revenue_usd) =
        calculate_realized_pnl_direct(&histories, sol_price).await?;

    // Calculate unrealized PnL and collect position costs in one pass
    let (
        total_unrealized_pnl,
        positions_calculated,
        positions_failed,
        current_positions_historical_cost,
    ) = calculate_unrealized_pnl_with_costs_direct(state, &positions).await?;

    let initial_value = calculate_initial_value_direct(
        state,
        wallet_address,
        start_time,
        current_account_value,
        total_costs_usd,
    )
    .await?;

    // Calculate percentages with consistent base values
    let realized_pnl_percentage = safe_percentage(total_realized_pnl, total_costs_usd);
    let unrealized_pnl_percentage =
        safe_percentage(total_unrealized_pnl, current_positions_historical_cost);

    // Log detailed calculation summary
    tracing::info!(
        "PnL calculation summary for wallet {}: realized_pnl=${:.2} ({:.2}%), unrealized_pnl=${:.2} ({:.2}%), account_value=${:.2}, initial_value=${:.2}",
        wallet_address,
        total_realized_pnl,
        realized_pnl_percentage * 100.0,
        total_unrealized_pnl,
        unrealized_pnl_percentage * 100.0,
        current_account_value,
        initial_value
    );

    tracing::debug!(
        "PnL calculation details for wallet {}: costs=${:.2}, revenue=${:.2}, positions_ok={}, positions_failed={}, positions_historical_cost=${:.2}",
        wallet_address,
        total_costs_usd,
        total_revenue_usd,
        positions_calculated,
        positions_failed,
        current_positions_historical_cost
    );

    // Calculate PnL views directly from account values using consistent initial value
    let pnl_views: Vec<PnlView> = if initial_value > 0.0 {
        account_values
            .iter()
            .map(|av| {
                let pnl = av.value - initial_value;
                let pnl_percentage = pnl / initial_value;

                PnlView { timestamp: av.timestamp, pnl, pnl_percentage }
            })
            .collect()
    } else {
        // If no meaningful initial value, return empty PnL views
        Vec::new()
    };

    tracing::debug!(
        "Generated {} PnL views for wallet {} using initial_value=${:.2}",
        pnl_views.len(),
        wallet_address,
        initial_value
    );

    // Comprehensive data consistency validation
    let consistency_report = validate_data_consistency(
        wallet_address,
        total_costs_usd,
        total_revenue_usd,
        current_account_value,
        positions_calculated,
        positions_failed,
        &histories,
        &account_values,
    );

    consistency_report.log_report();

    Ok(PnlStats {
        account_value: current_account_value,
        realized_pnl: total_realized_pnl,
        realized_pnl_percentage,
        unrealized_pnl: total_unrealized_pnl,
        unrealized_pnl_percentage,
        revenue: total_revenue_usd,
        spent: -total_costs_usd, // Negative value as expected
        total_bought: total_costs_usd,
        total_sold: total_revenue_usd,
        last_txn_date,
        account_values,
        pnl_views,
    })
}

/// Calculate realized PnL directly from historical data
/// Uses only DbHistory records to avoid double counting with DbRealizedPnl
pub async fn calculate_realized_pnl_direct(
    histories: &[DbHistory],
    sol_price: f64,
) -> Result<(f64, f64, f64), anyhow::Error> {
    let mut total_costs_usd = 0.0;
    let mut total_revenue_usd = 0.0;

    // Process historical trades (closed positions only)
    // DbHistory contains complete information about closed positions
    for history in histories {
        // Calculate USD values using current SOL price for consistency
        let native_cost_usd = (history.cost_native_amount as f64 / LAMPORTS_PER_SOL) * sol_price;
        let native_earnings_usd =
            (history.earnings_native_amount as f64 / LAMPORTS_PER_SOL) * sol_price;

        total_costs_usd += native_cost_usd + history.cost_usd;
        total_revenue_usd += native_earnings_usd + history.earnings_usd;
    }

    let total_pnl_usd = total_revenue_usd - total_costs_usd;

    tracing::debug!(
        "Realized PnL calculation: processed {} history records, total_costs=${:.2}, total_revenue=${:.2}, net_pnl=${:.2}",
        histories.len(),
        total_costs_usd,
        total_revenue_usd,
        total_pnl_usd
    );

    Ok((total_pnl_usd, total_costs_usd, total_revenue_usd))
}

/// Calculate unrealized PnL and historical costs from current positions in one pass
async fn calculate_unrealized_pnl_with_costs_direct(
    state: &StorageState,
    positions: &[DbPosition],
) -> Result<(f64, usize, usize, f64), anyhow::Error> {
    let mut total_pnl_usd = 0.0;
    let mut total_historical_cost = 0.0;
    let mut positions_calculated = 0;
    let mut positions_failed = 0;

    for position in positions {
        match Position::construct(state, position.clone()).await {
            Ok(constructed_position) => {
                total_pnl_usd += constructed_position.pnl_usd;
                total_historical_cost += constructed_position.cost_usd;
                positions_calculated += 1;
            }
            Err(e) => {
                positions_failed += 1;
                tracing::error!(
                    "Failed to construct position for token {}: {}",
                    position.token_mint,
                    e
                );
                // Fallback: use stored cost values (less accurate but better than nothing)
                total_historical_cost += position.cost_usd;
            }
        }
    }

    tracing::info!(
        "Unrealized PnL calculation: {} positions calculated, {} failed, total_cost=${:.2}",
        positions_calculated,
        positions_failed,
        total_historical_cost
    );

    Ok((total_pnl_usd, positions_calculated, positions_failed, total_historical_cost))
}

/// Calculate initial account value for PnL baseline calculation
/// This represents the account value at the start of the time period
async fn calculate_initial_value_direct(
    state: &StorageState,
    wallet_address: &str,
    start_time: i64,
    current_account_value: f64,
    total_costs_usd: f64,
) -> Result<f64, anyhow::Error> {
    // Try to get historical account value at or before start_time
    match state.get_latest_account_value_before_timestamp(wallet_address, start_time).await? {
        Some(account_value) if account_value.value > 0.0 => {
            tracing::debug!(
                "Using historical account value ${:.2} from timestamp {} for wallet {}",
                account_value.value,
                account_value.timestamp,
                wallet_address
            );
            Ok(account_value.value)
        }
        _ => {
            // No historical data available
            // TODO: use estimation
            // let estimated_initial = estimate_initial_value_from_trades(
            //     state,
            //     wallet_address,
            //     start_time,
            //     current_account_value,
            //     total_costs_usd,
            // )
            // .await?;

            // tracing::debug!(
            //     "No historical account value found, using estimated initial value ${:.2} for
            // wallet {}",     estimated_initial,
            //     wallet_address
            // );

            Ok(1.0) // Ensure minimum value to avoid division by zero
        }
    }
}

/// Estimate initial account value based on trading activity and current positions
async fn estimate_initial_value_from_trades(
    state: &StorageState,
    wallet_address: &str,
    start_time: i64,
    current_account_value: f64,
    total_realized_costs: f64,
) -> Result<f64, anyhow::Error> {
    let sol_price = crate::utils::get_sol_price().await;

    // Get trades before the start time to understand wallet activity
    let early_trades = state
        .api_db
        .get_trades_by_time_range(
            wallet_address,
            "", // All tokens
            0,  // From beginning
            start_time,
            None, // All chains
        )
        .await
        .unwrap_or_default();

    // Get current positions to understand unrealized investments
    let positions = state.get_positions(wallet_address).await.unwrap_or_default();

    // Calculate total historical investment (positions that existed before start_time)
    let mut historical_investment = 0.0;
    for position in &positions {
        // Estimate how much of this position existed before start_time
        let position_trades: Vec<_> =
            early_trades.iter().filter(|t| t.token_mint == position.token_mint).collect();

        if !position_trades.is_empty() {
            // Calculate investment in this position before start_time
            let mut pre_start_cost_native = 0i64;
            let mut pre_start_cost_usd = 0.0;

            for trade in position_trades {
                if trade.trade_type.is_buy() {
                    pre_start_cost_native += trade.base_amount;
                    // Note: We don't have historical USD costs in trades, so we estimate
                } else {
                    pre_start_cost_native -= trade.base_amount;
                }
            }

            if pre_start_cost_native > 0 {
                let pre_start_cost_usd_estimated =
                    (pre_start_cost_native as f64 / LAMPORTS_PER_SOL) * sol_price;
                historical_investment += pre_start_cost_usd_estimated;
            }
        }
    }

    // Strategy 1: If we have significant historical trading activity
    if historical_investment > 10.0 {
        // Threshold for meaningful investment
        tracing::debug!(
            "Using historical investment estimation: ${:.2} for wallet {}",
            historical_investment,
            wallet_address
        );
        return Ok(historical_investment);
    }

    // Strategy 2: If we have recent realized trades, work backwards
    if total_realized_costs > 0.0 {
        // Estimate initial value as current value plus net realized losses
        // This assumes the wallet started with enough to cover realized trades
        let estimated_initial = current_account_value + total_realized_costs;

        tracing::debug!(
            "Using realized trades estimation: current=${:.2} + costs=${:.2} = ${:.2} for wallet {}",
            current_account_value,
            total_realized_costs,
            estimated_initial,
            wallet_address
        );
        return Ok(estimated_initial);
    }

    // Strategy 3: Use current account value as baseline
    // This is the most conservative approach when we have no historical data
    tracing::debug!(
        "Using current account value as baseline: ${:.2} for wallet {}",
        current_account_value,
        wallet_address
    );
    Ok(current_account_value)
}

/// Comprehensive data consistency validation
#[derive(Debug, Clone)]
pub struct DataConsistencyReport {
    pub wallet_address: String,
    pub validation_timestamp: i64,
    pub warnings: Vec<String>,
    pub errors: Vec<String>,
    pub metrics: ConsistencyMetrics,
}

#[derive(Debug, Clone)]
pub struct ConsistencyMetrics {
    pub total_costs_usd: f64,
    pub total_revenue_usd: f64,
    pub current_account_value: f64,
    pub positions_calculated: usize,
    pub positions_failed: usize,
    pub histories_processed: usize,
    pub account_values_count: usize,
}

impl DataConsistencyReport {
    pub fn new(wallet_address: String) -> Self {
        Self {
            wallet_address,
            validation_timestamp: time::OffsetDateTime::now_utc().unix_timestamp(),
            warnings: Vec::new(),
            errors: Vec::new(),
            metrics: ConsistencyMetrics {
                total_costs_usd: 0.0,
                total_revenue_usd: 0.0,
                current_account_value: 0.0,
                positions_calculated: 0,
                positions_failed: 0,
                histories_processed: 0,
                account_values_count: 0,
            },
        }
    }

    pub fn add_warning(&mut self, message: String) {
        self.warnings.push(message);
    }

    pub fn add_error(&mut self, message: String) {
        self.errors.push(message);
    }

    pub fn has_issues(&self) -> bool {
        !self.warnings.is_empty() || !self.errors.is_empty()
    }

    pub fn log_report(&self) {
        if !self.errors.is_empty() {
            tracing::error!(
                "Data consistency errors for wallet {}: {}",
                self.wallet_address,
                self.errors.join("; ")
            );
        }

        if !self.warnings.is_empty() {
            tracing::warn!(
                "Data consistency warnings for wallet {}: {}",
                self.wallet_address,
                self.warnings.join("; ")
            );
        }

        if !self.has_issues() {
            tracing::debug!(
                "Data consistency validation passed for wallet {}: costs=${:.2}, revenue=${:.2}, account_value=${:.2}",
                self.wallet_address,
                self.metrics.total_costs_usd,
                self.metrics.total_revenue_usd,
                self.metrics.current_account_value
            );
        }
    }
}

/// Enhanced validation with comprehensive data consistency checks
pub fn validate_data_consistency(
    wallet_address: &str,
    total_costs_usd: f64,
    total_revenue_usd: f64,
    current_account_value: f64,
    positions_calculated: usize,
    positions_failed: usize,
    histories: &[DbHistory],
    account_values: &[AccountValuePoint],
) -> DataConsistencyReport {
    let mut report = DataConsistencyReport::new(wallet_address.to_string());

    // Update metrics
    report.metrics.total_costs_usd = total_costs_usd;
    report.metrics.total_revenue_usd = total_revenue_usd;
    report.metrics.current_account_value = current_account_value;
    report.metrics.positions_calculated = positions_calculated;
    report.metrics.positions_failed = positions_failed;
    report.metrics.histories_processed = histories.len();
    report.metrics.account_values_count = account_values.len();

    // Basic validation checks
    if total_costs_usd < 0.0 {
        report.add_error(format!("Negative total costs: ${:.2}", total_costs_usd));
    }
    if total_revenue_usd < 0.0 {
        report.add_error(format!("Negative total revenue: ${:.2}", total_revenue_usd));
    }
    if current_account_value < 0.0 {
        report.add_warning(format!("Negative account value: ${:.2}", current_account_value));
    }
    if positions_failed > 0 {
        report.add_warning(format!("Failed to calculate {} positions", positions_failed));
    }

    // Advanced consistency checks
    let pnl = total_revenue_usd - total_costs_usd;

    // Check for extreme PnL ratios
    if total_costs_usd > 0.0 && pnl.abs() > total_costs_usd * 1000.0 {
        report.add_warning(format!(
            "Extreme PnL ratio: {:.2}% (PnL: ${:.2}, Costs: ${:.2})",
            (pnl / total_costs_usd) * 100.0,
            pnl,
            total_costs_usd
        ));
    }

    // Check account value consistency
    if account_values.len() > 1 {
        let latest_account_value = account_values.last().unwrap().value;
        let value_diff = (latest_account_value - current_account_value).abs();
        if value_diff > current_account_value * 0.1 && current_account_value > 10.0 {
            report.add_warning(format!(
                "Account value mismatch: latest_historical=${:.2}, current=${:.2}, diff=${:.2}",
                latest_account_value, current_account_value, value_diff
            ));
        }
    }

    // Check for data completeness
    if histories.is_empty() && total_costs_usd > 0.0 {
        report.add_warning("No history records found despite having realized costs".to_string());
    }

    // Check for temporal consistency in histories
    for (i, history) in histories.iter().enumerate() {
        if history.open_time > history.close_time {
            report.add_error(format!(
                "History record {} has invalid timestamps: open={}, close={}",
                i, history.open_time, history.close_time
            ));
        }

        if history.bought_amount < history.sold_amount {
            report.add_warning(format!(
                "History record {} has more sold than bought: bought={}, sold={}",
                i, history.bought_amount, history.sold_amount
            ));
        }
    }

    // Check account value trends
    if account_values.len() > 2 {
        let mut extreme_changes = 0;
        for window in account_values.windows(2) {
            let change_ratio = (window[1].value - window[0].value).abs() / window[0].value.max(1.0);
            if change_ratio > 10.0 {
                // 1000% change
                extreme_changes += 1;
            }
        }

        if extreme_changes > account_values.len() / 4 {
            report.add_warning(format!(
                "High volatility detected: {} extreme changes in {} data points",
                extreme_changes,
                account_values.len()
            ));
        }
    }

    report
}
