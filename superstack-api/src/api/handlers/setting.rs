use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use serde::Deserialize;

use super::*;
use crate::{
    api::{types::Setting, StorageState},
    models::setting::DbSetting,
};

#[derive(Debug, Deserialize)]
pub struct GetSettingsParams {
    pub wallet_address: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SettingWithWalletAddress {
    pub wallet_address: String,
    pub setting: Setting,
}

pub async fn get_setting(
    State(state): State<StorageState>,
    Query(query): Query<GetSettingsParams>,
) -> Result<Json<Setting>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let _wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_e| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    let db_setting = state.get_setting(&wallet_address).await.map_err(|_e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to get setting")))
    })?;

    if let Some(db_setting) = db_setting {
        let json_value = db_setting.json_settings;
        let setting = Setting::from_json(json_value).map_err(|e| {
            tracing::error!("Failed to deserialize setting: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to deserialize setting")),
            )
        })?;
        Ok(Json(setting))
    } else {
        let setting = Setting::default();
        Ok(Json(setting))
    }
}

pub async fn change_setting(
    State(state): State<StorageState>,
    Json(setting_with_wallet_address): Json<SettingWithWalletAddress>,
) -> Result<StatusCode, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = setting_with_wallet_address.wallet_address;
    let wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_e| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    let current_db_setting = state.get_setting(&wallet_address).await.map_err(|_e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to get setting")))
    })?;

    let mut current_setting = if let Some(db_current_setting) = current_db_setting {
        if let Ok(setting) = Setting::from_json(db_current_setting.json_settings.clone()) {
            setting
        } else {
            tracing::error!("Failed to deserialize setting: {}", db_current_setting.json_settings);
            Setting::default()
        }
    } else {
        Setting::default()
    };

    current_setting.update_with(&setting_with_wallet_address.setting);

    let json_setting = serde_json::to_value(current_setting).map_err(|_e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to serialize setting")))
    })?;

    let db_setting = DbSetting { wallet_address: wallet_pubkey, json_settings: json_setting };

    state.insert_or_update_setting(db_setting).await.map_err(|_e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to update setting")))
    })?;

    Ok(StatusCode::OK)
}
