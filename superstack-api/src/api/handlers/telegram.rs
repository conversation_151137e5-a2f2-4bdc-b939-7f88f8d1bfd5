use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use serde::Deserialize;

use super::*;
use crate::{api::StorageState, config::Config};

#[derive(Debug, Deserialize)]
pub struct GetTelegramInfoParams {
    pub wallet_address: String,
}

#[derive(Debug, Serialize)]
pub struct TelegramUserInfo {
    pub id: u64,
    pub firstname: String,
    pub lastname: Option<String>,
    pub username: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct TelegramInfo {
    pub telegram_bot_username: String,
    pub telegram_user: Option<TelegramUserInfo>,
}

#[derive(Debug, Deserialize)]
pub struct TelegramBindPayload {
    pub wallet_address: String,
    pub state: String,
    pub telegram_id: u64,
}

pub async fn get_telegram_info(
    State(state): State<StorageState>,
    Query(query): Query<GetTelegramInfoParams>,
) -> Result<Json<TelegramInfo>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;
    let _wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_e| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    let telegram_user = state.get_telegram_user_by_wallet(&wallet_address).await.map_err(|_e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to get telegram user")))
    })?;

    let bot_username = Config::get().telegram_bot_username.clone();

    let telegram_user_info = if let Some(telegram_user) = telegram_user {
        Some(TelegramUserInfo {
            id: telegram_user.id,
            firstname: telegram_user.first_name,
            lastname: telegram_user.last_name,
            username: telegram_user.username,
        })
    } else {
        None
    };

    let telegram_info =
        TelegramInfo { telegram_bot_username: bot_username, telegram_user: telegram_user_info };

    Ok(Json(telegram_info))
}

pub async fn bind_telegram(
    State(state): State<StorageState>,
    Json(payload): Json<TelegramBindPayload>,
) -> Result<StatusCode, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = payload.wallet_address;
    let _wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_e| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    let telegram_id = payload.telegram_id;
    let auth_code = payload.state;

    // Check if the wallet is already bound to a telegram user
    if let Some(_telegram_user) =
        state.get_telegram_user_by_wallet(&wallet_address).await.map_err(|_e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to get telegram user")),
            )
        })?
    {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new("Another Telegram user already bound to this account")),
        ));
    };

    let telegram_user = match state.get_telegram_user_by_id(telegram_id).await.map_err(|_e| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to get telegram user")))
    })? {
        Some(telegram_user) => telegram_user,
        None => {
            return Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse::new("Telegram user not found, please connect bot first")),
            ));
        }
    };

    if let Some(bind_account) = telegram_user.bind_account {
        if bind_account == wallet_address {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new("Telegram user already bound to this account")),
            ));
        } else {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new("Telegram user already bound to another account")),
            ));
        }
    }

    if let Some(auth_code_from_db) = telegram_user.auth_code {
        if auth_code_from_db != auth_code {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(
                    "Invalid state code, please connect bot and use the latest bind link",
                )),
            ));
        }
    } else {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new("Invalid state code, please connect bot first")),
        ));
    }

    let bind_success = state
        .update_telegram_user_bind_account(telegram_id, &wallet_address, &auth_code)
        .await
        .map_err(|_e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to bind telegram user")),
            )
        })?;

    if bind_success {
        Ok(StatusCode::OK)
    } else {
        let latest_telegram_user = match state.get_telegram_user_by_id(telegram_id).await.map_err(
            |_e| {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to get telegram user")),
                )
            },
        )? {
            Some(telegram_user) => telegram_user,
            None => {
                return Err((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Telegram user not found when checking bind status, please connect bot first"))));
            }
        };

        if let Some(bind_account) = latest_telegram_user.bind_account {
            if bind_account == wallet_address {
                return Ok(StatusCode::OK);
            } else {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new("Telegram user already bound to another account")),
                ));
            }
        } else {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new("Telegram bind failed, please try again later")),
            ));
        }
    }
}
