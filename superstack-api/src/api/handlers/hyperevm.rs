use alloy::{
    consensus::TxEnvelope, eips::eip2718::Decodable2718, primitives::TxHash, providers::Provider,
};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::J<PERSON>,
};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use std::str::FromStr;

use crate::{
    api::handlers::ErrorResponse,
    constant::{CHAIN_HYPEREVM, HYPE_DECIMALS, HYPE_NATIVE_TOKEN},
    hyperevm::wallet::HyperEvmWalletManager,
    models::{types::TransactionStatus, DbPosition, DbPositionChange, DbTransaction, StorageState},
};

#[derive(Debug, Deserialize)]
pub struct WalletBalanceParams {
    pub wallet_address: String,
    pub token_addresses: Option<String>, // comma-separated list
}

// Request to send a HyperEVM transaction
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SendHyperEvmTransactionRequest {
    pub wallet_address: String,
    pub serialized_transaction: String, // Hex encoded signed transaction

    // Trade details for position tracking
    pub trade_details: Option<HyperEvmTradeDetails>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct HyperEvmTradeDetails {
    pub token_address: String,        // Token contract address
    pub token_amount: String,         // Token amount (as string to handle large numbers)
    pub token_decimals: u8,           // Token decimals
    pub base_amount: String,          // HYPE amount (as string)
    pub is_buy: bool,                 // true for buy, false for sell
    pub token_symbol: Option<String>, // Token symbol for display
    pub token_name: Option<String>,   // Token name for display
}

// Response with transaction hash
#[derive(Debug, Serialize)]
pub struct SendHyperEvmTransactionResponse {
    pub tx_hash: String,
}

// Transaction status response
#[derive(Debug, Serialize)]
pub struct HyperEvmTransactionStatusResponse {
    pub tx_hash: String,
    pub status: TransactionStatus,
    pub block_number: Option<u64>,
    pub confirmations: Option<u64>,
    pub gas_used: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self { success: true, data: Some(data), error: None }
    }

    pub fn error(error: String) -> Self {
        Self { success: false, data: None, error: Some(error) }
    }
}

/// Validate EVM address format
fn validate_evm_address(address: &str) -> Result<(), String> {
    if !address.starts_with("0x") {
        return Err("Address must start with 0x".to_string());
    }
    if address.len() != 42 {
        return Err("Address must be 42 characters long".to_string());
    }
    if !address[2..].chars().all(|c| c.is_ascii_hexdigit()) {
        return Err("Address contains invalid characters".to_string());
    }
    Ok(())
}

/// Parse and validate amount string to u128
fn parse_amount(amount_str: &str, field_name: &str) -> Result<u128, String> {
    if amount_str.is_empty() {
        return Err(format!("{} cannot be empty", field_name));
    }

    let amount =
        u128::from_str(amount_str).map_err(|_| format!("Invalid {} format", field_name))?;

    if amount == 0 {
        return Err(format!("{} must be greater than 0", field_name));
    }

    Ok(amount)
}

/// Validate trade details
fn validate_trade_details(trade_details: &HyperEvmTradeDetails) -> Result<(), String> {
    // Validate token address
    validate_evm_address(&trade_details.token_address)?;

    // Validate decimals range
    if trade_details.token_decimals > 30 {
        return Err("Token decimals cannot exceed 30".to_string());
    }

    // Validate amounts
    parse_amount(&trade_details.token_amount, "token_amount")?;
    parse_amount(&trade_details.base_amount, "base_amount")?;

    Ok(())
}

/// Get wallet balance information for HyperEVM
pub async fn get_wallet_balance(
    Query(params): Query<WalletBalanceParams>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let token_addresses: Vec<String> = params
        .token_addresses
        .map(|s| s.split(',').map(|addr| addr.trim().to_string()).collect())
        .unwrap_or_default();

    match HyperEvmWalletManager::get_wallet_info(&params.wallet_address, &token_addresses).await {
        Ok(wallet_info) => {
            Ok(Json(ApiResponse::success(serde_json::to_value(wallet_info).unwrap())))
        }
        Err(e) => Ok(Json(ApiResponse::error(format!("Failed to get wallet balance: {}", e)))),
    }
}

/// Send a HyperEVM transaction
pub async fn send_hyperevm_transaction(
    State(state): State<StorageState>,
    Json(request): Json<SendHyperEvmTransactionRequest>,
) -> Result<Json<SendHyperEvmTransactionResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet address
    if let Err(e) = validate_evm_address(&request.wallet_address) {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new(&format!("Invalid wallet address: {}", e))),
        ));
    }

    // Validate trade details if provided
    if let Some(ref trade_details) = request.trade_details {
        if let Err(e) = validate_trade_details(trade_details) {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!("Invalid trade details: {}", e))),
            ));
        }
    }

    // Remove 0x prefix if present
    let hex_tx = request
        .serialized_transaction
        .strip_prefix("0x")
        .unwrap_or(&request.serialized_transaction);

    // Parse transaction from hex
    let tx_bytes = hex::decode(hex_tx).map_err(|e| {
        tracing::error!("Failed to decode transaction hex: {}", e);
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid transaction hex encoding")))
    })?;

    // Parse as signed EVM transaction envelope
    let tx_envelope = TxEnvelope::decode_2718(&mut tx_bytes.as_slice()).map_err(|e| {
        tracing::error!("Failed to decode transaction envelope: {}", e);
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid signed transaction format")))
    })?;

    // The fact that we can decode a TxEnvelope means it's a valid signed transaction
    tracing::info!("Successfully parsed signed transaction with type: {:?}", tx_envelope.tx_type());

    // Send raw transaction via provider
    let provider = crate::utils::get_provider();
    let tx_hash =
        crate::transaction::evm::send_raw_transaction(provider, &tx_bytes).await.map_err(|e| {
            let error_message = e.to_string();
            tracing::error!("Send HyperEVM transaction error: {}", error_message);
            (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Send transaction failed")))
        })?;

    // Store transaction in database
    let db_transaction = DbTransaction {
        signature: tx_hash.to_string(),
        wallet_address: request.wallet_address.clone(),
        status: TransactionStatus::Pending,
        is_processed: false,
        created_at: Utc::now().timestamp(),
        chain: CHAIN_HYPEREVM,
    };

    state.insert_transaction(&db_transaction).await.map_err(|_| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Insert transaction failed")))
    })?;

    // Always create position record, even without trade details to ensure position table has data
    if let Some(trade_details) = request.trade_details {
        if let Err(e) = create_hyperevm_position(
            &state,
            &request.wallet_address,
            &tx_hash.to_string(),
            trade_details,
        )
        .await
        {
            tracing::error!(
                "Failed to create HyperEVM position for wallet {} tx {}: {}",
                request.wallet_address,
                tx_hash.to_string(),
                e
            );
            // Don't fail the transaction, just log the error
            // In production, you might want to queue this for retry
        } else {
            tracing::info!(
                "Successfully processed HyperEVM position for wallet {} tx {}",
                request.wallet_address,
                tx_hash.to_string()
            );
        }
    } else {
        // Create empty position record to ensure position table has data
        if let Err(e) = create_hyperevm_position_without_trade_details(
            &state,
            &request.wallet_address,
            &tx_hash.to_string(),
        )
        .await
        {
            tracing::error!(
                "Failed to create empty HyperEVM position for wallet {} tx {}: {}",
                request.wallet_address,
                tx_hash.to_string(),
                e
            );
        } else {
            tracing::info!(
                "Successfully created empty HyperEVM position for wallet {} tx {}",
                request.wallet_address,
                tx_hash.to_string()
            );
        }
    }

    Ok(Json(SendHyperEvmTransactionResponse { tx_hash: tx_hash.to_string() }))
}

/// Check HyperEVM transaction status
pub async fn get_hyperevm_transaction_status(
    State(state): State<StorageState>,
    Path(signature_str): Path<String>,
) -> Result<Json<HyperEvmTransactionStatusResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Parse the transaction hash (signature in HyperEVM context)
    let tx_hash: TxHash = signature_str.parse().map_err(|_| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid transaction hash")))
    })?;

    // Check the transaction status
    let provider = crate::utils::get_provider();
    let status = crate::transaction::evm::check_transaction_status(provider, tx_hash)
        .await
        .map_err(|_| {
            (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Check transaction status failed")))
        })?;

    // Update database if transaction is finalized
    if status.is_finalized() {
        state.update_transaction_status_if_pending(&signature_str, &status).await.map_err(
            |_| {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Update transaction status failed")),
                )
            },
        )?;
    }

    // Get additional transaction details if available
    let (block_number, confirmations, gas_used) = if status.is_success() {
        // Try to get transaction receipt for additional details
        match provider.get_transaction_receipt(tx_hash).await {
            Ok(Some(receipt)) => (
                Some(receipt.block_number.unwrap_or_default()),
                Some(1u64), // Simplified confirmation count
                Some(receipt.gas_used.to_string()),
            ),
            _ => (None, None, None),
        }
    } else {
        (None, None, None)
    };

    Ok(Json(HyperEvmTransactionStatusResponse {
        tx_hash: signature_str,
        status,
        block_number,
        confirmations,
        gas_used,
    }))
}

/// Create a position record for HyperEVM trade
pub async fn create_hyperevm_position(
    state: &StorageState,
    wallet_address: &str,
    tx_hash: &str,
    trade_details: HyperEvmTradeDetails,
) -> anyhow::Result<()> {
    let current_time = Utc::now().timestamp();

    // Parse amounts from strings using u128 to handle large EVM amounts
    let token_amount = parse_amount(&trade_details.token_amount, "token_amount")
        .map_err(|e| anyhow::anyhow!("Invalid token amount: {}", e))?;
    let base_amount = parse_amount(&trade_details.base_amount, "base_amount")
        .map_err(|e| anyhow::anyhow!("Invalid base amount: {}", e))?;

    // Convert to i64 for database storage (with overflow check)
    let token_amount_i64 = token_amount.min(i64::MAX as u128) as i64;
    let base_amount_i64 = base_amount.min(i64::MAX as u128) as i64;

    if token_amount > i64::MAX as u128 || base_amount > i64::MAX as u128 {
        tracing::warn!(
            "Amount overflow detected, clamping to i64::MAX for wallet {} token {}",
            wallet_address,
            trade_details.token_address
        );
    }

    // Normalize token address to lowercase for consistency
    let token_address = trade_details.token_address.to_lowercase();

    // Check if position already exists
    let existing_position =
        state.get_position(wallet_address, &token_address, CHAIN_HYPEREVM).await?;

    let position_change = DbPositionChange {
        is_buy: trade_details.is_buy,
        token_amount: token_amount_i64,
        base_amount: base_amount_i64,
        base_mint: HYPE_NATIVE_TOKEN.to_string(),
        base_decimals: HYPE_DECIMALS as i16,
        tx_sig: tx_hash.to_string(),
        timestamp: current_time,
        chain: CHAIN_HYPEREVM,
    };

    if let Some(mut position) = existing_position {
        // Update existing position
        if trade_details.is_buy {
            position.bought_amount = position.bought_amount.saturating_add(token_amount_i64);
            position.cost_native_amount =
                position.cost_native_amount.saturating_add(base_amount_i64);
        } else {
            position.sold_amount = position.sold_amount.saturating_add(token_amount_i64);
            position.earnings_native_amount =
                position.earnings_native_amount.saturating_add(base_amount_i64);
        }

        position.operations.push(position_change);

        // Check if position should be closed (fully sold)
        let net_amount = position.bought_amount - position.sold_amount;
        if net_amount <= 0 && !trade_details.is_buy {
            tracing::info!(
                "Position fully closed for wallet {} token {}, net amount: {}",
                wallet_address,
                token_address,
                net_amount
            );
            // Position is fully closed, could add close_time field if needed
        }

        state.update_position(position).await?;

        tracing::info!(
            "Updated HyperEVM position for wallet {} token {}, net amount: {}",
            wallet_address,
            token_address,
            net_amount
        );
    } else {
        // Create new position
        let (bought_amount, sold_amount, cost_native_amount, earnings_native_amount) =
            if trade_details.is_buy {
                (token_amount_i64, 0, base_amount_i64, 0)
            } else {
                (0, token_amount_i64, 0, base_amount_i64)
            };

        let position = DbPosition {
            wallet_address: wallet_address.to_string(),
            token_mint: token_address.clone(), // Use normalized lowercase address
            token_decimals: trade_details.token_decimals as i16,
            bought_amount,
            sold_amount,
            native_decimals: HYPE_DECIMALS as i16,
            cost_native_amount,
            earnings_native_amount,
            cost_usd: 0.0,
            earnings_usd: 0.0,
            operations: vec![position_change],
            open_time: current_time,
            chain: CHAIN_HYPEREVM,
        };

        state.insert_position(position).await?;

        tracing::info!(
            "Created new HyperEVM position for wallet {} token {}",
            wallet_address,
            token_address
        );
    }

    Ok(())
}

/// Create a position record for HyperEVM transaction without trade details
pub async fn create_hyperevm_position_without_trade_details(
    state: &StorageState,
    wallet_address: &str,
    tx_hash: &str,
) -> anyhow::Result<()> {
    let current_time = Utc::now().timestamp();

    // Check if position already exists for this wallet and a placeholder token
    let placeholder_token = "******************************************"; // Placeholder for unknown token
    let existing_position =
        state.get_position(wallet_address, placeholder_token, CHAIN_HYPEREVM).await?;

    let position_change = DbPositionChange {
        is_buy: true, // Default to buy for placeholder
        token_amount: 0,
        base_amount: 0,
        base_mint: HYPE_NATIVE_TOKEN.to_string(),
        base_decimals: HYPE_DECIMALS as i16,
        tx_sig: tx_hash.to_string(),
        timestamp: current_time,
        chain: CHAIN_HYPEREVM,
    };

    if let Some(mut position) = existing_position {
        // Update existing position with new operation
        position.operations.push(position_change);

        state.update_position(position).await?;

        tracing::info!(
            "Updated existing HyperEVM placeholder position for wallet {}",
            wallet_address
        );
    } else {
        // Create new placeholder position
        let position = DbPosition {
            wallet_address: wallet_address.to_string(),
            token_mint: placeholder_token.to_string(),
            token_decimals: HYPE_DECIMALS as i16,
            bought_amount: 0,
            sold_amount: 0,
            native_decimals: HYPE_DECIMALS as i16,
            cost_native_amount: 0,
            earnings_native_amount: 0,
            cost_usd: 0.0,
            earnings_usd: 0.0,
            operations: vec![position_change],
            open_time: current_time,
            chain: CHAIN_HYPEREVM,
        };

        state.insert_position(position).await?;

        tracing::info!("Created new HyperEVM placeholder position for wallet {}", wallet_address);
    }

    Ok(())
}

/// Calculate and log gas costs for the transaction (for future enhancement)
#[allow(dead_code)]
async fn calculate_gas_cost(tx_hash: &str) -> Option<u128> {
    // This could be enhanced to fetch actual gas costs from the transaction receipt
    // and convert to USD value for better position tracking
    let provider = crate::utils::get_provider();

    if let Ok(tx_hash_parsed) = tx_hash.parse::<TxHash>() {
        if let Ok(Some(receipt)) = provider.get_transaction_receipt(tx_hash_parsed).await {
            if let Ok(Some(tx)) = provider.get_transaction_by_hash(tx_hash_parsed).await {
                // Use effective_gas_price from the transaction or receipt
                let gas_price = tx.effective_gas_price.unwrap_or_default();
                let gas_cost = receipt.gas_used as u128 * gas_price as u128;
                return Some(gas_cost);
            }
        }
    }

    None
}
