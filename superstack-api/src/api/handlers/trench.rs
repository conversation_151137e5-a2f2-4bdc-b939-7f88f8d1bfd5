use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use superstack_data::postgres::enums::Dex;

use super::*;
use crate::{
    api::{types::*, StorageState},
    utils::get_sol_price,
};

pub async fn get_trench_tokens(
    State(state): State<StorageState>,
    Query(query): Query<PaginationParams>,
) -> Result<Json<Vec<TrenchTokenResponse>>, (StatusCode, Json<ErrorResponse>)> {
    let sol_price = get_sol_price().await;

    let checked_params = query.try_into_checked_params(sol_price)?;

    let tokens_query = checked_params.construct_trench_tokens_query();
    // todo: from redis
    let tokens = match state.indexer_data_provider.get_trench_tokens(&tokens_query).await {
        Ok(tokens) => {
            tracing::info!("Retrieved {} trench tokens", tokens.len());
            if tokens.is_empty() {
                tracing::warn!("No trench tokens found for query: {:?}", tokens_query);
            } else {
                // Log sample token for debugging
                if let Some(first_token) = tokens.first() {
                    tracing::debug!(
                        "Sample trench token: {} ({}) - Market Cap: ${:.2}, Holders: {:?}",
                        first_token.name,
                        first_token.symbol,
                        first_token.usd_market_cap,
                        first_token.holder_count
                    );
                }
            }
            tokens
        }
        Err(e) => {
            tracing::error!("Failed to get trench tokens: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new(e.to_string())),
            ));
        }
    };

    let mut trench_tokens = Vec::new();

    for token in tokens {
        let dex_url = match token.create_dex {
            Dex::Pumpfun => Some(format!("https://pump.fun/coin/{}", token.token_address)),
            _ => None,
        };

        let dev_holding = 0.0;
        let dev_sold = 0.0;
        let top10_holding = 0.0;
        let sniper_holding = 0.0;

        let market_cap_usd = token.usd_market_cap;
        let volume_usd = token.usd_total_volume;
        let bonding_curve_progress = token.bonding_curve_progress;

        let trench_token = TrenchTokenResponse {
            token_mint: token.token_address,
            name: token.name,
            symbol: token.symbol,
            image_url: token.image, // todo: translate url with path
            chain: Chain::Solana.to_string(),
            dex: Some(token.create_dex.to_string()),
            dex_url: dex_url.clone(),
            bonding_curve_progress,
            twitter: token.twitter,
            telegram: token.telegram,
            website: token.website,
            market_cap_usd,
            volume_usd,
            top10_holding,
            dev_holding,
            dev_sold,
            sniper_holding,
            holders: 0,
            snipers: 0,
            creation_time: if token.create_timestamp_millis < 0 {
                0 // Use 0 for invalid negative timestamps
            } else {
                (token.create_timestamp_millis / 1000) as u64 // Convert milliseconds to seconds
            },
        };
        trench_tokens.push(trench_token);
    }

    Ok(Json(trench_tokens))
}
