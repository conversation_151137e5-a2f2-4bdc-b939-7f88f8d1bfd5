use std::collections::HashMap;

use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use superstack_data::{
    data_provider::filter::{Time, Trend},
    postgres::enums::Chain,
};

use crate::utils::get_sol_price;

use super::*;
use crate::api::{types::*, StorageState};

#[derive(Debug, Deserialize)]
pub struct SearchQuery {
    context: Option<String>,
    chain: Option<String>,
}

pub async fn search_token(
    State(state): State<StorageState>,
    Query(query): Query<SearchQuery>,
) -> Result<Json<SearchResponse>, (StatusCode, Json<ErrorResponse>)> {
    let context = query.context.unwrap_or_else(|| "".to_string());

    // Parse chain parameter, default to Solana
    let chain = match query.chain.as_deref() {
        Some("solana") | None => Chain::Solana,
        Some("hypercore") => Chain::Hypercore,
        Some("hyperevm") => Chain::HyperEvm,
        Some(invalid) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!(
                    "Invalid chain: {}. Supported chains: solana, hypercore, hyperevm",
                    invalid
                ))),
            ));
        }
    };

    let _sol_price = get_sol_price().await;
    if let Ok(ca) = Pubkey::from_str(&context) {
        // Search for token by address using specified chain
        let token =
            state.indexer_data_provider.get_token(chain, &ca.to_string()).await.map_err(|e| {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
            })?;
        if let Some(token) = token {
            return Ok(Json(SearchResponse::new(vec![token.into()], vec![])));
        }

        // Search for pool by address using specified chain
        let pool =
            state.indexer_data_provider.get_pool(chain, &ca.to_string()).await.map_err(|e| {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
            })?;
        if let Some(pool) = pool {
            let token_mint = pool.token_address.clone();
            let token_metadata = state
                .indexer_db
                .get_token_metadata(chain, &token_mint)
                .await
                .map_err(|e| {
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
                })?
                .ok_or((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Token not found"))))?;
            let pool_response = SearchPoolResponse::from_pool_statistics(
                pool,
                token_mint,
                token_metadata.name,
                token_metadata.symbol,
                token_metadata.image,
            )
            .map_err(|e| {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
            })?;
            return Ok(Json(SearchResponse::new(vec![], vec![pool_response])));
        }
        tracing::info!("No token or pool found for address: {}", ca);
    } else {
        tracing::info!(
            "Context '{}' is not a valid Pubkey, proceeding with keyword search",
            context
        );
    }

    // Use parallel processing to fetch tokens and pools simultaneously
    let (tokens, mut search_pools) = if context.is_empty() {
        // For default search, get trending tokens and pools in parallel
        let tokens_future = async {
            let filter = superstack_data::data_provider::filter::TokensFilter {
                limit: 20,
                offset: 0,
                chain: Some(vec![chain]),
                market_cap_min: None,
                market_cap_max: None,
                fdv_min: None,
                fdv_max: None,
                liquidity_min: None,
                liquidity_max: None,
                volume_min: None,
                volume_max: None,
                txns_min: None,
                txns_max: None,
                buys_min: None,
                buys_max: None,
                sells_min: None,
                sells_max: None,
                change_min: None,
                change_max: None,
                time: Some(Time::Hour24),
                trend: Some(Trend::Trending),
                trench: None,
                create_dex: None,
                best_pool_dex: None,
                pool_dexes: None,
                is_active: Some(true),
                has_social: None,
                has_twitter: None,
                has_telegram: None,
                has_website: None,
                dex_paid: None,
                creation_time_min: None,
                creation_time_max: None,
                bonding_curve_min: None,
                bonding_curve_max: None,
                dev_holdings_min: None,
                dev_holdings_max: None,
                top10_holdings_min: None,
                top10_holdings_max: None,
                holders_min: None,
                holders_max: None,
                sniper_min: None,
                sniper_max: None,
                insider_min: None,
                insider_max: None,
                bot_min: None,
                bot_max: None,
            };
            // todo: from redis
            match state.indexer_data_provider.get_tokens(&filter).await {
                Ok(tokens) => {
                    tokens.into_iter().map(|t| t.into()).collect::<Vec<SearchTokenResponse>>()
                }
                Err(e) => {
                    tracing::error!("Failed to get default tokens: {}", e);
                    vec![]
                }
            }
        };

        let pools_future = async {
            match state.indexer_data_provider.get_trending_pools(Some(chain), 10).await {
                Ok(pools) => pools,
                Err(e) => {
                    tracing::error!("Failed to get trending pools: {}", e);
                    vec![]
                }
            }
        };

        tokio::join!(tokens_future, pools_future)
    } else {
        // For keyword search, get tokens and pools in parallel
        let tokens_future = async {
            match state.indexer_data_provider.search_tokens_by_keyword(chain, &context, 20).await {
                Ok(tokens) => {
                    tokens.into_iter().map(|t| t.into()).collect::<Vec<SearchTokenResponse>>()
                }
                Err(e) => {
                    tracing::error!("Failed to search tokens by keyword: {}", e);
                    vec![]
                }
            }
        };

        let pools_future = async {
            let mut pools_from_address = match state
                .indexer_data_provider
                .search_pools_by_address(&context)
                .await
            {
                Ok(pools) => {
                    tracing::info!(
                        "Found {} pools by address search for '{}'",
                        pools.len(),
                        context
                    );
                    pools
                }
                Err(e) => {
                    tracing::debug!("No pools found by address search for '{}': {}", context, e);
                    vec![]
                }
            };

            // Also search for pools by token name/symbol
            // Try to find tokens first then their pools
            match state.indexer_data_provider.search_tokens_by_keyword(chain, &context, 10).await {
                Ok(found_tokens) => {
                    for token in found_tokens {
                        // Use the token's own chain instead of the search chain parameter
                        match state
                            .indexer_data_provider
                            .get_token(token.chain, &token.token_address)
                            .await
                        {
                            Ok(Some(full_token)) => {
                                tracing::info!(
                                    "Token {} has pool_addresses: '{}'",
                                    token.token_address,
                                    full_token.pool_addresses
                                );
                                if !full_token.pool_addresses.is_empty() {
                                    let pool_addresses: Vec<&str> =
                                        full_token.pool_addresses.split(',').collect();
                                    match state
                                        .indexer_data_provider
                                        .get_pools(token.chain, &pool_addresses)
                                        .await
                                    {
                                        Ok(token_pools) => {
                                            tracing::info!(
                                                "Found {} pools for token {} on chain {:?}",
                                                token_pools.len(),
                                                token.token_address,
                                                token.chain
                                            );
                                            pools_from_address.extend(token_pools);
                                        }
                                        Err(e) => {
                                            tracing::warn!(
                                                "Failed to get pools for token {}: {}",
                                                token.token_address,
                                                e
                                            );
                                        }
                                    }
                                } else {
                                    tracing::info!(
                                        "Token {} has no pool addresses",
                                        token.token_address
                                    );
                                }
                            }
                            Ok(None) => {
                                tracing::info!(
                                    "Token {} not found when getting full data",
                                    token.token_address
                                );
                            }
                            Err(e) => {
                                tracing::warn!(
                                    "Failed to get full token data for {}: {}",
                                    token.token_address,
                                    e
                                );
                            }
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("Failed to search tokens by keyword for pool search: {}", e);
                }
            }

            // Remove duplicate pools
            let mut unique_pools = std::collections::HashMap::new();
            for pool in pools_from_address {
                let key = format!("{}:{}", pool.chain.to_string(), pool.pool_address);
                unique_pools.insert(key, pool);
            }
            let deduplicated_pools: Vec<_> = unique_pools.into_values().collect();

            tracing::info!(
                "Total unique pools found for '{}': {}",
                context,
                deduplicated_pools.len()
            );
            deduplicated_pools
        };

        tokio::join!(tokens_future, pools_future)
    };

    // Build tokens map for quick lookup
    let mut tokens_map: HashMap<String, SearchTokenResponse> = HashMap::new();
    for token in &tokens {
        tokens_map.insert(token.token_mint.clone(), token.clone());
    }
    search_pools.sort_by(|a, b| {
        b.usd_volume_24h.partial_cmp(&a.usd_volume_24h).unwrap_or(std::cmp::Ordering::Equal)
    });

    // Collect token addresses that need metadata lookup
    let mut missing_token_addresses = Vec::new();
    for pool in &search_pools {
        if !tokens_map.contains_key(&pool.token_address) {
            missing_token_addresses.push(pool.token_address.clone());
        }
    }

    // Batch fetch missing token metadata (fallback to individual queries for now)
    let mut missing_metadata = std::collections::HashMap::new();
    for token_address in missing_token_addresses {
        // todo: md from db
        match state.indexer_db.get_token_metadata(chain, &token_address).await {
            Ok(Some(metadata)) => {
                missing_metadata.insert(token_address, metadata);
            }
            Ok(None) => {
                tracing::warn!("Token metadata not found for {}", token_address);
            }
            Err(e) => {
                tracing::error!("Failed to get token metadata for {}: {}", token_address, e);
            }
        }
    }

    let mut pools: Vec<SearchPoolResponse> = vec![];
    for pool in search_pools {
        let token_mint = pool.token_address.clone();

        // Get token metadata from tokens_map or missing_metadata
        let (token_name, token_symbol, token_image) =
            if let Some(token) = tokens_map.get(&token_mint) {
                (token.name.clone(), token.symbol.clone(), token.image_url.clone()) // todo: translate url with path
            } else if let Some(metadata) = missing_metadata.get(&token_mint) {
                (metadata.name.clone(), metadata.symbol.clone(), metadata.image.clone()) // todo: translate url with path
            } else {
                tracing::warn!("Token metadata not found for {}", token_mint);
                continue; // Skip this pool if token metadata is missing
            };

        let pool_response = SearchPoolResponse::from_pool_statistics(
            pool,
            token_mint,
            token_name,
            token_symbol,
            token_image,
        )
        .map_err(|e| {
            (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string())))
        })?;
        pools.push(pool_response);
    }

    Ok(Json(SearchResponse::new(tokens, pools)))
}
