use axum::{extract::State, http::StatusCode, Json};

use crate::models::StorageState;

pub async fn health(
    State(state): State<StorageState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let health_status = state.health_check().await;

    let status_code = if health_status.status == "healthy" {
        StatusCode::OK
    } else {
        StatusCode::SERVICE_UNAVAILABLE
    };

    Ok(Json(serde_json::to_value(health_status).unwrap()))
}

pub async fn metrics(
    State(state): State<StorageState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let metrics_snapshot = state.get_metrics_snapshot();
    Ok(Json(serde_json::to_value(metrics_snapshot).unwrap()))
}

pub async fn simple_health(
    State(_state): State<StorageState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    // Simple health check for load balancers
    Ok(<PERSON><PERSON>(serde_json::json!({"status": "ok"})))
}
