pub mod activity;
pub mod candle;
pub mod dashboard;
pub mod depth;
pub mod healthz;
pub mod hyperevm;
pub mod hyperliquid_activity;
pub mod info;
pub mod internal;
pub mod invite;
pub mod markets;
pub mod moonpay;
pub mod perp;
pub mod perp_portfolio;
pub mod pnl_share_image;
pub mod portfolio;
pub mod position;
pub mod price;
pub mod referral;
pub mod search;
pub mod setting;
pub mod telegram;
pub mod token;
pub mod transaction;
pub mod trench;
pub mod user_indicators;
pub mod watchlist;

use std::str::FromStr;

use axum::{http::StatusCode, Json};
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use superstack_data::{
    data_provider::filter::{MarketTokensFilter, Time, Trench, TrenchTokensFilter, Trend},
    postgres::enums::{Chain, Dex},
};

use crate::utils::timestamp;

pub use activity::*;
pub use candle::*;
pub use dashboard::*;
pub use depth::*;
pub use healthz::{health, metrics, simple_health};
pub use hyperevm::*;
pub use hyperliquid_activity::*;
pub use info::*;
pub use internal::*;
pub use invite::*;
pub use markets::*;
pub use moonpay::*;
pub use perp::*;
pub use perp_portfolio::*;
pub use pnl_share_image::*;
pub use portfolio::*;
pub use position::*;
pub use price::*;
pub use referral::*;
pub use search::*;
pub use setting::*;
pub use telegram::*;
pub use token::*;
pub use transaction::*;
pub use trench::*;
pub use user_indicators::*;
pub use watchlist::*;

#[derive(Debug, Deserialize, Serialize)]
pub enum SortBy {
    #[serde(rename = "open_time")]
    OpenTime,
    #[serde(rename = "close_time")]
    CloseTime,
    #[serde(rename = "pnl")]
    Pnl,
    #[serde(rename = "pnl_percentage")]
    PnlPercentage,
}

#[derive(Debug, Deserialize, Serialize)]
pub enum PositionType {
    #[serde(rename = "spot")]
    Spot,
    #[serde(rename = "perps")]
    Perps,
}

#[derive(Debug, Deserialize, Serialize)]
pub enum PositionStatus {
    #[serde(rename = "active")]
    Active,
    #[serde(rename = "sold")]
    Sold,
    #[serde(rename = "open")]
    Open,
    #[serde(rename = "closed")]
    Closed,
}

#[derive(Debug, Deserialize, Serialize)]
pub enum PositionOrderType {
    #[serde(rename = "spot")]
    Spot,
    #[serde(rename = "perps")]
    Perps,
}

#[derive(Debug, Deserialize, Serialize)]
pub enum PositionOrderStatus {
    #[serde(rename = "open")]
    Open,
    #[serde(rename = "closed")]
    Closed,
}

#[derive(Debug, Deserialize)]
pub struct PaginationParams {
    pub wallet_address: Option<String>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
    pub sort_by: Option<SortBy>,
    pub time: Option<Time>,
    pub trend: Option<Trend>,
    pub mode: Option<Trench>,
    pub chain: Option<String>,
    pub has_social: Option<bool>,
    pub has_twitter: Option<bool>,
    pub has_telegram: Option<bool>,
    pub has_website: Option<bool>,
    pub bonding_curve_min: Option<f64>,
    pub bonding_curve_max: Option<f64>,
    pub creation_time_min: Option<u64>,
    pub creation_time_max: Option<u64>,
    pub dev_holding_min: Option<f64>,
    pub dev_holding_max: Option<f64>,
    pub top10_holding_min: Option<f64>,
    pub top10_holding_max: Option<f64>,
    pub holders_min: Option<u64>,
    pub holders_max: Option<u64>,
    pub snipers_min: Option<u64>,
    pub snipers_max: Option<u64>,
    pub insiders_min: Option<u64>,
    pub insiders_max: Option<u64>,
    pub bot_min: Option<u64>,
    pub bot_max: Option<u64>,
    pub market_cap_min: Option<f64>,
    pub market_cap_max: Option<f64>,
    pub fdv_min: Option<f64>,
    pub fdv_max: Option<f64>,
    pub liquidity_min: Option<f64>,
    pub liquidity_max: Option<f64>,
    pub volume_min: Option<f64>,
    pub volume_max: Option<f64>,
    pub txns_min: Option<u64>,
    pub txns_max: Option<u64>,
    pub buys_min: Option<u64>,
    pub buys_max: Option<u64>,
    pub sells_min: Option<u64>,
    pub sells_max: Option<u64>,
    pub change_min: Option<f64>,
    pub change_max: Option<f64>,
}

impl PaginationParams {
    fn validate_f64_with_min<T: AsRef<str>>(
        value: Option<f64>,
        min_threshold: f64,
        error_message: T,
    ) -> Result<Option<f64>, (StatusCode, Json<ErrorResponse>)> {
        match value {
            Some(val) => {
                if val < min_threshold {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse { error: error_message.as_ref().to_string() }),
                    ));
                }
                Ok(Some(val))
            }
            None => Ok(None),
        }
    }

    fn validate_positive_u64<T: AsRef<str>>(
        value: Option<u64>,
        _error_message: T,
    ) -> Result<Option<i64>, (StatusCode, Json<ErrorResponse>)> {
        match value {
            Some(val) => {
                if val > i64::MAX as u64 {
                    return Ok(Some(i64::MAX));
                }
                Ok(Some(val as i64))
            }
            None => Ok(None),
        }
    }

    pub fn try_into_checked_params(
        self,
        _sol_price: f64,
    ) -> Result<CheckedParams, (StatusCode, Json<ErrorResponse>)> {
        let wallet_address =
            self.wallet_address.map(|s| Pubkey::from_str(&s)).transpose().map_err(|_| {
                (
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse { error: "Invalid wallet address".to_string() }),
                )
            })?;

        let limit = self.limit.map(|limit| limit as i64);
        let offset = self.offset.map(|offset| offset as i64);

        // USD to SOL conversions
        let market_cap_min =
            Self::validate_f64_with_min(self.market_cap_min, 0.0, "Invalid market cap min")?;
        let market_cap_max =
            Self::validate_f64_with_min(self.market_cap_max, 0.0, "Invalid market cap max")?;
        let fdv_min = Self::validate_f64_with_min(self.fdv_min, 0.0, "Invalid fdv min")?;
        let fdv_max = Self::validate_f64_with_min(self.fdv_max, 0.0, "Invalid fdv max")?;
        let liquidity_min =
            Self::validate_f64_with_min(self.liquidity_min, 0.0, "Invalid liquidity min")?;
        let liquidity_max =
            Self::validate_f64_with_min(self.liquidity_max, 0.0, "Invalid liquidity max")?;
        let volume_min = Self::validate_f64_with_min(self.volume_min, 0.0, "Invalid volume min")?;
        let volume_max = Self::validate_f64_with_min(self.volume_max, 0.0, "Invalid volume max")?;

        // Integer validations
        let txns_min = Self::validate_positive_u64(self.txns_min, "Invalid txns min")?;
        let txns_max = Self::validate_positive_u64(self.txns_max, "Invalid txns max")?;
        let buys_min = Self::validate_positive_u64(self.buys_min, "Invalid buys min")?;
        let buys_max = Self::validate_positive_u64(self.buys_max, "Invalid buys max")?;
        let sells_min = Self::validate_positive_u64(self.sells_min, "Invalid sells min")?;
        let sells_max = Self::validate_positive_u64(self.sells_max, "Invalid sells max")?;
        let holders_min = Self::validate_positive_u64(self.holders_min, "Invalid holders min")?;
        let holders_max = Self::validate_positive_u64(self.holders_max, "Invalid holders max")?;
        let snipers_min = Self::validate_positive_u64(self.snipers_min, "Invalid snipers min")?;
        let snipers_max = Self::validate_positive_u64(self.snipers_max, "Invalid snipers max")?;
        let insiders_min = Self::validate_positive_u64(self.insiders_min, "Invalid insiders min")?;
        let insiders_max = Self::validate_positive_u64(self.insiders_max, "Invalid insiders max")?;
        let bot_min = Self::validate_positive_u64(self.bot_min, "Invalid bot min")?;
        let bot_max = Self::validate_positive_u64(self.bot_max, "Invalid bot max")?;

        // Special threshold validations
        let bonding_curve_min =
            Self::validate_f64_with_min(self.bonding_curve_min, 0.0, "Invalid bonding curve min")?;
        let bonding_curve_max =
            Self::validate_f64_with_min(self.bonding_curve_max, 0.0, "Invalid bonding curve max")?;
        let dev_holding_min =
            Self::validate_f64_with_min(self.dev_holding_min, 0.0, "Invalid dev holding min")?;
        let dev_holding_max =
            Self::validate_f64_with_min(self.dev_holding_max, 0.0, "Invalid dev holding max")?;
        let top10_holding_min =
            Self::validate_f64_with_min(self.top10_holding_min, 0.0, "Invalid top10 holding min")?;
        let top10_holding_max =
            Self::validate_f64_with_min(self.top10_holding_max, 0.0, "Invalid top10 holding max")?;

        // Normalize creation time timestamps
        let creation_time_min = timestamp::normalize_and_validate_optional(self.creation_time_min)
            .map_err(|e| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new(e))))?
            .map(|ts| {
                if ts < 0 {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse::new("creation_time_min cannot be negative")),
                    ));
                }
                Ok(ts as u64)
            })
            .transpose()?;
        let creation_time_max = timestamp::normalize_and_validate_optional(self.creation_time_max)
            .map_err(|e| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new(e))))?
            .map(|ts| {
                if ts < 0 {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse::new("creation_time_max cannot be negative")),
                    ));
                }
                Ok(ts as u64)
            })
            .transpose()?;

        // Chain validation - support comma-separated values
        let chain = match self.chain {
            Some(chain_str) => {
                let chain_parts: Vec<&str> = chain_str.split(',').map(|s| s.trim()).collect();
                let mut chains = Vec::new();

                for part in chain_parts {
                    match part.parse::<Chain>() {
                        Ok(chain) => chains.push(chain),
                        Err(_) => {
                            return Err((
                                StatusCode::BAD_REQUEST,
                                Json(ErrorResponse {
                                    error: format!(
                                        "Invalid chain '{}'. Supported values: solana, hypercore, hyperevm",
                                        part
                                    ),
                                }),
                            ));
                        }
                    }
                }

                if chains.is_empty() {
                    None
                } else {
                    Some(chains)
                }
            }
            None => None,
        };

        Ok(CheckedParams {
            wallet_address,
            limit,
            offset,
            sort_by: self.sort_by,
            time: self.time,
            trend: self.trend,
            mode: self.mode,
            chain,
            has_social: self.has_social,
            has_twitter: self.has_twitter,
            has_telegram: self.has_telegram,
            has_website: self.has_website,
            creation_time_min,
            creation_time_max,
            bonding_curve_min,
            bonding_curve_max,
            dev_holding_min,
            dev_holding_max,
            top10_holding_min,
            top10_holding_max,
            holders_min,
            holders_max,
            snipers_min,
            snipers_max,
            insiders_min,
            insiders_max,
            bot_min,
            bot_max,
            market_cap_min_amount: market_cap_min,
            market_cap_max_amount: market_cap_max,
            fdv_min_amount: fdv_min,
            fdv_max_amount: fdv_max,
            liquidity_min_amount: liquidity_min,
            liquidity_max_amount: liquidity_max,
            volume_min_amount: volume_min,
            volume_max_amount: volume_max,
            txns_min,
            txns_max,
            buys_min,
            buys_max,
            sells_min,
            sells_max,
            change_min: self.change_min,
            change_max: self.change_max,
        })
    }
}

#[derive(Debug)]
pub struct CheckedParams {
    pub wallet_address: Option<Pubkey>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
    pub sort_by: Option<SortBy>,
    pub time: Option<Time>,
    pub trend: Option<Trend>,
    pub mode: Option<Trench>,
    pub chain: Option<Vec<Chain>>,
    pub has_social: Option<bool>,
    pub has_twitter: Option<bool>,
    pub has_telegram: Option<bool>,
    pub has_website: Option<bool>,
    pub creation_time_min: Option<u64>,
    pub creation_time_max: Option<u64>,
    pub bonding_curve_min: Option<f64>,
    pub bonding_curve_max: Option<f64>,
    pub dev_holding_min: Option<f64>,
    pub dev_holding_max: Option<f64>,
    pub top10_holding_min: Option<f64>,
    pub top10_holding_max: Option<f64>,
    pub holders_min: Option<i64>,
    pub holders_max: Option<i64>,
    pub snipers_min: Option<i64>,
    pub snipers_max: Option<i64>,
    pub insiders_min: Option<i64>,
    pub insiders_max: Option<i64>,
    pub bot_min: Option<i64>,
    pub bot_max: Option<i64>,
    pub market_cap_min_amount: Option<f64>,
    pub market_cap_max_amount: Option<f64>,
    pub fdv_min_amount: Option<f64>,
    pub fdv_max_amount: Option<f64>,
    pub liquidity_min_amount: Option<f64>,
    pub liquidity_max_amount: Option<f64>,
    pub volume_min_amount: Option<f64>,
    pub volume_max_amount: Option<f64>,
    pub txns_min: Option<i64>,
    pub txns_max: Option<i64>,
    pub buys_min: Option<i64>,
    pub buys_max: Option<i64>,
    pub sells_min: Option<i64>,
    pub sells_max: Option<i64>,
    pub change_min: Option<f64>,
    pub change_max: Option<f64>,
}

impl CheckedParams {
    pub fn construct_market_tokens_query(self) -> MarketTokensFilter {
        let limit = self.limit.unwrap_or(20);
        let offset = self.offset.unwrap_or(0);
        let time = self.time.unwrap_or(Time::All);
        let trend = self.trend.unwrap_or(Trend::Trending);

        // Use provided chains or default to Solana for backward compatibility
        let chain = match self.chain {
            Some(chains) => Some(chains),
            None => Some(vec![Chain::Solana]),
        };

        let best_pool_dex = None;
        // TODO: Enable meteora filter
        let pool_dexes = None;
        // let pool_dexes = Some(vec![Dex::Meteora]);

        MarketTokensFilter {
            limit: limit as u64,
            offset: offset as u64,
            time,
            trend,
            chain,
            best_pool_dex,
            pool_dexes,
            market_cap_min: self.market_cap_min_amount,
            market_cap_max: self.market_cap_max_amount,
            fdv_min: self.fdv_min_amount,
            fdv_max: self.fdv_max_amount,
            liquidity_min: self.liquidity_min_amount,
            liquidity_max: self.liquidity_max_amount,
            volume_min: self.volume_min_amount,
            volume_max: self.volume_max_amount,
            txns_min: self.txns_min.map(|v| v as u64),
            txns_max: self.txns_max.map(|v| v as u64),
            buys_min: self.buys_min.map(|v| v as u64),
            buys_max: self.buys_max.map(|v| v as u64),
            sells_min: self.sells_min.map(|v| v as u64),
            sells_max: self.sells_max.map(|v| v as u64),
            change_min: self.change_min,
            change_max: self.change_max,
        }
    }

    pub fn construct_trench_tokens_query(self) -> TrenchTokensFilter {
        let limit = self.limit.unwrap_or(20);
        let offset = self.offset.unwrap_or(0);
        let trench = self.mode.unwrap_or(Trench::NewPairs);

        // Use provided chains or default to Solana for backward compatibility
        let chain = match self.chain {
            Some(chains) => Some(chains),
            None => Some(vec![Chain::Solana]),
        };

        TrenchTokensFilter {
            limit: limit as u64,
            offset: offset as u64,
            trench,
            chain,
            create_dex: None,
            dex_paid: None,
            has_social: self.has_social,
            has_twitter: self.has_twitter,
            has_telegram: self.has_telegram,
            has_website: self.has_website,
            creation_time_min: self.creation_time_min,
            creation_time_max: self.creation_time_max,
            bonding_curve_min: self.bonding_curve_min,
            bonding_curve_max: self.bonding_curve_max,
            dev_holdings_min: self.dev_holding_min,
            dev_holdings_max: self.dev_holding_max,
            top10_holdings_min: self.top10_holding_min,
            top10_holdings_max: self.top10_holding_max,
            holders_min: self.holders_min.map(|v| v as u64),
            holders_max: self.holders_max.map(|v| v as u64),
            sniper_min: self.snipers_min.map(|v| v as u64),
            sniper_max: self.snipers_max.map(|v| v as u64),
            insider_min: self.insiders_min.map(|v| v as u64),
            insider_max: self.insiders_max.map(|v| v as u64),
            bot_min: self.bot_min.map(|v| v as u64),
            bot_max: self.bot_max.map(|v| v as u64),
            market_cap_min: self.market_cap_min_amount,
            market_cap_max: self.market_cap_max_amount,
            fdv_min: self.fdv_min_amount,
            fdv_max: self.fdv_max_amount,
            liquidity_min: self.liquidity_min_amount,
            liquidity_max: self.liquidity_max_amount,
            volume_min: self.volume_min_amount,
            volume_max: self.volume_max_amount,
            txns_min: self.txns_min.map(|v| v as u64),
            txns_max: self.txns_max.map(|v| v as u64),
            buys_min: self.buys_min.map(|v| v as u64),
            buys_max: self.buys_max.map(|v| v as u64),
            sells_min: self.sells_min.map(|v| v as u64),
            sells_max: self.sells_max.map(|v| v as u64),
            change_min: self.change_min,
            change_max: self.change_max,
        }
    }
}

#[derive(Debug, Serialize)]
pub struct ErrorResponse {
    pub error: String,
}

impl ErrorResponse {
    pub fn new(error: impl AsRef<str>) -> Self {
        Self { error: error.as_ref().to_string() }
    }
}

#[derive(Debug, Deserialize, Serialize)]
pub struct SearchTokenParams {
    pub search_context: String,
}
