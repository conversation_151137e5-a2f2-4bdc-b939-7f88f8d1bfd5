use std::str::FromStr;

use axum::{
    extract::{Query, State},
    http::StatusCode,
    Json,
};
use serde::{Deserialize, Serialize};
use solana_sdk::{program_pack::Pack, pubkey::Pubkey};
use spl_token::state::Mint;

use super::ErrorResponse;
use crate::{
    models::{DbWalletActivity, StorageState},
    portfolio::position::PositionManager,
};

/// Helper function to validate EVM address format
fn is_valid_evm_address(addr: &str) -> bool {
    addr.starts_with("0x") && addr.len() == 42 && addr[2..].chars().all(|c| c.is_ascii_hexdigit())
}

/// Validate and deduplicate wallet addresses from comma-separated string
fn validate_wallet_addresses(
    addresses_str: Option<String>,
) -> Result<Vec<String>, (StatusCode, Json<ErrorResponse>)> {
    let addresses_str = match addresses_str {
        Some(addr) if !addr.trim().is_empty() => addr,
        _ => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new("At least one wallet address is required")),
            ));
        }
    };

    // Split by comma and trim whitespace
    let addresses: Vec<String> =
        addresses_str.split(',').map(|s| s.trim().to_string()).filter(|s| !s.is_empty()).collect();

    if addresses.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new("At least one wallet address is required")),
        ));
    }

    // Validate all wallet addresses and remove duplicates
    let mut validated_addresses = std::collections::HashSet::new();
    for address in addresses {
        let is_valid_solana = Pubkey::from_str(&address).is_ok();
        let is_valid_evm = is_valid_evm_address(&address);

        if !is_valid_solana && !is_valid_evm {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!("Invalid wallet address format: {}", address))),
            ));
        }

        validated_addresses.insert(address);
    }

    Ok(validated_addresses.into_iter().collect())
}

#[derive(Debug, Deserialize)]
pub struct ActivityParams {
    pub wallet_address: Option<String>, // Comma-separated wallet addresses to query
    pub limit: Option<i64>,
    pub before_timestamp: Option<i64>, // For timestamp-based pagination
    pub include_other: Option<bool>,   /* Whether to include "other" type activities (default:
                                        * false) */
    pub all: Option<bool>, // Whether to return all data without pagination (default: false)
    #[serde(rename = "isMainnet")]
    pub is_mainnet: Option<bool>, // Whether to filter by mainnet activities (for HyperEVM)
}

#[derive(Debug, Serialize)]
pub struct Activity {
    // Core fields
    pub tx_signature: String,
    pub activity_type: String,
    pub timestamp: i64,
    pub block_time: Option<i64>,
    pub slot: Option<i64>,
    pub chain: i16,
    pub usd_value: Option<f64>,

    // Display fields
    pub category: String,
    pub token: String,
    pub amount: String,

    // Token information for display
    pub token_name: Option<String>,
    pub token_symbol: Option<String>,
    pub token_image: Option<String>,
    pub token_ui_amount: Option<f64>,
    pub base_name: Option<String>,
    pub base_symbol: Option<String>,
    pub base_image: Option<String>,
    pub base_ui_amount: Option<f64>,

    // Address information (simplified)
    pub from_address: Option<String>,
    pub to_address: Option<String>,

    // Trading information (simplified)
    pub sell_token: Option<String>,
    pub sell_amount: Option<String>,
    pub sell_token_image: Option<String>,
    pub buy_token: Option<String>,
    pub buy_amount: Option<String>,
    pub buy_token_image: Option<String>,
    pub price: Option<String>, // Execution price (actual trade price)
    pub trading_pair: Option<String>,
    pub order_type: Option<String>,
    pub side: Option<String>, // "Buy" or "Sell" for clear direction

    // Fee information
    pub fee: Option<String>,
    pub fee_token: Option<String>,

    // Leverage information (for perp trades)
    pub leverage: Option<String>, // e.g., "10x"

    // Size information (for perp trades)
    pub size: Option<String>, // e.g., "0.1" (contract size)

    // Limit price information (for limit orders)
    pub limit_price: Option<String>, // Limit price set by user (e.g., "50000.0")

    // Legacy fields for backward compatibility (essential for internal processing)
    pub token_mint: Option<String>,
    pub token_decimals: Option<i16>,
    pub token_amount: Option<i64>,
    pub base_mint: Option<String>,
    pub base_decimals: Option<i16>,
    pub base_amount: Option<i64>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize)]
pub struct ActivityResponse {
    pub data: Vec<Activity>,
    pub has_more: bool,
    pub limit: i64,
    pub next_timestamp: Option<i64>, // Timestamp for next page
}

impl Activity {
    /// Extract reduce-only flag from metadata, supporting both regular orders and TWAP orders
    fn extract_reduce_only_flag(metadata: &serde_json::Value) -> Option<bool> {
        // First, try to extract from regular order parameters (orders[0].r)
        if let Some(order_params) = metadata.get("order_parameters") {
            if let Some(reduce_only) = order_params
                .get("orders")
                .and_then(|orders| orders.as_array())
                .and_then(|arr| arr.first())
                .and_then(|first| first.get("r"))
                .and_then(|r| r.as_bool())
            {
                return Some(reduce_only);
            }
        }

        // Second, try to extract from TWAP order parameters (r field directly)
        if let Some(twap_params) = metadata.get("twap_order_parameters") {
            if let Some(reduce_only) = twap_params.get("r").and_then(|r| r.as_bool()) {
                return Some(reduce_only);
            }
        }

        // Third, try to extract from order update metadata (reduce_only field)
        if let Some(reduce_only) = metadata.get("reduce_only").and_then(|r| r.as_bool()) {
            return Some(reduce_only);
        }

        // No reduce-only flag found
        None
    }

    /// Batch process multiple activities for better performance
    pub async fn from_db_activities_batch(
        db_activities: Vec<DbWalletActivity>,
        state: &StorageState,
    ) -> Vec<Self> {
        // Collect unique token mints for batch fetching (both token and base)
        let mut unique_tokens = std::collections::HashSet::new();
        for activity in &db_activities {
            if let Some(token_mint) = &activity.token_mint {
                unique_tokens.insert((token_mint.clone(), activity.chain));
            }
            if let Some(base_mint) = &activity.base_mint {
                unique_tokens.insert((base_mint.clone(), activity.chain));
            }
        }

        // Batch fetch token information
        let mut token_info_cache = std::collections::HashMap::new();
        for (token_mint, chain) in unique_tokens {
            let (name, symbol, image) = Self::get_token_info(state, &token_mint, chain).await;
            token_info_cache.insert(token_mint.clone(), (name, symbol, image));
        }

        // Process activities using cached token info
        let mut activities = Vec::new();
        for mut db_activity in db_activities {
            // Backfill position_action for old records that don't have it
            Self::backfill_position_action(&mut db_activity);

            let activity =
                Self::from_db_activity_with_cache(db_activity, state, &token_info_cache).await;
            activities.push(activity);
        }

        activities
    }

    /// Backfill position_action field for old records that don't have it
    fn backfill_position_action(db_activity: &mut DbWalletActivity) {
        // Only process perp_trade activities
        if !matches!(db_activity.activity_type, crate::models::ActivityType::PerpTrade) {
            return;
        }

        // Check if position_action already exists
        if let Some(metadata) = &db_activity.metadata {
            if metadata.get("position_action").is_some() {
                return; // Already has position_action
            }
        }

        // Extract side and reduce_only from metadata to determine position_action
        let metadata = db_activity.metadata.as_ref();
        let side = metadata.and_then(|m| m.get("side")).and_then(|s| s.as_str()).unwrap_or("");
        let reduce_only =
            metadata.and_then(|m| m.get("reduce_only")).and_then(|r| r.as_bool()).unwrap_or(false);

        // Determine position action
        let position_action = match (side, reduce_only) {
            ("B", false) => "Open Long",
            ("B", true) => "Close Short",
            ("A", false) => "Open Short",
            ("A", true) => "Close Long",
            _ => return, // Unknown combination, skip
        };

        // Add position_action to metadata
        if let Some(metadata) = &mut db_activity.metadata {
            if let Some(metadata_obj) = metadata.as_object_mut() {
                metadata_obj.insert(
                    "position_action".to_string(),
                    serde_json::Value::String(position_action.to_string()),
                );
            }
        } else {
            // Create new metadata with position_action
            db_activity.metadata = Some(serde_json::json!({
                "position_action": position_action
            }));
        }
    }

    /// Process single activity with token info cache
    async fn from_db_activity_with_cache(
        db_activity: DbWalletActivity,
        state: &StorageState,
        token_info_cache: &std::collections::HashMap<
            String,
            (Option<String>, Option<String>, Option<String>),
        >,
    ) -> Self {
        // Try to get token decimals from database if not present
        let token_decimals = if db_activity.token_decimals.is_some() {
            db_activity.token_decimals
        } else if let Some(token_mint) = &db_activity.token_mint {
            // Use common token decimals to avoid async calls
            Self::get_common_token_decimals(token_mint, db_activity.chain).or_else(|| {
                // Only do async lookup as last resort (this is rare)
                tracing::debug!("Async decimals lookup for unknown token: {}", token_mint);
                None // Skip async for performance, use default in UI
            })
        } else {
            None
        };

        // Try to get base token decimals from database if not present
        let base_decimals = if db_activity.base_decimals.is_some() {
            db_activity.base_decimals
        } else if let Some(base_mint) = &db_activity.base_mint {
            // Use common token decimals to avoid async calls
            Self::get_common_token_decimals(base_mint, db_activity.chain).or_else(|| {
                tracing::debug!("Async decimals lookup for unknown base token: {}", base_mint);
                None // Skip async for performance
            })
        } else {
            None
        };

        // Extract display information from metadata (simplified)
        let metadata = db_activity.metadata.as_ref();

        // Get category with enhanced perp trade classification
        let category = match db_activity.activity_type {
            crate::models::ActivityType::SpotTrade => "spot_trade".to_string(),
            crate::models::ActivityType::PerpTrade => {
                // Try to determine if it's open or close position
                // Enhanced logic to support both regular orders and TWAP orders
                if let Some(ref metadata) = db_activity.metadata {
                    let reduce_only = Self::extract_reduce_only_flag(metadata);

                    match reduce_only {
                        Some(true) => "close_position".to_string(),
                        Some(false) => "open_position".to_string(),
                        None => "perp_trade".to_string(), // Default when can't determine
                    }
                } else {
                    "perp_trade".to_string()
                }
            }
            crate::models::ActivityType::Deposit => "deposit".to_string(),
            crate::models::ActivityType::Withdraw => "withdraw".to_string(),
            crate::models::ActivityType::Transfer => "transfer".to_string(),
            crate::models::ActivityType::Liquidation => "liquidation".to_string(),
            crate::models::ActivityType::Other => "other".to_string(),
        };

        let from_address = metadata
            .and_then(|m| m.get("from_address"))
            .and_then(|a| a.as_str())
            .map(|s| s.to_string());

        let to_address = metadata
            .and_then(|m| m.get("to_address"))
            .and_then(|a| a.as_str())
            .map(|s| s.to_string());

        let trading_pair = metadata
            .and_then(|m| m.get("trading_pair"))
            .and_then(|p| p.as_str())
            .map(|s| s.to_string());

        let order_type = metadata
            .and_then(|m| m.get("order_type"))
            .and_then(|o| o.as_str())
            .map(|s| s.to_string());

        let fee = Self::extract_fee_from_metadata(&db_activity.metadata);

        let fee_token = metadata
            .and_then(|m| m.get("fee_token"))
            .and_then(|f| f.as_str())
            .map(|s| s.to_string());

        // Extract leverage information (for perp trades)
        let leverage = metadata
            .and_then(|m| m.get("leverage"))
            .and_then(|l| l.as_str())
            .map(|s| s.to_string());

        // Extract size information (for perp trades)
        let size = metadata
            .and_then(|m| {
                // Try order_parameters.orders[0].s first, then order_response[0].totalSz
                m.get("order_parameters")
                    .and_then(|op| op.get("orders"))
                    .and_then(|orders| orders.as_array())
                    .and_then(|arr| arr.first())
                    .and_then(|first| first.get("s"))
                    .and_then(|s| s.as_str())
                    .or_else(|| {
                        m.get("order_response")
                            .and_then(|or| or.as_array())
                            .and_then(|arr| arr.first())
                            .and_then(|first| first.get("totalSz"))
                            .and_then(|s| s.as_str())
                    })
            })
            .map(|s| s.to_string());

        // Extract limit price information (for limit orders only)
        let limit_price = if order_type.as_ref().map(|ot| ot.as_str()) == Some("Market") {
            // Market orders should not have limit price
            None
        } else {
            metadata
                .and_then(|m| {
                    // Primary source: OrderUpdate limit_price
                    m.get("limit_price").and_then(|lp| lp.as_str()).or_else(|| {
                        // Fallback: PerpsOrder orders[0].p (price field)
                        m.get("order_parameters")
                            .and_then(|op| op.get("orders"))
                            .and_then(|orders| orders.as_array())
                            .and_then(|arr| arr.first())
                            .and_then(|first| first.get("p"))
                            .and_then(|p| p.as_str())
                    })
                })
                .map(|s| s.to_string())
        };

        // Format token amount and calculate UI amount
        let (amount, token_ui_amount) = if let (Some(token_amount), Some(decimals)) =
            (db_activity.token_amount, token_decimals)
        {
            let ui_amount = token_amount as f64 / 10_f64.powi(decimals as i32);
            (Self::format_token_amount(token_amount, decimals), Some(ui_amount))
        } else {
            ("0".to_string(), None)
        };

        // Calculate base UI amount
        let base_ui_amount =
            if let (Some(base_amount), Some(decimals)) = (db_activity.base_amount, base_decimals) {
                Some(base_amount as f64 / 10_f64.powi(decimals as i32))
            } else {
                None
            };

        // Get token information from cache
        let (token_name, token_symbol_opt, token_image_opt) =
            if let Some(token_mint) = &db_activity.token_mint {
                token_info_cache.get(token_mint).cloned().unwrap_or((None, None, None))
            } else {
                (None, None, None)
            };

        let token_symbol = token_symbol_opt.clone().unwrap_or_else(|| {
            if let Some(token_mint) = &db_activity.token_mint {
                Self::get_common_token_symbol(token_mint, db_activity.chain)
            } else {
                "Unknown".to_string()
            }
        });

        // Apply fallback for token_image if not available from cache
        // First try to get from metadata, then from cache, then from common tokens
        let token_image = metadata
            .and_then(|m| m.get("token_image"))
            .and_then(|img| img.as_str())
            .map(|s| s.to_string())
            .or(token_image_opt)
            .or_else(|| {
                if let Some(token_mint) = &db_activity.token_mint {
                    Self::get_common_token_image(token_mint, db_activity.chain)
                } else {
                    None
                }
            });

        // Get base token information from cache
        let (base_name, base_symbol_opt, base_image_opt) =
            if let Some(base_mint) = &db_activity.base_mint {
                token_info_cache.get(base_mint).cloned().unwrap_or((None, None, None))
            } else {
                (None, None, None)
            };

        // Apply fallback for base_image if not available from cache
        let base_image = base_image_opt.or_else(|| {
            if let Some(base_mint) = &db_activity.base_mint {
                Self::get_common_token_image(base_mint, db_activity.chain)
            } else {
                // Use the same fallback logic as base_symbol for consistency
                let fallback_base_symbol = match db_activity.chain {
                    0 => "So11111111111111111111111111111111111111112", // SOL mint address
                    1 | 2 => "USDC",                                    /* HyperCore/HyperEVM */
                    // common
                    _ => "USDC", // Default for other chains
                };
                Self::get_common_token_image(fallback_base_symbol, db_activity.chain)
            }
        });

        // Determine sell/buy tokens and amounts for trades (simplified)
        let (
            sell_token,
            sell_amount,
            sell_token_image,
            buy_token,
            buy_amount,
            buy_token_image,
            price,
        ) = if category == "spot_trade" ||
            category == "perp_trade" ||
            category == "open_position" ||
            category == "close_position"
        {
            let side = metadata.and_then(|m| m.get("side")).and_then(|s| s.as_str()).unwrap_or("B");

            let price_str = metadata
                .and_then(|m| m.get("price"))
                .and_then(|p| p.as_str())
                .map(|p| p.to_string());

            let base_symbol = base_symbol_opt.clone().unwrap_or_else(|| {
                // Smart fallback based on chain and context
                if let Some(base_mint) = &db_activity.base_mint {
                    Self::get_common_token_symbol(base_mint, db_activity.chain)
                } else {
                    // For HyperEVM perp trades, base is typically USDC
                    match db_activity.chain {
                        0 => "SOL".to_string(),      // Solana native
                        1 | 2 => "USDC".to_string(), // HyperCore/HyperEVM common
                        _ => "USDC".to_string(),     // Default for other chains
                    }
                }
            });

            let base_amount_str = if let (Some(base_amount), Some(decimals)) =
                (db_activity.base_amount, base_decimals)
            {
                Self::format_token_amount(base_amount, decimals)
            } else {
                "0".to_string()
            };

            if side == "B" {
                // Buy order: sell base_token, buy main_token
                (
                    Some(base_symbol),          // sell_token
                    Some(base_amount_str),      // sell_amount
                    base_image.clone(),         // sell_token_image
                    Some(token_symbol.clone()), // buy_token
                    Some(amount.clone()),       // buy_amount
                    token_image.clone(),        // buy_token_image
                    price_str,
                )
            } else {
                // Sell order: sell main_token, buy base_token
                (
                    Some(token_symbol.clone()), // sell_token
                    Some(amount.clone()),       // sell_amount
                    token_image.clone(),        // sell_token_image
                    Some(base_symbol),          // buy_token
                    Some(base_amount_str),      // buy_amount
                    base_image.clone(),         // buy_token_image
                    price_str,
                )
            }
        } else {
            (None, None, None, None, None, None, None)
        };

        Activity {
            // Core fields
            tx_signature: db_activity.tx_signature,
            activity_type: db_activity.activity_type.to_string(),
            timestamp: db_activity.timestamp,
            block_time: db_activity.block_time,
            slot: db_activity.slot,
            chain: db_activity.chain,
            usd_value: db_activity.usd_value,

            // Display fields
            category,
            token: token_symbol.clone(),
            amount,

            // Token information for display
            token_name,
            token_symbol: token_symbol_opt,
            token_image,
            token_ui_amount,
            base_name,
            base_symbol: base_symbol_opt,
            base_image,
            base_ui_amount,

            from_address,
            to_address,
            sell_token,
            sell_amount,
            sell_token_image,
            buy_token,
            buy_amount,
            buy_token_image,
            price,
            trading_pair,
            order_type,
            side: Self::extract_side_from_metadata(&db_activity.metadata),
            fee,
            fee_token,
            leverage,
            size,
            limit_price,

            // Legacy fields for backward compatibility
            token_mint: db_activity.token_mint,
            token_decimals,
            token_amount: db_activity.token_amount,
            base_mint: db_activity.base_mint,
            base_decimals,
            base_amount: db_activity.base_amount,
            // Removed provider field - use exchange field instead
            metadata: db_activity.metadata,
        }
    }

    /// Get common token decimals without async lookup for performance
    fn get_common_token_decimals(token_mint: &str, chain: i16) -> Option<i16> {
        match chain {
            0 => {
                // Solana
                match token_mint {
                    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" => Some(6), // USDC
                    "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB" => Some(6), // USDT
                    "So11111111111111111111111111111111111111112" => Some(9),  // SOL
                    "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So" => Some(9),  // mSOL
                    _ => Some(9), // Default for Solana tokens
                }
            }
            1 | 2 => {
                // HyperCore (1) and HyperEVM (2) - same token decimals
                match token_mint.to_uppercase().as_str() {
                    "USDC" => Some(6),
                    "USDT" => Some(6),
                    "SOL" => Some(9), // SOL has 9 decimals even on HyperEVM
                    "BTC" | "BITCOIN" => Some(18),
                    "ETH" | "ETHEREUM" => Some(18),
                    _ => Some(18), // Default for EVM tokens
                }
            }
            _ => Some(18), // Default for other chains
        }
    }

    /// Get common token symbol for smart fallback
    pub fn get_common_token_symbol(token_mint: &str, chain: i16) -> String {
        match chain {
            0 => {
                // Solana
                match token_mint {
                    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" => "USDC".to_string(),
                    "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB" => "USDT".to_string(),
                    "So11111111111111111111111111111111111111112" => "SOL".to_string(),
                    "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So" => "mSOL".to_string(),
                    _ => {
                        // Try to extract symbol from mint address (last 4 chars as fallback)
                        if token_mint.len() >= 4 {
                            token_mint[token_mint.len() - 4..].to_uppercase()
                        } else {
                            "TOKEN".to_string()
                        }
                    }
                }
            }
            2 => {
                // HyperEVM
                match token_mint.to_uppercase().as_str() {
                    "USDC" => "USDC".to_string(),
                    "USDT" => "USDT".to_string(),
                    "BTC" | "BITCOIN" => "BTC".to_string(),
                    "ETH" | "ETHEREUM" => "ETH".to_string(),
                    _ => token_mint.to_uppercase(), // Use as-is for HyperEVM
                }
            }
            _ => token_mint.to_uppercase(), // Default for other chains
        }
    }

    /// Extract base token from trading pair (e.g., "SOL/USDC" -> "USDC")
    pub fn extract_base_token_from_trading_pair(trading_pair: &str) -> Option<String> {
        trading_pair.split('/').nth(1).map(|s| s.trim().to_string())
    }

    /// Get common token image for smart fallback
    pub fn get_common_token_image(token_mint: &str, chain: i16) -> Option<String> {
        match chain {
            0 => {
                // Solana
                match token_mint {
                    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" => Some(
                        "https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png"
                            .to_string(),
                    ), // USDC
                    "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB" => Some(
                        "https://assets.coingecko.com/coins/images/325/small/Tether.png"
                            .to_string(),
                    ), // USDT
                    "So11111111111111111111111111111111111111112" => Some(
                        "https://assets.coingecko.com/coins/images/4128/small/solana.png"
                            .to_string(),
                    ), // SOL
                    "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So" => Some(
                        "https://assets.coingecko.com/coins/images/17752/small/mSOL.png"
                            .to_string(),
                    ), // mSOL
                    _ => None,
                }
            }
            1 | 2 => {
                // HyperLiquid
                match token_mint.to_uppercase().as_str() {
                    "USDC" => Some(
                        "https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png"
                            .to_string(),
                    ),
                    "USDT" => Some(
                        "https://assets.coingecko.com/coins/images/325/small/Tether.png"
                            .to_string(),
                    ),
                    "BTC" | "BITCOIN" => Some(
                        "https://assets.coingecko.com/coins/images/1/small/bitcoin.png".to_string(),
                    ),
                    "ETH" | "ETHEREUM" => Some(
                        "https://assets.coingecko.com/coins/images/279/small/ethereum.png"
                            .to_string(),
                    ),
                    _ => Some(format!(
                        "https://app.hyperliquid.xyz/coins/{}.svg",
                        token_mint.to_uppercase()
                    )),
                }
            }
            _ => None, // No fallback for other chains
        }
    }

    /// Extract fee information from metadata, handling different data types and sources
    fn extract_fee_from_metadata(metadata: &Option<serde_json::Value>) -> Option<String> {
        if let Some(metadata) = metadata {
            // Priority 1: For aggregated trades, use total_fees (most accurate)
            if let Some(total_fees) = metadata.get("total_fees") {
                return Self::json_value_to_string(total_fees);
            }

            // Priority 2: Use fee field (supports both string and number types)
            if let Some(fee) = metadata.get("fee") {
                return Self::json_value_to_string(fee);
            }
        }
        None
    }

    /// Convert JSON value to string, handling both string and number types
    fn json_value_to_string(value: &serde_json::Value) -> Option<String> {
        match value {
            serde_json::Value::String(s) => Some(s.clone()),
            serde_json::Value::Number(n) => Some(n.to_string()),
            _ => None,
        }
    }

    /// Extract side information from metadata
    fn extract_side_from_metadata(metadata: &Option<serde_json::Value>) -> Option<String> {
        metadata.as_ref().and_then(|m| m.get("side")).and_then(|s| s.as_str()).map(
            |side| match side {
                "B" => "Buy".to_string(),
                "A" => "Sell".to_string(),
                _ => side.to_string(),
            },
        )
    }

    /// Format token amount with proper decimal places
    fn format_token_amount(amount: i64, decimals: i16) -> String {
        if decimals <= 0 {
            return amount.to_string();
        }

        let divisor = 10_i64.pow(decimals as u32);
        let whole = amount / divisor;
        let fractional = amount % divisor;

        if fractional == 0 {
            whole.to_string()
        } else {
            // Remove trailing zeros
            let fractional_str = format!("{:0width$}", fractional, width = decimals as usize);
            let trimmed = fractional_str.trim_end_matches('0');
            if trimmed.is_empty() {
                whole.to_string()
            } else {
                format!("{}.{}", whole, trimmed)
            }
        }
    }

    async fn get_token_decimals(state: &StorageState, token_mint: &str, chain: i16) -> Option<i16> {
        let chain_enum = match chain {
            0 => superstack_data::postgres::enums::Chain::Solana,
            1 => superstack_data::postgres::enums::Chain::Hypercore,
            2 => superstack_data::postgres::enums::Chain::HyperEvm,
            _ => superstack_data::postgres::enums::Chain::Solana,
        };

        // Try to get token from indexer data provider
        match state.indexer_data_provider.get_token(chain_enum, token_mint).await {
            Ok(Some(token)) => Some(token.decimals as i16),
            Ok(None) => {
                tracing::debug!("Token not found in indexer: {}", token_mint);
                // Fallback to RPC for Solana chain
                if chain == 0 {
                    Self::get_token_decimals_from_rpc(token_mint).await
                } else {
                    None
                }
            }
            Err(e) => {
                tracing::warn!("Failed to get token decimals for {}: {}", token_mint, e);
                // Fallback to RPC for Solana chain
                if chain == 0 {
                    Self::get_token_decimals_from_rpc(token_mint).await
                } else {
                    None
                }
            }
        }
    }

    /// Get token decimals from Solana RPC by querying mint account
    async fn get_token_decimals_from_rpc(token_mint: &str) -> Option<i16> {
        let mint_pubkey = match Pubkey::from_str(token_mint) {
            Ok(pubkey) => pubkey,
            Err(e) => {
                tracing::warn!("Invalid mint pubkey {}: {}", token_mint, e);
                return None;
            }
        };

        let rpc_client = crate::utils::get_rpc_client();
        match rpc_client.get_account(&mint_pubkey).await {
            Ok(account) => {
                // Check if this is a valid SPL Token mint account
                let spl_token_program_id = spl_token::id();

                if account.owner == spl_token_program_id {
                    // Standard SPL Token
                    match Mint::unpack(&account.data) {
                        Ok(mint) => {
                            tracing::debug!(
                                "Got decimals {} for SPL token {} from RPC",
                                mint.decimals,
                                token_mint
                            );
                            Some(mint.decimals as i16)
                        }
                        Err(e) => {
                            tracing::warn!(
                                "Failed to unpack SPL mint account for {}: {}",
                                token_mint,
                                e
                            );
                            None
                        }
                    }
                } else {
                    tracing::warn!("Unsupported token program owner for mint {}: {}. Only SPL Token is supported.", token_mint, account.owner);
                    None
                }
            }
            Err(e) => {
                tracing::warn!("Failed to get mint account for {}: {}", token_mint, e);
                None
            }
        }
    }

    async fn calculate_usd_value(
        db_activity: &DbWalletActivity,
        token_ui_amount: Option<f64>,
        base_ui_amount: Option<f64>,
        state: &StorageState,
        chain: i16,
    ) -> Option<f64> {
        // Removed unused imports: SOL_MINT, USDC_MINT, USDT_MINT

        // For swap activities, calculate based on base amount and price
        if db_activity.activity_type.to_string() == "swap" {
            if let (Some(base_mint), Some(base_amount)) = (&db_activity.base_mint, base_ui_amount) {
                return Self::convert_to_usd(base_amount, base_mint).await;
            }
        }

        // For limit orders, try to calculate based on token amount and current price
        if db_activity.activity_type.to_string() == "limit_order" {
            if let (Some(token_mint), Some(token_amount)) =
                (&db_activity.token_mint, token_ui_amount)
            {
                if let Some(token_price) = Self::get_token_price(state, token_mint, chain).await {
                    return Some(token_amount * token_price);
                }
            }
        }

        None
    }

    async fn convert_to_usd(amount: f64, mint: &str) -> Option<f64> {
        use crate::constant::{SOL_MINT, USDC_MINT, USDT_MINT};

        match mint {
            SOL_MINT => {
                let sol_price = crate::utils::get_sol_price().await;
                Some(amount * sol_price)
            }
            USDC_MINT | USDT_MINT => Some(amount), // Already in USD
            _ => {
                // For other tokens, try to get price from Jupiter
                match crate::jupiter::price::get_price(&[mint]).await {
                    Ok(prices) if !prices.is_empty() => Some(amount * prices[0].price),
                    _ => {
                        tracing::debug!("Could not get price for token: {}", mint);
                        None
                    }
                }
            }
        }
    }

    async fn get_token_price(state: &StorageState, token_mint: &str, chain: i16) -> Option<f64> {
        let chain_enum = match chain {
            0 => superstack_data::postgres::enums::Chain::Solana,
            1 => superstack_data::postgres::enums::Chain::Hypercore,
            2 => superstack_data::postgres::enums::Chain::HyperEvm,
            _ => superstack_data::postgres::enums::Chain::Solana,
        };

        // Try to get token price from indexer data provider
        match state.indexer_data_provider.get_token(chain_enum, token_mint).await {
            Ok(Some(token)) => Some(token.usd_price),
            Ok(None) => {
                // Fallback to Jupiter price API for Solana tokens
                if chain == 0 {
                    match crate::jupiter::price::get_price(&[token_mint]).await {
                        Ok(prices) if !prices.is_empty() => Some(prices[0].price),
                        _ => None,
                    }
                } else {
                    None
                }
            }
            Err(e) => {
                tracing::warn!("Failed to get token price for {}: {}", token_mint, e);
                None
            }
        }
    }

    /// Get token information (name, symbol and image) from indexer or database
    async fn get_token_info(
        state: &StorageState,
        token_mint: &str,
        chain: i16,
    ) -> (Option<String>, Option<String>, Option<String>) {
        // Convert chain to enum
        let chain_enum = match chain {
            0 => superstack_data::postgres::enums::Chain::Solana,
            1 => superstack_data::postgres::enums::Chain::Hypercore,
            2 => superstack_data::postgres::enums::Chain::HyperEvm,
            _ => return (None, None, None),
        };

        // Try to get token from indexer data provider first
        match state.indexer_data_provider.get_token(chain_enum, token_mint).await {
            Ok(Some(token)) => (Some(token.name), Some(token.symbol), token.image),
            Ok(None) | Err(_) => {
                // Fallback to database query for both None and Error cases
                Self::get_token_info_from_db(state, token_mint).await
            }
        }
    }

    /// Get token info from database (extracted to avoid duplication)
    async fn get_token_info_from_db(
        state: &StorageState,
        token_mint: &str,
    ) -> (Option<String>, Option<String>, Option<String>) {
        match state.get_token_info(token_mint).await {
            Ok(Some(token_info)) => {
                (Some(token_info.name), Some(token_info.symbol), token_info.image)
            }
            Ok(None) => (None, None, None),
            Err(e) => {
                tracing::warn!("Failed to get token info for {}: {}", token_mint, e);
                (None, None, None)
            }
        }
    }

    // Removed extract_provider_from_metadata - use exchange field instead

    /// Map AMM address to known DEX names (simplified to main DEXs)
    fn map_amm_to_dex_name(amm_address: &str) -> String {
        // Only include the most commonly used DEXs
        match amm_address {
            "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" => "Raydium".to_string(),
            "whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc" => "Orca".to_string(),
            "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" => "Meteora".to_string(),
            "PhoeNiXZ8ByJGLkxNfZRnkUfjvmuYqLR89jjFHGqdXY" => "Phoenix".to_string(),
            "srmqPvymJeFKQ4zGQed1GFppgkRHL9kaELCbyksJtPX" => "OpenBook".to_string(),
            // Default: truncated address for unknown DEXs
            _ => {
                if amm_address.len() > 8 {
                    format!("{}...{}", &amm_address[..4], &amm_address[amm_address.len() - 4..])
                } else {
                    amm_address.to_string()
                }
            }
        }
    }
}

pub async fn get_wallet_activity(
    State(state): State<StorageState>,
    Query(params): Query<ActivityParams>,
) -> Result<Json<ActivityResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet addresses
    let wallet_addresses = validate_wallet_addresses(params.wallet_address)?;

    // Handle all parameter - if true, ignore limit and return all data
    let return_all = params.all.unwrap_or(false);
    let limit = if return_all {
        i64::MAX // Use maximum value to effectively disable limit
    } else {
        params.limit.unwrap_or(50) // Remove the .min(200) cap
    };

    // Strategy: Try to update with 10s timeout, then return data
    // This balances user experience with data freshness

    // Trigger update for Solana addresses only (refresh is only needed for Solana)
    for address in &wallet_addresses {
        if Pubkey::from_str(address).is_ok() {
            // This is a valid Solana address, trigger background update
            let state_clone = state.clone();
            let wallet_clone = address.clone();
            tokio::spawn(async move {
                if let Err(e) =
                    PositionManager::update_wallet_activity(&state_clone, &wallet_clone).await
                {
                    tracing::warn!("Background activity update failed for {}: {}", wallet_clone, e);
                } else {
                    tracing::debug!("Background activity update completed for {}", wallet_clone);
                }
            });
            break; // Only update one Solana address to avoid duplicate work
        }
    }

    // Get activities from database with timestamp-based pagination
    // For all=true, don't fetch extra; for pagination, fetch one extra to check if there are more
    let fetch_limit = if return_all { limit } else { limit + 1 };
    let include_other = params.include_other.unwrap_or(false);

    // Multi-address query: get activities from all addresses and merge
    let mut all_activities = Vec::new();

    for address in &wallet_addresses {
        match state
            .get_wallet_activities_with_timestamp(
                address,
                fetch_limit,
                params.before_timestamp,
                include_other,
                params.is_mainnet,
            )
            .await
        {
            Ok(activities) => all_activities.extend(activities),
            Err(e) => {
                tracing::warn!("Failed to get activities for address {}: {}", address, e);
            }
        }
    }

    // Sort by timestamp descending and limit
    all_activities.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
    all_activities.truncate(fetch_limit as usize);

    let db_activities = all_activities;

    // Check if there are more activities than requested (only for paginated queries)
    let has_more = if return_all { false } else { db_activities.len() > limit as usize };

    // Take only the requested number of activities (for paginated queries)
    let activities_to_process = if return_all {
        &db_activities[..] // Return all activities
    } else if has_more {
        &db_activities[..limit as usize] // Return limited activities
    } else {
        &db_activities[..] // Return all available activities
    };

    // Generate next_timestamp for timestamp-based pagination (only for paginated queries)
    let next_timestamp = if return_all {
        None // No pagination for all=true
    } else if has_more && !activities_to_process.is_empty() {
        let last_activity = &activities_to_process[activities_to_process.len() - 1];
        Some(last_activity.timestamp)
    } else {
        None
    };

    // Convert to response format with batch token info fetching
    let activities =
        Activity::from_db_activities_batch(activities_to_process.to_vec(), &state).await;

    let response = ActivityResponse { data: activities, has_more, limit, next_timestamp };

    Ok(Json(response))
}

#[derive(Debug, Deserialize)]
pub struct RefreshActivityParams {
    pub wallet_address: String,
}

#[derive(Debug, Serialize)]
pub struct RefreshActivityResponse {
    pub success: bool,
    pub message: String,
    pub processed_signatures: usize,
}

/// Force refresh wallet activity by fetching and processing new signatures
pub async fn refresh_wallet_activity(
    State(state): State<StorageState>,
    Query(params): Query<RefreshActivityParams>,
) -> Result<Json<RefreshActivityResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet address
    let wallet_address = params.wallet_address;
    if Pubkey::from_str(&wallet_address).is_err() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new("Invalid wallet address format")),
        ));
    }

    // Update wallet activity
    match PositionManager::update_wallet_activity(&state, &wallet_address).await {
        Ok(_) => {
            let response = RefreshActivityResponse {
                success: true,
                message: "Wallet activity refreshed successfully".to_string(),
                processed_signatures: 0, // We could track this if needed
            };
            Ok(Json(response))
        }
        Err(e) => {
            tracing::error!("Failed to refresh wallet activity for {}: {}", wallet_address, e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to refresh wallet activity")),
            ))
        }
    }
}

#[derive(Debug, Deserialize)]
pub struct ActivityStatsParams {
    pub wallet_address: Option<String>, // Comma-separated wallet addresses for stats
}

#[derive(Debug, Serialize)]
pub struct ActivityStats {
    pub total_activities: i64,
    pub swap_count: i64,
    pub limit_order_count: i64,
    pub receive_count: i64,
    pub send_count: i64,
    pub other_count: i64,
    pub total_usd_volume: f64,
    pub first_activity_timestamp: Option<i64>,
    pub last_activity_timestamp: Option<i64>,
}

/// Get activity statistics for a wallet
pub async fn get_wallet_activity_stats(
    State(state): State<StorageState>,
    Query(params): Query<ActivityStatsParams>,
) -> Result<Json<ActivityStats>, (StatusCode, Json<ErrorResponse>)> {
    // Validate wallet addresses
    let wallet_addresses = validate_wallet_addresses(params.wallet_address)?;

    // Get activity statistics using efficient SQL aggregation
    // Combine stats from all addresses
    let mut combined_stats: (i64, i64, i64, i64, i64, i64, f64, Option<i64>, Option<i64>) =
        (0i64, 0i64, 0i64, 0i64, 0i64, 0i64, 0.0f64, None, None);

    for address in &wallet_addresses {
        match state.get_wallet_activity_stats(address).await {
            Ok(stats) => {
                combined_stats.0 += stats.0; // total_activities
                combined_stats.1 += stats.1; // swap_count
                combined_stats.2 += stats.2; // limit_order_count
                combined_stats.3 += stats.3; // receive_count
                combined_stats.4 += stats.4; // send_count
                combined_stats.5 += stats.5; // transfer_count
                combined_stats.6 += stats.6; // total_usd_volume

                // Use the earliest first timestamp
                combined_stats.7 = match (combined_stats.7, stats.7) {
                    (Some(a), Some(b)) => Some(a.min(b)),
                    (Some(a), None) => Some(a),
                    (None, Some(b)) => Some(b),
                    (None, None) => None,
                };

                // Use the latest last timestamp
                combined_stats.8 = match (combined_stats.8, stats.8) {
                    (Some(a), Some(b)) => Some(a.max(b)),
                    (Some(a), None) => Some(a),
                    (None, Some(b)) => Some(b),
                    (None, None) => None,
                };
            }
            Err(e) => {
                tracing::warn!(
                    "Failed to get wallet activity stats for address {}: {}",
                    address,
                    e
                );
            }
        }
    }

    let (
        total_activities,
        swap_count,
        limit_order_count,
        receive_count,
        send_count,
        transfer_count,
        total_usd_volume,
        first_timestamp,
        last_timestamp,
    ) = combined_stats;

    let stats = ActivityStats {
        total_activities,
        swap_count,
        limit_order_count,
        receive_count,
        send_count,
        other_count: transfer_count,
        total_usd_volume,
        first_activity_timestamp: first_timestamp,
        last_activity_timestamp: last_timestamp,
    };

    Ok(Json(stats))
}

// Removed tests for provider extraction - functionality moved to exchange field

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_common_token_image_solana() {
        // Test SOL
        let sol_image =
            Activity::get_common_token_image("So11111111111111111111111111111111111111112", 0);
        assert!(sol_image.is_some());
        assert!(sol_image.unwrap().contains("solana.png"));

        // Test USDC
        let usdc_image =
            Activity::get_common_token_image("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", 0);
        assert!(usdc_image.is_some());
        assert!(usdc_image.unwrap().contains("USD_Coin_icon.png"));

        // Test USDT
        let usdt_image =
            Activity::get_common_token_image("Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", 0);
        assert!(usdt_image.is_some());
        assert!(usdt_image.unwrap().contains("Tether.png"));

        // Test unknown token
        let unknown_image = Activity::get_common_token_image("UnknownTokenMint123456789", 0);
        assert!(unknown_image.is_none());
    }

    #[test]
    fn test_get_common_token_image_hyperevm() {
        // Test USDC
        let usdc_image = Activity::get_common_token_image("USDC", 2);
        assert!(usdc_image.is_some());
        assert!(usdc_image.unwrap().contains("USD_Coin_icon.png"));

        // Test BTC
        let btc_image = Activity::get_common_token_image("BTC", 2);
        assert!(btc_image.is_some());
        assert!(btc_image.unwrap().contains("bitcoin.png"));

        // Test ETH
        let eth_image = Activity::get_common_token_image("ETH", 2);
        assert!(eth_image.is_some());
        assert!(eth_image.unwrap().contains("ethereum.png"));

        // Test unknown token (should fallback to hyperliquid.xyz)
        let unknown_image = Activity::get_common_token_image("UNKNOWN", 2);
        assert!(unknown_image.is_some());
        assert!(unknown_image.unwrap().contains("hyperliquid.xyz/coins/UNKNOWN.svg"));
    }

    #[test]
    fn test_get_common_token_image_other_chains() {
        // Test unsupported chain
        let image = Activity::get_common_token_image("SomeToken", 999);
        assert!(image.is_none());
    }

    #[test]
    fn test_extract_reduce_only_flag() {
        use serde_json::json;

        // Test regular order parameters
        let metadata = json!({
            "order_parameters": {
                "orders": [
                    {
                        "r": true
                    }
                ]
            }
        });
        assert_eq!(Activity::extract_reduce_only_flag(&metadata), Some(true));

        let metadata = json!({
            "order_parameters": {
                "orders": [
                    {
                        "r": false
                    }
                ]
            }
        });
        assert_eq!(Activity::extract_reduce_only_flag(&metadata), Some(false));

        // Test TWAP order parameters
        let metadata = json!({
            "twap_order_parameters": {
                "r": true
            }
        });
        assert_eq!(Activity::extract_reduce_only_flag(&metadata), Some(true));

        let metadata = json!({
            "twap_order_parameters": {
                "r": false
            }
        });
        assert_eq!(Activity::extract_reduce_only_flag(&metadata), Some(false));

        // Test order update metadata
        let metadata = json!({
            "reduce_only": true
        });
        assert_eq!(Activity::extract_reduce_only_flag(&metadata), Some(true));

        // Test no reduce_only flag found
        let metadata = json!({
            "other_field": "value"
        });
        assert_eq!(Activity::extract_reduce_only_flag(&metadata), None);
    }

    #[test]
    fn test_extract_fee_from_metadata() {
        // Test case 1: total_fees as number (aggregated trades)
        let metadata_with_number_fee = Some(json!({
            "total_fees": 0.006682,
            "fee": "should_not_use_this",
            "fee_token": "USDC"
        }));
        assert_eq!(
            Activity::extract_fee_from_metadata(&metadata_with_number_fee),
            Some("0.006682".to_string())
        );

        // Test case 2: fee as string (deposits/withdraws)
        let metadata_with_string_fee = Some(json!({
            "fee": "1.0",
            "fee_token": "USDC"
        }));
        assert_eq!(
            Activity::extract_fee_from_metadata(&metadata_with_string_fee),
            Some("1.0".to_string())
        );

        // Test case 3: fee as number
        let metadata_with_number_fee_only = Some(json!({
            "fee": 2.5,
            "fee_token": "USDC"
        }));
        assert_eq!(
            Activity::extract_fee_from_metadata(&metadata_with_number_fee_only),
            Some("2.5".to_string())
        );

        // Test case 4: no fee information
        let metadata_without_fee = Some(json!({
            "other_field": "value"
        }));
        assert_eq!(
            Activity::extract_fee_from_metadata(&metadata_without_fee),
            None
        );

        // Test case 5: null metadata
        assert_eq!(Activity::extract_fee_from_metadata(&None), None);
    }

    #[test]
    fn test_json_value_to_string() {
        // Test string value
        let string_value = json!("1.5");
        assert_eq!(
            Activity::json_value_to_string(&string_value),
            Some("1.5".to_string())
        );

        // Test number value
        let number_value = json!(2.5);
        assert_eq!(
            Activity::json_value_to_string(&number_value),
            Some("2.5".to_string())
        );

        // Test integer value
        let int_value = json!(10);
        assert_eq!(
            Activity::json_value_to_string(&int_value),
            Some("10".to_string())
        );

        // Test null value
        let null_value = json!(null);
        assert_eq!(Activity::json_value_to_string(&null_value), None);

        // Test boolean value (should return None)
        let bool_value = json!(true);
        assert_eq!(Activity::json_value_to_string(&bool_value), None);
    }
}
