use std::str::FromStr;

use anyhow::Result;
use axum::{
    extract::{Json, Path, State},
    http::StatusCode,
};
use base64::{engine::general_purpose, Engine as _};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use solana_sdk::{pubkey::Pubkey, signature::Signature, transaction::VersionedTransaction};

use super::ErrorResponse;
use crate::{
    models::{DbTransaction, StorageState, TransactionStatus},
    relay::{CliqueRelayService, GetQuoteParams, GetSwapParams, QuoteResponse, SwapResponse},
};

// Request to send a signed transaction
#[derive(Debug, Clone, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SendTransactionRequest {
    wallet_address: String,
    serialized_transaction: String, // Base64 encoded signed transaction
}

// Response with transaction signature
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct SendTransactionResponse {
    signature: String,
}

// Transaction status response
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct TransactionStatusResponse {
    signature: String,
    status: TransactionStatus,
}

// Handler to send a transaction
pub async fn send_transaction(
    State(state): State<StorageState>,
    Json(request): Json<SendTransactionRequest>,
) -> Result<Json<SendTransactionResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Decode the transaction
    let transaction_bytes =
        general_purpose::STANDARD.decode(&request.serialized_transaction).map_err(|_| {
            (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid transaction encoding")))
        })?;

    let transaction: VersionedTransaction =
        bincode::deserialize(&transaction_bytes).map_err(|_| {
            (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Unable to deserialize transaction")))
        })?;

    let wallet_address = Pubkey::from_str(&request.wallet_address).map_err(|_| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address")))
    })?;

    crate::transaction::verify_transaction(&transaction, &wallet_address).map_err(|_| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Verify transaction failed")))
    })?;

    let signature =
        crate::transaction::send_transaction(&transaction, false, 10).await.map_err(|e| {
            let error_message = e.to_string();
            tracing::error!("Send transaction error: {}", error_message);

            let lower_error_message = error_message.to_lowercase();
            if lower_error_message.contains("insufficient lamports") ||
                lower_error_message.contains("insufficient balance")
            {
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Insufficient balance")))
            } else if lower_error_message.contains("incorrect program id") {
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Incorrect program ID")))
            } else if lower_error_message.contains("http status server error") {
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("RPC server error")))
            } else if lower_error_message.contains("http status client error") {
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("RPC client error")))
            } else if lower_error_message.contains("invalid instruction") {
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid instruction")))
            } else {
                (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Send transaction failed")))
            }
        })?;

    let db_transaction = DbTransaction {
        signature: signature.to_string(),
        wallet_address: wallet_address.to_string(),
        status: TransactionStatus::Pending,
        is_processed: false,
        created_at: Utc::now().timestamp(),
        chain: crate::constant::CHAIN_SOLANA, // Solana transactions
    };

    state.insert_transaction(&db_transaction).await.map_err(|_| {
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Insert transaction failed")))
    })?;

    Ok(Json(SendTransactionResponse { signature: signature.to_string() }))
}

// Handler to check transaction status
pub async fn get_transaction_status(
    State(state): State<StorageState>,
    Path(signature_str): Path<String>,
) -> Result<Json<TransactionStatusResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Parse the signature
    let signature = Signature::from_str(&signature_str).map_err(|_| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid transaction signature")))
    })?;

    // Check the transaction status
    let status =
        crate::transaction::check_transaction_status(&signature, false).await.map_err(|_| {
            (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Check transaction status failed")))
        })?;

    if status.is_finalized() {
        state.update_transaction_status_if_pending(&signature_str, &status).await.map_err(
            |_e| {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Update transaction status failed")),
                )
            },
        )?;
    }

    Ok(Json(TransactionStatusResponse { signature: signature_str, status }))
}

pub async fn get_relay_quote(
    Json(request): Json<GetQuoteParams>,
) -> Result<Json<QuoteResponse>, (StatusCode, Json<ErrorResponse>)> {
    let quote = CliqueRelayService::get_quote(&request)
        .await
        .map_err(|_| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Get quote failed"))))?;

    match serde_json::from_value(quote.clone()) {
        Ok(v) => Ok(Json(v)),
        Err(_) => {
            let error_message = format!("Get quote failed: {:?}", quote);
            Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new(error_message))))
        }
    }
}

pub async fn get_relay_swap(
    Json(request): Json<GetSwapParams>,
) -> Result<Json<SwapResponse>, (StatusCode, Json<ErrorResponse>)> {
    let swap = CliqueRelayService::get_swap(&request)
        .await
        .map_err(|_| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Get swap failed"))))?;

    match serde_json::from_value(swap.clone()) {
        Ok(v) => Ok(Json(v)),
        Err(_) => {
            let error_message = format!("Get swap failed: {:?}", swap);
            Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new(error_message))))
        }
    }
}
