use axum::{
    extract::{Multipart, Path, Query, State},
    http::StatusCode,
    Json,
};
use serde::Deserialize;
use std::str::FromStr;

use super::*;
use crate::{
    api::{types::*, StorageState},
    models::CreatePnlShareImage,
    utils::image::ImageProcessor,
};

#[derive(Debug, Deserialize)]
pub struct GetImagesQuery {
    pub wallet_address: String,
}

#[derive(Debug, Deserialize)]
pub struct UploadImageQuery {
    pub wallet_address: String,
}

/// Get all PnL share images for a user (only custom images)
pub async fn get_pnl_share_images(
    State(state): State<StorageState>,
    Query(query): Query<GetImagesQuery>,
) -> Result<Json<PnlShareImagesListResponse>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;

    // Validate wallet address format
    let _wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    // Get custom images from database
    let custom_images = state.get_pnl_share_images(&wallet_address).await.map_err(|e| {
        tracing::error!("Failed to get PnL share images: {}", e);
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to get images")))
    })?;

    // Convert to response format (all images are custom, none are selected by default)
    let config = crate::config::Config::get();
    let image_responses: Vec<PnlShareImageResponse> = custom_images
        .into_iter()
        .map(|img| PnlShareImageResponse::from_db_model(img, &config.gcs_base_url))
        .collect();

    Ok(Json(PnlShareImagesListResponse {
        images: image_responses,
        selected_image_path: None, // Frontend handles selection
    }))
}

/// Upload a new PnL share image
pub async fn upload_pnl_share_image(
    State(state): State<StorageState>,
    Query(query): Query<UploadImageQuery>,
    multipart: Multipart,
) -> Result<Json<UploadImageResponse>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;

    // Validate wallet address format
    let _wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    // Ensure wallet exists in database (required for foreign key constraint)
    if let Ok(None) = state.get_wallet(&wallet_address).await {
        // Create wallet record if it doesn't exist
        let wallet = crate::models::DbWallet {
            wallet_address: wallet_address.clone(),
            latest_tx_signature: None,
            latest_tx_slot: None,
            updated_at: None, // Will be set by the database
        };

        if let Err(e) = state.insert_or_update_wallet(wallet).await {
            tracing::error!("Failed to create wallet record: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to initialize wallet")),
            ));
        }
    }

    // Check image count limit
    let images = state.get_pnl_share_images(&wallet_address).await.map_err(|e| {
        tracing::error!("Failed to get images from database: {}", e);
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to check image count")))
    })?;

    let image_count = images.len();
    let config = crate::config::Config::get();
    let max_custom_images = config.max_custom_images_per_user;

    if image_count >= max_custom_images {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new(&format!(
                "Maximum number of images ({}) reached. Please delete some images before uploading new ones.",
                max_custom_images
            ))),
        ));
    }

    // Process image upload
    let image_processor = ImageProcessor::new();
    let image_path = image_processor.process_upload(multipart).await.map_err(|e| {
        tracing::error!("Failed to process image upload: {}", e);
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new(&format!("Image upload failed: {}", e))))
    })?;

    // Save to database
    let create_image = CreatePnlShareImage {
        wallet_address: wallet_address.clone(),
        image_path: image_path.clone(),
    };

    let db_image = state.insert_pnl_share_image(create_image).await.map_err(|e| {
        tracing::error!("Failed to save image to database: {}", e);
        (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to save image")))
    })?;

    let config = crate::config::Config::get();
    let image_url = format!("{}/{}", config.gcs_base_url, image_path);

    Ok(Json(UploadImageResponse {
        image_path,
        image_url,
        message: "Image uploaded successfully".to_string(),
    }))
}

/// Select a PnL share image (deprecated - frontend handles selection)
pub async fn select_pnl_share_image(
    State(_state): State<StorageState>,
    Path(_image_path): Path<String>,
    Query(_query): Query<GetImagesQuery>,
) -> Result<StatusCode, (StatusCode, Json<ErrorResponse>)> {
    // This endpoint is deprecated since frontend now handles image selection
    // Return OK for backward compatibility
    Ok(StatusCode::OK)
}

/// Delete a PnL share image
pub async fn delete_pnl_share_image(
    State(state): State<StorageState>,
    Path(image_path): Path<String>,
    Query(query): Query<GetImagesQuery>,
) -> Result<Json<DeleteImageResponse>, (StatusCode, Json<ErrorResponse>)> {
    let wallet_address = query.wallet_address;

    // Validate wallet address format
    let _wallet_pubkey = Pubkey::from_str(&wallet_address).map_err(|_| {
        (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid wallet address format")))
    })?;

    // Only custom images can be deleted (no default images in database)

    // Delete from database (only custom images reach here)
    let deleted_image =
        state.delete_pnl_share_image(&wallet_address, &image_path).await.map_err(|e| {
            tracing::error!("Failed to delete image from database: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new("Failed to delete image")))
        })?;

    if let Some(deleted_image) = deleted_image {
        // Delete image from disk
        let image_processor = ImageProcessor::new();
        if let Err(e) = image_processor.delete_image(&deleted_image.image_path).await {
            tracing::warn!("Failed to delete image file from disk: {}", e);
            // Don't fail the request if file deletion fails
        }

        Ok(Json(DeleteImageResponse {
            success: true,
            message: "Image deleted successfully".to_string(),
        }))
    } else {
        Err((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Image not found"))))
    }
}
