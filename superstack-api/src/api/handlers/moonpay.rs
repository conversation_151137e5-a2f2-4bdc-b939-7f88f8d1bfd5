use axum::{http::StatusCode, Json};
use base64::{engine::general_purpose::STANDARD as BASE64, Engine};
use hmac_sha256::HMAC;
use serde::{Deserialize, Serialize};
use url::Url;

use crate::{api::handlers::ErrorResponse, config::Config};

#[derive(Debug, Deserialize)]
pub struct MoonpaySignRequest {
    url: String,
}

#[derive(Debug, Serialize)]
pub struct MoonpaySignResponse {
    signature: String,
}

pub async fn sign_moonpay_request(
    Json(payload): Json<MoonpaySignRequest>,
) -> Result<Json<MoonpaySignResponse>, (StatusCode, Json<ErrorResponse>)> {
    let config = Config::get();
    let secret_key = config.moonpay_secret_key.as_str();
    if secret_key.is_empty() {
        return Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            J<PERSON>(ErrorResponse::new("Moonpay secret key is not set")),
        ));
    }

    if payload.url.is_empty() {
        return Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new("URL is required"))));
    }

    // Parse the URL to get the search parameters
    let url = Url::parse(&payload.url)
        .map_err(|_| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Invalid URL format"))))?;

    // Get the search part of the URL (everything after ?)
    let search_with_question = if !url.query().unwrap_or_default().is_empty() {
        format!("?{}", url.query().unwrap_or_default())
    } else {
        String::new()
    };

    // Create HMAC-SHA256
    let mut mac = HMAC::new(secret_key.as_bytes());

    // Update with the search parameters
    mac.update(search_with_question.as_bytes());

    // Get the signature and encode it in base64
    let signature = BASE64.encode(mac.finalize());

    Ok(Json(MoonpaySignResponse { signature }))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_sign_moonpay_request() {
        crate::utils::setup_tracing();

        let payload = MoonpaySignRequest {
            url: "https://buy.moonpay.com/v2/buy?xxx=xxx&yyy=yyy".to_string(),
        };
        let response = sign_moonpay_request(Json(payload)).await.unwrap();
        assert_eq!(response.signature, "M7XTEpM6+ekO8JyRERDdP8fP2fHzYsvCExZgMkX9p7I=");
    }
}
