use std::{cmp::Ordering, collections::HashMap};

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    Json,
};
use serde::Deserialize;
use superstack_data::postgres::enums::Chain;

use super::*;
use crate::{
    api::{types::*, StorageState},
    constant::{SOL_MINT, USDC_MINT, USDT_MINT},
    utils::{address, get_sol_price, lamports_to_token, timestamp},
};

#[derive(Deserialize)]
pub struct TokenDetailsQuery {
    pub chain: Option<String>,
}

pub async fn get_token_details(
    State(_state): State<StorageState>,
    Path(_mint): Path<String>,
    Query(_query): Query<TokenDetailsQuery>,
) -> Result<Json<TokenDetails>, (StatusCode, Json<ErrorResponse>)> {
    Err((StatusCode::GONE, Json(ErrorResponse::new("This endpoint is deprecated."))))
}

#[derive(Deserialize)]
pub struct TokenInfoQuery {
    pub pool_address: Option<String>,
    pub chain: Option<String>,
}

pub async fn get_token_info(
    State(state): State<StorageState>,
    Path(mint): Path<String>,
    Query(query): Query<TokenInfoQuery>,
) -> Result<Json<DetailedTokenResponse>, (StatusCode, Json<ErrorResponse>)> {
    let pool_address = query.pool_address;
    let is_user_selected = pool_address.is_some();

    if mint == SOL_MINT {
        return Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new("Not supported"))));
    }

    // Parse chain parameter, default to Solana, or use cross-chain search if not specified
    let specified_chain = match query.chain.as_deref() {
        Some("solana") => Some(Chain::Solana),
        Some("hypercore") => Some(Chain::Hypercore),
        Some("hyperevm") => Some(Chain::HyperEvm),
        Some(invalid) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!(
                    "Invalid chain: {}. Supported chains: solana, hypercore, hyperevm",
                    invalid
                ))),
            ));
        }
        None => None, // Use cross-chain search
    };

    let sol_price = get_sol_price().await;

    // Use specified chain or cross-chain search to find the token
    let mut token_found = None;
    if let Some(chain) = specified_chain {
        // Query specific chain for better performance
        // todo: state from redis
        if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
            token_found = Some(token);
        }
    } else {
        // Fall back to cross-chain search
        for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
            if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
                token_found = Some(token);
                break;
            }
        }
    }

    let token_statistic = match token_found {
        Some(token) => token,
        None => {
            return Err((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Token not found"))));
        }
    };

    // Extract the chain and token metadata
    let chain = token_statistic.chain;

    // Get pools associated with this token using the detected chain
    let pool_metadatas =
        match state.indexer_data_provider.get_pools_by_token_address(chain, &mint).await {
            Ok(pools) => pools,
            Err(e) => {
                tracing::error!("Failed to get pools for token {}: {}", mint, e);
                vec![]
            }
        };

    let mut pool_infos = Vec::new();
    let mut pool_statistic_map = HashMap::new();
    for pool_metadata in pool_metadatas.iter() {
        if !pool_metadata.is_active {
            continue;
        }

        // Get pool statistics for this pool using the detected chain
        let (
            price_usd,
            price_native,
            market_cap_usd,
            liquidity_usd,
            volume_usd,
            price_change,
            pool_statistic_response,
        ) = match state.indexer_data_provider.get_pool(chain, &pool_metadata.pool_address).await {
            Ok(Some(stats)) => {
                let price_usd = stats.usd_price;
                let price_native = price_usd / sol_price;
                let market_cap_usd = stats.usd_market_cap;
                let liquidity_usd = stats.usd_liquidity;
                let volume_usd = stats.usd_volume_24h;
                let price_change = stats.price_change_24h;
                let pool_statistic_response =
                    PoolStatisticResponse::from_pool_statistics(&stats, false, sol_price);
                (
                    price_usd,
                    price_native,
                    market_cap_usd,
                    liquidity_usd,
                    volume_usd,
                    price_change,
                    pool_statistic_response,
                )
            }
            Ok(None) | Err(_) => {
                tracing::debug!(
                    "No pool statistics found for pool {}, using defaults",
                    pool_metadata.pool_address
                );
                // Use default values when no statistics are available
                let price_usd = 0.0;
                let price_native = 0.0;
                let market_cap_usd = 0.0;
                let liquidity_usd = 0.0;
                let volume_usd = 0.0;
                let price_change = 0.0;
                let pool_statistic_response = PoolStatisticResponse::default();
                (
                    price_usd,
                    price_native,
                    market_cap_usd,
                    liquidity_usd,
                    volume_usd,
                    price_change,
                    pool_statistic_response,
                )
            }
        };

        pool_infos.push(PoolInfo {
            address: pool_metadata.pool_address.to_string(),
            pair_label: pool_metadata.pair_label.clone(),
            token1_mint: pool_metadata.token_address.clone(),
            token2_mint: pool_metadata.base_address.clone(),
            creation_time: if pool_metadata.create_timestamp_millis < 0 {
                0 // Use 0 for invalid negative timestamps
            } else {
                pool_metadata.create_timestamp_millis / 1000 // Convert milliseconds to seconds
            },
            dex: pool_metadata.dex.to_string(),
            pool_type: Some(pool_metadata.pool_type.to_string()),
            price_usd,
            price_native,
            price_change,
            market_cap_usd,
            fdv_usd: market_cap_usd,
            volume_usd,
            liquidity_usd,
            is_default: false, // Will be set to true for the default pool after sorting
        });

        pool_statistic_map.insert(pool_metadata.pool_address.to_string(), pool_statistic_response);
    }

    pool_infos.sort_by(|a, b| b.volume_usd.partial_cmp(&a.volume_usd).unwrap_or(Ordering::Equal));

    // Set is_default to true for the pool at default_pool_index (which is 0)
    let default_pool_index = 0;
    if let Some(pool_info) = pool_infos.get_mut(default_pool_index) {
        pool_info.is_default = true;
    }

    let (current_pool_index, current_pool_address) = if let Some(pool_address) = pool_address {
        let pool_index = pool_infos
            .iter()
            .enumerate()
            .find(|(_, pool_info)| pool_info.address == pool_address)
            .map(|(idx, _)| idx)
            .unwrap_or(0);
        (pool_index, pool_address)
    } else {
        (0, pool_infos.first().map(|pool_info| pool_info.address.clone()).unwrap_or_default())
    };

    let current_pool_statistic =
        pool_statistic_map.get(&current_pool_address).cloned().unwrap_or_default();

    let launch_dex_url = None; // TODO: Implement create_dex_url
    let is_trench_token = token_statistic.is_trench_token;
    let raw_supply = token_statistic.supply.parse::<u64>().unwrap_or(0);
    let total_supply = lamports_to_token(raw_supply, token_statistic.decimals);
    let circulating_supply = lamports_to_token(raw_supply, token_statistic.decimals);
    let token_info = DetailedTokenResponse {
        token_mint: mint,
        name: token_statistic.name,
        symbol: token_statistic.symbol,
        description: token_statistic.description,
        image_url: token_statistic.image, // todo: translate url with path
        twitter: token_statistic.twitter,
        telegram: token_statistic.telegram,
        website: token_statistic.website,
        launch_dex: Some(token_statistic.create_dex.to_string()),
        launch_dex_url,
        decimals: token_statistic.decimals,
        total_supply,
        circulating_supply,
        chain: token_statistic.chain.to_string(),
        is_trench_token,
        pools: pool_infos,
        default_pool_index: default_pool_index as u64,
        current_pool_index: current_pool_index as u64,
        current_pool_statistic,
        is_user_selected,
    };

    Ok(Json(token_info))
}

#[derive(Deserialize)]
pub struct MemeStatisticsQuery {
    pub chain: Option<String>,
}

pub async fn get_meme_statistics(
    State(state): State<StorageState>,
    Path(mint): Path<String>,
    Query(query): Query<MemeStatisticsQuery>,
) -> Result<Json<TokenMemeStatisticResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Parse chain parameter for optimization
    let specified_chain = match query.chain.as_deref() {
        Some("solana") => Some(Chain::Solana),
        Some("hypercore") => Some(Chain::Hypercore),
        Some("hyperevm") => Some(Chain::HyperEvm),
        Some(invalid) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!(
                    "Invalid chain: {}. Supported chains: solana, hypercore, hyperevm",
                    invalid
                ))),
            ));
        }
        None => None, // Use cross-chain search
    };

    let _sol_price = get_sol_price().await;

    // Use specified chain or cross-chain search to find the token
    let mut token_found = None;
    if let Some(chain) = specified_chain {
        // Query specific chain for better performance
        if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
            token_found = Some(token);
        }
    } else {
        // Fall back to cross-chain search
        for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
            if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
                token_found = Some(token);
                break;
            }
        }
    }

    let token_statistic = match token_found {
        Some(token) => token,
        None => {
            return Err((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Token not found"))));
        }
    };

    let chain = token_statistic.chain;

    let _meme_statistics = state
        .indexer_data_provider
        .get_token(chain, &mint)
        .await
        .map_err(|e| (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string()))))?
        .ok_or((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Meme statistics not found"))))?;

    Ok(Json(TokenMemeStatisticResponse {
        token_mint: mint,
        lp_burned: 1.0,
        is_mintable: token_statistic.mint_authority.as_ref().map_or(false, |auth| !auth.is_empty()),
        is_freezable: token_statistic
            .freeze_authority
            .as_ref()
            .map_or(false, |auth| !auth.is_empty()),
        is_mutable: token_statistic.is_mutable.unwrap_or(false),
        holders: 0,           // TODO: Get from actual meme statistics
        insiders: 0,          // TODO: Get from actual meme statistics
        snipers: 0,           // TODO: Get from actual meme statistics
        bot: 0,               // TODO: Get from actual meme statistics
        dev_holding: 0.0,     // TODO: Get from actual meme statistics
        top10_holding: 0.0,   // TODO: Get from actual meme statistics
        insider_holding: 0.0, // TODO: Get from actual meme statistics
        sniper_holding: 0.0,  // TODO: Get from actual meme statistics
    }))
}

#[derive(Deserialize)]
pub struct TradeQuery {
    pub pool_address: String,
    pub limit: Option<i64>,
    pub max_timestamp: Option<u64>,
    /// Filter by maker behavioral type: "all", "dev", "snipers"
    pub maker_type: Option<String>,
    /// Minimum USD amount for trade filtering
    pub min_usd_amount: Option<f64>,
    /// Maximum USD amount for trade filtering
    pub max_usd_amount: Option<f64>,
    /// Filter by specific maker address (auto-filter functionality)
    pub maker_address: Option<String>,
    pub chain: Option<String>,
}

pub async fn get_token_trades(
    State(state): State<StorageState>,
    Path(mint): Path<String>,
    Query(query): Query<TradeQuery>,
) -> Result<Json<Vec<Trade>>, (StatusCode, Json<ErrorResponse>)> {
    let limit = query.limit.unwrap_or(100);

    // Normalize max_timestamp if provided
    let max_timestamp_seconds = match query.max_timestamp {
        Some(ts) => Some(
            timestamp::normalize_and_validate(ts)
                .map_err(|e| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new(e))))?,
        ),
        None => None,
    };

    let pool_address = query.pool_address;

    // Parse and validate pool address based on chain or auto-detect
    let (detected_chain, validated_pool_address) = if let Some(chain_str) = query.chain.as_deref() {
        // Chain is specified, validate address for that chain
        let chain = match chain_str {
            "solana" => Chain::Solana,
            "hypercore" => Chain::Hypercore,
            "hyperevm" => Chain::HyperEvm,
            invalid => {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse::new(&format!(
                        "Invalid chain: {}. Supported chains: solana, hypercore, hyperevm",
                        invalid
                    ))),
                ));
            }
        };

        let validated_address = address::parse_address_for_chain(&pool_address, chain)
            .map_err(|e| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new(e))))?;

        (Some(chain), validated_address)
    } else {
        // Auto-detect chain from address format
        let (detected_chain, validated_address) = address::parse_and_detect_chain(&pool_address)
            .map_err(|e| (StatusCode::BAD_REQUEST, Json(ErrorResponse::new(e))))?;

        (Some(detected_chain), validated_address)
    };

    // Validate USD amount range
    if let (Some(min), Some(max)) = (query.min_usd_amount, query.max_usd_amount) {
        if min > max {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new("min_usd_amount cannot be greater than max_usd_amount")),
            ));
        }
    }

    // Use detected chain as specified chain for optimization
    let specified_chain = detected_chain;

    let sol_price = get_sol_price().await;

    // Use specified chain or cross-chain search to find the token
    let mut token_found = None;
    if let Some(chain) = specified_chain {
        // Query specific chain for better performance
        if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
            token_found = Some(token);
        }
    } else {
        // Fall back to cross-chain search
        for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
            if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
                token_found = Some(token);
                break;
            }
        }
    }

    let token_statistic = match token_found {
        Some(token) => token,
        None => {
            tracing::warn!("Token not found for trades request: {}", mint);
            return Ok(Json(vec![])); // Return empty trades array instead of error
        }
    };

    let chain = token_statistic.chain;
    let pool_metadata = state
        .indexer_db
        .get_pool_metadata(chain, &validated_pool_address)
        .await
        .map_err(|e| (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string()))))?
        .ok_or((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Pool metadata not found"))))?;

    // Use the filtered method if any filters are provided
    let use_filtered_query = query.maker_type.is_some() ||
        query.min_usd_amount.is_some() ||
        query.max_usd_amount.is_some() ||
        query.maker_address.is_some() ||
        max_timestamp_seconds.is_some();

    let tradeinfos: Vec<superstack_data::postgres::aggregator::ExtendedDexTrade> =
        if use_filtered_query {
            // TODO: Implement filtered trade search using existing methods
            // For now, use basic trade search and filter in memory
            match state
                .indexer_data_provider
                .get_extended_dex_trade(chain, &validated_pool_address, limit as u64)
                .await
            {
                Ok(mut trades) => {
                    // Apply filters
                    if let Some(min_usd) = query.min_usd_amount {
                        trades.retain(|t| t.usd >= min_usd);
                    }
                    if let Some(max_usd) = query.max_usd_amount {
                        trades.retain(|t| t.usd <= max_usd);
                    }
                    if let Some(maker_addr) = &query.maker_address {
                        trades.retain(|t| t.maker_address == *maker_addr);
                    }
                    if let Some(max_ts) = max_timestamp_seconds {
                        // Convert milliseconds to seconds for comparison
                        // Handle potential negative timestamps gracefully
                        trades.retain(|t| {
                            if t.timestamp_millis < 0 {
                                false // Exclude trades with invalid negative timestamps
                            } else {
                                (t.timestamp_millis / 1000) <= max_ts
                            }
                        });
                    }
                    trades
                }
                Err(e) => {
                    tracing::error!("Failed to get trades: {}", e);
                    vec![]
                }
            }
        } else {
            match state
                .indexer_data_provider
                .get_extended_dex_trade(chain, &validated_pool_address, limit as u64)
                .await
            {
                Ok(trades) => trades,
                Err(e) => {
                    tracing::error!("Failed to get trades: {}", e);
                    vec![]
                }
            }
        };

    // Determine base mint based on chain type
    let base_mint_str = if token_statistic.token_address == pool_metadata.token_address {
        pool_metadata.base_address.clone()
    } else {
        pool_metadata.token_address.clone()
    };

    // Validate base mint is supported based on chain
    match chain {
        Chain::Solana => {
            // For Solana, validate as Pubkey and check against known mints
            if let Ok(_base_pubkey) = Pubkey::from_str(&base_mint_str) {
                match base_mint_str.as_str() {
                    SOL_MINT | USDC_MINT => {}
                    _ => {
                        tracing::error!("Base mint not supported for Solana: {}", base_mint_str);
                        return Err((
                            StatusCode::INTERNAL_SERVER_ERROR,
                            Json(ErrorResponse::new("Base mint in the pool is not supported")),
                        ));
                    }
                }
            } else {
                tracing::error!("Invalid Solana base mint format: {}", base_mint_str);
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Invalid base mint format")),
                ));
            }
        }
        Chain::HyperEvm => {
            // For HyperEVM, validate as EVM address
            if let Err(e) = address::parse_address_for_chain(&base_mint_str, Chain::HyperEvm) {
                tracing::error!("Invalid HyperEVM base mint format: {} - {}", base_mint_str, e);
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Invalid base mint format")),
                ));
            }
            // For now, accept any valid EVM address as base mint
        }
        Chain::Hypercore => {
            // For Hypercore, just validate it's not empty
            if base_mint_str.is_empty() {
                tracing::error!("Base mint cannot be empty for Hypercore");
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Base mint cannot be empty")),
                ));
            }
        }
    }

    let mut trades = Vec::new();
    for tradeinfo in tradeinfos {
        // Use the available fields from ExtendedDexTrade
        let trade_amount_usd = tradeinfo.usd;
        let token_ui_amount = tradeinfo.token_ui_amount;
        let base_ui_amount = tradeinfo.base_ui_amount;
        let is_buy = tradeinfo.is_buy;
        let price_usd = tradeinfo.usd_price;
        let price_native = price_usd / sol_price;
        let market_cap_usd = tradeinfo.usd_market_cap;

        // Calculate sol_amount and base_token_symbol
        let (sol_amount, base_token_symbol) = match base_mint_str.as_str() {
            SOL_MINT => (base_ui_amount, "SOL".to_string()),
            USDC_MINT => (trade_amount_usd / sol_price, "USDC".to_string()),
            USDT_MINT => (trade_amount_usd / sol_price, "USDT".to_string()),
            _ => (trade_amount_usd / sol_price, "UNKNOWN".to_string()),
        };

        trades.push(Trade {
            tx_sig: tradeinfo.tx_hash.to_string(),
            maker: tradeinfo.maker_address.to_string(),
            maker_type: MakerType::new(trade_amount_usd),
            is_buy,
            token_ui_amount,
            base_ui_amount,
            usd: trade_amount_usd,
            sol_amount,
            base_token_symbol,
            price_usd,
            price_native,
            market_cap_usd,
            timestamp: if tradeinfo.timestamp_millis < 0 {
                0 // Use 0 for invalid negative timestamps
            } else {
                (tradeinfo.timestamp_millis / 1000) as u64 // Convert milliseconds to seconds
            },
        });
    }

    Ok(Json(trades))
}

#[derive(Deserialize)]
pub struct HolderQuery {
    pub limit: Option<i64>,
    pub offset: Option<i64>,
    pub chain: Option<String>,
}

pub async fn get_token_holders(
    State(state): State<StorageState>,
    Path(mint): Path<String>,
    Query(query): Query<HolderQuery>,
) -> Result<Json<Holders>, (StatusCode, Json<ErrorResponse>)> {
    let limit = query.limit.unwrap_or(20);
    let offset = query.offset.unwrap_or(0); // TODO: Implement pagination

    // Parse chain parameter for optimization
    let specified_chain = match query.chain.as_deref() {
        Some("solana") => Some(Chain::Solana),
        Some("hypercore") => Some(Chain::Hypercore),
        Some("hyperevm") => Some(Chain::HyperEvm),
        Some(invalid) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!(
                    "Invalid chain: {}. Supported chains: solana, hypercore, hyperevm",
                    invalid
                ))),
            ));
        }
        None => None, // Use cross-chain search
    };

    let _sol_price = get_sol_price().await;

    // Use specified chain or cross-chain search to find the token
    let mut token_found = None;
    if let Some(chain) = specified_chain {
        // Query specific chain for better performance
        if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
            token_found = Some(token);
        }
    } else {
        // Fall back to cross-chain search
        for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
            if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
                token_found = Some(token);
                break;
            }
        }
    }

    let token_statistic = match token_found {
        Some(token) => token,
        None => {
            tracing::warn!("Token not found for holders request: {}", mint);
            return Ok(Json(Holders { holders: vec![], total_holders: 0 })); // Return empty holders
                                                                            // instead of error
        }
    };

    let chain = token_statistic.chain;
    let holders = match state
        .indexer_data_provider
        .get_holders(chain, &mint, limit as u64, offset as u64)
        .await
    {
        Ok(holders) => holders,
        Err(e) => {
            tracing::error!("Failed to get token holders: {}", e);
            vec![]
        }
    };

    // Get total holders count (not just the returned page)
    let total_holders_count =
        match state.indexer_data_provider.get_holders_count(chain, &mint).await {
            Ok(count) => count,
            Err(e) => {
                tracing::error!("Failed to get total holders count: {}", e);
                holders.len() as u64 // Fallback to returned count
            }
        };

    let mut holders_response = Vec::new();
    for holder in holders {
        let trade_amount_usd = holder.spent_usd + holder.received_usd;
        let base_balance = holder.native_token_balance;

        let bought_ui_amount = holder.bought_ui_amount;
        let sold_ui_amount = holder.sold_ui_amount;
        let remaining_ui_amount = holder.remaining_ui_amount;

        // Data validation: ensure remaining amount is consistent
        let expected_remaining = bought_ui_amount - sold_ui_amount;
        let actual_remaining = remaining_ui_amount;

        // Log inconsistencies for debugging
        if (expected_remaining - actual_remaining).abs() > 0.001 {
            tracing::warn!(
                "Inconsistent remaining amount for holder {}: expected {}, actual {}, difference {}",
                holder.holder_address,
                expected_remaining,
                actual_remaining,
                (expected_remaining - actual_remaining).abs()
            );
        }

        let cost_usd = holder.spent_usd;
        let earned_usd = holder.received_usd;

        // Validate token price to avoid extreme PnL values
        let token_price = token_statistic.usd_price;
        if token_price <= 0.0 || token_price > 1000000.0 {
            tracing::warn!(
                "Suspicious token price for {}: {}, using fallback calculation",
                token_statistic.token_address,
                token_price
            );
        }

        let remaining_usd = remaining_ui_amount * token_price;

        // Calculate PnL with additional validation
        let pnl_usd = remaining_usd + earned_usd - cost_usd;

        // Log extreme PnL values for debugging
        if pnl_usd.abs() > 1_000_000_000.0 {
            tracing::warn!(
                "Extreme PnL value for holder {}: {} (remaining_usd: {}, earned_usd: {}, cost_usd: {}, token_price: {})",
                holder.holder_address,
                pnl_usd,
                remaining_usd,
                earned_usd,
                cost_usd,
                token_price
            );
        }

        // Calculate remaining percentage with proper bounds checking
        let remaining_percentage = if bought_ui_amount > 0.0 {
            let percentage = remaining_ui_amount / bought_ui_amount;
            // Clamp percentage to reasonable bounds (0.0 to 1.0)
            percentage.max(0.0).min(1.0)
        } else {
            0.0
        };

        holders_response.push(Holder {
            maker: holder.holder_address.to_string(),
            maker_type: MakerType::new(trade_amount_usd),
            base_balance,
            bought_ui_amount,
            bought_usd: cost_usd,
            bought_tx_count: holder.bought_txns,
            sold_ui_amount,
            sold_usd: earned_usd,
            sold_tx_count: holder.sold_txns,
            remaining_ui_amount,
            remaining_usd,
            remaining_percentage,
            pnl_usd,
        });
    }

    Ok(Json(Holders { holders: holders_response, total_holders: total_holders_count }))
}

pub async fn get_token_top_traders(
    State(state): State<StorageState>,
    Path(mint): Path<String>,
    Query(query): Query<HolderQuery>,
) -> Result<Json<Vec<Holder>>, (StatusCode, Json<ErrorResponse>)> {
    let limit = query.limit.unwrap_or(20);
    let _offset = query.offset.unwrap_or(0); // TODO: Implement pagination

    // Parse chain parameter for optimization
    let specified_chain = match query.chain.as_deref() {
        Some("solana") => Some(Chain::Solana),
        Some("hypercore") => Some(Chain::Hypercore),
        Some("hyperevm") => Some(Chain::HyperEvm),
        Some(invalid) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!(
                    "Invalid chain: {}. Supported chains: solana, hypercore, hyperevm",
                    invalid
                ))),
            ));
        }
        None => None, // Use cross-chain search
    };

    let _sol_price = get_sol_price().await;

    // Use specified chain or cross-chain search to find the token
    let mut token_found = None;
    if let Some(chain) = specified_chain {
        // Query specific chain for better performance
        if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
            token_found = Some(token);
        }
    } else {
        // Fall back to cross-chain search
        for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
            if let Ok(Some(token)) = state.indexer_data_provider.get_token(chain, &mint).await {
                token_found = Some(token);
                break;
            }
        }
    }

    let token_statistic = match token_found {
        Some(token) => token,
        None => {
            tracing::warn!("Token not found for top traders request: {}", mint);
            return Ok(Json(vec![])); // Return empty traders array instead of error
        }
    };

    let chain = token_statistic.chain;
    let holders =
        match state.indexer_data_provider.get_top_traders(chain, &mint, limit as u64).await {
            Ok(holders) => holders,
            Err(e) => {
                tracing::error!("Failed to get top traders: {}", e);
                vec![]
            }
        };

    let mut holders_response = Vec::new();
    for holder in holders {
        let trade_amount_usd = holder.spent_usd + holder.received_usd;
        let base_balance = holder.native_token_balance;

        let bought_ui_amount = holder.bought_ui_amount;
        let sold_ui_amount = holder.sold_ui_amount;
        let remaining_ui_amount = holder.remaining_ui_amount;

        // Data validation: ensure remaining amount is consistent
        let expected_remaining = bought_ui_amount - sold_ui_amount;
        let actual_remaining = remaining_ui_amount;

        // Log inconsistencies for debugging
        if (expected_remaining - actual_remaining).abs() > 0.001 {
            tracing::warn!(
                "Inconsistent remaining amount for top trader {}: expected {}, actual {}, difference {}",
                holder.holder_address,
                expected_remaining,
                actual_remaining,
                (expected_remaining - actual_remaining).abs()
            );
        }

        let cost_usd = holder.spent_usd;
        let earned_usd = holder.received_usd;

        // Validate token price to avoid extreme PnL values
        let token_price = token_statistic.usd_price;
        if token_price <= 0.0 || token_price > 1000000.0 {
            tracing::warn!(
                "Suspicious token price for {}: {}, using fallback calculation",
                token_statistic.token_address,
                token_price
            );
        }

        let remaining_usd = remaining_ui_amount * token_price;

        // Calculate PnL with additional validation
        let pnl_usd = remaining_usd + earned_usd - cost_usd;

        // Log extreme PnL values for debugging
        if pnl_usd.abs() > 1_000_000_000.0 {
            tracing::warn!(
                "Extreme PnL value for top trader {}: {} (remaining_usd: {}, earned_usd: {}, cost_usd: {}, token_price: {})",
                holder.holder_address,
                pnl_usd,
                remaining_usd,
                earned_usd,
                cost_usd,
                token_price
            );
        }

        // Calculate remaining percentage with proper bounds checking
        let remaining_percentage = if bought_ui_amount > 0.0 {
            let percentage = remaining_ui_amount / bought_ui_amount;
            // Clamp percentage to reasonable bounds (0.0 to 1.0)
            percentage.max(0.0).min(1.0)
        } else {
            0.0
        };

        holders_response.push(Holder {
            maker: holder.holder_address.to_string(),
            maker_type: MakerType::new(trade_amount_usd),
            base_balance,
            bought_ui_amount,
            bought_usd: cost_usd,
            bought_tx_count: holder.bought_txns,
            sold_ui_amount,
            sold_usd: earned_usd,
            sold_tx_count: holder.sold_txns,
            remaining_ui_amount,
            remaining_usd,
            remaining_percentage,
            pnl_usd,
        });
    }

    Ok(Json(holders_response))
}
