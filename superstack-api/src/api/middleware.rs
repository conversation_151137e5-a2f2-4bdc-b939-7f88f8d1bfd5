use std::task::{Context, Poll};

use axum::{
    body::Body,
    extract::Request,
    http::StatusCode,
    response::{IntoResponse, Response},
};
use futures::future::BoxFuture;
use tower::{Layer, Service};

use super::routes::{
    DASHBOARD_CODE_STATS_ROUTE, DASHBOARD_FUNDS_STATS_ROUTE, DASHBOARD_TRANSACTION_STATS_ROUTE,
    HEALTH_ROUTE, INVITE_BIND_ROUTE, METRICS_ROUTE, SIMPLE_HEALTH_ROUTE,
};
use crate::{config::Config, models::StorageState};

/// Referral code authentication parameters from request headers
#[derive(Debug, Clone)]
pub struct ReferralCodeAuth {
    pub wallet_address: String,
}

impl ReferralCodeAuth {
    /// Extract wallet address from request headers
    fn from_request(request: &Request) -> Option<Self> {
        let wallet_address = request.headers().get("X-Wallet-Address")?.to_str().ok()?.to_string();

        Some(Self { wallet_address })
    }
}

/// Dashboard authentication parameters from request headers
#[derive(Debug, Clone)]
pub struct DashboardAuth {
    pub dashboard_key: String,
}

impl DashboardAuth {
    /// Extract invite code and wallet address from request headers
    fn from_request(request: &Request) -> Option<Self> {
        let dashboard_key = request.headers().get("X-Dashboard-Key")?.to_str().ok()?.to_string();

        Some(Self { dashboard_key })
    }
}

/// Middleware for authenticating requests using referral codes
pub struct ReferralCodeAuthMiddleware<S> {
    inner: S,
    storage_state: StorageState,
    dashboard_key: String,
}

impl<S> Layer<S> for ReferralCodeAuthLayer {
    type Service = ReferralCodeAuthMiddleware<S>;

    fn layer(&self, inner: S) -> Self::Service {
        ReferralCodeAuthMiddleware {
            inner,
            storage_state: self.storage_state.clone(),
            dashboard_key: self.dashboard_key.clone(),
        }
    }
}

/// Layer that adds referral code authentication to services
#[derive(Clone)]
pub struct ReferralCodeAuthLayer {
    storage_state: StorageState,
    dashboard_key: String,
}

impl ReferralCodeAuthLayer {
    pub fn new(storage_state: StorageState, dashboard_key: String) -> Self {
        Self { storage_state, dashboard_key }
    }
}

/// Check if the given path is a public API that doesn't require any authentication
pub fn is_public_api(path: &str) -> bool {
    // Health and system endpoints
    if path == HEALTH_ROUTE ||
        path == SIMPLE_HEALTH_ROUTE ||
        path == METRICS_ROUTE ||
        path == INVITE_BIND_ROUTE
    {
        return true;
    }

    // Internal endpoints
    if path.starts_with("/internal/") {
        return true;
    }

    // Market data endpoints (no user-specific data)
    if matches!(
        path,
        "/api/sol-price" |
            "/api/markets/tokens" |
            "/api/markets/search" |
            "/api/trench/tokens" |
            "/api/candle/snapshot" |
            "/api/perps" |
            "/api/perps/categories" |
            "/api/perps/categories/detail" |
            "/api/tokens/info" |
            "/api/moonpay/sign"
    ) {
        return true;
    }

    // Token info endpoints (path parameters)
    if path.starts_with("/api/token/info/") ||
        path.starts_with("/api/token/image/") ||
        path.starts_with("/api/token/details/") ||
        path.starts_with("/api/token/meme-scan/") ||
        path.starts_with("/api/token/trades/") ||
        path.starts_with("/api/token/holders/") ||
        path.starts_with("/api/token/top-traders/")
    {
        return true;
    }

    // Transaction status endpoints (read-only)
    if path.starts_with("/api/transaction/status/") ||
        path.starts_with("/api/hyperevm/transaction/status/")
    {
        return true;
    }

    // Depth chart endpoints
    if path.starts_with("/api/depth/") {
        return true;
    }

    false
}

/// Check if the given path requires wallet authentication (X-Wallet-Address header)
pub fn requires_wallet_auth(path: &str) -> bool {
    // User-specific data endpoints
    if matches!(
        path,
        "/api/positions" |
            "/api/open-orders" |
            "/api/order-history" |
            "/api/trade-history" |
            "/api/position-history" |
            "/api/pnl/stats" |
            "/api/pnl/share" |
            "/api/activity" |
            "/api/activity/refresh" |
            "/api/activity/stats" |
            "/api/watchlist" |
            "/api/setting" |
            "/api/referral/code" |
            "/api/referral/stats" |
            "/api/referral/list" |
            "/api/referral/rewards" |
            "/api/referral/rewards/claim" |
            "/api/referral/network" |
            "/api/transaction/send" |
            "/api/transaction/relay/quote" |
            "/api/transaction/relay/swap" |
            "/api/hyperevm/balance" |
            "/api/hyperevm/transaction/send" |
            "/api/chart/user-indicators" |
            "/api/chart/trend-lines" |
            "/api/perps/portfolio"
    ) {
        return true;
    }

    // PnL share images endpoints (all require wallet auth)
    if path.starts_with("/api/pnl/share/images") {
        return true;
    }

    false
}

/// Check if the given path is a dashboard endpoint that requires dashboard authentication
pub fn is_dashboard_endpoint(path: &str) -> bool {
    path == DASHBOARD_CODE_STATS_ROUTE ||
        path == DASHBOARD_TRANSACTION_STATS_ROUTE ||
        path == DASHBOARD_FUNDS_STATS_ROUTE
}

impl<S> Service<Request> for ReferralCodeAuthMiddleware<S>
where
    S: Service<Request, Response = Response> + Clone + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, req: Request) -> Self::Future {
        let path = req.uri().path();

        // Check if this is a public API endpoint (no authentication required)
        if is_public_api(path) {
            return Box::pin(self.inner.call(req));
        }

        // Check if this is a dashboard endpoint that requires dashboard authentication
        if is_dashboard_endpoint(path) {
            let auth = match DashboardAuth::from_request(&req) {
                Some(auth) => auth,
                None => {
                    return Box::pin(async move {
                        Ok(Response::builder()
                            .status(StatusCode::UNAUTHORIZED)
                            .body(Body::from("Missing Authorization"))
                            .unwrap()
                            .into_response())
                    });
                }
            };

            if auth.dashboard_key == self.dashboard_key {
                return Box::pin(self.inner.call(req));
            } else {
                return Box::pin(async move {
                    Ok(Response::builder()
                        .status(StatusCode::UNAUTHORIZED)
                        .body(Body::from("Invalid Authorization"))
                        .unwrap()
                        .into_response())
                });
            }
        }

        // Check if this endpoint requires wallet authentication
        if requires_wallet_auth(path) {
            // Extract auth details from request
            let auth = match ReferralCodeAuth::from_request(&req) {
                Some(auth) => auth,
                None => {
                    return Box::pin(async move {
                        Ok(Response::builder()
                            .status(StatusCode::UNAUTHORIZED)
                            .body(Body::from("Missing wallet address"))
                            .unwrap()
                            .into_response())
                    });
                }
            };

            // Check if referral code verification is enabled
            let config = Config::get();
            if !config.enable_invite_code {
                // If referral code verification is disabled, skip verification and allow the
                // request
                return Box::pin(self.inner.call(req));
            }

            // Clone what we need for the async block
            let storage_state = self.storage_state.clone();
            let wallet_address = auth.wallet_address;

            // Verify the referral code (check if user has been referred or is legacy user)
            let mut inner = self.inner.clone();
            Box::pin(async move {
                // Check if wallet already exists in the system (legacy users)
                if let Ok(Some(_wallet)) = storage_state.get_wallet(&wallet_address).await {
                    return inner.call(req).await;
                }

                // Check if this wallet has been referred (has used a referral code)
                match storage_state.get_referral_by_referee(&wallet_address).await {
                    Ok(Some(_referral)) => {
                        // User has been referred, allow access
                        inner.call(req).await
                    }
                    Ok(None) => Ok(Response::builder()
                        .status(StatusCode::FORBIDDEN)
                        .body(Body::from("Wallet must use a referral code to access the system"))
                        .unwrap()
                        .into_response()),
                    Err(_) => Ok(Response::builder()
                        .status(StatusCode::INTERNAL_SERVER_ERROR)
                        .body(Body::from("Error verifying referral status"))
                        .unwrap()
                        .into_response()),
                }
            })
        } else {
            // For any other endpoints that don't require authentication, allow access
            Box::pin(self.inner.call(req))
        }
    }
}

impl<S: Clone> Clone for ReferralCodeAuthMiddleware<S> {
    fn clone(&self) -> Self {
        Self {
            inner: self.inner.clone(),
            storage_state: self.storage_state.clone(),
            dashboard_key: self.dashboard_key.clone(),
        }
    }
}
