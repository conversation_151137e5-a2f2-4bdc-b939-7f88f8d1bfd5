pub mod handlers;
mod middleware;
mod routes;
pub mod types;

use std::net::SocketAddr;

use tokio::{net::TcpListener, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};

use self::routes::create_router;
use crate::models::StorageState;

pub fn spawn_server(storage_state: StorageState, port: u16) -> Jo<PERSON><PERSON><PERSON><PERSON><()> {
    tokio::spawn(async move {
        let app = create_router(storage_state);
        let addr = SocketAddr::from(([0, 0, 0, 0], port));
        let listener = TcpListener::bind(addr).await.unwrap();

        tracing::info!("API server starting on {}", addr);

        match axum::serve(listener, app).await {
            Ok(_) => {}
            Err(e) => tracing::error!("Server error: {}", e),
        }
    })
}
