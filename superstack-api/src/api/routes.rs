use axum::{
    extract::Request,
    routing::{delete, get, post, Router},
};
use tower::ServiceBuilder;
use tower_http::{
    cors::{Any, CorsLayer},
    services::ServeDir,
    set_header::SetResponseHeaderLayer,
    trace::<PERSON><PERSON>ayer,
};

use super::{handlers::*, middleware::ReferralCodeAuthLayer};
use crate::{
    api::handlers::hyperevm::{get_hyperevm_transaction_status, send_hyperevm_transaction},
    config::Config,
    models::StorageState,
};

pub const HEALTH_ROUTE: &str = "/api/health";
pub const SIMPLE_HEALTH_ROUTE: &str = "/health";
pub const METRICS_ROUTE: &str = "/api/metrics";
pub const INVITE_BIND_ROUTE: &str = "/api/invite";

pub const DASHBOARD_CODE_STATS_ROUTE: &str = "/dashboard/code-stats";
pub const DASHBOARD_TRANSACTION_STATS_ROUTE: &str = "/dashboard/transaction-stats";
pub const DASHBOARD_FUNDS_STATS_ROUTE: &str = "/dashboard/funds-stats";

pub fn create_router(storage_state: StorageState) -> Router {
    // Set up CORS
    let cors = CorsLayer::new().allow_methods(Any).allow_origin(Any).allow_headers(Any);

    // Create the auth layer
    let config = Config::get();
    let data_config = superstack_data::config::Config::get();
    let auth_layer =
        ReferralCodeAuthLayer::new(storage_state.clone(), config.dashboard_key.clone());

    Router::new()
        .route(HEALTH_ROUTE, get(health))
        .route(SIMPLE_HEALTH_ROUTE, get(simple_health))
        .route(METRICS_ROUTE, get(metrics))
        .route(INVITE_BIND_ROUTE, post(handle_invite_code))
        // Internal API routes for websocket-service communication
        .route("/internal/validate-token", post(validate_token))
        .route("/api/positions", get(get_positions))
        .route("/api/open-orders", get(get_open_orders))
        .route("/api/order-history", get(get_order_histories))
        .route("/api/trade-history", get(get_trade_histories))
        .route("/api/position-history", get(get_position_history))
        .route("/api/pnl/stats", get(get_pnl_stats))
        .route("/api/pnl/share", get(get_pnl_share))
        .route("/api/sol-price", get(get_sol_price))
        .route("/api/markets/tokens", get(get_markets_tokens))
        .route("/api/markets/search", get(search_token))
        .route("/api/trench/tokens", get(get_trench_tokens))
        .route("/api/token/info/{token_address}", get(get_token_info))
        .route("/api/token/details/{token_address}", get(get_token_details))
        .route("/api/token/meme-scan/{token_address}", get(get_meme_statistics))
        .route("/api/token/trades/{token_address}", get(get_token_trades))
        .route("/api/token/holders/{token_address}", get(get_token_holders))
        .route("/api/token/top-traders/{token_address}", get(get_token_top_traders))
        .route("/api/transaction/status/{signature}", get(get_transaction_status))
        .route("/api/transaction/send", post(send_transaction))
        .route("/api/transaction/relay/quote", post(get_relay_quote))
        .route("/api/transaction/relay/swap", post(get_relay_swap))
        .route("/api/tokens/info", get(get_tokens_info))
        .route("/api/hyperevm/balance", get(get_wallet_balance))
        .route("/api/hyperevm/transaction/send", post(send_hyperevm_transaction))
        .route("/api/hyperevm/transaction/status/{signature}", get(get_hyperevm_transaction_status))
        .route("/api/moonpay/sign", post(sign_moonpay_request))
        .route("/api/candle/snapshot", get(get_candle_snapshot))
        .route("/api/depth/{pool_address}", get(get_depth_chart))
        .route("/api/chart/user-indicators", get(get_user_indicators))
        .route("/api/chart/trend-lines", get(get_trend_lines))
        .route("/api/watchlist", get(get_watchlist).post(change_watchlist))
        .route("/api/setting", get(get_setting).post(change_setting))
        .route("/api/activity", get(get_wallet_activity))
        .route("/api/activity/refresh", get(refresh_wallet_activity))
        .route("/api/activity/stats", get(get_wallet_activity_stats))
        .route("/api/record/activity", post(record_hyperliquid_activity))
        .route("/api/perps", get(get_perp_list))
        .route("/api/perps/categories", get(get_perp_categories))
        .route("/api/perps/categories/detail", get(get_perp_category_detail))
        .route("/api/perps/books/{perp_id}", get(get_perp_books))
        .route("/api/perps/portfolio", get(get_perp_portfolio))
        .route("/api/perps/leaderboard/{perp_id}", get(get_perp_top_users))
        // Referral system routes
        .route("/api/referral/code", get(get_referral_code))
        .route("/api/referral/stats", get(get_referral_stats))
        .route("/api/referral/list", get(get_referral_list))
        .route("/api/referral/rewards", get(get_reward_list))
        .route("/api/referral/rewards/claim", post(claim_reward))
        .route("/api/referral/network", get(get_referral_network))
        .route("/api/telegram/info", get(get_telegram_info))
        .route("/api/telegram/bind", post(bind_telegram))
        // PnL Share Images API
        .route("/api/pnl/share/images", get(get_pnl_share_images))
        .route("/api/pnl/share/images/upload", post(upload_pnl_share_image))
        .route("/api/pnl/share/images/{*image_path}", delete(delete_pnl_share_image))
        .route(DASHBOARD_CODE_STATS_ROUTE, get(get_code_stats))
        .route(DASHBOARD_TRANSACTION_STATS_ROUTE, get(get_transaction_stats))
        .route(DASHBOARD_FUNDS_STATS_ROUTE, get(get_user_funds_stats))
        // serve static files
        // .nest_service(
        //     "/static",
        //     ServiceBuilder::new()
        //         .layer(SetResponseHeaderLayer::overriding(
        //             header::CACHE_CONTROL,
        //             header::HeaderValue::from_static("public, max-age=31536000, immutable"),
        //         ))
        //         .service(ServeDir::new(&data_config.static_dir)),
        // )
        .layer(auth_layer)
        .layer(TraceLayer::new_for_http())
        .layer(cors)
        .layer(
            TraceLayer::new_for_http()
                .make_span_with(|req: &Request| {
                    let method = req.method();
                    let uri = req.uri();
                    tracing::info_span!("request", %method, %uri)
                })
                .on_request(|req: &Request<_>, _span: &tracing::Span| {
                    let method = req.method();
                    let uri = req.uri();
                    let path = uri.path();
                    if path == "/api/health" {
                        tracing::debug!("{} {} {}", method, uri, path);
                    } else {
                        tracing::info!("{} {} {}", method, uri, path);
                    }
                }),
        )
        .with_state(storage_state)
}
