pub mod evm;
pub mod tracker;

use std::str::FromStr;

use anyhow::Result;

use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_sdk::{
    commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature,
    transaction::VersionedTransaction,
};

use crate::models::types::TransactionStatus;

pub fn verify_transaction(
    signed_versioned_tx: &VersionedTransaction,
    signer: &Pubkey,
) -> Result<()> {
    if signed_versioned_tx.signatures.is_empty() {
        anyhow::bail!("Transaction has no signatures");
    }

    let signature = signed_versioned_tx.signatures[0];
    let verified = signature
        .verify(signer.to_bytes().as_ref(), signed_versioned_tx.message.serialize().as_ref());

    if !verified {
        anyhow::bail!("Transaction signature verification failed");
    }

    Ok(())
}

// For sending without blocking:
pub async fn send_transaction(
    signed_versioned_tx: &VersionedTransaction,
    skip_preflight: bool,
    max_retries: usize,
) -> Result<Signature> {
    let rpc_client = crate::utils::get_rpc_client();
    let config = RpcSendTransactionConfig {
        skip_preflight,
        max_retries: Some(max_retries),
        ..RpcSendTransactionConfig::default()
    };
    let signature = match rpc_client.send_transaction_with_config(signed_versioned_tx, config).await
    {
        Ok(signature) => signature,
        Err(e) => {
            let error_message = e.to_string();
            if error_message.contains("HTTP status server error") {
                // Retry once
                rpc_client.send_transaction_with_config(signed_versioned_tx, config).await?
            } else {
                anyhow::bail!(e);
            }
        }
    };

    Ok(signature)
}

pub async fn send_and_confirm_transaction(
    signed_versioned_tx: VersionedTransaction,
) -> Result<Signature> {
    let rpc_client = crate::utils::get_rpc_client();
    let tx = rpc_client
        .send_and_confirm_transaction_with_spinner_and_config(
            &signed_versioned_tx,
            CommitmentConfig::confirmed(),
            RpcSendTransactionConfig {
                skip_preflight: false,
                max_retries: Some(10),
                ..RpcSendTransactionConfig::default()
            },
        )
        .await?;
    Ok(tx)
}

// Separate function for checking status later
pub async fn check_transaction_status(
    signature: &Signature,
    search_history: bool,
) -> Result<TransactionStatus> {
    let rpc_client = crate::utils::get_rpc_client();
    let status = rpc_client
        .get_signature_status_with_commitment_and_history(
            signature,
            CommitmentConfig::confirmed(),
            search_history,
        )
        .await?;

    match status {
        Some(Ok(_)) => Ok(TransactionStatus::Success),
        Some(Err(_)) => Ok(TransactionStatus::Failed),
        None => Ok(TransactionStatus::Pending),
    }
}

#[cfg(test)]
mod tests {
    use base64::{engine::general_purpose, Engine};
    use solana_client::rpc_client::GetConfirmedSignaturesForAddress2Config;
    use solana_sdk::{native_token::sol_to_lamports, signature::Keypair, signer::Signer};

    use super::*;

    #[tokio::test]
    async fn test_send_transaction() -> Result<()> {
        crate::utils::setup_tracing();

        let wallet = Keypair::new();
        let input_mint = "So11111111111111111111111111111111111111112";
        let output_mint = "********************************************";
        let amount = sol_to_lamports(0.01);

        let swap_transaction_b64 = crate::jupiter::swap::get_unsigned_swap_transaction(
            input_mint,
            output_mint,
            amount,
            wallet.pubkey(),
        )
        .await?;

        let swap_transaction_bytes = general_purpose::STANDARD
            .decode(swap_transaction_b64)
            .map_err(|_| anyhow::anyhow!("Failed to decode swapTransaction from Base64"))?;

        let versioned_tx: VersionedTransaction = bincode::deserialize(&swap_transaction_bytes)
            .map_err(|_| anyhow::anyhow!("Failed to deserialize swapTransaction"))?;

        let signed_versioned_tx = VersionedTransaction::try_new(versioned_tx.message, &[&wallet])?;

        verify_transaction(&signed_versioned_tx, &wallet.pubkey())?;

        {
            let serialized_tx = bincode::serialize(&signed_versioned_tx)?;
            let base64_tx = general_purpose::STANDARD.encode(serialized_tx);

            let decoded_tx = general_purpose::STANDARD.decode(&base64_tx)?;
            let deserialized_tx: VersionedTransaction = bincode::deserialize(&decoded_tx)?;
            assert_eq!(signed_versioned_tx, deserialized_tx);
        }

        // let tx = send_and_confirm_transaction(signed_versioned_tx).await?;

        let tx = send_transaction(&signed_versioned_tx, true, 10).await?;
        let tx2 = send_transaction(&signed_versioned_tx, true, 10).await?;
        assert_eq!(tx, tx2);

        for _ in 0..10 {
            let status = check_transaction_status(&tx, false).await?;
            tracing::info!("Transaction status: {:?}", status);
            if status.is_finalized() {
                break;
            }
            tokio::time::sleep(std::time::Duration::from_millis(1000)).await;
        }

        tracing::info!("Transaction sent: {}", tx);

        Ok(())
    }

    #[tokio::test]
    async fn test_get_signatures_for_address() -> Result<()> {
        crate::utils::setup_tracing();

        let rpc_client = crate::utils::get_rpc_client();
        let wallet_address =
            Pubkey::from_str("********************************************").unwrap();
        let until = Signature::from_str("3ESbfsDUtcPf7VmdtEupHUay2XzUzxiLcLY81HQVBQjVndrwe9fqZ37FpwjPBZVondJtshYB3rS9mEQxiiAXLgQP").unwrap();
        let signatures = rpc_client
            .get_signatures_for_address_with_config(
                &wallet_address,
                GetConfirmedSignaturesForAddress2Config {
                    before: None,
                    limit: Some(100),
                    until: Some(until),
                    commitment: Some(CommitmentConfig::confirmed()),
                },
            )
            .await?;
        for signature in signatures {
            tracing::info!("Signature: {}", signature.signature);
        }

        Ok(())
    }
}
