use alloy::{
    consensus::TxEnvelope,
    eips::eip2718::Decodable2718,
    primitives::TxHash,
    providers::{<PERSON>yn<PERSON><PERSON><PERSON>, Provider},
};

use crate::models::types::TransactionStatus;

pub async fn send_raw_transaction(provider: &DynProvider, raw_tx: &[u8]) -> anyhow::Result<TxHash> {
    let pending_tx = provider.send_raw_transaction(raw_tx).await?;
    Ok(*pending_tx.tx_hash())
}

pub async fn send_transaction(
    provider: &DynProvider,
    tx: alloy::rpc::types::TransactionRequest,
) -> anyhow::Result<TxHash> {
    let tx = provider.send_transaction(tx).await?;

    Ok(*tx.tx_hash())
}

pub async fn check_transaction_status(
    provider: &DynProvider,
    tx: TxHash,
) -> anyhow::Result<TransactionStatus> {
    let tx = provider.get_transaction_receipt(tx).await?;

    match tx {
        Some(tx) => {
            let status = tx.status();
            if status {
                Ok(TransactionStatus::Success)
            } else {
                Ok(TransactionStatus::Failed)
            }
        }
        None => Ok(TransactionStatus::Pending),
    }
}

/// Test function to validate transaction parsing
pub fn validate_signed_transaction(hex_tx: &str) -> anyhow::Result<TxEnvelope> {
    // Remove 0x prefix if present
    let hex_tx = hex_tx.strip_prefix("0x").unwrap_or(hex_tx);

    // Parse transaction from hex
    let tx_bytes = hex::decode(hex_tx)?;

    // Parse as signed EVM transaction envelope
    let tx_envelope = TxEnvelope::decode_2718(&mut tx_bytes.as_slice())?;

    // Verify the transaction is signed (all decoded envelopes are signed by definition)
    // The fact that we successfully decoded it means it's signed

    Ok(tx_envelope)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_user_transaction() {
        // User provided transaction data
        let tx_hex = "0x02f901768203e780808407270e00830266f8944e2960a8cd19b467b82d26d83facb0fae26b094d87038d7ea4c68000b90104414bf38900000000000000000000000055555555555555555555555555555555555555550000000000000000000000003b4575e689ded21caad31d64c4df1f10f3b2cedf0000000000000000000000000000000000000000000000000000000000000bb8000000000000000000000000f22df182c3dab139338fff80e8f03cc6f2a3c06f00000000000000000000000000000000000000000000000000000000685c553b00000000000000000000000000000000000000000000000000038d7ea4c6800000000000000000000000000000000000000000000000000000000000000003a80000000000000000000000000000000000000000000000000000000000000000c080a063508ea43a84e9c5c6610b5acc4b0b4960aff85e74194a5bd344abdab881691ca01e54b8fb7cccd70a1d9bd38ef7f1dc8c403b82dd73aad902a7639e6dbe474e21";

        let result = validate_signed_transaction(tx_hex);

        match result {
            Ok(envelope) => {
                println!("Successfully parsed transaction envelope: {:?}", envelope);
                // assert!(envelope.is_signed());
            }
            Err(e) => {
                panic!("Failed to parse transaction: {}", e);
            }
        }
    }
}
