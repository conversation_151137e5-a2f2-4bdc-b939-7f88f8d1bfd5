use std::time::{Duration, Instant};

use super::*;
use crate::models::StorageState;
use anyhow::Result;
use chrono::Utc;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
pub struct TransactionTracker;

impl TransactionTracker {
    pub async fn track_pending_transactions(state: StorageState) -> Result<()> {
        let pending_transactions = state.get_pending_transactions().await?;
        for transaction in pending_transactions {
            let status =
                check_transaction_status(&Signature::from_str(&transaction.signature)?, true)
                    .await?;

            if status.is_pending() {
                // https://solana.com/ru/developers/guides/advanced/confirmation
                let utc_time = Utc::now().timestamp();
                if utc_time - transaction.created_at > 90 {
                    state
                        .update_transaction_status_if_pending(
                            &transaction.signature,
                            &TransactionStatus::Expired,
                        )
                        .await?;
                }
                continue;
            }

            if status.is_finalized() {
                state.update_transaction_status_if_pending(&transaction.signature, &status).await?;
            }
        }
        Ok(())
    }

    pub fn run(state: StorageState) -> <PERSON><PERSON><PERSON><PERSON><PERSON><()> {
        tokio::spawn(async move {
            tracing::info!("Transaction tracker started");
            loop {
                let start_time = Instant::now();

                if let Err(e) = TransactionTracker::track_pending_transactions(state.clone()).await
                {
                    tracing::error!("Failed to track pending transactions: {}", e);
                    tokio::time::sleep(Duration::from_secs(5)).await;
                }

                let elapsed_time = start_time.elapsed();
                tracing::debug!("Transaction tracker loop took {:?} seconds", elapsed_time);

                tokio::time::sleep(Duration::from_secs(1)).await;
            }
        })
    }
}
