use serde::{Deserialize, Serialize};
use std::time::Instant;

use crate::models::StorageState;

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthStatus {
    pub status: String,
    pub postgres_status: DatabaseStatus,
    pub timestamp: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DatabaseStatus {
    pub available: bool,
    pub response_time_ms: Option<u64>,
    pub error: Option<String>,
}

impl StorageState {
    pub async fn health_check(&self) -> HealthStatus {
        let _start_time = Instant::now();

        // Check PostgreSQL
        let postgres_status = self.check_postgres_health().await;

        HealthStatus {
            status: "ok".to_string(),
            postgres_status,
            timestamp: chrono::Utc::now().timestamp() as u64,
        }
    }

    async fn check_postgres_health(&self) -> DatabaseStatus {
        let start_time = Instant::now();

        match sqlx::query("SELECT 1").fetch_optional(&self.api_db.pool).await {
            Ok(_) => DatabaseStatus {
                available: true,
                response_time_ms: Some(start_time.elapsed().as_millis() as u64),
                error: None,
            },
            Err(e) => DatabaseStatus {
                available: false,
                response_time_ms: None,
                error: Some(e.to_string()),
            },
        }
    }
}
