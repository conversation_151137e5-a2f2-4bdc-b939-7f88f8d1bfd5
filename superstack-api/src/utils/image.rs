use anyhow::{anyhow, Result};
use axum::extract::Multipart;
use image::ImageFormat;
use std::path::Path;
use tokio::{fs, io::AsyncWriteExt};
use uuid::Uuid;

use crate::config::Config;

pub struct ImageProcessor {
    max_size_bytes: u64,
    allowed_formats: Vec<String>,
    storage_dir: String,
}

impl ImageProcessor {
    pub fn new() -> Self {
        let config = Config::get();
        Self {
            max_size_bytes: config.max_image_size_mb * 1024 * 1024, // Convert MB to bytes
            allowed_formats: config.allowed_image_formats.clone(),
            storage_dir: config.storage_dir.clone(),
        }
    }

    /// Process uploaded image from multipart form
    pub async fn process_upload(&self, mut multipart: Multipart) -> Result<String> {
        while let Some(field) = multipart.next_field().await? {
            let name = field.name().unwrap_or("").to_string();

            if name == "image" {
                let filename = field.file_name().map(|s| s.to_string());
                let content_type = field.content_type().map(|s| s.to_string());
                let data = field.bytes().await?;

                return self.process_image_data(data.to_vec(), filename, content_type).await;
            }
        }

        Err(anyhow!("No image field found in multipart data"))
    }

    /// Process and store image data to mounted GCS directory
    pub async fn process_image_data(
        &self,
        data: Vec<u8>,
        filename: Option<String>,
        content_type: Option<String>,
    ) -> Result<String> {
        // 1. Check file size
        if data.len() as u64 > self.max_size_bytes {
            return Err(anyhow!(
                "Image size {} bytes exceeds maximum allowed size {} bytes",
                data.len(),
                self.max_size_bytes
            ));
        }

        // 2. Detect and validate image format
        let format = self.detect_image_format(&data, &filename, &content_type)?;
        if !self.is_format_allowed(&format) {
            return Err(anyhow!(
                "Image format {:?} is not allowed. Allowed formats: {:?}",
                format,
                self.allowed_formats
            ));
        }

        // 3. Validate that the data is actually a valid image
        image::load_from_memory(&data).map_err(|e| anyhow!("Invalid image data: {}", e))?;

        // 4. Generate unique filename with UUID
        let file_id = Uuid::new_v4().to_string();
        let extension = self.format_to_extension(&format);
        let filename = format!("{}.{}", file_id, extension);
        let relative_path = format!("pnl-share-images/{}", filename);

        // 5. Create directory if it doesn't exist
        let images_dir = Path::new(&self.storage_dir).join("pnl-share-images");
        fs::create_dir_all(&images_dir)
            .await
            .map_err(|e| anyhow!("Failed to create images directory: {}", e))?;

        // 6. Write file to mounted GCS directory
        let file_path = images_dir.join(&filename);
        let mut file = fs::File::create(&file_path)
            .await
            .map_err(|e| anyhow!("Failed to create file: {}", e))?;

        file.write_all(&data).await.map_err(|e| anyhow!("Failed to write file: {}", e))?;

        file.flush().await.map_err(|e| anyhow!("Failed to flush file: {}", e))?;

        tracing::info!("Successfully stored image: {}", file_path.display());

        Ok(relative_path)
    }

    /// Detect image format from data, filename, or content type
    fn detect_image_format(
        &self,
        data: &[u8],
        filename: &Option<String>,
        content_type: &Option<String>,
    ) -> Result<ImageFormat> {
        // Try to detect from data first
        if let Ok(format) = image::guess_format(data) {
            return Ok(format);
        }

        // Try to detect from filename extension
        if let Some(filename) = filename {
            if let Some(ext) = Path::new(filename).extension() {
                let ext = ext.to_string_lossy().to_lowercase();
                match ext.as_str() {
                    "jpg" | "jpeg" => return Ok(ImageFormat::Jpeg),
                    "png" => return Ok(ImageFormat::Png),
                    "webp" => return Ok(ImageFormat::WebP),
                    _ => {}
                }
            }
        }

        // Try to detect from content type
        if let Some(content_type) = content_type {
            match content_type.as_str() {
                "image/jpeg" => return Ok(ImageFormat::Jpeg),
                "image/png" => return Ok(ImageFormat::Png),
                "image/webp" => return Ok(ImageFormat::WebP),
                _ => {}
            }
        }

        Err(anyhow!("Unable to detect image format"))
    }

    /// Check if format is allowed
    fn is_format_allowed(&self, format: &ImageFormat) -> bool {
        let format_str = self.format_to_extension(format);
        self.allowed_formats.contains(&format_str)
    }

    /// Convert ImageFormat to file extension
    fn format_to_extension(&self, format: &ImageFormat) -> String {
        match format {
            ImageFormat::Jpeg => "jpg".to_string(),
            ImageFormat::Png => "png".to_string(),
            ImageFormat::WebP => "webp".to_string(),
            _ => "jpg".to_string(), // Default fallback
        }
    }

    /// Delete image from mounted GCS directory
    pub async fn delete_image(&self, image_path: &str) -> Result<()> {
        let file_path = Path::new(&self.storage_dir).join(image_path);

        // Use async remove_file directly, handle NotFound error gracefully
        match fs::remove_file(&file_path).await {
            Ok(()) => {
                tracing::info!("Successfully deleted image: {}", file_path.display());
            }
            Err(e) if e.kind() == std::io::ErrorKind::NotFound => {
                tracing::warn!("Image file not found for deletion: {}", file_path.display());
            }
            Err(e) => {
                return Err(anyhow!("Failed to delete file {}: {}", file_path.display(), e));
            }
        }

        Ok(())
    }
}
