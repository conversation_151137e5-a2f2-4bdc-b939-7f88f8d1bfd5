use anyhow::Result;
use superstack_data::postgres::enums::Chain;

use crate::{
    api::types::{TradePill, TrendLines},
    constant::{SOL_MINT, USDC_MINT, USDT_MINT},
    models::{trade::DbTrade, types::TradeType, StorageState},
    utils::{get_sol_price, lamports_to_token},
};

/// Price calculator for user trading indicators
pub struct PriceCalculator;

impl PriceCalculator {
    /// Convert trades to trade pills with USD and SOL prices, including market cap
    pub async fn trades_to_pills(
        trades: Vec<DbTrade>,
        state: &StorageState,
        token_mint: &str,
        chain: Chain,
    ) -> Result<Vec<TradePill>> {
        let mut pills = Vec::new();
        let sol_price = get_sol_price().await;

        // Pre-calculate supply_ui to avoid repeated calculations
        let supply_ui = match state.indexer_data_provider.get_token(chain, token_mint).await {
            Ok(Some(token)) => {
                let supply = token.supply.parse::<u64>().unwrap_or(0);
                lamports_to_token(supply, token.decimals)
            }
            Ok(None) => {
                tracing::warn!("Token metadata not found for {}", token_mint);
                0.0
            }
            Err(e) => {
                tracing::error!("Failed to get token metadata for {}: {}", token_mint, e);
                0.0
            }
        };

        for trade in trades {
            let price_usd = Self::calculate_trade_price_usd(&trade, sol_price)?;
            let price_sol = price_usd / sol_price;
            let amount_usd = Self::calculate_trade_amount_usd(&trade, sol_price)?;

            // Calculate market cap using pre-calculated supply_ui
            let market_cap_usd = supply_ui * price_usd;
            let market_cap_sol = supply_ui * price_sol;

            let trade_type = match trade.trade_type {
                TradeType::MarketBuy => "buy".to_string(),
                TradeType::MarketSell => "sell".to_string(),
                TradeType::LimitBuy => "buy".to_string(),
                TradeType::LimitSell => "sell".to_string(),
            };

            pills.push(TradePill {
                timestamp: trade.timestamp,
                price_usd,
                price_sol,
                amount_usd,
                market_cap_usd,
                market_cap_sol,
                trade_type,
                tx_signature: trade.tx_sig,
            });
        }

        Ok(pills)
    }

    /// Calculate trend lines from trade pills
    pub fn calculate_trend_lines(pills: &[TradePill]) -> TrendLines {
        let mut buy_prices_usd = Vec::new();
        let mut sell_prices_usd = Vec::new();
        let mut buy_prices_sol = Vec::new();
        let mut sell_prices_sol = Vec::new();
        let mut buy_volumes = Vec::new();
        let mut sell_volumes = Vec::new();
        let mut total_buy_volume = 0.0;
        let mut total_sell_volume = 0.0;

        // Get the latest market cap from the most recent trade
        let (latest_market_cap_usd, latest_market_cap_sol) = pills
            .last()
            .map(|pill| (pill.market_cap_usd, pill.market_cap_sol))
            .unwrap_or((0.0, 0.0));

        for pill in pills {
            match pill.trade_type.as_str() {
                "buy" => {
                    buy_prices_usd.push(pill.price_usd);
                    buy_prices_sol.push(pill.price_sol);
                    buy_volumes.push(pill.amount_usd);
                    total_buy_volume += pill.amount_usd;
                }
                "sell" => {
                    sell_prices_usd.push(pill.price_usd);
                    sell_prices_sol.push(pill.price_sol);
                    sell_volumes.push(pill.amount_usd);
                    total_sell_volume += pill.amount_usd;
                }
                _ => {} // Ignore unknown trade types
            }
        }

        // Calculate simple averages using helper function
        let average_buy_price = Self::calculate_simple_average(&buy_prices_usd);
        let average_sell_price = Self::calculate_simple_average(&sell_prices_usd);
        let average_buy_price_sol = Self::calculate_simple_average(&buy_prices_sol);
        let average_sell_price_sol = Self::calculate_simple_average(&sell_prices_sol);

        // Calculate volume-weighted averages using helper function
        let buy_volume_weighted_price = Self::calculate_volume_weighted_average(
            &buy_prices_usd,
            &buy_volumes,
            total_buy_volume,
        );
        let sell_volume_weighted_price = Self::calculate_volume_weighted_average(
            &sell_prices_usd,
            &sell_volumes,
            total_sell_volume,
        );
        let buy_volume_weighted_price_sol = Self::calculate_volume_weighted_average(
            &buy_prices_sol,
            &buy_volumes,
            total_buy_volume,
        );
        let sell_volume_weighted_price_sol = Self::calculate_volume_weighted_average(
            &sell_prices_sol,
            &sell_volumes,
            total_sell_volume,
        );

        TrendLines {
            average_buy_price,
            average_sell_price,
            average_buy_price_sol,
            average_sell_price_sol,
            buy_volume_weighted_price,
            sell_volume_weighted_price,
            buy_volume_weighted_price_sol,
            sell_volume_weighted_price_sol,
            total_buy_volume_usd: total_buy_volume,
            total_sell_volume_usd: total_sell_volume,
            market_cap_usd: latest_market_cap_usd,
            market_cap_sol: latest_market_cap_sol,
            buy_trade_count: buy_prices_usd.len() as u32,
            sell_trade_count: sell_prices_usd.len() as u32,
        }
    }

    /// Convert amount to USD based on base token mint
    fn convert_to_usd(amount: f64, base_mint: &str, sol_price: f64) -> f64 {
        match base_mint {
            SOL_MINT => amount * sol_price,
            USDC_MINT | USDT_MINT => amount, // Already in USD
            _ => {
                // For unknown base tokens, try to use SOL price as fallback
                // This might not be accurate but provides a reasonable estimate
                tracing::warn!("Unknown base mint {}, using SOL price as fallback", base_mint);
                amount * sol_price
            }
        }
    }

    /// Calculate USD price for a trade
    fn calculate_trade_price_usd(trade: &DbTrade, sol_price: f64) -> Result<f64> {
        let token_amount = lamports_to_token(trade.token_amount as u64, trade.token_decimals as u8);
        let base_amount = lamports_to_token(trade.base_amount as u64, trade.base_decimals as u8);

        if token_amount == 0.0 {
            return Ok(0.0);
        }

        // Calculate price in base token units
        let price_in_base = base_amount / token_amount;

        // Convert to USD
        let price_usd = Self::convert_to_usd(price_in_base, &trade.base_mint, sol_price);
        Ok(price_usd)
    }

    /// Calculate USD amount for a trade
    fn calculate_trade_amount_usd(trade: &DbTrade, sol_price: f64) -> Result<f64> {
        let base_amount = lamports_to_token(trade.base_amount as u64, trade.base_decimals as u8);

        // Convert to USD
        let amount_usd = Self::convert_to_usd(base_amount, &trade.base_mint, sol_price);
        Ok(amount_usd)
    }

    /// Calculate simple average of prices
    fn calculate_simple_average(prices: &[f64]) -> f64 {
        if prices.is_empty() {
            0.0
        } else {
            prices.iter().sum::<f64>() / prices.len() as f64
        }
    }

    /// Calculate volume-weighted average price
    fn calculate_volume_weighted_average(
        prices: &[f64],
        volumes: &[f64],
        total_volume: f64,
    ) -> f64 {
        if total_volume == 0.0 {
            0.0
        } else {
            prices.iter().zip(volumes.iter()).map(|(price, volume)| price * volume).sum::<f64>() /
                total_volume
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::types::TradeType;

    #[test]
    fn test_calculate_trend_lines() {
        let sol_price = 100.0; // Assume 1 SOL = $100
        let pills = vec![
            TradePill {
                timestamp: 1000,
                price_usd: 1.0,
                price_sol: 0.01, // 1.0 / 100.0
                amount_usd: 100.0,
                market_cap_usd: 1000000.0,
                market_cap_sol: 10000.0,
                trade_type: "buy".to_string(),
                tx_signature: "test1".to_string(),
            },
            TradePill {
                timestamp: 2000,
                price_usd: 2.0,
                price_sol: 0.02, // 2.0 / 100.0
                amount_usd: 200.0,
                market_cap_usd: 2000000.0,
                market_cap_sol: 20000.0,
                trade_type: "buy".to_string(),
                tx_signature: "test2".to_string(),
            },
            TradePill {
                timestamp: 3000,
                price_usd: 1.5,
                price_sol: 0.015, // 1.5 / 100.0
                amount_usd: 150.0,
                market_cap_usd: 1500000.0,
                market_cap_sol: 15000.0,
                trade_type: "sell".to_string(),
                tx_signature: "test3".to_string(),
            },
        ];

        let trend_lines = PriceCalculator::calculate_trend_lines(&pills);

        // Test USD prices
        assert_eq!(trend_lines.average_buy_price, 1.5); // (1.0 + 2.0) / 2
        assert_eq!(trend_lines.average_sell_price, 1.5);

        // Test SOL prices
        assert_eq!(trend_lines.average_buy_price_sol, 0.015); // (0.01 + 0.02) / 2
        assert_eq!(trend_lines.average_sell_price_sol, 0.015);

        // Test volumes
        assert_eq!(trend_lines.total_buy_volume_usd, 300.0);
        assert_eq!(trend_lines.total_sell_volume_usd, 150.0);

        // Test market cap (should be from the last trade)
        assert_eq!(trend_lines.market_cap_usd, 1500000.0);
        assert_eq!(trend_lines.market_cap_sol, 15000.0);

        // Test trade counts
        assert_eq!(trend_lines.buy_trade_count, 2);
        assert_eq!(trend_lines.sell_trade_count, 1);
    }
}
