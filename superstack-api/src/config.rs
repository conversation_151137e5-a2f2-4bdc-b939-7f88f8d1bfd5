use std::{env, str::FromStr, sync::OnceLock};

use dotenv::dotenv;

pub struct Config {
    pub port: u16,
    pub api_database_url: String,
    pub max_db_connections: u32,
    pub moralis_api_key: String,
    pub redis_url: String,
    pub solana_rpc_url: String,

    pub hyperevm_rpc_url: String,

    pub jupiter_trigger_url: String,
    pub jupiter_price_url: String,
    pub jupiter_swap_url: String,

    pub moonpay_secret_key: String,

    pub enable_invite_code: bool,
    pub invite_code_file_path: String,
    pub perps_category_file_path: String,

    pub dashboard_key: String,

    pub clique_relay_api_base: String,

    // Referral system configuration
    pub referral_tier1_percentage: rust_decimal::Decimal,
    pub referral_tier2_percentage: rust_decimal::Decimal,
    pub referral_tier3_percentage: rust_decimal::Decimal,
    pub referral_signup_reward_usd: rust_decimal::Decimal,
    pub referral_unlimited_users: Vec<String>,

    pub telegram_bot_username: String,

    // Image upload configuration
    pub max_image_size_mb: u64,
    pub max_custom_images_per_user: usize,
    pub allowed_image_formats: Vec<String>,
    pub gcs_base_url: String,
    pub storage_dir: String,
    // pub server_host: String,
}

impl Config {
    pub fn get() -> &'static Config {
        static INSTANCE: OnceLock<Config> = OnceLock::new();

        INSTANCE.get_or_init(|| {
            dotenv().ok();

            let port = env::var("PORT").unwrap_or_else(|_| "3000".to_string());
            let api_database_url = env::var("DATABASE_URL").expect("DATABASE_URL is not set");

            let max_db_connections =
                env::var("MAX_DB_CONNECTIONS").unwrap_or_else(|_| "30".to_string());

            let moralis_api_key = env::var("MORALIS_API_KEY").unwrap_or_else(|_| "".to_string());

            let solana_rpc_url = env::var("SOLANA_RPC_URL").expect("SOLANA_RPC_URL is not set");

            let hyperevm_rpc_url =
                env::var("HYPEREVM_RPC_URL").expect("HYPEREVM_RPC_URL is not set");

            let jupiter_trigger_url = env::var("JUPITER_TRIGGER_URL")
                .unwrap_or_else(|_| "https://lite-api.jup.ag/trigger/v1".to_string());

            let jupiter_price_url = env::var("JUPITER_PRICE_URL")
                .unwrap_or_else(|_| "https://lite-api.jup.ag/price/v2".to_string());

            let jupiter_swap_url = env::var("JUPITER_SWAP_URL")
                .unwrap_or_else(|_| "https://lite-api.jup.ag/swap/v1".to_string());

            let redis_url = env::var("REDIS_URL").expect("REDIS_URL is not set");
            let moonpay_secret_key =
                env::var("MOONPAY_SECRET_KEY").expect("MOONPAY_SECRET_KEY is not set");

            let enable_invite_code = env::var("ENABLE_INVITE_CODE")
                .unwrap_or_else(|_| "false".to_string())
                .parse::<bool>()
                .unwrap_or(false);
            let invite_code_file_path = env::var("INVITE_CODE_FILE_PATH")
                .unwrap_or_else(|_| "invite_codes.json".to_string());
            let perps_category_file_path = env::var("PERPS_CATEGORY_FILE_PATH")
                .unwrap_or_else(|_| "perps_category.json".to_string());

            if enable_invite_code {
                tracing::info!("Invite code is enabled, using file: {}", invite_code_file_path);
            } else {
                tracing::info!("Invite code is disabled");
            }

            let dashboard_key = env::var("DASHBOARD_KEY")
                .unwrap_or_else(|_| "R7tp5wQ9hBv8j4cF62xLY3KjE8nM8gAd".to_string());

            let clique_relay_api_base = env::var("CLIQUE_RELAY_API_BASE")
                .unwrap_or_else(|_| "https://api.clique.xyz".to_string());

            // Multi-tier referral reward configuration
            let parse_percentage = |env_var: &str, default: &str| -> rust_decimal::Decimal {
                let value = env::var(env_var)
                    .unwrap_or_else(|_| default.to_string())
                    .parse::<rust_decimal::Decimal>()
                    .unwrap_or_else(|_| rust_decimal::Decimal::from_str(default).unwrap());

                if value < rust_decimal::Decimal::ZERO || value > rust_decimal::Decimal::ONE {
                    tracing::warn!(
                        "{} is out of range (0-1): {}, using default {}",
                        env_var,
                        value,
                        default
                    );
                    rust_decimal::Decimal::from_str(default).unwrap()
                } else {
                    value
                }
            };

            let referral_tier1_percentage = parse_percentage("REFERRAL_TIER1_PERCENTAGE", "0.20");
            let referral_tier2_percentage = parse_percentage("REFERRAL_TIER2_PERCENTAGE", "0.024");
            let referral_tier3_percentage = parse_percentage("REFERRAL_TIER3_PERCENTAGE", "0.016");

            let referral_signup_reward_usd = env::var("REFERRAL_SIGNUP_REWARD_USD")
                .unwrap_or_else(|_| "5.0".to_string())
                .parse::<rust_decimal::Decimal>()
                .unwrap_or_else(|_| rust_decimal::Decimal::from_str("5.0").unwrap());

            let referral_unlimited_users = env::var("REFERRAL_UNLIMITED_USERS")
                .unwrap_or_else(|_| "".to_string())
                .split(',')
                .filter(|s| !s.trim().is_empty())
                .map(|s| s.trim().to_string())
                .collect::<Vec<String>>();

            tracing::info!(
                "Referral rewards configured: Tier1={}%, Tier2={}%, Tier3={}%, Signup=${}",
                referral_tier1_percentage * rust_decimal::Decimal::from(100),
                referral_tier2_percentage * rust_decimal::Decimal::from(100),
                referral_tier3_percentage * rust_decimal::Decimal::from(100),
                referral_signup_reward_usd
            );

            let telegram_bot_username =
                env::var("TELEGRAM_BOT_USERNAME").expect("TELEGRAM_BOT_USERNAME is not set");

            // Image upload configuration
            let max_image_size_mb = env::var("MAX_IMAGE_SIZE_MB")
                .unwrap_or_else(|_| "10".to_string())
                .parse::<u64>()
                .unwrap_or(10);
            let max_custom_images_per_user = env::var("MAX_CUSTOM_IMAGES_PER_USER")
                .unwrap_or_else(|_| "10".to_string())
                .parse::<usize>()
                .unwrap_or(10);
            let allowed_image_formats = env::var("ALLOWED_IMAGE_FORMATS")
                .unwrap_or_else(|_| "jpg,jpeg,png,webp".to_string())
                .split(',')
                .map(|s| s.trim().to_lowercase())
                .collect::<Vec<String>>();

            let gcs_base_url = env::var("GCS_BASE_URL")
                .unwrap_or_else(|_| "https://static.test.superstack.xyz/public".to_string());
            let storage_dir =
                env::var("STORAGE_DIR").unwrap_or_else(|_| "/static-public-bucket".to_string());

            //let server_host = env::var("SERVER_HOST").expect("SERVER_HOST is not set");

            Config {
                port: port.parse().unwrap(),
                api_database_url,
                redis_url,
                max_db_connections: max_db_connections.parse().unwrap(),
                moralis_api_key,
                jupiter_trigger_url,
                jupiter_price_url,
                jupiter_swap_url,
                moonpay_secret_key,
                solana_rpc_url,
                hyperevm_rpc_url,
                enable_invite_code,
                invite_code_file_path,
                perps_category_file_path,
                dashboard_key,
                clique_relay_api_base,
                referral_tier1_percentage,
                referral_tier2_percentage,
                referral_tier3_percentage,
                referral_signup_reward_usd,
                referral_unlimited_users,
                telegram_bot_username,
                max_image_size_mb,
                max_custom_images_per_user,
                allowed_image_formats,
                gcs_base_url,
                storage_dir,
                // server_host,
            }
        })
    }
}
