use serde::{Deserialize, Serialize};
use std::str::FromStr;

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "text")]
pub enum TradeType {
    MarketBuy,
    MarketSell,
    LimitBuy,
    LimitSell,
}

impl TradeType {
    pub fn is_buy(&self) -> bool {
        matches!(self, Self::LimitBuy | Self::MarketBuy)
    }

    #[allow(dead_code)]
    pub fn is_sell(&self) -> bool {
        matches!(self, Self::LimitSell | Self::MarketSell)
    }

    #[allow(dead_code)]
    pub fn is_limit_order(&self) -> bool {
        matches!(self, Self::LimitBuy | Self::LimitSell)
    }

    #[allow(dead_code)]
    pub fn is_market_order(&self) -> bool {
        matches!(self, Self::MarketBuy | Self::MarketSell)
    }
}

impl FromStr for TradeType {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "MarketBuy" => Ok(TradeType::MarketBuy),
            "MarketSell" => Ok(TradeType::MarketSell),
            "LimitBuy" => Ok(TradeType::LimitBuy),
            "LimitSell" => Ok(TradeType::LimitSell),
            _ => Err(anyhow::anyhow!("Unknown trade type: {}", s)),
        }
    }
}

impl std::fmt::Display for TradeType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TradeType::MarketBuy => write!(f, "MarketBuy"),
            TradeType::MarketSell => write!(f, "MarketSell"),
            TradeType::LimitBuy => write!(f, "LimitBuy"),
            TradeType::LimitSell => write!(f, "LimitSell"),
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "text")]
pub enum OrderType {
    LimitBuy,
    LimitSell,
}

impl OrderType {
    pub fn is_buy(&self) -> bool {
        matches!(self, Self::LimitBuy)
    }

    pub fn is_sell(&self) -> bool {
        matches!(self, Self::LimitSell)
    }
}

impl FromStr for OrderType {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "LimitBuy" => Ok(OrderType::LimitBuy),
            "LimitSell" => Ok(OrderType::LimitSell),
            _ => Err(anyhow::anyhow!("Unknown trade type: {}", s)),
        }
    }
}

impl std::fmt::Display for OrderType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            OrderType::LimitBuy => write!(f, "LimitBuy"),
            OrderType::LimitSell => write!(f, "LimitSell"),
        }
    }
}

// https://solana.com/ru/developers/guides/advanced/confirmation
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, sqlx::Type)]
#[sqlx(type_name = "text")]
pub enum TransactionStatus {
    Expired,
    Pending,
    Success,
    Failed,
}

impl TransactionStatus {
    pub fn is_success(&self) -> bool {
        matches!(self, TransactionStatus::Success)
    }

    pub fn is_failed(&self) -> bool {
        matches!(self, TransactionStatus::Failed)
    }

    pub fn is_expired(&self) -> bool {
        matches!(self, TransactionStatus::Expired)
    }

    pub fn is_pending(&self) -> bool {
        matches!(self, TransactionStatus::Pending)
    }

    pub fn is_finalized(&self) -> bool {
        self.is_success() || self.is_failed() || self.is_expired()
    }
}

impl FromStr for TransactionStatus {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Expired" => Ok(TransactionStatus::Expired),
            "Pending" => Ok(TransactionStatus::Pending),
            "Success" => Ok(TransactionStatus::Success),
            "Failed" => Ok(TransactionStatus::Failed),
            _ => Err(anyhow::anyhow!("Unknown transaction status: {}", s)),
        }
    }
}

impl std::fmt::Display for TransactionStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TransactionStatus::Expired => write!(f, "Expired"),
            TransactionStatus::Pending => write!(f, "Pending"),
            TransactionStatus::Success => write!(f, "Success"),
            TransactionStatus::Failed => write!(f, "Failed"),
        }
    }
}
