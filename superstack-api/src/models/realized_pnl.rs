use anyhow::Result;

use serde::{Deserialize, Serialize};

use super::db::Database;

#[derive(C<PERSON>, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbRealizedPnl {
    pub wallet_address: String,
    pub timestamp: i64,

    pub pnl_usd: f64,
}

impl Database {
    pub async fn insert_or_update_realized_pnl(&self, realized_pnl: DbRealizedPnl) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO realized_pnl (
                wallet_address, timestamp, pnl_usd
            )
            VALUES ($1, $2, $3) 
            ON CONFLICT (wallet_address, timestamp) DO UPDATE SET 
                pnl_usd = $3 + realized_pnl.pnl_usd
            "#,
            realized_pnl.wallet_address,
            realized_pnl.timestamp,
            realized_pnl.pnl_usd
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_realized_pnls(
        &self,
        wallet_address: &str,
        start_timestamp: i64,
        end_timestamp: i64,
    ) -> Result<Vec<DbRealizedPnl>> {
        let mut realized_pnls = sqlx::query_as!(
            DbRealizedPnl,
            r#"
            SELECT * FROM realized_pnl WHERE wallet_address = $1 AND timestamp >= $2 AND timestamp <= $3
            ORDER BY timestamp DESC
            "#,
            wallet_address,
            start_timestamp,
            end_timestamp
        )
        .fetch_all(&self.pool)
        .await?;

        realized_pnls.reverse();
        Ok(realized_pnls)
    }
}
