use std::str::FromStr;

use anyhow::Result;
use serde::{Deserialize, Serialize};

use super::{db::Database, types::TradeType};

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbTrade {
    pub tx_sig: String,
    pub wallet_address: String,
    pub token_mint: String,
    pub token_decimals: i16,
    pub base_mint: String,
    pub base_decimals: i16,
    #[serde(serialize_with = "serialize_trade_type", deserialize_with = "deserialize_trade_type")]
    pub trade_type: TradeType,
    pub token_amount: i64,
    pub base_amount: i64,
    pub timestamp: i64,
    pub slot: i64,
    pub fee: i64,
    pub token_fee_amount: i64,
    pub base_fee_amount: i64,
    pub order_id: Option<String>,
    pub remaining_token_amount: Option<i64>,
    pub remaining_base_amount: Option<i64>,
    pub chain: i16,
}

fn serialize_trade_type<S>(trade_type: &TradeType, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    serializer.serialize_str(&trade_type.to_string())
}

fn deserialize_trade_type<'de, D>(deserializer: D) -> Result<TradeType, D::Error>
where
    D: serde::Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    TradeType::from_str(&s).map_err(serde::de::Error::custom)
}

impl Database {
    pub async fn insert_trade(&self, trade: &DbTrade) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO trades (
                tx_sig, wallet_address,
                token_mint, token_decimals, base_mint, base_decimals,
                trade_type,
                token_amount, base_amount,
                timestamp, slot, fee,
                token_fee_amount, base_fee_amount,
                order_id, remaining_token_amount, remaining_base_amount, chain)
            VALUES (
                $1, $2,
                $3, $4, $5, $6,
                $7,
                $8, $9,
                $10, $11, $12,
                $13, $14,
                $15, $16, $17, $18)
            ON CONFLICT (tx_sig) DO NOTHING
            "#,
            trade.tx_sig,
            trade.wallet_address,
            trade.token_mint,
            trade.token_decimals,
            trade.base_mint,
            trade.base_decimals,
            trade.trade_type.to_string(),
            trade.token_amount,
            trade.base_amount,
            trade.timestamp,
            trade.slot,
            trade.fee,
            trade.token_fee_amount,
            trade.base_fee_amount,
            trade.order_id,
            trade.remaining_token_amount,
            trade.remaining_base_amount,
            trade.chain,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn get_trade(&self, tx_sig: &str) -> Result<Option<DbTrade>> {
        let row = sqlx::query!(
            r#"
            SELECT * FROM trades WHERE tx_sig = $1
            "#,
            tx_sig
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            Ok(Some(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain, // Default to Solana for backward compatibility
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn get_trades(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbTrade>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM trades WHERE wallet_address = $1
            ORDER BY timestamp DESC
            LIMIT $2
            OFFSET $3
            "#,
            wallet_address,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain, // Default to Solana for backward compatibility
            });
        }
        Ok(trades)
    }

    pub async fn get_trades_for_wallet_and_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbTrade>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM trades WHERE wallet_address = $1 and token_mint = $2
            ORDER BY timestamp DESC
            LIMIT $3
            OFFSET $4
            "#,
            wallet_address,
            token_mint,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain, // Default to Solana for backward compatibility
            });
        }
        Ok(trades)
    }

    pub async fn get_latest_trade_for_wallet(
        &self,
        wallet_address: &str,
    ) -> Result<Option<DbTrade>> {
        let row = sqlx::query!(
            r#"
            SELECT * FROM trades WHERE wallet_address = $1
            ORDER BY timestamp DESC
            LIMIT 1
            "#,
            wallet_address
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            Ok(Some(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain, // Default to Solana for backward compatibility
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn get_trades_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbTrade>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM trades WHERE wallet_address = $1 AND chain = $2
            ORDER BY timestamp DESC
            LIMIT $3
            OFFSET $4
            "#,
            wallet_address,
            chain,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain,
            });
        }
        Ok(trades)
    }

    pub async fn get_trades_for_wallet_and_token_by_chain(
        &self,
        wallet_address: &str,
        token_mint: &str,
        chain: i16,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbTrade>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM trades WHERE wallet_address = $1 and token_mint = $2 AND chain = $3
            ORDER BY timestamp DESC
            LIMIT $4
            OFFSET $5
            "#,
            wallet_address,
            token_mint,
            chain,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain,
            });
        }
        Ok(trades)
    }

    pub async fn get_latest_trade_for_wallet_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> Result<Option<DbTrade>> {
        let row = sqlx::query!(
            r#"
            SELECT * FROM trades WHERE wallet_address = $1 AND chain = $2
            ORDER BY timestamp DESC
            LIMIT 1
            "#,
            wallet_address,
            chain
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            Ok(Some(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain,
            }))
        } else {
            Ok(None)
        }
    }

    /// Get trades for a specific wallet and token within a time range
    pub async fn get_trades_by_time_range(
        &self,
        wallet_address: &str,
        token_mint: &str,
        start_time: i64,
        end_time: i64,
        chain: Option<superstack_data::postgres::enums::Chain>,
    ) -> Result<Vec<DbTrade>> {
        // Convert Chain enum to i16 if provided
        let chain_i16 = chain.map(|c| match c {
            superstack_data::postgres::enums::Chain::Solana => 0,
            superstack_data::postgres::enums::Chain::Hypercore => 1,
            superstack_data::postgres::enums::Chain::HyperEvm => 2,
        });

        // Single query that handles both cases using SQL conditional logic
        let rows = sqlx::query!(
            r#"
            SELECT * FROM trades
            WHERE wallet_address = $1
            AND token_mint = $2
            AND timestamp >= $3
            AND timestamp <= $4
            AND ($5::int IS NULL OR chain = $5)
            ORDER BY timestamp ASC
            "#,
            wallet_address,
            token_mint,
            start_time,
            end_time,
            chain_i16
        )
        .fetch_all(&self.pool)
        .await?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain,
            });
        }
        Ok(trades)
    }

    /// Get all trades for a specific wallet and token (for position checking)
    pub async fn get_all_trades_for_position_check(
        &self,
        wallet_address: &str,
        token_mint: &str,
        chain: Option<superstack_data::postgres::enums::Chain>,
    ) -> Result<Vec<DbTrade>> {
        // Convert Chain enum to i16 if provided
        let chain_i16 = chain.map(|c| match c {
            superstack_data::postgres::enums::Chain::Solana => 0,
            superstack_data::postgres::enums::Chain::Hypercore => 1,
            superstack_data::postgres::enums::Chain::HyperEvm => 2,
        });

        let rows = sqlx::query!(
            r#"
            SELECT * FROM trades
            WHERE wallet_address = $1
            AND token_mint = $2
            AND ($3::int IS NULL OR chain = $3)
            ORDER BY timestamp ASC
            "#,
            wallet_address,
            token_mint,
            chain_i16
        )
        .fetch_all(&self.pool)
        .await?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(DbTrade {
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: TradeType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                base_amount: row.base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                token_fee_amount: row.token_fee_amount,
                base_fee_amount: row.base_fee_amount,
                order_id: row.order_id,
                remaining_token_amount: row.remaining_token_amount,
                remaining_base_amount: row.remaining_base_amount,
                chain: row.chain,
            });
        }
        Ok(trades)
    }
}
