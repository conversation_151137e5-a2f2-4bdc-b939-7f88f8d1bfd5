use anyhow::Result;
use sqlx::{postgres::PgPoolOptions, PgPool};

#[derive(Debug)]
pub struct Database {
    pub pool: PgPool,
}

impl Database {
    pub async fn new(
        database_url: &str,
        max_db_connections: u32,
        need_migrations: bool,
    ) -> Result<Self> {
        let pool =
            PgPoolOptions::new().max_connections(max_db_connections).connect(database_url).await?;

        if need_migrations {
            // Run migrations, but don't fail if there are checksum mismatches
            match sqlx::migrate!("./migrations").run(&pool).await {
                Ok(_) => {
                    tracing::info!("Database migrations completed successfully");
                }
                Err(e) => {
                    let error_msg = e.to_string();
                    if error_msg.contains("was previously applied but has been modified") {
                        tracing::warn!(
                            "Migration checksum mismatch detected, but continuing anyway: {}",
                            error_msg
                        );
                    } else {
                        tracing::error!("Migration failed: {}", error_msg);
                        return Err(e.into());
                    }
                }
            }
        }
        Ok(Self { pool })
    }
}
