use anyhow::Result;

use serde::{Deserialize, Serialize};

use super::{db::Database, position::DbPositionChange};

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbHistory {
    pub id: Option<i64>,

    pub wallet_address: String,
    pub token_mint: String,
    pub token_decimals: i16,

    pub bought_amount: i64,
    pub sold_amount: i64,

    pub native_decimals: i16,
    pub cost_native_amount: i64,
    pub earnings_native_amount: i64,

    pub cost_usd: f64,
    pub earnings_usd: f64,

    pub pnl_usd: f64,
    pub pnl_percentage: f64,

    pub operations: Vec<DbPositionChange>,

    pub open_time: i64,
    pub close_time: i64,
    pub chain: i16,
}

impl Database {
    pub async fn close_position(&self, history: DbHistory) -> Result<()> {
        // Begin a transaction
        let mut tx = self.pool.begin().await?;

        // Delete the existing position
        sqlx::query!(
            r#"
            DELETE FROM positions WHERE wallet_address = $1 AND token_mint = $2 AND chain = $3
            "#,
            history.wallet_address,
            history.token_mint,
            history.chain
        )
        .execute(&mut *tx)
        .await?;

        // Insert the history record
        sqlx::query!(
            r#"
            INSERT INTO histories (
                wallet_address, token_mint, token_decimals,
                bought_amount, sold_amount,
                native_decimals, cost_native_amount, earnings_native_amount,
                cost_usd, earnings_usd,
                pnl_usd, pnl_percentage,
                operations,
                open_time, close_time, chain
            )
            VALUES (
                $1, $2, $3,
                $4, $5,
                $6, $7, $8,
                $9, $10,
                $11, $12,
                $13,
                $14, $15, $16)
            "#,
            history.wallet_address,
            history.token_mint,
            history.token_decimals,
            history.bought_amount,
            history.sold_amount,
            history.native_decimals,
            history.cost_native_amount,
            history.earnings_native_amount,
            history.cost_usd,
            history.earnings_usd,
            history.pnl_usd,
            history.pnl_percentage,
            serde_json::to_value(&history.operations).unwrap(),
            history.open_time,
            history.close_time,
            history.chain
        )
        .execute(&mut *tx)
        .await?;

        // Commit the transaction
        tx.commit().await?;

        Ok(())
    }

    pub async fn get_histories_by_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
    ) -> Result<Vec<DbHistory>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM histories WHERE wallet_address = $1 AND token_mint = $2
            ORDER BY close_time DESC
            "#,
            wallet_address,
            token_mint
        )
        .fetch_all(&self.pool)
        .await?;

        let histories: Vec<DbHistory> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbHistory {
                    id: Some(row.id),
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    pnl_usd: row.pnl_usd,
                    pnl_percentage: row.pnl_percentage,
                    operations,
                    open_time: row.open_time,
                    close_time: row.close_time,
                    chain: row.chain, // Default to Solana for backward compatibility
                }
            })
            .collect();

        Ok(histories)
    }

    pub async fn get_histories_ordered_by_close_time(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbHistory>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM histories WHERE wallet_address = $1
            ORDER BY close_time DESC
            LIMIT $2
            OFFSET $3
            "#,
            wallet_address,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let histories: Vec<DbHistory> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbHistory {
                    id: Some(row.id),
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    pnl_usd: row.pnl_usd,
                    pnl_percentage: row.pnl_percentage,
                    operations,
                    open_time: row.open_time,
                    close_time: row.close_time,
                    chain: row.chain, // Default to Solana for backward compatibility
                }
            })
            .collect();

        Ok(histories)
    }

    pub async fn get_histories_by_time_range(
        &self,
        wallet_address: &str,
        start_time: i64,
        end_time: i64,
        limit: i64,
    ) -> Result<Vec<DbHistory>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM histories
            WHERE wallet_address = $1
            AND close_time >= $2
            AND close_time <= $3
            ORDER BY close_time DESC
            LIMIT $4
            "#,
            wallet_address,
            start_time,
            end_time,
            limit
        )
        .fetch_all(&self.pool)
        .await?;

        let histories: Vec<DbHistory> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbHistory {
                    id: Some(row.id),
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    pnl_usd: row.pnl_usd,
                    pnl_percentage: row.pnl_percentage,
                    operations,
                    open_time: row.open_time,
                    close_time: row.close_time,
                    chain: row.chain, // Default to Solana for backward compatibility
                }
            })
            .collect();

        Ok(histories)
    }

    pub async fn get_histories_ordered_by_open_time(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbHistory>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM histories WHERE wallet_address = $1
            ORDER BY open_time DESC
            LIMIT $2
            OFFSET $3
            "#,
            wallet_address,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let histories: Vec<DbHistory> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbHistory {
                    id: Some(row.id),
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    pnl_usd: row.pnl_usd,
                    pnl_percentage: row.pnl_percentage,
                    operations,
                    open_time: row.open_time,
                    close_time: row.close_time,
                    chain: row.chain, // Default to Solana for backward compatibility
                }
            })
            .collect();

        Ok(histories)
    }

    pub async fn get_histories_ordered_by_pnl_percentage(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbHistory>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM histories WHERE wallet_address = $1
            ORDER BY pnl_percentage DESC
            LIMIT $2
            OFFSET $3
            "#,
            wallet_address,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let histories: Vec<DbHistory> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbHistory {
                    id: Some(row.id),
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    pnl_usd: row.pnl_usd,
                    pnl_percentage: row.pnl_percentage,
                    operations,
                    open_time: row.open_time,
                    close_time: row.close_time,
                    chain: row.chain, // Default to Solana for backward compatibility
                }
            })
            .collect();

        Ok(histories)
    }

    pub async fn get_histories_ordered_by_pnl(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbHistory>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM histories WHERE wallet_address = $1
            ORDER BY pnl_usd DESC
            LIMIT $2
            OFFSET $3
            "#,
            wallet_address,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let histories: Vec<DbHistory> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbHistory {
                    id: Some(row.id),
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    pnl_usd: row.pnl_usd,
                    pnl_percentage: row.pnl_percentage,
                    operations,
                    open_time: row.open_time,
                    close_time: row.close_time,
                    chain: row.chain, // Default to Solana for backward compatibility
                }
            })
            .collect();

        Ok(histories)
    }

    pub async fn get_latest_history(
        &self,
        wallet_address: &str,
        token_mint: &str,
    ) -> Result<Option<DbHistory>> {
        let row = sqlx::query!(
            r#"
            SELECT * FROM histories WHERE wallet_address = $1 AND token_mint = $2
            ORDER BY close_time DESC
            LIMIT 1
            "#,
            wallet_address,
            token_mint
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            let operations: Vec<DbPositionChange> =
                match serde_json::from_value(row.operations.clone()) {
                    Ok(operations) => operations,
                    Err(e) => {
                        tracing::error!("Error deserializing operations: {}, row: {:?}", e, row);
                        vec![]
                    }
                };

            Ok(Some(DbHistory {
                id: Some(row.id),
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                bought_amount: row.bought_amount,
                sold_amount: row.sold_amount,
                native_decimals: row.native_decimals,
                cost_native_amount: row.cost_native_amount,
                earnings_native_amount: row.earnings_native_amount,
                cost_usd: row.cost_usd,
                earnings_usd: row.earnings_usd,
                pnl_usd: row.pnl_usd,
                pnl_percentage: row.pnl_percentage,
                operations,
                open_time: row.open_time,
                close_time: row.close_time,
                chain: row.chain, // Default to Solana for backward compatibility
            }))
        } else {
            Ok(None)
        }
    }
}
