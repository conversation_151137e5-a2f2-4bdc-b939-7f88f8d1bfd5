use std::str::FromStr;

use anyhow::Result;

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

use super::db::Database;

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum WatchType {
    SolanaToken,
    SolanaPool,
    HyperliquidPerps,
}

impl FromStr for WatchType {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "SolanaToken" => WatchType::SolanaToken,
            "SolanaPool" => WatchType::SolanaPool,
            "HyperliquidPerps" => WatchType::HyperliquidPerps,
            _ => return Err(anyhow::anyhow!("Invalid watch type: {}", s)),
        })
    }
}

impl std::fmt::Display for WatchType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            WatchType::SolanaToken => write!(f, "SolanaToken"),
            WatchType::SolanaPool => write!(f, "SolanaPool"),
            WatchType::HyperliquidPerps => write!(f, "HyperliquidPerps"),
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbWatch {
    pub wallet_address: Pubkey,
    pub add_time: i64,
    pub watch_type: WatchType,
    pub watch_id: String,
}

impl Database {
    pub async fn insert_watch(&self, watch: DbWatch) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO watches (
                wallet_address, add_time, watch_type, watch_id
            )
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (wallet_address, watch_type, watch_id) DO NOTHING
            "#,
            watch.wallet_address.to_string(),
            watch.add_time,
            watch.watch_type.to_string(),
            watch.watch_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_watches(&self, wallet_address: &str) -> Result<Vec<DbWatch>> {
        let records = sqlx::query!(
            r#"
            SELECT * FROM watches WHERE wallet_address = $1
            "#,
            wallet_address
        )
        .fetch_all(&self.pool)
        .await?;

        let mut watches = Vec::new();
        for record in records {
            watches.push(DbWatch {
                wallet_address: Pubkey::from_str(&record.wallet_address)?,
                add_time: record.add_time,
                watch_type: WatchType::from_str(&record.watch_type)?,
                watch_id: record.watch_id,
            });
        }

        Ok(watches)
    }

    pub async fn delete_watch(
        &self,
        wallet_address: &str,
        watch_type: &WatchType,
        watch_id: &str,
    ) -> Result<()> {
        sqlx::query!(
            r#"
            DELETE FROM watches WHERE wallet_address = $1 AND watch_type = $2 AND watch_id = $3
            "#,
            wallet_address,
            watch_type.to_string(),
            watch_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_watches_number(&self, wallet_address: &str) -> Result<i64> {
        let count = sqlx::query!(
            r#"
            SELECT COUNT(*) FROM watches WHERE wallet_address = $1
            "#,
            wallet_address
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.count.unwrap_or(0))
    }
}
