use anyhow::Result;
use chrono::Utc;
use serde::{Deserialize, Serialize};

use super::db::Database;

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbTokenInfo {
    pub mint: String,
    pub name: String,
    pub symbol: String,
    pub image: Option<String>,
    pub description: Option<String>,
    pub usd_price: f64,
    pub updated_at: i64,
}

impl Database {
    pub async fn insert_token_info(&self, info: DbTokenInfo) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO token_info (
                mint, name, symbol, image, description, usd_price, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7
            ) ON CONFLICT (mint) DO UPDATE SET
                name = $2,
                symbol = $3,
                image = $4,
                description = $5,
                usd_price = $6,
                updated_at = $7
            "#,
            info.mint,
            info.name,
            info.symbol,
            info.image,
            info.description,
            info.usd_price,
            info.updated_at
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_token_info(&self, mint: &str) -> Result<Option<DbTokenInfo>> {
        let info = sqlx::query_as!(
            DbTokenInfo,
            r#"
            SELECT * FROM token_info WHERE mint = $1
            "#,
            mint
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(info)
    }

    pub async fn get_token_infos(&self, mints: &[String]) -> Result<Vec<DbTokenInfo>> {
        let info = sqlx::query_as!(
            DbTokenInfo,
            r#"
            SELECT * FROM token_info WHERE mint = ANY($1)
            "#,
            mints
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(info)
    }

    pub async fn update_token_info_price(&self, mint: &str, usd_price: f64) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE token_info SET usd_price = $1, updated_at = $2 WHERE mint = $3
            "#,
            usd_price,
            Utc::now().timestamp(),
            mint
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_tokens_before_timestamp(
        &self,
        timestamp: i64,
        limit: i64,
    ) -> Result<Vec<String>> {
        let tokens = sqlx::query_scalar!(
            r#"
            SELECT mint FROM token_info WHERE updated_at < $1 ORDER BY updated_at ASC LIMIT $2
            "#,
            timestamp,
            limit
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(tokens)
    }
}
