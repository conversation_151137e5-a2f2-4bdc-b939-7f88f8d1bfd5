use std::sync::Arc;

pub mod account_value;
pub mod activity;
pub mod db;
pub mod history;
pub mod hyperliquid_activity;
pub mod info;

pub mod order;
pub mod pnl_share_image;
pub mod position;
pub mod realized_pnl;
pub mod referral;
pub mod setting;
pub mod telegram_user;
pub mod trade;
pub mod transaction;
pub mod types;
pub mod wallet;
pub mod watch;

pub use account_value::*;
pub use activity::*;
pub use history::*;
pub use hyperliquid_activity::*;
pub use info::*;

pub use order::*;
pub use pnl_share_image::*;
pub use position::*;
pub use realized_pnl::*;
pub use referral::*;
pub use setting::*;
pub use telegram_user::*;
pub use trade::*;
pub use transaction::*;
pub use types::*;
pub use wallet::*;
pub use watch::*;

pub use self::db::Database as ApiDatabase;

// Compatibility type for invite code API (kept for backward compatibility)
#[derive(Clone, Debug, serde::Serialize, serde::Deserialize)]
pub struct InviteCode {
    pub code: String,
    pub wallet_address: String,
    pub created_at: i64,
    pub is_active: bool,
}

impl From<ReferralCode> for InviteCode {
    fn from(rc: ReferralCode) -> Self {
        Self {
            code: rc.code,
            wallet_address: rc.used_by_wallet.unwrap_or_default(),
            created_at: rc.used_at.unwrap_or(rc.created_at),
            is_active: rc.status == "used",
        }
    }
}

impl InviteCode {
    pub fn is_valid(&self) -> bool {
        self.is_active
    }
}
use superstack_data::postgres::PostgresDatabase as IndexerDatabase;
pub use superstack_data::{
    data_provider::{IndexerDataProvider, TokenMetadataWithPrice},
    postgres::{
        aggregator::{PoolStatistic, TokenStatistic},
        enums::Chain,
    },
    redis::RedisClient,
};

#[derive(Clone)]
pub struct StorageState {
    pub api_db: Arc<ApiDatabase>,
    pub indexer_db: Arc<IndexerDatabase>,
    pub indexer_data_provider: Arc<IndexerDataProvider>,
    pub redis_client: RedisClient,
}

impl StorageState {
    // Trade operations
    pub async fn insert_trade(&self, trade: &DbTrade) -> anyhow::Result<()> {
        self.api_db.insert_trade(trade).await
    }

    pub async fn get_trade(&self, tx_sig: &str) -> anyhow::Result<Option<DbTrade>> {
        self.api_db.get_trade(tx_sig).await
    }

    pub async fn get_trades(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbTrade>> {
        self.api_db.get_trades(wallet_address, limit, offset).await
    }

    pub async fn get_trades_for_wallet_and_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbTrade>> {
        self.api_db.get_trades_for_wallet_and_token(wallet_address, token_mint, limit, offset).await
    }

    pub async fn get_latest_trade_for_wallet(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Option<DbTrade>> {
        self.api_db.get_latest_trade_for_wallet(wallet_address).await
    }

    // Multi-chain trade operations
    pub async fn get_trades_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbTrade>> {
        self.api_db.get_trades_by_chain(wallet_address, chain, limit, offset).await
    }

    pub async fn get_trades_for_wallet_and_token_by_chain(
        &self,
        wallet_address: &str,
        token_mint: &str,
        chain: i16,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbTrade>> {
        self.api_db
            .get_trades_for_wallet_and_token_by_chain(
                wallet_address,
                token_mint,
                chain,
                limit,
                offset,
            )
            .await
    }

    pub async fn get_latest_trade_for_wallet_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> anyhow::Result<Option<DbTrade>> {
        self.api_db.get_latest_trade_for_wallet_by_chain(wallet_address, chain).await
    }

    // Backward compatibility methods for Solana trades
    pub async fn get_solana_trades(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbTrade>> {
        self.get_trades_by_chain(wallet_address, 0, limit, offset).await // 0 = Solana
    }

    pub async fn get_solana_trades_for_wallet_and_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbTrade>> {
        self.get_trades_for_wallet_and_token_by_chain(wallet_address, token_mint, 0, limit, offset)
            .await // 0 = Solana
    }

    pub async fn get_latest_solana_trade_for_wallet(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Option<DbTrade>> {
        self.get_latest_trade_for_wallet_by_chain(wallet_address, 0).await // 0 = Solana
    }

    // Time-range trade operations for user indicators
    pub async fn get_trades_by_time_range(
        &self,
        wallet_address: &str,
        token_mint: &str,
        start_time: i64,
        end_time: i64,
        chain: Option<superstack_data::postgres::enums::Chain>,
    ) -> anyhow::Result<Vec<DbTrade>> {
        self.api_db
            .get_trades_by_time_range(wallet_address, token_mint, start_time, end_time, chain)
            .await
    }

    // Get all trades for a wallet and token to check for open positions
    pub async fn get_all_trades_for_position_check(
        &self,
        wallet_address: &str,
        token_mint: &str,
        chain: Option<superstack_data::postgres::enums::Chain>,
    ) -> anyhow::Result<Vec<DbTrade>> {
        self.api_db.get_all_trades_for_position_check(wallet_address, token_mint, chain).await
    }

    // Transaction operations
    pub async fn insert_transaction(&self, transaction: &DbTransaction) -> anyhow::Result<()> {
        self.api_db.insert_transaction(transaction.clone()).await
    }

    pub async fn get_transaction(&self, signature: &str) -> anyhow::Result<Option<DbTransaction>> {
        self.api_db.get_transaction(signature).await
    }

    pub async fn get_pending_transactions(&self) -> anyhow::Result<Vec<DbTransaction>> {
        self.api_db.get_pending_transactions().await
    }

    pub async fn get_unprocessed_successful_transactions(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Vec<DbTransaction>> {
        self.api_db.get_unprocessed_successful_transactions(wallet_address).await
    }

    pub async fn get_all_successful_transactions(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Vec<DbTransaction>> {
        self.api_db.get_all_successful_transactions(wallet_address).await
    }

    pub async fn get_transaction_counts(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<(usize, usize)> {
        self.api_db.get_transaction_counts(wallet_address).await
    }

    // Multi-chain transaction operations
    pub async fn get_pending_transactions_by_chain(
        &self,
        chain: i16,
    ) -> anyhow::Result<Vec<DbTransaction>> {
        self.api_db.get_pending_transactions_by_chain(chain).await
    }

    pub async fn get_unprocessed_successful_transactions_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> anyhow::Result<Vec<DbTransaction>> {
        self.api_db.get_unprocessed_successful_transactions_by_chain(wallet_address, chain).await
    }

    // Backward compatibility methods for Solana transactions
    pub async fn get_solana_pending_transactions(&self) -> anyhow::Result<Vec<DbTransaction>> {
        self.get_pending_transactions_by_chain(0).await // 0 = Solana
    }

    pub async fn get_solana_unprocessed_successful_transactions(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Vec<DbTransaction>> {
        self.get_unprocessed_successful_transactions_by_chain(wallet_address, 0).await // 0 = Solana
    }

    // Position operations
    pub async fn insert_position(&self, position: DbPosition) -> anyhow::Result<()> {
        self.api_db.insert_position(position).await
    }

    pub async fn update_position(&self, position: DbPosition) -> anyhow::Result<()> {
        self.api_db.update_position(position).await
    }

    pub async fn get_position(
        &self,
        wallet_address: &str,
        token_mint: &str,
        chain: i16,
    ) -> anyhow::Result<Option<DbPosition>> {
        self.api_db.get_position(wallet_address, token_mint, chain).await
    }

    // Backward compatibility method for Solana positions
    pub async fn get_solana_position(
        &self,
        wallet_address: &str,
        token_mint: &str,
    ) -> anyhow::Result<Option<DbPosition>> {
        self.get_position(wallet_address, token_mint, 0).await // 0 = Solana
    }

    pub async fn get_positions(&self, wallet_address: &str) -> anyhow::Result<Vec<DbPosition>> {
        self.api_db.get_positions(wallet_address).await
    }

    pub async fn get_positions_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> anyhow::Result<Vec<DbPosition>> {
        self.api_db.get_positions_by_chain(wallet_address, chain).await
    }

    // Backward compatibility method for Solana positions
    pub async fn get_solana_positions(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Vec<DbPosition>> {
        self.get_positions_by_chain(wallet_address, 0).await // 0 = Solana
    }

    // Wallet operations
    pub async fn insert_or_update_wallet(&self, wallet: DbWallet) -> anyhow::Result<()> {
        self.api_db.insert_or_update_wallet(wallet).await
    }

    pub async fn get_wallet(&self, wallet_address: &str) -> anyhow::Result<Option<DbWallet>> {
        self.api_db.get_wallet(wallet_address).await
    }

    // Order operations
    pub async fn get_active_orders_for_wallet(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Vec<DbOrder>> {
        self.api_db.get_active_orders_for_wallet(wallet_address).await
    }

    // History operations
    pub async fn close_position(&self, history: DbHistory) -> anyhow::Result<()> {
        self.api_db.close_position(history).await
    }

    // Realized PnL operations
    pub async fn insert_or_update_realized_pnl(
        &self,
        realized_pnl: DbRealizedPnl,
    ) -> anyhow::Result<()> {
        self.api_db.insert_or_update_realized_pnl(realized_pnl).await
    }

    // Transaction operations (additional methods)
    pub async fn update_transaction_status_if_pending(
        &self,
        signature: &str,
        status: &TransactionStatus,
    ) -> anyhow::Result<()> {
        self.api_db.update_transaction_status_if_pending(signature, status).await
    }

    pub async fn update_transaction_processed(&self, signature: &str) -> anyhow::Result<()> {
        self.api_db.update_transaction_processed(signature).await
    }

    // Transaction stats operations
    pub async fn get_transaction_stats(&self) -> anyhow::Result<TransactionStats> {
        self.api_db.get_transaction_stats().await
    }

    // Account value operations
    pub async fn insert_account_value(&self, account_value: DbAccountValue) -> anyhow::Result<()> {
        self.api_db.insert_account_value(account_value).await
    }

    pub async fn get_latest_account_value(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Option<DbAccountValue>> {
        self.api_db.get_latest_account_value(wallet_address).await
    }

    pub async fn get_all_latest_account_values(&self) -> anyhow::Result<Vec<DbAccountValue>> {
        self.api_db.get_all_latest_account_values().await
    }

    // Position operations (additional methods)
    pub async fn get_all_positions(&self) -> anyhow::Result<Vec<DbPosition>> {
        self.api_db.get_all_positions().await
    }

    // Order operations (additional methods)
    pub async fn get_active_orders_for_token(
        &self,
        token_mint: &str,
    ) -> anyhow::Result<Vec<DbOrder>> {
        self.api_db.get_active_orders_for_token(token_mint).await
    }

    // Watch operations
    pub async fn get_watches(&self, wallet_address: &str) -> anyhow::Result<Vec<DbWatch>> {
        self.api_db.get_watches(wallet_address).await
    }

    pub async fn insert_watch(&self, watch: DbWatch) -> anyhow::Result<()> {
        self.api_db.insert_watch(watch).await
    }

    pub async fn delete_watch(
        &self,
        wallet_address: &str,
        watch_type: &WatchType,
        watch_id: &str,
    ) -> anyhow::Result<()> {
        self.api_db.delete_watch(wallet_address, watch_type, watch_id).await
    }

    pub async fn get_watches_number(&self, wallet_address: &str) -> anyhow::Result<i64> {
        self.api_db.get_watches_number(wallet_address).await
    }

    // Setting operations
    pub async fn get_setting(&self, wallet_address: &str) -> anyhow::Result<Option<DbSetting>> {
        self.api_db.get_setting(wallet_address).await
    }

    pub async fn insert_or_update_setting(&self, setting: DbSetting) -> anyhow::Result<()> {
        self.api_db.insert_or_update_setting(setting).await
    }

    // PnL Share Image operations
    pub async fn get_pnl_share_images(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Vec<DbPnlShareImage>> {
        self.api_db.get_pnl_share_images(wallet_address).await
    }

    pub async fn insert_pnl_share_image(
        &self,
        image: CreatePnlShareImage,
    ) -> anyhow::Result<DbPnlShareImage> {
        self.api_db.insert_pnl_share_image(image).await
    }

    pub async fn delete_pnl_share_image(
        &self,
        wallet_address: &str,
        image_path: &str,
    ) -> anyhow::Result<Option<DbPnlShareImage>> {
        self.api_db.delete_pnl_share_image(wallet_address, image_path).await
    }

    pub async fn has_pnl_share_images(&self, wallet_address: &str) -> anyhow::Result<bool> {
        self.api_db.has_pnl_share_images(wallet_address).await
    }

    // History operations
    pub async fn get_histories_by_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
    ) -> anyhow::Result<Vec<DbHistory>> {
        self.api_db.get_histories_by_token(wallet_address, token_mint).await
    }

    pub async fn get_histories_ordered_by_open_time(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbHistory>> {
        self.api_db.get_histories_ordered_by_open_time(wallet_address, limit, offset).await
    }

    pub async fn get_histories_ordered_by_close_time(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbHistory>> {
        self.api_db.get_histories_ordered_by_close_time(wallet_address, limit, offset).await
    }

    pub async fn get_histories_ordered_by_pnl(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbHistory>> {
        self.api_db.get_histories_ordered_by_pnl(wallet_address, limit, offset).await
    }

    pub async fn get_histories_ordered_by_pnl_percentage(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbHistory>> {
        self.api_db.get_histories_ordered_by_pnl_percentage(wallet_address, limit, offset).await
    }

    pub async fn get_latest_history(
        &self,
        wallet_address: &str,
        token_mint: &str,
    ) -> anyhow::Result<Option<DbHistory>> {
        self.api_db.get_latest_history(wallet_address, token_mint).await
    }

    // Realized PnL operations (additional methods)
    pub async fn get_realized_pnls(
        &self,
        wallet_address: &str,
        start_timestamp: i64,
        end_timestamp: i64,
    ) -> anyhow::Result<Vec<DbRealizedPnl>> {
        self.api_db.get_realized_pnls(wallet_address, start_timestamp, end_timestamp).await
    }

    // Account value operations (additional methods)
    pub async fn get_account_values(
        &self,
        wallet_address: &str,
        start_timestamp: i64,
        end_timestamp: i64,
    ) -> anyhow::Result<Vec<DbAccountValue>> {
        self.api_db.get_account_values(wallet_address, start_timestamp, end_timestamp).await
    }

    pub async fn get_latest_account_value_before_timestamp(
        &self,
        wallet_address: &str,
        timestamp: i64,
    ) -> anyhow::Result<Option<DbAccountValue>> {
        self.api_db.get_latest_account_value_before_timestamp(wallet_address, timestamp).await
    }

    pub async fn get_first_non_zero_account_value_after_timestamp(
        &self,
        wallet_address: &str,
        timestamp: i64,
    ) -> anyhow::Result<Option<DbAccountValue>> {
        self.api_db
            .get_first_non_zero_account_value_after_timestamp(wallet_address, timestamp)
            .await
    }

    // Token info operations
    pub async fn get_token_info(&self, token_mint: &str) -> anyhow::Result<Option<DbTokenInfo>> {
        self.api_db.get_token_info(token_mint).await
    }

    pub async fn insert_token_info(&self, token_info: DbTokenInfo) -> anyhow::Result<()> {
        self.api_db.insert_token_info(token_info).await
    }

    // Order operations (additional methods)
    pub async fn insert_order(&self, order: DbOrder) -> anyhow::Result<()> {
        self.api_db.insert_order(order).await
    }

    pub async fn cancel_order(&self, order_id: &str) -> anyhow::Result<()> {
        self.api_db.cancel_order(order_id).await
    }

    pub async fn get_active_orders_for_wallet_and_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
    ) -> anyhow::Result<Vec<DbOrder>> {
        self.api_db.get_active_orders_for_wallet_and_token(wallet_address, token_mint).await
    }

    pub async fn get_history_orders_for_wallet_and_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbOrder>> {
        self.api_db
            .get_history_orders_for_wallet_and_token(wallet_address, token_mint, limit, offset)
            .await
    }

    pub async fn get_history_orders_for_wallet(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbOrder>> {
        self.api_db.get_history_orders_for_wallet(wallet_address, limit, offset).await
    }

    pub async fn get_order(&self, order_id: &str) -> anyhow::Result<Option<DbOrder>> {
        self.api_db.get_order(order_id).await
    }

    pub async fn update_order_remaining_amounts(
        &self,
        order_id: &str,
        remaining_token_amount: i64,
        remaining_base_amount: i64,
    ) -> anyhow::Result<()> {
        self.api_db
            .update_order_remaining_amounts(order_id, remaining_token_amount, remaining_base_amount)
            .await
    }

    // Chain-specific order operations
    pub async fn get_active_orders_for_wallet_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> anyhow::Result<Vec<DbOrder>> {
        self.api_db.get_active_orders_for_wallet_by_chain(wallet_address, chain).await
    }

    pub async fn get_history_orders_for_wallet_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<DbOrder>> {
        self.api_db
            .get_history_orders_for_wallet_by_chain(wallet_address, chain, limit, offset)
            .await
    }

    // Wallet activity operations
    pub async fn insert_wallet_activity(&self, activity: DbWalletActivity) -> anyhow::Result<()> {
        self.api_db.insert_wallet_activity(activity).await
    }

    pub async fn get_wallet_activities_with_timestamp(
        &self,
        wallet_address: &str,
        limit: i64,
        before_timestamp: Option<i64>,
        include_other: bool,
        is_mainnet: Option<bool>,
    ) -> anyhow::Result<Vec<DbWalletActivity>> {
        self.api_db
            .get_wallet_activities_with_timestamp(
                wallet_address,
                limit,
                before_timestamp,
                include_other,
                is_mainnet,
            )
            .await
    }

    pub async fn get_latest_wallet_activity_timestamp(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> anyhow::Result<Option<i64>> {
        self.api_db.get_latest_wallet_activity_timestamp(wallet_address, chain).await
    }

    pub async fn batch_insert_wallet_activities(
        &self,
        activities: Vec<DbWalletActivity>,
    ) -> anyhow::Result<()> {
        self.api_db.batch_insert_wallet_activities(activities).await
    }

    pub async fn get_wallet_activity_stats(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<(i64, i64, i64, i64, i64, i64, f64, Option<i64>, Option<i64>)> {
        self.api_db.get_wallet_activity_stats(wallet_address).await
    }

    // Referral system operations
    pub async fn get_user_referral_codes(
        &self,
        owner_wallet: &str,
    ) -> anyhow::Result<Vec<ReferralCode>> {
        self.api_db.get_user_referral_codes(owner_wallet).await
    }

    pub async fn mark_referral_code_used(
        &self,
        code: &str,
        used_by_wallet: &str,
    ) -> anyhow::Result<bool> {
        self.api_db.mark_referral_code_used(code, used_by_wallet).await
    }

    // API Access authorization methods (unified with referral codes)
    pub async fn bind_invite_code(&self, code: &str, wallet_address: &str) -> anyhow::Result<bool> {
        self.api_db.bind_invite_code(code, wallet_address).await
    }

    pub async fn get_all_referrals(&self) -> anyhow::Result<Vec<Referral>> {
        self.api_db.get_all_referrals().await
    }

    pub async fn get_used_referral_code_by_wallet(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Option<ReferralCode>> {
        self.api_db.get_used_referral_code_by_wallet(wallet_address).await
    }

    // Removed duplicate method definitions

    pub async fn get_referral_code_by_owner(
        &self,
        owner_wallet: &str,
    ) -> anyhow::Result<Option<ReferralCode>> {
        // Use the new get_user_referral_codes and return the first active code
        let codes = self.api_db.get_user_referral_codes(owner_wallet).await?;
        Ok(codes.into_iter().find(|code| code.status == "active"))
    }

    pub async fn get_referral_code_by_code(
        &self,
        code: &str,
    ) -> anyhow::Result<Option<ReferralCode>> {
        self.api_db.get_referral_code_by_code(code).await
    }

    pub async fn create_referral(
        &self,
        referrer_wallet: &str,
        referee_wallet: &str,
    ) -> anyhow::Result<bool> {
        self.api_db.create_referral(referrer_wallet, referee_wallet).await
    }

    pub async fn get_referral_by_referee(
        &self,
        referee_wallet: &str,
    ) -> anyhow::Result<Option<Referral>> {
        self.api_db.get_referral_by_referee(referee_wallet).await
    }

    pub async fn get_referrals_by_referrer(
        &self,
        referrer_wallet: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<Referral>> {
        self.api_db.get_referrals_by_referrer(referrer_wallet, limit, offset).await
    }

    pub async fn get_referral_count(&self, wallet_address: &str) -> anyhow::Result<i64> {
        // Use get_referral_stats and extract total_invites
        let stats = self.api_db.get_referral_stats(wallet_address).await?;
        Ok(stats.total_invites)
    }

    pub async fn get_referral_stats(&self, wallet_address: &str) -> anyhow::Result<ReferralStats> {
        self.api_db.get_referral_stats(wallet_address).await
    }

    pub async fn get_referral_network(&self, root_wallet: &str) -> anyhow::Result<ReferralNetwork> {
        self.api_db.get_referral_network(root_wallet).await
    }

    pub async fn create_referral_reward(
        &self,
        referrer_wallet: &str,
        referee_wallet: &str,
        reward_type: &str,
        trading_volume_usd: Option<rust_decimal::Decimal>,
        tx_signature: Option<&str>,
    ) -> anyhow::Result<Option<i64>> {
        self.api_db
            .create_referral_reward(
                referrer_wallet,
                referee_wallet,
                reward_type,
                trading_volume_usd,
                tx_signature,
            )
            .await
    }

    pub async fn get_referral_rewards(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> anyhow::Result<Vec<ReferralReward>> {
        self.api_db.get_referral_rewards(wallet_address, limit, offset).await
    }

    pub async fn get_referral_rewards_count(&self, wallet_address: &str) -> anyhow::Result<i64> {
        self.api_db.get_referral_rewards_count(wallet_address).await
    }

    pub async fn claim_referral_reward(
        &self,
        reward_id: i64,
        wallet_address: &str,
    ) -> anyhow::Result<bool> {
        self.api_db.claim_referral_reward(reward_id, wallet_address).await
    }

    pub async fn get_telegram_user_by_wallet(
        &self,
        wallet_address: &str,
    ) -> anyhow::Result<Option<TelegramUser>> {
        self.api_db.get_telegram_user_by_wallet(wallet_address).await
    }

    pub async fn get_telegram_user_by_id(
        &self,
        telegram_user_id: u64,
    ) -> anyhow::Result<Option<TelegramUser>> {
        self.api_db.get_telegram_user_by_id(telegram_user_id).await
    }

    pub async fn update_telegram_user_bind_account(
        &self,
        telegram_user_id: u64,
        bind_account: &str,
        auth_code: &str,
    ) -> anyhow::Result<bool> {
        self.api_db
            .update_telegram_user_bind_account(telegram_user_id, bind_account, auth_code)
            .await
    }

    // Metrics operations
    pub fn get_metrics_snapshot(&self) -> crate::metrics::MetricsSnapshot {
        crate::metrics::get_metrics().snapshot()
    }

    pub fn reset_metrics(&self) {
        crate::metrics::get_metrics().reset()
    }
}
