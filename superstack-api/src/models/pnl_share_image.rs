use anyhow::Result;
use chrono::Utc;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

use super::db::Database;

#[derive(Clone, Debug, Serialize, Deserialize, FromRow)]
pub struct DbPnlShareImage {
    pub wallet_address: String,
    pub image_path: String,
    pub created_at: i64,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct CreatePnlShareImage {
    pub wallet_address: String,
    pub image_path: String,
}

impl Database {
    /// Get all images for a user
    pub async fn get_pnl_share_images(&self, wallet_address: &str) -> Result<Vec<DbPnlShareImage>> {
        let records = sqlx::query_as!(
            DbPnlShareImage,
            r#"
            SELECT wallet_address, image_path, created_at
            FROM pnl_share_images
            WHERE wallet_address = $1
            ORDER BY created_at DESC
            "#,
            wallet_address
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(records)
    }

    /// Insert a new image
    pub async fn insert_pnl_share_image(
        &self,
        image: CreatePnlShareImage,
    ) -> Result<DbPnlShareImage> {
        let created_at = Utc::now().timestamp_millis();

        let record = sqlx::query_as!(
            DbPnlShareImage,
            r#"
            INSERT INTO pnl_share_images (wallet_address, image_path, created_at)
            VALUES ($1, $2, $3)
            RETURNING wallet_address, image_path, created_at
            "#,
            image.wallet_address,
            image.image_path,
            created_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(record)
    }

    /// Delete an image
    pub async fn delete_pnl_share_image(
        &self,
        wallet_address: &str,
        image_path: &str,
    ) -> Result<Option<DbPnlShareImage>> {
        let record = sqlx::query_as!(
            DbPnlShareImage,
            r#"
            DELETE FROM pnl_share_images
            WHERE wallet_address = $1 AND image_path = $2
            RETURNING wallet_address, image_path, created_at
            "#,
            wallet_address,
            image_path
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(record)
    }

    /// Check if user has any images
    pub async fn has_pnl_share_images(&self, wallet_address: &str) -> Result<bool> {
        let count = sqlx::query!(
            r#"
            SELECT COUNT(*) as count FROM pnl_share_images WHERE wallet_address = $1
            "#,
            wallet_address
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.count.unwrap_or(0) > 0)
    }
}
