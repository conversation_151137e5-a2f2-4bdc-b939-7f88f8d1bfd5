use anyhow::Result;
use serde::{Deserialize, Serialize};

use super::db::Database;

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbPosition {
    pub wallet_address: String,
    pub token_mint: String,
    pub token_decimals: i16,

    pub bought_amount: i64,
    pub sold_amount: i64,

    pub native_decimals: i16,
    pub cost_native_amount: i64,
    pub earnings_native_amount: i64,

    // other costs except for the native token will be converted to USD using the price at the time
    // of the trade for example, if the cost is 100 USDC, $100 will be stored in cost_usd
    pub cost_usd: f64,
    // other earnings except for the native token will be converted to USD using the price at the
    // time of the trade for example, if the earnings are 100 USDC, $100 will be stored in
    // earnings_usd
    pub earnings_usd: f64,

    pub operations: Vec<DbPositionChange>,

    pub open_time: i64,
    pub chain: i16,
}

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbPositionChange {
    pub is_buy: bool,

    pub token_amount: i64,
    pub base_amount: i64,

    pub base_mint: String,
    pub base_decimals: i16,

    pub tx_sig: String,
    pub timestamp: i64,
    #[serde(default)]
    pub chain: i16,
}

impl Database {
    pub async fn insert_position(&self, position: DbPosition) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO positions (
                wallet_address, token_mint, token_decimals,
                bought_amount, sold_amount,
                native_decimals, cost_native_amount, earnings_native_amount,
                cost_usd, earnings_usd,
                operations,
                open_time, chain
            )
            VALUES (
                $1, $2, $3,
                $4, $5,
                $6, $7, $8,
                $9, $10,
                $11,
                $12, $13
            )
            ON CONFLICT (wallet_address, token_mint, chain) DO NOTHING
            "#,
            position.wallet_address,
            position.token_mint,
            position.token_decimals,
            position.bought_amount,
            position.sold_amount,
            position.native_decimals,
            position.cost_native_amount,
            position.earnings_native_amount,
            position.cost_usd,
            position.earnings_usd,
            serde_json::to_value(&position.operations).unwrap(),
            position.open_time,
            position.chain,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn update_position(&self, position: DbPosition) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE positions SET
                bought_amount = $1, sold_amount = $2,
                cost_native_amount = $3, earnings_native_amount = $4,
                cost_usd = $5, earnings_usd = $6,
                operations = $7
            WHERE wallet_address = $8 AND token_mint = $9 AND chain = $10
            "#,
            position.bought_amount,
            position.sold_amount,
            position.cost_native_amount,
            position.earnings_native_amount,
            position.cost_usd,
            position.earnings_usd,
            serde_json::to_value(&position.operations).unwrap(),
            position.wallet_address,
            position.token_mint,
            position.chain,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_position(
        &self,
        wallet_address: &str,
        token_mint: &str,
        chain: i16,
    ) -> Result<Option<DbPosition>> {
        let row = match sqlx::query!(
            r#"
            SELECT wallet_address, token_mint, token_decimals,
                   bought_amount, sold_amount,
                   native_decimals, cost_native_amount, earnings_native_amount,
                   cost_usd, earnings_usd,
                   operations,
                   open_time, chain
            FROM positions WHERE wallet_address = $1 AND token_mint = $2 AND chain = $3
            "#,
            wallet_address,
            token_mint,
            chain
        )
        .fetch_optional(&self.pool)
        .await?
        {
            Some(row) => row,
            None => return Ok(None),
        };

        let operations: Vec<DbPositionChange> = match serde_json::from_value(row.operations.clone())
        {
            Ok(operations) => operations,
            Err(e) => {
                tracing::error!("Error deserializing operations: {}, row: {:?}", e, row);
                vec![]
            }
        };

        let position = DbPosition {
            wallet_address: row.wallet_address,
            token_mint: row.token_mint,
            token_decimals: row.token_decimals,
            bought_amount: row.bought_amount,
            sold_amount: row.sold_amount,
            native_decimals: row.native_decimals,
            cost_native_amount: row.cost_native_amount,
            earnings_native_amount: row.earnings_native_amount,
            cost_usd: row.cost_usd,
            earnings_usd: row.earnings_usd,
            operations,
            open_time: row.open_time,
            chain: row.chain,
        };

        Ok(Some(position))
    }

    pub async fn get_positions(&self, wallet_address: &str) -> Result<Vec<DbPosition>> {
        let rows = sqlx::query!(
            r#"
            SELECT wallet_address, token_mint, token_decimals,
                   bought_amount, sold_amount,
                   native_decimals, cost_native_amount, earnings_native_amount,
                   cost_usd, earnings_usd,
                   operations,
                   open_time, chain
            FROM positions WHERE wallet_address = $1
            "#,
            wallet_address
        )
        .fetch_all(&self.pool)
        .await?;

        let positions: Vec<DbPosition> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbPosition {
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    operations,
                    open_time: row.open_time,
                    chain: row.chain,
                }
            })
            .collect();

        Ok(positions)
    }

    pub async fn get_positions_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> Result<Vec<DbPosition>> {
        let rows = sqlx::query!(
            r#"
            SELECT wallet_address, token_mint, token_decimals,
                   bought_amount, sold_amount,
                   native_decimals, cost_native_amount, earnings_native_amount,
                   cost_usd, earnings_usd,
                   operations,
                   open_time, chain
            FROM positions WHERE wallet_address = $1 AND chain = $2
            "#,
            wallet_address,
            chain
        )
        .fetch_all(&self.pool)
        .await?;

        let positions: Vec<DbPosition> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbPosition {
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    operations,
                    open_time: row.open_time,
                    chain: row.chain,
                }
            })
            .collect();

        Ok(positions)
    }

    pub async fn get_all_positions(&self) -> Result<Vec<DbPosition>> {
        let rows = sqlx::query!(
            r#"
            SELECT wallet_address, token_mint, token_decimals,
                   bought_amount, sold_amount,
                   native_decimals, cost_native_amount, earnings_native_amount,
                   cost_usd, earnings_usd,
                   operations,
                   open_time, chain
            FROM positions
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let positions: Vec<DbPosition> = rows
            .into_iter()
            .map(|row| {
                let operations: Vec<DbPositionChange> =
                    match serde_json::from_value(row.operations.clone()) {
                        Ok(operations) => operations,
                        Err(e) => {
                            tracing::error!(
                                "Error deserializing operations: {}, row: {:?}",
                                e,
                                row
                            );
                            vec![]
                        }
                    };

                DbPosition {
                    wallet_address: row.wallet_address,
                    token_mint: row.token_mint,
                    token_decimals: row.token_decimals,
                    bought_amount: row.bought_amount,
                    sold_amount: row.sold_amount,
                    native_decimals: row.native_decimals,
                    cost_native_amount: row.cost_native_amount,
                    earnings_native_amount: row.earnings_native_amount,
                    cost_usd: row.cost_usd,
                    earnings_usd: row.earnings_usd,
                    operations,
                    open_time: row.open_time,
                    chain: row.chain,
                }
            })
            .collect();

        Ok(positions)
    }
}
