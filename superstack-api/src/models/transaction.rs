use std::str::FromStr;

use anyhow::Result;

use serde::{Deserialize, Serialize};

use super::db::Database;
use crate::models::types::TransactionStatus;

#[derive(<PERSON>lone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbTransaction {
    pub signature: String,
    pub wallet_address: String,
    #[serde(
        serialize_with = "serialize_transaction_status",
        deserialize_with = "deserialize_transaction_status"
    )]
    pub status: TransactionStatus,
    #[serde(serialize_with = "serialize_bool_as_u8", deserialize_with = "deserialize_u8_as_bool")]
    pub is_processed: bool,
    pub created_at: i64,
    pub chain: i16,
}

fn serialize_transaction_status<S>(
    status: &TransactionStatus,
    serializer: S,
) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    serializer.serialize_str(&status.to_string())
}

fn deserialize_transaction_status<'de, D>(deserializer: D) -> Result<TransactionStatus, D::Error>
where
    D: serde::Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    TransactionStatus::from_str(&s).map_err(serde::de::Error::custom)
}

fn serialize_bool_as_u8<S>(value: &bool, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    serializer.serialize_u8(if *value { 1 } else { 0 })
}

fn deserialize_u8_as_bool<'de, D>(deserializer: D) -> Result<bool, D::Error>
where
    D: serde::Deserializer<'de>,
{
    let value = u8::deserialize(deserializer)?;
    Ok(value != 0)
}

#[derive(Clone, Debug, Serialize, Deserialize, Default)]
pub struct TransactionStats {
    pub success_count: usize,
    pub success_count_24h: usize,
    pub pending_count: usize,
    pub pending_count_24h: usize,
    pub failed_count: usize,
    pub failed_count_24h: usize,
    pub expired_count: usize,
    pub expired_count_24h: usize,
}

impl Database {
    pub async fn insert_transaction(&self, transaction: DbTransaction) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO transactions (
                signature, wallet_address, status, is_processed, created_at, chain
            ) VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (signature) DO NOTHING
            "#,
            transaction.signature,
            transaction.wallet_address,
            transaction.status.to_string(),
            transaction.is_processed,
            transaction.created_at,
            transaction.chain
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn update_transaction_status_if_pending(
        &self,
        signature: &str,
        status: &TransactionStatus,
    ) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE transactions SET status = $1 
            WHERE signature = $2 AND status = $3
            "#,
            status.to_string(),
            signature,
            TransactionStatus::Pending.to_string()
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn get_transaction(&self, signature: &str) -> Result<Option<DbTransaction>> {
        let row = sqlx::query!(
            r#"
            SELECT * FROM transactions WHERE signature = $1
            "#,
            signature
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            let transaction = DbTransaction {
                signature: row.signature,
                wallet_address: row.wallet_address,
                status: TransactionStatus::from_str(&row.status)?,
                is_processed: row.is_processed,
                created_at: row.created_at,
                chain: row.chain, // Default to Solana for backward compatibility
            };

            Ok(Some(transaction))
        } else {
            Ok(None)
        }
    }

    pub async fn get_pending_transactions(&self) -> Result<Vec<DbTransaction>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM transactions WHERE status = $1
            "#,
            TransactionStatus::Pending.to_string()
        )
        .fetch_all(&self.pool)
        .await?;

        let mut transactions = Vec::new();
        for row in rows {
            transactions.push(DbTransaction {
                signature: row.signature,
                wallet_address: row.wallet_address,
                status: TransactionStatus::from_str(&row.status)?,
                is_processed: row.is_processed,
                created_at: row.created_at,
                chain: row.chain, // Default to Solana for backward compatibility
            });
        }

        Ok(transactions)
    }

    pub async fn get_unprocessed_successful_transactions(
        &self,
        wallet_address: &str,
    ) -> Result<Vec<DbTransaction>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM transactions
            WHERE wallet_address = $1 AND status = $2 AND is_processed = false
            ORDER BY created_at ASC
            "#,
            wallet_address,
            TransactionStatus::Success.to_string()
        )
        .fetch_all(&self.pool)
        .await?;

        let mut transactions = Vec::new();
        for row in rows {
            transactions.push(DbTransaction {
                signature: row.signature,
                wallet_address: row.wallet_address,
                status: TransactionStatus::from_str(&row.status)?,
                is_processed: row.is_processed,
                created_at: row.created_at,
                chain: row.chain, // Default to Solana for backward compatibility
            });
        }

        Ok(transactions)
    }

    pub async fn get_all_successful_transactions(
        &self,
        wallet_address: &str,
    ) -> Result<Vec<DbTransaction>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM transactions
            WHERE wallet_address = $1 AND status = $2
            ORDER BY created_at ASC
            "#,
            wallet_address,
            TransactionStatus::Success.to_string()
        )
        .fetch_all(&self.pool)
        .await?;

        let mut transactions = Vec::new();
        for row in rows {
            transactions.push(DbTransaction {
                signature: row.signature,
                wallet_address: row.wallet_address,
                status: TransactionStatus::from_str(&row.status)?,
                is_processed: row.is_processed,
                created_at: row.created_at,
                chain: row.chain,
            });
        }

        Ok(transactions)
    }

    pub async fn update_transaction_processed(&self, signature: &str) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE transactions SET is_processed = true WHERE signature = $1
            "#,
            signature
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_transaction_counts(&self, wallet_address: &str) -> Result<(usize, usize)> {
        let counts = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) FILTER (WHERE status = $2) as pending_count,
                COUNT(*) FILTER (WHERE status = $3 AND is_processed = false) as unprocessed_success_count
            FROM transactions
            WHERE wallet_address = $1
            "#,
            wallet_address,
            TransactionStatus::Pending.to_string(),
            TransactionStatus::Success.to_string()
        )
        .fetch_one(&self.pool)
        .await?;

        Ok((
            counts.pending_count.unwrap_or(0) as usize,
            counts.unprocessed_success_count.unwrap_or(0) as usize,
        ))
    }

    pub async fn get_pending_transactions_by_chain(
        &self,
        chain: i16,
    ) -> Result<Vec<DbTransaction>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM transactions WHERE status = $1 AND chain = $2
            "#,
            TransactionStatus::Pending.to_string(),
            chain
        )
        .fetch_all(&self.pool)
        .await?;

        let mut transactions = Vec::new();
        for row in rows {
            transactions.push(DbTransaction {
                signature: row.signature,
                wallet_address: row.wallet_address,
                status: TransactionStatus::from_str(&row.status)?,
                is_processed: row.is_processed,
                created_at: row.created_at,
                chain: row.chain,
            });
        }

        Ok(transactions)
    }

    pub async fn get_unprocessed_successful_transactions_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> Result<Vec<DbTransaction>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM transactions
            WHERE wallet_address = $1 AND status = $2 AND is_processed = false AND chain = $3
            ORDER BY created_at ASC
            "#,
            wallet_address,
            TransactionStatus::Success.to_string(),
            chain
        )
        .fetch_all(&self.pool)
        .await?;

        let mut transactions = Vec::new();
        for row in rows {
            transactions.push(DbTransaction {
                signature: row.signature,
                wallet_address: row.wallet_address,
                status: TransactionStatus::from_str(&row.status)?,
                is_processed: row.is_processed,
                created_at: row.created_at,
                chain: row.chain,
            });
        }

        Ok(transactions)
    }

    pub async fn get_transaction_stats(&self) -> Result<TransactionStats> {
        let now = chrono::Utc::now();
        let one_day_ago = (now - chrono::Duration::days(1)).timestamp();

        let rows = sqlx::query!(
            r#"
            SELECT 
                COUNT(CASE WHEN status = $1 THEN 1 END) as total_success_count,
                COUNT(CASE WHEN status = $2 THEN 1 END) as total_pending_count,
                COUNT(CASE WHEN status = $3 THEN 1 END) as total_failed_count,
                COUNT(CASE WHEN status = $4 THEN 1 END) as total_expired_count,
                COUNT(CASE WHEN status = $1 AND created_at >= $5 THEN 1 END) as success_count_24h,
                COUNT(CASE WHEN status = $2 AND created_at >= $5 THEN 1 END) as pending_count_24h,
                COUNT(CASE WHEN status = $3 AND created_at >= $5 THEN 1 END) as failed_count_24h,
                COUNT(CASE WHEN status = $4 AND created_at >= $5 THEN 1 END) as expired_count_24h
            FROM transactions
            "#,
            TransactionStatus::Success.to_string(),
            TransactionStatus::Pending.to_string(),
            TransactionStatus::Failed.to_string(),
            TransactionStatus::Expired.to_string(),
            one_day_ago,
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(rows) = rows {
            Ok(TransactionStats {
                success_count: rows.total_success_count.unwrap_or(0) as usize,
                pending_count: rows.total_pending_count.unwrap_or(0) as usize,
                failed_count: rows.total_failed_count.unwrap_or(0) as usize,
                expired_count: rows.total_expired_count.unwrap_or(0) as usize,
                success_count_24h: rows.success_count_24h.unwrap_or(0) as usize,
                pending_count_24h: rows.pending_count_24h.unwrap_or(0) as usize,
                failed_count_24h: rows.failed_count_24h.unwrap_or(0) as usize,
                expired_count_24h: rows.expired_count_24h.unwrap_or(0) as usize,
            })
        } else {
            Ok(TransactionStats::default())
        }
    }
}
