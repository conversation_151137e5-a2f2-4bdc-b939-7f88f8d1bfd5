use std::str::FromStr;

use anyhow::Result;

use serde::{Deserialize, Serialize};

use super::{db::Database, types::OrderType};

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbOrder {
    pub order_id: String,

    pub tx_sig: String,
    pub wallet_address: String,

    pub token_mint: String,
    pub token_decimals: i16,
    pub base_mint: String,
    pub base_decimals: i16,

    pub trade_type: OrderType,

    pub token_amount: i64,
    pub remaining_token_amount: i64,
    pub base_amount: i64,
    pub remaining_base_amount: i64,

    pub timestamp: i64,
    pub slot: i64,
    pub fee: i64,

    pub is_cancelled: bool,
    pub is_completed: bool,
    pub fee_bps: i16,
    pub chain: i16,
}

impl Database {
    pub async fn insert_order(&self, order: DbOrder) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO orders (
                order_id,
                tx_sig, wallet_address, 
                token_mint, token_decimals, 
                base_mint, base_decimals, 
                trade_type, 
                token_amount, remaining_token_amount,
                base_amount, remaining_base_amount,
                timestamp, slot, fee,
                is_cancelled, is_completed,
                fee_bps, chain
            )
            VALUES (
                $1, 
                $2, $3, 
                $4, $5, 
                $6, $7, 
                $8,
                $9, $10, 
                $11, $12, 
                $13, $14, $15,
                $16, $17, 
                $18, $19
            )
            ON CONFLICT (order_id) DO NOTHING
            "#,
            order.order_id,
            order.tx_sig,
            order.wallet_address,
            order.token_mint,
            order.token_decimals,
            order.base_mint,
            order.base_decimals,
            order.trade_type.to_string(),
            order.token_amount,
            order.remaining_token_amount,
            order.base_amount,
            order.remaining_base_amount,
            order.timestamp,
            order.slot,
            order.fee,
            order.is_cancelled,
            order.is_completed,
            order.fee_bps,
            order.chain,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_order(&self, order_id: &str) -> Result<Option<DbOrder>> {
        let row = sqlx::query!(
            r#"
            SELECT * FROM orders WHERE order_id = $1
            "#,
            order_id
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            Ok(Some(DbOrder {
                order_id: row.order_id,
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: OrderType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                remaining_token_amount: row.remaining_token_amount,
                base_amount: row.base_amount,
                remaining_base_amount: row.remaining_base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                is_cancelled: row.is_cancelled,
                is_completed: row.is_completed,
                fee_bps: row.fee_bps,
                chain: row.chain,
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn cancel_order(&self, order_id: &str) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE orders SET is_cancelled = true WHERE order_id = $1
            "#,
            order_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_active_orders_for_wallet(&self, wallet_address: &str) -> Result<Vec<DbOrder>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM orders
            WHERE wallet_address = $1 and is_cancelled = false and is_completed = false
            ORDER BY timestamp ASC
            "#,
            wallet_address
        )
        .fetch_all(&self.pool)
        .await?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(DbOrder {
                order_id: row.order_id,
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: OrderType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                remaining_token_amount: row.remaining_token_amount,
                base_amount: row.base_amount,
                remaining_base_amount: row.remaining_base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                is_cancelled: row.is_cancelled,
                is_completed: row.is_completed,
                fee_bps: row.fee_bps,
                chain: row.chain,
            });
        }
        Ok(orders)
    }

    pub async fn get_active_orders_for_token(&self, token_mint: &str) -> Result<Vec<DbOrder>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM orders
            WHERE token_mint = $1 and is_cancelled = false and is_completed = false
            ORDER BY timestamp ASC
            "#,
            token_mint
        )
        .fetch_all(&self.pool)
        .await?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(DbOrder {
                order_id: row.order_id,
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: OrderType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                remaining_token_amount: row.remaining_token_amount,
                base_amount: row.base_amount,
                remaining_base_amount: row.remaining_base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                is_cancelled: row.is_cancelled,
                is_completed: row.is_completed,
                fee_bps: row.fee_bps,
                chain: row.chain,
            });
        }
        Ok(orders)
    }

    pub async fn get_active_orders_for_wallet_and_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
    ) -> Result<Vec<DbOrder>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM orders
            WHERE wallet_address = $1 and token_mint = $2 and is_cancelled = false and is_completed = false
            ORDER BY timestamp ASC
            "#,
            wallet_address,
            token_mint
        )
        .fetch_all(&self.pool)
        .await?;

        let mut open_trades = Vec::new();
        for row in rows {
            open_trades.push(DbOrder {
                order_id: row.order_id,
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: OrderType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                remaining_token_amount: row.remaining_token_amount,
                base_amount: row.base_amount,
                remaining_base_amount: row.remaining_base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                is_cancelled: row.is_cancelled,
                is_completed: row.is_completed,
                fee_bps: row.fee_bps,
                chain: row.chain,
            });
        }

        Ok(open_trades)
    }

    pub async fn get_history_orders_for_wallet(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbOrder>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM orders
            WHERE wallet_address = $1 and (is_cancelled = true or is_completed = true)
            ORDER BY timestamp DESC
            LIMIT $2 OFFSET $3
            "#,
            wallet_address,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let mut open_trades = Vec::new();
        for row in rows {
            open_trades.push(DbOrder {
                order_id: row.order_id,
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: OrderType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                remaining_token_amount: row.remaining_token_amount,
                base_amount: row.base_amount,
                remaining_base_amount: row.remaining_base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                is_cancelled: row.is_cancelled,
                is_completed: row.is_completed,
                fee_bps: row.fee_bps,
                chain: row.chain,
            });
        }

        Ok(open_trades)
    }

    pub async fn get_history_orders_for_wallet_and_token(
        &self,
        wallet_address: &str,
        token_mint: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbOrder>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM orders
            WHERE wallet_address = $1 and token_mint = $2 and (is_cancelled = true or is_completed = true)
            ORDER BY timestamp DESC
            LIMIT $3 OFFSET $4
            "#,
            wallet_address,
            token_mint,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let mut open_trades = Vec::new();
        for row in rows {
            open_trades.push(DbOrder {
                order_id: row.order_id,
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: OrderType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                remaining_token_amount: row.remaining_token_amount,
                base_amount: row.base_amount,
                remaining_base_amount: row.remaining_base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                is_cancelled: row.is_cancelled,
                is_completed: row.is_completed,
                fee_bps: row.fee_bps,
                chain: row.chain,
            });
        }

        Ok(open_trades)
    }

    pub async fn update_order_remaining_amounts(
        &self,
        order_id: &str,
        remaining_token_amount: i64,
        remaining_base_amount: i64,
    ) -> Result<()> {
        let is_completed = remaining_token_amount == 0 && remaining_base_amount == 0;

        sqlx::query!(
            r#"
            UPDATE orders SET
                remaining_token_amount = $1,
                remaining_base_amount = $2,
                is_completed = $3
            WHERE
                order_id = $4 and is_completed = false and
                remaining_token_amount >= $1 and remaining_base_amount >= $2
            "#,
            remaining_token_amount,
            remaining_base_amount,
            is_completed,
            order_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    // Chain-specific order queries
    pub async fn get_active_orders_for_wallet_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> Result<Vec<DbOrder>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM orders
            WHERE wallet_address = $1 and chain = $2 and is_cancelled = false and is_completed = false
            ORDER BY timestamp ASC
            "#,
            wallet_address,
            chain
        )
        .fetch_all(&self.pool)
        .await?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(DbOrder {
                order_id: row.order_id,
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: OrderType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                remaining_token_amount: row.remaining_token_amount,
                base_amount: row.base_amount,
                remaining_base_amount: row.remaining_base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                is_cancelled: row.is_cancelled,
                is_completed: row.is_completed,
                fee_bps: row.fee_bps,
                chain: row.chain,
            });
        }
        Ok(orders)
    }

    pub async fn get_history_orders_for_wallet_by_chain(
        &self,
        wallet_address: &str,
        chain: i16,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<DbOrder>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM orders
            WHERE wallet_address = $1 and chain = $2 and (is_cancelled = true or is_completed = true)
            ORDER BY timestamp DESC
            LIMIT $3 OFFSET $4
            "#,
            wallet_address,
            chain,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(DbOrder {
                order_id: row.order_id,
                tx_sig: row.tx_sig,
                wallet_address: row.wallet_address,
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                trade_type: OrderType::from_str(&row.trade_type)?,
                token_amount: row.token_amount,
                remaining_token_amount: row.remaining_token_amount,
                base_amount: row.base_amount,
                remaining_base_amount: row.remaining_base_amount,
                timestamp: row.timestamp,
                slot: row.slot,
                fee: row.fee,
                is_cancelled: row.is_cancelled,
                is_completed: row.is_completed,
                fee_bps: row.fee_bps,
                chain: row.chain,
            });
        }
        Ok(orders)
    }
}
