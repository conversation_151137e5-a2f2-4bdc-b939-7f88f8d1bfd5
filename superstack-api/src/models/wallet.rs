use anyhow::Result;

use serde::{Deserialize, Serialize};

use super::db::Database;

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbWallet {
    pub wallet_address: String,
    pub latest_tx_signature: Option<String>,
    pub latest_tx_slot: Option<i64>,
    pub updated_at: Option<i64>,
}

impl Database {
    pub async fn insert_or_update_wallet(&self, wallet: DbWallet) -> Result<()> {
        let current_timestamp = chrono::Utc::now().timestamp();

        sqlx::query!(
            r#"
            INSERT INTO wallets (
                wallet_address, latest_tx_signature, latest_tx_slot, updated_at
            )
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (wallet_address) DO UPDATE SET
                latest_tx_signature = $2,
                latest_tx_slot = $3,
                updated_at = $4
            WHERE wallets.latest_tx_slot is null OR wallets.latest_tx_slot < $3
            "#,
            wallet.wallet_address,
            wallet.latest_tx_signature,
            wallet.latest_tx_slot,
            current_timestamp
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_wallet(&self, wallet_address: &str) -> Result<Option<DbWallet>> {
        let wallet = sqlx::query_as!(
            DbWallet,
            r#"
            SELECT wallet_address, latest_tx_signature, latest_tx_slot, updated_at
            FROM wallets WHERE wallet_address = $1
            "#,
            wallet_address
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(wallet)
    }
}
