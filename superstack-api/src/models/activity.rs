use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use super::db::Database;

/// Identifier types for matching records across different data types
#[derive(Clone, Debug, PartialEq, Eq, Hash)]
pub enum IdentifierType {
    /// Order ID - used by PerpsOrder, OrderUpdate, UserFill
    Oid(i64),
    /// TWAP ID - used by TwapFillsHistory
    TwapId(i64),
    /// Transaction hash - used by various types
    TxHash(String),
}

/// Update result containing categorized records
#[derive(Clone, Debug)]
pub struct UpdateResult {
    /// Records that were successfully updated
    pub updated_records: Vec<DbWalletActivity>,
    /// New records that had no existing matches
    pub new_records: Vec<DbWalletActivity>,
    /// Records that were skipped due to timestamp or other rules
    pub skipped_records: Vec<DbWalletActivity>,
    /// Update statistics
    pub stats: UpdateStats,
}

/// Statistics for update operations
#[derive(<PERSON><PERSON>, Debug, Default)]
pub struct UpdateStats {
    pub updated_count: usize,
    pub new_count: usize,
    pub skipped_count: usize,
}

impl UpdateResult {
    pub fn new() -> Self {
        Self {
            updated_records: Vec::new(),
            new_records: Vec::new(),
            skipped_records: Vec::new(),
            stats: UpdateStats::default(),
        }
    }

    pub fn finalize_stats(&mut self) {
        self.stats.updated_count = self.updated_records.len();
        self.stats.new_count = self.new_records.len();
        self.stats.skipped_count = self.skipped_records.len();
    }
}

#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum ActivityType {
    SpotTrade,
    PerpTrade,
    Deposit,
    Withdraw,
    Transfer,
    Liquidation,
    Other,
}

impl std::fmt::Display for ActivityType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ActivityType::SpotTrade => write!(f, "spot_trade"),
            ActivityType::PerpTrade => write!(f, "perp_trade"),
            ActivityType::Deposit => write!(f, "deposit"),
            ActivityType::Withdraw => write!(f, "withdraw"),
            ActivityType::Transfer => write!(f, "transfer"),
            ActivityType::Liquidation => write!(f, "liquidation"),
            ActivityType::Other => write!(f, "other"),
        }
    }
}

impl std::str::FromStr for ActivityType {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "spot_trade" => Ok(ActivityType::SpotTrade),
            "perp_trade" => Ok(ActivityType::PerpTrade),
            "deposit" => Ok(ActivityType::Deposit),
            "withdraw" => Ok(ActivityType::Withdraw),
            "transfer" => Ok(ActivityType::Transfer),
            "liquidation" => Ok(ActivityType::Liquidation),
            "other" => Ok(ActivityType::Other),
            _ => anyhow::bail!("Invalid activity type: {}", s),
        }
    }
}

impl From<String> for ActivityType {
    fn from(s: String) -> Self {
        s.as_str().parse().unwrap_or(ActivityType::Other)
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbWalletActivity {
    pub wallet_address: String,
    pub tx_signature: String,

    #[serde(
        serialize_with = "serialize_activity_type",
        deserialize_with = "deserialize_activity_type"
    )]
    pub activity_type: ActivityType,

    pub token_mint: Option<String>,
    pub token_decimals: Option<i16>,
    pub token_amount: Option<i64>,

    pub base_mint: Option<String>,
    pub base_decimals: Option<i16>,
    pub base_amount: Option<i64>,

    pub usd_value: Option<f64>,

    pub timestamp: i64,
    pub block_time: Option<i64>,
    pub slot: Option<i64>,

    pub chain: i16,

    pub metadata: Option<serde_json::Value>,
    pub created_at: i64,
}

// Serialization helpers
fn serialize_activity_type<S>(
    activity_type: &ActivityType,
    serializer: S,
) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    serializer.serialize_str(&activity_type.to_string())
}

fn deserialize_activity_type<'de, D>(deserializer: D) -> Result<ActivityType, D::Error>
where
    D: serde::Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    s.parse().map_err(serde::de::Error::custom)
}

impl Database {
    pub async fn insert_wallet_activity(&self, activity: DbWalletActivity) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO wallet_activity (
                wallet_address, tx_signature, activity_type,
                token_mint, token_decimals, token_amount,
                base_mint, base_decimals, base_amount,
                usd_value, timestamp, block_time, slot, chain, metadata, created_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
            ON CONFLICT (wallet_address, tx_signature, chain) DO UPDATE SET
                activity_type = $3,
                token_mint = $4,
                token_decimals = $5,
                token_amount = $6,
                base_mint = $7,
                base_decimals = $8,
                base_amount = $9,
                usd_value = $10,
                timestamp = $11,
                block_time = $12,
                slot = $13,
                metadata = $15,
                created_at = $16
            "#,
            activity.wallet_address,
            activity.tx_signature,
            activity.activity_type.to_string(),
            activity.token_mint,
            activity.token_decimals,
            activity.token_amount,
            activity.base_mint,
            activity.base_decimals,
            activity.base_amount,
            activity.usd_value,
            activity.timestamp,
            activity.block_time,
            activity.slot,
            activity.chain,
            activity.metadata,
            activity.created_at
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Update existing wallet activity records with userFill aggregated data
    /// This method only updates existing records, does not insert new ones
    /// Updates based on oid matching, preserving original metadata and merging with userFill data
    pub async fn update_with_userfill_data(&self, activity: DbWalletActivity) -> Result<bool> {
        // Extract oid from metadata for matching
        let oid = activity.metadata.as_ref().and_then(|m| m.get("oid")).and_then(|v| v.as_i64());

        if let Some(oid) = oid {
            // First, get the existing record to preserve its metadata
            let existing_record = sqlx::query!(
                r#"
                SELECT metadata FROM wallet_activity
                WHERE wallet_address = $1
                  AND chain = $2
                  AND metadata->>'oid' = $3::text
                LIMIT 1
                "#,
                activity.wallet_address,
                activity.chain,
                oid.to_string()
            )
            .fetch_optional(&self.pool)
            .await?;

            if let Some(existing) = existing_record {
                // Merge metadata: preserve original metadata, add userFill aggregated data
                let mut merged_metadata =
                    existing.metadata.unwrap_or_else(|| serde_json::json!({}));
                let userfill_metadata = activity.metadata.unwrap_or_else(|| serde_json::json!({}));

                // Add userFill aggregated data to existing metadata
                if let (Some(merged_obj), Some(userfill_obj)) =
                    (merged_metadata.as_object_mut(), userfill_metadata.as_object())
                {
                    // Preserve original order information (leverage, limit_price, etc.)
                    // Update with userFill aggregated execution data
                    merged_obj.insert(
                        "avg_price".to_string(),
                        userfill_obj.get("avg_price").unwrap_or(&serde_json::Value::Null).clone(),
                    );
                    merged_obj.insert(
                        "total_size".to_string(),
                        userfill_obj.get("total_size").unwrap_or(&serde_json::Value::Null).clone(),
                    );
                    merged_obj.insert(
                        "total_fees".to_string(),
                        userfill_obj.get("total_fees").unwrap_or(&serde_json::Value::Null).clone(),
                    );
                    merged_obj.insert(
                        "fill_count".to_string(),
                        userfill_obj.get("fill_count").unwrap_or(&serde_json::Value::Null).clone(),
                    );
                    merged_obj.insert(
                        "order_type".to_string(),
                        userfill_obj.get("order_type").unwrap_or(&serde_json::Value::Null).clone(),
                    );
                    merged_obj.insert(
                        "completion_time".to_string(),
                        userfill_obj
                            .get("completion_time")
                            .unwrap_or(&serde_json::Value::Null)
                            .clone(),
                    );
                    merged_obj.insert(
                        "fills".to_string(),
                        userfill_obj.get("fills").unwrap_or(&serde_json::Value::Null).clone(),
                    );
                    merged_obj.insert(
                        "source".to_string(),
                        serde_json::Value::String("userfill_aggregated".to_string()),
                    );
                    merged_obj
                        .insert("updated_with_userfill".to_string(), serde_json::Value::Bool(true));
                }

                // Update the record with merged metadata
                let updated_rows = sqlx::query!(
                    r#"
                    UPDATE wallet_activity SET
                        activity_type = $2,
                        token_mint = $3,
                        token_decimals = $4,
                        token_amount = $5,
                        base_mint = $6,
                        base_decimals = $7,
                        base_amount = $8,
                        usd_value = $9,
                        timestamp = $10,
                        block_time = $11,
                        slot = $12,
                        metadata = $13
                    WHERE wallet_address = $1
                      AND chain = $14
                      AND metadata->>'oid' = $15::text
                    "#,
                    activity.wallet_address,
                    activity.activity_type.to_string(),
                    activity.token_mint,
                    activity.token_decimals,
                    activity.token_amount,
                    activity.base_mint,
                    activity.base_decimals,
                    activity.base_amount,
                    activity.usd_value,
                    activity.timestamp,
                    activity.block_time,
                    activity.slot,
                    merged_metadata,
                    activity.chain,
                    oid.to_string()
                )
                .execute(&self.pool)
                .await?;

                let updated = updated_rows.rows_affected() > 0;
                if updated {
                    tracing::info!(
                        "Updated {} existing record(s) with userFill aggregated data for oid: {}, preserved original metadata",
                        updated_rows.rows_affected(),
                        oid
                    );
                }
                Ok(updated)
            } else {
                tracing::debug!(
                    "No existing records found for oid: {} to update with userFill data",
                    oid
                );
                Ok(false)
            }
        } else {
            tracing::warn!("No oid found in userFill aggregated data metadata");
            Ok(false)
        }
    }

    pub async fn get_wallet_activities_with_timestamp(
        &self,
        wallet_address: &str,
        limit: i64,
        before_timestamp: Option<i64>,
        include_other: bool,
        is_mainnet: Option<bool>,
    ) -> Result<Vec<DbWalletActivity>> {
        // Validate limit to prevent excessive queries, but allow unlimited for all=true case
        let limit = if limit == i64::MAX {
            limit // Don't limit when requesting all data
        } else {
            limit.max(1) // Ensure at least 1, but no upper limit
        };

        // Use a single SQL query with conditional timestamp and mainnet filters
        let rows = sqlx::query!(
            r#"
            SELECT wallet_address, tx_signature, activity_type,
                   token_mint, token_decimals, token_amount,
                   base_mint, base_decimals, base_amount,
                   usd_value, timestamp, block_time, slot, chain, metadata, created_at
            FROM wallet_activity
            WHERE wallet_address = $1
              AND ($2::bigint IS NULL OR timestamp < $2)
              AND ($4 OR activity_type != 'other')
              AND (
                -- Include all activity types except 'other' unless explicitly requested
                activity_type IN ('spot_trade', 'perp_trade', 'deposit', 'withdraw', 'transfer', 'liquidation')
                OR (metadata IS NOT NULL AND metadata != '{}')
              )
              AND (
                -- Mainnet filter: if is_mainnet is specified, filter HyperCore activities by metadata
                $5::boolean IS NULL
                OR chain != 1  -- Not HyperCore chain
                OR (
                  metadata IS NOT NULL
                  AND (metadata->>'is_hyperliquid_mainnet')::boolean = $5
                )
              )
            ORDER BY timestamp DESC, tx_signature DESC
            LIMIT $3
            "#,
            wallet_address,
            before_timestamp,
            limit,
            include_other,
            is_mainnet
        )
        .fetch_all(&self.pool)
        .await?;

        let activities: Vec<DbWalletActivity> = rows
            .into_iter()
            .map(|row| DbWalletActivity {
                wallet_address: row.wallet_address,
                tx_signature: row.tx_signature,
                activity_type: row.activity_type.parse().unwrap_or_else(|e| {
                    tracing::warn!("Failed to parse activity type '{}': {}", row.activity_type, e);
                    ActivityType::Other
                }),
                token_mint: row.token_mint,
                token_decimals: row.token_decimals,
                token_amount: row.token_amount,
                base_mint: row.base_mint,
                base_decimals: row.base_decimals,
                base_amount: row.base_amount,
                usd_value: row.usd_value,
                timestamp: row.timestamp,
                block_time: row.block_time,
                slot: row.slot,
                chain: row.chain,
                metadata: row.metadata,
                created_at: row.created_at,
            })
            .collect();

        Ok(activities)
    }

    pub async fn get_latest_wallet_activity_timestamp(
        &self,
        wallet_address: &str,
        chain: i16,
    ) -> Result<Option<i64>> {
        let row = sqlx::query!(
            r#"
            SELECT timestamp FROM wallet_activity
            WHERE wallet_address = $1 AND chain = $2
            ORDER BY timestamp DESC
            LIMIT 1
            "#,
            wallet_address,
            chain
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row.map(|r| r.timestamp))
    }

    pub async fn batch_insert_wallet_activities(
        &self,
        activities: Vec<DbWalletActivity>,
    ) -> Result<()> {
        if activities.is_empty() {
            return Ok(());
        }

        // Process in smaller batches to avoid memory issues
        const BATCH_SIZE: usize = 100;

        for chunk in activities.chunks(BATCH_SIZE) {
            let mut tx = self.pool.begin().await?;

            for activity in chunk {
                sqlx::query!(
                    r#"
                    INSERT INTO wallet_activity (
                        wallet_address, tx_signature, activity_type,
                        token_mint, token_decimals, token_amount,
                        base_mint, base_decimals, base_amount,
                        usd_value, timestamp, block_time, slot, chain, metadata, created_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                    ON CONFLICT (wallet_address, tx_signature, chain) DO UPDATE SET
                        activity_type = $3,
                        token_mint = $4,
                        token_decimals = $5,
                        token_amount = $6,
                        base_mint = $7,
                        base_decimals = $8,
                        base_amount = $9,
                        usd_value = $10,
                        timestamp = $11,
                        block_time = $12,
                        slot = $13,
                        metadata = $15,
                        created_at = $16
                    "#,
                    activity.wallet_address,
                    activity.tx_signature,
                    activity.activity_type.to_string(),
                    activity.token_mint,
                    activity.token_decimals,
                    activity.token_amount,
                    activity.base_mint,
                    activity.base_decimals,
                    activity.base_amount,
                    activity.usd_value,
                    activity.timestamp,
                    activity.block_time,
                    activity.slot,
                    activity.chain,
                    activity.metadata,
                    activity.created_at
                )
                .execute(&mut *tx)
                .await?;
            }

            tx.commit().await?;
        }

        Ok(())
    }

    pub async fn get_wallet_activity_stats(
        &self,
        wallet_address: &str,
    ) -> Result<(i64, i64, i64, i64, i64, i64, f64, Option<i64>, Option<i64>)> {
        let row = sqlx::query!(
            r#"
            SELECT
                COUNT(*) as total_activities,
                COUNT(CASE WHEN activity_type = 'spot_trade' THEN 1 END) as swap_count,
                COUNT(CASE WHEN activity_type IN ('spot_trade', 'perp_trade') THEN 1 END) as limit_order_count,
                COUNT(CASE WHEN activity_type = 'deposit' THEN 1 END) as receive_count,
                COUNT(CASE WHEN activity_type = 'withdraw' THEN 1 END) as send_count,
                COUNT(CASE WHEN activity_type = 'transfer' THEN 1 END) as transfer_count,
                COUNT(CASE WHEN activity_type IN ('other', 'liquidation') THEN 1 END) as other_count,
                COALESCE(SUM(usd_value), 0.0) as total_usd_volume,
                MIN(timestamp) as first_activity_timestamp,
                MAX(timestamp) as last_activity_timestamp
            FROM wallet_activity
            WHERE wallet_address = $1
            "#,
            wallet_address
        )
        .fetch_one(&self.pool)
        .await?;

        Ok((
            row.total_activities.unwrap_or(0),
            row.swap_count.unwrap_or(0),
            row.limit_order_count.unwrap_or(0),
            row.receive_count.unwrap_or(0),
            row.send_count.unwrap_or(0),
            row.transfer_count.unwrap_or(0), // Keep transfer_count separate from other_count
            row.total_usd_volume.unwrap_or(0.0),
            row.first_activity_timestamp,
            row.last_activity_timestamp,
        ))
    }

    /// Universal update framework for Hyperliquid data types
    /// Supports cross-type updates based on multiple identifiers with data-source-specific behavior
    pub async fn update_with_universal_framework(
        &self,
        activities: Vec<DbWalletActivity>,
    ) -> Result<UpdateResult> {
        let mut update_result = UpdateResult::new();

        for activity in activities {
            let identifiers = self.extract_identifiers(&activity);
            if identifiers.is_empty() {
                tracing::warn!("No identifiers found for activity: {}", activity.tx_signature);
                continue;
            }

            // Get data source type for behavior determination
            let data_source = activity
                .metadata
                .as_ref()
                .and_then(|m| m.get("source"))
                .and_then(|v| v.as_str())
                .unwrap_or("unknown");

            // Find existing records that match any of the identifiers
            let existing_records = self
                .find_matching_records(&activity.wallet_address, activity.chain, &identifiers)
                .await?;

            // Apply data-source-specific behavior
            match data_source {
                "userfill_aggregated" => {
                    // UserFill aggregated data: ONLY UPDATE, never create
                    if existing_records.is_empty() {
                        tracing::warn!(
                            "UserFill aggregated data found no existing record for oid: {:?}, skipping (this may indicate data sync issues)",
                            activity.metadata.as_ref().and_then(|m| m.get("oid"))
                        );
                        update_result.skipped_records.push(activity);
                        continue;
                    }

                    // Update all matching records (UserFill has highest priority)
                    for existing in existing_records {
                        let merged_activity = self.merge_activities(&existing, &activity)?;
                        update_result.updated_records.push(merged_activity);
                        tracing::debug!(
                            "Updated existing record with UserFill data: {}",
                            existing.tx_signature
                        );
                    }
                }
                "order_update" => {
                    // OrderUpdate: can create or update
                    if existing_records.is_empty() {
                        // No existing record, create new one
                        let tx_signature = activity.tx_signature.clone();
                        update_result.new_records.push(activity);
                        tracing::debug!("Created new record from OrderUpdate: {}", tx_signature);
                    } else {
                        // Update existing records if priority allows
                        let mut updated_any = false;
                        for existing in existing_records {
                            let should_update = self.should_update_record(&existing, &activity)?;
                            if should_update {
                                let merged_activity =
                                    self.merge_activities(&existing, &activity)?;
                                update_result.updated_records.push(merged_activity);
                                updated_any = true;
                                tracing::debug!(
                                    "Updated existing record with OrderUpdate: {}",
                                    existing.tx_signature
                                );
                            }
                        }
                        if !updated_any {
                            update_result.skipped_records.push(activity);
                        }
                    }
                }
                "perps_order" => {
                    // PerpsOrder: try to update existing records first, create only if no match
                    if existing_records.is_empty() {
                        // No existing record, create new one
                        let tx_signature = activity.tx_signature.clone();
                        update_result.new_records.push(activity);
                        tracing::debug!("Created new record from PerpsOrder: {}", tx_signature);
                    } else {
                        // Try to update existing records instead of creating duplicates
                        let mut updated_any = false;

                        for existing in existing_records {
                            let existing_source = existing
                                .metadata
                                .as_ref()
                                .and_then(|m| m.get("source"))
                                .and_then(|v| v.as_str())
                                .unwrap_or("unknown");

                            // Check if existing record is from higher priority source
                            if matches!(
                                existing_source,
                                "userfill_aggregated" | "nonfunding_ledger"
                            ) {
                                tracing::debug!(
                                    "Skipping PerpsOrder update - higher priority record exists: {} (source: {})",
                                    existing.tx_signature, existing_source
                                );
                                continue;
                            }

                            // Check if existing record is also from perps_order
                            // Allow update if new data is more complete
                            if existing_source == "perps_order" {
                                let existing_completeness =
                                    self.calculate_data_completeness(&existing);
                                let new_completeness = self.calculate_data_completeness(&activity);

                                if new_completeness <= existing_completeness {
                                    tracing::debug!(
                                        "Skipping PerpsOrder update: new data not more complete ({} vs {}) for {}",
                                        new_completeness, existing_completeness, existing.tx_signature
                                    );
                                    continue;
                                } else {
                                    tracing::debug!(
                                        "Allowing PerpsOrder update: new data more complete ({} vs {}) for {}",
                                        new_completeness, existing_completeness, existing.tx_signature
                                    );
                                }
                            }

                            // For other sources (like order_update), try to update
                            let should_update = self.should_update_record(&existing, &activity)?;
                            if should_update {
                                // Special handling: preserve real transaction hashes
                                let existing_tx_sig = &existing.tx_signature;
                                let new_tx_sig = &activity.tx_signature;

                                if Self::is_real_transaction_hash(existing_tx_sig) &&
                                    Self::is_synthetic_hash(new_tx_sig)
                                {
                                    tracing::info!(
                                        "PerpsOrder update: Preserving real transaction hash '{}', not overwriting with synthetic hash '{}' for oid {:?}",
                                        existing_tx_sig, new_tx_sig,
                                        activity.metadata.as_ref().and_then(|m| m.get("oid"))
                                    );
                                    // Create a modified activity that preserves the real
                                    // tx_signature
                                    let mut preserved_activity = activity.clone();
                                    preserved_activity.tx_signature = existing_tx_sig.clone();
                                    let merged_activity =
                                        self.merge_activities(&existing, &preserved_activity)?;
                                    update_result.updated_records.push(merged_activity);
                                } else {
                                    // Normal merge
                                    let merged_activity =
                                        self.merge_activities(&existing, &activity)?;
                                    update_result.updated_records.push(merged_activity);
                                }

                                updated_any = true;
                                tracing::debug!(
                                    "Updated existing record with PerpsOrder: {} -> {}",
                                    existing.tx_signature,
                                    activity.tx_signature
                                );
                            }
                        }

                        if !updated_any {
                            tracing::debug!(
                                "No suitable records to update with PerpsOrder, skipping: {}",
                                activity.tx_signature
                            );
                            update_result.skipped_records.push(activity);
                        }
                    }
                }
                "twap_fills" => {
                    // TwapFills: always create (independent oids)
                    let tx_signature = activity.tx_signature.clone();
                    update_result.new_records.push(activity);
                    tracing::debug!("Created new record from TwapFills: {}", tx_signature);
                }
                "nonfunding_ledger" => {
                    // NonFundingLedgerUpdate: can create or update (system authority)
                    if existing_records.is_empty() {
                        // No existing record, create new one
                        let tx_signature = activity.tx_signature.clone();
                        update_result.new_records.push(activity);
                        tracing::debug!(
                            "Created new record from NonFundingLedgerUpdate: {}",
                            tx_signature
                        );
                    } else {
                        // Update existing records if priority allows (system data has high
                        // authority)
                        let mut updated_any = false;
                        for existing in existing_records {
                            let should_update = self.should_update_record(&existing, &activity)?;
                            if should_update {
                                let merged_activity =
                                    self.merge_activities(&existing, &activity)?;
                                update_result.updated_records.push(merged_activity);
                                updated_any = true;
                                tracing::debug!(
                                    "Updated existing record with NonFundingLedgerUpdate: {}",
                                    existing.tx_signature
                                );
                            }
                        }
                        if !updated_any {
                            update_result.skipped_records.push(activity);
                        }
                    }
                }
                "deposit" => {
                    // Deposit: mainly create, can be updated by nonfunding_ledger
                    if existing_records.is_empty() {
                        // No existing record, create new one
                        let tx_signature = activity.tx_signature.clone();
                        update_result.new_records.push(activity);
                        tracing::debug!("Created new record from Deposit: {}", tx_signature);
                    } else {
                        // Check if existing record is from higher authority source
                        let existing_has_higher_authority =
                            existing_records.iter().any(|existing| {
                                existing
                                    .metadata
                                    .as_ref()
                                    .and_then(|m| m.get("source"))
                                    .and_then(|v| v.as_str()) ==
                                    Some("nonfunding_ledger")
                            });

                        if existing_has_higher_authority {
                            tracing::debug!(
                                "Skipping Deposit - higher authority record exists: {}",
                                activity.tx_signature
                            );
                            update_result.skipped_records.push(activity);
                        } else {
                            // No higher authority record, create new one
                            let tx_signature = activity.tx_signature.clone();
                            update_result.new_records.push(activity);
                            tracing::debug!(
                                "Created new record from Deposit (no higher authority): {}",
                                tx_signature
                            );
                        }
                    }
                }
                "withdraw" => {
                    // Withdraw: mainly create, can be updated by nonfunding_ledger
                    if existing_records.is_empty() {
                        // No existing record, create new one
                        let tx_signature = activity.tx_signature.clone();
                        update_result.new_records.push(activity);
                        tracing::debug!("Created new record from Withdraw: {}", tx_signature);
                    } else {
                        // Check if existing record is from higher authority source
                        let existing_has_higher_authority =
                            existing_records.iter().any(|existing| {
                                existing
                                    .metadata
                                    .as_ref()
                                    .and_then(|m| m.get("source"))
                                    .and_then(|v| v.as_str()) ==
                                    Some("nonfunding_ledger")
                            });

                        if existing_has_higher_authority {
                            tracing::debug!(
                                "Skipping Withdraw - higher authority record exists: {}",
                                activity.tx_signature
                            );
                            update_result.skipped_records.push(activity);
                        } else {
                            // No higher authority record, create new one
                            let tx_signature = activity.tx_signature.clone();
                            update_result.new_records.push(activity);
                            tracing::debug!(
                                "Created new record from Withdraw (no higher authority): {}",
                                tx_signature
                            );
                        }
                    }
                }
                _ => {
                    // Unknown source: default behavior (create if no existing, otherwise skip)
                    if existing_records.is_empty() {
                        let tx_signature = activity.tx_signature.clone();
                        update_result.new_records.push(activity);
                        tracing::debug!("Created new record from unknown source: {}", tx_signature);
                    } else {
                        let tx_signature = activity.tx_signature.clone();
                        update_result.skipped_records.push(activity);
                        tracing::debug!("Skipped unknown source activity: {}", tx_signature);
                    }
                }
            }
        }

        // Update statistics
        update_result.stats.updated_count = update_result.updated_records.len();
        update_result.stats.new_count = update_result.new_records.len();
        update_result.stats.skipped_count = update_result.skipped_records.len();

        Ok(update_result)
    }

    /// Extract identifiers from a wallet activity record
    fn extract_identifiers(&self, activity: &DbWalletActivity) -> Vec<IdentifierType> {
        let mut identifiers = Vec::new();

        if let Some(metadata) = &activity.metadata {
            // Extract oid if present (primary identifier for userFills)
            if let Some(oid) = metadata.get("oid").and_then(|v| v.as_i64()) {
                identifiers.push(IdentifierType::Oid(oid));
                tracing::debug!("Extracted oid identifier: {}", oid);
            }

            // Extract order_id if present (used by order_update records)
            if let Some(order_id) = metadata.get("order_id").and_then(|v| v.as_i64()) {
                identifiers.push(IdentifierType::Oid(order_id));
                tracing::debug!("Extracted order_id identifier: {}", order_id);
            }

            // Extract twapId if present
            if let Some(twap_id) = metadata.get("twap_id").and_then(|v| v.as_i64()) {
                identifiers.push(IdentifierType::TwapId(twap_id));
                tracing::debug!("Extracted twap_id identifier: {}", twap_id);
            }
        }

        // Always include tx_signature as a potential identifier
        identifiers.push(IdentifierType::TxHash(activity.tx_signature.clone()));
        tracing::debug!("Extracted tx_signature identifier: {}", activity.tx_signature);

        identifiers
    }

    /// Find existing records that match any of the given identifiers
    async fn find_matching_records(
        &self,
        wallet_address: &str,
        chain: i16,
        identifiers: &[IdentifierType],
    ) -> Result<Vec<DbWalletActivity>> {
        let mut conditions = Vec::new();
        let mut params: Vec<Box<dyn sqlx::Encode<'_, sqlx::Postgres> + Send + Sync>> = Vec::new();
        let mut param_index = 3; // Start after wallet_address and chain

        // Build dynamic WHERE conditions based on identifiers
        for identifier in identifiers {
            match identifier {
                IdentifierType::Oid(oid) => {
                    // Check both 'oid' and 'order_id' fields for maximum compatibility
                    conditions.push(format!(
                        "(metadata->>'oid' = ${} OR metadata->>'order_id' = ${})",
                        param_index,
                        param_index + 1
                    ));
                    params.push(Box::new(oid.to_string()));
                    params.push(Box::new(oid.to_string()));
                    param_index += 2;
                }
                IdentifierType::TwapId(twap_id) => {
                    conditions.push(format!("metadata->>'twap_id' = ${}", param_index));
                    params.push(Box::new(twap_id.to_string()));
                    param_index += 1;
                }
                IdentifierType::TxHash(hash) => {
                    conditions.push(format!("tx_signature = ${}", param_index));
                    params.push(Box::new(hash.clone()));
                    param_index += 1;
                }
            }
        }

        if conditions.is_empty() {
            return Ok(Vec::new());
        }

        let where_clause = conditions.join(" OR ");
        let query = format!(
            r#"
            SELECT wallet_address, tx_signature, activity_type,
                   token_mint, token_decimals, token_amount,
                   base_mint, base_decimals, base_amount,
                   usd_value, timestamp, block_time, slot, chain, metadata, created_at
            FROM wallet_activity
            WHERE wallet_address = $1 AND chain = $2 AND ({})
            ORDER BY timestamp DESC
            "#,
            where_clause
        );

        // Note: This is a simplified version. In practice, you'd need to use sqlx::query_as
        // with proper parameter binding. For now, let's implement a basic version.
        self.find_records_by_identifiers(wallet_address, chain, identifiers).await
    }

    /// Helper method to find records by identifiers (simplified implementation)
    async fn find_records_by_identifiers(
        &self,
        wallet_address: &str,
        chain: i16,
        identifiers: &[IdentifierType],
    ) -> Result<Vec<DbWalletActivity>> {
        let mut all_records = Vec::new();

        for identifier in identifiers {
            match identifier {
                IdentifierType::Oid(oid) => {
                    // Query both 'oid' and 'order_id' fields for maximum compatibility
                    let records = sqlx::query_as!(
                        DbWalletActivity,
                        r#"
                        SELECT wallet_address, tx_signature, activity_type,
                               token_mint, token_decimals, token_amount,
                               base_mint, base_decimals, base_amount,
                               usd_value, timestamp, block_time, slot, chain, metadata, created_at
                        FROM wallet_activity
                        WHERE wallet_address = $1 AND chain = $2
                          AND (metadata->>'oid' = $3::text OR metadata->>'order_id' = $3::text)
                        "#,
                        wallet_address,
                        chain,
                        oid.to_string()
                    )
                    .fetch_all(&self.pool)
                    .await?;
                    all_records.extend(records);
                }
                IdentifierType::TwapId(twap_id) => {
                    let records = sqlx::query_as!(
                        DbWalletActivity,
                        r#"
                        SELECT wallet_address, tx_signature, activity_type,
                               token_mint, token_decimals, token_amount,
                               base_mint, base_decimals, base_amount,
                               usd_value, timestamp, block_time, slot, chain, metadata, created_at
                        FROM wallet_activity
                        WHERE wallet_address = $1 AND chain = $2 AND metadata->>'twap_id' = $3::text
                        "#,
                        wallet_address,
                        chain,
                        twap_id.to_string()
                    )
                    .fetch_all(&self.pool)
                    .await?;
                    all_records.extend(records);
                }
                IdentifierType::TxHash(hash) => {
                    let records = sqlx::query_as!(
                        DbWalletActivity,
                        r#"
                        SELECT wallet_address, tx_signature, activity_type,
                               token_mint, token_decimals, token_amount,
                               base_mint, base_decimals, base_amount,
                               usd_value, timestamp, block_time, slot, chain, metadata, created_at
                        FROM wallet_activity
                        WHERE wallet_address = $1 AND chain = $2 AND tx_signature = $3
                        "#,
                        wallet_address,
                        chain,
                        hash
                    )
                    .fetch_all(&self.pool)
                    .await?;
                    all_records.extend(records);
                }
            }
        }

        // Remove duplicates based on tx_signature
        let mut unique_records = Vec::new();
        let mut seen_signatures = std::collections::HashSet::new();

        for record in all_records {
            if seen_signatures.insert(record.tx_signature.clone()) {
                unique_records.push(record);
            }
        }

        Ok(unique_records)
    }

    /// Determine if a record should be updated based on data source priority and business rules
    /// Implements redesigned priority rules for cross-type updates
    fn should_update_record(
        &self,
        existing: &DbWalletActivity,
        new: &DbWalletActivity,
    ) -> Result<bool> {
        let existing_source = existing
            .metadata
            .as_ref()
            .and_then(|m| m.get("source"))
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");

        let new_source = new
            .metadata
            .as_ref()
            .and_then(|m| m.get("source"))
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");

        // Check if both records represent the same order (by oid)
        let existing_oid =
            existing.metadata.as_ref().and_then(|m| m.get("oid")).and_then(|o| o.as_i64());

        let new_oid = new.metadata.as_ref().and_then(|m| m.get("oid")).and_then(|o| o.as_i64());

        tracing::debug!(
            "Evaluating update: {} -> {} (existing: {}, new: {}, oid: {:?} -> {:?})",
            existing_source,
            new_source,
            existing.tx_signature,
            new.tx_signature,
            existing_oid,
            new_oid
        );

        // Special rule: If both records have the same oid, prioritize real transaction hash
        if let (Some(existing_oid), Some(new_oid)) = (existing_oid, new_oid) {
            if existing_oid == new_oid {
                // Check if new record has a real transaction hash (starts with 0x)
                let new_has_real_hash =
                    new.tx_signature.starts_with("0x") && new.tx_signature.len() == 66;
                let existing_has_real_hash =
                    existing.tx_signature.starts_with("0x") && existing.tx_signature.len() == 66;

                if new_has_real_hash && !existing_has_real_hash {
                    tracing::debug!(
                        "Allowing update: same oid {}, new record has real transaction hash",
                        new_oid
                    );
                    return Ok(true);
                }

                if existing_has_real_hash && !new_has_real_hash {
                    tracing::debug!(
                        "Blocking update: same oid {}, existing record already has real transaction hash",
                        existing_oid
                    );
                    return Ok(false);
                }
            }
        }

        // Rule 1: NonFundingLedgerUpdate has highest system authority
        if new_source == "nonfunding_ledger" {
            // NonFundingLedgerUpdate can update most other sources except UserFill
            if existing_source != "userfill_aggregated" {
                tracing::debug!(
                    "Allowing NonFundingLedgerUpdate to update {} (system authority)",
                    existing_source
                );
                return Ok(true);
            }
        }

        // Rule 2: Never update NonFundingLedgerUpdate with lower authority sources
        if existing_source == "nonfunding_ledger" {
            if !["userfill_aggregated"].contains(&new_source) {
                tracing::debug!(
                    "Blocking update of NonFundingLedgerUpdate with {} (lower authority)",
                    new_source
                );
                return Ok(false);
            }
        }

        // Rule 3: UserFill aggregated data always updates (handled in framework, but double-check)
        if new_source == "userfill_aggregated" {
            tracing::debug!("Allowing update from userFill aggregated data (highest priority)");
            return Ok(true);
        }

        // Rule 4: Never update UserFill aggregated data with anything else
        if existing_source == "userfill_aggregated" {
            tracing::debug!("Blocking update of userFill aggregated data (immutable)");
            return Ok(false);
        }

        // Rule 5: Withdraw can update Deposit (user intent vs system record)
        if new_source == "withdraw" && existing_source == "deposit" {
            tracing::debug!("Allowing Withdraw to update Deposit");
            return Ok(true);
        }

        // Rule 6: Deposit should not update Withdraw (wrong direction)
        if new_source == "deposit" && existing_source == "withdraw" {
            tracing::debug!("Blocking Deposit from updating Withdraw (wrong priority)");
            return Ok(false);
        }

        // Rule 7: OrderUpdate can update PerpsOrder
        if new_source == "order_update" && existing_source == "perps_order" {
            tracing::debug!("Allowing OrderUpdate to update PerpsOrder");
            return Ok(true);
        }

        // Rule 8: PerpsOrder can update other PerpsOrder if data is more complete
        if new_source == "perps_order" && existing_source == "perps_order" {
            let existing_completeness = self.calculate_data_completeness(existing);
            let new_completeness = self.calculate_data_completeness(new);

            if new_completeness > existing_completeness {
                tracing::debug!(
                    "Allowing PerpsOrder update: new data more complete ({} vs {})",
                    new_completeness,
                    existing_completeness
                );
                return Ok(true);
            } else {
                tracing::debug!(
                    "Blocking PerpsOrder update: new data not more complete ({} vs {})",
                    new_completeness,
                    existing_completeness
                );
                return Ok(false);
            }
        }

        // Rule 9: PerpsOrder should not update OrderUpdate (wrong direction)
        if new_source == "perps_order" && existing_source == "order_update" {
            tracing::debug!("Blocking PerpsOrder from updating OrderUpdate (wrong priority)");
            return Ok(false);
        }

        // Rule 6: Data type priority hierarchy (fallback)
        let existing_priority = self.get_data_type_priority(
            &existing.activity_type.to_string(),
            existing.metadata.as_ref(),
        );
        let new_priority =
            self.get_data_type_priority(&new.activity_type.to_string(), new.metadata.as_ref());

        if new_priority > existing_priority + 10 {
            // Require significant priority difference
            tracing::debug!(
                "Allowing update due to significantly higher priority: {} > {}",
                new_priority,
                existing_priority
            );
            return Ok(true);
        }

        // Rule 7: Timestamp priority with tolerance (for same priority level)
        if existing_priority == new_priority {
            let time_diff = new.timestamp - existing.timestamp;
            if time_diff > 10 {
                // 10 second tolerance for same-priority updates
                tracing::debug!(
                    "Allowing update due to newer timestamp at same priority: {} vs {}",
                    new.timestamp,
                    existing.timestamp
                );
                return Ok(true);
            }
        }

        // Rule 8: Check if existing record is marked as preliminary/incomplete
        if let Some(existing_metadata) = &existing.metadata {
            if existing_metadata.get("is_preliminary").and_then(|v| v.as_bool()).unwrap_or(false) {
                tracing::debug!("Allowing update of preliminary record");
                return Ok(true);
            }
        }

        // Default: don't update
        tracing::debug!(
            "Skipping update - no priority rules matched (existing: {}, new: {})",
            existing_source,
            new_source
        );
        Ok(false)
    }

    /// Get priority score for different data types and sources
    /// Higher scores indicate higher priority for updates
    fn get_data_type_priority(
        &self,
        activity_type: &str,
        metadata: Option<&serde_json::Value>,
    ) -> u32 {
        let base_priority = match activity_type {
            "perp_trade" => 100,
            "spot_trade" => 90,
            "transfer" => 80,
            "deposit" => 70,
            "withdraw" => 70,
            _ => 50,
        };

        let mut priority = base_priority;

        if let Some(metadata) = metadata {
            // Boost priority based on data source (complete hierarchy)
            match metadata.get("source").and_then(|v| v.as_str()) {
                Some("nonfunding_ledger") => priority += 120, // System authority (220 total)
                Some("userfill_aggregated") => priority += 100, // Execution data (200 total)
                Some("withdraw") => priority += 70,           // User withdrawal intent (170 total)
                Some("deposit") => priority += 60,            // User deposit intent (160 total)
                Some("order_update") => priority += 50,       // Order status (150 total)
                Some("perps_order") => priority += 0,         // Base trading intent (100 total)
                Some("twap_fills") => priority += 0,          // Independent processing (100 total)
                _ => {}
            }

            // Additional boost for records with execution data
            if metadata.get("fills").is_some() {
                priority += 15;
            }
            if metadata.get("avg_price").is_some() {
                priority += 10;
            }
            if metadata.get("total_fees").is_some() {
                priority += 5;
            }

            // Boost for complete order information
            if metadata.get("leverage").is_some() {
                priority += 5;
            }
            if metadata.get("order_parameters").is_some() {
                priority += 5;
            }
        }

        priority
    }

    /// Calculate data completeness score for prioritizing updates
    fn calculate_data_completeness(&self, activity: &DbWalletActivity) -> u32 {
        let mut score = 0u32;

        // Basic fields
        if activity.token_mint.is_some() {
            score += 1;
        }
        if activity.token_amount.is_some() {
            score += 1;
        }
        if activity.usd_value.is_some() {
            score += 1;
        }
        if activity.block_time.is_some() {
            score += 1;
        }

        // Metadata richness
        if let Some(metadata) = &activity.metadata {
            score += metadata.as_object().map(|obj| obj.len() as u32).unwrap_or(0);

            // Bonus for specific high-value fields
            if metadata.get("avg_price").is_some() {
                score += 5;
            }
            if metadata.get("fills").is_some() {
                score += 5;
            }
            if metadata.get("leverage").is_some() {
                score += 3;
            }
            if metadata.get("order_parameters").is_some() {
                score += 3;
            }
        }

        score
    }

    /// Merge two activities with intelligent metadata combining
    fn merge_activities(
        &self,
        existing: &DbWalletActivity,
        new: &DbWalletActivity,
    ) -> Result<DbWalletActivity> {
        // Start with existing record to preserve non-null values
        let mut merged = existing.clone();

        // Merge basic fields: only update if new value is non-null
        if new.token_mint.is_some() {
            merged.token_mint = new.token_mint.clone();
        }
        if new.token_decimals.is_some() {
            merged.token_decimals = new.token_decimals;
        }
        if new.token_amount.is_some() {
            merged.token_amount = new.token_amount;
        }
        if new.base_mint.is_some() {
            merged.base_mint = new.base_mint.clone();
        }
        if new.base_decimals.is_some() {
            merged.base_decimals = new.base_decimals;
        }
        if new.base_amount.is_some() {
            merged.base_amount = new.base_amount;
        }
        if new.usd_value.is_some() {
            merged.usd_value = new.usd_value;
        }
        if new.block_time.is_some() {
            merged.block_time = new.block_time;
        }
        if new.slot.is_some() {
            merged.slot = new.slot;
        }

        // Always update timestamp to the newer value
        merged.timestamp = new.timestamp;

        // Get data sources for authority-based merging
        let existing_source = existing
            .metadata
            .as_ref()
            .and_then(|m| m.get("source"))
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");
        let new_source = new
            .metadata
            .as_ref()
            .and_then(|m| m.get("source"))
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");

        // Merge activity_type based on authority
        let authority_map = self.get_field_authority();
        if let Some(activity_type_authorities) = authority_map.get("activity_type") {
            let existing_pos = activity_type_authorities.iter().position(|&s| s == existing_source);
            let new_pos = activity_type_authorities.iter().position(|&s| s == new_source);

            match (existing_pos, new_pos) {
                (Some(existing_idx), Some(new_idx)) => {
                    if new_idx < existing_idx {
                        // New source is more authoritative, keep new activity_type
                        tracing::trace!(
                            "Using new activity_type {:?} from {} (more authoritative than {})",
                            new.activity_type,
                            new_source,
                            existing_source
                        );
                    } else {
                        // Existing source is more authoritative, use existing activity_type
                        tracing::trace!("Keeping existing activity_type {:?} from {} (more authoritative than {})",
                            existing.activity_type, existing_source, new_source);
                        merged.activity_type = existing.activity_type.clone();
                    }
                }
                (Some(_), None) => {
                    // Existing source has authority, new doesn't
                    tracing::trace!("Using existing activity_type {:?} (existing source {} has authority, new source {} doesn't)",
                        existing.activity_type, existing_source, new_source);
                    merged.activity_type = existing.activity_type.clone();
                }
                (None, Some(_)) => {
                    // New source has authority, existing doesn't - keep new (already set)
                    tracing::trace!("Using new activity_type {:?} (new source {} has authority, existing source {} doesn't)",
                        new.activity_type, new_source, existing_source);
                }
                (None, None) => {
                    // Neither source has specific authority, keep new (default behavior)
                    tracing::trace!(
                        "Using new activity_type {:?} (neither source has specific authority)",
                        new.activity_type
                    );
                }
            }

            // Keep debug level for final merge result (important for monitoring)
            if merged.activity_type != new.activity_type {
                tracing::debug!(
                    "Activity merge: changed activity_type from {:?} to {:?} (sources: {} -> {})",
                    new.activity_type,
                    merged.activity_type,
                    new_source,
                    existing_source
                );
            }
        }

        // Intelligent tx_signature merging with hash type protection
        if let Some(new_metadata) = new.metadata.as_ref() {
            if let Some(source) = new_metadata.get("source").and_then(|s| s.as_str()) {
                if source == "userfill_aggregated" {
                    // UserFill aggregated data has highest priority for tx_signature
                    if let Some(hash) = new_metadata.get("hash").and_then(|h| h.as_str()) {
                        // Check if the new hash is real and existing is synthetic
                        if Self::is_real_transaction_hash(hash) &&
                            Self::is_synthetic_hash(&existing.tx_signature)
                        {
                            tracing::info!(
                                "Merging: Updating synthetic hash '{}' with real transaction hash '{}' from userfill_aggregated",
                                existing.tx_signature, hash
                            );
                            merged.tx_signature = hash.to_string();
                        } else if Self::is_real_transaction_hash(&existing.tx_signature) &&
                            Self::is_synthetic_hash(hash)
                        {
                            tracing::info!(
                                "Merging: Preserving real transaction hash '{}', not overwriting with synthetic hash '{}' from userfill_aggregated",
                                existing.tx_signature, hash
                            );
                            merged.tx_signature = existing.tx_signature.clone();
                        } else {
                            // Both real or both synthetic, use new hash (userfill_aggregated has
                            // priority)
                            merged.tx_signature = hash.to_string();
                        }
                    } else {
                        // No hash in metadata, preserve existing
                        merged.tx_signature = existing.tx_signature.clone();
                    }
                } else {
                    // For non-userfill sources, apply similar protection logic
                    let new_tx_sig = &new.tx_signature;
                    let existing_tx_sig = &existing.tx_signature;

                    if Self::is_real_transaction_hash(new_tx_sig) &&
                        Self::is_synthetic_hash(existing_tx_sig)
                    {
                        tracing::info!(
                            "Merging: Updating synthetic hash '{}' with real transaction hash '{}' from {}",
                            existing_tx_sig, new_tx_sig, source
                        );
                        merged.tx_signature = new_tx_sig.clone();
                    } else if Self::is_real_transaction_hash(existing_tx_sig) &&
                        Self::is_synthetic_hash(new_tx_sig)
                    {
                        tracing::info!(
                            "Merging: Preserving real transaction hash '{}', not overwriting with synthetic hash '{}' from {}",
                            existing_tx_sig, new_tx_sig, source
                        );
                        merged.tx_signature = existing_tx_sig.clone();
                    } else {
                        // Default: preserve existing tx_signature for non-userfill sources
                        merged.tx_signature = existing_tx_sig.clone();
                    }
                }
            } else {
                merged.tx_signature = existing.tx_signature.clone();
            }
        } else {
            merged.tx_signature = existing.tx_signature.clone();
        }

        // Merge metadata intelligently
        let merged_metadata = self.merge_metadata_intelligent(
            existing.metadata.as_ref(),
            new.metadata.as_ref(),
            &existing.activity_type.to_string(),
            &new.activity_type.to_string(),
        )?;
        merged.metadata = Some(merged_metadata);

        // Use the newer timestamp but preserve creation time of original
        merged.created_at = existing.created_at;

        Ok(merged)
    }

    /// Get field authority mapping based on business meaning rather than simple field names
    /// This maps semantic field meanings to their authoritative data sources
    fn get_field_authority(&self) -> HashMap<&'static str, Vec<&'static str>> {
        HashMap::from([
            // === TRANSACTION IDENTIFIERS ===
            // L1 transaction hashes - system confirmed sources are equally authoritative
            ("hash", vec!["userfill_aggregated", "nonfunding_ledger"]), // Both are L1 confirmed
            ("tx_hash", vec!["userfill_aggregated", "nonfunding_ledger"]),
            // === ACTIVITY CLASSIFICATION ===
            // Activity type - nonfunding_ledger has highest authority for transfer classification
            (
                "activity_type",
                vec!["nonfunding_ledger", "userfill_aggregated", "deposit", "withdraw"],
            ),
            // === USER IDENTITY ===
            // User wallet address - trading intent is most authoritative for user identity
            ("wallet_address", vec!["perps_order", "order_update", "deposit", "withdraw"]),
            // === EXECUTION AMOUNTS (actual trade results) ===
            // Only compare fields representing the same business concept
            ("px", vec!["userfill_aggregated"]), // Actual execution price
            ("sz", vec!["userfill_aggregated"]), // Actual execution size
            ("avg_price", vec!["userfill_aggregated"]), // Aggregated execution price
            ("total_size", vec!["userfill_aggregated"]), // Aggregated execution size
            // === SYSTEM LEDGER AMOUNTS (system recorded balances) ===
            ("usdc", vec!["nonfunding_ledger"]), // System USDC balance changes
            // === USER INTENT AMOUNTS (what user wanted) ===
            ("amount", vec!["deposit", "withdraw"]), // User intended amount
            // === ORDER PARAMETERS (user trading intent) ===
            ("leverage", vec!["perps_order"]), // User set leverage
            ("leverage_type", vec!["perps_order"]), // User set leverage type
            ("order_parameters", vec!["perps_order"]), // User order params
            ("token_image", vec!["perps_order", "deposit", "withdraw"]), // User selected token
            // === ORDER STATUS (current order state) ===
            ("status", vec!["order_update", "withdraw"]), // Current status
            ("limit_price", vec!["order_update", "perps_order"]), // Current limit price
            ("limit_px", vec!["order_update", "perps_order"]), // Current limit price
            ("original_size", vec!["order_update", "perps_order"]), // Original order size
            ("orig_sz", vec!["order_update", "perps_order"]), // Original order size
            ("reduce_only", vec!["order_update", "perps_order"]), // Reduce only flag
            // === USER SPECIFIED ADDRESSES (user intent) ===
            ("from_address", vec!["withdraw", "deposit"]), // User specified source
            ("to_address", vec!["deposit", "withdraw"]),   // User specified destination
            // === SYSTEM RECORDED ADDRESSES (system perspective) ===
            ("user", vec!["nonfunding_ledger"]), // System recorded user
            ("destination", vec!["nonfunding_ledger"]), // System recorded destination
            // === TIME FIELDS (different business meanings) ===
            ("time", vec!["deposit", "withdraw", "userfill_aggregated", "nonfunding_ledger"]), /* Context-dependent time */
            ("completion_time", vec!["userfill_aggregated"]), // Trade completion time
            ("status_timestamp", vec!["order_update"]),       // Status change time
            ("order_timestamp", vec!["order_update", "perps_order"]), // Order placement time
            (
                "timestamp",
                vec!["userfill_aggregated", "nonfunding_ledger", "order_update", "perps_order"],
            ), // General timestamp
            // === ACTUAL FEES (what was really charged) ===
            ("fee", vec!["userfill_aggregated", "nonfunding_ledger", "withdraw"]), // Actual fees
            ("total_fees", vec!["userfill_aggregated"]), // Aggregated actual fees
            // === TRADING PNL (actual profit/loss) ===
            ("closedPnl", vec!["userfill_aggregated"]), // Actual closed P&L from fills
            ("pnl", vec!["userfill_aggregated"]),       // General P&L
            // === USER EXPECTED FEES (what user expected to pay) ===
            ("expected_fee", vec!["deposit", "withdraw"]), // User expected fees
            // === TOKEN SELECTION (user choice) ===
            ("token", vec!["deposit", "withdraw", "perps_order"]), // User selected token
            ("coin", vec!["perps_order", "order_update", "userfill_aggregated"]), /* Trading pair coin */
            ("symbol", vec!["perps_order", "order_update"]),                      // Trading symbol
            ("trading_pair", vec!["perps_order", "order_update"]), // Trading pair name
            ("fee_token", vec!["withdraw", "deposit", "userfill_aggregated"]), // Fee token
            // === EXECUTION DETAILS (actual trade results) ===
            ("fill_count", vec!["userfill_aggregated"]), // Number of fills
            ("fills", vec!["userfill_aggregated"]),      // Fill details
            ("crossed", vec!["userfill_aggregated"]),    // Taker/maker flag
            ("tid", vec!["userfill_aggregated"]),        // Trade ID
            ("side", vec!["perps_order", "order_update", "userfill_aggregated"]), /* Buy/sell direction */
            ("order_type", vec!["userfill_aggregated", "order_update"]), // Market/limit order type
            // === ORDER IDENTIFIERS ===
            ("oid", vec!["perps_order", "order_update", "userfill_aggregated"]), // Order ID
            ("order_id", vec!["perps_order", "order_update", "userfill_aggregated"]), // Order ID
            // === COMMON SPECIFIC FIELDS ===
            ("nonce", vec!["withdraw", "nonfunding_ledger"]), // Withdrawal nonce
            ("transfer_type", vec!["nonfunding_ledger"]),     // System transfer classification
            ("twap_id", vec!["twap_fills"]),                  // TWAP identifier
        ])
    }

    /// Merge a single field based on business meaning and data source authority
    /// This method preserves different business meanings rather than simply overwriting
    fn merge_field_with_authority(
        &self,
        field_name: &str,
        existing_value: Option<&serde_json::Value>,
        existing_source: &str,
        new_value: &serde_json::Value,
        new_source: &str,
        authority_map: &HashMap<&'static str, Vec<&'static str>>,
    ) -> serde_json::Value {
        // Special handling for fields that should preserve different business meanings
        match field_name {
            // Preserve both user intent and system confirmed amounts
            "amount" if existing_source != new_source => {
                if existing_source == "deposit" || existing_source == "withdraw" {
                    // Keep user intent amount, don't overwrite with system amount
                    tracing::debug!(
                        "Field '{}': preserving user intent amount from {}",
                        field_name,
                        existing_source
                    );
                    return existing_value.unwrap_or(new_value).clone();
                }
            }

            // Preserve both user specified and system recorded addresses
            "from_address" | "to_address" if existing_source != new_source => {
                if (existing_source == "deposit" || existing_source == "withdraw") &&
                    new_source == "nonfunding_ledger"
                {
                    // Keep user specified address, don't overwrite with system address
                    tracing::debug!(
                        "Field '{}': preserving user specified address from {}",
                        field_name,
                        existing_source
                    );
                    return existing_value.unwrap_or(new_value).clone();
                }
            }

            // Preserve different time meanings based on business context
            "time" if existing_source != new_source => {
                match (existing_source, new_source) {
                    // User initiation time is most important for user actions
                    ("deposit" | "withdraw", _) => {
                        tracing::debug!(
                            "Field '{}': preserving user initiation time from {}",
                            field_name,
                            existing_source
                        );
                        return existing_value.unwrap_or(new_value).clone();
                    }
                    // Execution time is important for trades
                    ("userfill_aggregated", "nonfunding_ledger") => {
                        tracing::debug!(
                            "Field '{}': preserving execution time from {}",
                            field_name,
                            existing_source
                        );
                        return existing_value.unwrap_or(new_value).clone();
                    }
                    // System confirmation time is authoritative for ledger updates
                    (_, "nonfunding_ledger") => {
                        tracing::debug!(
                            "Field '{}': updating with system confirmation time from {}",
                            field_name,
                            new_source
                        );
                        // Allow update
                    }
                    _ => {
                        // Default: preserve existing
                        tracing::debug!(
                            "Field '{}': preserving existing time from {}",
                            field_name,
                            existing_source
                        );
                        return existing_value.unwrap_or(new_value).clone();
                    }
                }
            }

            // Hash fields - L1 confirmed sources are equally valid
            "hash" | "tx_hash" => {
                if (existing_source == "userfill_aggregated" ||
                    existing_source == "nonfunding_ledger") &&
                    (new_source == "userfill_aggregated" || new_source == "nonfunding_ledger")
                {
                    // Both are L1 confirmed, keep existing
                    tracing::debug!(
                        "Field '{}': keeping existing L1 confirmed hash from {}",
                        field_name,
                        existing_source
                    );
                    return existing_value.unwrap_or(new_value).clone();
                }
            }

            _ => {}
        }

        // Standard authority-based merging for fields with same business meaning
        if let Some(authority_list) = authority_map.get(field_name) {
            // Find positions in authority list (lower index = higher authority)
            let existing_authority = authority_list.iter().position(|&s| s == existing_source);
            let new_authority = authority_list.iter().position(|&s| s == new_source);

            match (existing_authority, new_authority) {
                (Some(existing_pos), Some(new_pos)) => {
                    if new_pos < existing_pos {
                        // New source is more authoritative for this specific field
                        tracing::debug!("Field '{}': using new value from {} (authority pos {}) over {} (authority pos {})",
                            field_name, new_source, new_pos, existing_source, existing_pos);
                        new_value.clone()
                    } else {
                        // Existing source is more authoritative or equal
                        tracing::debug!("Field '{}': keeping existing value from {} (authority pos {}) over {} (authority pos {})",
                            field_name, existing_source, existing_pos, new_source, new_pos);
                        existing_value.unwrap_or(new_value).clone()
                    }
                }
                (None, Some(_)) => {
                    // Only new source is in authority list for this field
                    tracing::debug!(
                        "Field '{}': using new value from {} (only source in authority list)",
                        field_name,
                        new_source
                    );
                    new_value.clone()
                }
                (Some(_), None) => {
                    // Only existing source is in authority list for this field
                    tracing::debug!("Field '{}': keeping existing value from {} (only source in authority list)", field_name, existing_source);
                    existing_value.unwrap_or(new_value).clone()
                }
                (None, None) => {
                    // Neither source is in authority list, preserve existing to avoid data loss
                    tracing::debug!(
                        "Field '{}': preserving existing value (no authority mapping)",
                        field_name
                    );
                    existing_value.unwrap_or(new_value).clone()
                }
            }
        } else {
            // No authority mapping for this field, preserve existing to avoid data loss
            tracing::debug!(
                "Field '{}': preserving existing value (no authority mapping defined)",
                field_name
            );
            existing_value.unwrap_or(new_value).clone()
        }
    }

    /// Intelligent metadata merging with business-meaning-aware field preservation
    fn merge_metadata_intelligent(
        &self,
        existing_metadata: Option<&serde_json::Value>,
        new_metadata: Option<&serde_json::Value>,
        existing_type: &str,
        new_type: &str,
    ) -> Result<serde_json::Value> {
        let mut merged = serde_json::Map::new();
        let authority_map = self.get_field_authority();

        // Get data sources from metadata
        let existing_source = existing_metadata
            .and_then(|m| m.get("source"))
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");
        let new_source = new_metadata
            .and_then(|m| m.get("source"))
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");

        // Start with existing metadata
        if let Some(existing) = existing_metadata {
            if let Some(existing_obj) = existing.as_object() {
                for (key, value) in existing_obj {
                    merged.insert(key.clone(), value.clone());
                }
            }
        }

        // Merge new metadata with business-meaning-aware logic
        if let Some(new) = new_metadata {
            if let Some(new_obj) = new.as_object() {
                for (key, value) in new_obj {
                    let merged_value = self.merge_field_with_authority(
                        key,
                        merged.get(key),
                        existing_source,
                        value,
                        new_source,
                        &authority_map,
                    );
                    merged.insert(key.clone(), merged_value);
                }
            }
        }

        // Add semantic field mappings to preserve different business meanings
        self.add_semantic_field_mappings(&mut merged, existing_source, new_source);

        // Add merge tracking information
        merged.insert(
            "last_updated".to_string(),
            serde_json::Value::Number(serde_json::Number::from(chrono::Utc::now().timestamp())),
        );
        merged.insert(
            "merge_history".to_string(),
            serde_json::json!({
                "existing_source": existing_source,
                "new_source": new_source,
                "existing_type": existing_type,
                "new_type": new_type,
                "merged_at": chrono::Utc::now().timestamp(),
                "merge_strategy": "business_meaning_aware"
            }),
        );

        Ok(serde_json::Value::Object(merged))
    }

    /// Add semantic field mappings to preserve different business meanings
    /// Only creates essential mappings to avoid over-engineering
    fn add_semantic_field_mappings(
        &self,
        merged: &mut serde_json::Map<String, serde_json::Value>,
        existing_source: &str,
        new_source: &str,
    ) {
        // Core semantic mapping: Amount (user intent vs system confirmed)
        if self.should_create_amount_semantics(existing_source, new_source) {
            if let Some(amount) = merged.get("amount").cloned() {
                merged.insert("user_intent_amount".to_string(), amount);
            }
            if let Some(usdc) = merged.get("usdc").cloned() {
                merged.insert("system_confirmed_amount".to_string(), usdc);
            }
        }

        // Core semantic mapping: Time (different time meanings)
        if let Some(time) = merged.get("time").cloned() {
            match existing_source {
                "deposit" | "withdraw" => {
                    merged.insert("initiation_time".to_string(), time);
                }
                "userfill_aggregated" => {
                    merged.insert("execution_time".to_string(), time);
                }
                "nonfunding_ledger" => {
                    merged.insert("confirmation_time".to_string(), time);
                }
                _ => {}
            }
        }

        // Core semantic mapping: Address (user specified vs system recorded)
        if self.should_create_address_semantics(existing_source, new_source) {
            if let Some(from_addr) = merged.get("from_address").cloned() {
                merged.insert("user_specified_from_address".to_string(), from_addr);
            }
            if let Some(to_addr) = merged.get("to_address").cloned() {
                merged.insert("user_specified_to_address".to_string(), to_addr);
            }
            if let Some(user_addr) = merged.get("user").cloned() {
                merged.insert("system_recorded_user_address".to_string(), user_addr);
            }
            if let Some(dest_addr) = merged.get("destination").cloned() {
                merged.insert("system_recorded_destination_address".to_string(), dest_addr);
            }
        }

        // Core semantic mapping: Fee (expected vs actual)
        if self.should_create_fee_semantics(existing_source, new_source) {
            if let Some(fee) = merged.get("fee").cloned() {
                match existing_source {
                    "deposit" | "withdraw" => {
                        merged.insert("expected_fee".to_string(), fee);
                    }
                    "userfill_aggregated" | "nonfunding_ledger" => {
                        merged.insert("actual_fee".to_string(), fee);
                    }
                    _ => {}
                }
            }
        }
    }

    /// Check if amount semantics should be created
    fn should_create_amount_semantics(&self, existing_source: &str, new_source: &str) -> bool {
        matches!(
            (existing_source, new_source),
            ("deposit" | "withdraw", "nonfunding_ledger") |
                ("nonfunding_ledger", "deposit" | "withdraw")
        )
    }

    /// Check if address semantics should be created
    fn should_create_address_semantics(&self, existing_source: &str, new_source: &str) -> bool {
        matches!(
            (existing_source, new_source),
            ("deposit" | "withdraw", "nonfunding_ledger") |
                ("nonfunding_ledger", "deposit" | "withdraw")
        )
    }

    /// Check if fee semantics should be created
    fn should_create_fee_semantics(&self, existing_source: &str, new_source: &str) -> bool {
        matches!(
            (existing_source, new_source),
            ("deposit" | "withdraw", "userfill_aggregated" | "nonfunding_ledger") |
                ("userfill_aggregated" | "nonfunding_ledger", "deposit" | "withdraw")
        )
    }

    /// Check if a transaction signature is a synthetic hash (generated by system)
    fn is_synthetic_hash(tx_signature: &str) -> bool {
        // Check for unified oid_ format (new unified format)
        tx_signature.starts_with("oid_") ||
        // Check for legacy order formats (for backward compatibility)
        tx_signature.starts_with("order_execution_") ||
        tx_signature.starts_with("order_pending_") ||
        tx_signature.starts_with("order_update_") ||
        // Check for other synthetic hash patterns
        tx_signature.starts_with("twap_order_") ||
        tx_signature.starts_with("perps_order_") ||
        // Check for generated system operation hash (34 chars vs normal 66 chars)
        (tx_signature.starts_with("0x") && tx_signature.len() == 34)
    }

    /// Check if a transaction signature is a real transaction hash
    fn is_real_transaction_hash(tx_signature: &str) -> bool {
        // Real transaction hashes are 66 characters long (0x + 64 hex chars)
        tx_signature.starts_with("0x") &&
            tx_signature.len() == 66 &&
            tx_signature[2..].chars().all(|c| c.is_ascii_hexdigit())
    }

    /// Update wallet activity by oid (for userfill_aggregated data)
    /// Protects real transaction hashes from being overwritten by synthetic hashes
    pub async fn update_wallet_activity_by_oid(
        &self,
        activity: &DbWalletActivity,
        oid: i64,
    ) -> Result<()> {
        // First, check if there's an existing record and what type of tx_signature it has
        let existing_record = sqlx::query!(
            r#"
            SELECT tx_signature
            FROM wallet_activity
            WHERE wallet_address = $1
              AND chain = $2
              AND (metadata->>'oid' = $3::text OR metadata->>'order_id' = $3::text)
            "#,
            activity.wallet_address,
            activity.chain,
            oid.to_string()
        )
        .fetch_optional(&self.pool)
        .await?;

        // Determine whether to update tx_signature based on hash types
        let should_update_tx_signature = if let Some(existing) = existing_record {
            let existing_tx_sig = &existing.tx_signature;
            let new_tx_sig = &activity.tx_signature;

            // If existing is synthetic and new is real, allow update
            if Self::is_synthetic_hash(existing_tx_sig) &&
                Self::is_real_transaction_hash(new_tx_sig)
            {
                tracing::info!(
                    "Updating synthetic hash '{}' with real transaction hash '{}' for oid {}",
                    existing_tx_sig,
                    new_tx_sig,
                    oid
                );
                true
            }
            // If existing is real and new is synthetic, don't update tx_signature
            else if Self::is_real_transaction_hash(existing_tx_sig) &&
                Self::is_synthetic_hash(new_tx_sig)
            {
                tracing::info!(
                    "Preserving real transaction hash '{}', not overwriting with synthetic hash '{}' for oid {}",
                    existing_tx_sig, new_tx_sig, oid
                );
                false
            }
            // For other cases (both real, both synthetic, etc.), allow update
            else {
                true
            }
        } else {
            // No existing record, allow any tx_signature
            true
        };

        // Update with conditional tx_signature update
        if should_update_tx_signature {
            let result = sqlx::query!(
                r#"
                UPDATE wallet_activity SET
                    tx_signature = $2,
                    activity_type = $3,
                    token_mint = $4,
                    token_decimals = $5,
                    token_amount = $6,
                    base_mint = $7,
                    base_decimals = $8,
                    base_amount = $9,
                    usd_value = $10,
                    timestamp = $11,
                    block_time = $12,
                    slot = $13,
                    metadata = $14
                WHERE wallet_address = $1
                  AND chain = $15
                  AND (metadata->>'oid' = $16::text OR metadata->>'order_id' = $16::text)
                "#,
                activity.wallet_address,
                activity.tx_signature,
                activity.activity_type.to_string(),
                activity.token_mint,
                activity.token_decimals,
                activity.token_amount,
                activity.base_mint,
                activity.base_decimals,
                activity.base_amount,
                activity.usd_value,
                activity.timestamp,
                activity.block_time,
                activity.slot,
                activity.metadata,
                activity.chain,
                oid.to_string()
            )
            .execute(&self.pool)
            .await?;
        } else {
            // Update all fields except tx_signature
            let result = sqlx::query!(
                r#"
                UPDATE wallet_activity SET
                    activity_type = $2,
                    token_mint = $3,
                    token_decimals = $4,
                    token_amount = $5,
                    base_mint = $6,
                    base_decimals = $7,
                    base_amount = $8,
                    usd_value = $9,
                    timestamp = $10,
                    block_time = $11,
                    slot = $12,
                    metadata = $13
                WHERE wallet_address = $1
                  AND chain = $14
                  AND (metadata->>'oid' = $15::text OR metadata->>'order_id' = $15::text)
                "#,
                activity.wallet_address,
                activity.activity_type.to_string(),
                activity.token_mint,
                activity.token_decimals,
                activity.token_amount,
                activity.base_mint,
                activity.base_decimals,
                activity.base_amount,
                activity.usd_value,
                activity.timestamp,
                activity.block_time,
                activity.slot,
                activity.metadata,
                activity.chain,
                oid.to_string()
            )
            .execute(&self.pool)
            .await?;
        }

        Ok(())
    }

    /// Update wallet activity by tx_signature (for universal framework)
    pub async fn update_wallet_activity_by_signature(
        &self,
        activity: &DbWalletActivity,
    ) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE wallet_activity SET
                activity_type = $2,
                token_mint = $3,
                token_decimals = $4,
                token_amount = $5,
                base_mint = $6,
                base_decimals = $7,
                base_amount = $8,
                usd_value = $9,
                timestamp = $10,
                block_time = $11,
                slot = $12,
                metadata = $13
            WHERE wallet_address = $1
              AND tx_signature = $14
              AND chain = $15
            "#,
            activity.wallet_address,
            activity.activity_type.to_string(),
            activity.token_mint,
            activity.token_decimals,
            activity.token_amount,
            activity.base_mint,
            activity.base_decimals,
            activity.base_amount,
            activity.usd_value,
            activity.timestamp,
            activity.block_time,
            activity.slot,
            activity.metadata,
            activity.tx_signature,
            activity.chain
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}
