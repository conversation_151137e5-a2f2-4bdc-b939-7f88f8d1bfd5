use anyhow::Result;
use serde::{Deserialize, Serialize};

use super::db::Database;

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbAccountValue {
    pub wallet_address: String,
    pub timestamp: i64,
    pub value: f64,
    pub chain: i16,
}

impl Database {
    pub async fn insert_account_value(&self, account_value: DbAccountValue) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO account_values (
                wallet_address, timestamp, value, chain
            )
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (wallet_address, timestamp, chain) DO NOTHING
            "#,
            account_value.wallet_address,
            account_value.timestamp,
            account_value.value,
            account_value.chain
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_account_values(
        &self,
        wallet_address: &str,
        start_timestamp: i64,
        end_timestamp: i64,
    ) -> Result<Vec<DbAccountValue>> {
        let mut account_values = sqlx::query_as!(
            DbAccountValue,
            r#"
            SELECT wallet_address, timestamp, value, chain FROM account_values WHERE wallet_address = $1 AND timestamp >= $2 AND timestamp <= $3
            ORDER BY timestamp DESC
            "#,
            wallet_address,
            start_timestamp,
            end_timestamp
        )
        .fetch_all(&self.pool)
        .await?;

        account_values.reverse();
        Ok(account_values)
    }

    pub async fn get_latest_account_value(
        &self,
        wallet_address: &str,
    ) -> Result<Option<DbAccountValue>> {
        let account_value = sqlx::query_as!(
            DbAccountValue,
            r#"
            SELECT wallet_address, timestamp, value, chain FROM account_values WHERE wallet_address = $1 ORDER BY timestamp DESC LIMIT 1
            "#,
            wallet_address
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(account_value)
    }

    pub async fn get_latest_account_value_before_timestamp(
        &self,
        wallet_address: &str,
        timestamp: i64,
    ) -> Result<Option<DbAccountValue>> {
        if timestamp == 0 {
            return self.get_first_non_zero_account_value(wallet_address).await;
        }

        let account_value = sqlx::query_as!(
            DbAccountValue,
            r#"
            SELECT wallet_address, timestamp, value, chain FROM account_values
            WHERE wallet_address = $1 AND timestamp <= $2
            ORDER BY timestamp DESC LIMIT 1
            "#,
            wallet_address,
            timestamp
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(account_value)
    }

    pub async fn get_first_non_zero_account_value_after_timestamp(
        &self,
        wallet_address: &str,
        timestamp: i64,
    ) -> Result<Option<DbAccountValue>> {
        if timestamp == 0 {
            return self.get_first_non_zero_account_value(wallet_address).await;
        }

        let account_value = sqlx::query_as!(
            DbAccountValue,
            r#"
            SELECT wallet_address, timestamp, value, chain FROM account_values
            WHERE wallet_address = $1 AND timestamp >= $2 AND value > 0
            ORDER BY timestamp ASC LIMIT 1
            "#,
            wallet_address,
            timestamp
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(account_value)
    }

    pub async fn get_first_non_zero_account_value(
        &self,
        wallet_address: &str,
    ) -> Result<Option<DbAccountValue>> {
        let account_value = sqlx::query_as!(
            DbAccountValue,
            r#"
            SELECT wallet_address, timestamp, value, chain FROM account_values
            WHERE wallet_address = $1 AND value > 0
            ORDER BY timestamp ASC LIMIT 1
            "#,
            wallet_address
        )
        .fetch_optional(&self.pool)
        .await?;
        Ok(account_value)
    }

    pub async fn get_all_latest_account_values(&self) -> Result<Vec<DbAccountValue>> {
        let account_values = sqlx::query_as!(
            DbAccountValue,
            r#"
            WITH latest_values AS (
                SELECT DISTINCT ON (wallet_address) wallet_address, timestamp, value, chain
                FROM account_values
                ORDER BY wallet_address, timestamp DESC
            )
            SELECT wallet_address, timestamp, value, chain FROM latest_values
            "#
        )
        .fetch_all(&self.pool)
        .await?;
        Ok(account_values)
    }
}
