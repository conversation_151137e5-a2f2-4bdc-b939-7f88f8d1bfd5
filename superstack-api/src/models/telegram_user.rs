use anyhow::Result;
use serde::{Deserialize, Serialize};

use super::db::Database;

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct TelegramUser {
    pub id: u64,
    pub username: Option<String>,
    pub first_name: String,
    pub last_name: Option<String>,
    pub is_bot: bool,
    pub is_premium: bool,
    pub created_at: i64,
    pub updated_at: i64,
    pub bind_account: Option<String>,
    pub bind_at: Option<i64>,
    pub auth_code: Option<String>,
    pub auth_code_created_at: Option<i64>,
}

impl Database {
    pub async fn insert_telegram_user_or_update_init_info(
        &self,
        telegram_user: &TelegramUser,
    ) -> Result<Option<String>> {
        let result = sqlx::query!(
            r#"
            INSERT INTO telegram_users (
                id, username, first_name, last_name, is_bot, is_premium,
                created_at, updated_at,
                bind_account, bind_at,
                auth_code, auth_code_created_at
            )
            VALUES (
                $1, $2, $3, $4, $5, $6,
                $7, $8,
                $9, $10,
                $11, $12
            )
            ON CONFLICT (id) DO UPDATE SET
                username = EXCLUDED.username,
                first_name = EXCLUDED.first_name,
                last_name = EXCLUDED.last_name,
                is_bot = EXCLUDED.is_bot,
                is_premium = EXCLUDED.is_premium,
                updated_at = EXCLUDED.updated_at,
                auth_code = EXCLUDED.auth_code,
                auth_code_created_at = EXCLUDED.auth_code_created_at
            RETURNING bind_account
            "#,
            telegram_user.id as i64,
            telegram_user.username,
            telegram_user.first_name,
            telegram_user.last_name,
            telegram_user.is_bot,
            telegram_user.is_premium,
            telegram_user.created_at,
            telegram_user.updated_at,
            telegram_user.bind_account,
            telegram_user.bind_at,
            telegram_user.auth_code,
            telegram_user.auth_code_created_at,
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(result.and_then(|row| row.bind_account))
    }

    pub async fn get_telegram_user_by_id(
        &self,
        telegram_user_id: u64,
    ) -> Result<Option<TelegramUser>> {
        let result = sqlx::query!(
            r#"
            SELECT
                id, username, first_name, last_name, is_bot, is_premium,
                created_at, updated_at,
                bind_account, bind_at,
                auth_code, auth_code_created_at
            FROM telegram_users WHERE id = $1
            "#,
            telegram_user_id as i64
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = result {
            Ok(Some(TelegramUser {
                id: row.id as u64,
                username: row.username,
                first_name: row.first_name,
                last_name: row.last_name,
                is_bot: row.is_bot,
                is_premium: row.is_premium,
                created_at: row.created_at,
                updated_at: row.updated_at,
                bind_account: row.bind_account,
                bind_at: row.bind_at,
                auth_code: row.auth_code,
                auth_code_created_at: row.auth_code_created_at,
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn get_telegram_user_by_wallet(
        &self,
        wallet_address: &str,
    ) -> Result<Option<TelegramUser>> {
        let result = sqlx::query!(
            r#"
            SELECT
                id, username, first_name, last_name, is_bot, is_premium,
                created_at, updated_at,
                bind_account, bind_at,
                auth_code, auth_code_created_at
            FROM telegram_users WHERE bind_account = $1
            "#,
            wallet_address
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = result {
            Ok(Some(TelegramUser {
                id: row.id as u64,
                username: row.username,
                first_name: row.first_name,
                last_name: row.last_name,
                is_bot: row.is_bot,
                is_premium: row.is_premium,
                created_at: row.created_at,
                updated_at: row.updated_at,
                bind_account: row.bind_account,
                bind_at: row.bind_at,
                auth_code: row.auth_code,
                auth_code_created_at: row.auth_code_created_at,
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_telegram_user_bind_account(
        &self,
        telegram_user_id: u64,
        bind_account: &str,
        auth_code: &str,
    ) -> Result<bool> {
        let now = chrono::Utc::now().timestamp();
        let result = sqlx::query!(
            r#"
            UPDATE telegram_users
            SET
                bind_account = $1,
                bind_at = $2,
                updated_at = $2,
                auth_code = NULL,
                auth_code_created_at = NULL
            WHERE
                id = $3 AND
                bind_account IS NULL AND
                auth_code = $4
            "#,
            bind_account,
            now,
            telegram_user_id as i64,
            auth_code,
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }
}
