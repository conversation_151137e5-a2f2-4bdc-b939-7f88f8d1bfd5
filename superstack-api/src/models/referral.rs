use anyhow::Result;
use chrono::Utc;
use rand::Rng;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sqlx::types::BigDecimal;
use std::str::FromStr;

use super::db::Database;

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct ReferralCode {
    pub id: i64,
    pub owner_wallet: String,
    pub code: String,
    pub status: String,
    pub used_by_wallet: Option<String>,
    pub used_at: Option<i64>,
    pub created_at: i64,
}

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Referral {
    pub referrer_wallet: Option<String>, // NULL for seed users
    pub referee_wallet: String,
    pub created_at: i64,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ReferralReward {
    pub id: i64,
    pub referrer_wallet: String,
    pub referee_wallet: String,
    pub reward_type: String,
    pub trading_volume_usd: Option<rust_decimal::Decimal>,
    pub tx_signature: Option<String>,
    pub is_claimed: bool,
    pub created_at: i64,
    pub claimed_at: Option<i64>,
}

impl ReferralReward {
    /// Calculate the USD amount for this reward based on type and trading volume
    pub fn calculate_amount_usd(&self) -> rust_decimal::Decimal {
        Self::calculate_reward_amount(&self.reward_type, self.trading_volume_usd)
    }

    /// Static method to calculate reward amount without creating a full ReferralReward object
    pub fn calculate_reward_amount(
        reward_type: &str,
        trading_volume_usd: Option<rust_decimal::Decimal>,
    ) -> rust_decimal::Decimal {
        match (reward_type, trading_volume_usd) {
            ("signup", _) => RewardConfig::signup_reward_usd(),
            ("trading_tier1", Some(volume)) => {
                let config = crate::config::Config::get();
                volume * config.referral_tier1_percentage
            }
            ("trading_tier2", Some(volume)) => {
                let config = crate::config::Config::get();
                volume * config.referral_tier2_percentage
            }
            ("trading_tier3", Some(volume)) => {
                let config = crate::config::Config::get();
                volume * config.referral_tier3_percentage
            }
            _ => rust_decimal::Decimal::ZERO,
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ReferralStats {
    pub total_invites: i64,
    pub unclaimed_rewards_usd: Decimal,
    pub claimed_rewards_usd: Decimal,
}

impl Default for ReferralStats {
    fn default() -> Self {
        Self {
            total_invites: 0,
            unclaimed_rewards_usd: Decimal::ZERO,
            claimed_rewards_usd: Decimal::ZERO,
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ReferralNetworkNode {
    pub wallet: String,
    pub level: u8,
    pub referral_count: i64,
    pub total_rewards_usd: Decimal,
    pub trading_volume_usd: Decimal,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ReferralNetwork {
    pub root: String,
    pub relationships: Vec<(String, String)>, // (parent, child) pairs
    pub nodes: std::collections::HashMap<String, ReferralNetworkNode>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ReferralChain {
    pub tier1_referrer: Option<String>, // Direct referrer (Tier 1)
    pub tier2_referrer: Option<String>, // Second level referrer (Tier 2)
    pub tier3_referrer: Option<String>, // Third level referrer (Tier 3)
}

impl ReferralChain {
    pub fn new() -> Self {
        Self { tier1_referrer: None, tier2_referrer: None, tier3_referrer: None }
    }

    pub fn get_referrer_at_tier(&self, tier: u8) -> Option<&String> {
        match tier {
            1 => self.tier1_referrer.as_ref(),
            2 => self.tier2_referrer.as_ref(),
            3 => self.tier3_referrer.as_ref(),
            _ => None,
        }
    }
}

fn random_char() -> char {
    let chars = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    chars[rand::thread_rng().gen_range(0..chars.len())] as char
}

/// Generate a unique 6-character referral code
async fn generate_unique_6char_code(db: &Database) -> Result<String> {
    let max_attempts = 100;

    for _ in 0..max_attempts {
        // Generate 6 random characters (uppercase letters + numbers)
        let code: String = (0..6).map(|_| random_char()).collect();

        if db.get_referral_code_by_code(&code).await?.is_none() {
            return Ok(code);
        }
    }

    Err(anyhow::anyhow!("Failed to generate unique code after {} attempts", max_attempts))
}

impl Database {
    /// Create a single referral code for user
    pub async fn create_referral_code(&self, owner_wallet: &str) -> Result<ReferralCode> {
        let code = generate_unique_6char_code(self).await?;
        let created_at = Utc::now().timestamp();

        let row = sqlx::query_as!(
            ReferralCode,
            r#"
            INSERT INTO referral_codes (owner_wallet, code, status, created_at)
            VALUES ($1, $2, 'active', $3)
            RETURNING id, owner_wallet, code, status, used_by_wallet, used_at, created_at
            "#,
            owner_wallet,
            code,
            created_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(row)
    }

    /// Create multiple referral codes for user
    pub async fn create_multiple_referral_codes(
        &self,
        owner_wallet: &str,
        count: usize,
    ) -> Result<Vec<ReferralCode>> {
        let mut codes = Vec::new();

        for _ in 0..count {
            let code = self.create_referral_code(owner_wallet).await?;
            codes.push(code);
        }

        Ok(codes)
    }

    /// Get user's referral codes - ensure proper count based on user type
    pub async fn get_user_referral_codes(&self, owner_wallet: &str) -> Result<Vec<ReferralCode>> {
        // Get existing codes
        let existing_codes = sqlx::query_as!(
            ReferralCode,
            r#"
            SELECT id, owner_wallet, code, status, used_by_wallet, used_at, created_at
            FROM referral_codes
            WHERE owner_wallet = $1
            ORDER BY created_at DESC
            "#,
            owner_wallet
        )
        .fetch_all(&self.pool)
        .await?;

        // Check if user is unlimited
        let config = crate::config::Config::get();
        let is_unlimited = config.referral_unlimited_users.contains(&owner_wallet.to_string());

        // Calculate how many codes we need to create
        let needed = if is_unlimited {
            // Unlimited users: ensure 12 active (unused) codes
            let active_count = existing_codes.iter().filter(|c| c.status == "active").count();
            12_usize.saturating_sub(active_count)
        } else {
            // Regular users: ensure 12 total codes (active + used)
            12_usize.saturating_sub(existing_codes.len())
        };

        if needed > 0 {
            let new_codes = self.create_multiple_referral_codes(owner_wallet, needed).await?;
            let mut all_codes = existing_codes;
            all_codes.extend(new_codes);
            all_codes.sort_by(|a, b| b.created_at.cmp(&a.created_at));
            Ok(all_codes)
        } else {
            Ok(existing_codes)
        }
    }

    pub async fn get_referral_code_by_code(&self, code: &str) -> Result<Option<ReferralCode>> {
        let row = sqlx::query_as!(
            ReferralCode,
            r#"
            SELECT id, owner_wallet, code, status, used_by_wallet, used_at, created_at
            FROM referral_codes
            WHERE code = $1
            "#,
            code
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row)
    }

    pub async fn mark_referral_code_used(&self, code: &str, used_by_wallet: &str) -> Result<bool> {
        let used_at = Utc::now().timestamp();

        // Get the code info to check the owner before updating
        let code_info = self.get_referral_code_by_code(code).await?;

        let result = sqlx::query!(
            r#"
            UPDATE referral_codes
            SET status = 'used', used_by_wallet = $1, used_at = $2
            WHERE code = $3 AND status = 'active'
            "#,
            used_by_wallet,
            used_at,
            code
        )
        .execute(&self.pool)
        .await?;

        let success = result.rows_affected() > 0;

        // For unlimited users, automatically create a new code to maintain 12 active codes
        if success && code_info.is_some() {
            let owner_wallet = &code_info.unwrap().owner_wallet;
            let config = crate::config::Config::get();
            if config.referral_unlimited_users.contains(owner_wallet) {
                // Ignore errors when creating replacement code - this is best effort
                let _ = self.create_referral_code(owner_wallet).await;
            }
        }

        Ok(success)
    }

    // Referral relationship operations
    pub async fn create_referral(
        &self,
        referrer_wallet: &str,
        referee_wallet: &str,
    ) -> Result<bool> {
        let created_at = Utc::now().timestamp();

        let result = sqlx::query!(
            r#"
            INSERT INTO referrals (referrer_wallet, referee_wallet, created_at)
            VALUES ($1, $2, $3)
            ON CONFLICT (referee_wallet) DO NOTHING
            "#,
            referrer_wallet,
            referee_wallet,
            created_at
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn get_referral_by_referee(&self, referee_wallet: &str) -> Result<Option<Referral>> {
        let row = sqlx::query_as!(
            Referral,
            r#"
            SELECT referrer_wallet, referee_wallet, created_at
            FROM referrals
            WHERE referee_wallet = $1
            "#,
            referee_wallet
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row)
    }

    /// Get the complete referral chain for a given wallet (up to 3 tiers)
    /// Returns the chain of referrers from the given wallet upwards
    /// Optimized with single CTE query to avoid N+1 problem
    pub async fn get_referral_chain(&self, wallet_address: &str) -> Result<ReferralChain> {
        let rows = sqlx::query!(
            r#"
            WITH RECURSIVE referral_chain AS (
                -- Base case: direct referrer
                SELECT referrer_wallet, referee_wallet, 1 as level
                FROM referrals
                WHERE referee_wallet = $1
                AND referrer_wallet IS NOT NULL
                AND referrer_wallet != ''

                UNION ALL

                -- Recursive case: up to 3 levels
                SELECT r.referrer_wallet, r.referee_wallet, rc.level + 1
                FROM referrals r
                INNER JOIN referral_chain rc ON r.referee_wallet = rc.referrer_wallet
                WHERE rc.level < 3
                AND r.referrer_wallet IS NOT NULL
                AND r.referrer_wallet != ''
            )
            SELECT referrer_wallet, level FROM referral_chain
            ORDER BY level
            "#,
            wallet_address
        )
        .fetch_all(&self.pool)
        .await?;

        let mut chain = ReferralChain::new();
        for row in rows {
            if let Some(referrer) = row.referrer_wallet {
                match row.level {
                    Some(1) => chain.tier1_referrer = Some(referrer),
                    Some(2) => chain.tier2_referrer = Some(referrer),
                    Some(3) => chain.tier3_referrer = Some(referrer),
                    _ => {} // Ignore invalid levels
                }
            }
        }

        Ok(chain)
    }

    pub async fn get_referrals_by_referrer(
        &self,
        referrer_wallet: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<Referral>> {
        let rows = sqlx::query_as!(
            Referral,
            r#"
            SELECT referrer_wallet, referee_wallet, created_at
            FROM referrals
            WHERE referrer_wallet = $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
            "#,
            referrer_wallet,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(rows)
    }

    /// Get referral network starting from a root wallet (3 levels deep)
    pub async fn get_referral_network(&self, root_wallet: &str) -> Result<ReferralNetwork> {
        let mut relationships = Vec::new();
        let mut nodes = std::collections::HashMap::new();

        // Level 0: Root node
        let root_stats = self.get_referral_stats(root_wallet).await?;
        let root_total_rewards = root_stats.claimed_rewards_usd + root_stats.unclaimed_rewards_usd;
        let root_trading_volume = match self.get_user_trading_volume(root_wallet).await {
            Ok(volume) => volume,
            Err(e) => {
                tracing::warn!(
                    "Failed to get trading volume for root wallet {}: {}",
                    root_wallet,
                    e
                );
                Decimal::ZERO
            }
        };

        nodes.insert(
            root_wallet.to_string(),
            ReferralNetworkNode {
                wallet: root_wallet.to_string(),
                level: 0,
                referral_count: root_stats.total_invites,
                total_rewards_usd: root_total_rewards,
                trading_volume_usd: root_trading_volume,
            },
        );

        // Get all referral relationships up to 3 levels using recursive CTE
        let rows = sqlx::query!(
            r#"
            WITH RECURSIVE referral_tree AS (
                -- Level 1: Direct referrals
                SELECT referrer_wallet, referee_wallet, 1 as level
                FROM referrals
                WHERE referrer_wallet = $1

                UNION ALL

                -- Levels 2-3: Recursive referrals
                SELECT r.referrer_wallet, r.referee_wallet, rt.level + 1
                FROM referrals r
                INNER JOIN referral_tree rt ON r.referrer_wallet = rt.referee_wallet
                WHERE rt.level < 3
            )
            SELECT referrer_wallet, referee_wallet, level FROM referral_tree
            ORDER BY level, referrer_wallet, referee_wallet
            "#,
            root_wallet
        )
        .fetch_all(&self.pool)
        .await?;

        // Process relationships and collect unique wallets
        let mut wallet_set = std::collections::HashSet::new();
        wallet_set.insert(root_wallet.to_string());

        for row in &rows {
            if let (Some(referrer), Some(referee), Some(level)) =
                (&row.referrer_wallet, &row.referee_wallet, row.level)
            {
                relationships.push((referrer.clone(), referee.clone()));
                wallet_set.insert(referrer.clone());
                wallet_set.insert(referee.clone());
            }
        }

        // Batch query trading volumes for all wallets (performance optimization)
        let all_wallets: Vec<String> = wallet_set.iter().cloned().collect();
        let trading_volumes =
            self.get_users_trading_volumes(&all_wallets).await.unwrap_or_else(|e| {
                tracing::warn!("Failed to batch query trading volumes: {}", e);
                std::collections::HashMap::new()
            });

        // Get stats for all wallets in the network (except root which we already have)
        for wallet in wallet_set {
            if wallet != root_wallet && !nodes.contains_key(&wallet) {
                let stats = self.get_referral_stats(&wallet).await.unwrap_or_default();
                let total_rewards = stats.claimed_rewards_usd + stats.unclaimed_rewards_usd;
                let trading_volume = trading_volumes.get(&wallet).copied().unwrap_or(Decimal::ZERO);

                // Determine level from relationships
                let level = rows
                    .iter()
                    .find(|r| r.referee_wallet.as_ref() == Some(&wallet))
                    .and_then(|r| r.level)
                    .unwrap_or(0) as u8;

                nodes.insert(
                    wallet.clone(),
                    ReferralNetworkNode {
                        wallet: wallet.clone(),
                        level,
                        referral_count: stats.total_invites,
                        total_rewards_usd: total_rewards,
                        trading_volume_usd: trading_volume,
                    },
                );
            }
        }

        Ok(ReferralNetwork { root: root_wallet.to_string(), relationships, nodes })
    }

    pub async fn get_referral_stats(&self, wallet_address: &str) -> Result<ReferralStats> {
        // Get total invites
        let total_invites = sqlx::query!(
            r#"SELECT COUNT(*) as count FROM referrals WHERE referrer_wallet = $1"#,
            wallet_address
        )
        .fetch_one(&self.pool)
        .await?
        .count
        .unwrap_or(0);

        // Get all rewards for this wallet to calculate amounts dynamically
        let rewards = sqlx::query!(
            r#"
            SELECT reward_type, trading_volume_usd, is_claimed
            FROM referral_rewards
            WHERE referrer_wallet = $1
            "#,
            wallet_address
        )
        .fetch_all(&self.pool)
        .await?;

        let mut unclaimed_rewards_usd = rust_decimal::Decimal::ZERO;
        let mut claimed_rewards_usd = rust_decimal::Decimal::ZERO;

        for reward in rewards {
            // Convert database BigDecimal to rust_decimal::Decimal
            let trading_volume_usd = reward.trading_volume_usd.map(|vol| {
                let vol_str = vol.to_string();
                rust_decimal::Decimal::from_str(&vol_str).unwrap_or(rust_decimal::Decimal::ZERO)
            });

            let amount =
                ReferralReward::calculate_reward_amount(&reward.reward_type, trading_volume_usd);

            if reward.is_claimed {
                claimed_rewards_usd += amount;
            } else {
                unclaimed_rewards_usd += amount;
            }
        }

        Ok(ReferralStats { total_invites, unclaimed_rewards_usd, claimed_rewards_usd })
    }

    /// Get user's total trading volume from referral_rewards table
    /// Uses DISTINCT tx_signature to avoid double counting the same transaction
    pub async fn get_user_trading_volume(&self, wallet_address: &str) -> Result<Decimal> {
        let row = sqlx::query!(
            r#"
            SELECT COALESCE(SUM(DISTINCT_VOLUME.trading_volume_usd), 0.0) as total_volume
            FROM (
                SELECT DISTINCT tx_signature, trading_volume_usd
                FROM referral_rewards
                WHERE referee_wallet = $1
                  AND tx_signature IS NOT NULL
                  AND trading_volume_usd IS NOT NULL
            ) AS DISTINCT_VOLUME
            "#,
            wallet_address
        )
        .fetch_one(&self.pool)
        .await?;

        let volume = row.total_volume.unwrap_or_else(|| BigDecimal::from(0));
        // Convert BigDecimal to rust_decimal::Decimal
        let volume_str = volume.to_string();
        Ok(Decimal::from_str(&volume_str).unwrap_or(Decimal::ZERO))
    }

    /// Get trading volumes for multiple users in a single query (performance optimization)
    /// Uses DISTINCT tx_signature to avoid double counting the same transaction
    pub async fn get_users_trading_volumes(
        &self,
        wallet_addresses: &[String],
    ) -> Result<std::collections::HashMap<String, Decimal>> {
        if wallet_addresses.is_empty() {
            return Ok(std::collections::HashMap::new());
        }

        let rows = sqlx::query!(
            r#"
            SELECT
                referee_wallet,
                COALESCE(SUM(DISTINCT_VOLUME.trading_volume_usd), 0.0) as total_volume
            FROM (
                SELECT DISTINCT referee_wallet, tx_signature, trading_volume_usd
                FROM referral_rewards
                WHERE referee_wallet = ANY($1)
                  AND tx_signature IS NOT NULL
                  AND trading_volume_usd IS NOT NULL
            ) AS DISTINCT_VOLUME
            GROUP BY referee_wallet
            "#,
            wallet_addresses
        )
        .fetch_all(&self.pool)
        .await?;

        let mut result = std::collections::HashMap::new();

        // Initialize all wallets with 0 volume
        for wallet in wallet_addresses {
            result.insert(wallet.clone(), Decimal::ZERO);
        }

        // Update with actual volumes
        for row in rows {
            let wallet = row.referee_wallet;
            let volume = row.total_volume.unwrap_or_else(|| BigDecimal::from(0));
            // Convert BigDecimal to rust_decimal::Decimal
            let volume_str = volume.to_string();
            let decimal_volume = Decimal::from_str(&volume_str).unwrap_or(Decimal::ZERO);
            result.insert(wallet, decimal_volume);
        }

        Ok(result)
    }

    // Referral reward operations
    pub async fn create_referral_reward(
        &self,
        referrer_wallet: &str,
        referee_wallet: &str,
        reward_type: &str,
        trading_volume_usd: Option<rust_decimal::Decimal>,
        tx_signature: Option<&str>,
    ) -> Result<Option<i64>> {
        let created_at = Utc::now().timestamp();

        // Convert trading_volume_usd to BigDecimal for database
        let trading_volume_big = trading_volume_usd.map(|vol| {
            BigDecimal::from_str(&vol.to_string()).unwrap_or_else(|_| BigDecimal::from(0))
        });

        let result = sqlx::query!(
            r#"
            INSERT INTO referral_rewards (referrer_wallet, referee_wallet, reward_type, trading_volume_usd, tx_signature, is_claimed, created_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (referrer_wallet, referee_wallet, reward_type, tx_signature) DO NOTHING
            RETURNING id
            "#,
            referrer_wallet,
            referee_wallet,
            reward_type,
            trading_volume_big,
            tx_signature,
            false,
            created_at
        )
        .fetch_optional(&self.pool)
        .await?;

        match result {
            Some(row) => Ok(Some(row.id)),
            None => {
                // Reward already exists for this transaction, log and return None
                tracing::debug!(
                    "Duplicate referral reward skipped: referrer={}, referee={}, type={}, tx={}",
                    referrer_wallet,
                    referee_wallet,
                    reward_type,
                    tx_signature.unwrap_or("None")
                );
                Ok(None)
            }
        }
    }

    /// Helper function to process tier rewards with common logic
    async fn process_tier_reward(
        &self,
        tier_name: &str,
        referrer: &str,
        trader: &str,
        reward_type: RewardType,
        trading_volume_usd: rust_decimal::Decimal,
        tx_signature: &str,
        created_rewards: &mut Vec<i64>,
    ) -> Result<()> {
        match self
            .create_referral_reward(
                referrer,
                trader,
                reward_type.as_str(),
                Some(trading_volume_usd),
                Some(tx_signature),
            )
            .await?
        {
            Some(reward_id) => {
                created_rewards.push(reward_id);
                tracing::info!(
                    "Created {} trading reward: referrer={}, trader={}, volume=${}, tx={}, reward_id={}",
                    tier_name,
                    referrer,
                    trader,
                    trading_volume_usd,
                    tx_signature,
                    reward_id
                );
            }
            None => {
                tracing::debug!(
                    "{} trading reward already exists: referrer={}, trader={}, tx={}",
                    tier_name,
                    referrer,
                    trader,
                    tx_signature
                );
            }
        }
        Ok(())
    }

    pub async fn get_referral_rewards(
        &self,
        wallet_address: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<ReferralReward>> {
        let rows = sqlx::query!(
            r#"
            SELECT id, referrer_wallet, referee_wallet, reward_type, trading_volume_usd, tx_signature, is_claimed, created_at, claimed_at
            FROM referral_rewards
            WHERE referrer_wallet = $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
            "#,
            wallet_address,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        // Convert database rows to ReferralReward structs
        let rewards = rows
            .into_iter()
            .map(|row| {
                let trading_volume_usd = row.trading_volume_usd.map(|vol| {
                    let vol_str = vol.to_string();
                    rust_decimal::Decimal::from_str(&vol_str).unwrap_or(rust_decimal::Decimal::ZERO)
                });

                ReferralReward {
                    id: row.id,
                    referrer_wallet: row.referrer_wallet,
                    referee_wallet: row.referee_wallet,
                    reward_type: row.reward_type,
                    trading_volume_usd,
                    tx_signature: row.tx_signature,
                    is_claimed: row.is_claimed,
                    created_at: row.created_at,
                    claimed_at: row.claimed_at,
                }
            })
            .collect();

        Ok(rewards)
    }

    pub async fn get_referral_rewards_count(&self, wallet_address: &str) -> Result<i64> {
        let count = sqlx::query!(
            r#"
            SELECT COUNT(*) as count FROM referral_rewards
            WHERE referrer_wallet = $1
            "#,
            wallet_address
        )
        .fetch_one(&self.pool)
        .await?
        .count
        .unwrap_or(0);

        Ok(count)
    }

    pub async fn claim_referral_reward(
        &self,
        reward_id: i64,
        wallet_address: &str,
    ) -> Result<bool> {
        let claimed_at = Utc::now().timestamp();

        let result = sqlx::query!(
            r#"
            UPDATE referral_rewards
            SET is_claimed = true, claimed_at = $1
            WHERE id = $2 AND referrer_wallet = $3 AND is_claimed = false
            "#,
            claimed_at,
            reward_id,
            wallet_address
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    /// Process signup reward when a referee completes their first trade
    pub async fn process_signup_reward(&self, referee_wallet: &str) -> Result<Option<i64>> {
        // Check if referee has a referrer
        let referral = match self.get_referral_by_referee(referee_wallet).await? {
            Some(r) => r,
            None => return Ok(None), // No referrer, no reward
        };

        // Skip reward processing for seed users (no referrer)
        let referrer_wallet = match &referral.referrer_wallet {
            Some(wallet) => wallet,
            None => {
                tracing::info!("Skipping signup reward for seed user: {}", referee_wallet);
                return Ok(None);
            }
        };

        // Check if signup reward already exists for this referee
        let existing_reward = sqlx::query!(
            r#"
            SELECT id FROM referral_rewards
            WHERE referrer_wallet = $1 AND referee_wallet = $2 AND reward_type = $3
            LIMIT 1
            "#,
            referrer_wallet,
            referee_wallet,
            RewardType::Signup.as_str()
        )
        .fetch_optional(&self.pool)
        .await?;

        if existing_reward.is_some() {
            return Ok(None); // Signup reward already processed
        }

        // Create signup reward (signup rewards don't have trading volume or tx_signature)
        let reward_id = match self
            .create_referral_reward(
                referrer_wallet,
                &referral.referee_wallet,
                RewardType::Signup.as_str(),
                None, // No trading volume for signup rewards
                None, // No tx_signature for signup rewards
            )
            .await?
        {
            Some(id) => id,
            None => return Ok(None), /* Duplicate signup reward, should not happen due to
                                      * existing check */
        };

        tracing::info!(
            "Created signup reward: referrer={}, referee={}, amount=${}, reward_id={}",
            referrer_wallet,
            referee_wallet,
            RewardConfig::signup_reward_usd(),
            reward_id
        );

        Ok(Some(reward_id))
    }

    /// Process multi-tier trading rewards based on trading volume
    /// Returns a vector of created reward IDs
    /// Includes idempotency check to prevent duplicate rewards
    pub async fn process_multi_tier_trading_rewards(
        &self,
        trader_wallet: &str,
        trading_volume_usd: rust_decimal::Decimal,
        tx_signature: &str,
        tier1_percentage: rust_decimal::Decimal,
        tier2_percentage: rust_decimal::Decimal,
        tier3_percentage: rust_decimal::Decimal,
    ) -> Result<Vec<i64>> {
        let mut created_rewards = Vec::new();

        // Get the referral chain for the trader
        let referral_chain = self.get_referral_chain(trader_wallet).await?;

        // Tier 1 reward (direct referrer)
        if let Some(tier1_referrer) = &referral_chain.tier1_referrer {
            let tier1_amount = trading_volume_usd * tier1_percentage;
            if tier1_amount > rust_decimal::Decimal::ZERO {
                self.process_tier_reward(
                    "Tier 1",
                    tier1_referrer,
                    trader_wallet,
                    RewardType::TradingTier1,
                    trading_volume_usd,
                    tx_signature,
                    &mut created_rewards,
                )
                .await?;
            }
        }

        // Tier 2 reward (second level referrer)
        if let Some(tier2_referrer) = &referral_chain.tier2_referrer {
            let tier2_amount = trading_volume_usd * tier2_percentage;
            if tier2_amount > rust_decimal::Decimal::ZERO {
                self.process_tier_reward(
                    "Tier 2",
                    tier2_referrer,
                    trader_wallet,
                    RewardType::TradingTier2,
                    trading_volume_usd,
                    tx_signature,
                    &mut created_rewards,
                )
                .await?;
            }
        }

        // Tier 3 reward (third level referrer)
        if let Some(tier3_referrer) = &referral_chain.tier3_referrer {
            let tier3_amount = trading_volume_usd * tier3_percentage;
            if tier3_amount > rust_decimal::Decimal::ZERO {
                self.process_tier_reward(
                    "Tier 3",
                    tier3_referrer,
                    trader_wallet,
                    RewardType::TradingTier3,
                    trading_volume_usd,
                    tx_signature,
                    &mut created_rewards,
                )
                .await?;
            }
        }

        Ok(created_rewards)
    }

    /// Check if this is the first trade for a wallet (no previous signup rewards)
    pub async fn is_first_trade(&self, wallet_address: &str) -> Result<bool> {
        let count = sqlx::query!(
            r#"
            SELECT COUNT(*) as count FROM referral_rewards
            WHERE referee_wallet = $1 AND reward_type = 'signup'
            "#,
            wallet_address
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.count.unwrap_or(0) == 0)
    }

    // API Access authorization methods (using unified referral_codes table)
    pub async fn bind_invite_code(&self, code: &str, wallet_address: &str) -> Result<bool> {
        // Check if the code exists and is active
        let existing_code = self.get_referral_code_by_code(code).await?;

        match existing_code {
            Some(ref_code) if ref_code.status == "active" => {
                // Start a transaction to ensure atomicity
                let mut tx = self.pool.begin().await?;

                // Mark the code as used
                let used_at = Utc::now().timestamp();
                let result = sqlx::query!(
                    r#"
                    UPDATE referral_codes
                    SET status = 'used', used_by_wallet = $1, used_at = $2
                    WHERE code = $3 AND status = 'active'
                    "#,
                    wallet_address,
                    used_at,
                    code
                )
                .execute(&mut *tx)
                .await?;

                if result.rows_affected() > 0 {
                    // Create referral relationship
                    let referrer_wallet = &ref_code.owner_wallet;
                    let referee_wallet = wallet_address;

                    // Insert referral relationship (ignore if already exists)
                    let _ = sqlx::query!(
                        r#"
                        INSERT INTO referrals (referrer_wallet, referee_wallet, created_at)
                        VALUES ($1, $2, $3)
                        ON CONFLICT (referee_wallet) DO NOTHING
                        "#,
                        referrer_wallet,
                        referee_wallet,
                        used_at
                    )
                    .execute(&mut *tx)
                    .await?;

                    // Commit the transaction
                    tx.commit().await?;
                    Ok(true)
                } else {
                    // Rollback if code update failed
                    tx.rollback().await?;
                    Ok(false)
                }
            }
            _ => Ok(false), // Code doesn't exist or already used
        }
    }

    pub async fn get_all_referrals(&self) -> Result<Vec<Referral>> {
        let rows = sqlx::query_as!(
            Referral,
            r#"
            SELECT referrer_wallet, referee_wallet, created_at
            FROM referrals
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(rows)
    }

    pub async fn get_used_referral_code_by_wallet(
        &self,
        wallet_address: &str,
    ) -> Result<Option<ReferralCode>> {
        let row = sqlx::query_as!(
            ReferralCode,
            r#"
            SELECT id, owner_wallet, code, status, used_by_wallet, used_at, created_at
            FROM referral_codes
            WHERE used_by_wallet = $1 AND status = 'used'
            ORDER BY used_at DESC
            LIMIT 1
            "#,
            wallet_address
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row)
    }
}

/// Reward types for referral system
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RewardType {
    Signup,       // One-time reward when referee completes first trade
    TradingTier1, // Tier 1 trading reward (direct referrer gets from referee)
    TradingTier2, // Tier 2 trading reward (second level referrer gets from referee)
    TradingTier3, // Tier 3 trading reward (third level referrer gets from referee)
}

impl RewardType {
    pub fn as_str(&self) -> &'static str {
        match self {
            RewardType::Signup => "signup",
            RewardType::TradingTier1 => "trading_tier1",
            RewardType::TradingTier2 => "trading_tier2",
            RewardType::TradingTier3 => "trading_tier3",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "signup" => Some(RewardType::Signup),
            "trading_tier1" => Some(RewardType::TradingTier1),
            "trading_tier2" => Some(RewardType::TradingTier2),
            "trading_tier3" => Some(RewardType::TradingTier3),
            _ => None,
        }
    }
}

/// Reward configuration using environment variables
pub struct RewardConfig;

impl RewardConfig {
    pub fn signup_reward_usd() -> Decimal {
        crate::config::Config::get().referral_signup_reward_usd
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;
    use std::str::FromStr;

    #[test]
    fn test_reward_type_as_str() {
        assert_eq!(RewardType::Signup.as_str(), "signup");
        assert_eq!(RewardType::TradingTier1.as_str(), "trading_tier1");
        assert_eq!(RewardType::TradingTier2.as_str(), "trading_tier2");
        assert_eq!(RewardType::TradingTier3.as_str(), "trading_tier3");
    }

    #[test]
    fn test_reward_type_from_str() {
        assert_eq!(RewardType::from_str("signup"), Some(RewardType::Signup));
        assert_eq!(RewardType::from_str("trading_tier1"), Some(RewardType::TradingTier1));
        assert_eq!(RewardType::from_str("trading_tier2"), Some(RewardType::TradingTier2));
        assert_eq!(RewardType::from_str("trading_tier3"), Some(RewardType::TradingTier3));
        assert_eq!(RewardType::from_str("invalid"), None);
    }

    #[test]
    fn test_referral_chain_new() {
        let chain = ReferralChain::new();
        assert!(chain.tier1_referrer.is_none());
        assert!(chain.tier2_referrer.is_none());
        assert!(chain.tier3_referrer.is_none());
    }

    #[test]
    fn test_referral_chain_get_referrer_at_tier() {
        let mut chain = ReferralChain::new();
        chain.tier1_referrer = Some("wallet1".to_string());
        chain.tier2_referrer = Some("wallet2".to_string());
        chain.tier3_referrer = Some("wallet3".to_string());

        assert_eq!(chain.get_referrer_at_tier(1), Some(&"wallet1".to_string()));
        assert_eq!(chain.get_referrer_at_tier(2), Some(&"wallet2".to_string()));
        assert_eq!(chain.get_referrer_at_tier(3), Some(&"wallet3".to_string()));
        assert_eq!(chain.get_referrer_at_tier(4), None);
        assert_eq!(chain.get_referrer_at_tier(0), None);
    }

    #[test]
    fn test_multi_tier_percentage_calculations() {
        let trading_fee = Decimal::from_str("100.0").unwrap(); // $100 trading fee

        // Test tier percentages as specified in requirements
        let tier1_percentage = Decimal::from_str("0.20").unwrap(); // 20%
        let tier2_percentage = Decimal::from_str("0.024").unwrap(); // 2.4%
        let tier3_percentage = Decimal::from_str("0.016").unwrap(); // 1.6%

        let tier1_reward = trading_fee * tier1_percentage;
        let tier2_reward = trading_fee * tier2_percentage;
        let tier3_reward = trading_fee * tier3_percentage;

        assert_eq!(tier1_reward, Decimal::from_str("20.0").unwrap());
        assert_eq!(tier2_reward, Decimal::from_str("2.4").unwrap());
        assert_eq!(tier3_reward, Decimal::from_str("1.6").unwrap());
    }

    #[test]
    fn test_tier_percentage_relationships() {
        // Verify that tier2 is 12% of tier1 and tier3 is 8% of tier1
        let tier1_percentage = Decimal::from_str("0.20").unwrap(); // 20%
        let tier2_percentage = Decimal::from_str("0.024").unwrap(); // 2.4%
        let tier3_percentage = Decimal::from_str("0.016").unwrap(); // 1.6%

        let tier2_ratio = tier2_percentage / tier1_percentage;
        let tier3_ratio = tier3_percentage / tier1_percentage;

        // tier2 should be 12% of tier1 (0.024 / 0.20 = 0.12)
        assert_eq!(tier2_ratio, Decimal::from_str("0.12").unwrap());

        // tier3 should be 8% of tier1 (0.016 / 0.20 = 0.08)
        assert_eq!(tier3_ratio, Decimal::from_str("0.08").unwrap());
    }
}
