use std::str::FromStr;

use anyhow::Result;

use serde::{Deserialize, Serialize};
use serde_json::Value;
use solana_sdk::pubkey::Pubkey;

use super::db::Database;

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct DbSetting {
    pub wallet_address: Pubkey,
    pub json_settings: Value,
}

impl Database {
    pub async fn insert_or_update_setting(&self, setting: DbSetting) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO settings (
                wallet_address, json_settings
            )
            VALUES ($1, $2)
            ON CONFLICT (wallet_address) DO UPDATE SET json_settings = $2
            "#,
            setting.wallet_address.to_string(),
            setting.json_settings
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_setting(&self, wallet_address: &str) -> Result<Option<DbSetting>> {
        let record = sqlx::query!(
            r#"
            SELECT * FROM settings WHERE wallet_address = $1
            "#,
            wallet_address
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(record) = record {
            Ok(Some(DbSetting {
                wallet_address: Pubkey::from_str(&record.wallet_address)?,
                json_settings: record.json_settings,
            }))
        } else {
            Ok(None)
        }
    }
}
