use std::collections::HashMap;

use anyhow::Result;
use chrono::Utc;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use superstack_data::postgres::enums::Chain;

use crate::models::{DbTokenInfo, StorageState};

#[derive(<PERSON>lone, Debug, Serialize, Deserialize)]
pub struct TokenInfo {
    pub mint: String,
    pub name: String,
    pub symbol: String,
    pub image: Option<String>,
    pub description: Option<String>,
    pub usd_price: f64,
}

pub async fn get_tokens_info(
    state: &StorageState,
    tokens: &[Pubkey],
) -> Result<HashMap<Pubkey, TokenInfo>> {
    // TODO: impl batch
    // TODO: update token info in background

    let sol_price = crate::utils::get_sol_price().await;

    let mut token_infos = HashMap::new();
    for token_mint in tokens {
        // Try to find token across all chains
        let mut token_found = None;
        for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
            if let Ok(Some(token)) =
                state.indexer_data_provider.get_token(chain, &token_mint.to_string()).await
            {
                token_found = Some(token);
                break;
            }
        }

        if let Some(token_statistic) = token_found {
            let token_info = TokenInfo {
                mint: token_mint.to_string(),
                name: token_statistic.name,
                symbol: token_statistic.symbol,
                image: token_statistic.image,
                description: token_statistic.description,
                usd_price: token_statistic.usd_price,
            };
            token_infos.insert(*token_mint, token_info);
        } else {
            match get_token_info(state, token_mint).await {
                Ok(token_info) => {
                    token_infos.insert(*token_mint, token_info);
                }
                Err(e) => {
                    tracing::error!("Error fetching token info for {:?}: {:?}", token_mint, e);
                }
            }
        }
    }

    Ok(token_infos)
}

async fn get_token_info(state: &StorageState, token_mint: &Pubkey) -> Result<TokenInfo> {
    let token = state.get_token_info(&token_mint.to_string()).await?;

    if let Some(token) = token {
        let token_info = TokenInfo {
            mint: token.mint,
            name: token.name,
            symbol: token.symbol,
            image: token.image,
            description: token.description,
            usd_price: token.usd_price,
        };
        Ok(token_info)
    } else {
        let rpc_client = crate::utils::get_rpc_client();
        let token_info =
            superstack_data::token::solana::metadata::fetch_token_metadata(*token_mint, rpc_client)
                .await
                .map_err(|e| anyhow::anyhow!("Error fetching token metadata: {}", e))?;

        let (image, description) = if token_info.uri.is_empty() {
            (None, None)
        } else {
            let reqwest_client = crate::utils::get_reqwest_client();
            match superstack_data::token::solana::metaplex::fetch_metaplex_file(
                &token_info.uri,
                reqwest_client,
            )
            .await
            {
                Ok(metaplex_json) => (metaplex_json.image, metaplex_json.description),
                Err(e) => {
                    tracing::error!("Error fetching metaplex file for {}: {}", token_mint, e);
                    (None, None)
                }
            }
        };

        let price = {
            match crate::jupiter::price::get_price(&[&token_mint.to_string()]).await {
                Ok(prices) => match prices.first() {
                    Some(price) => price.price,
                    None => {
                        tracing::error!("Price not found for {:?}", token_mint);
                        0.0
                    }
                },
                Err(e) => {
                    tracing::error!("Error fetching price for {:?}: {:?}", token_mint, e);
                    0.0
                }
            }
        };

        let db_token_info = DbTokenInfo {
            mint: token_mint.to_string(),
            name: token_info.name.clone(),
            symbol: token_info.symbol.clone(),
            image: image.clone(),
            description: description.clone(),
            usd_price: price,
            updated_at: Utc::now().timestamp(),
        };
        state.insert_token_info(db_token_info).await?;

        let token_info = TokenInfo {
            mint: token_mint.to_string(),
            name: token_info.name,
            symbol: token_info.symbol,
            image,
            description,
            usd_price: price,
        };

        Ok(token_info)
    }
}
