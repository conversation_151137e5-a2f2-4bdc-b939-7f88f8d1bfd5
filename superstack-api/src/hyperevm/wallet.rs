use std::str::FromStr;

use alloy::{
    primitives::{Address, U256},
    providers::Provider,
};
use anyhow::Result;
use serde::{Deserialize, Serialize};
use superstack_data::postgres::enums::Chain;

use crate::utils::get_provider;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TokenBalance {
    pub token_address: String,
    pub balance: String,
    pub ui_amount: f64,
    pub decimals: u8,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HyperEvmWalletInfo {
    pub address: String,
    pub native_balance: String,
    pub native_ui_amount: f64,
    pub token_balances: Vec<TokenBalance>,
}

/// Simplified HyperEVM wallet utilities
pub struct HyperEvmWalletManager;

impl HyperEvmWalletManager {
    /// Get complete wallet information including native and token balances
    pub async fn get_wallet_info(
        wallet_address: &str,
        token_addresses: &[String],
    ) -> Result<HyperEvmWalletInfo> {
        let provider = get_provider();
        let address = Address::from_str(wallet_address)?;

        // Get native balance
        let native_balance = provider.get_balance(address).await?;
        let native_ui_amount = Self::to_ui_amount(native_balance, 18); // HYPE has 18 decimals

        // Get token balances
        let mut token_balances = Vec::new();
        for token_address in token_addresses {
            if let Ok(token_addr) = Address::from_str(token_address) {
                if let Ok(balance) = Self::get_erc20_balance(&provider, address, token_addr).await {
                    token_balances.push(balance);
                }
            }
        }

        Ok(HyperEvmWalletInfo {
            address: wallet_address.to_string(),
            native_balance: native_balance.to_string(),
            native_ui_amount,
            token_balances,
        })
    }

    /// Get ERC20 token balance (simplified)
    async fn get_erc20_balance(
        provider: &impl Provider,
        wallet_addr: Address,
        token_addr: Address,
    ) -> Result<TokenBalance> {
        use superstack_data::hyperevm::erc20::IERC20;

        let token_contract = IERC20::new(token_addr, provider);
        let balance = token_contract.balanceOf(wallet_addr).call().await?;
        let decimals = token_contract.decimals().call().await?;
        let ui_amount = Self::to_ui_amount(balance, decimals);

        Ok(TokenBalance {
            token_address: token_addr.to_string(),
            balance: balance.to_string(),
            ui_amount,
            decimals,
        })
    }

    /// Convert raw amount to UI amount
    fn to_ui_amount(amount: U256, decimals: u8) -> f64 {
        let divisor = U256::from(10).pow(U256::from(decimals));
        // Convert to string first, then parse to f64 for precision
        let amount_str = amount.to_string();
        let divisor_str = divisor.to_string();
        let amount_f64: f64 = amount_str.parse().unwrap_or(0.0);
        let divisor_f64: f64 = divisor_str.parse().unwrap_or(1.0);
        amount_f64 / divisor_f64
    }

    /// Convert UI amount to raw amount
    pub fn from_ui_amount(ui_amount: f64, decimals: u8) -> U256 {
        let multiplier = U256::from(10).pow(U256::from(decimals));
        let multiplier_str = multiplier.to_string();
        let multiplier_f64: f64 = multiplier_str.parse().unwrap_or(1.0);
        let raw_amount = (ui_amount * multiplier_f64) as u128;
        U256::from(raw_amount)
    }

    /// Get chain info
    pub fn get_chain() -> Chain {
        Chain::HyperEvm
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_native_balance() {
        // TODO: Add tests for native balance retrieval
    }

    #[tokio::test]
    async fn test_get_token_balance() {
        // TODO: Add tests for token balance retrieval
    }

    #[test]
    fn test_amount_conversion() {
        let ui_amount = 1.5;
        let decimals = 18;

        let raw_amount = HyperEvmWalletManager::from_ui_amount(ui_amount, decimals);
        let converted_back = HyperEvmWalletManager::to_ui_amount(raw_amount, decimals);

        assert!((ui_amount - converted_back).abs() < 0.0001);
    }
}
