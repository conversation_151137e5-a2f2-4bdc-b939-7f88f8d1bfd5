[package]
name = "superstack-api"
version = "0.1.0"
edition = "2021"

[lib]
name = "superstack_api"
path = "src/lib.rs"

[dependencies]
tokio = { workspace = true }
axum = { workspace = true }
tower-http = { workspace = true, features = ["fs"] }
tower = { workspace = true }
reqwest = { workspace = true }
rustls = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
sqlx = { workspace = true }

time = { workspace = true }
chrono = { workspace = true }

anyhow = { workspace = true }
dotenv = { workspace = true }
base64 = { workspace = true }
bincode = { workspace = true }
hmac-sha256 = { workspace = true }
url = { workspace = true }
urlencoding = { workspace = true }
futures = { workspace = true }
hex = { workspace = true }
sha2 = { workspace = true }
sha3 = { workspace = true }

solana-client = { workspace = true }
solana-sdk = { workspace = true }
solana-transaction-status = { workspace = true }
solana-account-decoder-client-types = { workspace = true }
spl-associated-token-account = { workspace = true }
spl-token = { workspace = true }
borsh = { workspace = true }
alloy = { workspace = true }
uuid = { workspace = true }
rust_decimal = { workspace = true }
rand = { workspace = true }
bigdecimal = { workspace = true }

# Image processing
image = "0.25"

teloxide = { workspace = true }

superstack-data = { path = "../superstack-data" }

[dev-dependencies]

[[bin]]
name = "api"
path = "src/main.rs"
