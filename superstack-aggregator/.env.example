PORT=3000

BLOCK_CONSUMER_CONCURRENCY=200
HOLDERS_CONSUMER_CONCURRENCY=100
TOKEN_CONSUMER_CONCURRENCY=60
POOL_CONSUMER_CONCURRENCY=20

STATISTIC_UPDATER_CONCURRENCY=100
STATISTIC_UPDATE_INTERVAL=300


SOLANA_RPC_URL=https://rpc.test.superstack.xyz


POSTGRES_INDEXER_DATABASE_URL=postgresql://superstack-indexer:password@127.0.0.1:5432/superstack-indexer
POSTGRES_INDEXER_MAX_CONNECTIONS=300
POSTGRES_INDEXER_NEED_MIGRATE=false

STORE_PRICE_HISTORY=true

REDIS_URL=rediss://default:<EMAIL>:14053

KAFKA_BOOTSTRAP_SERVERS=pkc-ldvr1.asia-southeast1.gcp.confluent.cloud:9092
KAFKA_API_KEY=KV7EDZDBGVNCBR32
KAFKA_API_SECRET=FZNQulI7Rs77h+XaHPylbXdWLZzC2FMiYoUG58vGuUemIiHAAN7UVBqeIRFWz5jq

COINGECKO_API_KEY=CG-T2Dk49gHP6uEZE1cotuJCezy

STATIC_DIR=/static-bucket
