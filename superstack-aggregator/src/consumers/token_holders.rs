use std::{
    sync::Arc,
    time::{Duration, Instant},
};

use anyhow::Result;
use futures::StreamExt;
use superstack_data::kafka::{
    consumer::ConsumerError, topics::TokenHoldersMsg, ConfluentConfig, KafkaConsumer,
    KafkaConsumerConfig,
};
use tokio::{sync::<PERSON><PERSON>ph<PERSON>, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};

use crate::{aggregator::Aggregator, config::Config};

const TOKEN_HOLDERS_CONSUMER_GROUP_ID: &str = "superstack-aggregator-token-holders-consumer";

pub struct TokenHoldersConsumer;

impl TokenHoldersConsumer {
    pub fn run() -> Jo<PERSON><PERSON><PERSON><PERSON><()> {
        tokio::spawn(async move {
            let mut retry_count = 0;
            let mut consecutive_failures = 0;

            loop {
                let start_time = Instant::now();

                match Self::run_consumer().await {
                    Ok(()) => {
                        // Consumer exited normally, reset counters
                        retry_count = 0;
                        consecutive_failures = 0;
                        tracing::warn!("TokenHolders consumer exited normally, restarting...");
                    }
                    Err(e) => {
                        // Check if this is a deserialize-related error (corrupted data)
                        let is_deserialize_error = {
                            // Try to downcast to our specific error type
                            if let Some(data_error) =
                                e.downcast_ref::<superstack_data::error::Error>()
                            {
                                matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::BincodeError(_)
                                    )
                                ) || matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::CiboriumError(_)
                                    )
                                )
                            } else {
                                // Fallback to string matching for other potential bincode errors
                                let error_msg = format!("{}", e);
                                error_msg.contains("bincode") ||
                                    error_msg.contains("ciborium") ||
                                    error_msg.contains("deserialize")
                            }
                        };

                        if is_deserialize_error {
                            tracing::warn!("Deserialize error detected: {}. This may indicate corrupted messages in Kafka topic.", e);
                            consecutive_failures += 1;

                            // For deserialize errors, we want to restart more aggressively
                            // but with exponential backoff to avoid overwhelming the system
                            if consecutive_failures > 20 {
                                tracing::error!(
                                    "Too many consecutive deserialize failures ({}). Exiting consumer.",
                                    consecutive_failures
                                );
                                break;
                            }

                            let backoff_seconds = std::cmp::min(consecutive_failures, 60);
                            tracing::info!(
                                "Restarting consumer after {} seconds due to data corruption...",
                                backoff_seconds
                            );
                            tokio::time::sleep(Duration::from_secs(backoff_seconds)).await;
                        } else {
                            // For other types of errors, use the original retry logic
                            // If the consumer has been running for more than 1 hour, reset the
                            // retry count
                            if start_time.elapsed() > Duration::from_secs(3600) {
                                retry_count = 0;
                            }

                            retry_count += 1;
                            consecutive_failures = 0; // Reset since this is not a bincode error

                            if retry_count > 5 {
                                tracing::error!(
                                    "Error running token holders consumer after {} retries: {}",
                                    retry_count,
                                    e
                                );
                                break;
                            }

                            tracing::warn!(
                                "Consumer error (attempt {}): {}. Retrying in {} seconds...",
                                retry_count,
                                e,
                                retry_count
                            );
                            tokio::time::sleep(Duration::from_secs(retry_count)).await;
                        }
                    }
                }
            }

            tracing::error!("TokenHolders consumer has stopped permanently");
        })
    }

    async fn run_consumer() -> Result<()> {
        let data_config = Config::get().data_config;
        let confluent_config = ConfluentConfig::new(
            data_config.kafka_bootstrap_servers.clone(),
            data_config.kafka_api_key.clone(),
            data_config.kafka_api_secret.clone(),
        );
        let auto_commit_interval_ms = Config::get().kafka_auto_commit_interval_secs * 1000;
        let consumer_config = KafkaConsumerConfig::new_earliest_with_auto_commit(
            TOKEN_HOLDERS_CONSUMER_GROUP_ID,
            auto_commit_interval_ms,
        );
        let consumer = KafkaConsumer::new(&confluent_config, &consumer_config)?;

        let max_concurrency = Config::get().holders_consumer_concurrency;
        let semaphore = Arc::new(Semaphore::new(max_concurrency));

        consumer.subscribe_topic::<TokenHoldersMsg>()?;
        let mut message_stream = consumer.stream();
        tracing::info!(
            "Starting message consumption for group: {}",
            TOKEN_HOLDERS_CONSUMER_GROUP_ID
        );

        let mut retry_count = 0;
        let mut skipped_messages = 0;
        let mut processed_messages = 0;

        let mut received_timestamp_millis = 0;

        while let Some(message_result) = message_stream.next().await {
            match message_result {
                Ok(borrowed_message) => {
                    match consumer.process_message::<TokenHoldersMsg>(&borrowed_message) {
                        Ok(message) => {
                            let permit = semaphore.clone().acquire_owned().await?;
                            processed_messages += 1;
                            received_timestamp_millis = message
                                .holders
                                .first()
                                .map(|s| s.update_timestamp_millis)
                                .unwrap_or(0);

                            tokio::spawn(async move {
                                if let Err(e) = Self::handle_token_holders(message).await {
                                    tracing::error!("Error handling token holders: {:?}", e);
                                }

                                // Drop the permit to release the semaphore
                                drop(permit);
                            });

                            // Reset retry count on successful processing
                            retry_count = 0;
                        }
                        Err(_) => {
                            // Skip this corrupted message and continue
                            skipped_messages += 1;
                            continue;
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {}", e);
                    retry_count += 1;
                    if retry_count > 10 {
                        return Err(ConsumerError::ChannelConsumerError(format!(
                            "Error receiving message: {}",
                            e
                        ))
                        .into());
                    }
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }

            // Log statistics every 1000 messages or if there are any skipped messages
            let total_messages = processed_messages + skipped_messages;
            if (total_messages % 1000 == 0 && total_messages > 0) ||
                (skipped_messages > 0 && total_messages % 100 == 0)
            {
                let time_diff = chrono::Utc::now().timestamp_millis() - received_timestamp_millis;
                let time_diff = Duration::from_millis(time_diff.max(0) as u64);
                tracing::warn!(
                    "TokenHolders message consumption stats for group '{}': processed={}, skipped={}, total={}, time_diff={:?}",
                    TOKEN_HOLDERS_CONSUMER_GROUP_ID,
                    processed_messages,
                    skipped_messages,
                    total_messages,
                    time_diff
                );
            }
        }

        Ok(())
    }

    pub async fn handle_token_holders(token_holders_msg: TokenHoldersMsg) -> Result<()> {
        Aggregator::aggregate_holders(token_holders_msg.holders).await?;

        Ok(())
    }
}
