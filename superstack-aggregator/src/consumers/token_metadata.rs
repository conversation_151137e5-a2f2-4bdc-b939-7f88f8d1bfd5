use std::{
    sync::Arc,
    time::{Duration, Instant},
};

use anyhow::Result;
use futures::StreamExt;
use superstack_data::{
    kafka::{ConfluentConfig, ConsumerError, KafkaConsumer, KafkaConsumerConfig},
    postgres::{
        indexer::{token_metadata::TokenMetadataV1, TokenMetadata},
        PostgresDatabase,
    },
    redis::RedisClient,
};
use tokio::{sync::Semaphore, task::Jo<PERSON><PERSON><PERSON><PERSON>};

use crate::{config::Config, updater::token_metadata::TokenMetadataUpdater};

const TOKEN_METADATA_CONSUMER_GROUP_ID: &'static str =
    "superstack-aggregator-token-metadata-consumer";

pub struct TokenMetadataConsumer {
    updater: TokenMetadataUpdater,
}

impl TokenMetadataConsumer {
    pub fn new() -> Self {
        Self { updater: TokenMetadataUpdater::new() }
    }

    pub fn run(self) -> <PERSON><PERSON><PERSON><PERSON><PERSON><()> {
        tokio::spawn(async move {
            let mut retry_count = 0;
            let mut consecutive_failures = 0;

            loop {
                let start_time = Instant::now();

                match self.run_consumer().await {
                    Ok(()) => {
                        // Consumer exited normally, reset counters
                        retry_count = 0;
                        consecutive_failures = 0;
                        tracing::warn!("TokenMetadata consumer exited normally, restarting...");
                    }
                    Err(e) => {
                        // Check if this is a deserialize-related error (corrupted data)
                        let is_deserialize_error = {
                            // Try to downcast to our specific error type
                            if let Some(data_error) =
                                e.downcast_ref::<superstack_data::error::Error>()
                            {
                                matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::BincodeError(_)
                                    )
                                ) || matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::CiboriumError(_)
                                    )
                                )
                            } else {
                                // Fallback to string matching for other potential bincode errors
                                let error_msg = format!("{}", e);
                                error_msg.contains("bincode") ||
                                    error_msg.contains("ciborium") ||
                                    error_msg.contains("deserialize")
                            }
                        };

                        if is_deserialize_error {
                            tracing::warn!("Deserialize error detected: {}. This may indicate corrupted messages in Kafka topic.", e);
                            consecutive_failures += 1;

                            // For deserialize errors, we want to restart more aggressively
                            // but with exponential backoff to avoid overwhelming the system
                            if consecutive_failures > 20 {
                                tracing::error!(
                                    "Too many consecutive deserialize failures ({}). Exiting consumer.",
                                    consecutive_failures
                                );
                                break;
                            }

                            let backoff_seconds = std::cmp::min(consecutive_failures, 60);
                            tracing::info!(
                                "Restarting consumer after {} seconds due to data corruption...",
                                backoff_seconds
                            );
                            tokio::time::sleep(Duration::from_secs(backoff_seconds)).await;
                        } else {
                            // For other types of errors, use the original retry logic
                            // If the consumer has been running for more than 1 hour, reset the
                            // retry count
                            if start_time.elapsed() > Duration::from_secs(3600) {
                                retry_count = 0;
                            }

                            retry_count += 1;
                            consecutive_failures = 0; // Reset since this is not a bincode error

                            if retry_count > 5 {
                                tracing::error!(
                                    "Error running token metadata consumer after {} retries: {}",
                                    retry_count,
                                    e
                                );
                                break;
                            }

                            tracing::warn!(
                                "Consumer error (attempt {}): {}. Retrying in {} seconds...",
                                retry_count,
                                e,
                                retry_count
                            );
                            tokio::time::sleep(Duration::from_secs(retry_count)).await;
                        }
                    }
                }
            }

            tracing::error!("IndexerBlock consumer has stopped permanently");
        })
    }

    async fn run_consumer(&self) -> Result<()> {
        let data_config = Config::get().data_config;
        let confluent_config = ConfluentConfig::new(
            data_config.kafka_bootstrap_servers.clone(),
            data_config.kafka_api_key.clone(),
            data_config.kafka_api_secret.clone(),
        );
        let auto_commit_interval_ms = Config::get().kafka_auto_commit_interval_secs * 1000;
        let consumer_config = KafkaConsumerConfig::new_earliest_with_auto_commit(
            TOKEN_METADATA_CONSUMER_GROUP_ID,
            auto_commit_interval_ms,
        );
        let consumer = KafkaConsumer::new(&confluent_config, &consumer_config)?;

        let max_concurrency = Config::get().token_consumer_concurrency;
        let semaphore = Arc::new(Semaphore::new(max_concurrency));

        consumer.subscribe_topic::<TokenMetadata>()?;
        let mut message_stream = consumer.stream();
        tracing::info!(
            "Starting message consumption for group: {}",
            TOKEN_METADATA_CONSUMER_GROUP_ID
        );

        let mut retry_count = 0;
        let mut skipped_messages = 0;
        let mut processed_messages = 0;

        while let Some(message_result) = message_stream.next().await {
            match message_result {
                Ok(borrowed_message) => {
                    let mut result = consumer.process_message::<TokenMetadata>(&borrowed_message);
                    if result.is_err() {
                        result = consumer
                            .process_message::<TokenMetadataV1>(&borrowed_message)
                            .map(|v| v.into());
                    }
                    match result {
                        Ok(message) => {
                            let permit = semaphore.clone().acquire_owned().await?;
                            processed_messages += 1;

                            let updater = self.updater.clone();
                            tokio::spawn(async move {
                                if let Err(e) = Self::handle_token_metadata(updater, &message).await
                                {
                                    tracing::warn!("Error handling token metadata: {:?}", e);
                                } else {
                                    tracing::debug!(
                                        "processed token {} {}",
                                        message.chain,
                                        message.address
                                    );
                                }

                                // Drop the permit to release the semaphore
                                drop(permit);
                            });

                            // Reset retry count on successful processing
                            retry_count = 0;
                        }
                        Err(_) => {
                            // Skip this corrupted message and continue
                            skipped_messages += 1;
                            continue;
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {}", e);
                    retry_count += 1;
                    if retry_count > 10 {
                        return Err(ConsumerError::ChannelConsumerError(format!(
                            "Error receiving message: {}",
                            e
                        ))
                        .into());
                    }
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }

            // Log statistics every 100 messages or if there are any skipped messages
            let total_messages = processed_messages + skipped_messages;
            if (total_messages % 100 == 0 && total_messages > 0) ||
                (skipped_messages > 0 && total_messages % 100 == 0)
            {
                tracing::warn!(
                    "TokenMetadata message consumption stats for group '{}': processed={}, skipped={}, total={}",
                    TOKEN_METADATA_CONSUMER_GROUP_ID,
                    processed_messages,
                    skipped_messages,
                    total_messages
                );
            }
        }

        Ok(())
    }

    async fn handle_token_metadata(
        updater: TokenMetadataUpdater,
        msg: &TokenMetadata,
    ) -> Result<()> {
        let redis_client = RedisClient::get_instance().await;

        // 1. get from redis cache
        let in_cache =
            redis_client.get_token_metadata_or_update_from_db(msg.chain, &msg.address).await?;

        // 2. check db
        let db = PostgresDatabase::get_indexer_db().await;
        let in_db = db.get_token_metadata(msg.chain, &msg.address).await?;
        if in_db.map(|v| v.is_missing_misc_info()).unwrap_or_default() {
            tracing::debug!(
                "token {} {} missing misc info, processing ..",
                in_cache.chain,
                in_cache.address
            );
            updater.update_token_metadata_misc_info(in_cache).await?;
        }

        Ok(())
    }
}
