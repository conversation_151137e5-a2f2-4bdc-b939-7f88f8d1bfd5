use std::{
    sync::Arc,
    time::{Duration, Instant},
};

use anyhow::Result;
use futures::StreamExt;
use superstack_data::{
    kafka::{consumer::ConsumerError, ConfluentConfig, Ka<PERSON><PERSON><PERSON>onsumer, KafkaConsumerConfig},
    postgres::indexer::PoolMetadata,
};
use tokio::{sync::Semaphore, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};

use crate::config::Config;

const POOL_METADATA_CONSUMER_GROUP_ID: &str = "superstack-aggregator-pool-metadata-consumer";

pub struct PoolMetadataConsumer;

impl PoolMetadataConsumer {
    pub fn run() -> Jo<PERSON><PERSON><PERSON><PERSON><()> {
        tokio::spawn(async move {
            let mut retry_count = 0;
            let mut consecutive_failures = 0;

            loop {
                let start_time = Instant::now();

                match Self::run_consumer().await {
                    Ok(()) => {
                        // Consumer exited normally, reset counters
                        retry_count = 0;
                        consecutive_failures = 0;
                        tracing::warn!("PoolMetadata consumer exited normally, restarting...");
                    }
                    Err(e) => {
                        // Check if this is a deserialize-related error (corrupted data)
                        let is_deserialize_error = {
                            // Try to downcast to our specific error type
                            if let Some(data_error) =
                                e.downcast_ref::<superstack_data::error::Error>()
                            {
                                matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::BincodeError(_)
                                    )
                                ) || matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::CiboriumError(_)
                                    )
                                ) || matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::SerdeJsonError(_)
                                    )
                                )
                            } else {
                                // Fallback to string matching for other potential bincode errors
                                let error_msg = format!("{}", e);
                                error_msg.contains("bincode") ||
                                    error_msg.contains("ciborium") ||
                                    error_msg.contains("deserialize") ||
                                    error_msg.contains("serde")
                            }
                        };

                        if is_deserialize_error {
                            tracing::warn!("Deserialize error detected: {}. This may indicate corrupted messages in Kafka topic.", e);
                            consecutive_failures += 1;

                            // For deserialize errors, we want to restart more aggressively
                            // but with exponential backoff to avoid overwhelming the system
                            if consecutive_failures > 20 {
                                tracing::error!(
                                    "Too many consecutive deserialize failures ({}). Exiting consumer.",
                                    consecutive_failures
                                );
                                break;
                            }

                            let backoff_seconds = std::cmp::min(consecutive_failures, 60);
                            tracing::info!(
                                "Restarting consumer after {} seconds due to data corruption...",
                                backoff_seconds
                            );
                            tokio::time::sleep(Duration::from_secs(backoff_seconds)).await;
                        } else {
                            // For other types of errors, use the original retry logic
                            // If the consumer has been running for more than 1 hour, reset the
                            // retry count
                            if start_time.elapsed() > Duration::from_secs(3600) {
                                retry_count = 0;
                            }

                            retry_count += 1;
                            consecutive_failures = 0; // Reset since this is not a bincode error

                            if retry_count > 5 {
                                tracing::error!(
                                    "Error running pool metadata consumer after {} retries: {}",
                                    retry_count,
                                    e
                                );
                                break;
                            }

                            tracing::warn!(
                                "Consumer error (attempt {}): {}. Retrying in {} seconds...",
                                retry_count,
                                e,
                                retry_count
                            );
                            tokio::time::sleep(Duration::from_secs(retry_count)).await;
                        }
                    }
                }
            }

            tracing::error!("PoolMetadata consumer has stopped permanently");
        })
    }

    async fn run_consumer() -> Result<()> {
        let data_config = Config::get().data_config;
        let confluent_config = ConfluentConfig::new(
            data_config.kafka_bootstrap_servers.clone(),
            data_config.kafka_api_key.clone(),
            data_config.kafka_api_secret.clone(),
        );
        let auto_commit_interval_ms = Config::get().kafka_auto_commit_interval_secs * 1000;
        let consumer_config = KafkaConsumerConfig::new_earliest_with_auto_commit(
            POOL_METADATA_CONSUMER_GROUP_ID,
            auto_commit_interval_ms,
        );
        let consumer = KafkaConsumer::new(&confluent_config, &consumer_config)?;

        consumer.subscribe_topic::<PoolMetadata>()?;
        let mut message_stream = consumer.stream();
        tracing::info!(
            "Starting message consumption for group: {}",
            POOL_METADATA_CONSUMER_GROUP_ID
        );

        let max_concurrency = Config::get().pool_consumer_concurrency;
        let semaphore = Arc::new(Semaphore::new(max_concurrency));

        let mut retry_count = 0;
        let mut skipped_messages = 0;
        let mut processed_messages = 0;

        while let Some(message_result) = message_stream.next().await {
            match message_result {
                Ok(borrowed_message) => {
                    match consumer.process_message::<PoolMetadata>(&borrowed_message) {
                        Ok(message) => {
                            let permit = semaphore.clone().acquire_owned().await?;
                            processed_messages += 1;

                            tokio::spawn(async move {
                                if let Err(e) = Self::handle_pool_metadata(&message).await {
                                    tracing::error!("Error handling pool metadata: {:?}", e);
                                }

                                // Drop the permit to release the semaphore
                                drop(permit);
                            });

                            // Reset retry count on successful processing
                            retry_count = 0;
                        }
                        Err(_) => {
                            // Skip this corrupted message and continue
                            skipped_messages += 1;
                            continue;
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {}", e);
                    retry_count += 1;
                    if retry_count > 10 {
                        return Err(ConsumerError::ChannelConsumerError(format!(
                            "Error receiving message: {}",
                            e
                        ))
                        .into());
                    }
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }

            // Log statistics every 100 messages or if there are any skipped messages
            let total_messages = processed_messages + skipped_messages;
            if (total_messages % 100 == 0 && total_messages > 0) ||
                (skipped_messages > 0 && total_messages % 100 == 0)
            {
                tracing::warn!(
                    "PoolMetadata message consumption stats for group '{}': processed={}, skipped={}, total={}",
                    POOL_METADATA_CONSUMER_GROUP_ID,
                    processed_messages,
                    skipped_messages,
                    total_messages
                );
            }
        }

        Ok(())
    }

    async fn handle_pool_metadata(_pool_metadata: &PoolMetadata) -> Result<()> {
        // Currently we don't need to do anything with pool metadata
        Ok(())
    }
}
