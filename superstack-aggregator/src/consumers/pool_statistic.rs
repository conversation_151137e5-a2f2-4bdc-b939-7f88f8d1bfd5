use std::{
    collections::HashMap,
    time::{Duration, Instant},
};

use anyhow::Result;
use futures::StreamExt;
use rdkafka::consumer::Consumer;
use superstack_data::{
    kafka::{consumer::ConsumerError, ConfluentConfig, Kafka<PERSON>onsumer, KafkaConsumerConfig},
    postgres::{aggregator::PoolStatistic, Chain, PostgresDatabase},
};
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;

use crate::config::Config;

const POOL_STATISTIC_CONSUMER_GROUP_ID: &str = "superstack-aggregator-pool-statistic-consumer";

pub struct PoolStatisticConsumer;

impl PoolStatisticConsumer {
    pub fn run() -> Join<PERSON><PERSON>le<()> {
        tokio::spawn(async move {
            let mut retry_count = 0;
            let mut consecutive_failures = 0;

            loop {
                let start_time = Instant::now();

                match Self::run_consumer().await {
                    Ok(()) => {
                        // Consumer exited normally, reset counters
                        retry_count = 0;
                        consecutive_failures = 0;
                        tracing::warn!("Pool Statistic consumer exited normally, restarting...");
                    }
                    Err(e) => {
                        // Check if this is a deserialize-related error (corrupted data)
                        let is_deserialize_error = {
                            // Try to downcast to our specific error type
                            if let Some(data_error) =
                                e.downcast_ref::<superstack_data::error::Error>()
                            {
                                matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::BincodeError(_)
                                    )
                                ) || matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::CiboriumError(_)
                                    )
                                ) || matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::SerdeJsonError(_)
                                    )
                                )
                            } else {
                                // Fallback to string matching for other potential bincode errors
                                let error_msg = format!("{}", e);
                                error_msg.contains("bincode") ||
                                    error_msg.contains("ciborium") ||
                                    error_msg.contains("deserialize") ||
                                    error_msg.contains("serde")
                            }
                        };

                        if is_deserialize_error {
                            tracing::warn!("Deserialize error detected: {}. This may indicate corrupted messages in Kafka topic.", e);
                            consecutive_failures += 1;

                            // For deserialize errors, we want to restart more aggressively
                            // but with exponential backoff to avoid overwhelming the system
                            if consecutive_failures > 20 {
                                tracing::error!(
                                    "Too many consecutive deserialize failures ({}). Exiting consumer.",
                                    consecutive_failures
                                );
                                break;
                            }

                            let backoff_seconds = std::cmp::min(consecutive_failures, 60);
                            tracing::info!(
                                "Restarting consumer after {} seconds due to data corruption...",
                                backoff_seconds
                            );
                            tokio::time::sleep(Duration::from_secs(backoff_seconds)).await;
                        } else {
                            // For other types of errors, use the original retry logic
                            // If the consumer has been running for more than 1 hour, reset the
                            // retry count
                            if start_time.elapsed() > Duration::from_secs(3600) {
                                retry_count = 0;
                            }

                            retry_count += 1;
                            consecutive_failures = 0; // Reset since this is not a bincode error

                            if retry_count > 5 {
                                tracing::error!(
                                    "Error running pool statistic consumer after {} retries: {}",
                                    retry_count,
                                    e
                                );
                                break;
                            }

                            tracing::warn!(
                                "Consumer error (attempt {}): {}. Retrying in {} seconds...",
                                retry_count,
                                e,
                                retry_count
                            );
                            tokio::time::sleep(Duration::from_secs(retry_count)).await;
                        }
                    }
                }
            }

            tracing::error!("Pool Statistic consumer has stopped permanently");
        })
    }

    async fn run_consumer() -> Result<()> {
        let data_config = Config::get().data_config;
        let confluent_config = ConfluentConfig::new(
            data_config.kafka_bootstrap_servers.clone(),
            data_config.kafka_api_key.clone(),
            data_config.kafka_api_secret.clone(),
        );
        let consumer_config =
            KafkaConsumerConfig::new_earliest_without_auto_commit(POOL_STATISTIC_CONSUMER_GROUP_ID);
        let consumer = KafkaConsumer::new(&confluent_config, &consumer_config)?;

        consumer.subscribe_topic::<PoolStatistic>()?;
        let mut message_stream = consumer.stream();
        tracing::info!(
            "Starting message consumption for group: {}",
            POOL_STATISTIC_CONSUMER_GROUP_ID
        );

        let mut retry_count = 0;
        let mut skipped_messages = 0;
        let mut processed_messages = 0;

        let mut received_timestamp_millis = 0;

        let mut pool_statistic_buffer = PoolStatisticBuffer::new();

        while let Some(message_result) = message_stream.next().await {
            match message_result {
                Ok(borrowed_message) => {
                    match consumer.process_message::<PoolStatistic>(&borrowed_message) {
                        Ok(message) => {
                            processed_messages += 1;
                            received_timestamp_millis = message.update_timestamp_millis;

                            pool_statistic_buffer.insert_pool_statistic(message);

                            if pool_statistic_buffer.should_save() {
                                pool_statistic_buffer.save().await;

                                if let Err(commit_err) = consumer.consumer.commit_message(
                                    &borrowed_message,
                                    rdkafka::consumer::CommitMode::Async,
                                ) {
                                    tracing::error!(
                                        "Failed to commit pool statistic message: {}",
                                        commit_err
                                    );
                                }
                            }

                            // Reset retry count on successful processing
                            retry_count = 0;
                        }
                        Err(_) => {
                            // Skip this corrupted message and continue
                            skipped_messages += 1;
                            continue;
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {}", e);
                    retry_count += 1;
                    if retry_count > 10 {
                        pool_statistic_buffer.save().await;

                        return Err(ConsumerError::ChannelConsumerError(format!(
                            "Error receiving message: {}",
                            e
                        ))
                        .into());
                    }
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }

            // Log statistics every 1000 messages or if there are any skipped messages
            let total_messages = processed_messages + skipped_messages;
            if (total_messages % 1000 == 0 && total_messages > 0) ||
                (skipped_messages > 0 && total_messages % 100 == 0)
            {
                let time_diff = chrono::Utc::now().timestamp_millis() - received_timestamp_millis;
                let time_diff = Duration::from_millis(time_diff.max(0) as u64);
                tracing::warn!(
                    "PoolStatistic message consumption stats for group '{}': processed={}, skipped={}, total={}, time_diff={:?}",
                    POOL_STATISTIC_CONSUMER_GROUP_ID,
                    processed_messages,
                    skipped_messages,
                    total_messages,
                    time_diff
                );
            }
        }

        Ok(())
    }
}

#[derive(Debug)]
pub struct PoolStatisticBuffer {
    pub pools: HashMap<(Chain, String), PoolStatistic>,
}

impl PoolStatisticBuffer {
    const CHUNK_SIZE: usize = 10;
    const SAVE_LIMIT: usize = 1000;

    pub fn new() -> Self {
        Self { pools: HashMap::new() }
    }

    pub fn should_save(&self) -> bool {
        self.pools.len() > Self::SAVE_LIMIT
    }

    pub fn insert_pool_statistic(&mut self, pool_statistic: PoolStatistic) {
        let chain = pool_statistic.chain;
        let pool_address = pool_statistic.pool_address.clone();

        // Use entry API for efficient insert-or-update
        match self.pools.entry((chain, pool_address)) {
            std::collections::hash_map::Entry::Occupied(mut entry) => {
                // Compare and keep the new one
                let existing = entry.get();
                if pool_statistic.update_timestamp_millis >= existing.update_timestamp_millis {
                    entry.insert(pool_statistic);
                }
            }
            std::collections::hash_map::Entry::Vacant(entry) => {
                entry.insert(pool_statistic);
            }
        }
    }

    pub async fn save(&mut self) {
        // Drain all data atomically
        let pools = self.pools.drain().map(|(_, pool)| pool).collect::<Vec<_>>();

        // Early return if no data
        if pools.is_empty() {
            return;
        }

        let db = PostgresDatabase::get_indexer_db().await;

        // Insert pools in little chunks to avoid db lock contention
        for pool in pools.chunks(Self::CHUNK_SIZE) {
            if let Err(e) = db.insert_or_update_pool_statistics(&pool).await {
                tracing::error!("Error inserting pool statistic: {:?}", e);
            }
        }
    }
}
