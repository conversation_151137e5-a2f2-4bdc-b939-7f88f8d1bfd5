use std::time::{Duration, Instant};

use anyhow::Result;
use superstack_data::kafka::{
    consumer::ConsumerError, topics::UpdateStatisticMsg, ConfluentConfig, KafkaConsumer,
    KafkaConsumerConfig,
};
use tokio::{sync::mpsc, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};

use crate::config::Config;

const UPDATE_STATISTIC_CONSUMER_GROUP_ID: &str = "superstack-aggregator-update-statistic-consumer";

pub struct UpdateStatisticConsumer;

impl UpdateStatisticConsumer {
    pub fn run(tx: mpsc::Sender<UpdateStatisticMsg>) -> Jo<PERSON><PERSON><PERSON><PERSON><()> {
        tokio::spawn(async move {
            let mut retry_count = 0;
            let mut consecutive_failures = 0;

            loop {
                let start_time = Instant::now();

                match Self::run_consumer(tx.clone()).await {
                    Ok(()) => {
                        // Consumer exited normally, reset counters
                        retry_count = 0;
                        consecutive_failures = 0;
                        tracing::warn!("Update Statistic consumer exited normally, restarting...");
                    }
                    Err(e) => {
                        // Check if this is a deserialize-related error (corrupted data)
                        let is_deserialize_error = {
                            // Try to downcast to our specific error type
                            if let Some(data_error) =
                                e.downcast_ref::<superstack_data::error::Error>()
                            {
                                matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::BincodeError(_)
                                    )
                                ) || matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::CiboriumError(_)
                                    )
                                ) || matches!(
                                    data_error,
                                    superstack_data::error::Error::KafkaConsumerError(
                                        ConsumerError::SerdeJsonError(_)
                                    )
                                )
                            } else {
                                // Fallback to string matching for other potential bincode errors
                                let error_msg = format!("{}", e);
                                error_msg.contains("bincode") ||
                                    error_msg.contains("ciborium") ||
                                    error_msg.contains("deserialize") ||
                                    error_msg.contains("serde")
                            }
                        };

                        if is_deserialize_error {
                            tracing::warn!("Deserialize error detected: {}. This may indicate corrupted messages in Kafka topic.", e);
                            consecutive_failures += 1;

                            // For deserialize errors, we want to restart more aggressively
                            // but with exponential backoff to avoid overwhelming the system
                            if consecutive_failures > 20 {
                                tracing::error!(
                                    "Too many consecutive deserialize failures ({}). Exiting consumer.",
                                    consecutive_failures
                                );
                                break;
                            }

                            let backoff_seconds = std::cmp::min(consecutive_failures, 60);
                            tracing::info!(
                                "Restarting consumer after {} seconds due to data corruption...",
                                backoff_seconds
                            );
                            tokio::time::sleep(Duration::from_secs(backoff_seconds)).await;
                        } else {
                            // For other types of errors, use the original retry logic
                            // If the consumer has been running for more than 1 hour, reset the
                            // retry count
                            if start_time.elapsed() > Duration::from_secs(3600) {
                                retry_count = 0;
                            }

                            retry_count += 1;
                            consecutive_failures = 0; // Reset since this is not a bincode error

                            if retry_count > 5 {
                                tracing::error!(
                                    "Error running update statistic consumer after {} retries: {}",
                                    retry_count,
                                    e
                                );
                                break;
                            }

                            tracing::warn!(
                                "Consumer error (attempt {}): {}. Retrying in {} seconds...",
                                retry_count,
                                e,
                                retry_count
                            );
                            tokio::time::sleep(Duration::from_secs(retry_count)).await;
                        }
                    }
                }
            }

            tracing::error!("Update Statistic consumer has stopped permanently");
        })
    }

    async fn run_consumer(tx: mpsc::Sender<UpdateStatisticMsg>) -> Result<()> {
        let data_config = Config::get().data_config;
        let confluent_config = ConfluentConfig::new(
            data_config.kafka_bootstrap_servers.clone(),
            data_config.kafka_api_key.clone(),
            data_config.kafka_api_secret.clone(),
        );
        let auto_commit_interval_ms = Config::get().kafka_auto_commit_interval_secs * 1000;
        let consumer_config = KafkaConsumerConfig::new_earliest_with_auto_commit(
            UPDATE_STATISTIC_CONSUMER_GROUP_ID,
            auto_commit_interval_ms,
        );
        let consumer = KafkaConsumer::new(&confluent_config, &consumer_config)?;

        consumer.consume_topic_to_channel::<UpdateStatisticMsg>(tx, false).await?;

        Ok(())
    }
}
