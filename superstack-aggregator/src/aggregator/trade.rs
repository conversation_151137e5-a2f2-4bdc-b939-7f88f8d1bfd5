use std::collections::HashMap;

use anyhow::Result;
use num_traits::ToPrimitive;
use superstack_data::{
    kafka::{topics::ExtendedDexTradesMsg, KafkaProducer},
    postgres::{aggregator::*, enums::MakerVolumeType, indexer::*, PostgresDatabase},
};

use super::*;

impl Aggregator {
    pub async fn aggregate_trades(
        pool_metadata: &PoolMetadata,
        token_metadata: &TokenMetadata,
        dex_trades: &[DexTrade],
        token_holders: &HashMap<String, TokenHolder>,
        base_token_usd_price: f64,
    ) -> Result<()> {
        let mut extended_dex_trades = Vec::with_capacity(dex_trades.len());
        let one_token_amount = 10u64.pow(pool_metadata.token_decimals as u32) as f64;
        let one_base_amount = 10u64.pow(pool_metadata.base_decimals as u32) as f64;
        for trade in dex_trades {
            let is_buy = trade.is_buy_token;
            let out_amount = trade
                .out_amount
                .to_f64()
                .ok_or(anyhow::anyhow!("Failed to convert out_amount to f64"))?;
            let in_amount = trade
                .in_amount
                .to_f64()
                .ok_or(anyhow::anyhow!("Failed to convert in_amount to f64"))?;
            let token_ui_amount: f64 =
                if is_buy { out_amount / one_token_amount } else { in_amount / one_token_amount };
            let base_ui_amount =
                if is_buy { in_amount / one_base_amount } else { out_amount / one_base_amount };

            let usd = base_ui_amount * base_token_usd_price;
            let usd_price = base_ui_amount / token_ui_amount * base_token_usd_price;
            let supply_f64 = token_metadata
                .supply
                .to_f64()
                .ok_or(anyhow::anyhow!("Failed to convert supply to f64"))?;
            let ui_supply: f64 = supply_f64 / one_token_amount;
            let usd_market_cap = usd_price * ui_supply;

            let token_holder = token_holders.get(&trade.maker_address);
            let maker_volume_type = if let Some(token_holder) = token_holder {
                MakerVolumeType::from_volume(
                    token_holder.total_spent_usd + token_holder.total_received_usd,
                )
            } else {
                MakerVolumeType::from_volume(usd)
            };

            let extended_dex_trade = ExtendedDexTrade {
                chain: trade.chain,
                pool_address: trade.pool_address.clone(),
                tx_hash: trade.tx_hash.clone(),
                ix_idx: trade.ix_idx,
                tx_idx: trade.tx_idx,
                maker_address: trade.maker_address.clone(),
                is_buy,
                token_ui_amount,
                base_ui_amount,
                usd,
                usd_price,
                usd_market_cap,
                block_number: trade.block_number,
                timestamp_millis: trade.timestamp_millis,
                maker_volume_type,
            };
            extended_dex_trades.push(extended_dex_trade);
        }

        let extended_dex_trades_msg = ExtendedDexTradesMsg::new(extended_dex_trades);
        let extended_dex_trades_msg_clone = extended_dex_trades_msg.clone();
        tokio::spawn(async move {
            let kafka_producer = KafkaProducer::get();
            if let Err(e) =
                kafka_producer.send::<ExtendedDexTradesMsg>(&extended_dex_trades_msg_clone).await
            {
                tracing::error!("Failed to send extended dex trades to Kafka: {}", e);
            }
        });

        // Can spawn this task
        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_extended_dex_trades(extended_dex_trades_msg.trades.as_slice()).await?;

        Ok(())
    }
}
