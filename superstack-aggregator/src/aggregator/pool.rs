use std::collections::HashMap;

use anyhow::Result;
use chrono::Utc;
use superstack_data::{
    kafka::KafkaProducer,
    postgres::{aggregator::*, enums::Chain, indexer::*, PostgresDatabase},
    price::NativeTokenPriceManager,
    redis::RedisClient,
};

use super::*;
use crate::config::Config;

impl Aggregator {
    pub async fn aggregate_pool(
        token_metadata: TokenMetadata,
        pool_metadata: PoolMetadata,
        pool_state: &PoolState,
        dex_trades: Vec<DexTrade>,
        token_holders: HashMap<String, TokenHolder>,
    ) -> Result<()> {
        let chain = pool_metadata.chain;
        let update_timestamp_millis = pool_state.timestamp_millis;

        let native_token_usd_price = NativeTokenPriceManager::get()
            .await
            .get_nearest_price(chain, update_timestamp_millis / 1000)
            .await
            .ok_or(anyhow::anyhow!("Native token price not found"))?;
        let base_token_usd_price = superstack_data::utils::get_base_token_usd_price(
            chain,
            &pool_metadata.base_address,
            update_timestamp_millis / 1000,
            Some(native_token_usd_price),
        )
        .await?;

        // TODO: uncomment this to aggregate candle
        // Self::aggregate_candle(&pool_state, base_token_usd_price, native_token_usd_price).await?;

        // Can spawn this task
        Self::aggregate_trades(
            &pool_metadata,
            &token_metadata,
            &dex_trades,
            &token_holders,
            base_token_usd_price,
        )
        .await?;

        // Can spawn this task
        Self::aggregate_pool_statistic(&pool_metadata, &pool_state, base_token_usd_price, false)
            .await?;

        Ok(())
    }

    pub async fn aggregate_candle(
        pool_state: &PoolState,
        base_token_usd_price: f64,
        native_token_usd_price: f64,
    ) -> Result<()> {
        tracing::trace!(
            "Starting candle aggregation for pool {} at block {} timestamp {}",
            pool_state.pool_address,
            pool_state.block_number,
            pool_state.timestamp_millis
        );

        let db = PostgresDatabase::get_indexer_db().await;

        // Process the pool state for candle generation
        if let Err(e) = db
            .process_pool_state_for_candle(pool_state, base_token_usd_price, native_token_usd_price)
            .await
        {
            tracing::error!(
                "Failed to process pool state for candle generation for pool {}: {}",
                pool_state.pool_address,
                e
            );
            return Err(e.into());
        }

        tracing::trace!(
            "Successfully processed candle for pool {} at timestamp {} (prices: base_usd={}, native_usd={})",
            pool_state.pool_address,
            pool_state.timestamp_millis,
            base_token_usd_price,
            native_token_usd_price
        );

        Ok(())
    }

    pub async fn aggregate_pool_statistic(
        pool_metadata: &PoolMetadata,
        pool_state: &PoolState,
        base_token_usd_price: f64,
        is_periodic_update: bool,
    ) -> Result<()> {
        let current_time_millis = Utc::now().timestamp_millis();

        let redis_client = RedisClient::get_instance().await;
        let db = PostgresDatabase::get_indexer_db().await;

        // Check if the pool statistic is already up to date
        let current_pool_statistic =
            redis_client.get_pool(pool_state.chain, &pool_metadata.pool_address).await?;
        if let Some(current_pool_statistic) = current_pool_statistic.as_ref() {
            let statistic_update_interval_millis =
                Config::get().statistic_update_interval as i64 * 1000;
            if (!is_periodic_update &&
                current_pool_statistic.total_txns >= pool_state.total_txns &&
                current_pool_statistic.update_timestamp_millis >= pool_state.timestamp_millis) ||
                (is_periodic_update &&
                    current_pool_statistic.update_timestamp_millis >=
                        current_time_millis - statistic_update_interval_millis)
            {
                return Ok(());
            }
        }

        let mut pool_statistic =
            PoolStatistic::new(&pool_metadata, pool_state, base_token_usd_price);
        // If the pool state is older than 24 hours, we can update the pool statistic directly
        // or the pool is from Hypercore, we can update the pool statistic directly
        if pool_state.timestamp_millis <= current_time_millis - MILLIS_24H ||
            pool_state.chain == Chain::Hypercore
        {
            Self::publish_pool_statistic(pool_statistic).await?;
            return Ok(());
        }

        let first_pool_state = {
            if let Some(first_pool_state) =
                db.get_first_non_zero_pool_state(pool_state.chain, &pool_state.pool_address).await?
            {
                if first_pool_state.block_number < pool_state.block_number {
                    first_pool_state
                } else {
                    pool_state.clone()
                }
            } else {
                pool_state.clone()
            }
        };

        // let ranges = [
        //     current_time_millis - MILLIS_5M,
        //     current_time_millis - MILLIS_1H,
        //     current_time_millis - MILLIS_6H,
        //     current_time_millis - MILLIS_24H,
        // ];
        // let mut timestamp_of_needed_pool_states = vec![];
        // for range in ranges {
        //     if pool_state.timestamp_millis > range && first_pool_state.timestamp_millis < range {
        //         timestamp_of_needed_pool_states.push(range);
        //     }
        // }

        // if timestamp_of_needed_pool_states.is_empty() {
        //     redis_client.set_pool(&pool_statistic).await?;
        //     kafka_producer.send::<PoolStatistic>(&pool_statistic).await?;
        //     db.insert_or_update_pool_statistic(&pool_statistic).await?;
        //     return Ok(());
        // }

        // TODO: uncomment this to optimize the performance
        // let all_pool_states = db
        //     .get_latest_pool_states_from_multiple_ranges(
        //         pool_state.chain,
        //         &pool_state.pool_address,
        //         &timestamp_of_needed_pool_states,
        //     )
        //     .await?;

        // 5m
        let five_minutes_statistic = Self::calculate_pool_statistic_for_range(
            pool_state,
            &first_pool_state,
            current_time_millis,
            MILLIS_5M,
            base_token_usd_price,
            current_pool_statistic.as_ref(),
        )
        .await?;
        pool_statistic.update_5m(five_minutes_statistic);

        // 1h
        let one_hour_statistic = Self::calculate_pool_statistic_for_range(
            pool_state,
            &first_pool_state,
            current_time_millis,
            MILLIS_1H,
            base_token_usd_price,
            current_pool_statistic.as_ref(),
        )
        .await?;
        pool_statistic.update_1h(one_hour_statistic);

        // 6h
        let six_hours_statistic = Self::calculate_pool_statistic_for_range(
            pool_state,
            &first_pool_state,
            current_time_millis,
            MILLIS_6H,
            base_token_usd_price,
            current_pool_statistic.as_ref(),
        )
        .await?;
        pool_statistic.update_6h(six_hours_statistic);

        // 24h
        let twenty_four_hours_statistic = Self::calculate_pool_statistic_for_range(
            pool_state,
            &first_pool_state,
            current_time_millis,
            MILLIS_24H,
            base_token_usd_price,
            current_pool_statistic.as_ref(),
        )
        .await?;
        pool_statistic.update_24h(twenty_four_hours_statistic);

        Self::publish_pool_statistic(pool_statistic).await?;

        Ok(())
    }

    async fn publish_pool_statistic(pool_statistic: PoolStatistic) -> Result<()> {
        tokio::spawn(async move {
            // Only send to kafka if the pool statistic is updated in the last 5 minutes
            if pool_statistic.update_timestamp_millis >
                chrono::Utc::now().timestamp_millis() - 1000 * 60 * 5
            {
                let kafka_producer = KafkaProducer::get();
                if let Err(e) = kafka_producer.send::<PoolStatistic>(&pool_statistic).await {
                    tracing::error!("Failed to send pool statistic to Kafka: {}", e);
                }
            }

            let redis_client = RedisClient::get_instance().await;
            if let Err(e) = redis_client.set_pool(&pool_statistic).await {
                tracing::error!("Failed to write pool statistic to Redis: {}", e);
            }
        });

        Ok(())
    }

    async fn calculate_pool_statistic_for_range(
        pool_state: &PoolState,
        first_pool_state: &PoolState,
        current_time_millis: i64,
        range_millis: i64,
        base_token_usd_price: f64,
        current_pool_statistic: Option<&PoolStatistic>,
    ) -> Result<RangedPoolStatistic> {
        let mut ranged_statistic = RangedPoolStatistic::default();

        let start_timestamp_millis = current_time_millis - range_millis;
        if pool_state.timestamp_millis <= start_timestamp_millis {
            return Ok(ranged_statistic);
        }

        let old_pool_state = if first_pool_state.timestamp_millis >= start_timestamp_millis {
            first_pool_state.clone()
        } else {
            let db = PostgresDatabase::get_indexer_db().await;
            db.get_latest_pool_state_before_timestamp_millis(
                pool_state.chain,
                &pool_state.pool_address,
                start_timestamp_millis,
            )
            .await?
            .unwrap_or_else(|| pool_state.clone())
        };

        ranged_statistic.price_change = if old_pool_state.price > 0.0 {
            (pool_state.price - old_pool_state.price) / old_pool_state.price
        } else {
            0.0
        };
        ranged_statistic.txns =
            pool_state.total_txns.checked_sub(old_pool_state.total_txns).unwrap_or(0);
        ranged_statistic.buy_txns =
            pool_state.total_buy_txns.checked_sub(old_pool_state.total_buy_txns).unwrap_or(0);
        ranged_statistic.sell_txns =
            pool_state.total_sell_txns.checked_sub(old_pool_state.total_sell_txns).unwrap_or(0);
        ranged_statistic.usd_volume =
            (pool_state.total_volume - old_pool_state.total_volume).max(0.0) * base_token_usd_price;
        ranged_statistic.usd_buy_volume =
            (pool_state.total_buy_volume - old_pool_state.total_buy_volume).max(0.0) *
                base_token_usd_price;
        ranged_statistic.usd_sell_volume =
            (pool_state.total_sell_volume - old_pool_state.total_sell_volume).max(0.0) *
                base_token_usd_price;

        if let Some(current_pool_statistic) = current_pool_statistic.as_ref() {
            // If the pool statistic is updated in the last 15 seconds (ref to pool_state), we don't
            // need to update the makers
            if current_pool_statistic.update_timestamp_millis >=
                pool_state.timestamp_millis - 1000 * 15
            {
                match range_millis {
                    MILLIS_5M => {
                        ranged_statistic.makers = current_pool_statistic.makers_5m;
                        ranged_statistic.buyers = current_pool_statistic.buyers_5m;
                        ranged_statistic.sellers = current_pool_statistic.sellers_5m;
                    }
                    MILLIS_1H => {
                        ranged_statistic.makers = current_pool_statistic.makers_1h;
                        ranged_statistic.buyers = current_pool_statistic.buyers_1h;
                        ranged_statistic.sellers = current_pool_statistic.sellers_1h;
                    }
                    MILLIS_6H => {
                        ranged_statistic.makers = current_pool_statistic.makers_6h;
                        ranged_statistic.buyers = current_pool_statistic.buyers_6h;
                        ranged_statistic.sellers = current_pool_statistic.sellers_6h;
                    }
                    MILLIS_24H => {
                        ranged_statistic.makers = current_pool_statistic.makers_24h;
                        ranged_statistic.buyers = current_pool_statistic.buyers_24h;
                        ranged_statistic.sellers = current_pool_statistic.sellers_24h;
                    }
                    _ => {
                        tracing::error!("[FIXME] Invalid range millis: {}", range_millis);
                        return Err(anyhow::anyhow!("Invalid range millis: {}", range_millis));
                    }
                }
            }
        }

        // Get maker counts for the specific time range
        let db = PostgresDatabase::get_indexer_db().await;
        let maker_count = db
            .get_makers_counts(
                pool_state.chain,
                &pool_state.pool_address,
                start_timestamp_millis,
                current_time_millis,
            )
            .await?;
        ranged_statistic.makers = maker_count.makers;
        ranged_statistic.buyers = maker_count.buyers;
        ranged_statistic.sellers = maker_count.sellers;

        Ok(ranged_statistic)
    }
}
