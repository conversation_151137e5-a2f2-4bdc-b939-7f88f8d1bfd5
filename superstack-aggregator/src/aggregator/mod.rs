pub mod candle;
pub mod holder;
pub mod pool;
pub mod store_task;
pub mod token;
pub mod trade;
pub mod trend;
pub mod unsync_database_buf;

use std::collections::HashMap;

use anyhow::Result;
use superstack_data::{kafka::topics::IndexerBlockMsg, postgres::indexer::*, redis::RedisClient};

use self::trend::TrendFactor;

const MILLIS_5M: i64 = 5 * 60 * 1000;
const MILLIS_1H: i64 = 60 * 60 * 1000;
const MILLIS_6H: i64 = 6 * 60 * 60 * 1000;
const MILLIS_24H: i64 = 24 * 60 * 60 * 1000;
const MILLIS_3D: i64 = 3 * 24 * 60 * 60 * 1000;
const MILLIS_7D: i64 = 7 * 24 * 60 * 60 * 1000;

pub struct Aggregator {}

impl Aggregator {
    pub async fn aggregate_all(indexer_block_msg: &IndexerBlockMsg) -> Result<()> {
        let mut token_pools: HashMap<String, (TokenMetadata, Vec<(PoolMetadata, PoolState)>)> =
            HashMap::new();
        let redis_client = RedisClient::get_instance().await;

        tracing::debug!(
            "Starting aggregation for {} pool states",
            indexer_block_msg.pool_states.len()
        );

        for pool_state in &indexer_block_msg.pool_states {
            let chain = pool_state.chain;
            let pool_address = &pool_state.pool_address;
            tracing::debug!("Processing pool: {}", pool_address);

            let pool_metadata =
                match redis_client.get_pool_metadata_or_update_from_db(chain, pool_address).await {
                    Ok(metadata) => metadata,
                    Err(e) => {
                        tracing::error!("Failed to get pool metadata for {}: {}", pool_address, e);
                        continue; // Skip this pool but continue with others
                    }
                };
            let token_address = &pool_metadata.token_address;

            // Validate token address before proceeding
            if token_address.is_empty() {
                tracing::error!("Pool {} has empty token_address, skipping", pool_address);
                continue;
            }

            let dex_trades = indexer_block_msg
                .dex_trades
                .iter()
                .filter(|dex_trade| dex_trade.pool_address == *pool_address)
                .map(|dex_trade| dex_trade.clone())
                .collect::<Vec<_>>();
            let token_holders = indexer_block_msg
                .token_holders
                .iter()
                .filter(|token_holder| token_holder.token_address == *token_address)
                .map(|token_holder| (token_holder.holder_address.clone(), token_holder.clone()))
                .collect::<HashMap<_, _>>();

            let token_metadata = match redis_client
                .get_token_metadata_or_update_from_db(chain, token_address)
                .await
            {
                Ok(metadata) => metadata,
                Err(e) => {
                    tracing::error!("Failed to get token metadata for {}: {}", token_address, e);
                    continue; // Skip this token but continue with others
                }
            };

            token_pools
                .entry(token_address.clone())
                .or_insert_with(|| (token_metadata.clone(), vec![]))
                .1
                .push((pool_metadata.clone(), pool_state.clone()));

            if let Err(e) = Self::aggregate_pool(
                token_metadata,
                pool_metadata,
                &pool_state,
                dex_trades,
                token_holders,
            )
            .await
            {
                tracing::error!("Failed to aggregate by pool {:?}: {:?}", pool_state, e);
            }
        }

        for (token_metadata, pool_metadata_pool_states) in token_pools.into_values() {
            if let Err(e) = Self::aggregate_token(token_metadata, pool_metadata_pool_states).await {
                tracing::error!("Failed to aggregate token statistics: {}", e);
            }
        }

        Ok(())
    }
}
