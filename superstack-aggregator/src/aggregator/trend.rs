use std::sync::OnceLock;

use superstack_data::{
    constant::solana::{USDC_MINT, USDT_MINT},
    postgres::{indexer::TokenMetadata, Chain},
};

/// Per-metric mean / std-dev computed on the estimated max values used to build the
/// trend table.
///
/// ## What mu and sigma represent:
/// In z-score formula: z = (raw_value - mu) / sigma
/// - `mu`: The average (mean) of all tokens in log-space
/// - `sigma`: How spread out the values are (standard deviation) in log-space
///
/// ## Why log-space?
/// Token metrics follow log-normal distribution:
/// - Normal data:     [100, 200, 300, 400, 500] - pretty even
/// - Crypto tokens:   [10, 50, 1000, 50000, 10000000] - extremely skewed!
/// - Log of crypto:   [2.3, 3.9, 6.9, 10.8, 16.1] - much more normal
///
/// ## How sigma affects range:
/// - **Larger sigma = smaller z-scores (compressed range)**
/// - **Smaller sigma = larger z-scores (expanded range)**
///
/// Example: raw_value=20, mu=10
/// - Small sigma=2.0: z = (20-10)/2.0 = 5.0  (large z-score!)
/// - Large sigma=10.0: z = (20-10)/10.0 = 1.0 (small z-score!)
#[derive(Clone, Debug)]
pub struct TrendNorms {
    pub max_liquidity: f64,
    pub mu_liquidity: f64,
    pub sigma_liquidity: f64,
    pub max_volume: f64,
    pub mu_volume: f64,
    pub sigma_volume: f64,
    pub max_txns: f64,
    pub mu_txns: f64,
    pub sigma_txns: f64,
    // pub max_participants: f64,
    // pub mu_participants: f64,
    // pub sigma_participants: f64,
}

impl TrendNorms {
    /// Create TrendNorms from market data statistics for Solana ecosystem
    /// Uses realistic estimates based on typical Solana token distributions
    ///
    /// ## Distribution assumptions for Solana:
    /// If max liquidity is $50M (like SOL/USDC), distribution looks like:
    /// - **1 token**: $50M (SOL/USDC)
    /// - **~10 tokens**: $1M-10M (JUP, TRUMP, etc.)
    /// - **~100 tokens**: $100K-1M (decent projects)
    /// - **~10,000 tokens**: $1K-100K (most meme tokens)
    /// - **~100,000 tokens**: <$1K (failed/new memes)
    ///
    /// ## Why these specific percentages:
    ///
    /// **mu (mean) percentages:**
    /// - 15% for liquidity: puts mean around $15K (typical token)
    /// - 20% for volume: less skewed than liquidity
    /// - 30% for txns: more evenly distributed across tokens
    ///
    /// **sigma (spread) percentages:**
    /// - 40% for liquidity: VERY wide range ($1 to $50M), large sigma compresses z-scores so USDC
    ///   doesn't dominate everything
    /// - 35% for volume: wide but less extreme than liquidity
    /// - 30% for txns: more normal distribution, smaller sigma gives more emphasis
    pub fn for_solana_tokens(
        max_liquidity: f64,
        max_volume: f64,
        max_txns: f64,
        // max_participants: f64,
    ) -> Self {
        // For Solana tokens, the distribution is heavily skewed:
        // - Few major tokens (SOL, USDC, JUP) dominate
        // - Many small meme tokens with minimal activity
        //
        // Using log-normal assumptions:
        // - Mean is around 10-30% of max in log space
        // - Std dev accounts for the wide spread

        let ln_max_liq = (1.0 + max_liquidity).ln();
        let ln_max_vol = (1.0 + max_volume).ln();
        let ln_max_txns = (1.0 + max_txns).ln();
        // let ln_max_participants = (1.0 + max_participants).ln();

        Self {
            max_liquidity,
            max_volume,
            max_txns,
            // max_participants,

            // Liquidity: Very skewed (USDC pools vs tiny meme tokens)
            // Large sigma (40%) compresses z-scores so big tokens don't dominate
            mu_liquidity: ln_max_liq * 0.15, // Mean at ~15% of max (~$15K typical)
            sigma_liquidity: ln_max_liq * 0.4, // Large spread to handle $1 to $50M range

            // Volume: Also very skewed but less extreme than liquidity
            mu_volume: ln_max_vol * 0.2,     // Mean at ~20% of max
            sigma_volume: ln_max_vol * 0.35, // Narrower spread than liquidity

            // Transactions: More normal distribution across token sizes
            // Smaller sigma (30%) gives more emphasis to txn differences
            mu_txns: ln_max_txns * 0.3,    // Mean at ~30% of max
            sigma_txns: ln_max_txns * 0.3, // Normal spread rewards genuine activity

            // Participants: Similar to transactions
            // mu_participants: ln_max_participants * 0.25, // Mean at ~25% of max
            // sigma_participants: ln_max_participants * 0.3, // Normal spread
        }
    }

    pub fn align_liquidity(&self, liquidity: f64) -> f64 {
        if liquidity > self.max_liquidity {
            // Soft cap: grows slowly after max_liquidity
            let soft_cap = (liquidity - self.max_liquidity).max(1.0).ln();
            self.max_liquidity + soft_cap.min(self.max_liquidity)
        } else {
            liquidity
        }
    }

    pub fn align_volume(&self, volume: f64) -> f64 {
        if volume > self.max_volume {
            // Soft cap: grows slowly after max_volume
            let soft_cap = (volume - self.max_volume).max(1.0).ln();
            self.max_volume + soft_cap.min(self.max_volume)
        } else {
            volume
        }
    }

    pub fn align_txns(&self, txns: f64) -> f64 {
        if txns > self.max_txns {
            // Soft cap: grows slowly after max_txns
            let soft_cap = (txns - self.max_txns).max(1.0).ln();
            self.max_txns + soft_cap.min(self.max_txns)
        } else {
            txns
        }
    }
}

#[derive(Debug, Clone, Copy, Eq, PartialEq, Hash)]
pub enum Timeframe {
    FiveMinutes,
    OneHour,
    SixHours,
    TwentyFourHours,
    ThreeDays,
    SevenDays,
    AllTime,
}

#[derive(Debug, Clone)]
pub struct MaxValues24H {
    pub max_liquidity: f64,
    pub max_volume: f64,
    pub max_txns: f64,
}

impl MaxValues24H {
    const DEFAULT_MAX_LIQUIDITY: u64 = 1_000_000_000; // $1B https://dexscreener.com/solana?rankBy=liquidity&order=desc
    const DEFAULT_MAX_VOLUME: u64 = 100_000_000; // $100M https://dexscreener.com/solana?rankBy=volume&order=desc
    const DEFAULT_MAX_TXNS: u64 = 500_000; // 500K txns https://dexscreener.com/solana?rankBy=txns&order=desc

    const FACTOR_5M: f64 = 5.0 / 1440.0;
    const FACTOR_1H: f64 = 1.0 / 24.0;
    const FACTOR_6H: f64 = 6.0 / 24.0;
    const FACTOR_24H: f64 = 1.0;
    const FACTOR_3D: f64 = 3.0;
    const FACTOR_7D: f64 = 7.0;
    const FACTOR_ALL_TIME: f64 = 30.0;

    pub fn get() -> &'static Self {
        static INSTANCE: OnceLock<MaxValues24H> = OnceLock::new();
        INSTANCE.get_or_init(|| Self::from_24h_stats())
    }

    pub fn from_24h_stats() -> Self {
        let config = crate::config::Config::get();

        let max_liquidity = config.max_liquidity.unwrap_or(Self::DEFAULT_MAX_LIQUIDITY);
        let max_volume = config.max_volume.unwrap_or(Self::DEFAULT_MAX_VOLUME);
        let max_txns = config.max_txns.unwrap_or(Self::DEFAULT_MAX_TXNS);

        Self {
            max_liquidity: max_liquidity as f64,
            max_volume: max_volume as f64,
            max_txns: max_txns as f64,
        }
    }

    fn as_trend_norms(&self, factor: f64) -> TrendNorms {
        let max_liquidity = self.max_liquidity;
        let max_volume = self.max_volume * factor;
        let max_txns = self.max_txns * factor;

        TrendNorms::for_solana_tokens(max_liquidity, max_volume, max_txns)
    }

    pub fn as_trend_norms_timeframe(&self, timeframe: Timeframe) -> TrendNorms {
        match timeframe {
            Timeframe::FiveMinutes => self.as_trend_norms(Self::FACTOR_5M),
            Timeframe::OneHour => self.as_trend_norms(Self::FACTOR_1H),
            Timeframe::SixHours => self.as_trend_norms(Self::FACTOR_6H),
            Timeframe::TwentyFourHours => self.as_trend_norms(Self::FACTOR_24H),
            Timeframe::ThreeDays => self.as_trend_norms(Self::FACTOR_3D),
            Timeframe::SevenDays => self.as_trend_norms(Self::FACTOR_7D),
            Timeframe::AllTime => self.as_trend_norms(Self::FACTOR_ALL_TIME),
        }
    }
}

#[derive(Debug, Clone)]
pub struct TrendFactor {
    pub timeframe: Timeframe,
    pub token_metadata: TokenMetadata,
    pub usd_liquidity: f64,
    pub usd_volume: f64,
    pub usd_buy_volume: f64,
    pub usd_sell_volume: f64,
    pub txns: u64,
    pub buy_txns: u64,
    pub sell_txns: u64,
}

impl TrendFactor {
    /// Clamp helper to avoid exploding ratios when one side is 0.
    #[inline]
    fn safe_ratio(a: f64, b: f64) -> f64 {
        // 1e-6 keeps the log bounded but still preserves sign information.
        (a + 1e-6) / (b + 1e-6)
    }

    /// z-score with σ-guard
    #[inline]
    fn z(raw: f64, mu: f64, sigma: f64) -> f64 {
        if sigma > 0.0 {
            (raw - mu) / sigma
        } else {
            0.0
        }
    }

    /// Main entry: returns 0-100 trend score.
    pub fn trend_score(&self) -> f64 {
        let norms = MaxValues24H::get().as_trend_norms_timeframe(self.timeframe);

        // --- 1. preprocess raw values --------------------------------------
        let ln_liquidity = (1.0 + norms.align_liquidity(self.usd_liquidity)).ln();
        let ln_volume = (1.0 + norms.align_volume(self.usd_volume)).ln();
        let ln_txns = (1.0 + norms.align_txns(self.txns as f64)).ln();

        // --- 2. convert to z-scores ----------------------------------------
        let z_liquidity = Self::z(ln_liquidity, norms.mu_liquidity, norms.sigma_liquidity);
        let z_volume = Self::z(ln_volume, norms.mu_volume, norms.sigma_volume);
        let z_txns = Self::z(ln_txns, norms.mu_txns, norms.sigma_txns);

        // --- 3. order-flow & social sub-scores (already roughly centred) ---
        // Clamp ln-ratio into ±5 then squash with tanh so it sits in (-1,1)
        let flow_ratio = (Self::safe_ratio(self.usd_buy_volume, self.usd_sell_volume)).ln();
        let order_flow = 0.5 * flow_ratio.clamp(-5.0, 5.0).tanh(); // scale ≈ ±0.5

        let social = {
            let mut s = 0.0;
            if self.token_metadata.twitter.is_some() {
                s += 0.4;
            } // follower counts could refine this
            if self.token_metadata.telegram.is_some() {
                s += 0.3;
            }
            if self.token_metadata.website.is_some() {
                s += 0.3;
            }
            s - 0.5 // centred roughly around 0
        };

        // --- 4. weighted linear blend  (weights sum to 1) -------------------
        let mut raw_score =
            0.2 * z_liquidity + 0.3 * z_volume + 0.3 * z_txns + 0.10 * order_flow + 0.10 * social;

        // --- 5. penalties ---------------------------------------------------
        match self.token_metadata.chain {
            Chain::Solana => {
                if self.token_metadata.address == USDC_MINT ||
                    self.token_metadata.address == USDT_MINT
                {
                    raw_score -= 0.5; // subtractive keeps variance intact
                }
            }
            _ => {}
        }

        if self.token_metadata.image.is_none() {
            raw_score -= 0.5;
        }

        // --- 6. squash to convenient 0-100 ----------------------------------
        // logistic maps (-∞,∞) → (0,1); multiply by 100 for UI.
        let score_0_1 = 1.0 / (1.0 + (-raw_score).exp());
        100.0 * score_0_1
    }
}
