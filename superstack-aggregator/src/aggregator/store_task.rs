use std::sync::OnceLock;

use anyhow::Result;
use tokio::{sync::mpsc, task::<PERSON><PERSON><PERSON><PERSON><PERSON>, time::Duration};

use super::unsync_database_buf::{Message, UnsyncDatabaseBuf};

pub static STORE_SENDER: OnceLock<mpsc::Sender<Message>> = OnceLock::new();

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct StoreTask {}

impl StoreTask {
    pub fn run(store_channel_size: usize) -> JoinHandle<()> {
        let (store_tx, store_rx) = mpsc::channel::<Message>(store_channel_size);

        let join_handle = tokio::spawn(Self::store(store_rx));
        STORE_SENDER.set(store_tx).expect("Failed to set store sender");

        join_handle
    }

    pub async fn send(message: Message) -> Result<()> {
        let store_tx = STORE_SENDER.get().unwrap();
        store_tx.send(message).await?;

        Ok(())
    }

    async fn store(mut store_rx: mpsc::Receiver<Message>) {
        tracing::info!("Starting store task");
        let mut unsync_database_buf = UnsyncDatabaseBuf::new();

        let mut interval = tokio::time::interval(Duration::from_millis(100));
        loop {
            interval.tick().await;

            if let Err(e) = unsync_database_buf.save().await {
                tracing::error!("Error saving database buf: {:?}", e);
            }

            loop {
                match store_rx.try_recv() {
                    Ok(token_holders) => {
                        for token_holder in token_holders {
                            unsync_database_buf.insert_token_holder(token_holder);
                        }

                        if unsync_database_buf.should_save() {
                            if let Err(e) = unsync_database_buf.save().await {
                                tracing::error!("Error saving database buf: {:?}", e);
                            }
                        }
                    }
                    Err(mpsc::error::TryRecvError::Empty) => {
                        break;
                    }
                    Err(mpsc::error::TryRecvError::Disconnected) => {
                        tracing::error!("Store task channel disconnected");
                        tracing::info!("Saving all unsynced data before exiting");
                        if let Err(e) = unsync_database_buf.save().await {
                            tracing::error!("Error saving all unsynced data: {:?}", e);
                        }
                        tracing::info!("All unsynced data saved, exiting store task");

                        std::process::exit(1);
                    }
                }
            }
        }
    }
}
