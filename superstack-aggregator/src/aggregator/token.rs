use std::collections::{hash_map::Entry, HashMap, HashSet};

use anyhow::Result;
use chrono::Utc;
use superstack_data::{
    kafka::KafkaProducer,
    postgres::{aggregator::*, indexer::*, Chain, Dex, PostgresDatabase},
    price::NativeTokenPriceManager,
    redis::RedisClient,
};

use super::{trend::Timeframe, *};
use crate::config::Config;

#[derive(Debug, Clone)]
pub struct PoolStateInfo {
    pub is_active: bool,
    pub dex: Dex,
    pub base_address: String,
    pub pool_state: PoolState,
}

impl Aggregator {
    pub async fn aggregate_token(
        token_metadata: TokenMetadata,
        new_pool_states: Vec<(PoolMetadata, PoolState)>,
    ) -> Result<()> {
        if new_pool_states.is_empty() {
            return Ok(());
        }

        let chain = token_metadata.chain;
        let token_address = &token_metadata.address;

        let redis_client = RedisClient::get_instance().await;
        let cur_token_statistic = redis_client.get_token(chain, token_address).await?;
        let cur_pool_states = if let Some(cur_token_statistic) = cur_token_statistic {
            let mut cur_pool_addresses =
                cur_token_statistic.pool_addresses.split(',').collect::<HashSet<_>>();
            let new_pool_addresses = new_pool_states
                .iter()
                .filter_map(|(pool_metadata, _)| {
                    let pool_address = pool_metadata.pool_address.as_str();
                    if !cur_pool_addresses.contains(&pool_address) {
                        Some(pool_address)
                    } else {
                        None
                    }
                })
                .collect::<Vec<_>>();
            if !new_pool_addresses.is_empty() {
                cur_pool_addresses.extend(new_pool_addresses);
            }

            let cur_pool_addresses = cur_pool_addresses.into_iter().collect::<Vec<_>>();
            let cur_pools = redis_client.get_pools(chain, &cur_pool_addresses).await?;
            let mut cur_pool_states = cur_pools
                .iter()
                .map(|pool| {
                    (
                        pool.pool_address.clone(),
                        PoolStateInfo {
                            is_active: pool.is_active,
                            dex: pool.dex,
                            base_address: pool.base_address.clone(),
                            pool_state: pool.to_pool_state(),
                        },
                    )
                })
                .collect::<HashMap<_, _>>();
            let mut should_exit = true;
            for (pool_metadata, pool_state) in new_pool_states {
                match cur_pool_states.entry(pool_metadata.pool_address.clone()) {
                    Entry::Occupied(mut entry) => {
                        if entry.get().pool_state.timestamp_millis <= pool_state.timestamp_millis {
                            entry.get_mut().pool_state = pool_state;
                            should_exit = false;
                        }
                    }
                    Entry::Vacant(entry) => {
                        entry.insert(PoolStateInfo {
                            is_active: pool_metadata.is_active,
                            dex: pool_metadata.dex,
                            base_address: pool_metadata.base_address.clone(),
                            pool_state,
                        });
                        should_exit = false;
                    }
                }
            }
            if should_exit {
                return Ok(());
            }
            cur_pool_states
        } else {
            let cur_pool_states = new_pool_states
                .into_iter()
                .map(|(pool_metadata, pool_state)| {
                    (
                        pool_metadata.pool_address,
                        PoolStateInfo {
                            is_active: pool_metadata.is_active,
                            dex: pool_metadata.dex,
                            base_address: pool_metadata.base_address.clone(),
                            pool_state,
                        },
                    )
                })
                .collect::<HashMap<_, _>>();
            cur_pool_states
        };

        let token_state =
            Self::aggregate_token_state_by_pool_states(&token_metadata, &cur_pool_states, false)
                .await?;

        // Can spawn this task
        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_token_state(&token_state).await?;

        // Can spawn this task
        let mut pool_addresses = vec![];
        let mut pool_dexes = vec![];
        for (pool_address, info) in cur_pool_states.into_iter() {
            pool_addresses.push(pool_address);
            if !pool_dexes.contains(&info.dex) {
                pool_dexes.push(info.dex);
            }
        }
        Self::aggregate_token_statistic(
            token_metadata,
            token_state,
            pool_addresses,
            pool_dexes,
            false,
        )
        .await?;

        Ok(())
    }

    pub async fn aggregate_token_periodic(
        token_metadata: TokenMetadata,
        cur_pool_states: &HashMap<String, PoolStateInfo>,
    ) -> Result<()> {
        let token_state =
            Self::aggregate_token_state_by_pool_states(&token_metadata, &cur_pool_states, true)
                .await?;

        // Can spawn this task
        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_token_state(&token_state).await?;

        // Can spawn this task
        let mut pool_addresses = vec![];
        let mut pool_dexes = vec![];
        for (pool_address, info) in cur_pool_states.into_iter() {
            pool_addresses.push(pool_address.clone());
            if !pool_dexes.contains(&info.dex) {
                pool_dexes.push(info.dex);
            }
        }
        Self::aggregate_token_statistic(
            token_metadata,
            token_state,
            pool_addresses,
            pool_dexes,
            true,
        )
        .await?;

        Ok(())
    }

    async fn aggregate_token_state_by_pool_states(
        token_metadata: &TokenMetadata,
        cur_pool_states: &HashMap<String, PoolStateInfo>,
        is_periodic_update: bool,
    ) -> Result<TokenState> {
        // If there is no active pool, disable the token and break the updating flow
        let has_active_pool = cur_pool_states.values().any(|info| info.is_active);
        if !has_active_pool {
            Self::disable_token(token_metadata.chain, &token_metadata.address, true).await?;
            return Err(anyhow::anyhow!("Disabled token: {}", token_metadata.address));
        }

        let mut token_state = TokenState::new(token_metadata.chain, &token_metadata.address);

        let max_timestamp_millis = if !is_periodic_update {
            cur_pool_states
                .values()
                .map(|info| info.pool_state.timestamp_millis)
                .max()
                .unwrap_or_default()
        } else {
            chrono::Utc::now().timestamp_millis()
        };
        token_state.timestamp_millis = max_timestamp_millis;

        let native_token_usd_price = NativeTokenPriceManager::get()
            .await
            .get_nearest_price(token_metadata.chain, max_timestamp_millis / 1000)
            .await
            .ok_or(anyhow::anyhow!("Native token price not found"))?;

        let mut max_usd_volume = f64::MIN;

        for info in cur_pool_states.values() {
            let is_active = info.is_active;
            let dex = info.dex;
            let base_address = &info.base_address;
            let pool_state = &info.pool_state;

            let base_token_usd_price = superstack_data::utils::get_base_token_usd_price(
                token_metadata.chain,
                base_address,
                max_timestamp_millis / 1000,
                Some(native_token_usd_price),
            )
            .await?;

            let pool_volume_usd = pool_state.total_volume * base_token_usd_price;
            if crate::utils::is_native_token(token_metadata.chain, base_address) {
                // Only update the best pool if the pool is active
                if is_active && pool_volume_usd > max_usd_volume && pool_state.price > 0.0 {
                    max_usd_volume = pool_volume_usd;

                    token_state.best_pool_address = pool_state.pool_address.clone();
                    token_state.best_pool_dex = dex;
                    token_state.usd_price = pool_state.price * base_token_usd_price;
                    token_state.usd_market_cap = pool_state.market_cap * base_token_usd_price;
                    token_state.native_price = Some(pool_state.price);
                }

                // Only add liquidity if the pool is active
                if is_active {
                    token_state.native_token_liquidity += pool_state.liquidity;
                }
                token_state.total_native_token_volume += pool_state.total_volume;
                token_state.total_native_token_buy_volume += pool_state.total_buy_volume;
                token_state.total_native_token_sell_volume += pool_state.total_sell_volume;
            } else {
                // Only update the best pool if the pool is active
                if is_active && pool_volume_usd > max_usd_volume && pool_state.price > 0.0 {
                    max_usd_volume = pool_volume_usd;

                    token_state.best_pool_address = pool_state.pool_address.clone();
                    token_state.best_pool_dex = dex;
                    token_state.usd_price = pool_state.price * base_token_usd_price;
                    token_state.usd_market_cap = pool_state.market_cap * base_token_usd_price;
                    token_state.native_price = None;
                }

                // Only add liquidity if the pool is active
                if is_active {
                    token_state.usd_token_liquidity += pool_state.liquidity * base_token_usd_price;
                }
                token_state.total_usd_token_volume +=
                    pool_state.total_volume * base_token_usd_price;
                token_state.total_usd_token_buy_volume +=
                    pool_state.total_buy_volume * base_token_usd_price;
                token_state.total_usd_token_sell_volume +=
                    pool_state.total_sell_volume * base_token_usd_price;
            }

            token_state.total_txns += pool_state.total_txns;
            token_state.total_buy_txns += pool_state.total_buy_txns;
            token_state.total_sell_txns += pool_state.total_sell_txns;

            match (token_state.bonding_curve_progress, pool_state.bonding_curve_progress) {
                (Some(cur_progress), Some(new_progress)) => {
                    token_state.bonding_curve_progress = Some(cur_progress.max(new_progress));
                }
                (None, Some(new_progress)) => {
                    token_state.bonding_curve_progress = Some(new_progress);
                }
                _ => {}
            }
        }

        // If the token state usd price is 0, it means that all pools are invalid
        if token_state.usd_price == 0.0 {
            tracing::error!(
                "FIXME: token state usd price is 0 for token: {}",
                token_metadata.address
            );
            // TODO: maybe we should update liquidity to 0?
            Self::disable_token(token_metadata.chain, &token_metadata.address, true).await?;
            return Err(anyhow::anyhow!(
                "Token state usd price is 0 for token: {}",
                token_metadata.address
            ));
        }

        Ok(token_state)
    }

    pub async fn disable_token(
        chain: Chain,
        token_address: &str,
        update_token_statistic: bool,
    ) -> Result<()> {
        Self::update_token_active_status(chain, token_address, false, update_token_statistic)
            .await?;

        Ok(())
    }

    pub async fn enable_token(
        chain: Chain,
        token_address: &str,
        update_token_statistic: bool,
    ) -> Result<()> {
        Self::update_token_active_status(chain, token_address, true, update_token_statistic)
            .await?;

        Ok(())
    }

    async fn update_token_active_status(
        chain: Chain,
        token_address: &str,
        is_active: bool,
        update_token_statistic: bool,
    ) -> Result<()> {
        let db = PostgresDatabase::get_indexer_db().await;
        db.update_token_metadata_active_status(chain, token_address, is_active).await?;

        let redis_client = RedisClient::get_instance().await;
        if redis_client.contains_token_metadata(chain, token_address).await? {
            redis_client.set_token_metadata_active_status(chain, token_address, is_active).await?;
        }
        if update_token_statistic && redis_client.contains_token(chain, token_address).await? {
            redis_client.set_token_statistic_active_status(chain, token_address, is_active).await?;
        }

        Ok(())
    }

    pub async fn aggregate_token_statistic(
        token_metadata: TokenMetadata,
        token_state: TokenState,
        pool_addresses: Vec<String>,
        pool_dexes: Vec<Dex>,
        is_periodic_update: bool,
    ) -> Result<()> {
        let current_time = Utc::now().timestamp_millis();

        // Check if the token statistic is already up to date
        let redis_client = RedisClient::get_instance().await;
        let current_token_statistic =
            redis_client.get_token(token_metadata.chain, &token_metadata.address).await?;

        let db = PostgresDatabase::get_indexer_db().await;
        let mut first_token_state = None;
        let init_usd_price = if let Some(current_token_statistic) = current_token_statistic.as_ref()
        {
            // Early return if the token statistic is already up to date
            let statistic_update_interval_millis =
                Config::get().statistic_update_interval as i64 * 1000;
            if (!is_periodic_update &&
                current_token_statistic.total_txns >= token_state.total_txns &&
                current_token_statistic.update_timestamp_millis >= token_state.timestamp_millis) ||
                (is_periodic_update &&
                    current_token_statistic.update_timestamp_millis >=
                        current_time - statistic_update_interval_millis)
            {
                return Ok(());
            }

            if let Some(init_usd_price) = current_token_statistic.init_usd_price {
                init_usd_price
            } else {
                match db
                    .get_first_non_zero_token_state(token_metadata.chain, &token_metadata.address)
                    .await?
                {
                    Some(state) => {
                        let init_usd_price = state.usd_price;
                        first_token_state = Some(state);
                        init_usd_price
                    }
                    None => {
                        let init_usd_price = token_state.usd_price;
                        first_token_state = Some(token_state.clone());
                        init_usd_price
                    }
                }
            }
        } else {
            token_state.usd_price
        };

        let token_state_7d_ago = db
            .get_latest_token_state_before_timestamp_millis(
                token_metadata.chain,
                &token_metadata.address,
                current_time - MILLIS_7D,
            )
            .await?;
        if token_state_7d_ago.is_none() && first_token_state.is_none() {
            first_token_state = db
                .get_first_non_zero_token_state(token_metadata.chain, &token_metadata.address)
                .await?;
        }

        let native_token_usd_price = NativeTokenPriceManager::get()
            .await
            .get_latest_price(token_metadata.chain)
            .await
            .ok_or(anyhow::anyhow!("Native token price not found"))?;

        let mut token_statistic = TokenStatistic::new(
            &token_metadata,
            &token_state,
            native_token_usd_price,
            init_usd_price,
        );
        token_statistic.pool_addresses = pool_addresses.join(",").to_string();
        token_statistic.pool_dexes =
            pool_dexes.iter().map(|dex| dex.to_string()).collect::<Vec<_>>().join(",");

        let min_liquidity_for_active = Config::get().min_liquidity_for_active as f64;
        // TODO: remove hypercore check when we have hypercore tokens's liquidity
        if token_statistic.usd_liquidity <= min_liquidity_for_active &&
            token_metadata.chain != Chain::Hypercore
        {
            tracing::info!(
                "Token liquidity is less than {}, disabling token: {}",
                min_liquidity_for_active,
                token_metadata.address
            );
            token_statistic.is_active = false;
            Self::disable_token(token_metadata.chain, &token_metadata.address, false).await?;
        } else if !token_metadata.is_active &&
            token_statistic.usd_liquidity > min_liquidity_for_active
        {
            token_statistic.is_active = true;
            Self::enable_token(token_metadata.chain, &token_metadata.address, false).await?;
        }

        token_statistic.total_price_change =
            token_state.total_price_change(init_usd_price, Some(native_token_usd_price));
        let trend_factor = TrendFactor {
            timeframe: Timeframe::AllTime,
            token_metadata: token_metadata.clone(),
            usd_liquidity: token_state.liquidity(native_token_usd_price),
            usd_volume: token_state.total_volume(native_token_usd_price),
            usd_buy_volume: token_state.total_buy_volume(native_token_usd_price),
            usd_sell_volume: token_state.total_sell_volume(native_token_usd_price),
            txns: token_state.total_txns(),
            buy_txns: token_state.total_buy_txns(),
            sell_txns: token_state.total_sell_txns(),
        };
        token_statistic.total_trend = trend_factor.trend_score();

        // 5m
        let five_minutes_statistic = Self::calculate_token_statistic_for_range(
            &token_metadata,
            &token_state,
            current_time,
            MILLIS_5M,
            native_token_usd_price,
            Timeframe::FiveMinutes,
            first_token_state.as_ref(),
            token_state_7d_ago.as_ref(),
        )
        .await?;
        token_statistic.update_5m(five_minutes_statistic);

        // 1h
        let one_hour_statistic = Self::calculate_token_statistic_for_range(
            &token_metadata,
            &token_state,
            current_time,
            MILLIS_1H,
            native_token_usd_price,
            Timeframe::OneHour,
            first_token_state.as_ref(),
            token_state_7d_ago.as_ref(),
        )
        .await?;
        token_statistic.update_1h(one_hour_statistic);

        // 6h
        let six_hours_statistic = Self::calculate_token_statistic_for_range(
            &token_metadata,
            &token_state,
            current_time,
            MILLIS_6H,
            native_token_usd_price,
            Timeframe::SixHours,
            first_token_state.as_ref(),
            token_state_7d_ago.as_ref(),
        )
        .await?;
        token_statistic.update_6h(six_hours_statistic);

        // 24h
        let twenty_four_hours_statistic = Self::calculate_token_statistic_for_range(
            &token_metadata,
            &token_state,
            current_time,
            MILLIS_24H,
            native_token_usd_price,
            Timeframe::TwentyFourHours,
            first_token_state.as_ref(),
            token_state_7d_ago.as_ref(),
        )
        .await?;
        token_statistic.update_24h(twenty_four_hours_statistic);

        // 3d
        let three_days_statistic = Self::calculate_token_statistic_for_range(
            &token_metadata,
            &token_state,
            current_time,
            MILLIS_3D,
            native_token_usd_price,
            Timeframe::ThreeDays,
            first_token_state.as_ref(),
            token_state_7d_ago.as_ref(),
        )
        .await?;
        token_statistic.update_3d(three_days_statistic);

        // 7d
        let seven_days_statistic = Self::calculate_token_statistic_for_range(
            &token_metadata,
            &token_state,
            current_time,
            MILLIS_7D,
            native_token_usd_price,
            Timeframe::SevenDays,
            first_token_state.as_ref(),
            token_state_7d_ago.as_ref(),
        )
        .await?;
        token_statistic.update_7d(seven_days_statistic);

        let redis_client = RedisClient::get_instance().await;
        if let Err(e) = redis_client.set_token(&token_statistic).await {
            tracing::error!("Failed to write token statistic to Redis: {}", e);
        }

        tokio::spawn(async move {
            // Only send to kafka if the token statistic is updated in the last 5 minutes
            if token_statistic.update_timestamp_millis >
                chrono::Utc::now().timestamp_millis() - 1000 * 60 * 5
            {
                let kafka_producer = KafkaProducer::get();
                if let Err(e) = kafka_producer.send::<TokenStatistic>(&token_statistic).await {
                    tracing::error!("Failed to send token statistic to Kafka: {}", e);
                }
            }
        });

        Ok(())
    }

    async fn calculate_token_statistic_for_range(
        token_metadata: &TokenMetadata,
        token_state: &TokenState,
        current_time_millis: i64,
        range_millis: i64,
        native_token_usd_price: f64,
        timeframe: Timeframe,
        first_token_state: Option<&TokenState>,
        token_state_7d_ago: Option<&TokenState>,
    ) -> Result<RangedTokenStatistic> {
        let mut ranged_statistic = RangedTokenStatistic::default();

        let start_timestamp_millis = current_time_millis - range_millis;
        if token_state.timestamp_millis <= start_timestamp_millis {
            return Ok(ranged_statistic);
        }

        let old_token_state = {
            if token_state_7d_ago.is_none() &&
                first_token_state.is_some() &&
                first_token_state.unwrap().timestamp_millis >= start_timestamp_millis
            {
                first_token_state.unwrap().clone()
            } else if token_state_7d_ago.is_some() && timeframe == Timeframe::SevenDays {
                token_state_7d_ago.unwrap().clone()
            } else if token_state_7d_ago.is_none() && first_token_state.is_none() {
                token_state.empty_total_state()
            } else {
                let db = PostgresDatabase::get_indexer_db().await;
                db.get_latest_token_state_before_timestamp_millis(
                    token_metadata.chain,
                    &token_metadata.address,
                    start_timestamp_millis,
                )
                .await?
                .unwrap_or_else(|| token_state.empty_total_state())
            }
        };

        ranged_statistic.price_change = token_state.price_change(&old_token_state, None);
        ranged_statistic.txns = token_state.txns(&old_token_state);
        ranged_statistic.buy_txns = token_state.buy_txns(&old_token_state);
        ranged_statistic.sell_txns = token_state.sell_txns(&old_token_state);
        ranged_statistic.usd_volume = token_state.volume(&old_token_state, native_token_usd_price);
        ranged_statistic.usd_buy_volume =
            token_state.buy_volume(&old_token_state, native_token_usd_price);
        ranged_statistic.usd_sell_volume =
            token_state.sell_volume(&old_token_state, native_token_usd_price);

        let trend_factor = TrendFactor {
            timeframe,
            token_metadata: token_metadata.clone(),
            usd_liquidity: token_state.liquidity(native_token_usd_price),
            usd_volume: ranged_statistic.usd_volume,
            usd_buy_volume: ranged_statistic.usd_buy_volume,
            usd_sell_volume: ranged_statistic.usd_sell_volume,
            txns: ranged_statistic.txns as u64,
            buy_txns: ranged_statistic.buy_txns as u64,
            sell_txns: ranged_statistic.sell_txns as u64,
        };
        ranged_statistic.trend = trend_factor.trend_score();

        Ok(ranged_statistic)
    }
}
