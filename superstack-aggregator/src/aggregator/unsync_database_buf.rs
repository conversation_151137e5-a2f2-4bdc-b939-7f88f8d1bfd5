use std::collections::HashMap;

use anyhow::Result;

use superstack_data::postgres::{aggregator::ExtendedTokenHolder, Chain, PostgresDatabase};

pub type Message = Vec<ExtendedTokenHolder>;

#[derive(Debug, Clone)]
pub struct UnsyncDatabaseBuf {
    token_holders: HashMap<(Chain, String, String), ExtendedTokenHolder>,
}

impl Default for UnsyncDatabaseBuf {
    fn default() -> Self {
        Self::new()
    }
}

impl UnsyncDatabaseBuf {
    pub fn new() -> Self {
        Self { token_holders: HashMap::new() }
    }

    pub fn should_save(&self) -> bool {
        self.token_holders.len() > 100
    }

    pub fn insert_token_holder(&mut self, token_holder: ExtendedTokenHolder) {
        let chain = token_holder.chain;
        let token_address = token_holder.token_address.clone();
        let holder_address = token_holder.holder_address.clone();

        // Use entry API for efficient insert-or-update
        match self.token_holders.entry((chain, token_address, holder_address)) {
            std::collections::hash_map::Entry::Occupied(mut entry) => {
                // Compare and keep the new one
                let existing = entry.get();
                if token_holder.is_newer_than(existing) {
                    entry.insert(token_holder);
                }
                // If equal or less, keep the existing one (no-op)
            }
            std::collections::hash_map::Entry::Vacant(entry) => {
                // Insert new token holder
                entry.insert(token_holder);
            }
        }
    }

    pub async fn save(&mut self) -> Result<()> {
        // Drain all data atomically
        let token_holders =
            self.token_holders.drain().map(|(_, holder)| holder).collect::<Vec<_>>();

        // Early return if no data
        if token_holders.is_empty() {
            return Ok(());
        }

        let db = PostgresDatabase::get_indexer_db().await;

        // Insert token holders one by one to avoid db lock contention
        for token_holder in token_holders {
            if let Err(e) = db.insert_or_update_extended_token_holder(&token_holder).await {
                tracing::error!("Error inserting token holder: {:?}", e);
            }
        }

        Ok(())
    }
}
