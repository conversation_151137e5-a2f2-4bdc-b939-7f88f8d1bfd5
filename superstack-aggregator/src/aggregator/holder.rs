use std::collections::HashMap;

use anyhow::Result;
use num_traits::ToPrimitive;
use superstack_data::{
    kafka::{topics::ExtendedTokenHoldersMsg, KafkaProducer},
    postgres::{
        aggregator::*,
        enums::{MakerTradeType, MakerVolumeType},
        indexer::*,
    },
    redis::RedisClient,
};

use super::{store_task::StoreTask, *};
use crate::cache::PriceMemCache;

impl Aggregator {
    pub async fn aggregate_holders(token_holders: Vec<TokenHolder>) -> Result<()> {
        if token_holders.is_empty() {
            return Ok(());
        }

        let mut extended_token_holders = Vec::with_capacity(token_holders.len());
        let mut token_holders_map = HashMap::new();
        for token_holder in token_holders {
            token_holders_map
                .entry((token_holder.chain, token_holder.token_address.clone()))
                .or_insert(vec![])
                .push(token_holder);
        }

        for token_holders in token_holders_map.into_values() {
            if token_holders.is_empty() {
                continue;
            }

            let price_cache = PriceMemCache::get_instance();
            let chain = token_holders[0].chain;
            let token_address = &token_holders[0].token_address;

            let (price, decimals) = {
                if let Ok((price, decimals)) =
                    price_cache.get_price_and_decimals(chain, token_address).await
                {
                    (price, decimals)
                } else {
                    let redis_client = RedisClient::get_instance().await;
                    let token_metadata = redis_client
                        .get_token_metadata_or_update_from_db(chain, token_address)
                        .await?;
                    (0.000001, token_metadata.decimals)
                }
            };

            let one_token_amount = 10u64.pow(decimals as u32) as f64;
            for token_holder in token_holders {
                let bought_amount = token_holder
                    .bought_amount
                    .to_f64()
                    .ok_or(anyhow::anyhow!("Failed to convert bought_amount to f64"))?;
                let sold_amount = token_holder
                    .sold_amount
                    .to_f64()
                    .ok_or(anyhow::anyhow!("Failed to convert sold_amount to f64"))?;
                let bought_ui_amount: f64 = bought_amount / one_token_amount;
                let sold_ui_amount: f64 = sold_amount / one_token_amount;
                let remaining_ui_amount: f64 = (bought_ui_amount - sold_ui_amount).max(0.0);
                let native_token_balance =
                    crate::utils::get_native_token_balance(chain, &token_holder.holder_address)
                        .await
                        .unwrap_or(0.0);
                let maker_volume_type = MakerVolumeType::from_volume(
                    token_holder.total_spent_usd + token_holder.total_received_usd,
                );
                // TODO: Add maker trade type
                let maker_trade_type = MakerTradeType::None;

                let pnl_usd = remaining_ui_amount * price + token_holder.total_received_usd -
                    token_holder.total_spent_usd;
                let extended_token_holder = ExtendedTokenHolder {
                    chain: token_holder.chain,
                    token_address: token_holder.token_address.clone(),
                    holder_address: token_holder.holder_address.clone(),
                    bought_ui_amount,
                    sold_ui_amount,
                    remaining_ui_amount,
                    bought_txns: token_holder.bought_txns,
                    sold_txns: token_holder.sold_txns,
                    spent_usd: token_holder.total_spent_usd,
                    received_usd: token_holder.total_received_usd,
                    pnl_usd,
                    update_timestamp_millis: token_holder.update_timestamp_millis,
                    update_block_number: token_holder.update_block_number,
                    native_token_balance,
                    maker_volume_type,
                    maker_trade_type,
                };

                extended_token_holders.push(extended_token_holder);
            }
        }

        let extended_token_holders_msg = ExtendedTokenHoldersMsg::new(extended_token_holders);

        let extended_token_holders_msg_clone = extended_token_holders_msg.clone();
        tokio::spawn(async move {
            let kafka_producer = KafkaProducer::get();
            if let Err(e) = kafka_producer
                .send::<ExtendedTokenHoldersMsg>(&extended_token_holders_msg_clone)
                .await
            {
                tracing::error!("Failed to send extended token holders to Kafka: {}", e);
            }
        });

        if extended_token_holders_msg.holders.len() > 0 {
            StoreTask::send(extended_token_holders_msg.holders).await?;
        }

        Ok(())
    }
}
