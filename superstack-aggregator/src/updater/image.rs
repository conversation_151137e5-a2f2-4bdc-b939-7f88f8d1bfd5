use anyhow::{Context, Result};
use resvg::{tiny_skia, usvg, usvg::TreeParsing};
use superstack_data::postgres::Chain;

pub struct Image {
    pub image_id: String,
    pub image_type: String,
    pub image_ext: String,
    pub image_url: String,
    pub image_data: Vec<u8>,
}

impl Image {
    pub fn construct_image_id(chain: Chain, address: &str) -> String {
        format!("{}-{}", chain.to_string(), address)
    }
}

pub fn convert_svg_to_png(svg_data: &[u8]) -> Result<Vec<u8>> {
    // Parse SVG
    let options = usvg::Options::default();
    let tree = usvg::Tree::from_data(svg_data, &options).context("Failed to parse SVG")?;

    let size = tree.size;
    let width = size.width() as u32;
    let height = size.height() as u32;

    if width == 0 || height == 0 {
        anyhow::bail!("Invalid SVG dimensions: {}x{}", width, height);
    }

    // Create pixmap
    let mut pixmap = tiny_skia::Pixmap::new(width, height).context("Failed to create pixmap")?;

    // Render using usvg and tiny-skia directly
    let rtree = resvg::Tree::from_usvg(&tree);
    rtree.render(tiny_skia::Transform::identity(), &mut pixmap.as_mut());

    // Encode as PNG
    pixmap.encode_png().context("Failed to encode PNG")
}

pub fn is_svg(buf: &[u8]) -> bool {
    buf.len() > 4 && &buf[0..4] == b"<svg"
}
