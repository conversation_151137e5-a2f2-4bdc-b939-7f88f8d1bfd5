use std::{collections::HashMap, str::FromStr, sync::Arc, time::Duration};

use anyhow::Result;
use backon::{ExponentialBuilder, Retryable};
use solana_sdk::pubkey::Pubkey;
use tokio::{
    sync::{mpsc, Semaphore},
    task::<PERSON><PERSON><PERSON><PERSON><PERSON>,
};

use superstack_data::{
    kafka::{topics::UpdateStatisticMsg, KafkaProducer},
    postgres::{Chain, PostgresDatabase},
    price::NativeTokenPriceManager,
    redis::{distributed_lock::DistributedLockGuard, RedisClient},
    token::solana::metadata::fetch_mint_info,
    utils::get_confirmed_rpc_client,
};

use crate::{
    aggregator::{token::PoolStateInfo, Aggregator},
    config::Config,
};

pub struct StatisticUpdater {}

impl StatisticUpdater {
    const STATISTIC_UPDATE_LOCK_KEY: &str = "statistic_update_lock";

    const FETCH_LIMIT: u32 = 5000;

    async fn update_token_supply(
        chain: Chain,
        token_address: &str,
        current_supply: &str,
    ) -> Result<()> {
        if chain != Chain::Solana {
            return Ok(());
        }

        let pubkey = Pubkey::from_str(token_address)?;
        let rpc_client = get_confirmed_rpc_client();
        let mint_info = fetch_mint_info(pubkey, rpc_client).await?;
        let new_token_supply = mint_info.supply;

        if new_token_supply.to_string() == current_supply {
            return Ok(());
        }

        // 1. update db
        let db = PostgresDatabase::get_indexer_db().await;
        let update_timestamp_millis = chrono::Utc::now().timestamp_millis();
        let fb = move || async move {
            db.update_token_metadata_supply(
                chain,
                token_address,
                new_token_supply.into(),
                update_timestamp_millis,
            )
            .await
        };
        fb.retry(ExponentialBuilder::default()).await?;

        // 2. update redis
        let ft = move || async move {
            let redis = RedisClient::get_instance().await;
            if redis.contains_token(chain, token_address).await? {
                redis
                    .set_token_metadata_supply(
                        chain,
                        token_address,
                        new_token_supply.to_string(),
                        update_timestamp_millis,
                    )
                    .await?;
            }
            Ok::<_, anyhow::Error>(())
        };
        ft.retry(ExponentialBuilder::default()).await?;

        Ok(())
    }

    async fn update_statistic_for_token(chain: Chain, token_address: &str) -> Result<()> {
        let redis = RedisClient::get_instance().await;
        let cur_token_statistic = match redis.get_token(chain, token_address).await? {
            Some(token_statistic) => token_statistic,
            None => {
                tracing::error!("[FIXME] Token statistic not found for token: {}", token_address);
                return Err(anyhow::anyhow!(
                    "Token statistic not found for token: {}",
                    token_address
                ));
            }
        };

        if let Err(e) =
            Self::update_token_supply(chain, token_address, &cur_token_statistic.supply).await
        {
            tracing::error!("Failed to update token supply for token {}: {}", token_address, e);
        }

        if !cur_token_statistic.is_active {
            tracing::debug!(
                "Token is not active, skipping statistic update for token: {}",
                token_address
            );
            return Ok(());
        }

        let statistic_update_interval_millis =
            Config::get().statistic_update_interval as i64 * 1000;
        let current_time_millis = chrono::Utc::now().timestamp_millis();
        if cur_token_statistic.update_timestamp_millis >=
            current_time_millis - statistic_update_interval_millis
        {
            tracing::debug!("Token statistic is already up to date for token: {}", token_address);
            return Ok(());
        }

        let native_token_usd_price = NativeTokenPriceManager::get()
            .await
            .get_latest_price(chain)
            .await
            .ok_or(anyhow::anyhow!("Native token price not found"))?;

        let db = PostgresDatabase::get_indexer_db().await;
        let pool_addresses = cur_token_statistic.pool_addresses.split(',').collect::<Vec<_>>();
        let mut cur_pool_states = HashMap::new();
        for pool_address in &pool_addresses {
            let pool_metadata =
                redis.get_pool_metadata_or_update_from_db(chain, pool_address).await?;

            let base_token_usd_price = superstack_data::utils::get_base_token_usd_price(
                chain,
                &pool_metadata.base_address,
                current_time_millis / 1000,
                Some(native_token_usd_price),
            )
            .await?;

            let pool_state = match db.get_latest_pool_state(chain, pool_address).await? {
                Some(pool_state) => pool_state,
                None => {
                    tracing::error!(
                        "[FIXME] Pool state not found for pool address: {}",
                        pool_address
                    );
                    continue;
                }
            };

            Aggregator::aggregate_pool_statistic(
                &pool_metadata,
                &pool_state,
                base_token_usd_price,
                true,
            )
            .await?;

            cur_pool_states.insert(
                pool_address.to_string(),
                PoolStateInfo {
                    is_active: pool_metadata.is_active,
                    dex: pool_metadata.dex,
                    base_address: pool_metadata.base_address,
                    pool_state,
                },
            );
        }

        let token_metadata =
            redis.get_token_metadata_or_update_from_db(chain, token_address).await?;

        Aggregator::aggregate_token_periodic(token_metadata, &cur_pool_states).await?;

        Ok(())
    }

    async fn run_as_producer() -> Result<()> {
        let redis = RedisClient::get_instance().await;

        let ttl_secs = 120;
        let lock =
            DistributedLockGuard::new(redis.clone(), Self::STATISTIC_UPDATE_LOCK_KEY, ttl_secs)
                .await?;
        // If the lock is not acquired, it means that there is another instance of producer running
        if !lock.is_acquired() {
            return Ok(());
        }

        // We have the lock, so we can proceed to produce update statistic messages
        let limit = Self::FETCH_LIMIT;
        let batch_size = Config::get().statistic_updater_concurrency.max(100);

        let tokens = redis.search_active_tokens_by_update_time_asc(limit, 0).await?;

        if tokens.is_empty() {
            tracing::warn!("No tokens to send");
            return Ok(());
        }

        tracing::info!("Sending {} update statistic messages to Kafka", tokens.len());
        let start_time = std::time::Instant::now();

        let mut msgs = Vec::new();
        for batch in tokens.chunks(batch_size) {
            msgs.push(UpdateStatisticMsg { tokens: batch.to_vec() });
        }

        let kafka_producer = KafkaProducer::get();
        for msg in msgs {
            if let Err(e) = kafka_producer.send::<UpdateStatisticMsg>(&msg).await {
                tracing::error!("Failed to send update statistic message to Kafka: {}", e);
            }
        }

        tracing::info!("Sent update statistic messages to Kafka in {:?}", start_time.elapsed());

        Ok(())
    }

    pub fn run(mut rx: mpsc::Receiver<UpdateStatisticMsg>) -> JoinHandle<()> {
        tokio::spawn(async move {
            let config = Config::get();
            let statistic_update_interval_millis = config.statistic_update_interval as i64 * 1000;
            let statistic_update_concurrency = config.statistic_updater_concurrency;
            let semaphore = Arc::new(Semaphore::new(statistic_update_concurrency));

            let sleep_millis = 200; // 200ms
            let time_to_produce = 1000 * 30; // 30s
            let max_sleep_count = time_to_produce / sleep_millis;
            let mut sleep_count = 0;

            let mut processed_count = 0;
            let mut skipped_count = 0;

            loop {
                match rx.try_recv() {
                    Ok(msg) => {
                        let now = chrono::Utc::now().timestamp_millis();
                        for (chain, token_address, update_timestamp_millis) in msg.tokens {
                            // Skip if the token has been updated within the last interval
                            if update_timestamp_millis >= now - statistic_update_interval_millis {
                                skipped_count += 1;
                                continue;
                            }

                            processed_count += 1;
                            let permit = match semaphore.clone().acquire_owned().await {
                                Ok(permit) => permit,
                                Err(e) => {
                                    tracing::error!("Failed to acquire semaphore: {}", e);
                                    break;
                                }
                            };

                            tokio::spawn(async move {
                                if let Err(e) =
                                    Self::update_statistic_for_token(chain, &token_address).await
                                {
                                    tracing::error!(
                                        "Failed to update statistic for token: {} (time diff: {}s): {}",
                                        token_address,
                                        (now - update_timestamp_millis) / 1000,
                                        e
                                    );
                                }

                                drop(permit);
                            });

                            if processed_count % 1000 == 0 {
                                tracing::info!(
                                    "Statistic updater: Processed {} tokens, skipped {} tokens",
                                    processed_count,
                                    skipped_count
                                );
                            }
                        }
                        sleep_count = 0;
                    }
                    Err(mpsc::error::TryRecvError::Empty) => {
                        tokio::time::sleep(Duration::from_millis(sleep_millis)).await;
                        sleep_count += 1;
                        if sleep_count >= max_sleep_count {
                            match Self::run_as_producer().await {
                                Ok(_) => {
                                    tracing::info!("Produced update statistic messages");
                                    sleep_count = 0;
                                }
                                Err(e) => {
                                    tracing::error!(
                                        "Failed to produce update statistic messages: {}",
                                        e
                                    );
                                }
                            }
                        }
                    }
                    Err(mpsc::error::TryRecvError::Disconnected) => {
                        tracing::error!("Update statistic channel disconnected");
                        break;
                    }
                }
            }
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    #[ignore]
    async fn test_update_statistic_loop() -> Result<()> {
        dotenv::dotenv().ok();
        superstack_data::utils::setup_tracing();

        loop {
            let redis = RedisClient::get_instance().await;

            let tokens = redis.search_active_tokens_by_update_time_asc(200, 0).await?;
            tracing::info!("Found {} tokens", tokens.len());
            let mut join_set = tokio::task::JoinSet::new();
            for (chain, token_address, update_timestamp_millis) in tokens {
                let time_diff = chrono::Utc::now().timestamp_millis() - update_timestamp_millis;
                tracing::info!(
                    "Updating statistic for token: {} (time diff: {}s)",
                    token_address,
                    time_diff / 1000
                );
                if time_diff < 1000 * 60 * 5 {
                    tracing::info!(
                        "Skipping token: {} (time diff: {}s)",
                        token_address,
                        time_diff / 1000
                    );
                    continue;
                }

                join_set.spawn(async move {
                    if let Err(e) =
                        StatisticUpdater::update_statistic_for_token(chain, &token_address).await
                    {
                        tracing::error!(
                            "Failed to update statistic for token: {} (time diff: {}s): {}",
                            token_address,
                            time_diff / 1000,
                            e
                        );

                        // // Read from stdin to decide whether to delete the token
                        // use std::io::{self, Write};
                        // tracing::info!("Delete token {} from Redis? (y/N): ",
                        // token.token_address); let mut input =
                        // String::new(); io::stdin().read_line(&mut
                        // input).unwrap(); let input =
                        // input.trim().to_lowercase();

                        // if input != "y" {
                        //     continue;
                        // }
                        //     redis.delete_token(token.chain, &token.token_address).await?;
                        // }
                    }
                });
            }

            let _ = join_set.join_all().await;
            tokio::time::sleep(Duration::from_secs(1)).await;
            tracing::info!("Sleeping for 1 second");
        }

        Ok(())
    }

    #[tokio::test]
    #[ignore]
    async fn test_update_statistic_for_token() -> Result<()> {
        dotenv::dotenv().ok();
        superstack_data::utils::setup_tracing();

        let token_address = "2T1AYo2vzWNn9omm5JxgxUct9g3h7UzJraBmvfDNmz48";
        let chain = Chain::Solana;

        StatisticUpdater::update_statistic_for_token(chain, token_address).await?;

        Ok(())
    }

    #[tokio::test]
    #[ignore]
    async fn test_get_outdated_tokens() -> Result<()> {
        dotenv::dotenv().ok();
        superstack_data::utils::setup_tracing();

        let redis = RedisClient::get_instance().await;
        let start_time = std::time::Instant::now();
        let limit = 10000;
        let tokens = redis.search_active_tokens_by_update_time_asc(limit, 0).await?;
        tracing::info!("Found {} tokens in {}ms", tokens.len(), start_time.elapsed().as_millis());

        Ok(())
    }

    #[tokio::test]
    async fn test_get_tokens_from_redis() -> Result<()> {
        dotenv::dotenv().ok();
        superstack_data::utils::setup_tracing();

        let redis = RedisClient::get_instance().await;
        let start_time = std::time::Instant::now();
        let token_address = "3sZRsydgSG9REcSi3X8AHpqknM2SAWX3ZtaDx3A2G59V";
        let tokens = redis.get_token(Chain::Solana, token_address).await?;
        tracing::info!("Found token in {}ms", start_time.elapsed().as_millis());
        tracing::info!("Token: {:?}", tokens);
        tracing::info!("Token: {:?}", tokens.unwrap().is_active);

        Ok(())
    }
}
