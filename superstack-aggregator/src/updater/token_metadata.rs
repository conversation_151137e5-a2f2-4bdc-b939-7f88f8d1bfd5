use std::time::Duration;

use anyhow::Result;
use backon::{ConstantBuilder, Retryable};
use reqwest::{header, StatusCode};
use superstack_data::{
    constant::USER_AGENT_HEADER_VALUE,
    kafka::KafkaProducer,
    postgres::{indexer::TokenMetadata, Chain, PostgresDatabase, TokenStatistic},
    redis::RedisClient,
    token::solana::{jupiter::JupiterProvider, metaplex},
    utils::get_reqwest_client,
};
use tokio::sync::mpsc;

use super::Image;

const IMAGE_SAVE_DIR: &str = "images";

#[derive(Clone)]
pub struct TokenMetadataUpdater {
    image_getter_tx: mpsc::Sender<(TokenMetadata, ImageUrl)>,
    jupiter_worker_tx: mpsc::Sender<TokenMetadata>,
}

impl TokenMetadataUpdater {
    pub fn new() -> Self {
        let (jupiter_worker_tx, jupiter_worker_rx) = mpsc::channel(1000);
        let (image_getter_tx, image_getter_rx) = mpsc::channel(1000);

        let image_getter = ImageGetter::new(image_getter_rx, jupiter_worker_tx.clone());
        tokio::spawn(image_getter.run());

        let jupiter_worker = JupiterWorker::new(jupiter_worker_rx, image_getter_tx.clone());
        tokio::spawn(jupiter_worker.run());

        Self { image_getter_tx, jupiter_worker_tx }
    }

    pub async fn update_token_metadata_misc_info(
        &self,
        token_metadata: TokenMetadata,
    ) -> Result<()> {
        // Only Solana is supported for now
        if token_metadata.chain != Chain::Solana {
            if let Some(image_url) = token_metadata.image.clone() {
                if !image_url.is_empty() {
                    self.image_getter_tx.send((token_metadata, ImageUrl::Image(image_url))).await?;
                }
            }
            return Ok(());
        }

        let uri = token_metadata.uri.clone().unwrap_or_default();
        if uri.is_empty() {
            self.jupiter_worker_tx.send(token_metadata).await?;
        } else {
            // 1. try metaplex
            if let Err(e) = self.try_metaplex(&uri, token_metadata.clone()).await {
                // 2. try jupiter
                tracing::warn!(
                    "metaplex failed for token: {} {}, try jupiter: {e}",
                    token_metadata.chain,
                    token_metadata.address
                );
                self.jupiter_worker_tx.send(token_metadata).await?;
            }
        }

        Ok(())
    }

    async fn try_metaplex(
        &self,
        metaplex_url: &str,
        mut token_metadata: TokenMetadata,
    ) -> Result<()> {
        let client = get_reqwest_client();

        let mp = metaplex::fetch_metaplex_file(metaplex_url, client).await?;
        token_metadata.description = mp.description;
        token_metadata.website = mp.website;
        token_metadata.twitter = mp.twitter;
        token_metadata.telegram = mp.telegram;

        let url = mp.image.ok_or(anyhow::anyhow!("metaplex no image"))?;
        self.image_getter_tx.send((token_metadata.clone(), ImageUrl::Metaplex(url))).await?;
        Ok(())
    }
}

async fn save_image_file(image: &Image) -> Result<String> {
    let save_dir = &crate::Config::get().static_dir;
    let rel_path = std::path::PathBuf::from(IMAGE_SAVE_DIR)
        .join(&image.image_id)
        .with_extension(&image.image_ext);
    let file_path = std::path::PathBuf::from(save_dir).join(&rel_path);
    tracing::info!("save image {} to: {}", image.image_id, file_path.display());
    tokio::fs::write(file_path, &image.image_data).await?;
    Ok(rel_path.to_string_lossy().to_string())
}

async fn persist_and_try_publish(
    token_metadata: &mut TokenMetadata,
    image_opt: Option<Image>,
) -> Result<()> {
    let db = PostgresDatabase::get_indexer_db().await;
    if let Some(image) = image_opt {
        let rel_path = save_image_file(&image).await?;
        token_metadata.image = Some(image.image_url.clone());
        token_metadata.image_path = Some(rel_path);
        db.insert_token_metadata_or_update_misc(&token_metadata).await?;
    } else {
        db.insert_token_metadata_or_update_misc(&token_metadata).await?;
    }

    // save redis
    let redis_cli = RedisClient::get_instance().await;

    if redis_cli.contains_token_metadata(token_metadata.chain, &token_metadata.address).await? {
        redis_cli
            .set_token_metadata_misc_info(
                token_metadata.chain,
                &token_metadata.address,
                token_metadata.description.clone(),
                token_metadata.image.clone(),
                token_metadata.website.clone(),
                token_metadata.twitter.clone(),
                token_metadata.telegram.clone(),
                token_metadata.image_path.clone(),
            )
            .await?;
    }

    if let Some(mut v) = redis_cli.get_token(token_metadata.chain, &token_metadata.address).await? {
        if v.description != token_metadata.description ||
            v.image != token_metadata.image ||
            v.website != token_metadata.website ||
            v.twitter != token_metadata.twitter ||
            v.telegram != token_metadata.telegram ||
            v.image_path != token_metadata.image_path
        {
            v.description = token_metadata.description.clone();
            v.image = token_metadata.image.clone();
            v.website = token_metadata.website.clone();
            v.twitter = token_metadata.twitter.clone();
            v.telegram = token_metadata.telegram.clone();
            v.image_path = token_metadata.image_path.clone();
            redis_cli
                .set_token_statistic_misc_info(
                    token_metadata.chain,
                    &token_metadata.address,
                    token_metadata.description.clone(),
                    token_metadata.image.clone(),
                    token_metadata.website.clone(),
                    token_metadata.twitter.clone(),
                    token_metadata.telegram.clone(),
                    token_metadata.image_path.clone(),
                )
                .await?;
            // push kafka
            let kafka_producer = KafkaProducer::get();
            kafka_producer.send::<TokenStatistic>(&v).await?;
        }
    }

    Ok(())
}

struct JupiterWorker {
    rx: mpsc::Receiver<TokenMetadata>,
    image_getter_tx: mpsc::Sender<(TokenMetadata, ImageUrl)>,
}

const MAX_ATTEMPTS: usize = 3;
impl JupiterWorker {
    fn new(
        rx: mpsc::Receiver<TokenMetadata>,
        image_getter_tx: mpsc::Sender<(TokenMetadata, ImageUrl)>,
    ) -> Self {
        Self { rx, image_getter_tx }
    }

    async fn run(mut self) {
        tracing::info!("jupiter worker start");
        let client = get_reqwest_client();
        while let Some(msg) = self.rx.recv().await {
            for _ in 0..MAX_ATTEMPTS {
                tracing::debug!("jupiter worker processing token: {} {}", msg.chain, msg.address);
                match JupiterProvider::fetch_token_info(&msg.address, client).await {
                    Ok(Some(info)) => {
                        if let Some(url) = info.logo_uri {
                            if let Err(e) = self
                                .image_getter_tx
                                .send((msg.clone(), ImageUrl::Jupiter(url)))
                                .await
                            {
                                tracing::warn!("jupiter worker send image getter: {:.50}", e);
                            }
                        } else {
                            tracing::warn!(
                                "jupiter no image for token: {} {}",
                                msg.chain,
                                msg.address
                            );
                        };
                        break;
                    }
                    Ok(None) => {
                        tracing::warn!("jupiter too many requests");
                        tokio::time::sleep(Duration::from_secs(10)).await;
                    }
                    Err(e) => {
                        tracing::warn!("jupiter error: {:.50}", e);
                        tokio::time::sleep(Duration::from_secs(2)).await;
                    }
                }
            }

            // sleep 2 seconds to avoid too many requests
            tokio::time::sleep(Duration::from_secs(2)).await;
        }
    }
}

enum ImageUrl {
    Image(String),
    Metaplex(String),
    Jupiter(String),
}

impl ImageUrl {
    fn url(&self) -> &str {
        match self {
            ImageUrl::Metaplex(url) => url,
            ImageUrl::Jupiter(url) => url,
            ImageUrl::Image(url) => url,
        }
    }
}
struct ImageGetter {
    rx: mpsc::Receiver<(TokenMetadata, ImageUrl)>,
    jupiter_worker_tx: mpsc::Sender<TokenMetadata>,
}

impl ImageGetter {
    fn new(
        rx: mpsc::Receiver<(TokenMetadata, ImageUrl)>,
        jupiter_worker_tx: mpsc::Sender<TokenMetadata>,
    ) -> Self {
        Self { rx, jupiter_worker_tx }
    }

    async fn run(mut self) {
        tracing::info!("image getter start");
        while let Some((mut msg, url)) = self.rx.recv().await {
            tracing::debug!("image getter processing token: {} {}", msg.chain, msg.address);
            let result = (|| async { Self::get_image(url.url(), msg.chain, &msg.address).await })
                .retry(ConstantBuilder::new().with_delay(Duration::from_secs(5)).with_jitter())
                .when(|e| {
                    e.downcast_ref::<StatusCode>()
                        .is_some_and(|st| st == &StatusCode::TOO_MANY_REQUESTS) ||
                        e.downcast_ref::<reqwest::Error>().is_some_and(|e| e.is_timeout())
                })
                .await;

            match result {
                Ok(image) => {
                    tracing::debug!("image getter processed token: {} {}", msg.chain, msg.address);
                    if let Err(e) = persist_and_try_publish(&mut msg, Some(image)).await {
                        tracing::error!("image getter failed to persist image: {:.50}", e);
                    }
                }
                Err(e) => {
                    match &url {
                        ImageUrl::Metaplex(_) => {
                            // try jupiter
                            if let Err(e) = self.jupiter_worker_tx.send(msg).await {
                                tracing::warn!("image getter send jupiter worker: {:.50}", e);
                            }
                        }
                        ImageUrl::Jupiter(_) => {
                            tracing::warn!("image getter error for jupiter: {:.50}", e);
                        }
                        ImageUrl::Image(_) => {
                            tracing::warn!("image getter error for image: {:.50}", e);
                        }
                    }
                }
            }
        }
    }

    async fn get_image(url: &str, chain: Chain, address: &str) -> Result<Image> {
        let client = get_reqwest_client();
        let res =
            client.get(url).header(header::USER_AGENT, USER_AGENT_HEADER_VALUE).send().await?;
        anyhow::ensure!(res.status().is_success(), res.status());

        // Extract content-type from header
        let content_type = res
            .headers()
            .get(header::CONTENT_TYPE)
            .and_then(|v| v.to_str().ok())
            .unwrap_or("")
            .to_string();

        let mut image_data = res.bytes().await?.to_vec();

        // Validate that the downloaded data is actually an image file
        let ty = Self::try_extract_image_type(&image_data, &content_type)?;

        // convert svg to png
        if ty.extension() == "svg" {
            tracing::info!("convert svg to png for token: {} {}", chain, address);
            let png_data =
                tokio::task::spawn_blocking(move || super::convert_svg_to_png(&image_data))
                    .await??;
            image_data = png_data;
            tracing::info!("convert svg to png done for token: {} {}", chain, address);
        }

        Ok(Image {
            image_id: Image::construct_image_id(chain, address),
            image_type: ty.mime_type().to_string(),
            image_ext: ty.extension().to_string(),
            image_url: url.to_string(),
            image_data,
        })
    }

    /// Validates if the downloaded data is a valid image file
    /// Checks both content-type header and file magic numbers using infer crate
    fn try_extract_image_type(data: &[u8], content_type: &str) -> Result<infer::Type> {
        // Check if data is empty
        anyhow::ensure!(!data.is_empty(), "data is empty");

        // Check content-type header first
        anyhow::ensure!(
            content_type.starts_with("image/"),
            "content-type not image: {}",
            content_type
        );

        // Special case for svg
        if content_type.starts_with("image/svg+xml") {
            return Ok(infer::Type::new(
                infer::MatcherType::Image,
                "image/svg+xml",
                "svg",
                super::is_svg,
            ));
        }

        // Use infer crate to check magic numbers
        let ty = infer::get(data).ok_or(anyhow::anyhow!("infer failed"))?;
        anyhow::ensure!(
            ty.mime_type().starts_with("image/"),
            "infer detect no image: {}",
            ty.mime_type()
        );

        Ok(ty)
    }
}
