use std::sync::OnceLock;

use anyhow::Result;
use quick_cache::sync::C<PERSON>;
use superstack_data::postgres::{indexer::TokenMetadata, Chain, PostgresDatabase};

const CACHE_SIZE: usize = 50000;

pub struct TokenMetadataMemCache {
    metadata: Cache<(Chain, String), TokenMetadata>,
}

impl TokenMetadataMemCache {
    fn new() -> Self {
        Self { metadata: Cache::new(CACHE_SIZE) }
    }

    pub fn get_instance() -> &'static TokenMetadataMemCache {
        static INSTANCE: OnceLock<TokenMetadataMemCache> = OnceLock::new();
        INSTANCE.get_or_init(TokenMetadataMemCache::new)
    }

    pub async fn get_token(&self, chain: Chain, address: &str) -> Result<TokenMetadata> {
        // Validate address is not empty
        if address.is_empty() {
            tracing::error!("Empty token address provided to get_token");
            return Err(anyhow::anyhow!("Empty token address"));
        }

        let token = self
            .metadata
            .get_or_insert_async(&(chain, address.to_string()), async move {
                let db = PostgresDatabase::get_indexer_db().await;
                match db.get_token_metadata(chain, address).await? {
                    Some(token) => {
                        tracing::debug!("Found token metadata for {}: {}", address, token.name);
                        Ok::<_, anyhow::Error>(token)
                    }
                    None => {
                        tracing::warn!(
                            "Token metadata not found for {} on chain {:?}",
                            address,
                            chain
                        );
                        Err(anyhow::anyhow!("Token metadata not found for {}", address))
                    }
                }
            })
            .await?;

        Ok(token)
    }

    pub fn update_metadata(&self, metadata: &TokenMetadata) {
        self.metadata.insert((metadata.chain, metadata.address.clone()), metadata.clone());
    }

    pub fn remove_metadata(&self, chain: Chain, address: &str) {
        self.metadata.remove(&(chain, address.to_string()));
    }

    pub fn len(&self) -> usize {
        self.metadata.len()
    }
}
