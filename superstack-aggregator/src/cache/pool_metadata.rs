use std::sync::OnceLock;

use anyhow::Result;
use quick_cache::sync::Cache;
use superstack_data::postgres::{indexer::PoolMetadata, Chain, PostgresDatabase};

const CACHE_SIZE: usize = 100000;

pub struct PoolMetadataMemCache {
    metadata: Cache<(Chain, String), PoolMetadata>,
}

impl PoolMetadataMemCache {
    fn new() -> Self {
        Self { metadata: Cache::new(CACHE_SIZE) }
    }

    pub fn get_instance() -> &'static PoolMetadataMemCache {
        static INSTANCE: OnceLock<PoolMetadataMemCache> = OnceLock::new();
        INSTANCE.get_or_init(PoolMetadataMemCache::new)
    }

    pub async fn get_pool(&self, chain: Chain, pool_address: &str) -> Result<PoolMetadata> {
        // Validate pool address is not empty
        if pool_address.is_empty() {
            tracing::error!("Empty pool address provided to get_pool");
            return Err(anyhow::anyhow!("Empty pool address"));
        }

        let pool = self
            .metadata
            .get_or_insert_async(&(chain, pool_address.to_string()), async move {
                let db = PostgresDatabase::get_indexer_db().await;
                match db.get_pool_metadata(chain, pool_address).await? {
                    Some(pool) => {
                        tracing::debug!(
                            "Found pool metadata for {}: {} - {}",
                            pool_address,
                            pool.token_address,
                            pool.base_address
                        );
                        Ok::<_, anyhow::Error>(pool)
                    }
                    None => {
                        tracing::warn!(
                            "Pool metadata not found for {} on chain {:?}, skipping pool to avoid fake data",
                            pool_address, chain
                        );
                        Err(anyhow::anyhow!("Pool metadata not found for {}", pool_address))
                    }
                }
            })
            .await?;

        Ok(pool)
    }

    pub async fn get_pool_without_fallback(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Option<PoolMetadata> {
        // Validate pool address is not empty
        if pool_address.is_empty() {
            tracing::error!("Empty pool address provided to get_pool");
            return None;
        }

        self.metadata.get(&(chain, pool_address.to_string()))
    }

    pub fn update_metadata(&self, metadata: PoolMetadata) {
        self.metadata.insert((metadata.chain, metadata.pool_address.clone()), metadata.clone());
    }

    pub fn len(&self) -> usize {
        self.metadata.len()
    }
}
