use std::sync::OnceLock;

use anyhow::Result;
use quick_cache::sync::C<PERSON>;
use superstack_data::{postgres::Chain, redis::RedisClient};

const CACHE_SIZE: usize = 100000;

pub struct PriceMemCache {
    price: Cache<(Chain, String), (f64, u8)>,
}

impl PriceMemCache {
    fn new() -> Self {
        Self { price: Cache::new(CACHE_SIZE) }
    }

    pub fn get_instance() -> &'static PriceMemCache {
        static INSTANCE: OnceLock<PriceMemCache> = OnceLock::new();
        INSTANCE.get_or_init(PriceMemCache::new)
    }

    pub async fn get_price_and_decimals(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<(f64, u8)> {
        // Validate pool address is not empty
        if token_address.is_empty() {
            tracing::error!("Empty token address provided to get_price_and_decimals");
            return Err(anyhow::anyhow!("Empty token address"));
        }

        let (price, decimals) = self
            .price
            .get_or_insert_async(&(chain, token_address.to_string()), async move {
                let redis_client = RedisClient::get_instance().await;
                match redis_client.get_token_price_and_decimals(chain, token_address).await {
                    Ok((price, decimals)) => Ok::<_, anyhow::Error>((price, decimals)),
                    Err(e) => Err(anyhow::anyhow!(
                        "Failed to get price and decimals for {}: {}",
                        token_address,
                        e
                    )),
                }
            })
            .await?;

        Ok((price, decimals))
    }
}
