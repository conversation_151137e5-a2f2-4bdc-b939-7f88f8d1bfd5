pub mod aggregator;
pub mod cache;
pub mod config;
pub mod consumers;
pub mod healthz;
pub mod updater;
pub mod utils;

use anyhow::Result;
use backon::{ExponentialBuilder, Retryable};
use superstack_data::{
    kafka::{topics::UpdateStatisticMsg, KafkaProducer},
    postgres::PostgresDatabase,
    price::NativeTokenPriceManager,
};
use tokio::sync::mpsc;

use crate::{
    aggregator::store_task::StoreTask,
    config::Config,
    consumers::{
        IndexerBlockConsumer, PoolStatisticConsumer, TokenHoldersConsumer, TokenMetadataConsumer,
        TokenStatisticConsumer, UpdateStatisticConsumer,
    },
    updater::StatisticUpdater,
};

#[tokio::main]
async fn main() -> Result<()> {
    dotenv::dotenv().ok();

    // Setup tracing
    superstack_data::utils::setup_tracing();

    let config = Config::get();

    // Initialize database
    let _db = PostgresDatabase::get_indexer_db().await;

    // Initialize price manager
    tracing::info!("Initializing price manager");
    let _price_manager = NativeTokenPriceManager::get().await;

    // Initialize kafka producer for indexer topics
    tracing::info!("Initializing kafka producer for indexer topics");
    let ft = move || async move {
        let producer = KafkaProducer::get();
        producer.create_aggregator_topics_if_not_exists().await
    };
    ft.retry(ExponentialBuilder::default()).await.expect("Failed to initialize indexer topics");

    // Start healthz server
    let healthz_server = healthz::spawn_server(config.port, "/hz");

    // Initialize store task
    tracing::info!("Initializing store task");
    let config = Config::get();
    let store_channel_size = config.block_consumer_concurrency.max(100) * 50;
    let store_join_handle = StoreTask::run(store_channel_size);

    let block_consumer_task = IndexerBlockConsumer::run();

    let holders_consumer_task = TokenHoldersConsumer::run();

    let token_metadata_consumer_task = TokenMetadataConsumer::new().run();

    let channel_size = config.statistic_updater_concurrency * 10;
    let (tx, rx) = mpsc::channel::<UpdateStatisticMsg>(channel_size);
    let update_statistic_consumer_task = UpdateStatisticConsumer::run(tx);
    let statistic_updater_task = StatisticUpdater::run(rx);

    let pool_statistic_consumer_task = PoolStatisticConsumer::run();
    let token_statistic_consumer_task = TokenStatisticConsumer::run();

    tokio::select! {
        _ = healthz_server => {
            tracing::info!("Healthz server completed");
        }
        _ = block_consumer_task => {
            tracing::info!("Block consumer task 1 completed");
        }
        _ = holders_consumer_task => {
            tracing::info!("Token Holders Consumer task completed");
        }
        _ = token_metadata_consumer_task => {
            tracing::info!("Token Metadata Consumer task completed");
        }
        _ = update_statistic_consumer_task => {
            tracing::info!("Update Statistic Consumer task completed");
        }
        _ = pool_statistic_consumer_task => {
            tracing::info!("Pool Statistic Consumer task completed");
        }
        _ = token_statistic_consumer_task => {
            tracing::info!("Token Statistic Consumer task completed");
        }
        _ = statistic_updater_task => {
            tracing::info!("Statistic Updater task completed");
        }
        _ = store_join_handle => {
            tracing::info!("Store task completed");
        }
    }

    Ok(())
}
