use std::str::FromStr;

use anyhow::Result;
use solana_sdk::{native_token::lamports_to_sol, pubkey::Pubkey};
use superstack_data::{postgres::Chain, utils::get_confirmed_rpc_client};

pub fn is_native_token(chain: Chain, address: &str) -> bool {
    match chain {
        Chain::Solana => address == superstack_data::constant::solana::SOL_MINT,
        Chain::Hypercore => {
            // For Hypercore, all base tokens are USD-pegged (USDC or similar)
            // Since aggregator handles both native and non-native tokens correctly,
            // and all HyperCore base tokens have USD price = 1.0, we can treat them all as "native"
            // This ensures consistent price calculation regardless of the specific base token ID
            tracing::debug!("HyperCore base token (treating as USD-native): {}", address);
            true
        }
        Chain::HyperEvm => {
            // For HyperEvm, check if it's the HYPE token (native token)
            // HYPE token address: ******************************************
            address == "******************************************"
        }
    }
}

pub async fn get_native_token_balance(chain: Chain, wallet_address: &str) -> Result<f64> {
    match chain {
        Chain::Solana => {
            let rpc_client = get_confirmed_rpc_client();
            let pubkey = Pubkey::from_str(wallet_address)?;
            let balance = rpc_client.get_balance(&pubkey).await?;
            let ui_amount = lamports_to_sol(balance);
            Ok(ui_amount)
        }
        Chain::Hypercore => {
            tracing::debug!(
                "Getting native token balance for Hypercore not implemented, returning 0.0"
            );
            Ok(0.0)
        }
        Chain::HyperEvm => {
            tracing::debug!(
                "Getting native token balance for HyperEvm not implemented, returning 0.0"
            );
            Ok(0.0)
        }
    }
}
