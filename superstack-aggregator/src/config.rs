use std::{env, sync::OnceLock};

use superstack_data::config::Config as DataConfig;

pub struct Config {
    pub port: u16,
    pub block_consumer_concurrency: usize,
    pub holders_consumer_concurrency: usize,
    pub token_consumer_concurrency: usize,
    pub pool_consumer_concurrency: usize,
    pub statistic_updater_concurrency: usize,
    pub statistic_update_interval: u64,

    pub max_liquidity: Option<u64>,
    pub max_volume: Option<u64>,
    pub max_txns: Option<u64>,

    pub kafka_auto_commit_interval_secs: u64,

    pub min_liquidity_for_active: u64,

    pub static_dir: String,

    pub data_config: &'static DataConfig,
}

impl Config {
    pub fn get() -> &'static Config {
        static INSTANCE: OnceLock<Config> = OnceLock::new();

        INSTANCE.get_or_init(|| {
            let port = env::var("PORT").unwrap_or_else(|_| "3000".to_string());

            let block_consumer_concurrency = env::var("BLOCK_CONSUMER_CONCURRENCY")
                .unwrap_or_else(|_| "200".to_string())
                .parse::<usize>()
                .unwrap();
            let holders_consumer_concurrency = env::var("HOLDERS_CONSUMER_CONCURRENCY")
                .unwrap_or_else(|_| "100".to_string())
                .parse::<usize>()
                .unwrap();
            let token_consumer_concurrency = env::var("TOKEN_CONSUMER_CONCURRENCY")
                .unwrap_or_else(|_| "10".to_string())
                .parse::<usize>()
                .unwrap();
            let pool_consumer_concurrency = env::var("POOL_CONSUMER_CONCURRENCY")
                .unwrap_or_else(|_| "10".to_string())
                .parse::<usize>()
                .unwrap();

            let statistic_updater_concurrency = env::var("STATISTIC_UPDATER_CONCURRENCY")
                .unwrap_or_else(|_| "100".to_string())
                .parse::<usize>()
                .unwrap();
            let statistic_update_interval = env::var("STATISTIC_UPDATE_INTERVAL")
                .unwrap_or_else(|_| "300".to_string())
                .parse::<u64>()
                .unwrap()
                .max(60);

            let max_liquidity = env::var("MAX_LIQUIDITY")
                .map(|v| v.parse::<u64>().expect("MAX_LIQUIDITY must be a number"))
                .ok();

            let max_volume = env::var("MAX_VOLUME")
                .map(|v| v.parse::<u64>().expect("MAX_VOLUME must be a number"))
                .ok();

            let max_txns = env::var("MAX_TXNS")
                .map(|v| v.parse::<u64>().expect("MAX_TXNS must be a number"))
                .ok();

            let kafka_auto_commit_interval_secs = env::var("KAFKA_AUTO_COMMIT_INTERVAL_SECS")
                .unwrap_or_else(|_| "15".to_string())
                .parse::<u64>()
                .unwrap();

            let min_liquidity_for_active = env::var("MIN_LIQUIDITY_FOR_ACTIVE")
                .map(|v| v.parse::<u64>().expect("MIN_LIQUIDITY_FOR_ACTIVE must be a number"))
                .unwrap_or(1000);

            let static_dir = env::var("STATIC_DIR").unwrap_or("/static-bucket".to_string());

            let data_config = DataConfig::get();

            tracing::info!(
                "Config:
                    port: {},
                    block_consumer_concurrency: {},
                    holders_consumer_concurrency: {},
                    token_consumer_concurrency: {},
                    pool_consumer_concurrency: {},
                    statistic_updater_concurrency: {},
                    statistic_update_interval: {}s,
                    kafka_auto_commit_interval_secs: {}s,
                    min_liquidity_for_active: {} usd,
                    max_db_connections: {},
                    max_liquidity: {:?} usd,
                    max_volume: {:?} usd,
                    max_txns: {:?},
                    static_dir: {}",
                port,
                block_consumer_concurrency,
                holders_consumer_concurrency,
                token_consumer_concurrency,
                pool_consumer_concurrency,
                statistic_updater_concurrency,
                statistic_update_interval,
                kafka_auto_commit_interval_secs,
                min_liquidity_for_active,
                data_config.postgres_indexer_max_connections,
                max_liquidity,
                max_volume,
                max_txns,
                static_dir,
            );

            Config {
                port: port.parse().unwrap(),
                block_consumer_concurrency,
                holders_consumer_concurrency,
                token_consumer_concurrency,
                pool_consumer_concurrency,
                statistic_updater_concurrency,
                statistic_update_interval,
                min_liquidity_for_active,
                max_liquidity,
                max_volume,
                max_txns,
                kafka_auto_commit_interval_secs,
                static_dir,
                data_config,
            }
        })
    }
}
