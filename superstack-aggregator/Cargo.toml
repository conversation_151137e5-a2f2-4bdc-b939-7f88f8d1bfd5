[package]
name = "superstack-aggregator"
version.workspace = true
edition.workspace = true

[dependencies]
tokio = { workspace = true }
axum = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
anyhow = { workspace = true }
dotenv = { workspace = true }
quick_cache = { workspace = true }
chrono = { workspace = true }
solana-sdk = { workspace = true }
futures = { workspace = true }
serde = { workspace = true }
reqwest = { workspace = true }
backon = { workspace = true }
alloy = { workspace = true }
num-traits = { workspace = true }
rdkafka = { workspace = true }
indexmap = { workspace = true }
# mime detection
infer = "0.19.0"
# svg2png
resvg = "0.37"

superstack-data = { path = "../superstack-data" }

[[bin]]
name = "aggregator"
path = "src/main.rs"
