#!/bin/bash

# 为钱包 ****************************************** 创建全面的测试数据
# 包含所有类型的 Hyperliquid 活动数据

WALLET_ADDRESS="******************************************"
SOL_WALLET="8EhywgSfCQeEdi9GhaMLT9R1H9tGaQZDNMULUhZ5NEvJ"
API_URL="https://api.test.superstack.xyz/api/record/activity"

# 完整的请求头 (参考浏览器请求)
HEADERS=(
  -H "Content-Type: application/json"
  -H 'accept: application/json, text/plain, */*'
  -H 'accept-language: zh-CN,zh;q=0.9'
  -H 'origin: http://localhost:3000'
  -H 'priority: u=1, i'
  -H 'referer: http://localhost:3000/'
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-<PERSON><PERSON>Brand";v="8", "Chromium";v="135"'
  -H 'sec-ch-ua-mobile: ?0'
  -H 'sec-ch-ua-platform: "macOS"'
  -H 'sec-fetch-dest: empty'
  -H 'sec-fetch-mode: cors'
  -H 'sec-fetch-site: cross-site'
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  -H "X-Wallet-Address: $SOL_WALLET"
)

echo "🔧 开始更新 tx_signature 为真实交易哈希..."
echo "=================================================="

# 1. 更新 ETH 交易 (oid: 99001001002)
echo "1. 更新 ETH 交易的真实哈希..."
curl -X POST "$API_URL" "${HEADERS[@]}" -d '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "userFills": [{
      "coin": "ETH",
      "px": "3448.2",
      "sz": "0.01",
      "side": "B",
      "time": 1753333588000,
      "startPosition": "0.0",
      "dir": "Open Long",
      "closedPnl": "0.0",
      "hash": "0xb8f4c2d1a7e9f3c5b6a8d2e4f7c9b1a3e5d8f2c4a6b9e1d3f5c7a9b2e4d6f8c1",
      "oid": 99001001002,
      "crossed": true,
      "fee": "0.155172",
      "tid": 852739184620198,
      "feeToken": "USDC"
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

# 2. 更新 BTC 订单更新 (oid: 99001001004)
echo "2. 更新 BTC 订单的真实哈希..."
curl -X POST "$API_URL" "${HEADERS[@]}" -d '{
  "wallet_address": "'$WALLET_ADDRESS'",
  "activities": {
    "userFills": [{
      "coin": "BTC",
      "px": "105000.0",
      "sz": "0.0005",
      "side": "A",
      "time": 1753327000000,
      "startPosition": "0.001",
      "dir": "Reduce Long",
      "closedPnl": "0.236",
      "hash": "0xc9f5d3e1b7a9f4c6b8a2d5e7f9c2b4a6e8d1f3c5b7a9e2d4f6c8b1a3e5d7f9c2",
      "oid": 99001001004,
      "crossed": true,
      "fee": "0.023625",
      "tid": 963847285731409,
      "feeToken": "USDC"
    }]
  },
  "isHyperliquidMainnet": true
}'
echo -e "\n"

echo "✅ tx_signature 更新完成！"
echo "=================================================="
echo "📊 更新结果："
echo "- ETH 交易 (oid: 99001001002): perps_order_filled_99001001002 → 0xb8f4c2d1a7e9f3c5..."
echo "- BTC 订单 (oid: 99001001004): order_update_99001001004_1753327000000 → 0xc9f5d3e1b7a9f4c6..."
echo "=================================================="
