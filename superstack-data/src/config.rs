use std::{env, sync::OnceLock};

pub struct Config {
    pub solana_rpc_url: String,

    pub postgres_indexer_database_url: String,
    pub postgres_indexer_max_connections: u32,
    pub postgres_indexer_need_migrate: bool,

    pub store_price_history: bool,

    pub kafka_bootstrap_servers: String,
    pub kafka_api_key: String,
    pub kafka_api_secret: String,
    pub kafka_indexer_topic_partition: Option<u16>,
    pub kafka_aggregator_topic_partition: Option<u16>,

    pub redis_url: String,

    pub coingecko_api_key: String,
}

impl Config {
    fn new() -> Self {
        let solana_rpc_url = env::var("SOLANA_RPC_URL").expect("SOLANA_RPC_URL is not set");

        let postgres_indexer_database_url = env::var("POSTGRES_INDEXER_DATABASE_URL")
            .expect("POSTGRES_INDEXER_DATABASE_URL is not set");
        let postgres_indexer_max_connections = env::var("POSTGRES_INDEXER_MAX_CONNECTIONS")
            .unwrap_or("300".to_string())
            .parse::<u32>()
            .expect("POSTGRES_INDEXER_MAX_CONNECTIONS is not a valid number");
        let postgres_indexer_need_migrate = env::var("POSTGRES_INDEXER_NEED_MIGRATE")
            .unwrap_or("true".to_string())
            .parse::<bool>()
            .expect("POSTGRES_INDEXER_NEED_MIGRATE is not a valid boolean");

        let store_price_history = env::var("STORE_PRICE_HISTORY")
            .expect("STORE_PRICE_HISTORY is not set")
            .parse::<bool>()
            .unwrap_or(false);

        let redis_url = env::var("REDIS_URL").expect("REDIS_URL is not set");

        let kafka_bootstrap_servers =
            env::var("KAFKA_BOOTSTRAP_SERVERS").expect("KAFKA_BOOTSTRAP_SERVERS is not set");
        let kafka_api_key = env::var("KAFKA_API_KEY").expect("KAFKA_API_KEY is not set");
        let kafka_api_secret = env::var("KAFKA_API_SECRET").expect("KAFKA_API_SECRET is not set");

        let kafka_indexer_topic_partition = env::var("KAFKA_INDEXER_TOPIC_PARTITION")
            .map(|v| v.parse::<u16>().expect("KAFKA_INDEXER_TOPIC_PARTITION is not a valid number"))
            .ok();
        let kafka_aggregator_topic_partition = env::var("KAFKA_AGGREGATOR_TOPIC_PARTITION")
            .map(|v| {
                v.parse::<u16>().expect("KAFKA_AGGREGATOR_TOPIC_PARTITION is not a valid number")
            })
            .ok();

        let coingecko_api_key =
            env::var("COINGECKO_API_KEY").expect("COINGECKO_API_KEY is not set");

        Self {
            solana_rpc_url,
            postgres_indexer_database_url,
            postgres_indexer_max_connections,
            postgres_indexer_need_migrate,
            store_price_history,
            redis_url,
            kafka_bootstrap_servers,
            kafka_api_key,
            kafka_api_secret,
            kafka_indexer_topic_partition,
            kafka_aggregator_topic_partition,
            coingecko_api_key,
        }
    }

    pub fn get() -> &'static Config {
        static INSTANCE: OnceLock<Config> = OnceLock::new();

        INSTANCE.get_or_init(|| Config::new())
    }
}
