#[derive(Debug, thiserror::Error)]
pub enum Error {
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("Migrate error: {0}")]
    MigrateError(#[from] sqlx::migrate::MigrateError),

    #[error("Kafka error: {0}")]
    Kafka(#[from] rdkafka::error::KafkaError),

    #[error("Kafka consumer error: {0}")]
    KafkaConsumerError(#[from] crate::kafka::consumer::ConsumerError),

    #[error("Kafka producer error: {0}")]
    KafkaProducerError(#[from] crate::kafka::producer::ProducerError),

    #[error("Anyhow error: {0}")]
    AnyhowError(#[from] anyhow::Error),

    #[error("Bincode error: {0}")]
    BincodeError(#[from] bincode::Error),

    #[error("Reqwest error: {0}")]
    ReqwestError(#[from] reqwest::Error),

    #[error("CoinGecko error: {0}")]
    CoingeckoError(#[from] crate::price::coingecko::CoinGeckoError),

    #[error("Redis error: {0}")]
    RedisError(#[from] redis::RedisError),

    #[error("U256 error: {0}")]
    U256Error(String),

    #[error("BigDecimal error: {0}")]
    BigDecimalError(String),

    #[error("Tokio join error: {0}")]
    TokioJoinError(#[from] tokio::task::JoinError),

    #[error("ParseBigDecimalError: {0}")]
    ParseBigDecimalError(#[from] bigdecimal::ParseBigDecimalError),
}

pub type Result<T> = core::result::Result<T, Error>;
