use std::collections::HashMap;

use futures::StreamExt;
use redis::AsyncCommands;

use super::{utils::*, value::RedisValue, *};
use crate::{
    error::Result,
    postgres::{aggregator::PoolStatistic, Chain},
};

const POOL_STATISTIC_KEY_PREFIX: &str = "pool:";
const POOL_STATISTIC_INDEX: &str = "pool_idx";

// Top10 trending pools cache configuration
const TRENDING_POOLS_CACHE_PREFIX: &str = "trending_pools:";
const TRENDING_POOLS_CACHE_TTL_SECONDS: u64 = 30;
const DEFAULT_TRENDING_LIMIT: u64 = 10;

// Human-readable constants for short field names
const FIELD_CHAIN: &str = "c";
const FIELD_POOL_ADDRESS: &str = "pa";
const FIELD_PAIR_LABEL: &str = "pl";
const FIELD_DEX: &str = "dx";
const FIELD_POOL_TYPE: &str = "pt";
const FIELD_CREATE_TIMESTAMP: &str = "ct";
const FIELD_TOKEN_ADDRESS: &str = "ta";
const FIELD_TOKEN_DECIMALS: &str = "td";
const FIELD_BASE_ADDRESS: &str = "ba";
const FIELD_BASE_DECIMALS: &str = "bd";
const FIELD_IS_TOKEN_FIRST: &str = "tf";
const FIELD_IS_ACTIVE: &str = "ia";
const FIELD_USD_PRICE: &str = "up";
const FIELD_USD_MARKET_CAP: &str = "um";
const FIELD_USD_LIQUIDITY: &str = "ul";
const FIELD_BONDING_CURVE_PROGRESS: &str = "bc";
const FIELD_PRICE: &str = "p";
const FIELD_MARKET_CAP: &str = "m";
const FIELD_LIQUIDITY: &str = "l";
const FIELD_TOTAL_VOLUME: &str = "tv";
const FIELD_TOTAL_BUY_VOLUME: &str = "bv";
const FIELD_TOTAL_SELL_VOLUME: &str = "sv";
const FIELD_TOTAL_TXNS: &str = "tx";
const FIELD_TOTAL_BUY_TXNS: &str = "tb";
const FIELD_TOTAL_SELL_TXNS: &str = "ts";
const FIELD_PRICE_CHANGE_5M: &str = "p5";
const FIELD_TXNS_5M: &str = "t5";
const FIELD_BUY_TXNS_5M: &str = "b5";
const FIELD_SELL_TXNS_5M: &str = "s5";
const FIELD_USD_VOLUME_5M: &str = "v5";
const FIELD_USD_BUY_VOLUME_5M: &str = "y5";
const FIELD_USD_SELL_VOLUME_5M: &str = "l5";
const FIELD_MAKERS_5M: &str = "m5";
const FIELD_BUYERS_5M: &str = "u5";
const FIELD_SELLERS_5M: &str = "e5";
const FIELD_PRICE_CHANGE_1H: &str = "p1";
const FIELD_TXNS_1H: &str = "t1";
const FIELD_BUY_TXNS_1H: &str = "b1";
const FIELD_SELL_TXNS_1H: &str = "s1";
const FIELD_USD_VOLUME_1H: &str = "v1";
const FIELD_USD_BUY_VOLUME_1H: &str = "y1";
const FIELD_USD_SELL_VOLUME_1H: &str = "l1";
const FIELD_MAKERS_1H: &str = "m1";
const FIELD_BUYERS_1H: &str = "u1";
const FIELD_SELLERS_1H: &str = "e1";
const FIELD_PRICE_CHANGE_6H: &str = "p6";
const FIELD_TXNS_6H: &str = "t6";
const FIELD_BUY_TXNS_6H: &str = "b6";
const FIELD_SELL_TXNS_6H: &str = "s6";
const FIELD_USD_VOLUME_6H: &str = "v6";
const FIELD_USD_BUY_VOLUME_6H: &str = "y6";
const FIELD_USD_SELL_VOLUME_6H: &str = "l6";
const FIELD_MAKERS_6H: &str = "m6";
const FIELD_BUYERS_6H: &str = "u6";
const FIELD_SELLERS_6H: &str = "e6";
const FIELD_PRICE_CHANGE_24H: &str = "p2";
const FIELD_TXNS_24H: &str = "t2";
const FIELD_BUY_TXNS_24H: &str = "b2";
const FIELD_SELL_TXNS_24H: &str = "s2";
const FIELD_USD_VOLUME_24H: &str = "v2";
const FIELD_USD_BUY_VOLUME_24H: &str = "y2";
const FIELD_USD_SELL_VOLUME_24H: &str = "l2";
const FIELD_MAKERS_24H: &str = "m2";
const FIELD_BUYERS_24H: &str = "u2";
const FIELD_SELLERS_24H: &str = "e2";
const FIELD_UPDATE_TIMESTAMP: &str = "ut";

// Cache keys for trending pools
const TRENDING_POOLS_CACHE_KEY: &str = "trending_pools_cache";
const TRENDING_POOLS_CACHE_TTL: u64 = 300; // 5 minutes

impl RedisClient {
    fn construct_pool_statistic_key(chain: Chain, pool_address: &str) -> String {
        format!("{}{}:{}", POOL_STATISTIC_KEY_PREFIX, pool_address, chain.to_string())
    }

    // Convert PoolStatistics to HashMap with short field names and proper types
    fn pool_statistic_to_hash(pool_statistic: &PoolStatistic) -> Vec<(&'static str, RedisValue)> {
        let mut hash_data = vec![
            // Basic metadata - using short keys to save space
            (FIELD_CHAIN, RedisValue::String(pool_statistic.chain.to_string())),
            (FIELD_POOL_ADDRESS, RedisValue::String(pool_statistic.pool_address.clone())),
            (FIELD_PAIR_LABEL, RedisValue::String(pool_statistic.pair_label.clone())),
            (FIELD_DEX, RedisValue::String(pool_statistic.dex.to_string())),
            (FIELD_POOL_TYPE, RedisValue::String(pool_statistic.pool_type.to_string())),
            (FIELD_CREATE_TIMESTAMP, RedisValue::Int64(pool_statistic.create_timestamp_millis)),
            (FIELD_TOKEN_ADDRESS, RedisValue::String(pool_statistic.token_address.clone())),
            (FIELD_TOKEN_DECIMALS, RedisValue::UInt8(pool_statistic.token_decimals)),
            (FIELD_BASE_ADDRESS, RedisValue::String(pool_statistic.base_address.clone())),
            (FIELD_BASE_DECIMALS, RedisValue::UInt8(pool_statistic.base_decimals)),
            (FIELD_IS_TOKEN_FIRST, RedisValue::Bool(pool_statistic.is_token_first)),
            (FIELD_IS_ACTIVE, RedisValue::Bool(pool_statistic.is_active)),
            // pool state info
            (FIELD_USD_PRICE, RedisValue::Float64(pool_statistic.usd_price)),
            (FIELD_USD_MARKET_CAP, RedisValue::Float64(pool_statistic.usd_market_cap)),
            (FIELD_USD_LIQUIDITY, RedisValue::Float64(pool_statistic.usd_liquidity)),
            (FIELD_PRICE, RedisValue::Float64(pool_statistic.price)),
            (FIELD_MARKET_CAP, RedisValue::Float64(pool_statistic.market_cap)),
            (FIELD_LIQUIDITY, RedisValue::Float64(pool_statistic.liquidity)),
            (FIELD_TOTAL_VOLUME, RedisValue::Float64(pool_statistic.total_volume)),
            (FIELD_TOTAL_BUY_VOLUME, RedisValue::Float64(pool_statistic.total_buy_volume)),
            (FIELD_TOTAL_SELL_VOLUME, RedisValue::Float64(pool_statistic.total_sell_volume)),
            (FIELD_TOTAL_TXNS, RedisValue::UInt64(pool_statistic.total_txns)),
            (FIELD_TOTAL_BUY_TXNS, RedisValue::UInt64(pool_statistic.total_buy_txns)),
            (FIELD_TOTAL_SELL_TXNS, RedisValue::UInt64(pool_statistic.total_sell_txns)),
            // 5m statistics
            (FIELD_PRICE_CHANGE_5M, RedisValue::Float64(pool_statistic.price_change_5m)),
            (FIELD_TXNS_5M, RedisValue::UInt64(pool_statistic.txns_5m)),
            (FIELD_BUY_TXNS_5M, RedisValue::UInt64(pool_statistic.buy_txns_5m)),
            (FIELD_SELL_TXNS_5M, RedisValue::UInt64(pool_statistic.sell_txns_5m)),
            (FIELD_USD_VOLUME_5M, RedisValue::Float64(pool_statistic.usd_volume_5m)),
            (FIELD_USD_BUY_VOLUME_5M, RedisValue::Float64(pool_statistic.usd_buy_volume_5m)),
            (FIELD_USD_SELL_VOLUME_5M, RedisValue::Float64(pool_statistic.usd_sell_volume_5m)),
            (FIELD_MAKERS_5M, RedisValue::UInt64(pool_statistic.makers_5m)),
            (FIELD_BUYERS_5M, RedisValue::UInt64(pool_statistic.buyers_5m)),
            (FIELD_SELLERS_5M, RedisValue::UInt64(pool_statistic.sellers_5m)),
            // 1h statistics
            (FIELD_PRICE_CHANGE_1H, RedisValue::Float64(pool_statistic.price_change_1h)),
            (FIELD_TXNS_1H, RedisValue::UInt64(pool_statistic.txns_1h)),
            (FIELD_BUY_TXNS_1H, RedisValue::UInt64(pool_statistic.buy_txns_1h)),
            (FIELD_SELL_TXNS_1H, RedisValue::UInt64(pool_statistic.sell_txns_1h)),
            (FIELD_USD_VOLUME_1H, RedisValue::Float64(pool_statistic.usd_volume_1h)),
            (FIELD_USD_BUY_VOLUME_1H, RedisValue::Float64(pool_statistic.usd_buy_volume_1h)),
            (FIELD_USD_SELL_VOLUME_1H, RedisValue::Float64(pool_statistic.usd_sell_volume_1h)),
            (FIELD_MAKERS_1H, RedisValue::UInt64(pool_statistic.makers_1h)),
            (FIELD_BUYERS_1H, RedisValue::UInt64(pool_statistic.buyers_1h)),
            (FIELD_SELLERS_1H, RedisValue::UInt64(pool_statistic.sellers_1h)),
            // 6h statistics
            (FIELD_PRICE_CHANGE_6H, RedisValue::Float64(pool_statistic.price_change_6h)),
            (FIELD_TXNS_6H, RedisValue::UInt64(pool_statistic.txns_6h)),
            (FIELD_BUY_TXNS_6H, RedisValue::UInt64(pool_statistic.buy_txns_6h)),
            (FIELD_SELL_TXNS_6H, RedisValue::UInt64(pool_statistic.sell_txns_6h)),
            (FIELD_USD_VOLUME_6H, RedisValue::Float64(pool_statistic.usd_volume_6h)),
            (FIELD_USD_BUY_VOLUME_6H, RedisValue::Float64(pool_statistic.usd_buy_volume_6h)),
            (FIELD_USD_SELL_VOLUME_6H, RedisValue::Float64(pool_statistic.usd_sell_volume_6h)),
            (FIELD_MAKERS_6H, RedisValue::UInt64(pool_statistic.makers_6h)),
            (FIELD_BUYERS_6H, RedisValue::UInt64(pool_statistic.buyers_6h)),
            (FIELD_SELLERS_6H, RedisValue::UInt64(pool_statistic.sellers_6h)),
            // 24h statistics
            (FIELD_PRICE_CHANGE_24H, RedisValue::Float64(pool_statistic.price_change_24h)),
            (FIELD_TXNS_24H, RedisValue::UInt64(pool_statistic.txns_24h)),
            (FIELD_BUY_TXNS_24H, RedisValue::UInt64(pool_statistic.buy_txns_24h)),
            (FIELD_SELL_TXNS_24H, RedisValue::UInt64(pool_statistic.sell_txns_24h)),
            (FIELD_USD_VOLUME_24H, RedisValue::Float64(pool_statistic.usd_volume_24h)),
            (FIELD_USD_BUY_VOLUME_24H, RedisValue::Float64(pool_statistic.usd_buy_volume_24h)),
            (FIELD_USD_SELL_VOLUME_24H, RedisValue::Float64(pool_statistic.usd_sell_volume_24h)),
            (FIELD_MAKERS_24H, RedisValue::UInt64(pool_statistic.makers_24h)),
            (FIELD_BUYERS_24H, RedisValue::UInt64(pool_statistic.buyers_24h)),
            (FIELD_SELLERS_24H, RedisValue::UInt64(pool_statistic.sellers_24h)),
            // Update info
            (FIELD_UPDATE_TIMESTAMP, RedisValue::Int64(pool_statistic.update_timestamp_millis)),
        ];

        if let Some(bonding_curve_progress) = pool_statistic.bonding_curve_progress {
            hash_data
                .push((FIELD_BONDING_CURVE_PROGRESS, RedisValue::Float64(bonding_curve_progress)));
        }

        hash_data
    }

    // Convert HashMap back to PoolStatistics
    fn hash_to_pool_statistic(hash_data: &HashMap<String, String>) -> Result<PoolStatistic> {
        Ok(PoolStatistic {
            // Basic metadata
            chain: parse_chain_from_map(hash_data, FIELD_CHAIN)?,
            pool_address: parse_string_from_map(hash_data, FIELD_POOL_ADDRESS)?,
            pair_label: parse_string_from_map(hash_data, FIELD_PAIR_LABEL)?,
            dex: parse_dex_from_map(hash_data, FIELD_DEX)?,
            pool_type: parse_pool_type_from_map(hash_data, FIELD_POOL_TYPE)?,
            create_timestamp_millis: parse_i64_from_map(hash_data, FIELD_CREATE_TIMESTAMP)?,
            token_address: parse_string_from_map(hash_data, FIELD_TOKEN_ADDRESS)?,
            token_decimals: parse_u8_from_map(hash_data, FIELD_TOKEN_DECIMALS)?,
            base_address: parse_string_from_map(hash_data, FIELD_BASE_ADDRESS)?,
            base_decimals: parse_u8_from_map(hash_data, FIELD_BASE_DECIMALS)?,
            is_token_first: parse_bool_from_map(hash_data, FIELD_IS_TOKEN_FIRST)?,
            is_active: parse_bool_from_map(hash_data, FIELD_IS_ACTIVE)?,
            usd_price: parse_f64_from_map(hash_data, FIELD_USD_PRICE)?,
            usd_market_cap: parse_f64_from_map(hash_data, FIELD_USD_MARKET_CAP)?,
            usd_liquidity: parse_f64_from_map(hash_data, FIELD_USD_LIQUIDITY)?,
            bonding_curve_progress: parse_f64_from_map(hash_data, FIELD_BONDING_CURVE_PROGRESS)
                .ok(),
            price: parse_f64_from_map(hash_data, FIELD_PRICE)?,
            market_cap: parse_f64_from_map(hash_data, FIELD_MARKET_CAP)?,
            liquidity: parse_f64_from_map(hash_data, FIELD_LIQUIDITY)?,
            total_volume: parse_f64_from_map(hash_data, FIELD_TOTAL_VOLUME)?,
            total_buy_volume: parse_f64_from_map(hash_data, FIELD_TOTAL_BUY_VOLUME)?,
            total_sell_volume: parse_f64_from_map(hash_data, FIELD_TOTAL_SELL_VOLUME)?,
            total_txns: parse_u64_from_map(hash_data, FIELD_TOTAL_TXNS)?,
            total_buy_txns: parse_u64_from_map(hash_data, FIELD_TOTAL_BUY_TXNS)?,
            total_sell_txns: parse_u64_from_map(hash_data, FIELD_TOTAL_SELL_TXNS)?,
            // 5m statistics
            price_change_5m: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_5M)?,
            txns_5m: parse_u64_from_map(hash_data, FIELD_TXNS_5M)?,
            buy_txns_5m: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_5M)?,
            sell_txns_5m: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_5M)?,
            usd_volume_5m: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_5M)?,
            usd_buy_volume_5m: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_5M)?,
            usd_sell_volume_5m: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_5M)?,
            makers_5m: parse_u64_from_map(hash_data, FIELD_MAKERS_5M)?,
            buyers_5m: parse_u64_from_map(hash_data, FIELD_BUYERS_5M)?,
            sellers_5m: parse_u64_from_map(hash_data, FIELD_SELLERS_5M)?,
            // 1h statistics
            price_change_1h: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_1H)?,
            txns_1h: parse_u64_from_map(hash_data, FIELD_TXNS_1H)?,
            buy_txns_1h: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_1H)?,
            sell_txns_1h: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_1H)?,
            usd_volume_1h: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_1H)?,
            usd_buy_volume_1h: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_1H)?,
            usd_sell_volume_1h: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_1H)?,
            makers_1h: parse_u64_from_map(hash_data, FIELD_MAKERS_1H)?,
            buyers_1h: parse_u64_from_map(hash_data, FIELD_BUYERS_1H)?,
            sellers_1h: parse_u64_from_map(hash_data, FIELD_SELLERS_1H)?,
            // 6h statistics
            price_change_6h: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_6H)?,
            txns_6h: parse_u64_from_map(hash_data, FIELD_TXNS_6H)?,
            buy_txns_6h: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_6H)?,
            sell_txns_6h: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_6H)?,
            usd_volume_6h: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_6H)?,
            usd_buy_volume_6h: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_6H)?,
            usd_sell_volume_6h: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_6H)?,
            makers_6h: parse_u64_from_map(hash_data, FIELD_MAKERS_6H)?,
            buyers_6h: parse_u64_from_map(hash_data, FIELD_BUYERS_6H)?,
            sellers_6h: parse_u64_from_map(hash_data, FIELD_SELLERS_6H)?,
            // 24h statistics
            price_change_24h: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_24H)?,
            txns_24h: parse_u64_from_map(hash_data, FIELD_TXNS_24H)?,
            buy_txns_24h: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_24H)?,
            sell_txns_24h: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_24H)?,
            usd_volume_24h: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_24H)?,
            usd_buy_volume_24h: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_24H)?,
            usd_sell_volume_24h: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_24H)?,
            makers_24h: parse_u64_from_map(hash_data, FIELD_MAKERS_24H)?,
            buyers_24h: parse_u64_from_map(hash_data, FIELD_BUYERS_24H)?,
            sellers_24h: parse_u64_from_map(hash_data, FIELD_SELLERS_24H)?,
            // Update info
            update_timestamp_millis: parse_i64_from_map(hash_data, FIELD_UPDATE_TIMESTAMP)?,
        })
    }

    // Store pool statistics as Redis Hash
    pub async fn set_pool(&self, pool_statistic: &PoolStatistic) -> Result<()> {
        let key =
            Self::construct_pool_statistic_key(pool_statistic.chain, &pool_statistic.pool_address);
        let mut conn = self.get_connection();

        let hash_data = Self::pool_statistic_to_hash(pool_statistic);

        let _: () = conn.hset_multiple(key, &hash_data).await?;

        Ok(())
    }

    pub async fn set_pool_statistic_is_active(
        &self,
        chain: Chain,
        pool_address: &str,
        is_active: bool,
    ) -> Result<()> {
        let key = Self::construct_pool_statistic_key(chain, pool_address);
        let mut conn = self.get_connection();

        let _: () = conn.hset(&key, FIELD_IS_ACTIVE, is_active).await?;

        Ok(())
    }

    // Get pool statistics by key
    pub async fn get_pool(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolStatistic>> {
        let key = Self::construct_pool_statistic_key(chain, pool_address);
        let mut conn = self.get_connection();

        let hash_data: HashMap<String, String> = conn.hgetall(&key).await?;

        if hash_data.is_empty() {
            return Ok(None);
        }

        Ok(Some(Self::hash_to_pool_statistic(&hash_data)?))
    }

    pub async fn get_pools_by_address(&self, pool_address: &str) -> Result<Vec<PoolStatistic>> {
        let mut conn = self.get_connection();
        let mut pipe = redis::pipe();

        // Directly query known chains instead of scanning - O(1) vs O(N)
        for chain in [Chain::Solana, Chain::Hypercore, Chain::HyperEvm] {
            let key = Self::construct_pool_statistic_key(chain, pool_address);
            pipe.hgetall(key);
        }

        // Execute all commands in one round trip
        let results: Vec<HashMap<String, String>> = pipe.query_async(&mut conn).await?;

        // Parse results
        let mut pool_statistics = Vec::new();
        for hash_data in results {
            if !hash_data.is_empty() {
                pool_statistics.push(Self::hash_to_pool_statistic(&hash_data)?);
            }
        }

        Ok(pool_statistics)
    }

    // Get pool statistics by address
    pub async fn get_pools(
        &self,
        chain: Chain,
        pool_addresses: &[&str],
    ) -> Result<Vec<PoolStatistic>> {
        if pool_addresses.is_empty() {
            return Ok(Vec::new());
        }

        let mut conn = self.get_connection();

        // 1. Use pipeline to batch all HGETALL commands
        let mut pipe = redis::pipe();
        for pool_address in pool_addresses {
            let key = Self::construct_pool_statistic_key(chain, pool_address);
            pipe.hgetall(key);
        }

        // 2. Execute all commands in one round trip
        let results: Vec<HashMap<String, String>> = pipe.query_async(&mut conn).await?;

        // 3. Parse results
        let mut pool_statistics = Vec::with_capacity(results.len());
        for hash_data in results {
            if !hash_data.is_empty() {
                pool_statistics.push(Self::hash_to_pool_statistic(&hash_data)?);
            }
        }

        Ok(pool_statistics)
    }

    pub async fn contains_pool(&self, chain: Chain, pool_address: &str) -> Result<bool> {
        let key = Self::construct_pool_statistic_key(chain, pool_address);
        let mut conn = self.get_connection();
        let exists: bool = conn.exists(&key).await?;
        Ok(exists)
    }

    #[cfg(test)]
    pub async fn drop_pool_statistic_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&POOL_STATISTIC_INDEX.to_string()) {
            tracing::info!("Index {} already exists", POOL_STATISTIC_INDEX);
            let _: () =
                redis::cmd("FT.DROPINDEX").arg(POOL_STATISTIC_INDEX).query_async(&mut conn).await?;
            tracing::info!("Index {} dropped", POOL_STATISTIC_INDEX);
        }
        Ok(())
    }

    pub async fn create_pool_statistic_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&POOL_STATISTIC_INDEX.to_string()) {
            tracing::info!("Index {} already exists", POOL_STATISTIC_INDEX);
            return Ok(());
        }

        let start_time = std::time::Instant::now();
        tracing::info!("Creating index {}", POOL_STATISTIC_INDEX);
        let _: () = redis::cmd("FT.CREATE")
            .arg(POOL_STATISTIC_INDEX)
            .arg("ON")
            .arg("HASH")
            .arg("PREFIX")
            .arg("1")
            .arg(POOL_STATISTIC_KEY_PREFIX)
            .arg("SCHEMA")
            // Minimal fields for trending pools only
            .arg(FIELD_CHAIN) // chain - for filtering
            .arg("TAG")
            .arg(FIELD_IS_ACTIVE) // is_active - for filtering
            .arg("TAG")
            .arg(FIELD_USD_VOLUME_24H) // usd_volume_24h - for sorting (main use case)
            .arg("NUMERIC")
            .arg("SORTABLE")
            .query_async(&mut conn)
            .await?;
        tracing::info!("Index {} created in {:?}", POOL_STATISTIC_INDEX, start_time.elapsed());
        Ok(())
    }

    pub async fn get_trending_pools(
        &self,
        chain: Option<Chain>,
        limit: u64,
    ) -> Result<Vec<PoolStatistic>> {
        // Three-layer strategy: Cache -> Index -> Legacy fallback

        // 1. Try cache first
        match self.get_trending_pools_cached(chain, limit).await {
            Ok(Some(pools)) => {
                tracing::debug!("Returning {} trending pools from cache", pools.len());
                return Ok(pools);
            }
            Ok(None) => {
                tracing::debug!("Cache miss for trending pools, proceeding to indexed search");
            }
            Err(e) => {
                tracing::warn!(
                    "Cache error for trending pools, proceeding to indexed search: {}",
                    e
                );
            }
        }

        // 2. Try indexed search
        let pools = match self.get_trending_pools_indexed(chain, limit).await {
            Ok(pools) => {
                tracing::debug!("Retrieved {} trending pools from indexed search", pools.len());
                pools
            }
            Err(e) => {
                tracing::warn!("Failed to use indexed search for trending pools, falling back to scan method: {}", e);
                // 3. Fallback to legacy scan method
                let pools = self.get_trending_pools_legacy(chain, limit).await?;
                tracing::debug!("Retrieved {} trending pools from legacy scan", pools.len());
                pools
            }
        };

        // Update cache with fresh data (fire and forget)
        if !pools.is_empty() {
            if let Err(e) = self.set_trending_pools_cache(chain, &pools).await {
                tracing::warn!("Failed to update trending pools cache: {}", e);
            }
        }

        Ok(pools)
    }

    /// Legacy implementation using scan (kept as fallback)
    async fn get_trending_pools_legacy(
        &self,
        chain: Option<Chain>,
        limit: u64,
    ) -> Result<Vec<PoolStatistic>> {
        let mut conn = self.get_connection();

        // Build cache key based on chain filter
        let cache_key = match chain {
            Some(c) => format!("{}:{}", TRENDING_POOLS_CACHE_KEY, c.to_string()),
            None => TRENDING_POOLS_CACHE_KEY.to_string(),
        };

        // Try to get from cache first
        if let Ok(cached_data) = conn.get::<&str, String>(&cache_key).await {
            if let Ok(cached_pools) = serde_json::from_str::<Vec<PoolStatistic>>(&cached_data) {
                let mut result = cached_pools;
                result.truncate(limit as usize);
                return Ok(result);
            }
        }

        // Cache miss, compute trending pools
        let pool_statistics = self.compute_trending_pools(chain).await?;

        // Cache the result for future requests
        if !pool_statistics.is_empty() {
            if let Ok(serialized) = serde_json::to_string(&pool_statistics) {
                let _: std::result::Result<(), redis::RedisError> =
                    conn.set_ex(&cache_key, serialized, TRENDING_POOLS_CACHE_TTL).await;
            }
        }

        // Limit results
        let mut result = pool_statistics;
        result.truncate(limit as usize);
        Ok(result)
    }

    async fn compute_trending_pools(&self, chain: Option<Chain>) -> Result<Vec<PoolStatistic>> {
        let mut conn = self.get_connection();

        // Build pattern based on chain filter
        let pattern = match chain {
            Some(c) => format!("{}*:{}", POOL_STATISTIC_KEY_PREFIX, c.to_string()),
            None => format!("{}*", POOL_STATISTIC_KEY_PREFIX),
        };

        // 1. Get all keys matching the pattern
        let keys = conn.scan_match::<&str, String>(&pattern).await?;
        let keys: Vec<_> = keys.collect().await;

        if keys.is_empty() {
            return Ok(Vec::new());
        }

        // 2. Use pipeline to batch all HGETALL commands
        let mut pipe = redis::pipe();
        for key in &keys {
            pipe.hgetall(key);
        }

        // 3. Execute all commands in one round trip
        let results: Vec<HashMap<String, String>> = pipe.query_async(&mut conn).await?;

        // 4. Parse results and sort by volume
        let mut pool_statistics = Vec::new();
        for hash_data in results {
            if !hash_data.is_empty() {
                if let Ok(pool) = Self::hash_to_pool_statistic(&hash_data) {
                    pool_statistics.push(pool);
                }
            }
        }

        // Sort by 24h USD volume in descending order
        pool_statistics.sort_by(|a, b| {
            b.usd_volume_24h.partial_cmp(&a.usd_volume_24h).unwrap_or(std::cmp::Ordering::Equal)
        });

        Ok(pool_statistics)
    }

    /// Get trending pools using RediSearch index (optimized version)
    pub async fn get_trending_pools_indexed(
        &self,
        chain: Option<Chain>,
        limit: u64,
    ) -> Result<Vec<PoolStatistic>> {
        let mut conn = self.get_connection();

        // Build query for trending pools (active pools sorted by volume)
        let query = if let Some(chain) = chain {
            format!("@{}:{{{}}} @{}:{{1}}", FIELD_CHAIN, chain.to_string(), FIELD_IS_ACTIVE)
        } else {
            format!("@{}:{{1}}", FIELD_IS_ACTIVE)
        };

        // Execute search with volume-based sorting
        let result: redis::Value = redis::cmd("FT.SEARCH")
            .arg(POOL_STATISTIC_INDEX)
            .arg(&query)
            .arg("LIMIT")
            .arg(0)
            .arg(limit)
            .arg("SORTBY")
            .arg(FIELD_USD_VOLUME_24H)
            .arg("DESC")
            .query_async(&mut conn)
            .await?;

        let parsed = parse_redis_search_result(result);

        let mut pool_statistics = Vec::with_capacity(parsed.len());
        for hash_data in parsed {
            if let Ok(pool) = Self::hash_to_pool_statistic(&hash_data) {
                pool_statistics.push(pool);
            }
        }

        Ok(pool_statistics)
    }

    /// Construct cache key for trending pools
    fn construct_trending_pools_cache_key(chain: Option<Chain>) -> String {
        match chain {
            Some(c) => format!("{}{}", TRENDING_POOLS_CACHE_PREFIX, c.to_string()),
            None => format!("{}all", TRENDING_POOLS_CACHE_PREFIX),
        }
    }

    /// Get trending pools from cache
    async fn get_trending_pools_cached(
        &self,
        chain: Option<Chain>,
        limit: u64,
    ) -> Result<Option<Vec<PoolStatistic>>> {
        let cache_key = Self::construct_trending_pools_cache_key(chain);
        let mut conn = self.get_connection();

        // Try to get from cache
        let cached_data: Option<String> = conn.get(&cache_key).await?;

        if let Some(data) = cached_data {
            match serde_json::from_str::<Vec<PoolStatistic>>(&data) {
                Ok(mut pools) => {
                    // Apply limit if cached data has more items
                    if pools.len() > limit as usize {
                        pools.truncate(limit as usize);
                    }

                    Ok(Some(pools))
                }
                Err(e) => {
                    tracing::warn!("Failed to deserialize cached trending pools: {}", e);
                    // Remove corrupted cache entry
                    let _: () = conn.del(&cache_key).await?;
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }

    /// Set trending pools cache
    async fn set_trending_pools_cache(
        &self,
        chain: Option<Chain>,
        pools: &[PoolStatistic],
    ) -> Result<()> {
        let cache_key = Self::construct_trending_pools_cache_key(chain);
        let mut conn = self.get_connection();

        // Only cache top 10 to keep cache size manageable
        let pools_to_cache: Vec<_> =
            pools.iter().take(DEFAULT_TRENDING_LIMIT as usize).cloned().collect();

        let serialized = serde_json::to_string(&pools_to_cache).map_err(|e| {
            tracing::error!("Failed to serialize trending pools for cache: {}", e);
            crate::error::Error::AnyhowError(anyhow::anyhow!("Serialization error: {}", e))
        })?;

        let _: () = conn.set_ex(&cache_key, &serialized, TRENDING_POOLS_CACHE_TTL_SECONDS).await?;
        Ok(())
    }

    /// Refresh trending pools cache by fetching fresh data
    pub async fn refresh_trending_pools_cache(
        &self,
        chain: Option<Chain>,
    ) -> Result<Vec<PoolStatistic>> {
        tracing::info!("Refreshing trending pools cache for chain: {:?}", chain);

        // Get fresh data using indexed search (with fallback)
        let pools = match self.get_trending_pools_indexed(chain, DEFAULT_TRENDING_LIMIT).await {
            Ok(pools) => pools,
            Err(e) => {
                tracing::warn!("Failed to use indexed search for cache refresh, falling back to scan method: {}", e);
                self.get_trending_pools_legacy(chain, DEFAULT_TRENDING_LIMIT).await?
            }
        };

        // Update cache
        if let Err(e) = self.set_trending_pools_cache(chain, &pools).await {
            tracing::warn!("Failed to update trending pools cache: {}", e);
        }

        Ok(pools)
    }

    /// Check if trending pools cache exists and is valid
    pub async fn is_trending_pools_cache_valid(&self, chain: Option<Chain>) -> Result<bool> {
        let cache_key = Self::construct_trending_pools_cache_key(chain);
        let mut conn = self.get_connection();

        let ttl: i64 = conn.ttl(&cache_key).await.map_err(|e| {
            tracing::warn!("Failed to check cache TTL: {}", e);
            e
        })?;

        // TTL > 0 means key exists and has not expired
        // TTL = -1 means key exists but has no expiration
        // TTL = -2 means key does not exist
        Ok(ttl > 0 || ttl == -1)
    }

    /// Clear trending pools cache for specific chain or all chains
    pub async fn clear_trending_pools_cache(&self, chain: Option<Chain>) -> Result<bool> {
        let cache_key = Self::construct_trending_pools_cache_key(chain);
        let mut conn = self.get_connection();

        let deleted: u64 = conn.del(&cache_key).await?;

        Ok(deleted > 0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::postgres::{Dex, PoolType};

    #[tokio::test]
    async fn test_redis_pool() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        let pool_address = "0x1234567890123456789012345678901234567890";
        let pool_statistic = PoolStatistic {
            chain: Chain::Solana,
            pool_address: pool_address.to_string(),

            pair_label: "Test/SOL".to_string(),
            dex: Dex::Pumpswap,
            pool_type: PoolType::None,

            create_timestamp_millis: 1718745600000,

            token_address: "0x1234567890123456789012345678901234567890".to_string(),
            token_decimals: 6,
            base_address: "0x1234567890123456789012345678901234567890".to_string(),
            base_decimals: 9,
            is_token_first: true,

            is_active: true,
            usd_price: 1.0,
            usd_market_cap: 10000000.0,
            usd_liquidity: 10000000.0,

            bonding_curve_progress: Some(0.5),

            price: 1.0,
            market_cap: 10000000.0,
            liquidity: 10000000.0,
            total_volume: 10000000.0,
            total_buy_volume: 10000000.0,
            total_sell_volume: 10000000.0,
            total_txns: 10000000,
            total_buy_txns: 10000000,
            total_sell_txns: 10000000,

            price_change_5m: 0.5,
            txns_5m: 5,
            buy_txns_5m: 5,
            sell_txns_5m: 5,
            usd_volume_5m: 5.0,
            usd_buy_volume_5m: 5.0,
            usd_sell_volume_5m: 5.0,
            makers_5m: 5,
            buyers_5m: 5,
            sellers_5m: 5,

            price_change_1h: 0.1,
            txns_1h: 60,
            buy_txns_1h: 60,
            sell_txns_1h: 60,
            usd_volume_1h: 60.0,
            usd_buy_volume_1h: 60.0,
            usd_sell_volume_1h: 60.0,
            makers_1h: 60,
            buyers_1h: 60,
            sellers_1h: 60,

            price_change_6h: 0.6,
            txns_6h: 360,
            buy_txns_6h: 360,
            sell_txns_6h: 360,
            usd_volume_6h: 360.0,
            usd_buy_volume_6h: 360.0,
            usd_sell_volume_6h: 360.0,
            makers_6h: 360,
            buyers_6h: 360,
            sellers_6h: 360,

            price_change_24h: 0.24,
            txns_24h: 2400,
            buy_txns_24h: 2400,
            sell_txns_24h: 2400,
            usd_volume_24h: 2400.0,
            usd_buy_volume_24h: 2400.0,
            usd_sell_volume_24h: 2400.0,
            makers_24h: 2400,
            buyers_24h: 2400,
            sellers_24h: 2400,

            update_timestamp_millis: 1718745600000,
        };

        redis_client.set_pool(&pool_statistic).await.unwrap();

        let mut pool_statistic2 = pool_statistic.clone();
        pool_statistic2.chain = Chain::Hypercore;
        redis_client.set_pool(&pool_statistic2).await.unwrap();

        let pool_statistic_from_redis =
            redis_client.get_pool(Chain::Solana, pool_address).await.unwrap().unwrap();

        tracing::info!("get_pool: {:?}", pool_statistic_from_redis);

        assert_eq!(pool_statistic, pool_statistic_from_redis);

        let pools_by_address = redis_client.get_pools_by_address(pool_address).await.unwrap();
        tracing::info!("get_pools_by_address: {:?}", pools_by_address);

        let pools = redis_client.get_pools(Chain::Solana, &[pool_address]).await.unwrap();
        tracing::info!("get_pools: {:?}", pools);
    }

    #[tokio::test]
    async fn test_pool_index_and_search() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        // Drop and recreate index for clean test
        let _ = redis_client.drop_pool_statistic_index().await;
        redis_client.create_pool_statistic_index().await.unwrap();

        // Create test pool data
        let pool_statistic = PoolStatistic {
            chain: Chain::Solana,
            pool_address: "test_pool_123".to_string(),
            pair_label: "TEST/SOL".to_string(),
            dex: Dex::Pumpswap,
            pool_type: PoolType::None,
            create_timestamp_millis: chrono::Utc::now().timestamp_millis(),
            token_address: "test_token_123".to_string(),
            token_decimals: 6,
            base_address: "So11111111111111111111111111111111111111112".to_string(),
            base_decimals: 9,
            is_token_first: true,
            is_active: true,
            usd_price: 1.5,
            usd_market_cap: 1500000.0,
            usd_liquidity: 50000.0,
            bonding_curve_progress: Some(0.75),
            price: 1.5,
            market_cap: 1500000.0,
            liquidity: 50000.0,
            total_volume: 100000.0,
            total_buy_volume: 60000.0,
            total_sell_volume: 40000.0,
            total_txns: 1000,
            total_buy_txns: 600,
            total_sell_txns: 400,
            price_change_5m: 2.5,
            txns_5m: 10,
            buy_txns_5m: 6,
            sell_txns_5m: 4,
            usd_volume_5m: 1000.0,
            usd_buy_volume_5m: 600.0,
            usd_sell_volume_5m: 400.0,
            makers_5m: 8,
            buyers_5m: 5,
            sellers_5m: 3,
            price_change_1h: 5.0,
            txns_1h: 50,
            buy_txns_1h: 30,
            sell_txns_1h: 20,
            usd_volume_1h: 5000.0,
            usd_buy_volume_1h: 3000.0,
            usd_sell_volume_1h: 2000.0,
            makers_1h: 40,
            buyers_1h: 25,
            sellers_1h: 15,
            price_change_6h: 8.0,
            txns_6h: 200,
            buy_txns_6h: 120,
            sell_txns_6h: 80,
            usd_volume_6h: 20000.0,
            usd_buy_volume_6h: 12000.0,
            usd_sell_volume_6h: 8000.0,
            makers_6h: 150,
            buyers_6h: 90,
            sellers_6h: 60,
            price_change_24h: 15.0,
            txns_24h: 500,
            buy_txns_24h: 300,
            sell_txns_24h: 200,
            usd_volume_24h: 75000.0,
            usd_buy_volume_24h: 45000.0,
            usd_sell_volume_24h: 30000.0,
            makers_24h: 400,
            buyers_24h: 240,
            sellers_24h: 160,
            update_timestamp_millis: chrono::Utc::now().timestamp_millis(),
        };

        // Set pool data
        redis_client.set_pool(&pool_statistic).await.unwrap();

        // Test indexed trending pools
        let trending_pools =
            redis_client.get_trending_pools_indexed(Some(Chain::Solana), 10).await.unwrap();
        tracing::info!("Indexed trending pools: {:?}", trending_pools.len());
        assert!(!trending_pools.is_empty());

        // Test fallback mechanism
        let trending_pools_fallback =
            redis_client.get_trending_pools(Some(Chain::Solana), 10).await.unwrap();
        tracing::info!("Trending pools (with fallback): {:?}", trending_pools_fallback.len());
        assert!(!trending_pools_fallback.is_empty());
    }

    #[tokio::test]
    #[ignore]
    async fn test_get_pool_for_hypercore() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        let pool_address = "@1";
        let pool_statistic =
            redis_client.get_pool(Chain::Hypercore, pool_address).await.unwrap().unwrap();
        tracing::info!("get_pool: {:?}", pool_statistic);
    }

    #[tokio::test]
    async fn test_trending_pools_cache() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        // Clear any existing cache
        let _ = redis_client.clear_trending_pools_cache(Some(Chain::Solana)).await;

        // Create minimal test pool data
        let pool_statistic = PoolStatistic {
            chain: Chain::Solana,
            pool_address: "test_cache_pool_123".to_string(),
            pair_label: "TEST/SOL".to_string(),
            dex: Dex::Pumpswap,
            pool_type: PoolType::None,
            create_timestamp_millis: chrono::Utc::now().timestamp_millis(),
            token_address: "test_token_123".to_string(),
            token_decimals: 6,
            base_address: "So11111111111111111111111111111111111111112".to_string(),
            base_decimals: 9,
            is_token_first: true,
            is_active: true,
            bonding_curve_progress: Some(0.5),
            price: 0.001,
            usd_price: 0.001,
            market_cap: 100000.0,
            usd_market_cap: 100000.0,
            liquidity: 50000.0,
            usd_liquidity: 50000.0,
            total_volume: 100000.0,
            total_buy_volume: 60000.0,
            total_sell_volume: 40000.0,
            total_txns: 500,
            total_buy_txns: 300,
            total_sell_txns: 200,
            price_change_5m: 0.05,
            txns_5m: 10,
            buy_txns_5m: 6,
            sell_txns_5m: 4,
            usd_volume_5m: 1000.0,
            usd_buy_volume_5m: 600.0,
            usd_sell_volume_5m: 400.0,
            makers_5m: 5,
            buyers_5m: 3,
            sellers_5m: 2,
            price_change_1h: 0.1,
            txns_1h: 50,
            buy_txns_1h: 30,
            sell_txns_1h: 20,
            usd_volume_1h: 5000.0,
            usd_buy_volume_1h: 3000.0,
            usd_sell_volume_1h: 2000.0,
            makers_1h: 25,
            buyers_1h: 15,
            sellers_1h: 10,
            price_change_6h: 0.2,
            txns_6h: 200,
            buy_txns_6h: 120,
            sell_txns_6h: 80,
            usd_volume_6h: 15000.0,
            usd_buy_volume_6h: 9000.0,
            usd_sell_volume_6h: 6000.0,
            makers_6h: 100,
            buyers_6h: 60,
            sellers_6h: 40,
            price_change_24h: 0.3,
            usd_volume_24h: 25000.0,
            usd_buy_volume_24h: 15000.0,
            usd_sell_volume_24h: 10000.0,
            txns_24h: 100,
            buy_txns_24h: 60,
            sell_txns_24h: 40,
            makers_24h: 50,
            buyers_24h: 30,
            sellers_24h: 20,
            update_timestamp_millis: chrono::Utc::now().timestamp_millis(),
        };

        // Set pool data
        redis_client.set_pool(&pool_statistic).await.unwrap();

        // Test cache miss and hit
        let is_valid_before =
            redis_client.is_trending_pools_cache_valid(Some(Chain::Solana)).await.unwrap();
        assert!(!is_valid_before, "Cache should not exist initially");

        let trending_pools_1 =
            redis_client.get_trending_pools(Some(Chain::Solana), 10).await.unwrap();
        let trending_pools_2 =
            redis_client.get_trending_pools(Some(Chain::Solana), 10).await.unwrap();

        // Results should be the same
        assert_eq!(trending_pools_1.len(), trending_pools_2.len());

        // Test cache refresh
        let refreshed_pools =
            redis_client.refresh_trending_pools_cache(Some(Chain::Solana)).await.unwrap();
        assert!(!refreshed_pools.is_empty());

        // Test cache clearing
        let cleared = redis_client.clear_trending_pools_cache(Some(Chain::Solana)).await.unwrap();
        assert!(cleared, "Cache should have been cleared");
    }
}
