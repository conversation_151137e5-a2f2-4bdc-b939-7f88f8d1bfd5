use std::collections::HashMap;

use futures::StreamExt;
use redis::AsyncCommands;

use super::{utils::*, value::RedisValue, *};
use crate::{
    data_provider::filter::{Time, TokensFilter, Trench, Trend},
    error::Result,
    postgres::{
        aggregator::TokenStatistic,
        enums::{Chain, Dex, DexPaid},
    },
};

const TOKEN_STATISTIC_KEY_PREFIX: &str = "token:";
const TOKEN_STATISTIC_INDEX: &str = "token_idx";

// Human-readable constants for short field names
const FIELD_CHAIN: &str = "c";
const FIELD_TOKEN_ADDRESS: &str = "ta";
const FIELD_NAME: &str = "n";
const FIELD_SYMBOL: &str = "s";
const FIELD_DECIMALS: &str = "dc";
const FIELD_SUPPLY: &str = "sp";
const FIELD_DESCRIPTION: &str = "ds";
const FIELD_IMAGE: &str = "im";
const FIELD_WEBSITE: &str = "wb";
const FIELD_TWITTER: &str = "tw";
const FIELD_TELEGRAM: &str = "tg";
const FIELD_HAS_SOCIAL: &str = "hs";
const FIELD_HAS_TWITTER: &str = "hx";
const FIELD_HAS_TELEGRAM: &str = "ht";
const FIELD_HAS_WEBSITE: &str = "hw";
const FIELD_DEX_PAID: &str = "dp";
const FIELD_CREATE_DEX: &str = "cd";
const FIELD_CREATE_TIMESTAMP: &str = "ct";
const FIELD_CREATE_BLOCK: &str = "cb";
const FIELD_CREATE_TX_HASH: &str = "ch";
const FIELD_CREATE_BONDING_CURVE: &str = "cc";
const FIELD_MIGRATION_POOL_ADDRESS: &str = "cm";
const FIELD_CREATE_DEV: &str = "cv";
const FIELD_IS_TRENCH_TOKEN: &str = "it";
const FIELD_MIGRATION_TIMESTAMP: &str = "mt";
const FIELD_IS_MUTABLE: &str = "mu";
const FIELD_UPDATE_AUTHORITY: &str = "ua";
const FIELD_MINT_AUTHORITY: &str = "ma";
const FIELD_FREEZE_AUTHORITY: &str = "fa";
const FIELD_BEST_POOL_ADDRESS: &str = "bp";
const FIELD_POOL_ADDRESSES: &str = "pa";
const FIELD_USD_PRICE: &str = "up";
const FIELD_USD_MARKET_CAP: &str = "um";
const FIELD_USD_LIQUIDITY: &str = "ul";
const FIELD_BONDING_CURVE_PROGRESS: &str = "bc";
const FIELD_USD_TOTAL_VOLUME: &str = "uv";
const FIELD_USD_TOTAL_BUY_VOLUME: &str = "ub";
const FIELD_USD_TOTAL_SELL_VOLUME: &str = "us";
const FIELD_TOTAL_TXNS: &str = "tx";
const FIELD_TOTAL_BUY_TXNS: &str = "tb";
const FIELD_TOTAL_SELL_TXNS: &str = "ts";
const FIELD_TOTAL_TREND: &str = "tt";
const FIELD_TOTAL_PRICE_CHANGE: &str = "tp";
const FIELD_TREND_5M: &str = "d5";
const FIELD_PRICE_CHANGE_5M: &str = "p5";
const FIELD_TXNS_5M: &str = "x5";
const FIELD_BUY_TXNS_5M: &str = "b5";
const FIELD_SELL_TXNS_5M: &str = "s5";
const FIELD_USD_VOLUME_5M: &str = "v5";
const FIELD_USD_BUY_VOLUME_5M: &str = "y5";
const FIELD_USD_SELL_VOLUME_5M: &str = "l5";
const FIELD_TREND_1H: &str = "d1";
const FIELD_PRICE_CHANGE_1H: &str = "p1";
const FIELD_TXNS_1H: &str = "x1";
const FIELD_BUY_TXNS_1H: &str = "b1";
const FIELD_SELL_TXNS_1H: &str = "s1";
const FIELD_USD_VOLUME_1H: &str = "v1";
const FIELD_USD_BUY_VOLUME_1H: &str = "y1";
const FIELD_USD_SELL_VOLUME_1H: &str = "l1";
const FIELD_TREND_6H: &str = "d6";
const FIELD_PRICE_CHANGE_6H: &str = "p6";
const FIELD_TXNS_6H: &str = "x6";
const FIELD_BUY_TXNS_6H: &str = "b6";
const FIELD_SELL_TXNS_6H: &str = "s6";
const FIELD_USD_VOLUME_6H: &str = "v6";
const FIELD_USD_BUY_VOLUME_6H: &str = "y6";
const FIELD_USD_SELL_VOLUME_6H: &str = "l6";
const FIELD_TREND_24H: &str = "d2";
const FIELD_PRICE_CHANGE_24H: &str = "p2";
const FIELD_TXNS_24H: &str = "x2";
const FIELD_BUY_TXNS_24H: &str = "b2";
const FIELD_SELL_TXNS_24H: &str = "s2";
const FIELD_USD_VOLUME_24H: &str = "v2";
const FIELD_USD_BUY_VOLUME_24H: &str = "y2";
const FIELD_USD_SELL_VOLUME_24H: &str = "l2";
const FIELD_TREND_3D: &str = "d3";
const FIELD_PRICE_CHANGE_3D: &str = "p3";
const FIELD_TXNS_3D: &str = "x3";
const FIELD_BUY_TXNS_3D: &str = "b3";
const FIELD_SELL_TXNS_3D: &str = "s3";
const FIELD_USD_VOLUME_3D: &str = "v3";
const FIELD_USD_BUY_VOLUME_3D: &str = "y3";
const FIELD_USD_SELL_VOLUME_3D: &str = "l3";
const FIELD_TREND_7D: &str = "d7";
const FIELD_PRICE_CHANGE_7D: &str = "p7";
const FIELD_TXNS_7D: &str = "x7";
const FIELD_BUY_TXNS_7D: &str = "b7";
const FIELD_SELL_TXNS_7D: &str = "s7";
const FIELD_USD_VOLUME_7D: &str = "v7";
const FIELD_USD_BUY_VOLUME_7D: &str = "y7";
const FIELD_USD_SELL_VOLUME_7D: &str = "l7";
const FIELD_DEV_HOLD_PERCENTAGE: &str = "dhp";
const FIELD_DEV_SOLD_PERCENTAGE: &str = "dsp";
const FIELD_TOP10_HOLD_PERCENTAGE: &str = "thp";
const FIELD_SNIPER_HOLD_PERCENTAGE: &str = "shp";
const FIELD_INSIDER_HOLD_PERCENTAGE: &str = "ihp";
const FIELD_BOT_HOLD_PERCENTAGE: &str = "bhp";
const FIELD_HOLDER_COUNT: &str = "hct";
const FIELD_SNIPER_COUNT: &str = "sct";
const FIELD_INSIDER_COUNT: &str = "ict";
const FIELD_BOT_COUNT: &str = "bct";
const FIELD_UPDATE_TIMESTAMP: &str = "ut";

const FIELD_IS_ACTIVE: &str = "ia";
const FIELD_BEST_POOL_DEX: &str = "bpd";
const FIELD_POOL_DEXES: &str = "pd";
const FIELD_INIT_USD_PRICE: &str = "iup";
const FIELD_IMAGE_PATH: &str = "ip";

impl RedisClient {
    fn construct_token_statistic_key(chain: Chain, token_address: &str) -> String {
        format!("{}{}:{}", TOKEN_STATISTIC_KEY_PREFIX, token_address, chain.to_string())
    }

    fn construct_token_statistic_key_pattern(token_address: &str) -> String {
        format!("{}{}:*", TOKEN_STATISTIC_KEY_PREFIX, token_address)
    }

    // Convert TokenStatistics to HashMap with short field names and proper types
    fn token_statistic_to_hash(
        token_statistic: &TokenStatistic,
    ) -> Vec<(&'static str, RedisValue)> {
        let mut hash_data = vec![
            // Basic metadata - using short keys to save space
            (FIELD_CHAIN, RedisValue::String(token_statistic.chain.to_string())),
            (FIELD_TOKEN_ADDRESS, RedisValue::String(token_statistic.token_address.clone())),
            (FIELD_NAME, RedisValue::String(token_statistic.name.clone())),
            (FIELD_SYMBOL, RedisValue::String(token_statistic.symbol.clone())),
            (FIELD_DECIMALS, RedisValue::UInt8(token_statistic.decimals)),
            (FIELD_SUPPLY, RedisValue::String(token_statistic.supply.clone())),
            (
                FIELD_HAS_SOCIAL,
                RedisValue::Bool(
                    token_statistic.twitter.is_some() ||
                        token_statistic.telegram.is_some() ||
                        token_statistic.website.is_some(),
                ),
            ),
            (FIELD_HAS_TWITTER, RedisValue::Bool(token_statistic.twitter.is_some())),
            (FIELD_HAS_TELEGRAM, RedisValue::Bool(token_statistic.telegram.is_some())),
            (FIELD_HAS_WEBSITE, RedisValue::Bool(token_statistic.website.is_some())),
            (FIELD_DEX_PAID, RedisValue::String(token_statistic.dex_paid.to_string())),
            (FIELD_CREATE_DEX, RedisValue::String(token_statistic.create_dex.to_string())),
            (FIELD_CREATE_TIMESTAMP, RedisValue::Int64(token_statistic.create_timestamp_millis)),
            (FIELD_IS_TRENCH_TOKEN, RedisValue::Bool(token_statistic.is_trench_token)),
            (
                FIELD_MIGRATION_TIMESTAMP,
                RedisValue::Int64(token_statistic.migration_timestamp_millis),
            ),
            // Pool info
            (
                FIELD_BEST_POOL_ADDRESS,
                RedisValue::String(token_statistic.best_pool_address.clone()),
            ),
            (FIELD_POOL_ADDRESSES, RedisValue::String(token_statistic.pool_addresses.clone())),
            // Token state info
            (FIELD_USD_PRICE, RedisValue::Float64(token_statistic.usd_price)),
            (FIELD_USD_MARKET_CAP, RedisValue::Float64(token_statistic.usd_market_cap)),
            (FIELD_USD_LIQUIDITY, RedisValue::Float64(token_statistic.usd_liquidity)),
            // Total statistics
            (FIELD_USD_TOTAL_VOLUME, RedisValue::Float64(token_statistic.usd_total_volume)),
            (FIELD_USD_TOTAL_BUY_VOLUME, RedisValue::Float64(token_statistic.usd_total_buy_volume)),
            (
                FIELD_USD_TOTAL_SELL_VOLUME,
                RedisValue::Float64(token_statistic.usd_total_sell_volume),
            ),
            (FIELD_TOTAL_TXNS, RedisValue::UInt64(token_statistic.total_txns)),
            (FIELD_TOTAL_BUY_TXNS, RedisValue::UInt64(token_statistic.total_buy_txns)),
            (FIELD_TOTAL_SELL_TXNS, RedisValue::UInt64(token_statistic.total_sell_txns)),
            (FIELD_TOTAL_TREND, RedisValue::Float64(token_statistic.total_trend)),
            (FIELD_TOTAL_PRICE_CHANGE, RedisValue::Float64(token_statistic.total_price_change)),
            // 5m statistics
            (FIELD_TREND_5M, RedisValue::Float64(token_statistic.trend_5m)),
            (FIELD_PRICE_CHANGE_5M, RedisValue::Float64(token_statistic.price_change_5m)),
            (FIELD_TXNS_5M, RedisValue::UInt64(token_statistic.txns_5m)),
            (FIELD_BUY_TXNS_5M, RedisValue::UInt64(token_statistic.buy_txns_5m)),
            (FIELD_SELL_TXNS_5M, RedisValue::UInt64(token_statistic.sell_txns_5m)),
            (FIELD_USD_VOLUME_5M, RedisValue::Float64(token_statistic.usd_volume_5m)),
            (FIELD_USD_BUY_VOLUME_5M, RedisValue::Float64(token_statistic.usd_buy_volume_5m)),
            (FIELD_USD_SELL_VOLUME_5M, RedisValue::Float64(token_statistic.usd_sell_volume_5m)),
            // 1h statistics
            (FIELD_TREND_1H, RedisValue::Float64(token_statistic.trend_1h)),
            (FIELD_PRICE_CHANGE_1H, RedisValue::Float64(token_statistic.price_change_1h)),
            (FIELD_TXNS_1H, RedisValue::UInt64(token_statistic.txns_1h)),
            (FIELD_BUY_TXNS_1H, RedisValue::UInt64(token_statistic.buy_txns_1h)),
            (FIELD_SELL_TXNS_1H, RedisValue::UInt64(token_statistic.sell_txns_1h)),
            (FIELD_USD_VOLUME_1H, RedisValue::Float64(token_statistic.usd_volume_1h)),
            (FIELD_USD_BUY_VOLUME_1H, RedisValue::Float64(token_statistic.usd_buy_volume_1h)),
            (FIELD_USD_SELL_VOLUME_1H, RedisValue::Float64(token_statistic.usd_sell_volume_1h)),
            // 6h statistics
            (FIELD_TREND_6H, RedisValue::Float64(token_statistic.trend_6h)),
            (FIELD_PRICE_CHANGE_6H, RedisValue::Float64(token_statistic.price_change_6h)),
            (FIELD_TXNS_6H, RedisValue::UInt64(token_statistic.txns_6h)),
            (FIELD_BUY_TXNS_6H, RedisValue::UInt64(token_statistic.buy_txns_6h)),
            (FIELD_SELL_TXNS_6H, RedisValue::UInt64(token_statistic.sell_txns_6h)),
            (FIELD_USD_VOLUME_6H, RedisValue::Float64(token_statistic.usd_volume_6h)),
            (FIELD_USD_BUY_VOLUME_6H, RedisValue::Float64(token_statistic.usd_buy_volume_6h)),
            (FIELD_USD_SELL_VOLUME_6H, RedisValue::Float64(token_statistic.usd_sell_volume_6h)),
            // 24h statistics
            (FIELD_TREND_24H, RedisValue::Float64(token_statistic.trend_24h)),
            (FIELD_PRICE_CHANGE_24H, RedisValue::Float64(token_statistic.price_change_24h)),
            (FIELD_TXNS_24H, RedisValue::UInt64(token_statistic.txns_24h)),
            (FIELD_BUY_TXNS_24H, RedisValue::UInt64(token_statistic.buy_txns_24h)),
            (FIELD_SELL_TXNS_24H, RedisValue::UInt64(token_statistic.sell_txns_24h)),
            (FIELD_USD_VOLUME_24H, RedisValue::Float64(token_statistic.usd_volume_24h)),
            (FIELD_USD_BUY_VOLUME_24H, RedisValue::Float64(token_statistic.usd_buy_volume_24h)),
            (FIELD_USD_SELL_VOLUME_24H, RedisValue::Float64(token_statistic.usd_sell_volume_24h)),
            // 3d statistics
            (FIELD_TREND_3D, RedisValue::Float64(token_statistic.trend_3d)),
            (FIELD_PRICE_CHANGE_3D, RedisValue::Float64(token_statistic.price_change_3d)),
            (FIELD_TXNS_3D, RedisValue::UInt64(token_statistic.txns_3d)),
            (FIELD_BUY_TXNS_3D, RedisValue::UInt64(token_statistic.buy_txns_3d)),
            (FIELD_SELL_TXNS_3D, RedisValue::UInt64(token_statistic.sell_txns_3d)),
            (FIELD_USD_VOLUME_3D, RedisValue::Float64(token_statistic.usd_volume_3d)),
            (FIELD_USD_BUY_VOLUME_3D, RedisValue::Float64(token_statistic.usd_buy_volume_3d)),
            (FIELD_USD_SELL_VOLUME_3D, RedisValue::Float64(token_statistic.usd_sell_volume_3d)),
            // 7d statistics
            (FIELD_TREND_7D, RedisValue::Float64(token_statistic.trend_7d)),
            (FIELD_PRICE_CHANGE_7D, RedisValue::Float64(token_statistic.price_change_7d)),
            (FIELD_TXNS_7D, RedisValue::UInt64(token_statistic.txns_7d)),
            (FIELD_BUY_TXNS_7D, RedisValue::UInt64(token_statistic.buy_txns_7d)),
            (FIELD_SELL_TXNS_7D, RedisValue::UInt64(token_statistic.sell_txns_7d)),
            (FIELD_USD_VOLUME_7D, RedisValue::Float64(token_statistic.usd_volume_7d)),
            (FIELD_USD_BUY_VOLUME_7D, RedisValue::Float64(token_statistic.usd_buy_volume_7d)),
            (FIELD_USD_SELL_VOLUME_7D, RedisValue::Float64(token_statistic.usd_sell_volume_7d)),
            // Update info
            (FIELD_UPDATE_TIMESTAMP, RedisValue::Int64(token_statistic.update_timestamp_millis)),
            // New fields
            (FIELD_IS_ACTIVE, RedisValue::Bool(token_statistic.is_active)),
            (FIELD_BEST_POOL_DEX, RedisValue::String(token_statistic.best_pool_dex.to_string())),
            (FIELD_POOL_DEXES, RedisValue::String(token_statistic.pool_dexes.clone())),
        ];

        if let Some(description) = token_statistic.description.as_ref() {
            hash_data.push((FIELD_DESCRIPTION, RedisValue::String(description.clone())));
        }

        if let Some(image) = token_statistic.image.as_ref() {
            hash_data.push((FIELD_IMAGE, RedisValue::String(image.clone())));
        }

        if let Some(website) = token_statistic.website.as_ref() {
            hash_data.push((FIELD_WEBSITE, RedisValue::String(website.clone())));
        }

        if let Some(twitter) = token_statistic.twitter.as_ref() {
            hash_data.push((FIELD_TWITTER, RedisValue::String(twitter.clone())));
        }

        if let Some(telegram) = token_statistic.telegram.as_ref() {
            hash_data.push((FIELD_TELEGRAM, RedisValue::String(telegram.clone())));
        }

        if let Some(create_block_number) = token_statistic.create_block_number.as_ref() {
            hash_data.push((FIELD_CREATE_BLOCK, RedisValue::UInt64(*create_block_number)));
        }

        if let Some(create_tx_hash) = token_statistic.create_tx_hash.as_ref() {
            hash_data.push((FIELD_CREATE_TX_HASH, RedisValue::String(create_tx_hash.clone())));
        }

        if let Some(create_bonding_curve) = token_statistic.create_bonding_curve.as_ref() {
            hash_data.push((
                FIELD_CREATE_BONDING_CURVE,
                RedisValue::String(create_bonding_curve.clone()),
            ));
        }

        if let Some(create_dev) = token_statistic.create_dev.as_ref() {
            hash_data.push((FIELD_CREATE_DEV, RedisValue::String(create_dev.clone())));
        }

        if let Some(migration_pool_address) = token_statistic.migration_pool_address.as_ref() {
            hash_data.push((
                FIELD_MIGRATION_POOL_ADDRESS,
                RedisValue::String(migration_pool_address.clone()),
            ));
        }

        if let Some(is_mutable) = token_statistic.is_mutable {
            hash_data.push((FIELD_IS_MUTABLE, RedisValue::Bool(is_mutable)));
        }

        if let Some(update_authority) = token_statistic.update_authority.as_ref() {
            hash_data.push((FIELD_UPDATE_AUTHORITY, RedisValue::String(update_authority.clone())));
        }

        if let Some(mint_authority) = token_statistic.mint_authority.as_ref() {
            hash_data.push((FIELD_MINT_AUTHORITY, RedisValue::String(mint_authority.clone())));
        }

        if let Some(freeze_authority) = token_statistic.freeze_authority.as_ref() {
            hash_data.push((FIELD_FREEZE_AUTHORITY, RedisValue::String(freeze_authority.clone())));
        }

        if let Some(bonding_curve_progress) = token_statistic.bonding_curve_progress.as_ref() {
            hash_data
                .push((FIELD_BONDING_CURVE_PROGRESS, RedisValue::Float64(*bonding_curve_progress)));
        }

        if let Some(dev_hold_percentage) = token_statistic.dev_hold_percentage {
            hash_data.push((FIELD_DEV_HOLD_PERCENTAGE, RedisValue::Float64(dev_hold_percentage)));
        }

        if let Some(dev_sold_percentage) = token_statistic.dev_sold_percentage {
            hash_data.push((FIELD_DEV_SOLD_PERCENTAGE, RedisValue::Float64(dev_sold_percentage)));
        }

        if let Some(top10_hold_percentage) = token_statistic.top10_hold_percentage {
            hash_data
                .push((FIELD_TOP10_HOLD_PERCENTAGE, RedisValue::Float64(top10_hold_percentage)));
        }

        if let Some(sniper_hold_percentage) = token_statistic.sniper_hold_percentage {
            hash_data
                .push((FIELD_SNIPER_HOLD_PERCENTAGE, RedisValue::Float64(sniper_hold_percentage)));
        }

        if let Some(insider_hold_percentage) = token_statistic.insider_hold_percentage {
            hash_data.push((
                FIELD_INSIDER_HOLD_PERCENTAGE,
                RedisValue::Float64(insider_hold_percentage),
            ));
        }

        if let Some(bot_hold_percentage) = token_statistic.bot_hold_percentage {
            hash_data.push((FIELD_BOT_HOLD_PERCENTAGE, RedisValue::Float64(bot_hold_percentage)));
        }

        if let Some(holder_count) = token_statistic.holder_count {
            hash_data.push((FIELD_HOLDER_COUNT, RedisValue::UInt64(holder_count)));
        }

        if let Some(sniper_count) = token_statistic.sniper_count {
            hash_data.push((FIELD_SNIPER_COUNT, RedisValue::UInt64(sniper_count)));
        }

        if let Some(insider_count) = token_statistic.insider_count {
            hash_data.push((FIELD_INSIDER_COUNT, RedisValue::UInt64(insider_count)));
        }

        if let Some(bot_count) = token_statistic.bot_count {
            hash_data.push((FIELD_BOT_COUNT, RedisValue::UInt64(bot_count)));
        }

        if let Some(init_usd_price) = token_statistic.init_usd_price {
            hash_data.push((FIELD_INIT_USD_PRICE, RedisValue::Float64(init_usd_price)));
        }

        if let Some(image_path) = token_statistic.image_path.as_ref() {
            hash_data.push((FIELD_IMAGE_PATH, RedisValue::String(image_path.clone())));
        }

        hash_data
    }

    // Convert HashMap back to TokenStatistic
    fn hash_to_token_statistic(hash_data: &HashMap<String, String>) -> Result<TokenStatistic> {
        Ok(TokenStatistic {
            // Basic metadata
            chain: parse_chain_from_map(hash_data, FIELD_CHAIN)?,
            token_address: parse_string_from_map(hash_data, FIELD_TOKEN_ADDRESS)?,
            name: parse_string_from_map(hash_data, FIELD_NAME)?,
            symbol: parse_string_from_map(hash_data, FIELD_SYMBOL)?,
            decimals: parse_u8_from_map(hash_data, FIELD_DECIMALS)?,
            supply: parse_string_from_map(hash_data, FIELD_SUPPLY)?,
            description: parse_string_from_map(hash_data, FIELD_DESCRIPTION).ok(),
            image: parse_string_from_map(hash_data, FIELD_IMAGE).ok(),
            website: parse_string_from_map(hash_data, FIELD_WEBSITE).ok(),
            twitter: parse_string_from_map(hash_data, FIELD_TWITTER).ok(),
            telegram: parse_string_from_map(hash_data, FIELD_TELEGRAM).ok(),
            dex_paid: parse_dex_paid_from_map(hash_data, FIELD_DEX_PAID)?,

            is_trench_token: parse_bool_from_map(hash_data, FIELD_IS_TRENCH_TOKEN)?,

            // Creation info
            create_dex: parse_dex_from_map(hash_data, FIELD_CREATE_DEX)?,
            create_block_number: parse_u64_from_map(hash_data, FIELD_CREATE_BLOCK).ok(),
            create_tx_hash: parse_string_from_map(hash_data, FIELD_CREATE_TX_HASH).ok(),
            create_bonding_curve: parse_string_from_map(hash_data, FIELD_CREATE_BONDING_CURVE).ok(),
            create_dev: parse_string_from_map(hash_data, FIELD_CREATE_DEV).ok(),
            create_timestamp_millis: parse_i64_from_map(hash_data, FIELD_CREATE_TIMESTAMP)?,
            migration_pool_address: parse_string_from_map(hash_data, FIELD_MIGRATION_POOL_ADDRESS)
                .ok(),
            migration_timestamp_millis: parse_i64_from_map(hash_data, FIELD_MIGRATION_TIMESTAMP)?,

            // Solana specific info
            is_mutable: parse_bool_from_map(hash_data, FIELD_IS_MUTABLE).ok(),
            update_authority: parse_string_from_map(hash_data, FIELD_UPDATE_AUTHORITY).ok(),
            mint_authority: parse_string_from_map(hash_data, FIELD_MINT_AUTHORITY).ok(),
            freeze_authority: parse_string_from_map(hash_data, FIELD_FREEZE_AUTHORITY).ok(),

            // Pool info
            best_pool_address: parse_string_from_map(hash_data, FIELD_BEST_POOL_ADDRESS)?,
            pool_addresses: parse_string_from_map(hash_data, FIELD_POOL_ADDRESSES)?,

            // Token state info
            usd_price: parse_f64_from_map(hash_data, FIELD_USD_PRICE)?,
            usd_market_cap: parse_f64_from_map(hash_data, FIELD_USD_MARKET_CAP)?,
            usd_liquidity: parse_f64_from_map(hash_data, FIELD_USD_LIQUIDITY)?,
            bonding_curve_progress: parse_f64_from_map(hash_data, FIELD_BONDING_CURVE_PROGRESS)
                .ok(),

            // Total statistics
            usd_total_volume: parse_f64_from_map(hash_data, FIELD_USD_TOTAL_VOLUME)?,
            usd_total_buy_volume: parse_f64_from_map(hash_data, FIELD_USD_TOTAL_BUY_VOLUME)?,
            usd_total_sell_volume: parse_f64_from_map(hash_data, FIELD_USD_TOTAL_SELL_VOLUME)?,
            total_txns: parse_u64_from_map(hash_data, FIELD_TOTAL_TXNS)?,
            total_buy_txns: parse_u64_from_map(hash_data, FIELD_TOTAL_BUY_TXNS)?,
            total_sell_txns: parse_u64_from_map(hash_data, FIELD_TOTAL_SELL_TXNS)?,
            total_trend: parse_f64_from_map(hash_data, FIELD_TOTAL_TREND)?,
            total_price_change: parse_f64_from_map(hash_data, FIELD_TOTAL_PRICE_CHANGE)?,

            // 5m statistics
            trend_5m: parse_f64_from_map(hash_data, FIELD_TREND_5M)?,
            price_change_5m: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_5M)?,
            txns_5m: parse_u64_from_map(hash_data, FIELD_TXNS_5M)?,
            buy_txns_5m: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_5M)?,
            sell_txns_5m: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_5M)?,
            usd_volume_5m: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_5M)?,
            usd_buy_volume_5m: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_5M)?,
            usd_sell_volume_5m: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_5M)?,

            // 1h statistics
            trend_1h: parse_f64_from_map(hash_data, FIELD_TREND_1H)?,
            price_change_1h: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_1H)?,
            txns_1h: parse_u64_from_map(hash_data, FIELD_TXNS_1H)?,
            buy_txns_1h: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_1H)?,
            sell_txns_1h: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_1H)?,
            usd_volume_1h: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_1H)?,
            usd_buy_volume_1h: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_1H)?,
            usd_sell_volume_1h: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_1H)?,

            // 6h statistics
            trend_6h: parse_f64_from_map(hash_data, FIELD_TREND_6H)?,
            price_change_6h: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_6H)?,
            txns_6h: parse_u64_from_map(hash_data, FIELD_TXNS_6H)?,
            buy_txns_6h: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_6H)?,
            sell_txns_6h: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_6H)?,
            usd_volume_6h: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_6H)?,
            usd_buy_volume_6h: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_6H)?,
            usd_sell_volume_6h: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_6H)?,

            // 24h statistics
            trend_24h: parse_f64_from_map(hash_data, FIELD_TREND_24H)?,
            price_change_24h: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_24H)?,
            txns_24h: parse_u64_from_map(hash_data, FIELD_TXNS_24H)?,
            buy_txns_24h: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_24H)?,
            sell_txns_24h: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_24H)?,
            usd_volume_24h: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_24H)?,
            usd_buy_volume_24h: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_24H)?,
            usd_sell_volume_24h: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_24H)?,

            // 3d statistics
            trend_3d: parse_f64_from_map(hash_data, FIELD_TREND_3D)?,
            price_change_3d: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_3D)?,
            txns_3d: parse_u64_from_map(hash_data, FIELD_TXNS_3D)?,
            buy_txns_3d: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_3D)?,
            sell_txns_3d: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_3D)?,
            usd_volume_3d: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_3D)?,
            usd_buy_volume_3d: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_3D)?,
            usd_sell_volume_3d: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_3D)?,

            // 7d statistics
            trend_7d: parse_f64_from_map(hash_data, FIELD_TREND_7D)?,
            price_change_7d: parse_f64_from_map(hash_data, FIELD_PRICE_CHANGE_7D)?,
            txns_7d: parse_u64_from_map(hash_data, FIELD_TXNS_7D)?,
            buy_txns_7d: parse_u64_from_map(hash_data, FIELD_BUY_TXNS_7D)?,
            sell_txns_7d: parse_u64_from_map(hash_data, FIELD_SELL_TXNS_7D)?,
            usd_volume_7d: parse_f64_from_map(hash_data, FIELD_USD_VOLUME_7D)?,
            usd_buy_volume_7d: parse_f64_from_map(hash_data, FIELD_USD_BUY_VOLUME_7D)?,
            usd_sell_volume_7d: parse_f64_from_map(hash_data, FIELD_USD_SELL_VOLUME_7D)?,

            // Meme statistics
            dev_hold_percentage: parse_f64_from_map(hash_data, FIELD_DEV_HOLD_PERCENTAGE).ok(),
            dev_sold_percentage: parse_f64_from_map(hash_data, FIELD_DEV_SOLD_PERCENTAGE).ok(),
            top10_hold_percentage: parse_f64_from_map(hash_data, FIELD_TOP10_HOLD_PERCENTAGE).ok(),
            sniper_hold_percentage: parse_f64_from_map(hash_data, FIELD_SNIPER_HOLD_PERCENTAGE)
                .ok(),
            insider_hold_percentage: parse_f64_from_map(hash_data, FIELD_INSIDER_HOLD_PERCENTAGE)
                .ok(),
            bot_hold_percentage: parse_f64_from_map(hash_data, FIELD_BOT_HOLD_PERCENTAGE).ok(),
            holder_count: parse_u64_from_map(hash_data, FIELD_HOLDER_COUNT).ok(),
            sniper_count: parse_u64_from_map(hash_data, FIELD_SNIPER_COUNT).ok(),
            insider_count: parse_u64_from_map(hash_data, FIELD_INSIDER_COUNT).ok(),
            bot_count: parse_u64_from_map(hash_data, FIELD_BOT_COUNT).ok(),

            // Update info
            update_timestamp_millis: parse_i64_from_map(hash_data, FIELD_UPDATE_TIMESTAMP)?,

            // New fields
            is_active: parse_bool_from_map(hash_data, FIELD_IS_ACTIVE).unwrap_or(true),
            best_pool_dex: parse_dex_from_map(hash_data, FIELD_BEST_POOL_DEX)
                .unwrap_or(Dex::Unknown),
            pool_dexes: parse_string_from_map(hash_data, FIELD_POOL_DEXES)
                .unwrap_or_else(|_| String::new()),
            init_usd_price: parse_f64_from_map(hash_data, FIELD_INIT_USD_PRICE).ok(),
            image_path: parse_string_from_map(hash_data, FIELD_IMAGE_PATH).ok(),
        })
    }

    #[cfg(test)]
    pub async fn drop_token_statistic_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&TOKEN_STATISTIC_INDEX.to_string()) {
            tracing::info!("Index {} already exists", TOKEN_STATISTIC_INDEX);
            let _: () = redis::cmd("FT.DROPINDEX")
                .arg(TOKEN_STATISTIC_INDEX)
                .query_async(&mut conn)
                .await?;
            tracing::info!("Index {} dropped", TOKEN_STATISTIC_INDEX);
        }
        Ok(())
    }

    pub async fn create_token_statistic_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&TOKEN_STATISTIC_INDEX.to_string()) {
            tracing::info!("Index {} already exists", TOKEN_STATISTIC_INDEX);
            return Ok(());
        }

        let start_time = std::time::Instant::now();
        tracing::info!("Creating index {}", TOKEN_STATISTIC_INDEX);
        let _: () = redis::cmd("FT.CREATE")
            .arg(TOKEN_STATISTIC_INDEX)
            .arg("ON")
            .arg("HASH")
            .arg("PREFIX")
            .arg("1")
            .arg(TOKEN_STATISTIC_KEY_PREFIX)
            .arg("SCHEMA")
            // Basic metadata
            .arg(FIELD_CHAIN) // chain
            .arg("TAG")
            .arg(FIELD_NAME) // name
            .arg("TEXT")
            .arg("WEIGHT")
            .arg("2.0")
            .arg(FIELD_SYMBOL) // symbol
            .arg("TEXT")
            .arg("WEIGHT")
            .arg("3.0")
            .arg(FIELD_DESCRIPTION) // description
            .arg("TEXT")
            .arg("WEIGHT")
            .arg("0.5")
            .arg(FIELD_HAS_WEBSITE) // website
            .arg("TAG")
            .arg(FIELD_HAS_TWITTER) // twitter
            .arg("TAG")
            .arg(FIELD_HAS_TELEGRAM) // telegram
            .arg("TAG")
            .arg(FIELD_HAS_SOCIAL) // social
            .arg("TAG")
            .arg(FIELD_DEX_PAID) // dex_paid
            .arg("TAG")
            // Creation info
            .arg(FIELD_CREATE_DEX) // create_dex
            .arg("TAG")
            .arg(FIELD_CREATE_TIMESTAMP) // create_timestamp_millis
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_CREATE_DEV) // create_dev
            .arg("TAG")
            .arg(FIELD_IS_TRENCH_TOKEN) // is_trench_token
            .arg("TAG")
            .arg(FIELD_MIGRATION_TIMESTAMP) // migration_timestamp_millis
            .arg("NUMERIC")
            .arg("SORTABLE")
            // Solana specific info
            .arg(FIELD_IS_MUTABLE) // is_mutable
            .arg("TAG")
            .arg(FIELD_UPDATE_AUTHORITY) // update_authority
            .arg("TAG")
            .arg(FIELD_MINT_AUTHORITY) // mint_authority
            .arg("TAG")
            .arg(FIELD_FREEZE_AUTHORITY) // freeze_authority
            .arg("TAG")
            // Token state info
            .arg(FIELD_USD_PRICE) // usd_price
            .arg("NUMERIC")
            .arg(FIELD_USD_MARKET_CAP) // usd_market_cap
            .arg("NUMERIC")
            .arg(FIELD_USD_LIQUIDITY) // usd_liquidity
            .arg("NUMERIC")
            .arg(FIELD_BONDING_CURVE_PROGRESS) // bonding_curve_progress
            .arg("NUMERIC")
            .arg("SORTABLE")
            // Total statistics
            .arg(FIELD_USD_TOTAL_VOLUME) // usd_total_volume
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_USD_TOTAL_BUY_VOLUME) // usd_total_buy_volume
            .arg("NUMERIC")
            .arg(FIELD_USD_TOTAL_SELL_VOLUME) // usd_total_sell_volume
            .arg("NUMERIC")
            .arg(FIELD_TOTAL_TXNS) // total_txns
            .arg("NUMERIC")
            .arg(FIELD_TOTAL_BUY_TXNS) // total_buy_txns
            .arg("NUMERIC")
            .arg(FIELD_TOTAL_SELL_TXNS) // total_sell_txns
            .arg("NUMERIC")
            .arg(FIELD_TOTAL_TREND) // total_trend
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_TOTAL_PRICE_CHANGE) // total_price_change
            .arg("NUMERIC")
            .arg("SORTABLE")
            // 5m statistics
            .arg(FIELD_TREND_5M) // trend_5m
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_PRICE_CHANGE_5M) // price_change_5m
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_TXNS_5M) // txns_5m
            .arg("NUMERIC")
            .arg(FIELD_BUY_TXNS_5M) // buy_txns_5m
            .arg("NUMERIC")
            .arg(FIELD_SELL_TXNS_5M) // sell_txns_5m
            .arg("NUMERIC")
            .arg(FIELD_USD_VOLUME_5M) // usd_volume_5m
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_USD_BUY_VOLUME_5M) // usd_buy_volume_5m
            .arg("NUMERIC")
            .arg(FIELD_USD_SELL_VOLUME_5M) // usd_sell_volume_5m
            .arg("NUMERIC")
            // 1h statistics
            .arg(FIELD_TREND_1H) // trend_1h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_PRICE_CHANGE_1H) // price_change_1h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_TXNS_1H) // txns_1h
            .arg("NUMERIC")
            .arg(FIELD_BUY_TXNS_1H) // buy_txns_1h
            .arg("NUMERIC")
            .arg(FIELD_SELL_TXNS_1H) // sell_txns_1h
            .arg("NUMERIC")
            .arg(FIELD_USD_VOLUME_1H) // usd_volume_1h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_USD_BUY_VOLUME_1H) // usd_buy_volume_1h
            .arg("NUMERIC")
            .arg(FIELD_USD_SELL_VOLUME_1H) // usd_sell_volume_1h
            .arg("NUMERIC")
            // 6h statistics
            .arg(FIELD_TREND_6H) // trend_6h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_PRICE_CHANGE_6H) // price_change_6h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_TXNS_6H) // txns_6h
            .arg("NUMERIC")
            .arg(FIELD_BUY_TXNS_6H) // buy_txns_6h
            .arg("NUMERIC")
            .arg(FIELD_SELL_TXNS_6H) // sell_txns_6h
            .arg("NUMERIC")
            .arg(FIELD_USD_VOLUME_6H) // usd_volume_6h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_USD_BUY_VOLUME_6H) // usd_buy_volume_6h
            .arg("NUMERIC")
            .arg(FIELD_USD_SELL_VOLUME_6H) // usd_sell_volume_6h
            .arg("NUMERIC")
            // 24h statistics
            .arg(FIELD_TREND_24H) // trend_24h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_PRICE_CHANGE_24H) // price_change_24h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_TXNS_24H) // txns_24h
            .arg("NUMERIC")
            .arg(FIELD_BUY_TXNS_24H) // buy_txns_24h
            .arg("NUMERIC")
            .arg(FIELD_SELL_TXNS_24H) // sell_txns_24h
            .arg("NUMERIC")
            .arg(FIELD_USD_VOLUME_24H) // usd_volume_24h
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_USD_BUY_VOLUME_24H) // usd_buy_volume_24h
            .arg("NUMERIC")
            .arg(FIELD_USD_SELL_VOLUME_24H) // usd_sell_volume_24h
            .arg("NUMERIC")
            // 3d statistics
            .arg(FIELD_TREND_3D) // trend_3d
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_PRICE_CHANGE_3D) // price_change_3d
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_TXNS_3D) // txns_3d
            .arg("NUMERIC")
            .arg(FIELD_BUY_TXNS_3D) // buy_txns_3d
            .arg("NUMERIC")
            .arg(FIELD_SELL_TXNS_3D) // sell_txns_3d
            .arg("NUMERIC")
            .arg(FIELD_USD_VOLUME_3D) // usd_volume_3d
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_USD_BUY_VOLUME_3D) // usd_buy_volume_3d
            .arg("NUMERIC")
            .arg(FIELD_USD_SELL_VOLUME_3D) // usd_sell_volume_3d
            .arg("NUMERIC")
            // 7d statistics
            .arg(FIELD_TREND_7D) // trend_7d
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_PRICE_CHANGE_7D) // price_change_7d
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_TXNS_7D) // txns_7d
            .arg("NUMERIC")
            .arg(FIELD_BUY_TXNS_7D) // buy_txns_7d
            .arg("NUMERIC")
            .arg(FIELD_SELL_TXNS_7D) // sell_txns_7d
            .arg("NUMERIC")
            .arg(FIELD_USD_VOLUME_7D) // usd_volume_7d
            .arg("NUMERIC")
            .arg("SORTABLE")
            .arg(FIELD_USD_BUY_VOLUME_7D) // usd_buy_volume_7d
            .arg("NUMERIC")
            .arg(FIELD_USD_SELL_VOLUME_7D) // usd_sell_volume_7d
            .arg("NUMERIC")
            // Meme statistics
            .arg(FIELD_DEV_HOLD_PERCENTAGE) // dev_hold_percentage
            .arg("NUMERIC")
            .arg(FIELD_TOP10_HOLD_PERCENTAGE) // top10_hold_percentage
            .arg("NUMERIC")
            .arg(FIELD_HOLDER_COUNT) // holder_count
            .arg("NUMERIC")
            .arg(FIELD_SNIPER_COUNT) // sniper_count
            .arg("NUMERIC")
            .arg(FIELD_INSIDER_COUNT) // insider_count
            .arg("NUMERIC")
            .arg(FIELD_BOT_COUNT) // bot_count
            .arg("NUMERIC")
            // update info
            .arg(FIELD_UPDATE_TIMESTAMP) // update_timestamp_millis
            .arg("NUMERIC")
            .arg("SORTABLE")
            // New fields
            .arg(FIELD_IS_ACTIVE) // is_active
            .arg("TAG")
            .arg(FIELD_BEST_POOL_DEX) // best_pool_dex
            .arg("TAG")
            .arg(FIELD_POOL_DEXES) // pool_dexes
            .arg("TAG")
            .query_async(&mut conn)
            .await?;
        tracing::info!("Index {} created in {:?}", TOKEN_STATISTIC_INDEX, start_time.elapsed());
        Ok(())
    }

    pub async fn get_token_index_info(&self) -> Result<()> {
        let mut conn = self.get_connection();
        let info: redis::Value =
            redis::cmd("FT.INFO").arg(TOKEN_STATISTIC_INDEX).query_async(&mut conn).await?;
        tracing::info!("Index {} info: {:?}", TOKEN_STATISTIC_INDEX, info);
        Ok(())
    }

    // Store token statistics as Redis Hash
    pub async fn set_token(&self, token_statistic: &TokenStatistic) -> Result<()> {
        let key = Self::construct_token_statistic_key(
            token_statistic.chain,
            &token_statistic.token_address,
        );
        let mut conn = self.get_connection();

        let hash_data = Self::token_statistic_to_hash(token_statistic);

        let _: () = conn.hset_multiple(key, &hash_data).await?;

        Ok(())
    }

    pub async fn set_token_supply_and_market_cap(
        &self,
        chain: Chain,
        token_address: &str,
        supply: String,
        market_cap: f64,
    ) -> Result<()> {
        let key = Self::construct_token_statistic_key(chain, token_address);
        let mut conn = self.get_connection();

        // Use pipeline to batch both HSET commands
        let mut pipe = redis::pipe();
        pipe.hset(&key, FIELD_SUPPLY, supply);
        pipe.hset(&key, FIELD_USD_MARKET_CAP, market_cap);

        // Execute both commands in one round trip
        let _: () = pipe.query_async(&mut conn).await?;
        Ok(())
    }

    pub async fn set_token_statistic_active_status(
        &self,
        chain: Chain,
        token_address: &str,
        is_active: bool,
    ) -> Result<()> {
        let key = Self::construct_token_statistic_key(chain, token_address);
        let mut conn = self.get_connection();

        let _: () = conn.hset(&key, FIELD_IS_ACTIVE, is_active).await?;

        Ok(())
    }

    pub async fn set_token_statistic_misc_info(
        &self,
        chain: Chain,
        token_address: &str,
        description: Option<String>,
        image: Option<String>,
        website: Option<String>,
        twitter: Option<String>,
        telegram: Option<String>,
        image_path: Option<String>,
    ) -> Result<()> {
        if description.is_none() &&
            image.is_none() &&
            website.is_none() &&
            twitter.is_none() &&
            telegram.is_none() &&
            image_path.is_none()
        {
            return Ok(());
        }

        let key = Self::construct_token_statistic_key(chain, token_address);
        let mut conn = self.get_connection();

        // Use pipeline to batch both HSET commands
        let mut pipe = redis::pipe();
        if let Some(description) = description {
            pipe.hset(&key, FIELD_DESCRIPTION, description);
        }
        if let Some(image) = image {
            pipe.hset(&key, FIELD_IMAGE, image);
        }
        if website.is_some() || twitter.is_some() || telegram.is_some() {
            pipe.hset(&key, FIELD_HAS_SOCIAL, true);
        }
        if let Some(website) = website {
            pipe.hset(&key, FIELD_WEBSITE, website);
            pipe.hset(&key, FIELD_HAS_WEBSITE, true);
        }
        if let Some(twitter) = twitter {
            pipe.hset(&key, FIELD_TWITTER, twitter);
            pipe.hset(&key, FIELD_HAS_TWITTER, true);
        }
        if let Some(telegram) = telegram {
            pipe.hset(&key, FIELD_TELEGRAM, telegram);
            pipe.hset(&key, FIELD_HAS_TELEGRAM, true);
        }
        if let Some(image_path) = image_path {
            pipe.hset(&key, FIELD_IMAGE_PATH, image_path);
        }

        // Execute both commands in one round trip
        let _: () = pipe.query_async(&mut conn).await?;
        Ok(())
    }

    #[cfg(test)]
    pub async fn delete_token(&self, chain: Chain, token_address: &str) -> Result<()> {
        let key = Self::construct_token_statistic_key(chain, token_address);
        let mut conn = self.get_connection();
        let _: () = conn.del(&key).await?;
        Ok(())
    }

    pub async fn contains_token(&self, chain: Chain, token_address: &str) -> Result<bool> {
        let key = Self::construct_token_statistic_key(chain, token_address);
        let mut conn = self.get_connection();
        let exists: bool = conn.exists(&key).await?;
        Ok(exists)
    }

    // Get token statistics by key
    pub async fn get_token(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<Option<TokenStatistic>> {
        let key = Self::construct_token_statistic_key(chain, token_address);
        let mut conn = self.get_connection();

        let hash_data: HashMap<String, String> = conn.hgetall(&key).await?;

        if hash_data.is_empty() {
            return Ok(None);
        }

        Ok(Some(Self::hash_to_token_statistic(&hash_data)?))
    }

    // Get token statistics by key
    pub async fn get_tokens_by_address(&self, token_address: &str) -> Result<Vec<TokenStatistic>> {
        let pattern = Self::construct_token_statistic_key_pattern(token_address);
        let mut conn = self.get_connection();

        // 1. Get all keys
        let keys = conn.scan_match::<&str, String>(&pattern).await?;
        let keys: Vec<_> = keys.collect().await;

        if keys.is_empty() {
            return Ok(Vec::new());
        }

        // 2. Use pipeline to batch all HGETALL commands
        let mut pipe = redis::pipe();
        for key in &keys {
            pipe.hgetall(key);
        }

        // 3. Execute all commands in one round trip
        let results: Vec<HashMap<String, String>> = pipe.query_async(&mut conn).await?;

        // 4. Parse results
        let mut token_statistics = Vec::with_capacity(results.len());
        for hash_data in results {
            if !hash_data.is_empty() {
                token_statistics.push(Self::hash_to_token_statistic(&hash_data)?);
            }
        }

        Ok(token_statistics)
    }

    pub async fn get_token_price(&self, chain: Chain, token_address: &str) -> Result<f64> {
        let key = Self::construct_token_statistic_key(chain, token_address);
        let mut conn = self.get_connection();
        let price: f64 = conn.hget(key, FIELD_USD_PRICE).await?;
        Ok(price)
    }

    pub async fn get_token_price_and_decimals(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<(f64, u8)> {
        let key = Self::construct_token_statistic_key(chain, token_address);
        let mut conn = self.get_connection();
        let price: f64 = conn.hget(&key, FIELD_USD_PRICE).await?;
        let decimals: u8 = conn.hget(&key, FIELD_DECIMALS).await?;
        Ok((price, decimals))
    }

    pub async fn search_active_tokens_by_update_time_asc(
        &self,
        limit: u32,
        offset: u32,
    ) -> Result<Vec<(Chain, String, i64)>> {
        let mut conn = self.get_connection();
        let result: redis::Value = redis::cmd("FT.SEARCH")
            .arg(TOKEN_STATISTIC_INDEX)
            .arg(&format!("@{}:{{1}}", FIELD_IS_ACTIVE))
            .arg("SORTBY")
            .arg(FIELD_UPDATE_TIMESTAMP)
            .arg("ASC")
            .arg("LIMIT")
            .arg(offset)
            .arg(limit)
            .arg("RETURN")
            .arg(3)
            .arg(FIELD_CHAIN)
            .arg(FIELD_TOKEN_ADDRESS)
            .arg(FIELD_UPDATE_TIMESTAMP)
            .query_async(&mut conn)
            .await?;

        let parsed = parse_redis_search_result(result);

        let mut tuples = Vec::with_capacity(parsed.len());
        for hash_data in parsed {
            // Both fields are required, so unwrap is safe if data is correct
            let chain = parse_chain_from_map(&hash_data, FIELD_CHAIN)?;
            let token_address = parse_string_from_map(&hash_data, FIELD_TOKEN_ADDRESS)?;
            let update_timestamp = parse_i64_from_map(&hash_data, FIELD_UPDATE_TIMESTAMP)?;
            tuples.push((chain, token_address, update_timestamp));
        }

        Ok(tuples)
    }

    /// Search token statistics based on TokensFilter
    pub async fn search_tokens_by_filter(
        &self,
        filter: &TokensFilter,
    ) -> Result<Vec<TokenStatistic>> {
        let mut conn = self.get_connection();

        // Build RediSearch query
        let query = Self::build_token_filter_search_query(filter);
        tracing::debug!("Searching with query: {}", query);

        // Execute search with pagination - return all fields
        let (sort_field, sort_order) = Self::get_token_filter_sort_field(filter);
        let result: redis::Value = redis::cmd("FT.SEARCH")
            .arg(TOKEN_STATISTIC_INDEX)
            .arg(&query)
            .arg("LIMIT")
            .arg(filter.offset)
            .arg(filter.limit)
            .arg("SORTBY")
            .arg(sort_field)
            .arg(sort_order)
            .query_async(&mut conn)
            .await?;

        let parsed = parse_redis_search_result(result);

        let mut token_statistics = Vec::with_capacity(parsed.len());
        for hash_data in parsed {
            token_statistics.push(Self::hash_to_token_statistic(&hash_data).unwrap());
        }

        Ok(token_statistics)
    }

    pub async fn search_tokens_by_keyword(
        &self,
        chain: Chain,
        keyword: &str,
        limit: u32,
    ) -> Result<Vec<TokenStatistic>> {
        let mut conn = self.get_connection();

        // Build fuzzy search query for better matching
        let search_query = Self::build_fuzzy_search_query(chain, keyword);
        tracing::debug!("Fuzzy search query: {}", search_query);

        // Execute search with pagination - return all fields
        let result: redis::Value = redis::cmd("FT.SEARCH")
            .arg(TOKEN_STATISTIC_INDEX)
            .arg(&search_query)
            .arg("LIMIT")
            .arg(0)
            .arg(limit)
            .arg("SORTBY")
            .arg(FIELD_USD_VOLUME_24H)
            .arg("DESC")
            .query_async(&mut conn)
            .await?;

        let parsed = parse_redis_search_result(result);

        let mut token_statistics = Vec::with_capacity(parsed.len());
        for hash_data in parsed {
            token_statistics.push(Self::hash_to_token_statistic(&hash_data).unwrap());
        }

        Ok(token_statistics)
    }

    /// Build fuzzy search query for keyword-based token search
    /// Supports partial matching across name, symbol, and description fields
    fn build_fuzzy_search_query(chain: Chain, keyword: &str) -> String {
        if keyword.is_empty() {
            // Only return active tokens for the specified chain
            return format!("@{}:{{1}} @{}:{{{}}}", FIELD_IS_ACTIVE, FIELD_CHAIN, chain);
        }

        // Clean and escape the keyword
        let cleaned_keyword = keyword.trim().to_lowercase();

        // Escape special RediSearch characters
        let escaped_keyword = cleaned_keyword
            .replace("(", "\\(")
            .replace(")", "\\)")
            .replace("[", "\\[")
            .replace("]", "\\]")
            .replace("{", "\\{")
            .replace("}", "\\}")
            .replace(":", "\\:")
            .replace("|", "\\|")
            .replace("@", "\\@");

        // Build optimized fuzzy search query
        // Use RediSearch's natural field weights (symbol:3.0, name:2.0, description:0.5)
        let mut search_conditions = Vec::new();

        // Primary search: prefix and wildcard in name and symbol fields
        search_conditions.push(format!("@{}:({}*)", FIELD_NAME, escaped_keyword));
        search_conditions.push(format!("@{}:({}*)", FIELD_SYMBOL, escaped_keyword));
        search_conditions.push(format!("@{}:(*{}*)", FIELD_NAME, escaped_keyword));
        search_conditions.push(format!("@{}:(*{}*)", FIELD_SYMBOL, escaped_keyword));

        // Secondary search: description field for longer keywords only
        if escaped_keyword.len() >= 3 {
            search_conditions.push(format!("@{}:({}*)", FIELD_DESCRIPTION, escaped_keyword));
            search_conditions.push(format!("@{}:(*{}*)", FIELD_DESCRIPTION, escaped_keyword));
        }

        // Combine all conditions with proper syntax
        // Format: @is_active:{1} (@name:(keyword*) | @symbol:(keyword*) | ...) @chain:{chain}
        let search_query = search_conditions.join(" | ");
        format!("@{}:{{1}} ({}) @{}:{{{}}}", FIELD_IS_ACTIVE, search_query, FIELD_CHAIN, chain)
    }

    /// Build RediSearch query from filter
    fn build_token_filter_search_query(filter: &TokensFilter) -> String {
        let mut query_parts = Vec::new();

        // Is active filter
        if let Some(is_active) = &filter.is_active {
            if *is_active {
                query_parts.push(format!("@{}:{{1}}", FIELD_IS_ACTIVE));
            } else {
                query_parts.push(format!("@{}:{{0}}", FIELD_IS_ACTIVE));
            }
        }

        if let Some(trench) = &filter.trench {
            query_parts.push(format!("@{}:{{1}}", FIELD_IS_TRENCH_TOKEN));
            if let Trench::Migrated = trench {
                query_parts.push(format!("@{}:[(0 +inf]", FIELD_MIGRATION_TIMESTAMP));
            } else {
                query_parts.push(format!("@{}:[0 0]", FIELD_MIGRATION_TIMESTAMP));
            }

            let one_day_ago = (chrono::Utc::now() - chrono::Duration::days(1)).timestamp_millis();
            query_parts.push(format!("@{}:[{} +inf]", FIELD_CREATE_TIMESTAMP, one_day_ago));
        } else {
            query_parts.push(format!("@{}:{{0}}", FIELD_IS_TRENCH_TOKEN));
        }

        // Chain filter
        if let Some(chains) = &filter.chain {
            if !chains.is_empty() {
                let chain_filters =
                    chains.iter().map(|chain| chain.to_string()).collect::<Vec<String>>().join("|");
                query_parts.push(format!("@{}:{{{}}}", FIELD_CHAIN, chain_filters));
            }
        }

        // Best pool dex filter
        if let Some(best_pool_dex) = &filter.best_pool_dex {
            if !best_pool_dex.is_empty() {
                let best_pool_dex_filters = best_pool_dex
                    .iter()
                    .map(|dex| dex.to_string())
                    .collect::<Vec<String>>()
                    .join("|");
                query_parts.push(format!("@{}:{{{}}}", FIELD_BEST_POOL_DEX, best_pool_dex_filters));
            }
        }

        // Pool dexes filter
        if let Some(pool_dexes) = &filter.pool_dexes {
            if !pool_dexes.is_empty() {
                let pool_dex_filters =
                    pool_dexes.iter().map(|dex| dex.to_string()).collect::<Vec<String>>().join("|");
                query_parts.push(format!("@{}:{{{}}}", FIELD_POOL_DEXES, pool_dex_filters));
            }
        }

        // Market cap range
        match (filter.market_cap_min, filter.market_cap_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_USD_MARKET_CAP, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_USD_MARKET_CAP, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_USD_MARKET_CAP, max));
            }
            (None, None) => {}
        }

        // FDV range (using usd_market_cap as approximation)
        match (filter.fdv_min, filter.fdv_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_USD_MARKET_CAP, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_USD_MARKET_CAP, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_USD_MARKET_CAP, max));
            }
            (None, None) => {}
        }

        // Liquidity range
        match (filter.liquidity_min, filter.liquidity_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_USD_LIQUIDITY, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_USD_LIQUIDITY, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_USD_LIQUIDITY, max));
            }
            (None, None) => {}
        }

        // Volume range (based on time period)
        let volume_field = Self::get_token_filter_volume_field(&filter);
        match (filter.volume_min, filter.volume_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", volume_field, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", volume_field, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", volume_field, max));
            }
            (None, None) => {}
        }

        // Transaction count range (based on time period)
        let txns_field = Self::get_token_filter_txns_field(&filter);
        match (filter.txns_min, filter.txns_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", txns_field, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", txns_field, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", txns_field, max));
            }
            (None, None) => {}
        }

        // Buy transactions range (based on time period)
        let buys_field = Self::get_token_filter_buys_field(&filter);
        match (filter.buys_min, filter.buys_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", buys_field, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", buys_field, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", buys_field, max));
            }
            (None, None) => {}
        }

        // Sell transactions range (based on time period)
        let sells_field = Self::get_token_filter_sells_field(&filter);
        match (filter.sells_min, filter.sells_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", sells_field, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", sells_field, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", sells_field, max));
            }
            (None, None) => {}
        }

        // Price change range (based on time period)
        let change_field = Self::get_token_filter_change_field(&filter);
        match (filter.change_min, filter.change_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", change_field, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", change_field, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", change_field, max));
            }
            (None, None) => {}
        }

        // Dex filter
        if let Some(create_dex) = &filter.create_dex {
            if !create_dex.is_empty() {
                let create_dex_filters =
                    create_dex.iter().map(|dex| dex.to_string()).collect::<Vec<String>>().join("|");
                query_parts.push(format!("@{}:{{{}}}", FIELD_CREATE_DEX, create_dex_filters));
            }
        }

        // Has social filter
        if let Some(has_social) = filter.has_social {
            if has_social {
                query_parts.push(format!("@{}:{{1}}", FIELD_HAS_SOCIAL));
            }
        }

        // Has twitter filter
        if let Some(has_twitter) = filter.has_twitter {
            if has_twitter {
                query_parts.push(format!("@{}:{{1}}", FIELD_HAS_TWITTER));
            }
        }

        // Has telegram filter
        if let Some(has_telegram) = filter.has_telegram {
            if has_telegram {
                query_parts.push(format!("@{}:{{1}}", FIELD_HAS_TELEGRAM));
            }
        }

        // Has website filter
        if let Some(has_website) = filter.has_website {
            if has_website {
                query_parts.push(format!("@{}:{{1}}", FIELD_HAS_WEBSITE));
            }
        }

        // Dex paid filter
        if let Some(dex_paid) = filter.dex_paid {
            if dex_paid {
                query_parts.push(format!("@{}:{{{}}}", FIELD_DEX_PAID, DexPaid::Paid.to_string()));
            } else {
                query_parts.push(format!(
                    "@{}:{{{}}}",
                    FIELD_DEX_PAID,
                    DexPaid::Unpaid.to_string()
                ));
            }
        }

        match (filter.creation_time_min, filter.creation_time_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!(
                    "@{}:[{} {}]",
                    FIELD_CREATE_TIMESTAMP,
                    min * 1000,
                    max * 1000
                ));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_CREATE_TIMESTAMP, min * 1000));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_CREATE_TIMESTAMP, max * 1000));
            }
            (None, None) => {}
        }

        // Bonding curve range
        match (filter.bonding_curve_min, filter.bonding_curve_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_BONDING_CURVE_PROGRESS, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_BONDING_CURVE_PROGRESS, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_BONDING_CURVE_PROGRESS, max));
            }
            (None, None) => {}
        }

        // Dev holdings range
        match (filter.dev_holdings_min, filter.dev_holdings_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_DEV_HOLD_PERCENTAGE, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_DEV_HOLD_PERCENTAGE, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_DEV_HOLD_PERCENTAGE, max));
            }
            (None, None) => {}
        }

        // Top 10 holdings range
        match (filter.top10_holdings_min, filter.top10_holdings_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_TOP10_HOLD_PERCENTAGE, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_TOP10_HOLD_PERCENTAGE, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_TOP10_HOLD_PERCENTAGE, max));
            }
            (None, None) => {}
        }

        // Holders range
        match (filter.holders_min, filter.holders_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_HOLDER_COUNT, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_HOLDER_COUNT, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_HOLDER_COUNT, max));
            }
            (None, None) => {}
        }

        // Sniper range
        match (filter.sniper_min, filter.sniper_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_SNIPER_COUNT, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_SNIPER_COUNT, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_SNIPER_COUNT, max));
            }
            (None, None) => {}
        }

        // Insider range
        match (filter.insider_min, filter.insider_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_INSIDER_COUNT, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_INSIDER_COUNT, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_INSIDER_COUNT, max));
            }
            (None, None) => {}
        }

        // Bot range
        match (filter.bot_min, filter.bot_max) {
            (Some(min), Some(max)) => {
                query_parts.push(format!("@{}:[{} {}]", FIELD_BOT_COUNT, min, max));
            }
            (Some(min), None) => {
                query_parts.push(format!("@{}:[{} +inf]", FIELD_BOT_COUNT, min));
            }
            (None, Some(max)) => {
                query_parts.push(format!("@{}:[-inf {}]", FIELD_BOT_COUNT, max));
            }
            (None, None) => {}
        }

        // Join all parts with AND
        if query_parts.is_empty() {
            "*".to_string()
        } else {
            query_parts.join(" ")
        }
    }

    /// Get volume field based on time period
    fn get_token_filter_volume_field(filter: &TokensFilter) -> &'static str {
        if let Some(time) = &filter.time {
            match time {
                Time::All => "uv",     // usd_total_volume
                Time::Minute5 => "v5", // usd_volume_5m
                Time::Hour1 => "v1",   // usd_volume_1h
                Time::Hour6 => "v6",   // usd_volume_6h
                Time::Hour24 => "v2",  // usd_volume_24h
                Time::Day3 => "v3",    // usd_volume_3d
                Time::Day7 => "v7",    // usd_volume_7d
            }
        } else {
            "uv"
        }
    }

    /// Get transactions field based on time period
    fn get_token_filter_txns_field(filter: &TokensFilter) -> &'static str {
        if let Some(time) = &filter.time {
            match time {
                Time::All => "tx",     // total_txns
                Time::Minute5 => "x5", // txns_5m
                Time::Hour1 => "x1",   // txns_1h
                Time::Hour6 => "x6",   // txns_6h
                Time::Hour24 => "x2",  // txns_24h
                Time::Day3 => "x3",    // txns_3d
                Time::Day7 => "x7",    // txns_7d
            }
        } else {
            "tx"
        }
    }

    /// Get buy transactions field based on time period
    fn get_token_filter_buys_field(filter: &TokensFilter) -> &'static str {
        if let Some(time) = &filter.time {
            match time {
                Time::All => "tb",     // total_buy_txns
                Time::Minute5 => "b5", // buy_txns_5m
                Time::Hour1 => "b1",   // buy_txns_1h
                Time::Hour6 => "b6",   // buy_txns_6h
                Time::Hour24 => "b2",  // buy_txns_24h
                Time::Day3 => "b3",    // buy_txns_3d
                Time::Day7 => "b7",    // buy_txns_7d
            }
        } else {
            "tb"
        }
    }

    /// Get sell transactions field based on time period
    fn get_token_filter_sells_field(filter: &TokensFilter) -> &'static str {
        if let Some(time) = &filter.time {
            match time {
                Time::All => "ts",     // total_sell_txns
                Time::Minute5 => "s5", // sell_txns_5m
                Time::Hour1 => "s1",   // sell_txns_1h
                Time::Hour6 => "s6",   // sell_txns_6h
                Time::Hour24 => "s2",  // sell_txns_24h
                Time::Day3 => "s3",    // sell_txns_3d
                Time::Day7 => "s7",    // sell_txns_7d
            }
        } else {
            "ts"
        }
    }

    /// Get price change field based on time period
    fn get_token_filter_change_field(filter: &TokensFilter) -> &'static str {
        if let Some(time) = &filter.time {
            match time {
                Time::All => "tp",     // total_price_change
                Time::Minute5 => "p5", // price_change_5m
                Time::Hour1 => "p1",   // price_change_1h
                Time::Hour6 => "p6",   // price_change_6h
                Time::Hour24 => "p2",  // price_change_24h
                Time::Day3 => "p3",    // price_change_3d
                Time::Day7 => "p7",    // price_change_7d
            }
        } else {
            "tp"
        }
    }

    fn get_token_filter_trend_field(filter: &TokensFilter) -> &'static str {
        if let Some(time) = &filter.time {
            match time {
                Time::All => "tt",     // total_trend
                Time::Minute5 => "d5", // trend_5m
                Time::Hour1 => "d1",   // trend_1h
                Time::Hour6 => "d6",   // trend_6h
                Time::Hour24 => "d2",  // trend_24h
                Time::Day3 => "d3",    // trend_3d
                Time::Day7 => "d7",    // trend_7d
            }
        } else {
            "tt"
        }
    }

    /// Get sort field based on trend
    fn get_token_filter_sort_field(filter: &TokensFilter) -> (&'static str, &'static str) {
        const SORT_DESC: &str = "DESC";
        const SORT_ASC: &str = "ASC";
        if let Some(trend) = &filter.trend {
            match trend {
                Trend::Top => (FIELD_USD_MARKET_CAP, SORT_DESC),
                Trend::Trending => (Self::get_token_filter_trend_field(&filter), SORT_DESC),
                Trend::New => (FIELD_CREATE_TIMESTAMP, SORT_DESC), // create_timestamp_millis
                Trend::Popular => (Self::get_token_filter_volume_field(&filter), SORT_DESC),
                Trend::Gainers => (Self::get_token_filter_change_field(&filter), SORT_DESC),
                Trend::Losers => (Self::get_token_filter_change_field(&filter), SORT_ASC),
            }
        } else if let Some(trench) = &filter.trench {
            match trench {
                Trench::NewPairs => (FIELD_CREATE_TIMESTAMP, SORT_DESC), // create_timestamp_millis
                Trench::FinalStretch => (FIELD_BONDING_CURVE_PROGRESS, SORT_DESC), /* bonding_curve_progress */
                Trench::Migrated => (FIELD_MIGRATION_TIMESTAMP, SORT_DESC), /* migration_timestamp_millis */
            }
        } else {
            (FIELD_USD_MARKET_CAP, SORT_DESC)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::postgres::*;

    #[tokio::test]
    // #[ignore]
    async fn test_redis_token_filter() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        let token_address = "0x1234567890123456789012345678901234567890";
        let token_statistic = TokenStatistic {
            chain: Chain::Solana,
            token_address: token_address.to_string(),

            name: "Test Token".to_string(),
            symbol: "TEST".to_string(),

            decimals: 6,
            supply: "10000000000000000".to_string(),

            description: Some("Test description".to_string()),
            image: Some("https://example.com/image.png".to_string()),
            website: Some("https://example.com".to_string()),
            twitter: Some("https://x.com/test".to_string()),
            telegram: Some("https://t.me/test".to_string()),
            dex_paid: DexPaid::Paid,

            create_dex: Dex::Unknown,
            create_timestamp_millis: 0,
            create_block_number: None,
            create_tx_hash: None,
            create_bonding_curve: None,
            create_dev: None,
            is_trench_token: false,
            migration_pool_address: None,
            migration_timestamp_millis: 0,

            is_mutable: Some(false),
            update_authority: None,
            mint_authority: None,
            freeze_authority: None,

            best_pool_address: "0x1234567890123456789012345678901234567890".to_string(),
            pool_addresses: "0x1234567890123456789012345678901234567890".to_string(),

            usd_price: 1.0,
            usd_market_cap: 1.0,
            usd_liquidity: 1.0,

            bonding_curve_progress: Some(0.5),

            usd_total_volume: 1.0,
            usd_total_buy_volume: 1.0,
            usd_total_sell_volume: 1.0,

            total_txns: 1,
            total_buy_txns: 1,
            total_sell_txns: 1,

            total_trend: 1.0,
            total_price_change: 1.0,

            trend_5m: 0.01,
            price_change_5m: 0.5,
            txns_5m: 5,
            buy_txns_5m: 5,
            sell_txns_5m: 5,
            usd_volume_5m: 5.0,
            usd_buy_volume_5m: 5.0,
            usd_sell_volume_5m: 5.0,

            trend_1h: 0.02,
            price_change_1h: 0.1,
            txns_1h: 60,
            buy_txns_1h: 60,
            sell_txns_1h: 60,
            usd_volume_1h: 60.0,
            usd_buy_volume_1h: 60.0,
            usd_sell_volume_1h: 60.0,

            trend_6h: 0.03,
            price_change_6h: 0.6,
            txns_6h: 360,
            buy_txns_6h: 360,
            sell_txns_6h: 360,
            usd_volume_6h: 360.0,
            usd_buy_volume_6h: 360.0,
            usd_sell_volume_6h: 360.0,

            trend_24h: 0.04,
            price_change_24h: 0.24,
            txns_24h: 2400,
            buy_txns_24h: 2400,
            sell_txns_24h: 2400,
            usd_volume_24h: 2400.0,
            usd_buy_volume_24h: 2400.0,
            usd_sell_volume_24h: 2400.0,

            trend_3d: 0.05,
            price_change_3d: 0.3,
            txns_3d: 3000,
            buy_txns_3d: 3000,
            sell_txns_3d: 3000,
            usd_volume_3d: 3000.0,
            usd_buy_volume_3d: 3000.0,
            usd_sell_volume_3d: 3000.0,

            trend_7d: 0.06,
            price_change_7d: 0.7,
            txns_7d: 7000,
            buy_txns_7d: 7000,
            sell_txns_7d: 7000,
            usd_volume_7d: 7000.0,
            usd_buy_volume_7d: 7000.0,
            usd_sell_volume_7d: 7000.0,

            dev_hold_percentage: Some(0.1),
            dev_sold_percentage: Some(0.2),
            top10_hold_percentage: Some(0.3),
            sniper_hold_percentage: Some(0.4),
            insider_hold_percentage: Some(0.5),
            bot_hold_percentage: Some(0.6),
            holder_count: Some(10),
            sniper_count: Some(9),
            insider_count: Some(8),
            bot_count: Some(7),

            update_timestamp_millis: 1718745600000,

            // New fields
            is_active: true,
            best_pool_dex: Dex::Pumpswap,
            pool_dexes: "pumpswap,meteora".to_string(),
            init_usd_price: Some(1.0),
            image_path: None,
        };

        redis_client.set_token(&token_statistic).await.unwrap();

        let mut filter = TokensFilter {
            is_active: Some(true),
            limit: 1,
            offset: 0,
            time: Some(Time::Hour24),
            trend: Some(Trend::Top),
            dex_paid: Some(true),
            ..Default::default()
        };

        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!("search_tokens_by_filter, active = true: {:?}", stats);

        filter.is_active = Some(false);
        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!("search_tokens_by_filter, active = false: {:?}", stats);

        filter.is_active = None;
        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!("search_tokens_by_filter, active = None: {:?}", stats);

        filter.best_pool_dex = Some(vec![Dex::Meteora]);
        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!("search_tokens_by_filter, best_pool_dex = Meteora: {:?}", stats);

        filter.best_pool_dex = Some(vec![Dex::Pumpswap]);
        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!("search_tokens_by_filter, best_pool_dex = Pumpswap: {:?}", stats);

        filter.best_pool_dex = Some(vec![Dex::Pumpswap, Dex::Meteora]);
        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!("search_tokens_by_filter, best_pool_dex = Pumpswap, Meteora: {:?}", stats);

        filter.best_pool_dex = None;
        filter.pool_dexes = Some(vec![Dex::Meteora]);
        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!(
            "search_tokens_by_filter, best_pool_dex = None, pool_dexes = Meteora: {:?}",
            stats
        );

        filter.best_pool_dex = None;
        filter.pool_dexes = Some(vec![Dex::Pumpswap, Dex::Meteora]);
        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!(
            "search_tokens_by_filter, best_pool_dex = None, pool_dexes = Pumpswap, Meteora: {:?}",
            stats
        );

        filter.best_pool_dex = None;
        filter.pool_dexes = Some(vec![Dex::Pumpfun]);
        let stats = redis_client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!(
            "search_tokens_by_filter, best_pool_dex = None, pool_dexes = Pumpfun: {:?}",
            stats
        );

        redis_client.delete_token(Chain::Solana, token_address).await.unwrap();
    }

    #[tokio::test]
    async fn test_get_token_price() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();
        let usdc_address = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
        let price = redis_client.get_token_price(Chain::Solana, usdc_address).await.unwrap();
        tracing::info!("get_token_price: {:?}", price);
    }

    #[tokio::test]
    async fn test_get_token_price_and_decimals() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();
        let usdc_address = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
        let (price, decimals) =
            redis_client.get_token_price_and_decimals(Chain::Solana, usdc_address).await.unwrap();
        tracing::info!("get_token_price_and_decimals: {:?}", (price, decimals));
    }

    #[tokio::test]
    #[ignore]
    async fn test_get_token_for_hypercore() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();
        let token_address = "0xbaf265ef389da684513d98d68edf4eae";
        let token_statistic =
            redis_client.get_token(Chain::Hypercore, token_address).await.unwrap().unwrap();
        tracing::info!("get_token: {:?}", token_statistic);
    }

    #[tokio::test]
    async fn test_get_token_index_info() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();
        redis_client.get_token_index_info().await.unwrap();
    }

    #[tokio::test]
    #[ignore]
    async fn test_new_token_index() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();
        redis_client.drop_token_statistic_index().await.unwrap();
        redis_client.create_token_statistic_index().await.unwrap();
        redis_client.get_token_index_info().await.unwrap();
    }
}
