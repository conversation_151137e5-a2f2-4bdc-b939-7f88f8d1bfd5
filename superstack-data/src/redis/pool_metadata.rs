use std::collections::HashMap;

use redis::AsyncCommands;

use super::{utils::*, value::RedisValue, *};
use crate::{
    error::Result,
    postgres::{enums::Chain, indexer::PoolMetadata},
};

const POOL_METADATA_KEY_PREFIX: &str = "pool_metadata:";

// Human-readable constants for short field names
const FIELD_CHAIN: &str = "c";
const FIELD_POOL_ADDRESS: &str = "a";

const FIELD_PAIR_LABEL: &str = "pl";
const FIELD_DEX: &str = "d";
const FIELD_POOL_TYPE: &str = "pt";
const FIELD_CREATE_TIMESTAMP: &str = "ct";
const FIELD_UPDATE_TIMESTAMP: &str = "ut";

const FIELD_TOKEN_ADDRESS: &str = "ta";
const FIELD_TOKEN_DECIMALS: &str = "td";
const FIELD_BASE_ADDRESS: &str = "ba";
const FIELD_BASE_DECIMALS: &str = "bd";
const FIELD_IS_TOKEN_FIRST: &str = "tf";

const FIELD_IS_ACTIVE: &str = "ia";
const FIELD_BIN_STEP: &str = "bs";

impl RedisClient {
    fn construct_pool_metadata_key(chain: Chain, pool_address: &str) -> String {
        format!("{}{}:{}", POOL_METADATA_KEY_PREFIX, pool_address, chain.to_string())
    }

    // Convert TokenMetadata to HashMap with short field names and proper types
    fn pool_metadata_to_hash(pool_metadata: &PoolMetadata) -> Vec<(&'static str, RedisValue)> {
        let mut hash_data = vec![
            (FIELD_CHAIN, RedisValue::String(pool_metadata.chain.to_string())),
            (FIELD_POOL_ADDRESS, RedisValue::String(pool_metadata.pool_address.clone())),
            (FIELD_PAIR_LABEL, RedisValue::String(pool_metadata.pair_label.clone())),
            (FIELD_DEX, RedisValue::String(pool_metadata.dex.to_string())),
            (FIELD_POOL_TYPE, RedisValue::String(pool_metadata.pool_type.to_string())),
            (FIELD_CREATE_TIMESTAMP, RedisValue::Int64(pool_metadata.create_timestamp_millis)),
            (FIELD_UPDATE_TIMESTAMP, RedisValue::Int64(pool_metadata.update_timestamp_millis)),
            (FIELD_TOKEN_ADDRESS, RedisValue::String(pool_metadata.token_address.clone())),
            (FIELD_TOKEN_DECIMALS, RedisValue::UInt8(pool_metadata.token_decimals)),
            (FIELD_BASE_ADDRESS, RedisValue::String(pool_metadata.base_address.clone())),
            (FIELD_BASE_DECIMALS, RedisValue::UInt8(pool_metadata.base_decimals)),
            (FIELD_IS_TOKEN_FIRST, RedisValue::Bool(pool_metadata.is_token_first)),
            (FIELD_IS_ACTIVE, RedisValue::Bool(pool_metadata.is_active)),
        ];

        if let Some(bin_step) = pool_metadata.bin_step {
            hash_data.push((FIELD_BIN_STEP, RedisValue::UInt16(bin_step)));
        }

        hash_data
    }

    // Convert HashMap back to TokenStatistic
    fn hash_to_pool_metadata(hash_data: &HashMap<String, String>) -> Result<PoolMetadata> {
        Ok(PoolMetadata {
            // Basic metadata
            chain: parse_chain_from_map(hash_data, FIELD_CHAIN)?,
            pool_address: parse_string_from_map(hash_data, FIELD_POOL_ADDRESS)?,
            pair_label: parse_string_from_map(hash_data, FIELD_PAIR_LABEL)?,
            dex: parse_dex_from_map(hash_data, FIELD_DEX)?,
            pool_type: parse_pool_type_from_map(hash_data, FIELD_POOL_TYPE)?,
            create_timestamp_millis: parse_i64_from_map(hash_data, FIELD_CREATE_TIMESTAMP)?,
            update_timestamp_millis: parse_i64_from_map(hash_data, FIELD_UPDATE_TIMESTAMP)?,
            token_address: parse_string_from_map(hash_data, FIELD_TOKEN_ADDRESS)?,
            token_decimals: parse_u8_from_map(hash_data, FIELD_TOKEN_DECIMALS)?,
            base_address: parse_string_from_map(hash_data, FIELD_BASE_ADDRESS)?,
            base_decimals: parse_u8_from_map(hash_data, FIELD_BASE_DECIMALS)?,
            is_token_first: parse_bool_from_map(hash_data, FIELD_IS_TOKEN_FIRST)?,
            is_active: parse_bool_from_map(hash_data, FIELD_IS_ACTIVE)?,
            bin_step: parse_u16_from_map(hash_data, FIELD_BIN_STEP).ok(),
        })
    }

    pub async fn set_pool_metadata(&self, pool_metadata: &PoolMetadata) -> Result<()> {
        let key =
            Self::construct_pool_metadata_key(pool_metadata.chain, &pool_metadata.pool_address);
        let mut conn = self.get_connection();

        let hash_data = Self::pool_metadata_to_hash(pool_metadata);

        let _: () = conn.hset_multiple(key, &hash_data).await?;

        Ok(())
    }

    pub async fn set_pool_metadata_is_active(
        &self,
        chain: Chain,
        pool_address: &str,
        is_active: bool,
        update_timestamp_millis: i64,
    ) -> Result<()> {
        let key = Self::construct_pool_metadata_key(chain, pool_address);
        let mut conn = self.get_connection();

        let mut pipe = redis::pipe();
        pipe.hset(&key, FIELD_IS_ACTIVE, is_active);
        pipe.hset(&key, FIELD_UPDATE_TIMESTAMP, update_timestamp_millis);

        // Execute both commands in one round trip
        let _: () = pipe.query_async(&mut conn).await?;
        Ok(())
    }

    pub async fn set_pool_metadata_create_timestamp(
        &self,
        chain: Chain,
        pool_address: &str,
        create_timestamp_millis: i64,
    ) -> Result<()> {
        let key = Self::construct_pool_metadata_key(chain, pool_address);
        let mut conn = self.get_connection();

        let _: () = conn.hset(&key, FIELD_CREATE_TIMESTAMP, create_timestamp_millis).await?;
        Ok(())
    }

    pub async fn contains_pool_metadata(&self, chain: Chain, pool_address: &str) -> Result<bool> {
        let key = Self::construct_pool_metadata_key(chain, pool_address);
        let mut conn = self.get_connection();
        let exists: bool = conn.exists(&key).await?;
        Ok(exists)
    }

    async fn get_pool_metadata(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolMetadata>> {
        let key = Self::construct_pool_metadata_key(chain, pool_address);
        let mut conn = self.get_connection();

        let hash_data: HashMap<String, String> = conn.hgetall(&key).await?;

        if hash_data.is_empty() {
            return Ok(None);
        }

        Ok(Some(Self::hash_to_pool_metadata(&hash_data)?))
    }

    pub async fn get_pool_metadata_or_update_from_db(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<PoolMetadata> {
        // Validate address is not empty
        if pool_address.is_empty() {
            tracing::error!("Empty pool address provided to get_pool_metadata_or_fetch_from_db");
            return Err(anyhow::anyhow!("Empty pool address").into());
        }

        match self.get_pool_metadata(chain, pool_address).await? {
            Some(pool) => Ok(pool),
            None => {
                let db = PostgresDatabase::get_indexer_db().await;
                match db.get_pool_metadata(chain, pool_address).await? {
                    Some(pool) => {
                        self.set_pool_metadata(&pool).await?;
                        Ok(pool)
                    }
                    None => {
                        tracing::warn!(
                            "Pool metadata not found in db for {} on chain {:?}",
                            pool_address,
                            chain
                        );
                        Err(anyhow::anyhow!("Pool metadata not found in db for {}", pool_address)
                            .into())
                    }
                }
            }
        }
    }

    pub async fn try_get_pool_metadata_or_update_from_db(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolMetadata>> {
        // Validate address is not empty
        if pool_address.is_empty() {
            tracing::error!("Empty pool address provided to get_pool_metadata_or_fetch_from_db");
            return Err(anyhow::anyhow!("Empty pool address").into());
        }

        match self.get_pool_metadata(chain, pool_address).await? {
            Some(pool) => Ok(Some(pool)),
            None => {
                let db = PostgresDatabase::get_indexer_db().await;
                match db.get_pool_metadata(chain, pool_address).await? {
                    Some(pool) => {
                        self.set_pool_metadata(&pool).await?;
                        Ok(Some(pool))
                    }
                    None => Ok(None),
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::postgres::*;

    #[tokio::test]
    async fn test_redis_pool_metadata() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();
        let pool_metadata = PoolMetadata {
            chain: Chain::Solana,
            pool_address: "113DEG1r4DwHJncBbpe9xYXHcHmmS8pGtZCdvZto9F6".to_string(),
            pair_label: "SHIT/SOL".to_string(),
            dex: Dex::Pumpfun,
            pool_type: PoolType::None,
            create_timestamp_millis: 1749885723000,
            update_timestamp_millis: 1749885723000,
            token_address: "Ad1hzrvm4LLNNookeh9LTAsQojBj7brUiLWtx2oCpump".to_string(),
            token_decimals: 6,
            base_address: "So11111111111111111111111111111111111111112".to_string(),
            base_decimals: 9,
            is_token_first: true,
            is_active: true,
            bin_step: None,
        };

        if redis_client
            .contains_pool_metadata(Chain::Solana, &pool_metadata.pool_address)
            .await
            .unwrap()
        {
            tracing::info!("Pool metadata already exists");
        }

        redis_client.set_pool_metadata(&pool_metadata).await.unwrap();

        let pool_metadata_from_redis = redis_client
            .get_pool_metadata(Chain::Solana, &pool_metadata.pool_address)
            .await
            .unwrap()
            .unwrap();
        assert_eq!(pool_metadata, pool_metadata_from_redis);

        let pool_metadata_2 = PoolMetadata {
            chain: Chain::Solana,
            pool_address: "12bLf4hwaewmAwEyK1467Ac7RqzZKEwfAGdFY7kqjaFY".to_string(),
            pair_label: "MAJA/SOL".to_string(),
            dex: Dex::Meteora,
            pool_type: PoolType::Dlmm,
            create_timestamp_millis: 1750694084000,
            update_timestamp_millis: 1750694084000,
            token_address: "6KxQBeUGQWc7BWLswASHsoBe2ScdxDfaj2AuNKHcPLXL".to_string(),
            token_decimals: 9,
            base_address: "So11111111111111111111111111111111111111112".to_string(),
            base_decimals: 9,
            is_token_first: true,
            is_active: true,
            bin_step: Some(80),
        };

        redis_client.set_pool_metadata(&pool_metadata_2).await.unwrap();

        let pool_metadata_from_redis = redis_client
            .get_pool_metadata(Chain::Solana, &pool_metadata_2.pool_address)
            .await
            .unwrap()
            .unwrap();
        assert_eq!(pool_metadata_2, pool_metadata_from_redis);
    }
}
