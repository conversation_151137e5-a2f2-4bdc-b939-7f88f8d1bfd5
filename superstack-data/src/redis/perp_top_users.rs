use std::collections::HashMap;

use crate::{error::Result, postgres::indexer::perp_top_user_state::PerpTopUserState};
use redis::AsyncCommands;

use super::{utils::*, value::RedisValue, RedisClient};

const PERP_TOP_USER_KEY_PREFIX: &str = "perp_top_user:";
const PERP_TOP_USER_INDEX: &str = "perp_top_user_idx";

const PERP_TOP_USER_IDS_KEY_PREFIX: &str = "perp_top_user_ids:";

// Human-readable constants for short field names
const FIELD_PERP_NAME: &str = "pn"; // tag for search
const FIELD_USER_ADDR: &str = "ua";
const FIELD_NTL: &str = "ntl";
const FIELD_IS_LONG: &str = "il"; // tag for search
const FIELD_ENTRY_PX: &str = "epx";
const FIELD_LIQ_PX: &str = "lpx";
const FIELD_SIZE: &str = "sz";
const FIELD_FUNDING: &str = "fnd";
const FIELD_ACCOUNT_VALUE: &str = "av";
const FIELD_UPDATE_TIMESTAMP: &str = "ut";

impl RedisClient {
    // perp_top_user:$perp:$user
    fn construct_perp_top_user_key(perp_name: &str, user_addr: &str) -> String {
        format!("{}{}:{}", PERP_TOP_USER_KEY_PREFIX, perp_name, user_addr)
    }

    fn perp_top_user_to_hash(perp_top_user: &PerpTopUserState) -> Vec<(&'static str, RedisValue)> {
        let mut hash_data = vec![
            (FIELD_PERP_NAME, RedisValue::String(perp_top_user.perp_name.clone())),
            (FIELD_USER_ADDR, RedisValue::String(perp_top_user.user_addr.clone())),
            (FIELD_NTL, RedisValue::Float64(perp_top_user.ntl)),
            (FIELD_IS_LONG, RedisValue::Bool(perp_top_user.is_long)),
            (FIELD_ENTRY_PX, RedisValue::Float64(perp_top_user.entry_px)),
            (FIELD_SIZE, RedisValue::Float64(perp_top_user.size)),
            (FIELD_ACCOUNT_VALUE, RedisValue::Float64(perp_top_user.account_value)),
            (FIELD_UPDATE_TIMESTAMP, RedisValue::Int64(perp_top_user.timestamp_millis)),
        ];

        if let Some(funding) = perp_top_user.funding {
            hash_data.push((FIELD_FUNDING, RedisValue::Float64(funding)));
        }

        if let Some(liq_px) = perp_top_user.liq_px {
            hash_data.push((FIELD_LIQ_PX, RedisValue::Float64(liq_px)));
        }

        hash_data
    }

    fn hash_to_perp_top_user(hash_data: &HashMap<String, String>) -> Result<PerpTopUserState> {
        let perp_name = parse_string_from_map(hash_data, FIELD_PERP_NAME)?;
        let user_addr = parse_string_from_map(hash_data, FIELD_USER_ADDR)?;
        let ntl = parse_f64_from_map(hash_data, FIELD_NTL)?;
        let is_long = parse_bool_from_map(hash_data, FIELD_IS_LONG)?;
        let entry_px = parse_f64_from_map(hash_data, FIELD_ENTRY_PX)?;
        let liq_px = parse_f64_from_map(hash_data, FIELD_LIQ_PX).ok();
        let size = parse_f64_from_map(hash_data, FIELD_SIZE)?;
        let funding = parse_f64_from_map(hash_data, FIELD_FUNDING).ok();
        let account_value = parse_f64_from_map(hash_data, FIELD_ACCOUNT_VALUE)?;
        let timestamp_millis = parse_i64_from_map(hash_data, FIELD_UPDATE_TIMESTAMP)?;

        Ok(PerpTopUserState {
            perp_name,
            user_addr,
            ntl,
            is_long,
            entry_px,
            liq_px,
            size,
            funding,
            account_value,
            timestamp_millis,
        })
    }

    pub async fn set_perp_top_user(&self, user_state: &PerpTopUserState) -> Result<()> {
        let key = Self::construct_perp_top_user_key(&user_state.perp_name, &user_state.user_addr);
        let hash_data = Self::perp_top_user_to_hash(user_state);

        let _: () = self.get_connection().hset_multiple(&key, &hash_data).await?;

        Ok(())
    }

    pub async fn create_perp_top_user_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&PERP_TOP_USER_INDEX.to_string()) {
            tracing::info!("Index {} already exists", PERP_TOP_USER_INDEX);
            return Ok(());
        }

        let start_time = std::time::Instant::now();
        tracing::info!("Creating index {}", PERP_TOP_USER_INDEX);
        let _: () = redis::cmd("FT.CREATE")
            .arg(PERP_TOP_USER_INDEX)
            .arg("ON")
            .arg("HASH")
            .arg("PREFIX")
            .arg("1")
            .arg(PERP_TOP_USER_KEY_PREFIX)
            .arg("SCHEMA")
            // minimal fields for filter by perp_name and is_long
            .arg(FIELD_PERP_NAME) // perp_name - for filtering
            .arg("TAG")
            .arg(FIELD_IS_LONG) // is_long - for filtering
            .arg("TAG")
            .query_async(&mut conn)
            .await?;
        tracing::info!("Index {} created in {:?}", PERP_TOP_USER_INDEX, start_time.elapsed());
        Ok(())
    }

    #[cfg(test)]
    pub async fn drop_perp_top_user_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&PERP_TOP_USER_INDEX.to_string()) {
            tracing::info!("Index {} already exists", PERP_TOP_USER_INDEX);
            let _: () =
                redis::cmd("FT.DROPINDEX").arg(PERP_TOP_USER_INDEX).query_async(&mut conn).await?;
            tracing::info!("Index {} dropped", PERP_TOP_USER_INDEX);
        }
        Ok(())
    }

    pub async fn get_perp_top_users_indexed(
        &self,
        perp_name: &str,
        is_long: Option<bool>,
        limit: u64,
    ) -> Result<Vec<PerpTopUserState>> {
        let mut conn = self.get_connection();

        let mut query = format!("@{}:{{{}}}", FIELD_PERP_NAME, perp_name);

        if let Some(is_long) = is_long {
            query.push_str(&format!(" @{}:{{{}}}", FIELD_IS_LONG, is_long));
        }

        let result: redis::Value = redis::cmd("FT.SEARCH")
            .arg(PERP_TOP_USER_INDEX)
            .arg(&query)
            .arg("LIMIT")
            .arg(0)
            .arg(limit)
            .query_async(&mut conn)
            .await?;

        let parsed = parse_redis_search_result(result);

        let mut vs = Vec::with_capacity(parsed.len());
        for hash_data in parsed {
            if let Ok(v) = Self::hash_to_perp_top_user(&hash_data) {
                vs.push(v);
            }
        }

        Ok(vs)
    }

    fn construct_perp_top_user_ids_key(perp_name: &str) -> String {
        format!("{}{}", PERP_TOP_USER_IDS_KEY_PREFIX, perp_name)
    }

    pub async fn set_perp_top_user_ids(&self, perp_name: &str, ids: &[String]) -> Result<()> {
        let key = Self::construct_perp_top_user_ids_key(perp_name);
        let mut conn = self.get_connection();

        let json = serde_json::to_string(ids).map_err(|e| anyhow::anyhow!(e))?;
        let _: () = conn.set(&key, json).await?;

        Ok(())
    }

    pub async fn get_perp_top_user_ids(&self, perp_name: &str) -> Result<Vec<String>> {
        let key = Self::construct_perp_top_user_ids_key(perp_name);
        let mut conn = self.get_connection();

        let json: String = conn.get(&key).await?;
        let ids: Vec<String> = serde_json::from_str(&json).map_err(|e| anyhow::anyhow!(e))?;

        Ok(ids)
    }

    pub async fn get_perp_top_users(&self, perp_name: &str) -> Result<Vec<PerpTopUserState>> {
        let key = Self::construct_perp_top_user_ids_key(perp_name);
        let mut conn = self.get_connection();

        let json: String = conn.get(&key).await?;
        let ids: Vec<String> = serde_json::from_str(&json).map_err(|e| anyhow::anyhow!(e))?;

        let mut pipe = redis::pipe();
        for id in ids {
            let key = Self::construct_perp_top_user_key(perp_name, &id);
            pipe.hgetall(&key);
        }

        let results: Vec<HashMap<String, String>> = pipe.query_async(&mut conn).await?;
        let mut vs = Vec::with_capacity(results.len());
        for hash_data in results {
            if !hash_data.is_empty() {
                let v = Self::hash_to_perp_top_user(&hash_data)?;
                vs.push(v);
            }
        }

        Ok(vs)
    }
}
