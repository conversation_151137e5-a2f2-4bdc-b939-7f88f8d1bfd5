use std::time::Duration;

use redis::{AsyncCommands, RedisResult};
use tokio::time::sleep;

use super::RedisClient;
use crate::Error;

/// Distributed lock implementation using Redis
pub struct DistributedLock {
    redis_client: RedisClient,
    key: String,
    value: String,
    ttl_seconds: u64,
}

impl DistributedLock {
    /// Create a new distributed lock
    pub fn new(redis_client: RedisClient, lock_key: &str, ttl_seconds: u64) -> Self {
        let value = format!(
            "{}:{}",
            std::process::id(),
            chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0)
        );
        Self { redis_client, key: format!("lock:{}", lock_key), value, ttl_seconds }
    }

    /// Try to acquire the lock
    pub async fn try_acquire(&self) -> Result<bool, Error> {
        let mut conn = self.redis_client.get_connection();

        // Use SET with NX (only if not exists) and EX (expiration)
        let result: RedisResult<String> = redis::cmd("SET")
            .arg(&self.key)
            .arg(&self.value)
            .arg("NX")
            .arg("EX")
            .arg(self.ttl_seconds)
            .query_async(&mut conn)
            .await;

        match result {
            Ok(response) => {
                if response == "OK" {
                    Ok(true) // Lock acquired
                } else {
                    Ok(false) // Lock not acquired
                }
            }
            Err(_) => Ok(false), // Lock not acquired (key already exists)
        }
    }

    /// Acquire the lock with retry and timeout
    pub async fn acquire_with_retry(
        &self,
        retry_interval: Duration,
        max_wait: Duration,
    ) -> Result<bool, Error> {
        let start = std::time::Instant::now();

        loop {
            if self.try_acquire().await? {
                return Ok(true);
            }

            if start.elapsed() >= max_wait {
                return Ok(false);
            }

            sleep(retry_interval).await;
        }
    }

    /// Release the lock (only if we own it)
    pub async fn release(&self) -> Result<bool, Error> {
        let mut conn = self.redis_client.get_connection();

        // Lua script to ensure we only delete the lock if we own it
        let script = r#"
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
        "#;

        let result: i32 = redis::cmd("EVAL")
            .arg(script)
            .arg(1)
            .arg(&self.key)
            .arg(&self.value)
            .query_async(&mut conn)
            .await
            .map_err(Error::RedisError)?;

        Ok(result == 1)
    }

    /// Extend the lock TTL (only if we own it)
    pub async fn extend(&self, additional_seconds: u64) -> Result<bool, Error> {
        let mut conn = self.redis_client.get_connection();

        // Lua script to extend TTL only if we own the lock
        let script = r#"
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("EXPIRE", KEYS[1], ARGV[2])
            else
                return 0
            end
        "#;

        let new_ttl = self.ttl_seconds + additional_seconds;
        let result: i32 = redis::cmd("EVAL")
            .arg(script)
            .arg(1)
            .arg(&self.key)
            .arg(&self.value)
            .arg(new_ttl.to_string())
            .query_async(&mut conn)
            .await
            .map_err(Error::RedisError)?;

        Ok(result == 1)
    }

    /// Check if the lock is still held by us
    pub async fn is_held(&self) -> Result<bool, Error> {
        let mut conn = self.redis_client.get_connection();

        let current_value: Option<String> = conn.get(&self.key).await.map_err(Error::RedisError)?;

        Ok(current_value.as_ref() == Some(&self.value))
    }
}

/// RAII wrapper for distributed lock that automatically releases on drop
pub struct DistributedLockGuard {
    lock: DistributedLock,
    acquired: bool,
}

impl DistributedLockGuard {
    /// Create a new lock guard and try to acquire the lock
    pub async fn new(
        redis_client: RedisClient,
        lock_key: &str,
        ttl_seconds: u64,
    ) -> Result<Self, Error> {
        let lock = DistributedLock::new(redis_client, lock_key, ttl_seconds);
        let acquired = lock.try_acquire().await?;

        Ok(Self { lock, acquired })
    }

    /// Create a new lock guard with retry
    pub async fn new_with_retry(
        redis_client: RedisClient,
        lock_key: &str,
        ttl_seconds: u64,
        retry_interval: Duration,
        max_wait: Duration,
    ) -> Result<Self, Error> {
        let lock = DistributedLock::new(redis_client, lock_key, ttl_seconds);
        let acquired = lock.acquire_with_retry(retry_interval, max_wait).await?;

        Ok(Self { lock, acquired })
    }

    /// Check if the lock was successfully acquired
    pub fn is_acquired(&self) -> bool {
        self.acquired
    }

    /// Extend the lock TTL
    pub async fn extend(&self, additional_seconds: u64) -> Result<bool, Error> {
        if self.acquired {
            self.lock.extend(additional_seconds).await
        } else {
            Ok(false)
        }
    }

    /// Check if the lock is still held
    pub async fn is_held(&self) -> Result<bool, Error> {
        if self.acquired {
            self.lock.is_held().await
        } else {
            Ok(false)
        }
    }
}

impl Drop for DistributedLockGuard {
    fn drop(&mut self) {
        if self.acquired {
            // Create a new lock with the same parameters for cleanup
            let redis_client = self.lock.redis_client.clone();
            let key = self.lock.key.clone();
            let value = self.lock.value.clone();
            let ttl_seconds = self.lock.ttl_seconds;

            let cleanup_lock = DistributedLock { redis_client, key, value, ttl_seconds };

            // Spawn a task to release the lock
            tokio::spawn(async move {
                if let Err(e) = cleanup_lock.release().await {
                    tracing::error!("Failed to release distributed lock: {}", e);
                }
            });
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_distributed_lock() {
        // This test requires a running Redis instance
        // Skip if Redis is not available
        if std::env::var("REDIS_URL").is_err() {
            return;
        }

        let redis_client = RedisClient::new("redis://localhost:6379").await.unwrap();

        let lock_key = "test_lock";
        let ttl = 10;

        // Test acquiring lock
        let guard1 = DistributedLockGuard::new(redis_client.clone(), lock_key, ttl).await.unwrap();
        assert!(guard1.is_acquired());

        // Test that second lock fails
        let guard2 = DistributedLockGuard::new(redis_client.clone(), lock_key, ttl).await.unwrap();
        assert!(!guard2.is_acquired());

        // Test that lock is held
        assert!(guard1.is_held().await.unwrap());

        // Drop first lock
        drop(guard1);

        // Give some time for the lock to be released
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Test that new lock can be acquired
        let guard3 = DistributedLockGuard::new(redis_client, lock_key, ttl).await.unwrap();
        assert!(guard3.is_acquired());
    }
}
