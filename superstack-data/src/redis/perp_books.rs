use std::collections::HashMap;

use crate::{
    error::Result,
    postgres::indexer::perp_books::{PerpBook, PerpBooks},
};
use redis::AsyncCommands;

use super::{utils::*, value::RedisValue, RedisClient};

const PERP_KEY_PREFIX: &str = "perp:";

// Human-readable constants for short field names
const FIELD_TOTAL_SZ: &str = "ts";
const FIELD_BID_ORDERS: &str = "bo"; // vec of PerpBook
const FIELD_ASK_ORDERS: &str = "ao"; // vec of PerpBook
const FIELD_UPDATE_TIMESTAMP: &str = "ut";

impl RedisClient {
    fn construct_perp_books_key(perp_id: &str) -> String {
        format!("{}{}", PERP_KEY_PREFIX, perp_id)
    }

    // perp books
    fn perp_books_to_string(perp_books: &[PerpBook]) -> RedisValue {
        // Serialize Vec<PerpBook> to JSON string for storage
        let json_string = serde_json::to_string(perp_books).unwrap_or_default();
        RedisValue::String(json_string)
    }

    fn perp_books_state_to_hash(perp_state: &PerpBooks) -> Vec<(&'static str, RedisValue)> {
        vec![
            (FIELD_TOTAL_SZ, RedisValue::Float64(perp_state.total_sz)),
            (FIELD_BID_ORDERS, Self::perp_books_to_string(&perp_state.books[0])),
            (FIELD_ASK_ORDERS, Self::perp_books_to_string(&perp_state.books[1])),
            (FIELD_UPDATE_TIMESTAMP, RedisValue::Int64(perp_state.updated_at_millis)),
        ]
    }

    /// Write PerpBooks to Redis
    pub async fn set_perp_books(&self, perp_books: &PerpBooks) -> Result<()> {
        let key = Self::construct_perp_books_key(&perp_books.perp_id);
        let mut conn = self.get_connection();

        let hash_data = Self::perp_books_state_to_hash(perp_books);

        let _: () = conn.hset_multiple(key, &hash_data).await?;

        Ok(())
    }

    /// Write only Vec<PerpBook> to Redis with custom key
    pub async fn set_perp_book_list(
        &self,
        perp_id: &str,
        side: &str, // "bid" or "ask"
        perp_books: &[PerpBook],
        timestamp: i64,
    ) -> Result<()> {
        let key = Self::construct_perp_books_key(perp_id);
        let mut conn = self.get_connection();

        let field = match side {
            "bid" => FIELD_BID_ORDERS,
            "ask" => FIELD_ASK_ORDERS,
            _ => {
                return Err(crate::error::Error::AnyhowError(anyhow::anyhow!(
                    "side must be 'bid' or 'ask'"
                )))
            }
        };

        let mut pipe = redis::pipe();
        pipe.hset(&key, field, Self::perp_books_to_string(perp_books));
        pipe.hset(&key, FIELD_UPDATE_TIMESTAMP, timestamp);

        let _: () = pipe.query_async(&mut conn).await?;
        Ok(())
    }

    /// Get PerpBooks from Redis
    pub async fn get_perp_books(&self, perp_id: &str) -> Result<Option<PerpBooks>> {
        let key = Self::construct_perp_books_key(perp_id);
        let mut conn = self.get_connection();

        let hash_data: std::collections::HashMap<String, String> = conn.hgetall(&key).await?;

        if hash_data.is_empty() {
            return Ok(None);
        }

        // Parse the hash data back to PerpBooks
        let bid_orders: Vec<PerpBook> = parse_perp_books_from_map(&hash_data, FIELD_BID_ORDERS)?;
        let ask_orders: Vec<PerpBook> = parse_perp_books_from_map(&hash_data, FIELD_ASK_ORDERS)?;

        let perp_books = PerpBooks {
            perp_id: perp_id.to_string(),
            perp_exchange: crate::postgres::PerpExchange::Hyperliquid, /* Default, should be set
                                                                        * properly */
            updated_at_millis: parse_i64_from_map(&hash_data, FIELD_UPDATE_TIMESTAMP)?,
            total_sz: parse_f64_from_map(&hash_data, FIELD_TOTAL_SZ)?,
            books: [bid_orders, ask_orders],
        };

        Ok(Some(perp_books))
    }

    pub async fn get_perp_books_total_sz(&self, perp_id: &str) -> Result<Option<f64>> {
        let key = Self::construct_perp_books_key(perp_id);
        let mut conn = self.get_connection();

        let total_sz: Option<String> = conn.hget(&key, FIELD_TOTAL_SZ).await?;

        match total_sz {
            Some(value) => {
                let parsed = value.parse::<f64>().map_err(|e| {
                    crate::error::Error::AnyhowError(anyhow::anyhow!(
                        "Failed to parse total_sz: {}",
                        e
                    ))
                })?;
                Ok(Some(parsed))
            }
            None => Ok(None),
        }
    }
}

fn parse_perp_books_from_map(
    hash_data: &HashMap<String, String>,
    key: &str,
) -> anyhow::Result<Vec<PerpBook>> {
    let s = hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key))?;
    let perp_books = serde_json::from_str::<Vec<PerpBook>>(s)?;
    Ok(perp_books)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_set_get_perp_books() {
        dotenv::dotenv().ok();
        let redis_url = std::env::var("REDIS_URL").unwrap();
        assert!(!redis_url.is_empty());
        let client = RedisClient::new(&redis_url).await.unwrap();

        let perp_books = PerpBooks {
            perp_id: "tBTC".to_string(),
            perp_exchange: crate::postgres::PerpExchange::Hyperliquid,
            updated_at_millis: chrono::Utc::now().timestamp_millis(),
            total_sz: 0.0,
            books: [
                vec![PerpBook { px: 29179., sz: 0.04, n: 1 }],
                vec![PerpBook { px: 118780.0, sz: 2.3, n: 2 }],
            ],
        };
        client.set_perp_books(&perp_books).await.unwrap();

        let perp_books = client.get_perp_books("tBTC").await.unwrap().unwrap();
        assert_eq!(perp_books.perp_id, "tBTC");
        println!("{:?}", perp_books);
    }
}
