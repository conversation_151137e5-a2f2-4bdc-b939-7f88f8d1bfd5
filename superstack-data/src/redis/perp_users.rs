use crate::{
    error::Result,
    postgres::{indexer::perp_user_state::PerpUserState, PerpExchange},
};
use redis::AsyncCommands;
use std::collections::HashMap;

use super::{utils::*, value::RedisValue, RedisClient};

const PERP_USER_KEY_PREFIX: &str = "perp_user:";
const PERP_USER_INDEX: &str = "perp_user_idx";

const FIELD_ACCOUNT_VALUE: &str = "av";
const FIELD_PERPS_ACCOUNT_VALUE: &str = "pav";
const FIELD_HYPERLIQUID_ACC_COLLATERAL: &str = "hac";
const FIELD_HYPERLIQUID_ACC_PERPS: &str = "hap";
const FIELD_UPDATE_TIMESTAMP: &str = "ut";

// currently `perp_users` used for leaderboard, all-market wise

impl RedisClient {
    fn construct_perp_user_key(user: &str) -> String {
        format!("{}{}", PERP_USER_KEY_PREFIX, user)
    }

    fn perp_user_to_hash(perp_user: &PerpUserState) -> Vec<(&'static str, RedisValue)> {
        vec![
            (FIELD_ACCOUNT_VALUE, RedisValue::Float64(perp_user.account_value)),
            (FIELD_PERPS_ACCOUNT_VALUE, RedisValue::Float64(perp_user.perps_account_value)),
            (
                FIELD_HYPERLIQUID_ACC_COLLATERAL,
                RedisValue::Float64(perp_user.hyperliquid_acc_collateral),
            ),
            (FIELD_HYPERLIQUID_ACC_PERPS, RedisValue::Float64(perp_user.hyperliquid_acc_perps)),
            (FIELD_UPDATE_TIMESTAMP, RedisValue::Int64(perp_user.timestamp_millis)),
        ]
    }

    pub async fn set_perp_user(&self, perp_user: &PerpUserState) -> Result<()> {
        let key = Self::construct_perp_user_key(&perp_user.perp_user);
        let mut conn = self.get_connection();
        let hash_data = Self::perp_user_to_hash(perp_user);
        let _: () = conn.hset_multiple(key, &hash_data).await?;
        Ok(())
    }

    pub async fn get_perp_user(&self, perp_user: &str) -> Result<Option<PerpUserState>> {
        let key = Self::construct_perp_user_key(perp_user);
        let mut conn = self.get_connection();
        let hash_data: HashMap<String, String> = conn.hgetall(&key).await?;

        if hash_data.is_empty() {
            return Ok(None);
        }

        let account_value = parse_f64_from_map(&hash_data, FIELD_ACCOUNT_VALUE)?;
        let perps_account_value = parse_f64_from_map(&hash_data, FIELD_PERPS_ACCOUNT_VALUE)?;
        let hyperliquid_acc_collateral =
            parse_f64_from_map(&hash_data, FIELD_HYPERLIQUID_ACC_COLLATERAL)?;
        let hyperliquid_acc_perps = parse_f64_from_map(&hash_data, FIELD_HYPERLIQUID_ACC_PERPS)?;
        let timestamp_millis = parse_i64_from_map(&hash_data, FIELD_UPDATE_TIMESTAMP)?;

        let perp_user = PerpUserState {
            perp_exchange: PerpExchange::Hyperliquid,
            perp_user: perp_user.to_string(),
            account_value,
            perps_account_value,
            hyperliquid_acc_collateral,
            hyperliquid_acc_perps,
            timestamp_millis,
        };

        Ok(Some(perp_user))
    }

    pub async fn drop_perp_user_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&PERP_USER_INDEX.to_string()) {
            tracing::info!("Index {} already exists", PERP_USER_INDEX);
            let _: () =
                redis::cmd("FT.DROPINDEX").arg(PERP_USER_INDEX).query_async(&mut conn).await?;
            tracing::info!("Index {} dropped", PERP_USER_INDEX);
        }
        Ok(())
    }

    pub async fn create_perp_user_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&PERP_USER_INDEX.to_string()) {
            tracing::info!("Index {} already exists", PERP_USER_INDEX);
            return Ok(());
        }

        let _: () = redis::cmd("FT.CREATE")
            .arg(PERP_USER_INDEX)
            .arg("ON")
            .arg("HASH")
            .arg("PREFIX")
            .arg("1")
            .arg(PERP_USER_KEY_PREFIX)
            .arg("SCHEMA")
            .arg(FIELD_ACCOUNT_VALUE)
            .arg("NUMERIC")
            .arg("SORTABLE")
            .query_async(&mut conn)
            .await?;
        tracing::info!("Index {} created", PERP_USER_INDEX);
        Ok(())
    }
}
