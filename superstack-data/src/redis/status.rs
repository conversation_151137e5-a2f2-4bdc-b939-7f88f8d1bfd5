use std::collections::HashMap;

use chrono::Utc;
use redis::AsyncCommands;

use super::{utils::*, value::RedisValue, *};
use crate::error::Result;

// Status key constants
const INDEXER_STATUS_KEY: &str = "indexer:status";

// Status field constants
const FIELD_IS_INITIALIZED: &str = "initialized";
const FIELD_INIT_TIMESTAMP: &str = "init_timestamp";
const FIELD_INIT_TOKEN_COUNT: &str = "init_token_count";
const FIELD_INIT_POOL_COUNT: &str = "init_pool_count";

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct IndexerStatus {
    pub is_initialized: bool,
    pub init_timestamp: i64,
    pub init_token_count: u64,
    pub init_pool_count: u64,
}

impl Default for IndexerStatus {
    fn default() -> Self {
        Self {
            is_initialized: false,
            init_timestamp: chrono::Utc::now().timestamp(),
            init_token_count: 0,
            init_pool_count: 0,
        }
    }
}

impl RedisClient {
    /// Get the current Redis initialization status
    pub async fn get_status(&self) -> Result<IndexerStatus> {
        let mut conn = self.get_connection();

        let hash_data: HashMap<String, String> = conn.hgetall(INDEXER_STATUS_KEY).await?;

        if hash_data.is_empty() {
            return Ok(IndexerStatus::default());
        }

        Ok(Self::hash_to_indexer_status(&hash_data)?)
    }

    /// Set the Redis initialization status
    pub async fn set_status(&self, status: &IndexerStatus) -> Result<()> {
        let mut conn = self.get_connection();

        let hash_data = Self::indexer_status_to_hash(status);
        let _: () = conn.hset_multiple(INDEXER_STATUS_KEY, &hash_data).await?;

        Ok(())
    }

    /// Check if Redis is initialized with all required data
    pub async fn is_initialized(&self) -> Result<bool> {
        let status = self.get_status().await?;
        Ok(status.is_initialized)
    }

    /// Mark Redis as initialized with current data counts
    pub async fn mark_as_initialized(&self, token_count: u64, pool_count: u64) -> Result<()> {
        let mut status = self.get_status().await?;

        status.is_initialized = true;
        status.init_timestamp = Utc::now().timestamp();
        status.init_token_count = token_count;
        status.init_pool_count = pool_count;

        self.set_status(&status).await?;

        tracing::info!(
            "Redis marked as initialized with {} tokens, {} pools",
            token_count,
            pool_count,
        );

        Ok(())
    }

    /// Reset Redis initialization status (useful for testing or re-initialization)
    pub async fn reset_status(&self) -> Result<()> {
        let mut conn = self.get_connection();
        let _: () = conn.del(INDEXER_STATUS_KEY).await?;

        tracing::info!("Indexer status reset");
        Ok(())
    }

    fn indexer_status_to_hash(status: &IndexerStatus) -> Vec<(String, RedisValue)> {
        vec![
            (FIELD_IS_INITIALIZED.to_string(), RedisValue::Bool(status.is_initialized)),
            (FIELD_INIT_TIMESTAMP.to_string(), RedisValue::Int64(status.init_timestamp)),
            (FIELD_INIT_TOKEN_COUNT.to_string(), RedisValue::UInt64(status.init_token_count)),
            (FIELD_INIT_POOL_COUNT.to_string(), RedisValue::UInt64(status.init_pool_count)),
        ]
    }

    fn hash_to_indexer_status(hash_data: &HashMap<String, String>) -> Result<IndexerStatus> {
        Ok(IndexerStatus {
            is_initialized: parse_bool_from_map(hash_data, FIELD_IS_INITIALIZED)?,
            init_timestamp: parse_i64_from_map(hash_data, FIELD_INIT_TIMESTAMP)?,
            init_token_count: parse_u64_from_map(hash_data, FIELD_INIT_TOKEN_COUNT)?,
            init_pool_count: parse_u64_from_map(hash_data, FIELD_INIT_POOL_COUNT)?,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_redis_status() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        // Reset status first
        redis_client.reset_status().await.unwrap();

        // Check initial status
        let initial_status = redis_client.get_status().await.unwrap();
        assert!(!initial_status.is_initialized);
        assert_eq!(initial_status.init_token_count, 0);
        assert_eq!(initial_status.init_pool_count, 0);

        // Mark as initialized
        let token_count = 100;
        let pool_count = 200;
        redis_client.mark_as_initialized(token_count, pool_count).await.unwrap();

        // Check updated status
        let updated_status = redis_client.get_status().await.unwrap();
        assert!(updated_status.is_initialized);
        assert!(updated_status.init_timestamp > 0);
        assert_eq!(updated_status.init_token_count, token_count);
        assert_eq!(updated_status.init_pool_count, pool_count);

        // Test is_initialized check
        let is_init = redis_client.is_initialized().await.unwrap();
        assert!(is_init);

        // Reset and verify
        redis_client.reset_status().await.unwrap();
        let is_init_after_reset = redis_client.is_initialized().await.unwrap();
        assert!(!is_init_after_reset);
    }
}
