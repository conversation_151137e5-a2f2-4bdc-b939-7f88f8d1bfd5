use std::collections::HashMap;

use anyhow::Result;
use redis;

use crate::postgres::enums::{<PERSON>, <PERSON>, DexPaid, PoolType};

pub fn parse_string_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<String> {
    hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key)).map(|s| s.clone())
}

pub fn parse_i64_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<i64> {
    hash_data
        .get(key)
        .ok_or(anyhow::anyhow!("{} not found", key))
        .and_then(|s| s.parse::<i64>().map_err(|_| anyhow::anyhow!("{} is not a valid i64", key)))
}

pub fn parse_u16_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<u16> {
    hash_data
        .get(key)
        .ok_or(anyhow::anyhow!("{} not found", key))
        .and_then(|s| s.parse::<u16>().map_err(|_| anyhow::anyhow!("{} is not a valid u16", key)))
}

pub fn parse_u64_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<u64> {
    hash_data
        .get(key)
        .ok_or(anyhow::anyhow!("{} not found", key))
        .and_then(|s| s.parse::<u64>().map_err(|_| anyhow::anyhow!("{} is not a valid u64", key)))
}

pub fn parse_u128_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<u128> {
    hash_data
        .get(key)
        .ok_or(anyhow::anyhow!("{} not found", key))
        .and_then(|s| s.parse::<u128>().map_err(|_| anyhow::anyhow!("{} is not a valid u128", key)))
}

pub fn parse_u8_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<u8> {
    parse_i64_from_map(hash_data, key).map(|i| i as u8)
}

pub fn parse_f64_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<f64> {
    hash_data
        .get(key)
        .ok_or(anyhow::anyhow!("{} not found", key))
        .and_then(|s| s.parse::<f64>().map_err(|_| anyhow::anyhow!("{} is not a valid f64", key)))
}

pub fn parse_bool_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<bool> {
    hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key)).and_then(|s| {
        if s == "1" || s == "True" || s == "true" {
            Ok(true)
        } else if s == "0" || s == "False" || s == "false" {
            Ok(false)
        } else {
            Err(anyhow::anyhow!("{} is not a valid bool", key))
        }
    })
}

pub fn parse_chain_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<Chain> {
    hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key)).and_then(|s| {
        if let Ok(chain) = s.parse::<Chain>() {
            Ok(chain)
        } else {
            Err(anyhow::anyhow!("{} is not a valid chain", key))
        }
    })
}

pub fn parse_dex_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<Dex> {
    hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key)).and_then(|s| {
        if let Ok(dex) = s.parse::<Dex>() {
            Ok(dex)
        } else {
            Err(anyhow::anyhow!("{} is not a valid dex", key))
        }
    })
}

pub fn parse_dex_paid_from_map(hash_data: &HashMap<String, String>, key: &str) -> Result<DexPaid> {
    hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key)).and_then(|s| {
        if let Ok(dex_paid) = s.parse::<DexPaid>() {
            Ok(dex_paid)
        } else {
            Err(anyhow::anyhow!("{} is not a valid dex paid", key))
        }
    })
}

pub fn parse_pool_type_from_map(
    hash_data: &HashMap<String, String>,
    key: &str,
) -> Result<PoolType> {
    hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key)).and_then(|s| {
        if let Ok(pool_type) = s.parse::<PoolType>() {
            Ok(pool_type)
        } else {
            Err(anyhow::anyhow!("{} is not a valid pool type", key))
        }
    })
}

// NOTE: when we write as values::RedisValue::String, it will be encoded as a bulk string
pub fn parse_string_from_redis_value_map(
    hash_data: &HashMap<String, redis::Value>,
    key: &str,
) -> Result<String> {
    let value = hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key))?;
    match value {
        redis::Value::BulkString(s) => {
            String::from_utf8(s.clone()).map_err(|e| anyhow::anyhow!("utf8 decode: {}", e))
        }
        _ => Err(anyhow::anyhow!("{} is not a valid string", key)),
    }
}

// NOTE: when we write as values::RedisValue::Int64, it will be encoded as a bulk string
pub fn parse_i64_from_redis_value_map(
    hash_data: &HashMap<String, redis::Value>,
    key: &str,
) -> Result<i64> {
    let value = hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key))?;
    match value {
        redis::Value::Int(i) => Ok(*i),
        redis::Value::BulkString(s) => String::from_utf8(s.clone())
            .map_err(|e| anyhow::anyhow!("utf8 decode: {}", e))
            .and_then(|s| {
                s.parse::<i64>().map_err(|_| anyhow::anyhow!("{} is not a valid i64", key))
            }),
        _ => Err(anyhow::anyhow!("{} is not a valid i64 str", key)),
    }
}

pub fn parse_bytes_from_redis_value_map(
    hash_data: &HashMap<String, redis::Value>,
    key: &str,
) -> Result<Vec<u8>> {
    let value = hash_data.get(key).ok_or(anyhow::anyhow!("{} not found", key))?;
    match value {
        redis::Value::BulkString(v) => Ok(v.clone()),
        _ => Err(anyhow::anyhow!("{} is not a valid bytes", key)),
    }
}

/// Parse RediSearch FT.SEARCH result into Vec<HashMap<String, String>>
/// This is a common utility used by both token and pool search functions
pub fn parse_redis_search_result(result: redis::Value) -> Vec<HashMap<String, String>> {
    let mut parsed = Vec::new();

    if let redis::Value::Array(arr) = result {
        if arr.len() >= 2 {
            // Skip the first element (total count) and process results
            for i in (1..arr.len()).step_by(2) {
                if let (Some(_key), Some(redis::Value::Array(fields))) =
                    (arr.get(i), arr.get(i + 1))
                {
                    let mut hash_data = HashMap::new();
                    for j in (0..fields.len()).step_by(2) {
                        if let (
                            Some(redis::Value::BulkString(field)),
                            Some(redis::Value::BulkString(value)),
                        ) = (fields.get(j), fields.get(j + 1))
                        {
                            if let (Ok(field_str), Ok(value_str)) =
                                (String::from_utf8(field.clone()), String::from_utf8(value.clone()))
                            {
                                // Remove quotes if present (for compatibility)
                                let field_clean = field_str.trim_matches('"').to_string();
                                let value_clean = value_str.trim_matches('"').to_string();
                                hash_data.insert(field_clean, value_clean);
                            }
                        }
                    }
                    if !hash_data.is_empty() {
                        parsed.push(hash_data);
                    }
                }
            }
        }
    }

    parsed
}
