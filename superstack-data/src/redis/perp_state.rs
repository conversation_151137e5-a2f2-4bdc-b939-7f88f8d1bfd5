use crate::{error::Result, postgres::indexer::perp_state::PerpBriefState};
use redis::AsyncCommands;

use super::{utils::*, value::RedisValue, RedisClient};

const PERP_KEY_PREFIX: &str = "perp:";

// Human-readable constants for short field names
const FIELD_LONG_NTL: &str = "ln";
const FIELD_SHORT_NTL: &str = "sn";
const FIELD_LONG_SZ: &str = "ls";
const FIELD_SHORT_SZ: &str = "ss";
const FIELD_LONG_TRADERS: &str = "lt";
const FIELD_SHORT_TRADERS: &str = "st";
const FIELD_LONG_ENTRY: &str = "le";
const FIELD_SHORT_ENTRY: &str = "se";
const FIELD_LONG_PNL: &str = "lp";
const FIELD_SHORT_PNL: &str = "sp";
const FIELD_LONG_LIQ_DIST: &str = "lld";
const FIELD_SHORT_LIQ_DIST: &str = "sld";
const FIELD_UPDATE_TIMESTAMP: &str = "ut";

impl RedisClient {
    fn construct_perp_state_key(perp_id: &str) -> String {
        format!("{}{}", PERP_KEY_PREFIX, perp_id)
    }

    // perp state
    fn perp_state_to_hash(perp_state: &PerpBriefState) -> Vec<(&'static str, RedisValue)> {
        vec![
            (FIELD_LONG_NTL, RedisValue::UInt128(perp_state.long_ntl)),
            (FIELD_SHORT_NTL, RedisValue::UInt128(perp_state.short_ntl)),
            (FIELD_LONG_SZ, RedisValue::Float64(perp_state.long_sz)),
            (FIELD_SHORT_SZ, RedisValue::Float64(perp_state.short_sz)),
            (FIELD_LONG_TRADERS, RedisValue::UInt64(perp_state.long_traders)),
            (FIELD_SHORT_TRADERS, RedisValue::UInt64(perp_state.short_traders)),
            (FIELD_LONG_ENTRY, RedisValue::Float64(perp_state.long_entry)),
            (FIELD_SHORT_ENTRY, RedisValue::Float64(perp_state.short_entry)),
            (FIELD_LONG_PNL, RedisValue::Float64(perp_state.long_pnl)),
            (FIELD_SHORT_PNL, RedisValue::Float64(perp_state.short_pnl)),
            (FIELD_LONG_LIQ_DIST, RedisValue::Float64(perp_state.long_liq_dist)),
            (FIELD_SHORT_LIQ_DIST, RedisValue::Float64(perp_state.short_liq_dist)),
            (FIELD_UPDATE_TIMESTAMP, RedisValue::Int64(perp_state.updated_at_millis)),
        ]
    }

    pub async fn set_perp_state(&self, perp_state: &PerpBriefState) -> Result<()> {
        let key = Self::construct_perp_state_key(&perp_state.perp_id);
        let mut conn = self.get_connection();
        let hash_data = Self::perp_state_to_hash(perp_state);

        let _: () = conn.hset_multiple(key, &hash_data).await?;

        Ok(())
    }

    pub async fn get_perp_state(&self, perp_id: &str) -> Result<Option<PerpBriefState>> {
        let key = Self::construct_perp_state_key(perp_id);
        let mut conn = self.get_connection();

        let hash_data: std::collections::HashMap<String, String> = conn.hgetall(&key).await?;

        if hash_data.is_empty() {
            return Ok(None);
        }

        let long_ntl = parse_u128_from_map(&hash_data, FIELD_LONG_NTL)?;
        let short_ntl = parse_u128_from_map(&hash_data, FIELD_SHORT_NTL)?;
        let long_sz = parse_f64_from_map(&hash_data, FIELD_LONG_SZ)?;
        let short_sz = parse_f64_from_map(&hash_data, FIELD_SHORT_SZ)?;
        let long_traders = parse_u64_from_map(&hash_data, FIELD_LONG_TRADERS)?;
        let short_traders = parse_u64_from_map(&hash_data, FIELD_SHORT_TRADERS)?;
        let long_entry = parse_f64_from_map(&hash_data, FIELD_LONG_ENTRY)?;
        let short_entry = parse_f64_from_map(&hash_data, FIELD_SHORT_ENTRY)?;
        let long_pnl = parse_f64_from_map(&hash_data, FIELD_LONG_PNL)?;
        let short_pnl = parse_f64_from_map(&hash_data, FIELD_SHORT_PNL)?;
        let long_liq_dist = parse_f64_from_map(&hash_data, FIELD_LONG_LIQ_DIST)?;
        let short_liq_dist = parse_f64_from_map(&hash_data, FIELD_SHORT_LIQ_DIST)?;
        let updated_at_millis = parse_i64_from_map(&hash_data, FIELD_UPDATE_TIMESTAMP)?;

        let perp_state = PerpBriefState {
            perp_id: perp_id.to_string(),
            long_ntl,
            short_ntl,
            long_sz,
            short_sz,
            long_traders,
            short_traders,
            long_entry,
            short_entry,
            long_pnl,
            short_pnl,
            long_liq_dist,
            short_liq_dist,
            updated_at_millis,
        };

        Ok(Some(perp_state))
    }
}
