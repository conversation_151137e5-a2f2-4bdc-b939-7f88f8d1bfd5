use redis::{NumericBehavior, RedisWrite, ToRedisArgs};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum RedisValue {
    Int8(i8),
    Int16(i16),
    Int32(i32),
    Int64(i64),
    UInt8(u8),
    UInt16(u16),
    UInt32(u32),
    UInt64(u64),
    UInt128(u128),
    <PERSON>loat32(f32),
    <PERSON>loat64(f64),
    <PERSON><PERSON>(bool),
    String(String),
    Bytes(Vec<u8>),
    // FIXME: Current array implementation is broken, need to fix it
    // Array(Vec<RedisValue>),
}

impl ToRedisArgs for RedisValue {
    fn write_redis_args<W>(&self, out: &mut W)
    where
        W: ?Sized + RedisWrite,
    {
        match &self {
            RedisValue::Int8(i) => i.write_redis_args(out),
            RedisValue::Int16(i) => i.write_redis_args(out),
            RedisValue::Int32(i) => i.write_redis_args(out),
            RedisValue::Int64(i) => i.write_redis_args(out),
            RedisValue::UInt8(i) => i.write_redis_args(out),
            RedisValue::UInt16(i) => i.write_redis_args(out),
            RedisValue::UInt32(i) => i.write_redis_args(out),
            RedisValue::UInt64(i) => i.write_redis_args(out),
            RedisValue::UInt128(i) => i.write_redis_args(out),
            RedisValue::Float32(i) => i.write_redis_args(out),
            RedisValue::Float64(i) => i.write_redis_args(out),
            RedisValue::Bool(i) => i.write_redis_args(out),
            RedisValue::String(i) => i.write_redis_args(out),
            RedisValue::Bytes(i) => i.write_redis_args(out),
            // RedisValue::Array(arr) => arr.write_redis_args(out),
        }
    }

    fn describe_numeric_behavior(&self) -> NumericBehavior {
        match &self {
            RedisValue::Int8(i) => i.describe_numeric_behavior(),
            RedisValue::Int16(i) => i.describe_numeric_behavior(),
            RedisValue::Int32(i) => i.describe_numeric_behavior(),
            RedisValue::Int64(i) => i.describe_numeric_behavior(),
            RedisValue::UInt8(i) => i.describe_numeric_behavior(),
            RedisValue::UInt16(i) => i.describe_numeric_behavior(),
            RedisValue::UInt32(i) => i.describe_numeric_behavior(),
            RedisValue::UInt64(i) => i.describe_numeric_behavior(),
            RedisValue::UInt128(i) => i.describe_numeric_behavior(),
            RedisValue::Float32(i) => i.describe_numeric_behavior(),
            RedisValue::Float64(i) => i.describe_numeric_behavior(),
            RedisValue::Bool(i) => i.describe_numeric_behavior(),
            RedisValue::String(i) => i.describe_numeric_behavior(),
            RedisValue::Bytes(i) => i.describe_numeric_behavior(),
            // RedisValue::Array(arr) => arr.describe_numeric_behavior(),
        }
    }

    fn num_of_args(&self) -> usize {
        match &self {
            RedisValue::Int8(i) => i.num_of_args(),
            RedisValue::Int16(i) => i.num_of_args(),
            RedisValue::Int32(i) => i.num_of_args(),
            RedisValue::Int64(i) => i.num_of_args(),
            RedisValue::UInt8(i) => i.num_of_args(),
            RedisValue::UInt16(i) => i.num_of_args(),
            RedisValue::UInt32(i) => i.num_of_args(),
            RedisValue::UInt64(i) => i.num_of_args(),
            RedisValue::UInt128(i) => i.num_of_args(),
            RedisValue::Float32(i) => i.num_of_args(),
            RedisValue::Float64(i) => i.num_of_args(),
            RedisValue::Bool(i) => i.num_of_args(),
            RedisValue::String(i) => i.num_of_args(),
            RedisValue::Bytes(i) => i.num_of_args(),
            // RedisValue::Array(arr) => arr.num_of_args(),
        }
    }
}
