use std::{collections::HashMap, str::FromStr};

use redis::AsyncCommands;
use sqlx::types::BigDecimal;

use super::{utils::*, value::RedisValue, *};
use crate::{
    error::Result,
    postgres::{
        enums::{Chain, Dex},
        indexer::TokenMetadata,
    },
};

const TOKEN_METADATA_KEY_PREFIX: &str = "token_metadata:";

// Human-readable constants for short field names
const FIELD_CHAIN: &str = "c";
const FIELD_TOKEN_ADDRESS: &str = "ta";

const FIELD_NAME: &str = "n";
const FIELD_SYMBOL: &str = "s";

const FIELD_DECIMALS: &str = "dc";
const FIELD_SUPPLY: &str = "sp";

const FIELD_DESCRIPTION: &str = "ds";
const FIELD_IMAGE: &str = "im";
const FIELD_WEBSITE: &str = "wb";
const FIELD_TWITTER: &str = "tw";
const FIELD_TELEGRAM: &str = "tg";
const FIELD_DEX_PAID: &str = "dp";

const FIELD_IS_TRENCH_TOKEN: &str = "it";

const FIELD_CREATE_DEX: &str = "cd";
const FIELD_CREATE_BLOCK: &str = "cb";
const FIELD_CREATE_TX_HASH: &str = "ch";
const FIELD_CREATE_BONDING_CURVE: &str = "cc";
const FIELD_CREATE_DEV: &str = "cv";
const FIELD_CREATE_TIMESTAMP: &str = "ct";

const FIELD_MIGRATION_POOL_ADDRESS: &str = "cm";
const FIELD_MIGRATION_TIMESTAMP: &str = "mt";

const FIELD_UPDATE_TIMESTAMP: &str = "ut";

const FIELD_URI: &str = "u";
const FIELD_SELLER_FEE_BASIS_POINTS: &str = "sf";
const FIELD_CREATORS: &str = "cs";
const FIELD_PRIMARY_SALE_HAPPENED: &str = "ps";

const FIELD_IS_MUTABLE: &str = "mu";
const FIELD_UPDATE_AUTHORITY: &str = "ua";
const FIELD_MINT_AUTHORITY: &str = "ma";
const FIELD_FREEZE_AUTHORITY: &str = "fa";

const FIELD_IS_ACTIVE: &str = "ia";
const FIELD_IMAGE_PATH: &str = "ip";

impl RedisClient {
    fn construct_token_metadata_key(chain: Chain, token_address: &str) -> String {
        format!("{}{}:{}", TOKEN_METADATA_KEY_PREFIX, token_address, chain.to_string())
    }

    fn token_metadata_to_hash(token_metadata: &TokenMetadata) -> Vec<(&'static str, RedisValue)> {
        let mut hash_data = vec![
            (FIELD_CHAIN, RedisValue::String(token_metadata.chain.to_string())),
            (FIELD_TOKEN_ADDRESS, RedisValue::String(token_metadata.address.clone())),
            (FIELD_NAME, RedisValue::String(token_metadata.name.clone())),
            (FIELD_SYMBOL, RedisValue::String(token_metadata.symbol.clone())),
            (FIELD_DECIMALS, RedisValue::UInt8(token_metadata.decimals)),
            (FIELD_SUPPLY, RedisValue::String(token_metadata.supply.to_string())),
            (FIELD_DEX_PAID, RedisValue::String(token_metadata.dex_paid.to_string())),
            (FIELD_IS_TRENCH_TOKEN, RedisValue::Bool(token_metadata.is_trench_token)),
            (FIELD_CREATE_DEX, RedisValue::String(token_metadata.create_dex.to_string())),
            (FIELD_CREATE_TIMESTAMP, RedisValue::Int64(token_metadata.create_timestamp_millis)),
            (
                FIELD_MIGRATION_TIMESTAMP,
                RedisValue::Int64(token_metadata.migration_timestamp_millis),
            ),
            (FIELD_UPDATE_TIMESTAMP, RedisValue::Int64(token_metadata.update_timestamp_millis)),
            (FIELD_IS_ACTIVE, RedisValue::Bool(token_metadata.is_active)),
        ];

        if let Some(description) = token_metadata.description.as_ref() {
            hash_data.push((FIELD_DESCRIPTION, RedisValue::String(description.clone())));
        }

        if let Some(image) = token_metadata.image.as_ref() {
            hash_data.push((FIELD_IMAGE, RedisValue::String(image.clone())));
        }

        if let Some(website) = token_metadata.website.as_ref() {
            hash_data.push((FIELD_WEBSITE, RedisValue::String(website.clone())));
        }

        if let Some(twitter) = token_metadata.twitter.as_ref() {
            hash_data.push((FIELD_TWITTER, RedisValue::String(twitter.clone())));
        }

        if let Some(telegram) = token_metadata.telegram.as_ref() {
            hash_data.push((FIELD_TELEGRAM, RedisValue::String(telegram.clone())));
        }

        if let Some(create_block_number) = token_metadata.create_block_number.as_ref() {
            hash_data.push((FIELD_CREATE_BLOCK, RedisValue::UInt64(*create_block_number)));
        }

        if let Some(create_tx_hash) = token_metadata.create_tx_hash.as_ref() {
            hash_data.push((FIELD_CREATE_TX_HASH, RedisValue::String(create_tx_hash.clone())));
        }

        if let Some(create_bonding_curve) = token_metadata.create_bonding_curve.as_ref() {
            hash_data.push((
                FIELD_CREATE_BONDING_CURVE,
                RedisValue::String(create_bonding_curve.clone()),
            ));
        }

        if let Some(create_dev) = token_metadata.create_dev.as_ref() {
            hash_data.push((FIELD_CREATE_DEV, RedisValue::String(create_dev.clone())));
        }

        if let Some(migration_pool_address) = token_metadata.migration_pool_address.as_ref() {
            hash_data.push((
                FIELD_MIGRATION_POOL_ADDRESS,
                RedisValue::String(migration_pool_address.clone()),
            ));
        }

        if let Some(uri) = token_metadata.uri.as_ref() {
            hash_data.push((FIELD_URI, RedisValue::String(uri.clone())));
        }

        if let Some(seller_fee_basis_points) = token_metadata.seller_fee_basis_points {
            hash_data
                .push((FIELD_SELLER_FEE_BASIS_POINTS, RedisValue::UInt16(seller_fee_basis_points)));
        }

        if let Some(creators) = token_metadata.creators.as_ref() {
            hash_data.push((FIELD_CREATORS, RedisValue::String(creators.join(","))));
        }

        if let Some(primary_sale_happened) = token_metadata.primary_sale_happened {
            hash_data.push((FIELD_PRIMARY_SALE_HAPPENED, RedisValue::Bool(primary_sale_happened)));
        }

        if let Some(is_mutable) = token_metadata.is_mutable {
            hash_data.push((FIELD_IS_MUTABLE, RedisValue::Bool(is_mutable)));
        }

        if let Some(update_authority) = token_metadata.update_authority.as_ref() {
            hash_data.push((FIELD_UPDATE_AUTHORITY, RedisValue::String(update_authority.clone())));
        }

        if let Some(mint_authority) = token_metadata.mint_authority.as_ref() {
            hash_data.push((FIELD_MINT_AUTHORITY, RedisValue::String(mint_authority.clone())));
        }

        if let Some(freeze_authority) = token_metadata.freeze_authority.as_ref() {
            hash_data.push((FIELD_FREEZE_AUTHORITY, RedisValue::String(freeze_authority.clone())));
        }

        if let Some(image_path) = token_metadata.image_path.as_ref() {
            hash_data.push((FIELD_IMAGE_PATH, RedisValue::String(image_path.clone())));
        }

        hash_data
    }

    fn hash_to_token_metadata(hash_data: &HashMap<String, String>) -> Result<TokenMetadata> {
        Ok(TokenMetadata {
            // Basic metadata
            chain: parse_chain_from_map(hash_data, FIELD_CHAIN)?,
            address: parse_string_from_map(hash_data, FIELD_TOKEN_ADDRESS)?,
            name: parse_string_from_map(hash_data, FIELD_NAME)?,
            symbol: parse_string_from_map(hash_data, FIELD_SYMBOL)?,
            decimals: parse_u8_from_map(hash_data, FIELD_DECIMALS)?,
            supply: BigDecimal::from_str(parse_string_from_map(hash_data, FIELD_SUPPLY)?.as_str())?,
            description: parse_string_from_map(hash_data, FIELD_DESCRIPTION).ok(),
            image: parse_string_from_map(hash_data, FIELD_IMAGE).ok(),
            website: parse_string_from_map(hash_data, FIELD_WEBSITE).ok(),
            twitter: parse_string_from_map(hash_data, FIELD_TWITTER).ok(),
            telegram: parse_string_from_map(hash_data, FIELD_TELEGRAM).ok(),
            dex_paid: parse_dex_paid_from_map(hash_data, FIELD_DEX_PAID)?,

            is_trench_token: parse_bool_from_map(hash_data, FIELD_IS_TRENCH_TOKEN)?,

            // Creation info
            create_dex: parse_dex_from_map(hash_data, FIELD_CREATE_DEX)?,
            create_block_number: parse_u64_from_map(hash_data, FIELD_CREATE_BLOCK).ok(),
            create_tx_hash: parse_string_from_map(hash_data, FIELD_CREATE_TX_HASH).ok(),
            create_bonding_curve: parse_string_from_map(hash_data, FIELD_CREATE_BONDING_CURVE).ok(),
            create_dev: parse_string_from_map(hash_data, FIELD_CREATE_DEV).ok(),
            create_timestamp_millis: parse_i64_from_map(hash_data, FIELD_CREATE_TIMESTAMP)?,
            migration_pool_address: parse_string_from_map(hash_data, FIELD_MIGRATION_POOL_ADDRESS)
                .ok(),
            migration_timestamp_millis: parse_i64_from_map(hash_data, FIELD_MIGRATION_TIMESTAMP)?,
            update_timestamp_millis: parse_i64_from_map(hash_data, FIELD_UPDATE_TIMESTAMP)?,
            // Solana specific info
            uri: parse_string_from_map(hash_data, FIELD_URI).ok(),
            seller_fee_basis_points: parse_u16_from_map(hash_data, FIELD_SELLER_FEE_BASIS_POINTS)
                .ok(),
            creators: parse_string_from_map(hash_data, FIELD_CREATORS)
                .ok()
                .map(|s| s.split(',').map(|s| s.to_string()).collect()),
            primary_sale_happened: parse_bool_from_map(hash_data, FIELD_PRIMARY_SALE_HAPPENED).ok(),
            is_mutable: parse_bool_from_map(hash_data, FIELD_IS_MUTABLE).ok(),
            update_authority: parse_string_from_map(hash_data, FIELD_UPDATE_AUTHORITY).ok(),
            mint_authority: parse_string_from_map(hash_data, FIELD_MINT_AUTHORITY).ok(),
            freeze_authority: parse_string_from_map(hash_data, FIELD_FREEZE_AUTHORITY).ok(),
            is_active: parse_bool_from_map(hash_data, FIELD_IS_ACTIVE).unwrap_or(true),
            image_path: parse_string_from_map(hash_data, FIELD_IMAGE_PATH).ok(),
        })
    }

    pub async fn set_token_metadata(&self, token_metadata: &TokenMetadata) -> Result<()> {
        let key = Self::construct_token_metadata_key(token_metadata.chain, &token_metadata.address);
        let mut conn = self.get_connection();

        let hash_data = Self::token_metadata_to_hash(token_metadata);

        let _: () = conn.hset_multiple(key, &hash_data).await?;

        Ok(())
    }

    pub async fn set_token_metadata_supply(
        &self,
        chain: Chain,
        token_address: &str,
        supply: String,
        update_timestamp_millis: i64,
    ) -> Result<()> {
        let key = Self::construct_token_metadata_key(chain, token_address);
        let mut conn = self.get_connection();

        // Use pipeline to batch both HSET commands
        let mut pipe = redis::pipe();
        pipe.hset(&key, FIELD_SUPPLY, supply);
        pipe.hset(&key, FIELD_UPDATE_TIMESTAMP, update_timestamp_millis);

        // Execute both commands in one round trip
        let _: () = pipe.query_async(&mut conn).await?;
        Ok(())
    }

    pub async fn set_token_metadata_migration_info(
        &self,
        chain: Chain,
        token_address: &str,
        migration_pool_address: String,
        migration_timestamp_millis: i64,
    ) -> Result<()> {
        let key = Self::construct_token_metadata_key(chain, token_address);
        let mut conn = self.get_connection();

        // Use pipeline to batch both HSET commands
        let mut pipe = redis::pipe();
        pipe.hset(&key, FIELD_MIGRATION_POOL_ADDRESS, migration_pool_address);
        pipe.hset(&key, FIELD_MIGRATION_TIMESTAMP, migration_timestamp_millis);

        // Execute both commands in one round trip
        let _: () = pipe.query_async(&mut conn).await?;
        Ok(())
    }

    pub async fn set_token_metadata_active_status(
        &self,
        chain: Chain,
        token_address: &str,
        is_active: bool,
    ) -> Result<()> {
        let key = Self::construct_token_metadata_key(chain, token_address);
        let mut conn = self.get_connection();

        let _: () = conn.hset(&key, FIELD_IS_ACTIVE, is_active).await?;

        Ok(())
    }

    pub async fn set_token_metadata_create_info(
        &self,
        chain: Chain,
        token_address: &str,
        create_dex: Dex,
        create_block_number: Option<u64>,
        create_tx_hash: Option<String>,
        create_bonding_curve: Option<String>,
        create_dev: Option<String>,
        create_timestamp_millis: i64,
    ) -> Result<()> {
        let key = Self::construct_token_metadata_key(chain, token_address);
        let mut conn = self.get_connection();

        // Use pipeline to batch both HSET commands
        let mut pipe = redis::pipe();
        pipe.hset(&key, FIELD_CREATE_DEX, create_dex.to_string());
        if let Some(create_block_number) = create_block_number {
            pipe.hset(&key, FIELD_CREATE_BLOCK, create_block_number);
        }
        if let Some(create_tx_hash) = create_tx_hash {
            pipe.hset(&key, FIELD_CREATE_TX_HASH, create_tx_hash);
        }
        if let Some(create_bonding_curve) = create_bonding_curve {
            pipe.hset(&key, FIELD_CREATE_BONDING_CURVE, create_bonding_curve);
        }
        if let Some(create_dev) = create_dev {
            pipe.hset(&key, FIELD_CREATE_DEV, create_dev);
        }
        pipe.hset(&key, FIELD_CREATE_TIMESTAMP, create_timestamp_millis);

        // Execute both commands in one round trip
        let _: () = pipe.query_async(&mut conn).await?;
        Ok(())
    }

    pub async fn set_token_metadata_misc_info(
        &self,
        chain: Chain,
        token_address: &str,
        description: Option<String>,
        image: Option<String>,
        website: Option<String>,
        twitter: Option<String>,
        telegram: Option<String>,
        image_path: Option<String>,
    ) -> Result<()> {
        if description.is_none() &&
            image.is_none() &&
            website.is_none() &&
            twitter.is_none() &&
            telegram.is_none() &&
            image_path.is_none()
        {
            return Ok(());
        }

        let key = Self::construct_token_metadata_key(chain, token_address);
        let mut conn = self.get_connection();

        // Use pipeline to batch both HSET commands
        let mut pipe = redis::pipe();
        if let Some(description) = description {
            pipe.hset(&key, FIELD_DESCRIPTION, description);
        }
        if let Some(image) = image {
            pipe.hset(&key, FIELD_IMAGE, image);
        }
        if let Some(website) = website {
            pipe.hset(&key, FIELD_WEBSITE, website);
        }
        if let Some(twitter) = twitter {
            pipe.hset(&key, FIELD_TWITTER, twitter);
        }
        if let Some(telegram) = telegram {
            pipe.hset(&key, FIELD_TELEGRAM, telegram);
        }
        if let Some(image_path) = image_path {
            pipe.hset(&key, FIELD_IMAGE_PATH, image_path);
        }

        // Execute both commands in one round trip
        let _: () = pipe.query_async(&mut conn).await?;
        Ok(())
    }

    pub async fn contains_token_metadata(&self, chain: Chain, token_address: &str) -> Result<bool> {
        let key = Self::construct_token_metadata_key(chain, token_address);
        let mut conn = self.get_connection();
        let exists: bool = conn.exists(&key).await?;
        Ok(exists)
    }

    async fn get_token_metadata(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<Option<TokenMetadata>> {
        let key = Self::construct_token_metadata_key(chain, token_address);
        let mut conn = self.get_connection();

        let hash_data: HashMap<String, String> = conn.hgetall(&key).await?;

        if hash_data.is_empty() {
            return Ok(None);
        }

        Ok(Some(Self::hash_to_token_metadata(&hash_data)?))
    }

    pub async fn get_token_metadata_or_update_from_db(
        &self,
        chain: Chain,
        address: &str,
    ) -> Result<TokenMetadata> {
        // Validate address is not empty
        if address.is_empty() {
            tracing::error!("Empty token address provided to get_token_metadata_or_fetch_from_db");
            return Err(anyhow::anyhow!("Empty token address").into());
        }

        match self.get_token_metadata(chain, address).await? {
            Some(token) => Ok(token),
            None => {
                let db = PostgresDatabase::get_indexer_db().await;
                match db.get_token_metadata(chain, address).await? {
                    Some(token) => {
                        self.set_token_metadata(&token).await?;
                        Ok(token)
                    }
                    None => {
                        tracing::warn!(
                            "Token metadata not found in db for {} on chain {:?}",
                            address,
                            chain
                        );
                        Err(anyhow::anyhow!("Token metadata not found in db for {}", address)
                            .into())
                    }
                }
            }
        }
    }

    pub async fn try_get_token_metadata_or_update_from_db(
        &self,
        chain: Chain,
        address: &str,
    ) -> Result<Option<TokenMetadata>> {
        // Validate address is not empty
        if address.is_empty() {
            tracing::error!("Empty token address provided to get_token_metadata_or_fetch_from_db");
            return Err(anyhow::anyhow!("Empty token address").into());
        }

        match self.get_token_metadata(chain, address).await? {
            Some(token) => Ok(Some(token)),
            None => {
                let db = PostgresDatabase::get_indexer_db().await;
                match db.get_token_metadata(chain, address).await? {
                    Some(token) => {
                        self.set_token_metadata(&token).await?;
                        Ok(Some(token))
                    }
                    None => Ok(None),
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::postgres::*;

    use solana_sdk::pubkey::Pubkey;

    #[tokio::test]
    async fn test_redis_token_metadata() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();
        let token_address = "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN";
        let token_metadata = TokenMetadata::construct_solana_token_metadata_from_rpc(
            Pubkey::from_str(token_address).unwrap(),
            Dex::Unknown,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            chrono::Utc::now().timestamp_millis(),
            None,
        )
        .await
        .unwrap();

        if redis_client.contains_token_metadata(Chain::Solana, token_address).await.unwrap() {
            tracing::info!("Token metadata already exists");
        }

        redis_client.set_token_metadata(&token_metadata).await.unwrap();

        let token_metadata_from_redis =
            redis_client.get_token_metadata(Chain::Solana, token_address).await.unwrap().unwrap();
        assert_eq!(token_metadata, token_metadata_from_redis);
    }
}
