use anyhow::Result;
use backon::{ExponentialBuilder, Retryable};
use mpl_token_metadata::accounts::Metadata as MetadataAccount;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::{program_pack::Pack, pubkey::Pubkey};
use spl_token::state::Mint;
use spl_token_2022::extension::{
    metadata_pointer::MetadataPointer, BaseStateWithExtensions, StateWithExtensions,
};

/// Derives the metadata PDA for a mint
pub fn get_metadata_pda(mint_pubkey: &Pubkey) -> Pubkey {
    let program_id = mpl_token_metadata::ID;
    let seeds = &[MetadataAccount::PREFIX, program_id.as_ref(), mint_pubkey.as_ref()];
    let (metadata_address, _) = Pubkey::find_program_address(seeds, &program_id);
    metadata_address
}

/// Fetches token metadata and mint info for a given mint
///
/// # Arguments
///
/// * `mint_pubkey` - The mint public key
/// * `rpc_client` - The RPC client
/// * `max_retries` - The maximum number of retries to fetch the metadata and mint info
///   - If `None`, the default is 0 (no retries)
///   - If `Some(n)`, the maximum number of retries is `n.clamp(0, 3)`
pub async fn fetch_token_metadata_and_mint_info(
    mint_pubkey: Pubkey,
    rpc_client: &RpcClient,
    max_retries: Option<u8>,
) -> Result<(MetadataAccount, Mint)> {
    let max_retries = max_retries.unwrap_or(0).clamp(0, 3);

    // Get metadata account
    let metadata_pubkey = get_metadata_pda(&mint_pubkey);

    let mut retry_count = 0;
    loop {
        if let Ok(mut accounts) =
            rpc_client.get_multiple_accounts(&[metadata_pubkey, mint_pubkey]).await
        {
            if accounts.len() == 2 {
                match (accounts[0].take(), accounts[1].take()) {
                    (Some(metadata_account), Some(mint_account)) => {
                        // Deserialize metadata
                        let mut metadata =
                            MetadataAccount::safe_deserialize(&metadata_account.data)?;
                        metadata.name = metadata.name.trim_matches(char::from(0)).to_string();
                        metadata.symbol = metadata.symbol.trim_matches(char::from(0)).to_string();
                        metadata.uri = metadata.uri.trim_matches(char::from(0)).to_string();

                        // Deserialize mint
                        let mint = Mint::unpack(mint_account.data.as_ref())?;

                        return Ok((metadata, mint));
                    }
                    (None, Some(_)) => {
                        return Err(anyhow::anyhow!(
                            "Unsupport token2022 metadata: {:?}",
                            mint_pubkey
                        ));
                    }
                    _ => {
                        return Err(anyhow::anyhow!("Metadata or mint account not found"));
                    }
                }
            } else {
                return Err(anyhow::anyhow!("Invalid number of accounts fetched"));
            }
        }

        retry_count += 1;
        if retry_count > max_retries {
            return Err(anyhow::anyhow!("Failed to fetch token metadata and mint info"));
        }
    }
}

/// Fetches token metadata for a given mint
pub async fn fetch_token_metadata(
    mint_pubkey: Pubkey,
    rpc_client: &RpcClient,
) -> Result<MetadataAccount> {
    // Get metadata account
    let metadata_pubkey = get_metadata_pda(&mint_pubkey);
    let fetch_account = || async move {
        let metadata_account = rpc_client.get_account(&metadata_pubkey).await?;
        Ok::<_, anyhow::Error>(metadata_account)
    };
    let metadata_account = fetch_account.retry(ExponentialBuilder::default()).await?;

    // Deserialize metadata
    let mut metadata = MetadataAccount::safe_deserialize(&metadata_account.data)?;
    metadata.name = metadata.name.trim_matches(char::from(0)).to_string();
    metadata.symbol = metadata.symbol.trim_matches(char::from(0)).to_string();
    metadata.uri = metadata.uri.trim_matches(char::from(0)).to_string();

    Ok(metadata)
}

pub async fn fetch_mint_info(mint_pubkey: Pubkey, rpc_client: &RpcClient) -> Result<Mint> {
    let fetch_account = || async move {
        let mint = rpc_client.get_account(&mint_pubkey).await?;
        Ok::<_, anyhow::Error>(mint)
    };
    let mint = fetch_account.retry(ExponentialBuilder::default()).await?;
    let mint = Mint::unpack(mint.data.as_ref())?;
    Ok(mint)
}

pub async fn fetch_token_metadata_from_extension(
    mint_pubkey: &Pubkey,
    rpc_client: &RpcClient,
) -> Result<MetadataAccount> {
    let fetch_account = || async move {
        let account = rpc_client.get_account(&mint_pubkey).await?;
        Ok::<_, anyhow::Error>(account)
    };
    let account = fetch_account.retry(ExponentialBuilder::default()).await?;
    let state = StateWithExtensions::<spl_token_2022::state::Mint>::unpack(&account.data)?;
    let pointer = state.get_extension::<MetadataPointer>()?;

    let fetch_metadata = || async move {
        let metadata_account = rpc_client.get_account(&pointer.metadata_address.0).await?;
        Ok::<_, anyhow::Error>(metadata_account)
    };
    let metadata_account = fetch_metadata.retry(ExponentialBuilder::default()).await?;

    let mut metadata = MetadataAccount::safe_deserialize(&metadata_account.data)?;
    metadata.name = metadata.name.trim_matches(char::from(0)).to_string();
    metadata.symbol = metadata.symbol.trim_matches(char::from(0)).to_string();
    metadata.uri = metadata.uri.trim_matches(char::from(0)).to_string();

    Ok(metadata)
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use crate::{token::solana::metaplex::fetch_metaplex_file, utils::*};

    use super::*;

    #[tokio::test]
    async fn test_fetch_token_metadata_1() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let rpc_client = get_confirmed_rpc_client();
        let mint = Pubkey::from_str("FJAuBQEKJoGeCcq4kiVCQs6CCeyS7pze44RJ3aBApump").unwrap();

        let metadata = fetch_token_metadata(mint, rpc_client).await.unwrap();

        tracing::info!("Metadata: {:?}", metadata);
        assert_eq!(metadata.name, "Trenching is for everyone");
        assert_eq!(metadata.symbol, "TRENCH");
        assert_eq!(
            metadata.uri,
            "https://ipfs.io/ipfs/QmRJXqFZCKEg4rvp8cAAbnwBLpkTH9kParoMHHQJWYrZqK"
        );

        let reqwest_client = get_reqwest_client();
        let metaplex_json = fetch_metaplex_file(&metadata.uri, reqwest_client).await.unwrap();
        tracing::info!("Metaplex JSON: {:?}", metaplex_json);

        let mint_info = fetch_mint_info(mint, rpc_client).await.unwrap();
        tracing::info!("Mint info: {:?}", mint_info);
    }

    #[tokio::test]
    async fn test_fetch_token_metadata_2() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let rpc_client = get_confirmed_rpc_client();
        let mint = Pubkey::from_str("6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN").unwrap();

        let metadata = fetch_token_metadata(mint, &rpc_client).await.unwrap();

        tracing::info!("Metadata: {:?}", metadata);

        let reqwest_client = get_reqwest_client();
        let metaplex_json = fetch_metaplex_file(&metadata.uri, &reqwest_client).await.unwrap();
        tracing::info!("Metaplex JSON: {:?}", metaplex_json);

        let mint_info = fetch_mint_info(mint, &rpc_client).await.unwrap();
        tracing::info!("Mint info: {:?}", mint_info);

        // SOL
        let mint = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let metadata = fetch_token_metadata(mint, &rpc_client).await.unwrap();
        tracing::info!("Metadata: {:?}", metadata);
    }

    #[tokio::test]
    async fn test_fetch_token_metadata_3() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let rpc_client = get_confirmed_rpc_client();
        let mint = Pubkey::from_str("Grass7B4RdKfBCjTKgSqnXkqjwiGvQyFbuSCUJr3XXjs").unwrap();

        let metadata = fetch_token_metadata(mint, &rpc_client).await.unwrap();

        tracing::info!("Metadata: {:?}", metadata);

        let reqwest_client = get_reqwest_client();
        let metaplex_json = fetch_metaplex_file(&metadata.uri, &reqwest_client).await.unwrap();
        tracing::info!("Metaplex JSON: {:?}", metaplex_json);

        let mint_info = fetch_mint_info(mint, &rpc_client).await.unwrap();
        tracing::info!("Mint info: {:?}", mint_info);
    }

    #[tokio::test]
    #[ignore]
    // TODO: Support token2022 metadata
    async fn test_fetch_token2022_metadata() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let rpc_client = get_confirmed_rpc_client();
        let mint = Pubkey::from_str("Ey59PH7Z4BFU4HjyKnyMdWt5GGN76KazTAwQihoUXRnk").unwrap();

        let metadata = fetch_token_metadata_from_extension(&mint, &rpc_client).await.unwrap();
        tracing::info!("Metadata: {:?}", metadata);
    }

    #[tokio::test]
    async fn test_fetch_token_metadata_and_mint_info() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let rpc_client = get_confirmed_rpc_client();
        let mint = Pubkey::from_str("FJAuBQEKJoGeCcq4kiVCQs6CCeyS7pze44RJ3aBApump").unwrap();

        let (metadata, mint) =
            fetch_token_metadata_and_mint_info(mint, &rpc_client, None).await.unwrap();
        tracing::info!("Metadata: {:?}", metadata);
        tracing::info!("Mint: {:?}", mint);
    }

    #[tokio::test]
    async fn test_fetch_token_metadata_and_mint_info_for_token2022() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let rpc_client = get_confirmed_rpc_client();
        let mint = Pubkey::from_str("Ey59PH7Z4BFU4HjyKnyMdWt5GGN76KazTAwQihoUXRnk").unwrap();

        let (metadata, mint) =
            fetch_token_metadata_and_mint_info(mint, &rpc_client, None).await.unwrap();
        tracing::info!("Metadata: {:?}", metadata);
        tracing::info!("Mint: {:?}", mint);
    }
}
