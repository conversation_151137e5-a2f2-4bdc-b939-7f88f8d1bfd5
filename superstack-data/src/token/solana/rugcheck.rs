use anyhow::Result;
use chrono::DateTime;
use reqwest::{header, Client};
use serde::Deserialize;

use crate::constant::USER_AGENT_HEADER_VALUE;

pub const RUG_CHECK_BASE_URL: &str = "https://api.rugcheck.xyz/v1/tokens";

pub const RUG_CHECK_ORIGIN: &str = "https://rugcheck.xyz";

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Token {
    pub mint_authority: Option<String>,
    pub supply: Option<u64>,
    pub decimals: Option<u8>,
    pub is_initialized: Option<bool>,
    pub freeze_authority: Option<String>,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenMeta {
    pub name: Option<String>,
    pub symbol: Option<String>,
    pub uri: Option<String>,
    pub mutable: Option<bool>,
    pub update_authority: Option<String>,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct FileMeta {
    pub description: Option<String>,
    pub name: Option<String>,
    pub symbol: Option<String>,
    pub image: Option<String>,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ReportResponse {
    pub mint: Option<String>,
    pub creator: Option<String>,
    pub token: Option<Token>,
    pub token_meta: Option<TokenMeta>,
    pub file_meta: Option<FileMeta>,
    pub detected_at: Option<String>, // "2024-09-26T14:09:54.789332957Z",
}

impl ReportResponse {
    pub fn get_image_url(&self) -> Option<String> {
        self.file_meta
            .as_ref()
            .and_then(|meta| meta.image.clone())
            .filter(|image_url| !image_url.is_empty())
    }

    pub fn get_supply(&self) -> Option<u64> {
        self.token.as_ref().and_then(|token| token.supply)
    }

    pub fn get_creator(&self) -> Option<String> {
        self.creator.clone()
    }

    pub fn get_create_timestamp(&self) -> Option<i64> {
        self.detected_at.as_ref().and_then(|date_str| {
            DateTime::parse_from_rfc3339(date_str).ok().map(|dt| dt.timestamp())
        })
    }
}

pub struct RugCheckProvider {}

impl RugCheckProvider {
    pub async fn fetch_report(mint: &str, client: &Client) -> Result<ReportResponse> {
        let url = format!("{RUG_CHECK_BASE_URL}/{mint}/report");
        let response = client
            .get(url)
            .header(header::USER_AGENT, USER_AGENT_HEADER_VALUE)
            .header(header::ORIGIN, RUG_CHECK_ORIGIN)
            .header(header::CONTENT_TYPE, "application/json")
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let body = response.text().await.unwrap_or("Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Failed to fetch rugcheck report for token: {mint}, status: {status}, body: {body}"
            ));
        }

        let report: ReportResponse = response.json().await?;
        Ok(report)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_rugcheck_report() {
        crate::utils::setup_tracing();

        let client = crate::utils::get_reqwest_client();
        let report =
            RugCheckProvider::fetch_report("3JgFwoYV74f6LwWjQWnr3YDPFnmBdwQfNyubv99jqUoq", &client)
                .await
                .unwrap();
        tracing::info!("{:?}", report);
        tracing::info!("image_url: {}", report.get_image_url().unwrap());
        tracing::info!("supply: {}", report.get_supply().unwrap());
        tracing::info!("creator: {:?}", report.get_creator());
        tracing::info!("create_timestamp: {}", report.get_create_timestamp().unwrap());

        let report =
            RugCheckProvider::fetch_report("3NZ9JMVBmGAqocybic2c7LQCJScmgsAZ6vQqTDzcqmJh", &client)
                .await
                .unwrap();
        tracing::info!("{:?}", report);
        tracing::info!("image_url: {:?}", report.get_image_url());
        tracing::info!("supply: {}", report.get_supply().unwrap());
        tracing::info!("creator: {:?}", report.get_creator());
        tracing::info!("create_timestamp: {}", report.get_create_timestamp().unwrap());
    }
}
