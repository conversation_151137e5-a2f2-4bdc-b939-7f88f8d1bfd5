use std::collections::HashMap;

use anyhow::Result;
use chrono::DateTime;
use reqwest::{header, Client, StatusCode};
use serde::Deserialize;

use crate::constant::{solana::SOL_MINT, USER_AGENT_HEADER_VALUE};

pub const JUPITER_TOKEN_BASE_URL: &str = "https://lite-api.jup.ag/tokens/v1";
pub const JUPITER_PRICE_BASE_URL: &str = "https://lite-api.jup.ag/price/v2";

#[derive(Clone, Debug, Deserialize)]
pub struct TokenInfo {
    pub address: Option<String>,
    pub name: Option<String>,
    pub symbol: Option<String>,
    pub decimals: Option<u8>,
    #[serde(rename = "logoURI")]
    pub logo_uri: Option<String>,
    pub created_at: Option<String>, // 2024-04-26T10:56:58.893768Z
    pub minted_at: Option<String>,
    pub freeze_authority: Option<String>,
    pub mint_authority: Option<String>,
}

impl TokenInfo {
    pub fn get_image_url(&self) -> Option<String> {
        self.logo_uri.clone().filter(|image_url| !image_url.is_empty())
    }

    pub fn get_create_timestamp(&self) -> Option<i64> {
        self.created_at
            .as_ref()
            .and_then(|created_at| DateTime::parse_from_rfc3339(created_at).ok())
            .map(|date_time| date_time.timestamp())
    }
}

#[derive(Debug, Clone, Deserialize)]
pub struct Price {
    pub id: String,
    pub price: f64,
}

#[derive(Debug, Clone, Deserialize)]
pub struct SinglePriceResponse {
    pub id: String,
    pub price: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct PriceResponse {
    pub data: HashMap<String, Option<SinglePriceResponse>>,
}

pub struct JupiterProvider {}

impl JupiterProvider {
    pub async fn fetch_token_info(mint: &str, client: &Client) -> Result<Option<TokenInfo>> {
        let url = format!("{JUPITER_TOKEN_BASE_URL}/token/{mint}");
        let response = client
            .get(url)
            .header(header::USER_AGENT, USER_AGENT_HEADER_VALUE)
            .header(header::ACCEPT, "application/json")
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            if status == StatusCode::TOO_MANY_REQUESTS {
                return Ok(None);
            }
            let body = response.text().await.unwrap_or("Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Failed to fetch jupiter token info for token: {mint}, status: {status}, body: {body}"
            ));
        }

        let token_info: TokenInfo = response.json().await?;
        Ok(Some(token_info))
    }

    pub async fn get_prices(ids: &[&str], client: &Client) -> Result<Vec<Price>> {
        if ids.is_empty() {
            return Err(anyhow::anyhow!("No ids provided"));
        }

        let url = format!("{}?ids={}", JUPITER_PRICE_BASE_URL, ids.join(","));
        let response =
            client.get(url).header(reqwest::header::ACCEPT, "application/json").send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to get price with status: {}, body: {}",
                response.status(),
                response.text().await.unwrap_or("Unknown body".to_string())
            ));
        }

        let price_response: PriceResponse = response.json().await?;

        let mut prices = Vec::new();
        for (id, price) in price_response.data {
            if let Some(price) = price {
                prices.push(Price { id: id.to_string(), price: price.price.parse::<f64>()? });
            } else {
                tracing::warn!("No price found for token: {}", id);
            }
        }

        Ok(prices)
    }

    pub async fn get_sol_price() -> Result<f64> {
        let url = format!("{}?ids={}", JUPITER_PRICE_BASE_URL, SOL_MINT);
        let client = crate::utils::get_reqwest_client();
        let response =
            client.get(url).header(reqwest::header::ACCEPT, "application/json").send().await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "Failed to get price with status: {}, body: {}",
                response.status(),
                response.text().await.unwrap_or("Unknown body".to_string())
            ));
        }

        let price_response: PriceResponse = response.json().await?;

        let price =
            price_response.data.get(SOL_MINT).ok_or(anyhow::anyhow!("No SOL price found"))?;
        let price =
            price.as_ref().ok_or(anyhow::anyhow!("No SOL price found"))?.price.parse::<f64>()?;

        Ok(price)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::constant::solana::{USDC_MINT, USDT_MINT};

    #[tokio::test]
    async fn test_jupiter_token_info() {
        crate::utils::setup_tracing();

        let client = crate::utils::get_reqwest_client();

        let token_info = JupiterProvider::fetch_token_info(
            "3NZ9JMVBmGAqocybic2c7LQCJScmgsAZ6vQqTDzcqmJh",
            &client,
        )
        .await
        .unwrap()
        .unwrap();
        tracing::info!("{:?}", token_info);
        tracing::info!("image_url: {:?}", token_info.get_image_url());
        tracing::info!("create_timestamp: {:?}", token_info.get_create_timestamp());

        let token_info =
            JupiterProvider::fetch_token_info(USDC_MINT, &client).await.unwrap().unwrap();
        tracing::info!("{:?}", token_info);
        tracing::info!("image_url: {:?}", token_info.get_image_url());
        tracing::info!("create_timestamp: {:?}", token_info.get_create_timestamp());

        let token_info =
            JupiterProvider::fetch_token_info(USDT_MINT, &client).await.unwrap().unwrap();
        tracing::info!("{:?}", token_info);
        tracing::info!("image_url: {:?}", token_info.get_image_url());
        tracing::info!("create_timestamp: {:?}", token_info.get_create_timestamp());
    }

    #[tokio::test]
    async fn test_jupiter_get_sol_price() {
        crate::utils::setup_tracing();

        let price = JupiterProvider::get_sol_price().await.unwrap();
        tracing::info!("sol price: {}", price);
    }
}
