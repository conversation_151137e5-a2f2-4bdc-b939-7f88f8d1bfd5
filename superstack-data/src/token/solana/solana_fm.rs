use anyhow::Result;
use reqwest::{header, Client};
use serde::Deserialize;

use crate::constant::USER_AGENT_HEADER_VALUE;

pub const SOLANA_FM_TOKEN_INFO_URL: &str = "https://api.solana.fm/v0/tokens";

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenData {
    pub mint: Option<String>,
    pub token_name: Option<String>,
    pub symbol: Option<String>,
    pub decimals: Option<u8>,
    pub description: Option<String>,
    pub logo: Option<String>,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenResult {
    pub token_hash: Option<String>,
    pub data: Option<TokenData>,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenResponse {
    pub status: Option<String>,
    pub message: Option<String>,
    pub result: Option<TokenResult>,
}

impl TokenResponse {
    pub fn get_image_url(&self) -> Option<String> {
        self.result
            .as_ref()
            .and_then(|result| result.data.as_ref().and_then(|data| data.logo.clone()))
            .filter(|image_url| !image_url.is_empty())
    }
}

pub struct SolanaFmProvider {}

impl SolanaFmProvider {
    pub async fn fetch_token_info(mint: &str, client: &Client) -> Result<TokenResponse> {
        let url = format!("{SOLANA_FM_TOKEN_INFO_URL}/{mint}");
        let response = client
            .get(url)
            .header(header::USER_AGENT, USER_AGENT_HEADER_VALUE)
            .header(header::ACCEPT, "application/json")
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let body = response.text().await.unwrap_or("Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Failed to fetch solana.fm token info for token: {mint}, status: {status}, body: {body}"
            ));
        }

        let token_info: TokenResponse = response.json().await?;
        Ok(token_info)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::constant::solana::{USDC_MINT, USDT_MINT};

    #[tokio::test]
    async fn test_solana_fm_token_info() {
        crate::utils::setup_tracing();

        let client = crate::utils::get_reqwest_client();

        let token_info = SolanaFmProvider::fetch_token_info(
            "3NZ9JMVBmGAqocybic2c7LQCJScmgsAZ6vQqTDzcqmJh",
            &client,
        )
        .await
        .unwrap();
        tracing::info!("{:?}", token_info);
        tracing::info!("image_url: {:?}", token_info.get_image_url());

        let token_info = SolanaFmProvider::fetch_token_info(USDC_MINT, &client).await.unwrap();
        tracing::info!("{:?}", token_info);
        tracing::info!("image_url: {:?}", token_info.get_image_url());

        let token_info = SolanaFmProvider::fetch_token_info(USDT_MINT, &client).await.unwrap();
        tracing::info!("{:?}", token_info);
        tracing::info!("image_url: {:?}", token_info.get_image_url());
    }
}
