use anyhow::Result;
use reqwest::{header, Client};
use serde::{Deserialize, Serialize};

use crate::constant::USER_AGENT_HEADER_VALUE;

#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, Default)]
#[serde(rename_all = "camelCase")]
pub struct MetaplexJson {
    pub name: Option<String>,
    pub symbol: Option<String>,
    pub description: Option<String>,
    pub image: Option<String>,

    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub website: Option<String>,
}

pub async fn fetch_metaplex_file(url: &str, reqwest_client: &Client) -> Result<MetaplexJson> {
    if url.is_empty() {
        anyhow::bail!("URL is empty");
    }

    let fetch_metaplex = || async move {
        let response = reqwest_client
            .get(url)
            .header(header::USER_AGENT, USER_AGENT_HEADER_VALUE)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let text = response.text().await.unwrap_or_else(|_| "Unknown response".to_string());
            return Err(anyhow::anyhow!(
                "Failed to fetch metaplex file for {}: HTTP {} {:.50}",
                url,
                status,
                text
            ));
        }

        let metaplex_json = response.json::<MetaplexJson>().await?;
        Ok::<_, anyhow::Error>(metaplex_json)
    };

    let metaplex_json = fetch_metaplex().await?;
    Ok(metaplex_json)
}
