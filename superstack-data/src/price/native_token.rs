use std::{cmp::Ordering, collections::VecDeque, sync::Arc, time::Duration};

use backon::{ExponentialBuilder, Retryable};
use chrono::Utc;
use tokio::sync::{OnceCell, RwLock};

use super::CoinGeckoProvider;
use crate::{
    config::Config,
    error::Result,
    postgres::{indexer::NativeTokenPrice, Chain, PostgresDatabase},
    token::solana::jupiter::JupiterProvider,
};

pub const ONE_DAY_SECONDS: i64 = 60 * 60 * 24;
pub const PRICE_HISTORY_TIME_RANGE_SECONDS: i64 = ONE_DAY_SECONDS * 60; // 60 days
pub const PRICE_HISTORY_WINDOW_SIZE: i64 = PRICE_HISTORY_TIME_RANGE_SECONDS / 5; // 60 days, 5 seconds resolution

#[derive(Debug, Clone)]
pub struct TokenPrice {
    pub timestamp: i64,
    pub price: f64,
}

impl TokenPrice {
    fn timestamp_5s(&self) -> i64 {
        self.timestamp - (self.timestamp % 5)
    }
}

impl PartialEq for TokenPrice {
    fn eq(&self, other: &Self) -> bool {
        self.timestamp_5s() == other.timestamp_5s()
    }
}

impl Eq for TokenPrice {}

impl PartialOrd for TokenPrice {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for TokenPrice {
    fn cmp(&self, other: &Self) -> Ordering {
        self.timestamp_5s().cmp(&other.timestamp_5s())
    }
}

#[derive(Debug, Clone)]
pub struct PriceHistory {
    pub chain: Chain,
    pub prices: Arc<RwLock<VecDeque<TokenPrice>>>,
}

impl PriceHistory {
    pub fn max_capacity() -> usize {
        PRICE_HISTORY_WINDOW_SIZE as usize
    }

    pub fn new(chain: Chain, sorted_prices: Vec<TokenPrice>) -> Self {
        let vec_deque = VecDeque::from(sorted_prices);
        Self { chain, prices: Arc::new(RwLock::new(vec_deque)) }
    }

    pub async fn insert(&self, price: TokenPrice) {
        // If we're at capacity, remove the oldest price
        let mut prices = self.prices.write().await;
        while prices.len() >= Self::max_capacity() {
            prices.pop_front();
        }

        // Find the insertion point using binary search
        match prices.binary_search(&price) {
            Ok(pos) => {
                prices[pos] = price;
            }
            Err(pos) => {
                prices.insert(pos, price);
            }
        }
    }

    pub async fn get_nearest_price(&self, timestamp: i64) -> Option<TokenPrice> {
        let prices = self.prices.read().await;
        if prices.is_empty() {
            return None;
        }

        let target = timestamp - (timestamp % 5);
        match prices.binary_search_by_key(&target, |p| p.timestamp_5s()) {
            Ok(pos) => Some(prices[pos].clone()),
            Err(pos) => {
                let before = if pos > 0 { Some(&prices[pos - 1]) } else { None };
                let after = if pos < prices.len() { Some(&prices[pos]) } else { None };

                match (before, after) {
                    (Some(b), Some(a)) => {
                        let dist_b = (b.timestamp_5s() - target).abs();
                        let dist_a = (a.timestamp_5s() - target).abs();
                        if dist_b <= dist_a {
                            Some(b.clone())
                        } else {
                            Some(a.clone())
                        }
                    }
                    (Some(b), None) => Some(b.clone()),
                    (None, Some(a)) => Some(a.clone()),
                    (None, None) => None,
                }
            }
        }
    }

    pub async fn get_latest_price(&self) -> Option<TokenPrice> {
        let prices = self.prices.read().await;
        if let Some(price) = prices.back() {
            Some(price.clone())
        } else {
            None
        }
    }
}

#[derive(Debug, Clone)]
pub struct NativeTokenPriceManager {
    pub sol: PriceHistory,
    pub hype: PriceHistory,
    pub store_price_history: bool,
}

impl NativeTokenPriceManager {
    pub async fn init_hype_price_history() -> Result<PriceHistory> {
        let store_price_history = Config::get().store_price_history;

        let end_time = Utc::now().timestamp();
        let start_time = end_time - PRICE_HISTORY_TIME_RANGE_SECONDS; // 60 days ago

        let db = PostgresDatabase::get_indexer_db().await;
        let prices = db.get_native_token_prices(Chain::HyperEvm, start_time, end_time).await?;
        let mut sorted_prices = prices
            .into_iter()
            .map(|p| TokenPrice { timestamp: p.timestamp_seconds, price: p.usd_price })
            .collect::<Vec<_>>();

        if sorted_prices.is_empty() || sorted_prices[0].timestamp > start_time + ONE_DAY_SECONDS {
            let ft = || async move {
                let coin_gecko_provider = CoinGeckoProvider::get();
                let hype_price_history =
                    coin_gecko_provider.get_historical_chart_data_for_hype(60).await?;
                Ok(hype_price_history)
            };
            match ft.retry(ExponentialBuilder::default()).await {
                Ok(hype_price_history) => {
                    let mut native_token_prices = vec![];
                    for (timestamp_millis, price) in hype_price_history.prices {
                        let timestamp_seconds = timestamp_millis / 1000;
                        sorted_prices.push(TokenPrice { timestamp: timestamp_seconds, price });

                        let native_token_price =
                            NativeTokenPrice::new_hype_native_token_price(timestamp_seconds, price);
                        native_token_prices.push(native_token_price);
                    }
                    if store_price_history {
                        db.insert_native_token_prices(&native_token_prices).await?;
                    }

                    sorted_prices.sort_by_key(|p| p.timestamp);
                    tracing::info!("fetched hype prices from coin gecko: {}", sorted_prices.len());
                }
                Err(e) => {
                    tracing::error!("Error fetching Hype price history: {:?}", e);
                    return Err(e);
                }
            }
        }

        let hype_price_history = PriceHistory::new(Chain::HyperEvm, sorted_prices);
        Ok(hype_price_history)
    }

    pub async fn init() -> Result<Self> {
        let store_price_history = Config::get().store_price_history;

        let end_time = Utc::now().timestamp();
        let start_time = end_time - PRICE_HISTORY_TIME_RANGE_SECONDS; // 60 days ago

        let db = PostgresDatabase::get_indexer_db().await;
        let mut prices = db.get_native_token_prices(Chain::Solana, start_time, end_time).await?;
        tracing::info!("fetched solana prices from db: {}", prices.len());
        prices.sort_by_key(|p| p.timestamp_seconds);
        let mut sorted_prices = prices
            .into_iter()
            .map(|p| TokenPrice { timestamp: p.timestamp_seconds, price: p.usd_price })
            .collect::<Vec<_>>();

        if sorted_prices.is_empty() || sorted_prices[0].timestamp > start_time + ONE_DAY_SECONDS {
            let ft = || async move {
                let coin_gecko_provider = CoinGeckoProvider::get();
                let sol_price_history =
                    coin_gecko_provider.get_historical_chart_data_for_solana(60).await?;
                Ok(sol_price_history)
            };
            match ft.retry(ExponentialBuilder::default()).await {
                Ok(sol_price_history) => {
                    let mut native_token_prices = vec![];
                    for (timestamp_millis, price) in sol_price_history.prices {
                        let timestamp_seconds = timestamp_millis / 1000;
                        sorted_prices.push(TokenPrice { timestamp: timestamp_seconds, price });

                        let native_token_price = NativeTokenPrice::new_solana_native_token_price(
                            timestamp_seconds,
                            price,
                        );
                        native_token_prices.push(native_token_price);
                    }
                    if store_price_history {
                        db.insert_native_token_prices(&native_token_prices).await?;
                    }

                    sorted_prices.sort_by_key(|p| p.timestamp);
                    tracing::info!(
                        "fetched solana prices from coin gecko: {}",
                        sorted_prices.len()
                    );
                }
                Err(e) => {
                    tracing::error!("Error fetching Solana price history: {:?}", e);
                    return Err(e);
                }
            }
        }

        let sol_price_history = PriceHistory::new(Chain::Solana, sorted_prices);
        let hype_price_history = Self::run_hype_price_updater().await?;

        let ft = || async move {
            let sol_price = JupiterProvider::get_sol_price().await?;
            Ok::<_, anyhow::Error>(sol_price)
        };
        match ft.retry(ExponentialBuilder::default()).await {
            Ok(sol_price) => {
                let now = Utc::now().timestamp();
                sol_price_history.insert(TokenPrice { timestamp: now, price: sol_price }).await;
                tracing::info!("fetched latest solana price from jupiter: {}", sol_price);
            }
            Err(e) => {
                tracing::error!("Error fetching Solana price from jupiter: {:?}, ignoring", e);
            }
        };

        let sol_price_clone = sol_price_history.clone();
        tokio::spawn(async move {
            let db = PostgresDatabase::get_indexer_db().await;
            let mut interval = tokio::time::interval(Duration::from_secs(5));
            loop {
                interval.tick().await;
                if let Ok(price) = JupiterProvider::get_sol_price().await {
                    let now = Utc::now().timestamp();
                    sol_price_clone.insert(TokenPrice { timestamp: now, price }).await;
                    let native_token_price =
                        NativeTokenPrice::new_solana_native_token_price(now, price);
                    if store_price_history {
                        if let Err(e) = db.insert_native_token_price(&native_token_price).await {
                            tracing::error!("Error inserting solana price into db: {:?}", e);
                        }
                    }
                    tracing::info!(
                        "fetched latest solana price from jupiter: {} at {}",
                        price,
                        now
                    );
                } else {
                    tokio::time::sleep(Duration::from_secs(15)).await;
                }
            }
        });

        let manager =
            Self { sol: sol_price_history, hype: hype_price_history, store_price_history };
        Ok(manager)
    }

    pub async fn run_hype_price_updater() -> Result<PriceHistory> {
        let hype_price_history = Self::init_hype_price_history().await?;
        let store_price_history = Config::get().store_price_history;

        let hype_price_history_clone = hype_price_history.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(5));
            let db = PostgresDatabase::get_indexer_db().await;
            loop {
                interval.tick().await;
                if let Ok(Some(pool_state)) =
                    db.get_latest_pool_state(Chain::Hypercore, "@107").await
                {
                    let price = pool_state.price;
                    let now = Utc::now().timestamp();
                    hype_price_history_clone.insert(TokenPrice { timestamp: now, price }).await;
                    let native_token_price =
                        NativeTokenPrice::new_hype_native_token_price(now, price);
                    if store_price_history {
                        if let Err(e) = db.insert_native_token_price(&native_token_price).await {
                            tracing::error!("Error inserting hype price into db: {:?}", e);
                        }
                    }
                    tracing::info!("fetched latest hype price from db: {}", price);
                } else {
                    tokio::time::sleep(Duration::from_secs(15)).await;
                }
            }
        });

        Ok(hype_price_history)
    }

    pub async fn get() -> &'static Self {
        static INSTANCE: OnceCell<NativeTokenPriceManager> = OnceCell::const_new();
        INSTANCE
            .get_or_init(|| async {
                NativeTokenPriceManager::init()
                    .await
                    .expect("Failed to initialize native token price manager")
            })
            .await
    }

    pub async fn get_latest_price(&self, chain: Chain) -> Option<f64> {
        match chain {
            Chain::Solana => {
                let price = self.sol.get_latest_price().await?;
                Some(price.price)
            }
            Chain::Hypercore => Some(1.0),
            Chain::HyperEvm => {
                let price = self.hype.get_latest_price().await?;
                Some(price.price)
            }
        }
    }

    pub async fn get_nearest_price(&self, chain: Chain, timestamp_seconds: i64) -> Option<f64> {
        match chain {
            Chain::Solana => {
                let price = self.sol.get_nearest_price(timestamp_seconds).await?;
                Some(price.price)
            }
            Chain::Hypercore => Some(1.0),
            Chain::HyperEvm => {
                let price = self.hype.get_nearest_price(timestamp_seconds).await?;
                Some(price.price)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_token_price_ordering() {
        // Test equal timestamps (within 5s window)
        let p1 = TokenPrice { timestamp: 1000, price: 1.0 };
        let p2 = TokenPrice { timestamp: 1003, price: 2.0 };
        assert_eq!(p1.cmp(&p2), Ordering::Equal);
        assert_eq!(p2.cmp(&p1), Ordering::Equal);

        // Test different timestamps (different 5s windows)
        let p3 = TokenPrice { timestamp: 1006, price: 3.0 };
        assert_eq!(p1.cmp(&p3), Ordering::Less);
        assert_eq!(p3.cmp(&p1), Ordering::Greater);

        // Test sorting
        let mut prices = vec![
            TokenPrice { timestamp: 1006, price: 3.0 },
            TokenPrice { timestamp: 1000, price: 1.0 },
            TokenPrice { timestamp: 1003, price: 2.0 },
        ];
        prices.sort();

        // Prices in same 5s window should be considered equal
        assert_eq!(prices[0].timestamp_5s(), prices[1].timestamp_5s());
        assert_eq!(prices[0].timestamp_5s(), 1000);
        assert_eq!(prices[2].timestamp_5s(), 1005);
    }

    #[tokio::test]
    async fn test_price_history() {
        crate::utils::setup_tracing();

        let price_history = PriceHistory::new(Chain::Solana, vec![]);
        price_history.insert(TokenPrice { timestamp: 1000, price: 1.0 }).await;
        price_history.insert(TokenPrice { timestamp: 1003, price: 2.0 }).await;
        price_history.insert(TokenPrice { timestamp: 1018, price: 8.1 }).await;
        price_history.insert(TokenPrice { timestamp: 1019, price: 9.1 }).await;
        price_history.insert(TokenPrice { timestamp: 1006, price: 3.0 }).await;
        price_history.insert(TokenPrice { timestamp: 1009, price: 4.0 }).await;
        price_history.insert(TokenPrice { timestamp: 1012, price: 5.0 }).await;
        price_history.insert(TokenPrice { timestamp: 1015, price: 6.0 }).await;
        price_history.insert(TokenPrice { timestamp: 1018, price: 7.0 }).await;
        price_history.insert(TokenPrice { timestamp: 1021, price: 8.0 }).await;
        price_history.insert(TokenPrice { timestamp: 1024, price: 9.0 }).await;
        tracing::info!("price_history: {:?}", price_history);
        assert_eq!(price_history.get_nearest_price(1).await.unwrap().price, 2.0);
        assert_eq!(price_history.get_nearest_price(1001).await.unwrap().price, 2.0);
        assert_eq!(price_history.get_nearest_price(1004).await.unwrap().price, 2.0);
        assert_eq!(price_history.get_nearest_price(1007).await.unwrap().price, 4.0);
        assert_eq!(price_history.get_nearest_price(1009).await.unwrap().price, 4.0);
        assert_eq!(price_history.get_nearest_price(1012).await.unwrap().price, 5.0);
        assert_eq!(price_history.get_nearest_price(1013).await.unwrap().price, 5.0);
        assert_eq!(price_history.get_nearest_price(1015).await.unwrap().price, 7.0);
        assert_eq!(price_history.get_nearest_price(1018).await.unwrap().price, 7.0);
        assert_eq!(price_history.get_nearest_price(1021).await.unwrap().price, 9.0);
        assert_eq!(price_history.get_nearest_price(1029).await.unwrap().price, 9.0);
        assert_eq!(price_history.get_nearest_price(10290).await.unwrap().price, 9.0);
    }

    #[tokio::test]
    async fn test_native_token_price_manager() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let manager = NativeTokenPriceManager::init().await.unwrap();
        let latest_price = manager.get_latest_price(Chain::Solana).await.unwrap();
        let nearest_price = manager.get_nearest_price(Chain::Solana, 100000).await.unwrap();
        tracing::info!("latest_price: {}", latest_price);
        tracing::info!("nearest_price: {}", nearest_price);
    }
}
