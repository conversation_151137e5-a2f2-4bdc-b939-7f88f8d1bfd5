use std::{collections::HashMap, sync::OnceLock};

use reqwest::header;
use serde::{Deserialize, Serialize};
use thiserror::Error;

use crate::{config::Config, utils::get_reqwest_client};

pub const COINGECKO_PUBLIC_BASE_URL: &str = "https://api.coingecko.com/api/v3";
pub const AUTH_HEADER_KEY: &str = "x-cg-demo-api-key";
pub const SOLANA_COIN_ID: &str = "solana";
pub const HYPE_COIN_ID: &str = "hyperliquid";
pub const VS_CURRENCY: &str = "usd";

#[derive(Error, Debug)]
pub enum CoinGeckoError {
    #[error("Reqwest error: {0}")]
    ReqwestError(#[from] reqwest::Error),

    #[error("Rate limit exceeded")]
    RateLimit,

    #[error("Invalid API key")]
    InvalidApiKey,

    #[error("Unauthorized")]
    Unauthorized,

    #[error("Authentication error")]
    AuthenticationFailed,

    #[error("Other error: {0}")]
    Other(String),
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MarketChartResponse {
    pub prices: Vec<(i64, f64)>,        // [timestamp, price]
    pub market_caps: Vec<(i64, f64)>,   // [timestamp, market_cap]
    pub total_volumes: Vec<(i64, f64)>, // [timestamp, total_volume]
}

pub struct CoinGeckoProvider {
    base_url: String,
    api_key: String,
}

impl CoinGeckoProvider {
    pub fn new(base_url: String, api_key: String) -> Self {
        Self { base_url, api_key }
    }

    pub fn get() -> &'static CoinGeckoProvider {
        static INSTANCE: OnceLock<CoinGeckoProvider> = OnceLock::new();
        INSTANCE.get_or_init(|| {
            let config = Config::get();
            CoinGeckoProvider::new(
                COINGECKO_PUBLIC_BASE_URL.to_string(),
                config.coingecko_api_key.clone(),
            )
        })
    }

    pub async fn get_historical_chart_data_for_solana(
        &self,
        days: u32,
    ) -> Result<MarketChartResponse, CoinGeckoError> {
        let response = self.get_historical_chart_data(SOLANA_COIN_ID, VS_CURRENCY, days).await?;
        Ok(response)
    }

    pub async fn get_historical_chart_data_for_hype(
        &self,
        days: u32,
    ) -> Result<MarketChartResponse, CoinGeckoError> {
        let response = self.get_historical_chart_data(HYPE_COIN_ID, VS_CURRENCY, days).await?;
        Ok(response)
    }

    /// Get historical chart data for a specific coin
    ///
    /// # Arguments
    /// * `id` - The coin id (e.g., "solana")
    /// * `vs_currency` - The target currency (e.g., "usd")
    /// * `days` - Number of days of data to retrieve
    pub async fn get_historical_chart_data(
        &self,
        id: &str,
        vs_currency: &str,
        days: u32,
    ) -> Result<MarketChartResponse, CoinGeckoError> {
        let mut params = HashMap::new();
        params.insert("vs_currency", vs_currency.to_string());
        params.insert("days", days.to_string());

        let url = format!("{}/coins/{}/market_chart", self.base_url, id);

        let client = get_reqwest_client();
        let response = client
            .get(&url)
            .query(&params)
            .header(header::ACCEPT, "application/json")
            .header(AUTH_HEADER_KEY, self.api_key.clone())
            .send()
            .await?;

        let status = response.status();
        if !status.is_success() {
            let body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            match status.as_u16() {
                401 => return Err(CoinGeckoError::Unauthorized),
                403 => return Err(CoinGeckoError::AuthenticationFailed),
                429 => return Err(CoinGeckoError::RateLimit),
                10002 => return Err(CoinGeckoError::InvalidApiKey),
                _ => {
                    return Err(CoinGeckoError::Other(format!(
                        "url: {}, vs_currency: {}, days: {}, status: {}, body: {}",
                        url, vs_currency, days, status, body
                    )))
                }
            }
        }

        let response = response.json::<MarketChartResponse>().await?;

        Ok(response)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_historical_chart_data_for_solana() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let client = CoinGeckoProvider::get();
        let response = client.get_historical_chart_data_for_solana(60).await.unwrap();

        assert!(!response.prices.is_empty());
        assert!(!response.market_caps.is_empty());
        assert!(!response.total_volumes.is_empty());

        tracing::info!(
            "prices first: {:?}, prices last: {:?}; length: {}",
            response.prices.first().unwrap(),
            response.prices.last().unwrap(),
            response.prices.len()
        );
    }
}
