use serde::{de::DeserializeOwned, Serialize};

use super::{ConsumerError, ProducerError};

pub const REPLICATION_FACTOR: i32 = 3;
pub const MAX_MESSAGE_SIZE: usize = 1024 * 1024; // 1MB

pub const INDEXER_TOPIC_PARTITION: u16 = 12;
pub const AGGREGATOR_TOPIC_PARTITION: u16 = 4;

pub trait KeyedTopic: Serialize + DeserializeOwned {
    type KeyComponent;

    fn key(&self) -> Option<String>;

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError>;

    fn payload(&self) -> Result<Vec<u8>, ProducerError> {
        let mut bytes = Vec::new();
        ciborium::into_writer(self, &mut bytes)?;

        if bytes.len() <= MAX_MESSAGE_SIZE {
            return Ok(bytes);
        } else {
            return Err(ProducerError::MessageSizeExceedsError(format!(
                "Message size exceeds max message size: {}",
                bytes.len()
            )));
        }
    }

    fn trunk_payload(&self) -> Result<Vec<Vec<u8>>, ProducerError> {
        Ok(vec![self.payload()?])
    }

    fn deserialize_payload(payload: &[u8]) -> Result<Self, ConsumerError> {
        match ciborium::de::from_reader(payload) {
            Ok(msg) => Ok(msg),
            Err(e) => Err(ConsumerError::CiboriumError(e)),
        }
    }
}

pub trait Topic {
    type Message: 'static + Serialize + DeserializeOwned + Sync + Send + KeyedTopic;

    fn topic() -> TopicType;

    fn partition() -> i32;

    fn replication_factor() -> i32 {
        REPLICATION_FACTOR
    }
}

pub enum TopicType {
    // Indexer topics
    TokenMetadata,
    PoolMetadata,
    IndexerBlock,
    TokenHolders,

    // Aggregator topics
    Candles,
    ExtendedDexTrades,
    ExtendedTokenHolders,
    PoolStatistic,
    TokenStatistic,
    NewToken,

    // Aggregator produce and consume
    UpdateStatistic,

    #[cfg(test)]
    Test,
}

impl ToString for TopicType {
    fn to_string(&self) -> String {
        match self {
            // Indexer topics
            TopicType::TokenMetadata => "token_metadata".to_string(),
            TopicType::PoolMetadata => "pool_metadata".to_string(),
            TopicType::IndexerBlock => "indexer_block".to_string(),
            TopicType::TokenHolders => "token_holders".to_string(),
            // Aggregator topics
            TopicType::Candles => "candles".to_string(),
            TopicType::ExtendedDexTrades => "extended_dex_trades".to_string(),
            TopicType::ExtendedTokenHolders => "extended_token_holders".to_string(),
            TopicType::PoolStatistic => "pool_statistic".to_string(),
            TopicType::TokenStatistic => "token_statistic".to_string(),
            TopicType::NewToken => "new_token".to_string(),
            // Aggregator produce and consume
            TopicType::UpdateStatistic => "update_statistic".to_string(),
            // Test topic
            #[cfg(test)]
            TopicType::Test => "superstack_test".to_string(),
        }
    }
}

#[cfg(test)]
#[derive(Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq)]
pub struct TestMsg {
    pub message: String,
}

#[cfg(test)]
impl Topic for TestMsg {
    type Message = TestMsg;

    fn topic() -> TopicType {
        TopicType::Test
    }

    fn partition() -> i32 {
        1
    }
}

#[cfg(test)]
impl KeyedTopic for TestMsg {
    type KeyComponent = String;

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        Ok(key.to_string())
    }

    fn key(&self) -> Option<String> {
        Some(self.message.clone())
    }
}
