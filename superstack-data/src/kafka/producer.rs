use std::{sync::OnceLock, time::Duration};

use rdkafka::{
    admin::{AdminClient, AdminOptions, NewTopic, TopicReplication},
    client::DefaultClientContext,
    producer::{FutureProducer, FutureRecord, Producer},
};

use super::{ConfluentConfig, KeyedTopic, Topic};
use crate::{
    config::Config,
    kafka::topics::*,
    postgres::{
        aggregator::*,
        indexer::{PoolMetadata, TokenMetadata},
    },
};

#[derive(Debug, thiserror::Error)]
pub enum ProducerError {
    #[error("Kafka error: {0}")]
    Kafka(#[from] rdkafka::error::KafkaError),

    #[error("create client error: {0}")]
    CreateClientError(String),

    #[error("create topic error: {0}")]
    CreateTopicError(String),

    #[error("send message error: {0}")]
    SendMessageError(String),

    #[error("message size exceeds max message size")]
    MessageSizeExceedsError(String),

    #[error("bincode error: {0}")]
    BincodeError(#[from] bincode::Error),

    #[error("ciborium error: {0}")]
    CiboriumError(#[from] ciborium::ser::Error<std::io::Error>),

    #[error("serde_json error: {0}")]
    SerdeJsonError(#[from] serde_json::Error),

    #[error("fetch metadata error: {0}")]
    FetchMetadataError(String),
}

#[derive(Clone)]
pub struct KafkaProducer {
    config: ConfluentConfig,
    producer: FutureProducer,
}

impl KafkaProducer {
    /// Create a new Kafka producer for Confluent Cloud
    pub fn new(config: &ConfluentConfig) -> Result<Self, ProducerError> {
        let producer: FutureProducer = config
            .to_producer_config()
            .create()
            .map_err(|e| ProducerError::CreateClientError(e.to_string()))?;
        tracing::info!("Kafka producer created successfully for: {}", config.bootstrap_servers);

        Ok(Self { config: config.clone(), producer })
    }

    pub fn get() -> &'static KafkaProducer {
        static PRODUCER: OnceLock<KafkaProducer> = OnceLock::new();
        PRODUCER.get_or_init(|| {
            let config = Config::get();
            let confluent_config = ConfluentConfig::new(
                config.kafka_bootstrap_servers.clone(),
                config.kafka_api_key.clone(),
                config.kafka_api_secret.clone(),
            );
            KafkaProducer::new(&confluent_config).expect("Failed to create Kafka producer")
        })
    }

    pub async fn create_indexer_topics_if_not_exists(&self) -> Result<(), ProducerError> {
        let existing_topics = self.get_all_topics().await?;
        if !existing_topics.contains(&TokenMetadata::topic().to_string()) {
            self.create_topic::<TokenMetadata>().await?;
        }
        if !existing_topics.contains(&PoolMetadata::topic().to_string()) {
            self.create_topic::<PoolMetadata>().await?;
        }
        if !existing_topics.contains(&IndexerBlockMsg::topic().to_string()) {
            self.create_topic::<IndexerBlockMsg>().await?;
        }
        if !existing_topics.contains(&TokenHoldersMsg::topic().to_string()) {
            self.create_topic::<TokenHoldersMsg>().await?;
        }
        Ok(())
    }

    pub async fn create_aggregator_topics_if_not_exists(&self) -> Result<(), ProducerError> {
        let existing_topics = self.get_all_topics().await?;
        if !existing_topics.contains(&CandlesMsg::topic().to_string()) {
            self.create_topic::<CandlesMsg>().await?;
        }
        if !existing_topics.contains(&ExtendedDexTradesMsg::topic().to_string()) {
            self.create_topic::<ExtendedDexTradesMsg>().await?;
        }
        if !existing_topics.contains(&ExtendedTokenHoldersMsg::topic().to_string()) {
            self.create_topic::<ExtendedTokenHoldersMsg>().await?;
        }
        if !existing_topics.contains(&PoolStatistic::topic().to_string()) {
            self.create_topic::<PoolStatistic>().await?;
        }
        if !existing_topics.contains(&TokenStatistic::topic().to_string()) {
            self.create_topic::<TokenStatistic>().await?;
        }
        if !existing_topics.contains(&NewTokenMsg::topic().to_string()) {
            self.create_topic::<NewTokenMsg>().await?;
        }
        if !existing_topics.contains(&UpdateStatisticMsg::topic().to_string()) {
            self.create_topic::<UpdateStatisticMsg>().await?;
        }
        Ok(())
    }

    #[cfg(test)]
    pub async fn delete_all_topics(&self) -> Result<(), ProducerError> {
        let admin: AdminClient<DefaultClientContext> = self
            .config
            .to_admin_config()
            .create()
            .map_err(|e| ProducerError::CreateClientError(e.to_string()))?;

        let topics = self.get_all_topics().await?;
        for topic in topics {
            tracing::info!("Deleting topic: {}", topic);
            let result = admin.delete_topics(&[&topic], &AdminOptions::new()).await?;
            tracing::info!("Result: {:?}", result);
        }
        Ok(())
    }

    pub async fn create_topic<T: Topic>(&self) -> Result<(), ProducerError> {
        let admin: AdminClient<DefaultClientContext> = self
            .config
            .to_admin_config()
            .create()
            .map_err(|e| ProducerError::CreateClientError(e.to_string()))?;

        let topic_name = T::topic().to_string();
        let num_partitions = T::partition();
        let replication_factor = T::replication_factor();
        let new_topic =
            NewTopic::new(&topic_name, num_partitions, TopicReplication::Fixed(replication_factor));
        let opts = AdminOptions::new().operation_timeout(Some(Duration::from_secs(30)));

        match admin.create_topics(&[new_topic], &opts).await {
            Ok(results) => {
                for result in results {
                    match result {
                        Ok(res) => {
                            tracing::info!(
                                "Topic '{}' created successfully: {:?}",
                                topic_name,
                                res
                            );
                        }
                        Err(e) => match e.1 {
                            rdkafka::error::RDKafkaErrorCode::TopicAlreadyExists => {
                                tracing::info!("Topic '{}' already exists", topic_name);
                                return Ok(());
                            }
                            _ => {
                                tracing::error!("Topic '{}' creation failed: {:?}", topic_name, e);
                                return Err(ProducerError::CreateTopicError(format!(
                                    "Topic '{}' creation failed: {:?}",
                                    topic_name, e
                                )));
                            }
                        },
                    }
                }
                Ok(())
            }
            Err(e) => {
                // Topic might already exist, which is fine
                if e.to_string().contains("already exists") {
                    tracing::info!("Topic '{}' already exists", topic_name);
                    Ok(())
                } else {
                    Err(ProducerError::CreateTopicError(e.to_string()).into())
                }
            }
        }
    }

    async fn send_bytes(
        &self,
        topic: &str,
        key: Option<&String>,
        payload: &[u8],
    ) -> Result<(), ProducerError> {
        let mut record = FutureRecord::to(topic).payload(payload);
        if let Some(key) = key {
            record = record.key(key);
        }

        match self.producer.send(record, Duration::from_secs(10)).await {
            Ok(_delivery) => Ok(()),
            Err((kafka_error, _)) => {
                tracing::error!("Failed to send message to topic '{}': {}", topic, kafka_error);
                Err(ProducerError::SendMessageError(kafka_error.to_string()).into())
            }
        }
    }

    /// Send a message to Kafka topic
    pub async fn send<T: Topic>(&self, message: &T::Message) -> Result<(), ProducerError> {
        let topic = T::topic().to_string();
        match message.payload() {
            Ok(payload) => {
                let key = message.key();
                self.send_bytes(&topic, key.as_ref(), &payload).await?;
            }
            Err(e) => match e {
                ProducerError::MessageSizeExceedsError(_) => {
                    let payloads = message.trunk_payload()?;
                    for payload in payloads {
                        self.send_bytes(&topic, message.key().as_ref(), &payload).await?;
                    }
                }
                _ => {
                    tracing::error!("Failed to serialize message: {}", e);
                    return Err(e.into());
                }
            },
        }

        Ok(())
    }

    /// Flush all pending messages
    pub fn flush(&self, timeout: Duration) {
        if let Err(e) = self.producer.flush(timeout) {
            tracing::error!("Failed to flush Kafka producer: {}", e);
        }
    }

    /// Check if a topic exists using the producer client
    pub async fn topic_exists<T: Topic>(&self) -> Result<bool, ProducerError> {
        let timeout = Duration::from_secs(10);
        let topic_name = T::topic().to_string();

        match self.producer.client().fetch_metadata(Some(&topic_name), timeout) {
            Ok(metadata) => {
                let exists = metadata
                    .topics()
                    .iter()
                    .any(|topic| topic.name() == topic_name && topic.error().is_none());

                Ok(exists)
            }
            Err(e) => {
                tracing::error!(
                    "Failed to check producer topic existence for topic {}: {}",
                    topic_name,
                    e
                );
                Err(ProducerError::FetchMetadataError(e.to_string()).into())
            }
        }
    }

    /// Get topic metadata (partitions, etc.)
    pub async fn get_all_topics(&self) -> Result<Vec<String>, ProducerError> {
        let timeout = Duration::from_secs(10);

        match self.producer.client().fetch_metadata(None, timeout) {
            Ok(metadata) => {
                let topics =
                    metadata.topics().iter().map(|topic| topic.name().to_string()).collect();
                tracing::info!("All topics from Kafka producer: {:?}", topics);
                Ok(topics)
            }
            Err(e) => {
                tracing::error!("Failed to fetch all topics from producer: {}", e);
                Err(ProducerError::FetchMetadataError(e.to_string()).into())
            }
        }
    }
}

#[cfg(test)]
pub(crate) mod tests {
    use super::*;
    use crate::{config::Config, kafka::topic::TestMsg};

    pub async fn send_test_message() {
        let config = Config::get();
        let confluent_config = ConfluentConfig::new(
            config.kafka_bootstrap_servers.clone(),
            config.kafka_api_key.clone(),
            config.kafka_api_secret.clone(),
        );
        let producer = KafkaProducer::new(&confluent_config).unwrap();

        if !producer.topic_exists::<TestMsg>().await.unwrap() {
            producer.create_topic::<TestMsg>().await.unwrap();
        }

        let mut attempts = 0;
        loop {
            let topics = producer.get_all_topics().await.unwrap();
            tracing::info!("Topics: {:?}", topics);
            if topics.contains(&TestMsg::topic().to_string()) {
                break;
            }
            tracing::info!("Waiting for topic to be created...");
            tokio::time::sleep(Duration::from_secs(1)).await;
            attempts += 1;
            if attempts > 10 {
                assert!(false, "Topic not found after 10 attempts");
            }
        }

        let message = TestMsg { message: "Hello Kafka!".to_string() };

        producer.send::<TestMsg>(&message).await.unwrap();
        tracing::info!("Message sent to Kafka");
    }

    #[tokio::test]
    async fn test_producer() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        send_test_message().await;
    }

    #[tokio::test]
    #[ignore]
    async fn test_delete_all_topics() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let producer = KafkaProducer::get();
        producer.delete_all_topics().await.unwrap();
    }
}
