use rdkafka::config::ClientConfig;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ConfluentConfig {
    pub bootstrap_servers: String,
    pub api_key: String,
    pub api_secret: String,
}

impl ConfluentConfig {
    pub fn new(bootstrap_servers: String, api_key: String, api_secret: String) -> Self {
        Self { bootstrap_servers, api_key, api_secret }
    }

    fn to_common_config(&self) -> ClientConfig {
        let mut config = ClientConfig::new();
        config.set("bootstrap.servers", &self.bootstrap_servers);

        if !self.api_key.is_empty() && !self.api_secret.is_empty() {
            config
                .set("security.protocol", "SASL_SSL")
                .set("sasl.mechanism", "PLAIN")
                .set("sasl.username", &self.api_key)
                .set("sasl.password", &self.api_secret);
        } else {
            config.set("security.protocol", "PLAINTEXT");
        }

        config
    }

    /// Create producer configuration for Confluent Cloud
    pub fn to_producer_config(&self) -> ClientConfig {
        self.to_common_config()
    }

    pub fn to_consumer_config(&self) -> ClientConfig {
        self.to_common_config()
    }

    pub fn to_admin_config(&self) -> ClientConfig {
        self.to_common_config()
    }
}
