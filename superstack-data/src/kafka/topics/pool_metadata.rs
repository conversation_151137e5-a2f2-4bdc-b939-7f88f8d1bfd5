use crate::{
    kafka::{
        topic::INDEXER_TOPIC_PARTITION, ConsumerError, KeyedTopic, ProducerError, Topic, TopicType,
    },
    postgres::{indexer::PoolMetadata, Chain},
};

impl KeyedTopic for PoolMetadata {
    type KeyComponent = (Chain, String);

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        let parts = key.split('-').collect::<Vec<&str>>();
        if parts.len() != 2 {
            return Err(ConsumerError::InvalidKey(key.to_string()));
        }
        let chain =
            parts[0].parse::<Chain>().map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        let pool_address = parts[1].to_string();
        Ok((chain, pool_address))
    }

    fn key(&self) -> Option<String> {
        let key = format!("{}-{}", self.chain.to_string(), self.pool_address);
        Some(key)
    }

    fn payload(&self) -> Result<Vec<u8>, ProducerError> {
        let bytes = serde_json::to_vec(self)?;
        Ok(bytes)
    }

    fn deserialize_payload(payload: &[u8]) -> Result<Self, ConsumerError> {
        let pool_metadata: PoolMetadata = serde_json::from_slice(payload)?;
        Ok(pool_metadata)
    }
}

impl Topic for PoolMetadata {
    type Message = Self;

    fn topic() -> TopicType {
        TopicType::PoolMetadata
    }

    fn partition() -> i32 {
        let config = crate::config::Config::get();
        config.kafka_indexer_topic_partition.unwrap_or(INDEXER_TOPIC_PARTITION) as i32
    }
}
