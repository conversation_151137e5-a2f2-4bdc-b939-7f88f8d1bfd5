use crate::{
    kafka::{topic::INDEXER_TOPIC_PARTITION, ConsumerError, KeyedTopic, Topic, TopicType},
    postgres::{
        indexer::{token_metadata::TokenMetadataV1, TokenMetadata},
        Chain,
    },
};

impl KeyedTopic for TokenMetadata {
    type KeyComponent = (Chain, String);

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        let parts = key.split('-').collect::<Vec<&str>>();
        if parts.len() != 2 {
            return Err(ConsumerError::InvalidKey(key.to_string()));
        }
        let chain =
            parts[0].parse::<Chain>().map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        let address = parts[1].to_string();
        Ok((chain, address))
    }

    fn key(&self) -> Option<String> {
        let key = format!("{}-{}", self.chain.to_string(), self.address);
        Some(key)
    }
}

impl Topic for TokenMetadata {
    type Message = Self;

    fn topic() -> TopicType {
        TopicType::TokenMetadata
    }

    fn partition() -> i32 {
        let config = crate::config::Config::get();
        config.kafka_indexer_topic_partition.unwrap_or(INDEXER_TOPIC_PARTITION) as i32
    }
}

impl KeyedTopic for TokenMetadataV1 {
    type KeyComponent = (Chain, String);

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        let parts = key.split('-').collect::<Vec<&str>>();
        if parts.len() != 2 {
            return Err(ConsumerError::InvalidKey(key.to_string()));
        }
        let chain =
            parts[0].parse::<Chain>().map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        let address = parts[1].to_string();
        Ok((chain, address))
    }

    fn key(&self) -> Option<String> {
        let key = format!("{}-{}", self.chain.to_string(), self.address);
        Some(key)
    }
}

impl Topic for TokenMetadataV1 {
    type Message = Self;

    fn topic() -> TopicType {
        TopicType::TokenMetadata
    }

    fn partition() -> i32 {
        let config = crate::config::Config::get();
        config.kafka_indexer_topic_partition.unwrap_or(INDEXER_TOPIC_PARTITION) as i32
    }
}
