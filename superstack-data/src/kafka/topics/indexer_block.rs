use std::collections::HashMap;

use serde::{Deserialize, Serialize};

use crate::{
    config::Config,
    kafka::{
        topic::{INDEXER_TOPIC_PARTITION, MAX_MESSAGE_SIZE},
        ConsumerError, KeyedTopic, ProducerError, Topic, TopicType,
    },
    postgres::{
        indexer::{DexTrade, PoolState, TokenHolder},
        Chain,
    },
};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct IndexerBlockMsg {
    pub chain: Chain,
    pub block_number: u64,
    pub trunk_mode: bool,
    pub dex_trades: Vec<DexTrade>,
    pub pool_states: Vec<PoolState>,
    pub token_holders: Vec<TokenHolder>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct IndexerBlockMsgV2 {
    pub chain: Chain,
    pub block_number: u64,
    pub trunk_mode: bool,
    pub dex_trades: Vec<DexTrade>,
    pub pool_states: Vec<PoolState>,
}

impl From<IndexerBlockMsg> for IndexerBlockMsgV2 {
    fn from(msg: IndexerBlockMsg) -> Self {
        Self {
            chain: msg.chain,
            block_number: msg.block_number,
            trunk_mode: msg.trunk_mode,
            dex_trades: msg.dex_trades,
            pool_states: msg.pool_states,
        }
    }
}

impl IndexerBlockMsg {
    pub fn new(
        chain: Chain,
        block_number: u64,
        dex_trades: Vec<DexTrade>,
        pool_states: Vec<PoolState>,
        token_holders: Vec<TokenHolder>,
    ) -> Self {
        let trunk_mode = false;
        Self { chain, block_number, trunk_mode, dex_trades, pool_states, token_holders }
    }

    pub fn construct_message(&self) -> Result<Vec<Vec<u8>>, ProducerError> {
        tracing::error!(
            "FIXME: current message split is buggy, pool_state, and corresponding dex_trade and token holder should be in the same chunk, message size: {}",
            self.payload()?.len()
        );

        let mut bytes = Vec::new();
        ciborium::into_writer(&self, &mut bytes)?;
        if bytes.len() <= MAX_MESSAGE_SIZE {
            return Ok(vec![bytes]);
        }

        // Split the message into chunks
        let mut chunks = Vec::new();
        let trade_and_pool_states_msg = Self {
            chain: self.chain.clone(),
            block_number: self.block_number,
            trunk_mode: true,
            dex_trades: self.dex_trades.clone(),
            pool_states: self.pool_states.clone(),
            token_holders: vec![],
        };
        let mut bytes1 = Vec::new();
        ciborium::into_writer(&trade_and_pool_states_msg, &mut bytes1)?;
        if bytes1.len() <= MAX_MESSAGE_SIZE {
            chunks.push(bytes1);
        } else {
            // Split the dex_trades and pool_states according to the pool address
            let mut pool_map: HashMap<String, (PoolState, Vec<DexTrade>)> = HashMap::new();
            for pool_state in self.pool_states.iter() {
                pool_map.insert(pool_state.pool_address.clone(), (pool_state.clone(), vec![]));
            }
            for dex_trade in self.dex_trades.iter() {
                if let Some(pool) = pool_map.get_mut(&dex_trade.pool_address) {
                    pool.1.push(dex_trade.clone());
                } else {
                    tracing::error!("[FIXME] Pool state not found for dex trade: {:?}", dex_trade);
                }
            }
            for (pool_state, dex_trades) in pool_map.into_values() {
                let pool_state_msg = Self {
                    chain: self.chain.clone(),
                    block_number: self.block_number,
                    trunk_mode: true,
                    dex_trades: dex_trades.clone(),
                    pool_states: vec![pool_state.clone()],
                    token_holders: vec![],
                };
                let mut bytes = Vec::new();
                ciborium::into_writer(&pool_state_msg, &mut bytes)?;
                if bytes.len() <= MAX_MESSAGE_SIZE {
                    chunks.push(bytes);
                } else {
                    // Split the dex_trades, ensure each trunk is less than MAX_MESSAGE_SIZE
                    let trunk_num = bytes.len() / MAX_MESSAGE_SIZE + 1;
                    let mut expected_trunk_size = dex_trades.len() / trunk_num / 2;
                    let mut start = 0;
                    while start < dex_trades.len() {
                        let end = (start + expected_trunk_size).min(dex_trades.len());
                        let chunk = dex_trades[start..end].to_vec();
                        let chunk_msg = Self {
                            chain: self.chain.clone(),
                            block_number: self.block_number,
                            trunk_mode: true,
                            dex_trades: chunk,
                            pool_states: vec![pool_state.clone()],
                            token_holders: vec![],
                        };
                        let mut bytes = Vec::new();
                        ciborium::into_writer(&chunk_msg, &mut bytes)?;
                        if bytes.len() <= MAX_MESSAGE_SIZE {
                            chunks.push(bytes);
                            start = end;
                        } else {
                            expected_trunk_size /= 2;
                            continue;
                        }
                    }
                }
            }
        }

        let token_holders_msg = Self {
            chain: self.chain.clone(),
            block_number: self.block_number,
            trunk_mode: true,
            dex_trades: vec![],
            pool_states: vec![],
            token_holders: self.token_holders.clone(),
        };
        let mut bytes2 = Vec::new();
        ciborium::into_writer(&token_holders_msg, &mut bytes2)?;
        if bytes2.len() <= MAX_MESSAGE_SIZE {
            chunks.push(bytes2);
        } else {
            // Split the token_holders, ensure each trunk is less than MAX_MESSAGE_SIZE
            let trunk_num = bytes2.len() / MAX_MESSAGE_SIZE + 1;
            let mut expected_trunk_size = token_holders_msg.token_holders.len() / trunk_num / 2;
            let mut start = 0;
            while start < token_holders_msg.token_holders.len() {
                let end = (start + expected_trunk_size).min(token_holders_msg.token_holders.len());
                let chunk = token_holders_msg.token_holders[start..end].to_vec();
                let chunk_msg = Self {
                    chain: self.chain.clone(),
                    block_number: self.block_number,
                    trunk_mode: true,
                    dex_trades: vec![],
                    pool_states: vec![],
                    token_holders: chunk,
                };
                let mut bytes = Vec::new();
                ciborium::into_writer(&chunk_msg, &mut bytes)?;
                if bytes.len() <= MAX_MESSAGE_SIZE {
                    chunks.push(bytes);
                    start = end;
                } else {
                    expected_trunk_size /= 2;
                    continue;
                }
            }
        }

        Ok(chunks)
    }
}

impl Topic for IndexerBlockMsg {
    type Message = IndexerBlockMsg;

    fn topic() -> TopicType {
        TopicType::IndexerBlock
    }

    fn partition() -> i32 {
        let config = Config::get();
        config.kafka_indexer_topic_partition.unwrap_or(INDEXER_TOPIC_PARTITION) as i32
    }
}

impl KeyedTopic for IndexerBlockMsg {
    type KeyComponent = ();

    fn parse_key(_key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        Ok(())
    }

    fn key(&self) -> Option<String> {
        None
    }

    fn trunk_payload(&self) -> Result<Vec<Vec<u8>>, ProducerError> {
        let message = self.construct_message()?;
        Ok(message)
    }
}

impl IndexerBlockMsgV2 {
    pub fn new(
        chain: Chain,
        block_number: u64,
        dex_trades: Vec<DexTrade>,
        pool_states: Vec<PoolState>,
    ) -> Self {
        let trunk_mode = false;
        Self { chain, block_number, trunk_mode, dex_trades, pool_states }
    }

    pub fn construct_message(&self) -> Result<Vec<Vec<u8>>, ProducerError> {
        let mut bytes = Vec::new();
        ciborium::into_writer(&self, &mut bytes)?;
        if bytes.len() <= MAX_MESSAGE_SIZE {
            return Ok(vec![bytes]);
        }

        // Split the dex_trades and pool_states according to the pool address
        let mut pool_map: HashMap<String, (PoolState, Vec<DexTrade>)> = HashMap::new();
        for pool_state in self.pool_states.iter() {
            pool_map.insert(pool_state.pool_address.clone(), (pool_state.clone(), vec![]));
        }
        for dex_trade in self.dex_trades.iter() {
            if let Some(pool) = pool_map.get_mut(&dex_trade.pool_address) {
                pool.1.push(dex_trade.clone());
            } else {
                tracing::error!("[FIXME] Pool state not found for dex trade: {:?}", dex_trade);
            }
        }

        let mut chunks = Vec::new();
        for (pool_state, dex_trades) in pool_map.into_values() {
            let pool_state_msg = Self {
                chain: self.chain.clone(),
                block_number: self.block_number,
                trunk_mode: true,
                dex_trades: dex_trades.clone(),
                pool_states: vec![pool_state.clone()],
            };
            let mut bytes = Vec::new();
            ciborium::into_writer(&pool_state_msg, &mut bytes)?;
            if bytes.len() <= MAX_MESSAGE_SIZE {
                chunks.push(bytes);
            } else {
                // Split the dex_trades, ensure each trunk is less than MAX_MESSAGE_SIZE
                let trunk_num = bytes.len() / MAX_MESSAGE_SIZE + 1;
                let mut expected_trunk_size = dex_trades.len() / trunk_num / 2;
                let mut start = 0;
                while start < dex_trades.len() {
                    let end = (start + expected_trunk_size).min(dex_trades.len());
                    let chunk = dex_trades[start..end].to_vec();
                    let chunk_msg = Self {
                        chain: self.chain.clone(),
                        block_number: self.block_number,
                        trunk_mode: true,
                        dex_trades: chunk,
                        pool_states: vec![pool_state.clone()],
                    };
                    let mut bytes = Vec::new();
                    ciborium::into_writer(&chunk_msg, &mut bytes)?;
                    if bytes.len() <= MAX_MESSAGE_SIZE {
                        chunks.push(bytes);
                        start = end;
                    } else {
                        expected_trunk_size /= 2;
                        continue;
                    }
                }
            }
        }

        Ok(chunks)
    }
}

impl Topic for IndexerBlockMsgV2 {
    type Message = IndexerBlockMsgV2;

    fn topic() -> TopicType {
        TopicType::IndexerBlock
    }

    fn partition() -> i32 {
        let config = Config::get();
        config.kafka_indexer_topic_partition.unwrap_or(INDEXER_TOPIC_PARTITION) as i32
    }
}

impl KeyedTopic for IndexerBlockMsgV2 {
    type KeyComponent = ();

    fn parse_key(_key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        Ok(())
    }

    fn key(&self) -> Option<String> {
        None
    }

    fn trunk_payload(&self) -> Result<Vec<Vec<u8>>, ProducerError> {
        let message = self.construct_message()?;
        Ok(message)
    }
}

#[cfg(test)]
mod tests {
    use solana_sdk::{pubkey::Pubkey, signature::Signature};
    use sqlx::types::BigDecimal;

    use super::*;

    #[test]
    fn test_indexer_block_msg() {
        crate::utils::setup_tracing();

        let pool_address = Pubkey::new_unique();
        let dex_trade = DexTrade {
            chain: Chain::Solana,
            tx_hash: Signature::new_unique().to_string(),
            pool_address: pool_address.to_string(),
            tx_idx: 0,
            ix_idx: 0,
            maker_address: Pubkey::new_unique().to_string(),
            is_buy_token: true,
            in_address: Pubkey::new_unique().to_string(),
            in_amount: BigDecimal::from(0),
            out_address: Pubkey::new_unique().to_string(),
            out_amount: BigDecimal::from(0),
            block_number: 0,
            timestamp_millis: 0,
        };
        let dex_trade_size = bincode::serialize(&dex_trade).unwrap().len();
        tracing::info!(
            "Dex trade size: {}, size of DexTrade: {}",
            dex_trade_size,
            core::mem::size_of::<DexTrade>()
        );
        let max_dex_trade_number = MAX_MESSAGE_SIZE / dex_trade_size;
        tracing::info!("Max dex trade number: {}", max_dex_trade_number);

        let pool_state = PoolState::new_solana_pool_state(pool_address, 1, 1);
        let mut bytes = Vec::new();
        ciborium::into_writer(&pool_state, &mut bytes).unwrap();
        let pool_state_size = bytes.len();
        tracing::info!(
            "Pool state size: {}, size of PoolState: {}",
            pool_state_size,
            core::mem::size_of::<PoolState>()
        );
        let max_pool_state_number = MAX_MESSAGE_SIZE / pool_state_size;
        tracing::info!("Max pool state number: {}", max_pool_state_number);

        let token_holder =
            TokenHolder::new_solana_token_holder(Pubkey::new_unique(), Pubkey::new_unique(), 1, 1);
        let mut bytes = Vec::new();
        ciborium::into_writer(&token_holder, &mut bytes).unwrap();
        let token_holder_size = bytes.len();
        tracing::info!(
            "Token holder size: {}, size of TokenHolder: {}",
            token_holder_size,
            core::mem::size_of::<TokenHolder>()
        );
        let max_token_holder_number = MAX_MESSAGE_SIZE / token_holder_size;
        tracing::info!("Max token holder number: {}", max_token_holder_number);

        let small_msg = IndexerBlockMsg::new(
            Chain::Solana,
            1,
            vec![dex_trade.clone(); 20],
            vec![pool_state.clone(); 20],
            vec![token_holder.clone(); 20],
        );
        let chunks = small_msg.construct_message().unwrap();
        tracing::info!("Chunks number: {}", chunks.len());
        for (i, chunk) in chunks.iter().enumerate() {
            let msg = IndexerBlockMsg::deserialize_payload(chunk).unwrap();
            tracing::info!(
                "Chunk {}: dex_trades: {}, pool_states: {}, token_holders: {}",
                i,
                msg.dex_trades.len(),
                msg.pool_states.len(),
                msg.token_holders.len()
            );
        }

        let large_msg = IndexerBlockMsg::new(
            Chain::Solana,
            1,
            vec![dex_trade.clone(); 10000],
            vec![pool_state.clone(); 10000],
            vec![token_holder.clone(); 10000],
        );
        let chunks = large_msg.construct_message().unwrap();
        tracing::info!("Chunks number: {}", chunks.len());
        for (i, chunk) in chunks.iter().enumerate() {
            let msg = IndexerBlockMsg::deserialize_payload(chunk).unwrap();
            tracing::info!(
                "Chunk {}: message size: {}, dex_trades: {}, pool_states: {}, token_holders: {}",
                i,
                chunk.len(),
                msg.dex_trades.len(),
                msg.pool_states.len(),
                msg.token_holders.len()
            );
        }
    }
}
