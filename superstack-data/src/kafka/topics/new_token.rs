use serde::{Deserialize, Serialize};

use crate::{
    kafka::{ConsumerError, KeyedTopic, Topic, TopicType},
    postgres::{aggregator::TokenStatistic, Chain},
};

#[derive(Debug, Serialize, Deserialize)]
pub struct NewTokenMsg {
    pub token: TokenStatistic,
}

impl KeyedTopic for NewTokenMsg {
    type KeyComponent = (Chain, String);

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        let parts = key.split('-').collect::<Vec<&str>>();
        if parts.len() != 2 {
            return Err(ConsumerError::InvalidKey(key.to_string()));
        }
        let chain =
            parts[0].parse::<Chain>().map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        let token_address = parts[1].to_string();
        Ok((chain, token_address))
    }

    fn key(&self) -> Option<String> {
        let key = format!("{}-{}", self.token.chain.to_string(), self.token.token_address);
        Some(key)
    }
}

impl Topic for NewTokenMsg {
    type Message = NewTokenMsg;

    fn topic() -> TopicType {
        TopicType::NewToken
    }

    fn partition() -> i32 {
        1
    }
}
