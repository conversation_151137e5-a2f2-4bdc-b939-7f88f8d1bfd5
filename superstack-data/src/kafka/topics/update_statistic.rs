use serde::{Deserialize, Serialize};

use crate::{
    config::Config,
    kafka::{topic::INDEXER_TOPIC_PARTITION, ConsumerError, KeyedTopic, Topic, TopicType},
    postgres::Chain,
};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct UpdateStatisticMsg {
    pub tokens: Vec<(Chain, String, i64)>,
}

impl KeyedTopic for UpdateStatisticMsg {
    type KeyComponent = ();

    fn parse_key(_key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        Ok(())
    }

    fn key(&self) -> Option<String> {
        None
    }
}

impl Topic for UpdateStatisticMsg {
    type Message = Self;

    fn topic() -> TopicType {
        TopicType::UpdateStatistic
    }

    fn partition() -> i32 {
        let config = Config::get();
        config.kafka_indexer_topic_partition.unwrap_or(INDEXER_TOPIC_PARTITION) as i32
    }
}
