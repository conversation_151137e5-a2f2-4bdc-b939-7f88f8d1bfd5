use serde::{Deserialize, Serialize};

use crate::{
    kafka::{topic::AGGREGATOR_TOPIC_PARTITION, ConsumerError, KeyedTopic, Topic, TopicType},
    postgres::{aggregator::ExtendedDexTrade, Chain},
};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ExtendedDexTradesMsg {
    pub trades: Vec<ExtendedDexTrade>,
}

impl ExtendedDexTradesMsg {
    pub fn new(trades: Vec<ExtendedDexTrade>) -> Self {
        Self { trades }
    }
}

impl KeyedTopic for ExtendedDexTradesMsg {
    type KeyComponent = (Chain, String);

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        let parts = key.split('-').collect::<Vec<&str>>();
        if parts.len() != 2 {
            return Err(ConsumerError::InvalidKey(key.to_string()));
        }
        let chain =
            parts[0].parse::<Chain>().map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        let pool_address = parts[1].to_string();
        Ok((chain, pool_address))
    }

    fn key(&self) -> Option<String> {
        let key = if let Some(trade) = self.trades.first() {
            format!("{}-{}", trade.chain.to_string(), trade.pool_address,)
        } else {
            String::new()
        };
        Some(key)
    }
}

impl Topic for ExtendedDexTradesMsg {
    type Message = Self;

    fn topic() -> TopicType {
        TopicType::ExtendedDexTrades
    }

    fn partition() -> i32 {
        let config = crate::config::Config::get();
        config.kafka_aggregator_topic_partition.unwrap_or(AGGREGATOR_TOPIC_PARTITION) as i32
    }
}
