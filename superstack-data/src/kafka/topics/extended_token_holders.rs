use serde::{Deserialize, Serialize};

use crate::{
    kafka::{topic::AGGREGATOR_TOPIC_PARTITION, ConsumerError, KeyedTopic, Topic, TopicType},
    postgres::{aggregator::ExtendedTokenHolder, Chain},
};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ExtendedTokenHoldersMsg {
    pub holders: Vec<ExtendedTokenHolder>,
}

impl ExtendedTokenHoldersMsg {
    pub fn new(holders: Vec<ExtendedTokenHolder>) -> Self {
        Self { holders }
    }
}

impl KeyedTopic for ExtendedTokenHoldersMsg {
    type KeyComponent = (Chain, String);

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        let parts = key.split('-').collect::<Vec<&str>>();
        if parts.len() != 2 {
            return Err(ConsumerError::InvalidKey(key.to_string()));
        }
        let chain =
            parts[0].parse::<Chain>().map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        let token_address = parts[1].to_string();
        Ok((chain, token_address))
    }

    fn key(&self) -> Option<String> {
        let key = if let Some(holder) = self.holders.first() {
            format!("{}-{}", holder.chain.to_string(), holder.token_address,)
        } else {
            String::new()
        };
        Some(key)
    }
}

impl Topic for ExtendedTokenHoldersMsg {
    type Message = Self;

    fn topic() -> TopicType {
        TopicType::ExtendedTokenHolders
    }

    fn partition() -> i32 {
        let config = crate::config::Config::get();
        config.kafka_aggregator_topic_partition.unwrap_or(AGGREGATOR_TOPIC_PARTITION) as i32
    }
}
