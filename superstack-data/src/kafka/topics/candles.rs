use serde::{Deserialize, Serialize};

use crate::{
    kafka::{topic::AGGREGATOR_TOPIC_PARTITION, ConsumerError, KeyedTopic, Topic, TopicType},
    postgres::{aggregator::Candle, CandleInterval, Chain},
};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CandlesMsg {
    pub candles: Vec<Candle>,
}

impl CandlesMsg {
    pub fn new(candles: Vec<Candle>) -> Self {
        Self { candles }
    }
}

impl KeyedTopic for CandlesMsg {
    type KeyComponent = (Chain, String, CandleInterval);

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        let parts = key.split('-').collect::<Vec<&str>>();
        if parts.len() != 3 {
            return Err(ConsumerError::InvalidKey(key.to_string()));
        }
        let chain =
            parts[0].parse::<Chain>().map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        let pool_address = parts[1].to_string();
        let interval = parts[2]
            .parse::<CandleInterval>()
            .map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        Ok((chain, pool_address, interval))
    }

    fn key(&self) -> Option<String> {
        let key = if let Some(candle) = self.candles.first() {
            format!(
                "{}-{}-{}",
                candle.chain.to_string(),
                candle.pool_address,
                candle.interval.to_string()
            )
        } else {
            String::new()
        };
        Some(key)
    }
}

impl Topic for CandlesMsg {
    type Message = Self;

    fn topic() -> TopicType {
        TopicType::Candles
    }

    fn partition() -> i32 {
        let config = crate::config::Config::get();
        config.kafka_aggregator_topic_partition.unwrap_or(AGGREGATOR_TOPIC_PARTITION) as i32
    }
}
