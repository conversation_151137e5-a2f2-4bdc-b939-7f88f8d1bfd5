use serde::{Deserialize, Serialize};

use crate::{
    kafka::{topic::INDEXER_TOPIC_PARTITION, ConsumerError, KeyedTopic, Topic, TopicType},
    postgres::{indexer::TokenHolder, Chain},
};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct TokenHoldersMsg {
    pub holders: Vec<TokenHolder>,
}

impl TokenHoldersMsg {
    pub fn new(holders: Vec<TokenHolder>) -> Self {
        Self { holders }
    }
}

impl KeyedTopic for TokenHoldersMsg {
    type KeyComponent = (Chain, String);

    fn parse_key(key: &str) -> Result<Self::KeyComponent, ConsumerError> {
        let parts = key.split('-').collect::<Vec<&str>>();
        if parts.len() != 2 {
            return Err(ConsumerError::InvalidKey(key.to_string()));
        }
        let chain =
            parts[0].parse::<Chain>().map_err(|_| ConsumerError::ParseKeyError(key.to_string()))?;
        let token_address = parts[1].to_string();
        Ok((chain, token_address))
    }

    fn key(&self) -> Option<String> {
        let key = if let Some(holder) = self.holders.first() {
            format!("{}-{}", holder.chain.to_string(), holder.token_address)
        } else {
            String::new()
        };
        Some(key)
    }
}

impl Topic for TokenHoldersMsg {
    type Message = Self;

    fn topic() -> TopicType {
        TopicType::TokenHolders
    }

    fn partition() -> i32 {
        let config = crate::config::Config::get();
        config.kafka_indexer_topic_partition.unwrap_or(INDEXER_TOPIC_PARTITION) as i32
    }
}
