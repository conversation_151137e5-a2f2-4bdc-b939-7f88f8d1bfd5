use std::time::Duration;

use futures::StreamExt;
use rdkafka::{
    consumer::{Consumer, MessageStream, StreamConsumer},
    message::Message,
    TopicPartitionList,
};
use tokio::sync::mpsc;

use super::{ConfluentConfig, KeyedTopic, Topic, TopicType};
use crate::error::Result;

#[derive(Debug, thiserror::Error)]
pub enum ConsumerError {
    #[error("Kafka error: {0}")]
    Kafka(#[from] rdkafka::error::KafkaError),

    #[error("create client error: {0}")]
    CreateClientError(String),

    #[error("subscribe error: {0}")]
    SubscribeError(String),

    #[error("commit message error: {0}")]
    CommitMessageError(String),

    #[error("assignment error: {0}")]
    AssignmentError(String),

    #[error("bincode error: {0}")]
    BincodeError(#[from] bincode::Error),

    #[error("ciborium error: {0}")]
    CiboriumError(#[from] ciborium::de::Error<std::io::Error>),

    #[error("serde_json error: {0}")]
    SerdeJsonError(#[from] serde_json::Error),

    #[error("empty payload")]
    EmptyPayloadError(String),

    #[error("seek error: {0}")]
    SeekError(String),

    #[error("callback consumer error: {0}")]
    CallbackConsumerError(String),

    #[error("channel consumer error: {0}")]
    ChannelConsumerError(String),

    #[error("fetch metadata error: {0}")]
    FetchMetadataError(String),

    #[error("invalid key: {0}")]
    InvalidKey(String),

    #[error("parse key error: {0}")]
    ParseKeyError(String),

    #[error("fetch info error: {0}")]
    FetchInfoError(String),
}

pub struct KafkaConsumer {
    pub consumer: StreamConsumer,
    pub group_id: String,
}

#[derive(Debug, Clone)]
pub struct KafkaConsumerConfig {
    pub group_id: String,
    pub enable_auto_commit: bool,
    pub auto_commit_interval_ms: u64,
    pub session_timeout_ms: u64,
    pub heartbeat_interval_ms: u64,
    pub auto_offset_reset: String,
}

impl KafkaConsumerConfig {
    pub fn new_earliest_with_auto_commit(group_id: &str, auto_commit_interval_ms: u64) -> Self {
        Self {
            group_id: group_id.to_string(),
            enable_auto_commit: true,
            auto_commit_interval_ms,
            session_timeout_ms: 10000,
            heartbeat_interval_ms: 3000,
            auto_offset_reset: "earliest".to_string(),
        }
    }

    pub fn new_earliest_without_auto_commit(group_id: &str) -> Self {
        Self {
            group_id: group_id.to_string(),
            enable_auto_commit: false,
            auto_commit_interval_ms: 1000,
            session_timeout_ms: 10000,
            heartbeat_interval_ms: 3000,
            auto_offset_reset: "earliest".to_string(),
        }
    }

    pub fn new_earliest(group_id: &str) -> Self {
        Self {
            group_id: group_id.to_string(),
            enable_auto_commit: true,
            auto_commit_interval_ms: 1000,
            session_timeout_ms: 10000,
            heartbeat_interval_ms: 3000,
            auto_offset_reset: "earliest".to_string(),
        }
    }

    pub fn new_latest(group_id: &str) -> Self {
        Self {
            group_id: group_id.to_string(),
            enable_auto_commit: true,
            auto_commit_interval_ms: 1000,
            session_timeout_ms: 10000,
            heartbeat_interval_ms: 3000,
            auto_offset_reset: "latest".to_string(),
        }
    }
}

impl KafkaConsumer {
    /// Check if the error is a bincode deserialization error
    fn is_deserialize_error(error: &crate::error::Error) -> bool {
        matches!(error, crate::error::Error::KafkaConsumerError(ConsumerError::BincodeError(_))) ||
            matches!(
                error,
                crate::error::Error::KafkaConsumerError(ConsumerError::CiboriumError(_))
            )
    }

    /// Create a new Kafka consumer
    pub fn new(
        confluent_config: &ConfluentConfig,
        consumer_config: &KafkaConsumerConfig,
    ) -> Result<Self> {
        let mut config = confluent_config.to_consumer_config();
        config
            .set("group.id", consumer_config.group_id.clone())
            .set("session.timeout.ms", consumer_config.session_timeout_ms.to_string())
            .set("heartbeat.interval.ms", consumer_config.heartbeat_interval_ms.to_string())
            .set("auto.offset.reset", consumer_config.auto_offset_reset.clone());
        if consumer_config.enable_auto_commit {
            config.set("enable.auto.commit", "true");
            config.set(
                "auto.commit.interval.ms",
                consumer_config.auto_commit_interval_ms.to_string(),
            );
        }

        let consumer: StreamConsumer =
            config.create().map_err(|e| ConsumerError::CreateClientError(e.to_string()))?;

        tracing::info!(
            "Kafka consumer created successfully for group: {}",
            consumer_config.group_id
        );

        Ok(Self { consumer, group_id: consumer_config.group_id.clone() })
    }

    /// Subscribe to a single topic using the Topic trait
    pub fn subscribe_topic<T: Topic>(&self) -> Result<()> {
        let topic_name = T::topic().to_string();
        self.consumer
            .subscribe(&[&topic_name])
            .map_err(|e| ConsumerError::SubscribeError(e.to_string()))?;
        Ok(())
    }

    /// Subscribe to multiple topics using the Topic trait
    pub fn subscribe_topics(&self, topics: &[TopicType]) -> Result<()> {
        let topic_names: Vec<String> = topics.iter().map(|t| t.to_string()).collect();
        let topic_refs: Vec<&str> = topic_names.iter().map(|s| s.as_str()).collect();
        self.consumer
            .subscribe(&topic_refs)
            .map_err(|e| ConsumerError::SubscribeError(e.to_string()))?;
        Ok(())
    }

    pub fn stream(&self) -> MessageStream<'_, rdkafka::consumer::DefaultConsumerContext> {
        self.consumer.stream()
    }

    /// Consume messages and send them to a channel
    pub async fn consume_to_channel<T: Topic>(
        self,
        sender: mpsc::Sender<T::Message>,
        enable_commit: bool,
    ) -> Result<()> {
        let mut message_stream = self.consumer.stream();

        tracing::info!("Starting message consumption for group: {}", self.group_id);

        let mut retry_count = 0;
        let mut skipped_messages = 0;
        let mut processed_messages = 0;

        while let Some(message_result) = message_stream.next().await {
            match message_result {
                Ok(borrowed_message) => {
                    match self.process_message::<T>(&borrowed_message) {
                        Ok(message) => {
                            if let Err(e) = sender.send(message).await {
                                tracing::error!("Channel receiver dropped, stopping consumer");
                                return Err(ConsumerError::ChannelConsumerError(format!(
                                    "Send message to channel error: {}",
                                    e
                                ))
                                .into());
                            }
                            processed_messages += 1;

                            // Reset retry count on successful processing
                            retry_count = 0;
                        }
                        Err(e) => {
                            // Check if it's a deserialize error (corrupted message)
                            if Self::is_deserialize_error(&e) {
                                // Skip this corrupted message and continue
                                skipped_messages += 1;

                                // Still commit the message to avoid reprocessing
                                if enable_commit {
                                    if let Err(commit_err) = self.consumer.commit_message(
                                        &borrowed_message,
                                        rdkafka::consumer::CommitMode::Async,
                                    ) {
                                        tracing::error!(
                                            "Failed to commit skipped message: {}",
                                            commit_err
                                        );
                                    }
                                }

                                continue;
                            }

                            // For other errors, return and let the retry logic handle it
                            tracing::error!("Failed to process message: {}", e);
                            return Err(e);
                        }
                    }

                    // Commit the message
                    if enable_commit {
                        if let Err(e) = self
                            .consumer
                            .commit_message(&borrowed_message, rdkafka::consumer::CommitMode::Async)
                        {
                            tracing::error!("Failed to commit message: {}", e);
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {}", e);
                    retry_count += 1;
                    if retry_count > 10 {
                        return Err(ConsumerError::ChannelConsumerError(format!(
                            "Error receiving message: {}",
                            e
                        ))
                        .into());
                    }
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }

            // Log statistics every 10000 messages or if there are any skipped messages
            let total_messages = processed_messages + skipped_messages;
            if (total_messages % 10000 == 0 && total_messages > 0) ||
                (skipped_messages > 0 && total_messages % 100 == 0)
            {
                tracing::info!(
                    "Message consumption stats for group '{}': processed={}, skipped={}, total={}",
                    self.group_id,
                    processed_messages,
                    skipped_messages,
                    total_messages
                );
            }
        }

        tracing::info!(
            "Message consumption completed for group '{}': processed={}, skipped={}",
            self.group_id,
            processed_messages,
            skipped_messages
        );

        Ok(())
    }

    /// Consume messages with a callback function
    pub async fn consume_with_callback<T, F>(self, mut callback: F) -> Result<()>
    where
        T: Topic,
        F: FnMut(T::Message) -> Result<()> + Send,
    {
        let mut message_stream = self.consumer.stream();

        tracing::info!("Starting callback-based consumption for group: {}", self.group_id);

        let mut retry_count = 0;
        while let Some(message_result) = message_stream.next().await {
            match message_result {
                Ok(borrowed_message) => {
                    match self.process_message::<T>(&borrowed_message) {
                        Ok(message) => {
                            if let Err(e) = callback(message) {
                                tracing::error!("Callback function failed: {}", e);
                                return Err(
                                    ConsumerError::CallbackConsumerError(e.to_string()).into()
                                );
                            }
                        }
                        Err(e) => {
                            // Check if it's a deserialize error (corrupted message)
                            if Self::is_deserialize_error(&e) {
                                tracing::warn!(
                                    "Skipping corrupted message {} from topic '{}' in callback consumer",
                                    borrowed_message.offset(),
                                    borrowed_message.topic()
                                );

                                // Still commit the message to avoid reprocessing
                                if let Err(commit_err) = self.consumer.commit_message(
                                    &borrowed_message,
                                    rdkafka::consumer::CommitMode::Async,
                                ) {
                                    tracing::error!(
                                        "Failed to commit skipped message: {}",
                                        commit_err
                                    );
                                    return Err(ConsumerError::CommitMessageError(
                                        commit_err.to_string(),
                                    )
                                    .into());
                                }

                                continue;
                            }

                            // For other errors, return
                            return Err(e);
                        }
                    }

                    // Commit the message
                    if let Err(e) = self
                        .consumer
                        .commit_message(&borrowed_message, rdkafka::consumer::CommitMode::Async)
                    {
                        tracing::error!("Failed to commit message: {}", e);
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {}", e);
                    retry_count += 1;
                    if retry_count > 10 {
                        return Err(ConsumerError::CallbackConsumerError(format!(
                            "Error receiving message: {}",
                            e
                        ))
                        .into());
                    }
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }
        }

        Ok(())
    }

    /// Consume messages using the Topic trait
    pub async fn consume_topic_to_channel<T: Topic>(
        self,
        sender: mpsc::Sender<T::Message>,
        enable_commit: bool,
    ) -> Result<()> {
        self.subscribe_topic::<T>()?;
        self.consume_to_channel::<T>(sender, enable_commit).await
    }

    /// Consume messages using the Topic trait with callback
    pub async fn consume_topic_with_callback<T: Topic, F>(self, callback: F) -> Result<()>
    where
        F: FnMut(T::Message) -> Result<()> + Send,
    {
        self.subscribe_topic::<T>()?;
        self.consume_with_callback::<T, F>(callback).await
    }

    pub async fn topic_exists<T: Topic>(&self) -> Result<bool> {
        let timeout = Duration::from_secs(10);
        let topic_name = T::topic().to_string();

        match self.consumer.fetch_metadata(Some(&topic_name), timeout) {
            Ok(metadata) => {
                let exists = metadata
                    .topics()
                    .iter()
                    .any(|topic| topic.name() == topic_name && topic.error().is_none());

                Ok(exists)
            }
            Err(e) => {
                tracing::error!(
                    "Failed to check consumer topic existence for topic {}: {}",
                    topic_name,
                    e
                );
                Err(ConsumerError::FetchMetadataError(e.to_string()).into())
            }
        }
    }

    /// Get topic metadata (partitions, etc.)
    pub async fn get_all_topics(&self) -> Result<Vec<String>> {
        let timeout = Duration::from_secs(10);

        match self.consumer.fetch_metadata(None, timeout) {
            Ok(metadata) => {
                let topics =
                    metadata.topics().iter().map(|topic| topic.name().to_string()).collect();
                tracing::info!("All topics from Kafka consumer: {:?}", topics);
                Ok(topics)
            }
            Err(e) => {
                tracing::error!("Failed to fetch all topics from consumer: {}", e);
                Err(ConsumerError::FetchMetadataError(e.to_string()).into())
            }
        }
    }

    /// Get current assignment
    pub fn assignment(&self) -> Result<TopicPartitionList> {
        match self.consumer.assignment() {
            Ok(assignment) => Ok(assignment),
            Err(e) => Err(ConsumerError::AssignmentError(e.to_string()).into()),
        }
    }

    /// Seek to specific offset
    pub fn seek<T: Topic>(&self, partition: i32, offset: i64, timeout: Duration) -> Result<()> {
        let topic = T::topic().to_string();
        let offset = rdkafka::Offset::Offset(offset);

        match self.consumer.seek(&topic, partition, offset, timeout) {
            Ok(_) => {
                tracing::info!(
                    "Seeked to topic '{}' partition {} offset {:?}",
                    topic,
                    partition,
                    offset
                );
                Ok(())
            }
            Err(e) => Err(ConsumerError::SeekError(e.to_string()).into()),
        }
    }

    /// Process a single message
    pub fn process_message<T: Topic>(
        &self,
        message: &rdkafka::message::BorrowedMessage,
    ) -> Result<T::Message> {
        match message.payload() {
            Some(payload) => {
                // Try to deserialize the message with detailed error handling
                match T::Message::deserialize_payload(payload) {
                    Ok(deserialized) => {
                        tracing::debug!(
                            "Successfully processed message from topic '{}' partition {} offset {} (payload size: {} bytes)",
                            message.topic(),
                            message.partition(),
                            message.offset(),
                            payload.len()
                        );
                        Ok(deserialized)
                    }
                    Err(e) => {
                        // // Log detailed error information for debugging
                        // let key_str = message
                        //     .key()
                        //     .and_then(|k| std::str::from_utf8(k).ok())
                        //     .unwrap_or("<invalid_utf8>");

                        // tracing::error!(
                        //     "Failed to deserialize message from topic '{}' partition {} offset {}
                        // with key '{}': {}",     message.topic(),
                        //     message.partition(),
                        //     message.offset(),
                        //     key_str,
                        //     e
                        // );

                        Err(e.into())
                    }
                }
            }
            None => {
                tracing::error!("Received message with empty payload");
                Err(ConsumerError::EmptyPayloadError(format!(
                    "Empty payload for topic: {}, key: {:?}",
                    message.topic(),
                    message.key()
                ))
                .into())
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config::Config, kafka::topic::TestMsg};

    async fn get_test_consumer(group_id: &str) -> KafkaConsumer {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let config = Config::get();
        let confluent_config = ConfluentConfig::new(
            config.kafka_bootstrap_servers.clone(),
            config.kafka_api_key.clone(),
            config.kafka_api_secret.clone(),
        );

        let consumer_config = KafkaConsumerConfig::new_earliest(group_id);
        KafkaConsumer::new(&confluent_config, &consumer_config).unwrap()
    }

    #[tokio::test]
    async fn test_consumer_creation() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let consumer = get_test_consumer("test_consumer_creation").await;
        println!("✅ Consumer created successfully for group: {}", consumer.group_id);
    }

    #[tokio::test]
    async fn test_consume_with_stream() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        crate::kafka::producer::tests::send_test_message().await;

        let consumer = get_test_consumer("test_consume_with_stream").await;
        consumer.subscribe_topic::<TestMsg>().unwrap();

        let mut stream = consumer.stream();

        while let Some(message) = stream.next().await {
            match message {
                Ok(borrowed_message) => {
                    let message = consumer.process_message::<TestMsg>(&borrowed_message);
                    tracing::info!("✅ Received message: {:?}", message);
                    return;
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {}", e);
                }
            }
        }
    }

    #[tokio::test]
    async fn test_consume_with_channel() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        crate::kafka::producer::tests::send_test_message().await;

        let consumer = get_test_consumer("test_consume_with_channel").await;
        consumer.subscribe_topic::<TestMsg>().unwrap();

        let (tx, mut rx) = mpsc::channel::<TestMsg>(100);

        // Start consumer in background
        let consumer_handle = tokio::spawn(async move {
            if let Err(e) = consumer.consume_to_channel::<TestMsg>(tx, true).await {
                tracing::error!("Consumer error: {}", e);
            }
        });

        // Wait for messages with timeout
        tokio::select! {
            Some(message) = rx.recv() => {
                tracing::info!("✅ Received message: {:?}", message);
            }
            _ = tokio::time::sleep(Duration::from_secs(5)) => {
                tracing::info!("⏰ No messages received within timeout");
            }
        }

        consumer_handle.abort();
    }

    #[tokio::test]
    async fn test_consume_with_callback() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        crate::kafka::producer::tests::send_test_message().await;

        let consumer = get_test_consumer("test_consume_with_callback").await;
        consumer.subscribe_topic::<TestMsg>().unwrap();

        let callback_handle = tokio::spawn(async move {
            let mut count = 0;
            consumer
                .consume_with_callback::<TestMsg, _>(|msg| {
                    count += 1;
                    tracing::info!("✅ Callback received message #{}: {:?}", count, msg);
                    Ok(())
                })
                .await
        });

        // Let it run for a bit
        tokio::select! {
            result = callback_handle => {
                if let Err(e) = result {
                    tracing::error!("Callback consumer error: {}", e);
                }
            }
            _ = tokio::time::sleep(Duration::from_secs(5)) => {
                tracing::info!("⏰ Callback consumer timeout");
            }
        }
    }

    #[tokio::test]
    async fn test_topic_trait_consumption() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        crate::kafka::producer::tests::send_test_message().await;

        let consumer = get_test_consumer("test_topic_trait_consumption").await;
        let (tx, mut rx) = mpsc::channel::<TestMsg>(100);

        // Start consumer using Topic trait
        let consumer_handle = tokio::spawn(async move {
            if let Err(e) = consumer.consume_topic_to_channel::<TestMsg>(tx, true).await {
                tracing::error!("Topic consumer error: {}", e);
            }
        });

        tokio::select! {
            Some(message) = rx.recv() => {
                tracing::info!("✅ Topic trait received: {:?}", message);
            }
            _ = tokio::time::sleep(Duration::from_secs(5)) => {
                tracing::info!("⏰ Topic trait consumer timeout");
            }
        }

        consumer_handle.abort();
    }
}
