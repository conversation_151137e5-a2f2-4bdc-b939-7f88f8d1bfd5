use serde::{de::DeserializeOwned, Serialize};

use crate::store::error::StorageError;

use super::map::Map;

pub trait Singleton<T>: Clone
where
    T: Serialize + DeserializeOwned + Clone + Send + Sync,
{
    fn load(&self) -> Result<Option<T>, StorageError>;

    fn save(&self, value: T) -> Result<(), StorageError>;
}

impl<M, T> Singleton<T> for M
where
    T: Serialize + DeserializeOwned + Clone + Send + Sync,
    M: Map<(), T>,
{
    fn load(&self) -> Result<Option<T>, StorageError> {
        self.get(&())
    }

    fn save(&self, value: T) -> Result<(), StorageError> {
        self.insert((), value)
    }
}
