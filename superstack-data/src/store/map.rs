use serde::{de::DeserializeOwned, Serialize};
use std::{
    borrow::{<PERSON><PERSON>, <PERSON>w},
    hash::Hash,
};

use crate::store::error::StorageError;

pub trait Map<
    K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
    V: Serialize + DeserializeOwned + Clone + Send + Sync,
>: Clone
{
    type Iterator<'a>: Iterator<Item = (Cow<'a, K>, Cow<'a, V>)>
    where
        K: 'a,
        V: 'a,
        Self: 'a;

    fn insert(&self, key: K, value: V) -> Result<(), StorageError>;

    fn get<Q>(&self, key: &Q) -> Result<Option<V>, StorageError>
    where
        K: Borrow<Q>,
        Q: Serialize + ?Sized + ToOwned<Owned = K>;

    fn remove<Q>(&self, key: &Q) -> Result<(), StorageError>
    where
        K: Borrow<Q>,
        Q: Serialize + ?Sized + ToOwned<Owned = K>;

    fn len(&self) -> usize;

    fn is_empty(&self) -> bool {
        self.len() == 0
    }

    fn contain<Q>(&self, key: &Q) -> Result<bool, StorageError>
    where
        K: Borrow<Q>,
        Q: Serialize + ?Sized + ToOwned<Owned = K>;

    fn iter(&self) -> Self::Iterator<'_>;
}
