pub mod iter;
pub mod map;

use std::{
    hash::Hash,
    ops::Deref,
    sync::{Arc, OnceLock},
};

use rocksdb::SliceTransform;
use serde::{de::DeserializeOwned, Serialize};
use sha2::{Digest, Sha256};

use super::Store;

#[derive(Clone)]
pub struct RocksDB {
    inner: Arc<rocksdb::DB>,
}

impl Deref for RocksDB {
    type Target = rocksdb::DB;

    fn deref(&self) -> &Self::Target {
        &self.inner
    }
}

impl Store for RocksDB {
    type Map<K, V>
        = map::RocksDBMap<K, V>
    where
        K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
        V: Serialize + DeserializeOwned + Clone + Send + Sync;

    type Singleton<T>
        = map::RocksDBMap<(), T>
    where
        T: Serialize + DeserializeOwned + Clone + Send + Sync;

    fn open() -> Self
    where
        Self: Sized,
    {
        static DB: OnceLock<Arc<rocksdb::DB>> = OnceLock::new();

        let path = std::env::var("EMBEDED_PATH").unwrap_or_else(|_| ".superstack_data".to_string());
        let path = std::path::PathBuf::from(path);

        // Retrieve the database.
        let database = DB.get_or_init(|| {
            // Customize database options.
            let mut options = rocksdb::Options::default();
            options.set_compression_type(rocksdb::DBCompressionType::Lz4);
            // Set the prefix extractor to the first two bytes of the key.
            options.set_prefix_extractor(SliceTransform::create_fixed_prefix(32));
            {
                options.increase_parallelism(2);
                options.set_max_background_jobs(4);
                options.create_if_missing(true);

                Arc::new(rocksdb::DB::open(&options, path).expect("Failed to open RocksDB"))
            }
        });

        Self { inner: database.clone() }
    }

    fn open_map<K, V>(map_id: impl AsRef<str>) -> Self::Map<K, V>
    where
        K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
        V: Serialize + DeserializeOwned + Clone + Send + Sync,
    {
        let inner = Self::open();

        let mut hasher = Sha256::new();
        hasher.update(map_id.as_ref().as_bytes());
        let hash = hasher.finalize().into();

        map::RocksDBMap::new(inner, hash)
    }

    fn open_singleton<T>(prefix: impl AsRef<str>) -> Self::Singleton<T>
    where
        T: Serialize + DeserializeOwned + Clone + Send + Sync,
    {
        let inner = Self::open();

        let mut hasher = Sha256::new();
        hasher.update(prefix.as_ref().as_bytes());
        let hash = hasher.finalize().into();

        map::RocksDBMap::new(inner, hash)
    }
}
