use super::RocksDB;
use crate::{MerklizeStore, error::StorageError, merklize::MerklizeDB};
use std::sync::OnceLock;
use zkvm_merkle_btree::{DatabaseGet, DatabaseSet, NodeHash, NodeOrLeafDb};

impl MerklizeStore for RocksDB {
    fn open_merkle_db() -> MerklizeDB<Self>
    where
        Self: Sized,
    {
        static MDB: OnceLock<MerklizeDB<RocksDB>> = OnceLock::new();
        MDB.get_or_init(MerklizeDB::new).clone()
    }
}

impl DatabaseGet<Vec<u8>, Vec<u8>> for RocksDB {
    type GetError = StorageError;

    fn get(&self, hash: &NodeHash) -> Result<NodeOrLeafDb<Vec<u8>, Vec<u8>>, Self::GetError> {
        let key = bincode::serialize(hash)?;
        let key = [b"merkle_db", key.as_slice()].concat();

        let value = self.inner.get(&key)?;

        match value {
            Some(value) => Ok(bincode::deserialize(&value)?),
            None => Err(StorageError::NodeHashNotFound(*hash)),
        }
    }
}

impl DatabaseSet<Vec<u8>, Vec<u8>> for RocksDB {
    type SetError = StorageError;

    fn set(
        &self,
        hash: &NodeHash,
        node: NodeOrLeafDb<Vec<u8>, Vec<u8>>,
    ) -> Result<(), Self::SetError> {
        let key = bincode::serialize(hash)?;
        let key = [b"merkle_db", key.as_slice()].concat();
        let value = bincode::serialize(&node)?;

        self.inner.put(&key, &value)?;

        Ok(())
    }
}
