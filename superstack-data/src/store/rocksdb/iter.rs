use std::borrow::Cow;

use serde::{de::DeserializeOwned, Serialize};
use tracing::error;

pub struct RocksDBIter<
    'a,
    K: 'a + Serialize + DeserializeOwned + Clone,
    V: 'a + Serialize + DeserializeOwned + Clone,
> {
    prefix: &'a [u8],
    iter: rocksdb::DBRawIterator<'a>,
    phantom: std::marker::PhantomData<(&'a K, &'a V)>,
}

impl<
        'a,
        K: 'a + Serialize + DeserializeOwned + Clone,
        V: 'a + Serialize + DeserializeOwned + Clone,
    > RocksDBIter<'a, K, V>
{
    pub fn new(iter: rocksdb::DBRawIterator<'a>, prefix: &'a [u8]) -> Self {
        Self { prefix, iter, phantom: std::marker::PhantomData }
    }
}

impl<
        'a,
        K: 'a + Serialize + DeserializeOwned + Clone,
        V: 'a + Serialize + DeserializeOwned + Clone,
    > Iterator for RocksDBIter<'a, K, V>
{
    type Item = (Cow<'a, K>, Cow<'a, V>);

    fn next(&mut self) -> Option<Self::Item> {
        if !self.iter.valid() {
            return None;
        }

        let (key, value) = self.iter.item()?;

        let key = bincode::deserialize(&key[self.prefix.len()..])
            .map_err(|e| {
                error!("Store Iter deserialize(key) error: {e}");
            })
            .ok()?;

        let value = bincode::deserialize(value)
            .map_err(|e| {
                error!("Store Iter deserialize(value) error: {e}");
            })
            .ok()?;

        self.iter.next();
        Some((Cow::Owned(key), Cow::Owned(value)))
    }
}
