use serde::{de::DeserializeOwned, Serialize};
use std::{borrow::Borrow, hash::Hash, marker::PhantomData};

use crate::store::{error::StorageError, map::Map};

use super::{iter::RocksDBIter, RocksDB};

#[derive(Clone)]
pub struct RocksDBMap<K: Serialize + DeserializeOwned, V: Serialize + DeserializeOwned> {
    pub map_id: [u8; 32],
    pub inner: RocksDB,
    phantom: PhantomData<(K, V)>,
}

impl<K: Serialize + DeserializeOwned, V: Serialize + DeserializeOwned> RocksDBMap<K, V> {
    pub fn new(inner: RocksDB, map_id: [u8; 32]) -> Self {
        Self { map_id, inner, phantom: PhantomData }
    }
}

impl<
        K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
        V: Serialize + DeserializeOwned + Clone + Send + Sync,
    > Map<K, V> for RocksDBMap<K, V>
{
    type Iterator<'a>
        = RocksDBIter<'a, K, V>
    where
        K: 'a,
        V: 'a;

    fn get<Q>(&self, key: &Q) -> Result<Option<V>, StorageError>
    where
        K: Borrow<Q>,
        Q: Serialize + ?Sized,
    {
        let key = bincode::serialize(key)?;
        let key = [&self.map_id, key.as_slice()].concat();
        let value = self.inner.get(&key)?;

        match value {
            Some(value) => Ok(Some(bincode::deserialize(&value)?)),
            None => Ok(None),
        }
    }

    fn insert(&self, key: K, value: V) -> Result<(), StorageError> {
        let key = bincode::serialize(&key)?;
        let value = bincode::serialize(&value)?;
        let key = [&self.map_id, key.as_slice()].concat();
        self.inner.put(&key, &value)?;

        Ok(())
    }

    fn remove<Q>(&self, key: &Q) -> Result<(), StorageError>
    where
        K: Borrow<Q>,
        Q: Serialize + ?Sized,
    {
        let key = bincode::serialize(key)?;
        let key = [&self.map_id, key.as_slice()].concat();

        self.inner.delete(&key)?;

        Ok(())
    }

    fn iter(&self) -> Self::Iterator<'_> {
        let iter = self.inner.prefix_iterator(self.map_id);
        let iter = RocksDBIter::<K, V>::new(iter.into(), &self.map_id);

        iter
    }

    fn contain<Q>(&self, key: &Q) -> Result<bool, StorageError>
    where
        K: Borrow<Q>,
        Q: Serialize + ?Sized,
    {
        let key = bincode::serialize(key)?;
        let key = [&self.map_id, key.as_slice()].concat();

        Ok(self.inner.get(&key)?.is_some())
    }

    fn len(&self) -> usize {
        let mut iter = self.inner.raw_iterator();
        iter.seek(self.map_id);

        let mut len = 0usize;

        while iter.valid() {
            if let Some(key) = iter.key() {
                if !key.starts_with(&self.map_id) {
                    break;
                }

                len += 1;
                iter.next();
            } else {
                break;
            }
        }

        len
    }
}
