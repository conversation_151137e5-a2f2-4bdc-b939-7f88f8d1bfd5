pub mod error;
pub mod map;
pub mod mem;
pub mod singleton;

pub mod rocksdb;

use serde::{de::DeserializeOwned, Serialize};
use singleton::Singleton;
use std::hash::Hash;

pub trait Store: Clone + Send + Sync + 'static {
    type Map<K, V>: map::Map<K, V> + Send + Sync
    where
        K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
        V: Serialize + DeserializeOwned + Clone + Send + Sync;

    type Singleton<T>: Singleton<T> + Send + Sync
    where
        T: Serialize + DeserializeOwned + Clone + Send + Sync;

    fn open() -> Self
    where
        Self: Sized;

    fn open_map<K, V>(prefix: impl AsRef<str>) -> Self::Map<K, V>
    where
        K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
        V: Serialize + DeserializeOwned + Clone + Send + Sync;

    fn open_singleton<T>(prefix: impl AsRef<str>) -> Self::Singleton<T>
    where
        T: Serialize + DeserializeOwned + Clone + Send + Sync;

    // TODO(ethan): need to implement AtomicBatch
}
