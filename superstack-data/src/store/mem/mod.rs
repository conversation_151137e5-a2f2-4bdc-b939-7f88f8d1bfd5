pub mod map;

use map::{<PERSON><PERSON>ap, MemorySingleton};
use parking_lot::Mutex;
use serde::{de::DeserializeOwned, Serialize};
use std::{
    collections::HashMap,
    hash::Hash,
    sync::{Arc, OnceLock},
};

use super::Store;

type MemoryDBInner = Arc<Mutex<HashMap<Vec<u8>, Vec<u8>>>>;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct MemoryDB {
    inner: MemoryDBInner,
}

impl Store for MemoryDB {
    type Map<K, V>
        = MemoryMap<K, V>
    where
        K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
        V: Serialize + DeserializeOwned + Clone + Send + Sync;

    type Singleton<T>
        = MemorySingleton<T>
    where
        T: Serialize + DeserializeOwned + Clone + Send + Sync;

    fn open() -> Self {
        static DB: OnceLock<MemoryDB> = OnceLock::new();
        DB.get_or_init(MemoryDB::default).clone()
    }

    fn open_map<K, V>(prefix: impl AsRef<str>) -> Self::Map<K, V>
    where
        K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
        V: Serialize + DeserializeOwned + Clone + Send + Sync,
    {
        let db = Self::open();

        MemoryMap::new(db.inner.clone(), prefix.as_ref())
    }

    fn open_singleton<T>(prefix: impl AsRef<str>) -> Self::Singleton<T>
    where
        T: Serialize + DeserializeOwned + Clone + Send + Sync,
    {
        let db = Self::open();

        MemorySingleton::new(db.inner.clone(), prefix.as_ref())
    }
}
