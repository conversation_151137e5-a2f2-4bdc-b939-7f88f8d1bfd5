use std::{borrow::Cow, hash::Hash, marker::PhantomData};

use serde::{de::DeserializeOwned, Serialize};

use crate::store::{error::StorageError, map::Map};

use super::MemoryDBInner;

pub type MemorySingleton<T> = MemoryMap<(), T>;

#[derive(Clone)]
pub struct MemoryMap<K, V>
where
    K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
    V: Serialize + DeserializeOwned + Clone + Send + Sync,
{
    inner: MemoryDBInner,
    map_id: Box<[u8]>,
    phantom: PhantomData<(K, V)>,
}

impl<K, V> MemoryMap<K, V>
where
    K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
    V: Serialize + DeserializeOwned + Clone + Send + Sync,
{
    pub fn new(inner: MemoryDBInner, map_id: impl AsRef<[u8]>) -> Self {
        Self {
            inner,
            map_id: map_id.as_ref().to_vec().into_boxed_slice(),
            phantom: Default::default(),
        }
    }
}

impl<K, V> Map<K, V> for MemoryMap<K, V>
where
    K: Serialize + DeserializeOwned + Clone + Send + Sync + Eq + Hash,
    V: Serialize + DeserializeOwned + Clone + Send + Sync,
{
    type Iterator<'a>
        = std::vec::IntoIter<(Cow<'a, K>, Cow<'a, V>)>
    where
        K: 'a,
        V: 'a,
        Self: 'a;

    fn get<Q>(&self, key: &Q) -> Result<Option<V>, StorageError>
    where
        K: std::borrow::Borrow<Q>,
        Q: Serialize + ?Sized + ToOwned<Owned = K>,
    {
        let key = bincode::serialize(key)?;
        let key = [&self.map_id, key.as_slice()].concat();

        let inner = self.inner.lock();
        let value = inner.get(&key);

        match value {
            Some(value) => Ok(Some(bincode::deserialize(value)?)),
            None => Ok(None),
        }
    }

    fn contain<Q>(&self, key: &Q) -> Result<bool, StorageError>
    where
        K: std::borrow::Borrow<Q>,
        Q: Serialize + ?Sized + ToOwned<Owned = K>,
    {
        let key = bincode::serialize(key)?;
        let key = [&self.map_id, key.as_slice()].concat();

        let inner = self.inner.lock();

        Ok(inner.contains_key(&key))
    }

    fn insert(&self, key: K, value: V) -> Result<(), StorageError> {
        let key = bincode::serialize(&key)?;
        let value = bincode::serialize(&value)?;
        let key = [&self.map_id, key.as_slice()].concat();

        let mut inner = self.inner.lock();
        inner.insert(key, value);

        Ok(())
    }

    fn remove<Q>(&self, key: &Q) -> Result<(), StorageError>
    where
        K: std::borrow::Borrow<Q>,
        Q: Serialize + ?Sized + ToOwned<Owned = K>,
    {
        let key = bincode::serialize(key)?;
        let key = [&self.map_id, key.as_slice()].concat();

        let mut inner = self.inner.lock();

        inner.remove(&key);

        Ok(())
    }

    fn len(&self) -> usize {
        let inner = self.inner.lock();
        inner.len()
    }

    fn iter(&self) -> Self::Iterator<'_> {
        let inner = self.inner.lock();
        let iter = inner
            .iter()
            .filter_map(|(k, v)| {
                match (bincode::deserialize::<K>(k), bincode::deserialize::<V>(v)) {
                    (Ok(k), Ok(v)) => Some((Cow::Owned(k), Cow::Owned(v))),
                    _ => None,
                }
            })
            .collect::<Vec<_>>();

        iter.into_iter()
    }
}
