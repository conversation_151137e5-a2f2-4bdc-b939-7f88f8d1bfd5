use std::{str::FromStr, sync::OnceLock, time::Duration};

use alloy::primitives::U256;
use bigdecimal::BigDecimal;
use reqwest::Client;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::{config::Config, postgres::Chain, price::NativeTokenPriceManager};

pub fn setup_tracing() {
    let _ = tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
        ))
        .with(tracing_subscriber::fmt::layer())
        .try_init();
}

pub fn get_reqwest_client() -> &'static Client {
    static CLIENT: OnceLock<Client> = OnceLock::new();
    CLIENT.get_or_init(Client::new)
}

pub fn get_confirmed_rpc_client() -> &'static RpcClient {
    static RPC_CLIENT: OnceLock<RpcClient> = OnceLock::new();
    RPC_CLIENT.get_or_init(|| {
        let solana_rpc_url = Config::get().solana_rpc_url.clone();
        RpcClient::new_with_timeout_and_commitment(
            solana_rpc_url,
            Duration::from_secs(5),
            CommitmentConfig::confirmed(),
        )
    })
}

pub fn to_ui_amount(amount: u64, decimals: u8) -> f64 {
    amount as f64 / (10_u64.pow(decimals as u32) as f64)
}

pub fn big_decimal_to_u256(amount: BigDecimal) -> Result<U256, crate::Error> {
    // Convert to integer representation to avoid scientific notation
    // with_scale(0) removes any fractional parts and ensures we get a plain integer
    let integer_decimal = amount.with_scale(0);
    let string_repr = integer_decimal.to_string();

    Ok(U256::from_str(&string_repr)
        .map_err(|e| anyhow::anyhow!("Failed to parse supply to u256: {}", e))?)
}

pub fn u256_to_big_decimal(amount: U256) -> Result<BigDecimal, crate::Error> {
    Ok(BigDecimal::from_str(&amount.to_string())
        .map_err(|e| anyhow::anyhow!("Failed to parse supply to u256: {}", e))?)
}

/// Get base token USD price for a given chain and timestamp
pub async fn get_base_token_usd_price(
    chain: Chain,
    base_address: &str,
    timestamp_secs: i64,
    native_token_usd_price: Option<f64>,
) -> Result<f64, crate::Error> {
    match chain {
        Chain::Solana => match base_address {
            crate::constant::solana::SOL_MINT => {
                if let Some(price) = native_token_usd_price {
                    Ok(price)
                } else {
                    let price_manager = NativeTokenPriceManager::get().await;
                    let price = price_manager
                        .get_nearest_price(chain, timestamp_secs)
                        .await
                        .ok_or_else(|| {
                            crate::Error::AnyhowError(anyhow::anyhow!(
                                "No price found for chain: {:?}",
                                chain
                            ))
                        })?;
                    Ok(price)
                }
            }
            crate::constant::solana::USDC_MINT => Ok(1.0),
            crate::constant::solana::USDT_MINT => Ok(1.0),
            _ => Err(crate::Error::AnyhowError(anyhow::anyhow!(
                "Unsupported base address: {:?}",
                base_address
            ))),
        },
        Chain::Hypercore => {
            // For Hypercore, all base tokens are USD-based (USDC or similar)
            // Log the base address for debugging but always return 1.0
            tracing::debug!("Hypercore base token: {}", base_address);
            Ok(1.0)
        }
        Chain::HyperEvm => {
            // For HyperEvm, check if base token is HYPE (native token)
            if base_address == "0x5555555555555555555555555555555555555555" {
                // HYPE token - use native token price
                if let Some(price) = native_token_usd_price {
                    Ok(price)
                } else {
                    let price_manager = NativeTokenPriceManager::get().await;
                    let price = price_manager
                        .get_nearest_price(chain, timestamp_secs)
                        .await
                        .ok_or_else(|| {
                            crate::Error::AnyhowError(anyhow::anyhow!(
                                "No HYPE price found for chain: {:?}",
                                chain
                            ))
                        })?;
                    Ok(price)
                }
            } else {
                // Other tokens - assume USD-based for now
                tracing::debug!(
                    "HyperEvm non-HYPE base token: {}, defaulting to 1.0 USD",
                    base_address
                );
                Ok(1.0)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    #[test]
    fn test_u256_to_big_decimal_basic() {
        // Test basic conversion
        let u256_val = U256::from(12345u64);
        let big_decimal_result = u256_to_big_decimal(u256_val).unwrap();
        let expected = BigDecimal::from_str("12345").unwrap();
        assert_eq!(big_decimal_result, expected);
    }

    #[test]
    fn test_big_decimal_to_u256_basic() {
        // Test basic conversion
        let big_decimal_val = BigDecimal::from_str("12345").unwrap();
        let u256_result = big_decimal_to_u256(big_decimal_val).unwrap();
        let expected = U256::from(12345u64);
        assert_eq!(u256_result, expected);
    }

    #[test]
    fn test_round_trip_conversion_small_numbers() {
        // Test round trip conversion for small numbers
        let original = U256::from(123456789u64);
        let big_decimal = u256_to_big_decimal(original).unwrap();
        let converted_back = big_decimal_to_u256(big_decimal).unwrap();
        assert_eq!(original, converted_back);
    }

    #[test]
    fn test_round_trip_conversion_large_numbers() {
        // Test round trip conversion for large numbers
        let original = U256::from_str("100000000000000000000000000").unwrap(); // 100 million with 18 decimals
        let big_decimal = u256_to_big_decimal(original).unwrap();
        let converted_back = big_decimal_to_u256(big_decimal).unwrap();
        assert_eq!(original, converted_back);
    }

    #[test]
    fn test_token_supply_like_values() {
        // Test values that are similar to token supplies
        let token_supplies = vec![
            "1000000000000000000000000000", // 1 billion with 18 decimals
            "21000000000000000000000000",   /* 21 million with 18 decimals (like Bitcoin max
                                             * supply) */
            "1000000000000000000",      // 1 token with 18 decimals
            "500000000000000000000000", // 500k tokens with 18 decimals
        ];

        for supply_str in token_supplies {
            let original = U256::from_str(supply_str).unwrap();
            let big_decimal = u256_to_big_decimal(original).unwrap();
            let converted_back = big_decimal_to_u256(big_decimal).unwrap();
            assert_eq!(original, converted_back, "Failed for supply: {}", supply_str);
        }
    }

    #[test]
    fn test_big_decimal_with_negative_scale() {
        // Test the specific case that was failing: BigDecimal with negative scale
        // This simulates what happens when we have a BigDecimal like BigDecimal(sign=Plus,
        // scale=-24, digits=[100])
        let big_decimal = BigDecimal::from_str("100000000000000000000000000").unwrap();

        // Convert to U256 and back
        let u256_result = big_decimal_to_u256(big_decimal.clone()).unwrap();
        let big_decimal_back = u256_to_big_decimal(u256_result).unwrap();

        // They should be equal when normalized
        assert_eq!(big_decimal.normalized(), big_decimal_back.normalized());
    }

    #[test]
    fn test_big_decimal_with_fractional_parts() {
        // Test BigDecimal with fractional parts (should be truncated)
        let big_decimal = BigDecimal::from_str("12345.67890").unwrap();
        let u256_result = big_decimal_to_u256(big_decimal).unwrap();
        let expected = U256::from(12345u64); // Fractional part should be removed
        assert_eq!(u256_result, expected);
    }

    #[test]
    fn test_zero_values() {
        // Test zero values
        let zero_u256 = U256::ZERO;
        let zero_big_decimal = u256_to_big_decimal(zero_u256).unwrap();
        let back_to_u256 = big_decimal_to_u256(zero_big_decimal).unwrap();
        assert_eq!(zero_u256, back_to_u256);
    }

    #[test]
    fn test_max_u64_value() {
        // Test maximum u64 value
        let max_u64 = U256::from(u64::MAX);
        let big_decimal = u256_to_big_decimal(max_u64).unwrap();
        let converted_back = big_decimal_to_u256(big_decimal).unwrap();
        assert_eq!(max_u64, converted_back);
    }

    #[test]
    fn test_scientific_notation_handling() {
        // Test that we properly handle values that might be represented in scientific notation
        let large_val = U256::from_str("1000000000000000000000000000000000000000").unwrap();
        let big_decimal = u256_to_big_decimal(large_val).unwrap();

        // Ensure the string representation doesn't use scientific notation
        let string_repr = big_decimal.with_scale(0).to_string();
        assert!(!string_repr.contains('e') && !string_repr.contains('E'));

        let converted_back = big_decimal_to_u256(big_decimal).unwrap();
        assert_eq!(large_val, converted_back);
    }

    #[test]
    fn test_decimal_normalization() {
        // Test that normalization works correctly
        let big_decimal1 = BigDecimal::from_str("1000").unwrap();
        let big_decimal2 = BigDecimal::from_str("1000.0").unwrap();
        let big_decimal3 = BigDecimal::from_str("1000.00").unwrap();

        let u256_1 = big_decimal_to_u256(big_decimal1).unwrap();
        let u256_2 = big_decimal_to_u256(big_decimal2).unwrap();
        let u256_3 = big_decimal_to_u256(big_decimal3).unwrap();

        assert_eq!(u256_1, u256_2);
        assert_eq!(u256_2, u256_3);
        assert_eq!(u256_1, U256::from(1000u64));
    }
}
