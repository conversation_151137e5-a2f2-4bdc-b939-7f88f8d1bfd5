use serde::{de::DeserializeOwned, Serialize};

pub trait Topic {
    type Message: 'static + Serialize + DeserializeOwned + Sync + Send;

    fn topic() -> TopicType;
}

pub enum TopicType {
    // Indexer topics
    TokenMetadata,
    PoolMetadata,
    PoolState,
    LatestPoolState,
    DexTrade,
    TokenHolder,

    // Aggregator topics
    TokenState,
    LatestTokenState,
    PoolStatistics,
    TokenStatistics,
    MemeStatistics,
    Maker,
    Candle,

    #[cfg(test)]
    Test,
}

impl ToString for TopicType {
    fn to_string(&self) -> String {
        match self {
            TopicType::TokenMetadata => "token_metadata".to_string(),
            TopicType::PoolMetadata => "pool_metadata".to_string(),
            TopicType::PoolState => "pool_state".to_string(),
            TopicType::LatestPoolState => "latest_pool_state".to_string(),
            TopicType::DexTrade => "dex_trade".to_string(),
            TopicType::TokenHolder => "token_holder".to_string(),
            TopicType::TokenState => "token_state".to_string(),
            TopicType::LatestTokenState => "latest_token_state".to_string(),
            TopicType::PoolStatistics => "pool_statistics".to_string(),
            TopicType::TokenStatistics => "token_statistics".to_string(),
            TopicType::MemeStatistics => "meme_statistics".to_string(),
            TopicType::Maker => "maker".to_string(),
            TopicType::Candle => "candle".to_string(),
            #[cfg(test)]
            TopicType::Test => "test".to_string(),
        }
    }
}

#[cfg(test)]
#[derive(Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq)]
pub struct TestMsg {
    pub message: String,
}

#[cfg(test)]
impl Topic for TestMsg {
    type Message = TestMsg;

    fn topic() -> TopicType {
        TopicType::Test
    }
}
