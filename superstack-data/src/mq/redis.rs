use futures::{stream::BoxStream, StreamExt};
use redis::{AsyncCommands, Client};
use tokio::sync::mpsc;
use tokio_stream;
use tracing;

use super::{error::Error, message::Topic, PubSub};

#[derive(<PERSON>bu<PERSON>, Clone)]
pub struct RedisPubSub {
    pub client: Client,
}

impl RedisPubSub {
    pub fn new(url: &str) -> anyhow::Result<Self> {
        let client = Client::open(url)?;
        Ok(Self { client })
    }
}

impl PubSub for RedisPubSub {
    #[allow(dependency_on_unit_never_type_fallback)]
    async fn publish<T: Topic>(&self, message: &T::Message) -> Result<(), Error> {
        let mut conn = self.client.get_multiplexed_tokio_connection().await?;
        let topic = T::topic().to_string();
        let json = serde_json::to_string(message)?;

        conn.publish(topic, json).await?;
        Ok(())
    }

    async fn subscribe<T: Topic>(&self) -> Result<BoxStream<T::Message>, Error> {
        let mut pubsub = self.client.get_async_pubsub().await?;
        let topic = T::topic().to_string();
        pubsub.subscribe(topic.clone()).await?;

        let (tx, rx) = mpsc::channel(128);

        tokio::spawn(async move {
            while let Some(msg) = pubsub.on_message().next().await {
                if let Ok(payload) = msg.get_payload::<String>() {
                    match serde_json::from_str::<T::Message>(&payload) {
                        Ok(parsed_msg) => {
                            if tx.send(parsed_msg).await.is_err() {
                                break;
                            }
                        }
                        Err(e) => {
                            tracing::error!("Failed to deserialize message: {}", e);
                        }
                    }
                }
            }

            tracing::error!("Unsubscribed from topic: {}", topic);
            pubsub.unsubscribe(topic).await.expect("Failed to unsubscribe from topic");
        });

        Ok(tokio_stream::wrappers::ReceiverStream::new(rx).boxed())
    }
}

#[cfg(test)]
mod tests {
    use futures::StreamExt;

    use crate::mq::{message::TestMsg, PubSub};

    #[tokio::test]
    async fn test_publish() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis = crate::utils::get_pubsub();

        let msg = TestMsg { message: "Hello, world!".to_string() };

        let mut sub = redis.subscribe::<TestMsg>().await.unwrap();

        redis.publish::<TestMsg>(&msg).await.unwrap();

        let received_msg = sub.next().await.unwrap();
        assert_eq!(received_msg, msg);
    }

    #[tokio::test]
    async fn test_publish_multiple() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis = crate::utils::get_pubsub();

        let mut sub = redis.subscribe::<TestMsg>().await.unwrap();

        let msgs = (0..10)
            .map(|i| TestMsg { message: format!("Hello, world! {}", i) })
            .collect::<Vec<_>>();

        for msg in &msgs {
            redis.publish::<TestMsg>(msg).await.unwrap();
        }

        for _ in 0..msgs.len() {
            let msg = sub.next().await.unwrap();
            assert!(msgs.contains(&msg));
        }
    }
}
