use sqlx::{postgres::PgPoolOptions, PgPool};
use tokio::sync::OnceCell;

use crate::{config::Config, error::Result};

#[derive(Debug, <PERSON><PERSON>)]
pub struct PostgresDatabase {
    pub pool: PgPool,
}

impl PostgresDatabase {
    pub async fn new(
        database_url: &str,
        max_db_connections: u32,
        need_migrate: bool,
    ) -> Result<Self> {
        let min_connections = (max_db_connections / 4).max(10);
        let pool = PgPoolOptions::new()
            .max_connections(max_db_connections)
            .min_connections(min_connections)
            .connect(database_url)
            .await?;

        // Run migrations
        if need_migrate {
            sqlx::migrate!("./migrations").run(&pool).await?;
        }

        Ok(Self { pool })
    }

    pub async fn get_indexer_db() -> &'static Self {
        static INSTANCE: OnceCell<PostgresDatabase> = OnceCell::const_new();
        INSTANCE
            .get_or_init(|| async {
                let config = Config::get();
                let database_url = config.postgres_indexer_database_url.clone();
                let max_connections = config.postgres_indexer_max_connections;
                let need_migrate = config.postgres_indexer_need_migrate;
                tracing::info!(
                    "Initializing indexer database with max connections: {}, need migrate: {}",
                    max_connections,
                    need_migrate
                );
                let database = PostgresDatabase::new(&database_url, max_connections, need_migrate)
                    .await
                    .expect("Failed to initialize indexer database");
                tracing::info!("Initialized indexer database successfully");
                database
            })
            .await
    }
}

#[cfg(test)]
pub(crate) mod tests {
    use super::*;

    pub async fn get_postgres_database() -> PostgresDatabase {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let database_url = std::env::var("DATABASE_URL").expect("DATABASE_URL is not set");
        let max_db_connections = 10;
        let need_migrate = true;
        let database =
            PostgresDatabase::new(&database_url, max_db_connections, need_migrate).await.unwrap();
        database
    }

    #[tokio::test]
    async fn test_get_postgres_database() {
        let _database = get_postgres_database().await;
    }
}
