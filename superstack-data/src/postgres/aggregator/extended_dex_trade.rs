use serde::{Deserialize, Serialize};

use crate::{postgres::*, Error};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct ExtendedDexTrade {
    pub chain: Chain,
    pub tx_hash: String,
    pub ix_idx: u32,
    pub tx_idx: u32,

    pub pool_address: String,
    pub maker_address: String,

    pub is_buy: bool,
    pub token_ui_amount: f64,
    pub base_ui_amount: f64,

    pub usd: f64,
    pub usd_price: f64,
    pub usd_market_cap: f64,

    pub block_number: u64,
    pub timestamp_millis: i64,

    pub maker_volume_type: MakerVolumeType,
}

impl PostgresDatabase {
    pub async fn insert_extended_dex_trade(
        &self,
        extended_dex_trade: &ExtendedDexTrade,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO extended_dex_trade (
                chain, tx_hash, ix_idx,
                pool_address, maker_address,
                is_buy, token_ui_amount, base_ui_amount,
                usd, usd_price, usd_market_cap,
                block_number, timestamp_millis,
                maker_volume_type,
                tx_idx
            )
            VALUES (
                $1, $2, $3,
                $4, $5,
                $6, $7, $8,
                $9, $10, $11,
                $12, $13,
                $14,
                $15
            )
            ON CONFLICT (chain, tx_hash, ix_idx) DO NOTHING
            "#,
            extended_dex_trade.chain as Chain,
            extended_dex_trade.tx_hash,
            extended_dex_trade.ix_idx as i32,
            extended_dex_trade.pool_address,
            extended_dex_trade.maker_address,
            extended_dex_trade.is_buy,
            extended_dex_trade.token_ui_amount,
            extended_dex_trade.base_ui_amount,
            extended_dex_trade.usd,
            extended_dex_trade.usd_price,
            extended_dex_trade.usd_market_cap,
            extended_dex_trade.block_number as i64,
            extended_dex_trade.timestamp_millis,
            extended_dex_trade.maker_volume_type as MakerVolumeType,
            extended_dex_trade.tx_idx as i32,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_extended_dex_trades(
        &self,
        chain: Chain,
        pool_address: &str,
        start_timestamp_millis: i64,
        end_timestamp_millis: i64,
        limit: u64,
    ) -> Result<Vec<ExtendedDexTrade>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT 
                chain AS "chain: Chain",
                tx_hash,
                ix_idx,
                pool_address,
                maker_address,
                is_buy,
                token_ui_amount,
                base_ui_amount,
                usd,
                usd_price,
                usd_market_cap,
                block_number,
                timestamp_millis,
                maker_volume_type AS "maker_volume_type: MakerVolumeType",
                tx_idx
            FROM extended_dex_trade
            WHERE chain = $1 AND pool_address = $2
            AND timestamp_millis >= $3 AND timestamp_millis <= $4
            ORDER BY timestamp_millis DESC, block_number DESC, tx_idx DESC, ix_idx DESC
            LIMIT $5
            "#,
            chain as Chain,
            pool_address,
            start_timestamp_millis,
            end_timestamp_millis,
            limit as i64,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut candles = Vec::with_capacity(rows.len());
        for row in rows {
            candles.push(ExtendedDexTrade {
                chain: row.chain,
                tx_hash: row.tx_hash,
                ix_idx: row.ix_idx as u32,
                tx_idx: row.tx_idx as u32,
                pool_address: row.pool_address,
                maker_address: row.maker_address,
                is_buy: row.is_buy,
                token_ui_amount: row.token_ui_amount,
                base_ui_amount: row.base_ui_amount,
                usd: row.usd,
                usd_price: row.usd_price,
                usd_market_cap: row.usd_market_cap,
                block_number: row.block_number as u64,
                timestamp_millis: row.timestamp_millis,
                maker_volume_type: row.maker_volume_type,
            });
        }

        Ok(candles)
    }

    pub async fn insert_extended_dex_trades(
        &self,
        extended_dex_trades: &[ExtendedDexTrade],
    ) -> Result<(), Error> {
        let len = extended_dex_trades.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_extended_dex_trade(&extended_dex_trades[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = extended_dex_trades.iter().map(|c| c.chain).collect();
        let tx_hashes: Vec<String> =
            extended_dex_trades.iter().map(|c| c.tx_hash.clone()).collect();
        let ix_idxs: Vec<i32> = extended_dex_trades.iter().map(|c| c.ix_idx as i32).collect();
        let pool_addresses: Vec<String> =
            extended_dex_trades.iter().map(|c| c.pool_address.clone()).collect();
        let maker_addresses: Vec<String> =
            extended_dex_trades.iter().map(|c| c.maker_address.clone()).collect();
        let is_buys: Vec<bool> = extended_dex_trades.iter().map(|c| c.is_buy).collect();
        let token_ui_amounts: Vec<f64> =
            extended_dex_trades.iter().map(|c| c.token_ui_amount).collect();
        let base_ui_amounts: Vec<f64> =
            extended_dex_trades.iter().map(|c| c.base_ui_amount).collect();
        let usds: Vec<f64> = extended_dex_trades.iter().map(|c| c.usd).collect();
        let usd_prices: Vec<f64> = extended_dex_trades.iter().map(|c| c.usd_price).collect();
        let usd_market_caps: Vec<f64> =
            extended_dex_trades.iter().map(|c| c.usd_market_cap).collect();
        let block_numbers: Vec<i64> =
            extended_dex_trades.iter().map(|c| c.block_number as i64).collect();
        let timestamp_millis: Vec<i64> =
            extended_dex_trades.iter().map(|c| c.timestamp_millis).collect();
        let maker_volume_types: Vec<MakerVolumeType> =
            extended_dex_trades.iter().map(|c| c.maker_volume_type).collect();
        let tx_idxs: Vec<i32> = extended_dex_trades.iter().map(|c| c.tx_idx as i32).collect();

        sqlx::query!(
            r#"
            INSERT INTO extended_dex_trade (
                chain, tx_hash, ix_idx,
                pool_address, maker_address,
                is_buy, token_ui_amount, base_ui_amount,
                usd, usd_price, usd_market_cap,
                block_number, timestamp_millis,
                maker_volume_type,
                tx_idx
            )
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[], $3::integer[],
                $4::varchar[], $5::varchar[],
                $6::boolean[], $7::double precision[], $8::double precision[],
                $9::double precision[], $10::double precision[], $11::double precision[],
                $12::bigint[], $13::bigint[],
                $14::maker_volume_type_enum[],
                $15::integer[])
            ON CONFLICT (chain, tx_hash, ix_idx) DO NOTHING
            "#,
            &chains as &[Chain],
            &tx_hashes as &[String],
            &ix_idxs as &[i32],
            &pool_addresses as &[String],
            &maker_addresses as &[String],
            &is_buys as &[bool],
            &token_ui_amounts as &[f64],
            &base_ui_amounts as &[f64],
            &usds as &[f64],
            &usd_prices as &[f64],
            &usd_market_caps as &[f64],
            &block_numbers as &[i64],
            &timestamp_millis as &[i64],
            &maker_volume_types as &[MakerVolumeType],
            &tx_idxs as &[i32],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}
