use std::collections::BTreeMap;

use serde::{Deserialize, Serialize};

use crate::{
    kafka::topics::CandlesMsg,
    postgres::{indexer::PoolState, *},
    Error,
};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Candle {
    pub chain: Chain,
    pub pool_address: String,

    pub open_timestamp_seconds: i64,
    pub close_timestamp_seconds: i64,

    pub interval: CandleInterval,

    pub native_token_usd_price: f64,

    pub usd_open_price: f64,
    pub usd_close_price: f64,
    pub usd_high_price: f64,
    pub usd_low_price: f64,

    pub usd_open_market_cap: f64,
    pub usd_close_market_cap: f64,
    pub usd_high_market_cap: f64,
    pub usd_low_market_cap: f64,

    pub usd_volume: f64,
    pub txns: u64,
}

impl Candle {
    /// Fill missing candles between actual candles with appropriate values
    pub fn fill_gaps(mut actual_candles: Vec<Candle>, interval: CandleInterval) -> Vec<Candle> {
        if actual_candles.len() <= 1 {
            return actual_candles;
        }

        // Sort actual candles by timestamp
        actual_candles.sort_by_key(|c| c.open_timestamp_seconds);

        let mut result = Vec::new();
        let interval_seconds = interval.as_seconds();

        for i in 0..actual_candles.len() {
            // Add current actual candle
            result.push(actual_candles[i].clone());

            // Check if we need to fill gap to next candle
            if i < actual_candles.len() - 1 {
                let current_close = actual_candles[i].close_timestamp_seconds;
                let next_open = actual_candles[i + 1].open_timestamp_seconds;

                // Fill gaps between current and next candle
                let mut fill_timestamp = current_close;
                while fill_timestamp < next_open {
                    let missing_candle = Candle {
                        chain: actual_candles[i].chain,
                        pool_address: actual_candles[i].pool_address.clone(),
                        open_timestamp_seconds: fill_timestamp,
                        close_timestamp_seconds: fill_timestamp + interval_seconds,
                        interval: actual_candles[i].interval,
                        native_token_usd_price: actual_candles[i].native_token_usd_price,
                        usd_open_price: actual_candles[i].usd_close_price,
                        usd_close_price: actual_candles[i].usd_close_price,
                        usd_high_price: actual_candles[i].usd_close_price,
                        usd_low_price: actual_candles[i].usd_close_price,
                        usd_open_market_cap: actual_candles[i].usd_close_market_cap,
                        usd_close_market_cap: actual_candles[i].usd_close_market_cap,
                        usd_high_market_cap: actual_candles[i].usd_close_market_cap,
                        usd_low_market_cap: actual_candles[i].usd_close_market_cap,
                        usd_volume: 0.0,
                        txns: 0,
                    };

                    result.push(missing_candle);
                    fill_timestamp += interval_seconds;
                }
            }
        }

        result
    }

    /// accmulate poolstates into candles by second
    pub fn acc_states(
        poolstates: &[PoolState],
        avg_base_usd_price: f64,
        avg_native_token_usd_price: f64,
    ) -> Result<Vec<Self>, Error> {
        if poolstates.is_empty() {
            tracing::debug!("No pool states provided for candle generation");
            return Ok(vec![]);
        }

        tracing::debug!(
            "Generating candles from {} pool states with base_usd_price={}, native_usd_price={}",
            poolstates.len(),
            avg_base_usd_price,
            avg_native_token_usd_price
        );

        if poolstates.iter().any(|s| s.pool_address != poolstates[0].pool_address) {
            return Err(Error::AnyhowError(anyhow::anyhow!("pool address mismatch")));
        }

        // Group PoolStates by second timestamp
        let mut candles_map: BTreeMap<i64, Vec<&PoolState>> = BTreeMap::new();

        // Group all states by second
        for state in poolstates {
            let timestamp_seconds = state.timestamp_millis / 1000;
            candles_map.entry(timestamp_seconds).or_default().push(state);
        }

        tracing::debug!("Grouped pool states into {} second-level buckets", candles_map.len());

        // Create 1-second candles from grouped states
        let mut candles = Vec::new();
        for (timestamp, states) in candles_map.into_iter() {
            if states.is_empty() {
                continue;
            }

            let first = states.first().unwrap();

            // Calculate high and low prices
            let mut high_price = first.price;
            let mut low_price = first.price;
            let mut high_market_cap = first.market_cap;
            let mut low_market_cap = first.market_cap;

            for state in &states {
                high_price = high_price.max(state.price);
                low_price = low_price.min(state.price);
                high_market_cap = high_market_cap.max(state.market_cap);
                low_market_cap = low_market_cap.min(state.market_cap);
            }

            // Volume and trades should be the difference between last and first cumulative values
            // Sort states by slot to ensure correct chronological order within the same second
            let mut sorted_states: Vec<_> = states.iter().collect();
            sorted_states.sort_by_key(|s| s.block_number);

            let first_state = sorted_states.first().unwrap();
            let last_state = sorted_states.last().unwrap();

            // Calculate volume and transaction changes with validation
            let volume_change = if last_state.total_volume >= first_state.total_volume {
                last_state.total_volume - first_state.total_volume
            } else {
                tracing::warn!(
                    "Negative volume change detected at timestamp {}: {} -> {}",
                    timestamp,
                    first_state.total_volume,
                    last_state.total_volume
                );
                0.0 // Ensure non-negative
            };

            let trades_change = if last_state.total_txns >= first_state.total_txns {
                last_state.total_txns - first_state.total_txns
            } else {
                tracing::warn!(
                    "Negative transaction change detected at timestamp {}: {} -> {}",
                    timestamp,
                    first_state.total_txns,
                    last_state.total_txns
                );
                0 // Ensure non-negative
            };

            let candle = Candle {
                chain: first.chain,
                pool_address: first.pool_address.clone(),
                open_timestamp_seconds: timestamp,
                close_timestamp_seconds: timestamp + 1,
                interval: CandleInterval::S1,
                native_token_usd_price: avg_native_token_usd_price,
                usd_open_price: first_state.price * avg_base_usd_price,
                usd_close_price: last_state.price * avg_base_usd_price,
                usd_high_price: high_price * avg_base_usd_price,
                usd_low_price: low_price * avg_base_usd_price,
                usd_volume: volume_change * avg_base_usd_price,
                txns: trades_change as u64,
                usd_open_market_cap: first_state.market_cap * avg_base_usd_price,
                usd_close_market_cap: last_state.market_cap * avg_base_usd_price,
                usd_high_market_cap: high_market_cap * avg_base_usd_price,
                usd_low_market_cap: low_market_cap * avg_base_usd_price,
            };

            candles.push(candle);
        }

        // Sort result by open timestamp
        candles.sort_by_key(|c| c.open_timestamp_seconds);

        tracing::debug!(
            "Successfully generated {} candles from {} pool states",
            candles.len(),
            poolstates.len()
        );

        Ok(candles)
    }

    // just for lazy calculation
    pub fn acc(candles: &[Self], interval: CandleInterval) -> Vec<Self> {
        if candles.is_empty() {
            return Vec::new();
        }

        // Get interval in seconds
        let interval_seconds = interval.as_seconds();
        // If already at the requested interval, return as is
        if interval == CandleInterval::S1 {
            return candles.to_vec();
        }

        // Group candles by interval
        let mut grouped_candles: BTreeMap<i64, Vec<&Self>> = BTreeMap::new();

        for candle in candles {
            // Calculate the interval bucket this candle belongs to
            // Truncate timestamp to interval boundary
            let interval_timestamp =
                (candle.open_timestamp_seconds / interval_seconds) * interval_seconds;
            grouped_candles.entry(interval_timestamp).or_default().push(candle);
        }

        // Aggregate candles within each interval
        let mut result = Vec::new();

        for (timestamp, interval_candles) in grouped_candles {
            if interval_candles.is_empty() {
                continue;
            }

            // Sort candles by timestamp to ensure correct order
            let mut interval_candles = interval_candles.clone();
            interval_candles.sort_by_key(|c| c.open_timestamp_seconds);

            let first = interval_candles.first().unwrap();
            let last = interval_candles.last().unwrap();

            // Calculate average native token price for this interval
            let mut interval_avg_native_price = 0.0;
            for candle in &interval_candles {
                interval_avg_native_price += candle.native_token_usd_price;
            }
            interval_avg_native_price /= interval_candles.len() as f64;

            // Find high and low prices across all candles in this interval
            let mut usd_high_price = first.usd_high_price;
            let mut usd_low_price = first.usd_low_price;
            let mut usd_high_market_cap = first.usd_high_market_cap;
            let mut usd_low_market_cap = first.usd_low_market_cap;
            let mut usd_volume: f64 = 0.0;
            let mut txns = 0;

            for candle in &interval_candles {
                usd_high_price = usd_high_price.max(candle.usd_high_price);
                usd_low_price = usd_low_price.min(candle.usd_low_price);
                usd_volume += candle.usd_volume;
                txns += candle.txns;
                usd_high_market_cap = usd_high_market_cap.max(candle.usd_high_market_cap);
                usd_low_market_cap = usd_low_market_cap.min(candle.usd_low_market_cap);
            }

            result.push(Candle {
                chain: first.chain,
                pool_address: first.pool_address.clone(),
                open_timestamp_seconds: timestamp,
                close_timestamp_seconds: timestamp + interval_seconds,
                native_token_usd_price: interval_avg_native_price,
                interval,
                usd_open_price: first.usd_open_price,
                usd_close_price: last.usd_close_price,
                usd_high_price,
                usd_low_price,
                usd_volume,
                txns,
                usd_open_market_cap: first.usd_open_market_cap,
                usd_close_market_cap: last.usd_close_market_cap,
                usd_high_market_cap,
                usd_low_market_cap,
            });
        }

        // Sort result by open timestamp
        result.sort_by_key(|c| c.open_timestamp_seconds);

        result
    }
}

impl PostgresDatabase {
    pub async fn insert_or_update_candle(&self, candle: &Candle) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO candle (
                chain, pool_address,
                open_timestamp_seconds, close_timestamp_seconds,
                interval,
                native_token_usd_price,
                usd_open_price, usd_close_price, usd_high_price, usd_low_price,
                usd_open_market_cap, usd_close_market_cap, usd_high_market_cap, usd_low_market_cap,
                usd_volume, txns
            )
            VALUES (
                $1, $2,
                $3, $4,
                $5,
                $6,
                $7, $8, $9, $10,
                $11, $12, $13, $14,
                $15, $16
            )
            ON CONFLICT (chain, pool_address, interval, open_timestamp_seconds) DO UPDATE SET
                native_token_usd_price = EXCLUDED.native_token_usd_price,
                usd_open_price = EXCLUDED.usd_open_price,
                usd_close_price = EXCLUDED.usd_close_price,
                usd_high_price = EXCLUDED.usd_high_price,
                usd_low_price = EXCLUDED.usd_low_price,
                usd_open_market_cap = EXCLUDED.usd_open_market_cap,
                usd_close_market_cap = EXCLUDED.usd_close_market_cap,
                usd_high_market_cap = EXCLUDED.usd_high_market_cap,
                usd_low_market_cap = EXCLUDED.usd_low_market_cap,
                usd_volume = EXCLUDED.usd_volume,
                txns = EXCLUDED.txns
            "#,
            candle.chain as Chain,
            candle.pool_address,
            candle.open_timestamp_seconds as i64,
            candle.close_timestamp_seconds as i64,
            candle.interval as CandleInterval,
            candle.native_token_usd_price,
            candle.usd_open_price,
            candle.usd_close_price,
            candle.usd_high_price,
            candle.usd_low_price,
            candle.usd_open_market_cap,
            candle.usd_close_market_cap,
            candle.usd_high_market_cap,
            candle.usd_low_market_cap,
            candle.usd_volume,
            candle.txns as i64,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_candles(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_timestamp_seconds: i64,
        end_timestamp_seconds: i64,
    ) -> Result<Vec<Candle>, Error> {
        tracing::debug!(
            "Querying candles: chain={:?}, pool={}, interval={:?}, start={}, end={}",
            chain,
            pool_address,
            interval,
            start_timestamp_seconds,
            end_timestamp_seconds
        );
        tracing::debug!(
            "Querying candles: chain={:?}, pool={}, interval={:?}, start={}, end={}",
            chain,
            pool_address,
            interval,
            start_timestamp_seconds,
            end_timestamp_seconds
        );
        let rows = sqlx::query!(
            r#"
            SELECT 
                chain AS "chain: Chain",
                pool_address,
                open_timestamp_seconds, close_timestamp_seconds,
                interval AS "interval: CandleInterval",
                native_token_usd_price,
                usd_open_price, usd_close_price, usd_high_price, usd_low_price,
                usd_open_market_cap, usd_close_market_cap, usd_high_market_cap, usd_low_market_cap,
                usd_volume, txns
            FROM candle
            WHERE chain = $1 AND pool_address = $2 AND interval = $3 AND open_timestamp_seconds >= $4 AND open_timestamp_seconds <= $5
            ORDER BY open_timestamp_seconds DESC
            "#,
            chain as Chain,
            pool_address,
            interval as CandleInterval,
            start_timestamp_seconds,
            end_timestamp_seconds,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut candles = Vec::with_capacity(rows.len());
        for row in rows {
            candles.push(Candle {
                chain: row.chain,
                pool_address: row.pool_address,
                open_timestamp_seconds: row.open_timestamp_seconds,
                close_timestamp_seconds: row.close_timestamp_seconds,
                interval: row.interval,
                native_token_usd_price: row.native_token_usd_price,
                usd_open_price: row.usd_open_price,
                usd_close_price: row.usd_close_price,
                usd_high_price: row.usd_high_price,
                usd_low_price: row.usd_low_price,
                usd_open_market_cap: row.usd_open_market_cap,
                usd_close_market_cap: row.usd_close_market_cap,
                usd_high_market_cap: row.usd_high_market_cap,
                usd_low_market_cap: row.usd_low_market_cap,
                usd_volume: row.usd_volume,
                txns: row.txns as u64,
            });
        }

        Ok(candles)
    }

    pub async fn insert_or_update_candles(&self, candles: &[Candle]) -> Result<(), Error> {
        let len = candles.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_candle(&candles[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = candles.iter().map(|c| c.chain).collect();
        let pool_addresses: Vec<String> = candles.iter().map(|c| c.pool_address.clone()).collect();
        let open_timestamp_seconds: Vec<i64> =
            candles.iter().map(|c| c.open_timestamp_seconds).collect();
        let close_timestamp_seconds: Vec<i64> =
            candles.iter().map(|c| c.close_timestamp_seconds).collect();
        let interval: Vec<CandleInterval> = candles.iter().map(|c| c.interval).collect();
        let native_token_usd_price: Vec<f64> =
            candles.iter().map(|c| c.native_token_usd_price).collect();
        let usd_open_price: Vec<f64> = candles.iter().map(|c| c.usd_open_price).collect();
        let usd_close_price: Vec<f64> = candles.iter().map(|c| c.usd_close_price).collect();
        let usd_high_price: Vec<f64> = candles.iter().map(|c| c.usd_high_price).collect();
        let usd_low_price: Vec<f64> = candles.iter().map(|c| c.usd_low_price).collect();
        let usd_open_market_cap: Vec<f64> = candles.iter().map(|c| c.usd_open_market_cap).collect();
        let usd_close_market_cap: Vec<f64> =
            candles.iter().map(|c| c.usd_close_market_cap).collect();
        let usd_high_market_cap: Vec<f64> = candles.iter().map(|c| c.usd_high_market_cap).collect();
        let usd_low_market_cap: Vec<f64> = candles.iter().map(|c| c.usd_low_market_cap).collect();
        let usd_volume: Vec<f64> = candles.iter().map(|c| c.usd_volume).collect();
        let txns: Vec<i64> = candles.iter().map(|c| c.txns as i64).collect();

        sqlx::query!(
            r#"
            INSERT INTO candle (
                chain, pool_address,
                open_timestamp_seconds, close_timestamp_seconds,
                interval,
                native_token_usd_price,
                usd_open_price, usd_close_price, usd_high_price, usd_low_price,
                usd_open_market_cap, usd_close_market_cap, usd_high_market_cap, usd_low_market_cap,
                usd_volume, txns
            )
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[],
                $3::bigint[], $4::bigint[],
                $5::candle_interval_enum[],
                $6::double precision[],
                $7::double precision[], $8::double precision[],
                $9::double precision[], $10::double precision[],
                $11::double precision[], $12::double precision[],
                $13::double precision[], $14::double precision[],
                $15::double precision[], $16::bigint[])
            ON CONFLICT (chain, pool_address, interval, open_timestamp_seconds) DO UPDATE SET
                native_token_usd_price = EXCLUDED.native_token_usd_price,
                usd_open_price = EXCLUDED.usd_open_price,
                usd_close_price = EXCLUDED.usd_close_price,
                usd_high_price = EXCLUDED.usd_high_price,
                usd_low_price = EXCLUDED.usd_low_price,
                usd_open_market_cap = EXCLUDED.usd_open_market_cap,
                usd_close_market_cap = EXCLUDED.usd_close_market_cap,
                usd_high_market_cap = EXCLUDED.usd_high_market_cap,
                usd_low_market_cap = EXCLUDED.usd_low_market_cap,
                usd_volume = EXCLUDED.usd_volume,
                txns = EXCLUDED.txns
            "#,
            &chains as &[Chain],
            &pool_addresses as &[String],
            &open_timestamp_seconds as &[i64],
            &close_timestamp_seconds as &[i64],
            &interval as &[CandleInterval],
            &native_token_usd_price as &[f64],
            &usd_open_price as &[f64],
            &usd_close_price as &[f64],
            &usd_high_price as &[f64],
            &usd_low_price as &[f64],
            &usd_open_market_cap as &[f64],
            &usd_close_market_cap as &[f64],
            &usd_high_market_cap as &[f64],
            &usd_low_market_cap as &[f64],
            &usd_volume as &[f64],
            &txns as &[i64],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Get the latest candle for a specific pool and interval
    pub async fn get_latest_candle(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
    ) -> Result<Option<Candle>, Error> {
        // For now, we'll use a simpler approach without complex queries
        // This can be optimized later when we have proper database setup
        let candles = self
            .get_candles(
                chain,
                pool_address,
                interval,
                0,        // start from beginning
                i64::MAX, // to end
            )
            .await?;

        // Return the latest candle (last in the sorted list)
        Ok(candles.into_iter().last())
    }

    /// Update an existing candle with new data
    pub async fn update_candle(&self, candle: &Candle) -> Result<(), Error> {
        // For now, we'll use insert with ON CONFLICT DO UPDATE
        // This is simpler and doesn't require complex UPDATE queries
        self.insert_or_update_candle(candle).await
    }

    /// Process pool state and update/insert 1-second candles
    /// Implements the logic: accumulate up to 5 seconds, then update
    pub async fn process_pool_state_for_candle(
        &self,
        pool_state: &PoolState,
        base_token_usd_price: f64,
        native_token_usd_price: f64,
    ) -> Result<(), Error> {
        let current_second = pool_state.timestamp_millis / 1000;

        tracing::debug!(
            "Processing candle for pool {} at timestamp {} (second: {})",
            pool_state.pool_address,
            pool_state.timestamp_millis,
            current_second
        );

        // Get the latest 1-second candle for this pool
        let latest_candle = self
            .get_latest_candle(pool_state.chain, &pool_state.pool_address, CandleInterval::S1)
            .await?;

        match latest_candle {
            Some(mut latest) => {
                let latest_second = latest.open_timestamp_seconds;
                let time_diff = current_second - latest_second;

                tracing::debug!(
                    "Found existing candle for pool {} at second {}, time_diff: {}",
                    pool_state.pool_address,
                    latest_second,
                    time_diff
                );

                if time_diff == 0 {
                    // Same second - update the existing candle
                    tracing::debug!("Updating existing candle for same second");
                    self.update_candle_with_pool_state(
                        &mut latest,
                        pool_state,
                        base_token_usd_price,
                        native_token_usd_price,
                    )
                    .await?;
                    self.update_candle(&latest).await?;
                    self.publish_candle_to_kafka(&latest).await?;

                    // Trigger interval aggregation for other intervals
                    if let Err(e) = self.trigger_interval_aggregation(&latest).await {
                        tracing::warn!("Failed to trigger interval aggregation: {}", e);
                    }
                } else if time_diff > 0 && time_diff <= 5 {
                    // Within 5 seconds - update the existing candle to span the time range
                    tracing::debug!("Extending existing candle to span {} seconds", time_diff);
                    latest.close_timestamp_seconds = current_second + 1;
                    self.update_candle_with_pool_state(
                        &mut latest,
                        pool_state,
                        base_token_usd_price,
                        native_token_usd_price,
                    )
                    .await?;
                    self.update_candle(&latest).await?;
                    self.publish_candle_to_kafka(&latest).await?;

                    // Trigger interval aggregation for other intervals
                    if let Err(e) = self.trigger_interval_aggregation(&latest).await {
                        tracing::warn!("Failed to trigger interval aggregation: {}", e);
                    }
                } else {
                    // More than 5 seconds or negative time diff - create new candle
                    tracing::debug!("Creating new candle (time_diff: {})", time_diff);
                    let new_candle = self
                        .create_candle_from_pool_state(
                            pool_state,
                            base_token_usd_price,
                            native_token_usd_price,
                        )
                        .await?;
                    self.insert_or_update_candle(&new_candle).await?;
                    self.publish_candle_to_kafka(&new_candle).await?;

                    // Trigger interval aggregation for other intervals
                    if let Err(e) = self.trigger_interval_aggregation(&new_candle).await {
                        tracing::warn!("Failed to trigger interval aggregation: {}", e);
                    }
                }
            }
            None => {
                // No existing candle - create new one
                tracing::debug!(
                    "No existing candle found, creating new one for pool {}",
                    pool_state.pool_address
                );
                let new_candle = self
                    .create_candle_from_pool_state(
                        pool_state,
                        base_token_usd_price,
                        native_token_usd_price,
                    )
                    .await?;

                tracing::debug!(
                    "Created new candle: pool={}, timestamp={}, volume={}, txns={}",
                    new_candle.pool_address,
                    new_candle.open_timestamp_seconds,
                    new_candle.usd_volume,
                    new_candle.txns
                );

                self.insert_or_update_candle(&new_candle).await?;
                self.publish_candle_to_kafka(&new_candle).await?;

                // Trigger interval aggregation for other intervals
                if let Err(e) = self.trigger_interval_aggregation(&new_candle).await {
                    tracing::warn!("Failed to trigger interval aggregation: {}", e);
                }

                tracing::debug!(
                    "Successfully inserted new candle for pool {} at timestamp {}",
                    pool_state.pool_address,
                    current_second
                );
            }
        }

        Ok(())
    }

    /// Create a new candle from a pool state
    async fn create_candle_from_pool_state(
        &self,
        pool_state: &PoolState,
        base_token_usd_price: f64,
        native_token_usd_price: f64,
    ) -> Result<Candle, Error> {
        let current_second = pool_state.timestamp_millis / 1000;

        // Calculate volume and transaction deltas from previous state
        let previous_pool_state = self.get_previous_pool_state(pool_state).await?;
        let (volume_delta, txns_delta) = if let Some(prev_state) = previous_pool_state {
            let vol_delta = (pool_state.total_volume - prev_state.total_volume).max(0.0);
            let txn_delta = (pool_state.total_txns.saturating_sub(prev_state.total_txns)) as u64;
            (vol_delta, txn_delta)
        } else {
            // If no previous state, use current totals as delta (first candle for this pool)
            (pool_state.total_volume, pool_state.total_txns)
        };

        Ok(Candle {
            chain: pool_state.chain,
            pool_address: pool_state.pool_address.clone(),
            open_timestamp_seconds: current_second,
            close_timestamp_seconds: current_second + 1,
            interval: CandleInterval::S1,
            native_token_usd_price,
            usd_open_price: pool_state.price * base_token_usd_price,
            usd_close_price: pool_state.price * base_token_usd_price,
            usd_high_price: pool_state.price * base_token_usd_price,
            usd_low_price: pool_state.price * base_token_usd_price,
            usd_open_market_cap: pool_state.market_cap * base_token_usd_price,
            usd_close_market_cap: pool_state.market_cap * base_token_usd_price,
            usd_high_market_cap: pool_state.market_cap * base_token_usd_price,
            usd_low_market_cap: pool_state.market_cap * base_token_usd_price,
            usd_volume: volume_delta * base_token_usd_price,
            txns: txns_delta,
        })
    }

    /// Update candle with new pool state data
    async fn update_candle_with_pool_state(
        &self,
        candle: &mut Candle,
        pool_state: &PoolState,
        base_token_usd_price: f64,
        native_token_usd_price: f64,
    ) -> Result<(), Error> {
        // Get previous pool state to calculate volume and transaction deltas
        let previous_pool_state = self.get_previous_pool_state(pool_state).await?;

        let (volume_delta, txns_delta) = if let Some(prev_state) = previous_pool_state {
            let vol_delta = (pool_state.total_volume - prev_state.total_volume).max(0.0);
            let txn_delta = (pool_state.total_txns.saturating_sub(prev_state.total_txns)) as u64;
            (vol_delta, txn_delta)
        } else {
            (0.0, 0)
        };

        // Update candle fields
        candle.native_token_usd_price = native_token_usd_price;
        candle.usd_close_price = pool_state.price * base_token_usd_price;
        candle.usd_high_price = candle.usd_high_price.max(pool_state.price * base_token_usd_price);
        candle.usd_low_price = candle.usd_low_price.min(pool_state.price * base_token_usd_price);
        candle.usd_close_market_cap = pool_state.market_cap * base_token_usd_price;
        candle.usd_high_market_cap =
            candle.usd_high_market_cap.max(pool_state.market_cap * base_token_usd_price);
        candle.usd_low_market_cap =
            candle.usd_low_market_cap.min(pool_state.market_cap * base_token_usd_price);
        candle.usd_volume += volume_delta * base_token_usd_price;
        candle.txns += txns_delta;

        Ok(())
    }

    /// Get the previous pool state for volume/transaction delta calculation
    async fn get_previous_pool_state(
        &self,
        current_pool_state: &PoolState,
    ) -> Result<Option<PoolState>, Error> {
        // For now, we'll use the existing method from the database
        // This can be optimized later when we have proper database setup
        self.get_latest_pool_state_before_block_number(
            current_pool_state.chain,
            &current_pool_state.pool_address,
            current_pool_state.block_number,
        )
        .await
    }

    /// Publish candle to Kafka
    pub async fn publish_candle_to_kafka(&self, candle: &Candle) -> Result<(), Error> {
        use crate::kafka::producer::KafkaProducer;

        let candles_msg = CandlesMsg::new(vec![candle.clone()]);
        let kafka_producer = KafkaProducer::get();

        if let Err(e) = kafka_producer.send::<CandlesMsg>(&candles_msg).await {
            tracing::error!("Failed to publish candle to Kafka: {}", e);
            return Err(Error::AnyhowError(anyhow::anyhow!(
                "Failed to publish candle to Kafka: {}",
                e
            )));
        }

        tracing::debug!(
            "Successfully published candle to Kafka for pool {} at timestamp {}",
            candle.pool_address,
            candle.open_timestamp_seconds
        );

        Ok(())
    }

    /// Trigger aggregation for multiple intervals when a 1s candle is written
    async fn trigger_interval_aggregation(&self, candle: &Candle) -> Result<(), Error> {
        // Only trigger aggregation for 1s candles
        if candle.interval != CandleInterval::S1 {
            return Ok(());
        }

        let current_timestamp = candle.open_timestamp_seconds;

        // Define all intervals to check for aggregation
        let intervals_to_check = vec![
            CandleInterval::S5,
            CandleInterval::S15,
            CandleInterval::S30,
            CandleInterval::M1,
            CandleInterval::M5,
            CandleInterval::M15,
            CandleInterval::M30,
            CandleInterval::H1,
            CandleInterval::H4,
            CandleInterval::H8,
            CandleInterval::H12,
            CandleInterval::H24,
            CandleInterval::D3,
            CandleInterval::D7,
            CandleInterval::D30,
        ];

        for interval in intervals_to_check {
            let interval_seconds = interval.as_seconds();

            // Check if we're at the boundary of this interval
            if current_timestamp % interval_seconds == 0 {
                // This is the start of a new interval, aggregate the previous interval
                let end_time = current_timestamp;
                let start_time = current_timestamp - interval_seconds;

                if let Err(e) = self
                    .aggregate_and_publish_interval(
                        candle.chain,
                        &candle.pool_address,
                        interval,
                        start_time,
                        end_time,
                    )
                    .await
                {
                    tracing::warn!(
                        "Failed to aggregate {} interval for pool {}: {}",
                        interval.as_str(),
                        candle.pool_address,
                        e
                    );
                }
            }
        }

        Ok(())
    }

    /// Aggregate a specific interval and publish to Kafka
    async fn aggregate_and_publish_interval(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_time: i64,
        end_time: i64,
    ) -> Result<(), Error> {
        use crate::postgres::aggregator::enhanced_candle::EnhancedCandle;

        // Check if this interval already exists
        let existing_candles =
            self.get_candles(chain, pool_address, interval, start_time, end_time).await?;

        if !existing_candles.is_empty() {
            // Already aggregated, skip
            return Ok(());
        }

        // Get 1s candles for aggregation
        let s1_candles =
            self.get_candles(chain, pool_address, CandleInterval::S1, start_time, end_time).await?;

        if s1_candles.is_empty() {
            // No source data, skip
            return Ok(());
        }

        // Convert to EnhancedCandle and aggregate
        let s1_enhanced: Vec<EnhancedCandle> =
            s1_candles.into_iter().map(|c| EnhancedCandle::from_candle(c)).collect();

        let aggregated = EnhancedCandle::aggregate(&s1_enhanced, interval);

        if aggregated.is_empty() {
            return Ok(());
        }

        // Convert back to Candle and save
        let candles_to_save: Vec<Candle> = aggregated.iter().map(|c| c.to_candle()).collect();

        // Save to database
        self.insert_or_update_candles(&candles_to_save).await?;

        // Publish to Kafka
        for candle in &candles_to_save {
            if let Err(e) = self.publish_candle_to_kafka(candle).await {
                tracing::error!(
                    "Failed to publish aggregated candle to Kafka: pool={}, interval={}, timestamp={}, error={}",
                    candle.pool_address,
                    interval.as_str(),
                    candle.open_timestamp_seconds,
                    e
                );
            } else {
                tracing::debug!(
                    "Published aggregated candle to Kafka: pool={}, interval={}, timestamp={}",
                    candle.pool_address,
                    interval.as_str(),
                    candle.open_timestamp_seconds
                );
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_candle_time_logic() {
        // Test the time difference logic
        let current_second = 1000;
        let latest_second = 995;
        let time_diff = current_second - latest_second;

        assert_eq!(time_diff, 5);
        assert!(time_diff <= 5); // Should update existing candle

        let latest_second = 994;
        let time_diff = current_second - latest_second;
        assert_eq!(time_diff, 6);
        assert!(time_diff > 5); // Should create new candle
    }

    #[test]
    fn test_candle_creation_logic() {
        // Test candle creation from pool state
        let pool_state = PoolState {
            chain: Chain::Solana,
            pool_address: "test_pool".to_string(),
            block_number: 100,
            timestamp_millis: 1000000, // 1000 seconds
            price: 10.0,
            market_cap: 1000000.0,
            liquidity: 50000.0,
            total_volume: 100000.0,
            total_buy_volume: 60000.0,
            total_sell_volume: 40000.0,
            total_txns: 500,
            total_buy_txns: 300,
            total_sell_txns: 200,
            bonding_curve_progress: Some(0.5),
        };

        let base_token_usd_price = 1.0;
        let expected_second = pool_state.timestamp_millis / 1000;

        // Verify the calculation logic
        assert_eq!(expected_second, 1000);
        assert_eq!(pool_state.price * base_token_usd_price, 10.0);
        assert_eq!(pool_state.market_cap * base_token_usd_price, 1000000.0);
    }
}
