use serde::{Deserialize, Serialize};

use crate::{
    postgres::{
        indexer::{PoolMetadata, PoolState},
        *,
    },
    Error,
};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, sqlx::FromRow)]
pub struct PoolStatistic {
    pub chain: Chain,
    pub pool_address: String,

    pub pair_label: String,
    pub dex: Dex,
    pub pool_type: PoolType,

    pub create_timestamp_millis: i64,

    pub token_address: String,
    pub token_decimals: u8,
    pub base_address: String,
    pub base_decimals: u8,
    pub is_token_first: bool,

    pub is_active: bool,

    pub usd_price: f64,
    pub usd_market_cap: f64,
    pub usd_liquidity: f64,

    pub bonding_curve_progress: Option<f64>,

    pub price: f64,
    pub market_cap: f64,
    pub liquidity: f64,
    pub total_volume: f64,
    pub total_buy_volume: f64,
    pub total_sell_volume: f64,
    pub total_txns: u64,
    pub total_buy_txns: u64,
    pub total_sell_txns: u64,

    pub price_change_5m: f64,
    pub txns_5m: u64,
    pub buy_txns_5m: u64,
    pub sell_txns_5m: u64,
    pub usd_volume_5m: f64,
    pub usd_buy_volume_5m: f64,
    pub usd_sell_volume_5m: f64,
    pub makers_5m: u64,
    pub buyers_5m: u64,
    pub sellers_5m: u64,

    pub price_change_1h: f64,
    pub txns_1h: u64,
    pub buy_txns_1h: u64,
    pub sell_txns_1h: u64,
    pub usd_volume_1h: f64,
    pub usd_buy_volume_1h: f64,
    pub usd_sell_volume_1h: f64,
    pub makers_1h: u64,
    pub buyers_1h: u64,
    pub sellers_1h: u64,

    pub price_change_6h: f64,
    pub txns_6h: u64,
    pub buy_txns_6h: u64,
    pub sell_txns_6h: u64,
    pub usd_volume_6h: f64,
    pub usd_buy_volume_6h: f64,
    pub usd_sell_volume_6h: f64,
    pub makers_6h: u64,
    pub buyers_6h: u64,
    pub sellers_6h: u64,

    pub price_change_24h: f64,
    pub txns_24h: u64,
    pub buy_txns_24h: u64,
    pub sell_txns_24h: u64,
    pub usd_volume_24h: f64,
    pub usd_buy_volume_24h: f64,
    pub usd_sell_volume_24h: f64,
    pub makers_24h: u64,
    pub buyers_24h: u64,
    pub sellers_24h: u64,

    pub update_timestamp_millis: i64,
}

#[derive(Debug, Clone, Default)]
pub struct RangedPoolStatistic {
    pub price_change: f64,
    pub txns: u64,
    pub buy_txns: u64,
    pub sell_txns: u64,
    pub usd_volume: f64,
    pub usd_buy_volume: f64,
    pub usd_sell_volume: f64,
    pub makers: u64,
    pub buyers: u64,
    pub sellers: u64,
}

impl PoolStatistic {
    pub fn to_pool_state(&self) -> PoolState {
        PoolState {
            chain: self.chain.clone(),
            pool_address: self.pool_address.clone(),
            timestamp_millis: self.update_timestamp_millis,
            block_number: 0,
            bonding_curve_progress: self.bonding_curve_progress,
            total_buy_volume: self.total_buy_volume,
            total_sell_volume: self.total_sell_volume,
            total_txns: self.total_txns,
            total_buy_txns: self.total_buy_txns,
            total_sell_txns: self.total_sell_txns,
            total_volume: self.total_volume,
            price: self.price,
            market_cap: self.market_cap,
            liquidity: self.liquidity,
        }
    }

    pub fn new(
        pool_metadata: &PoolMetadata,
        pool_state: &PoolState,
        base_token_usd_price: f64,
    ) -> Self {
        Self {
            chain: pool_metadata.chain.clone(),
            pool_address: pool_metadata.pool_address.clone(),
            pair_label: pool_metadata.pair_label.clone(),
            dex: pool_metadata.dex.clone(),
            pool_type: pool_metadata.pool_type.clone(),
            create_timestamp_millis: pool_metadata.create_timestamp_millis,
            token_address: pool_metadata.token_address.clone(),
            token_decimals: pool_metadata.token_decimals,
            base_address: pool_metadata.base_address.clone(),
            base_decimals: pool_metadata.base_decimals,
            is_token_first: pool_metadata.is_token_first,
            is_active: pool_metadata.is_active,
            usd_price: pool_state.price * base_token_usd_price,
            usd_market_cap: pool_state.market_cap * base_token_usd_price,
            usd_liquidity: pool_state.liquidity * base_token_usd_price,
            bonding_curve_progress: pool_state.bonding_curve_progress,
            price: pool_state.price,
            market_cap: pool_state.market_cap,
            liquidity: pool_state.liquidity,
            total_volume: pool_state.total_volume,
            total_buy_volume: pool_state.total_buy_volume,
            total_sell_volume: pool_state.total_sell_volume,
            total_txns: pool_state.total_txns,
            total_buy_txns: pool_state.total_buy_txns,
            total_sell_txns: pool_state.total_sell_txns,
            price_change_5m: 0.0,
            txns_5m: 0,
            buy_txns_5m: 0,
            sell_txns_5m: 0,
            usd_volume_5m: 0.0,
            usd_buy_volume_5m: 0.0,
            usd_sell_volume_5m: 0.0,
            makers_5m: 0,
            buyers_5m: 0,
            sellers_5m: 0,
            price_change_1h: 0.0,
            txns_1h: 0,
            buy_txns_1h: 0,
            sell_txns_1h: 0,
            usd_volume_1h: 0.0,
            usd_buy_volume_1h: 0.0,
            usd_sell_volume_1h: 0.0,
            makers_1h: 0,
            buyers_1h: 0,
            sellers_1h: 0,
            price_change_6h: 0.0,
            txns_6h: 0,
            buy_txns_6h: 0,
            sell_txns_6h: 0,
            usd_volume_6h: 0.0,
            usd_buy_volume_6h: 0.0,
            usd_sell_volume_6h: 0.0,
            makers_6h: 0,
            buyers_6h: 0,
            sellers_6h: 0,
            price_change_24h: 0.0,
            txns_24h: 0,
            buy_txns_24h: 0,
            sell_txns_24h: 0,
            usd_volume_24h: 0.0,
            usd_buy_volume_24h: 0.0,
            usd_sell_volume_24h: 0.0,
            makers_24h: 0,
            buyers_24h: 0,
            sellers_24h: 0,
            update_timestamp_millis: pool_state.timestamp_millis,
        }
    }

    pub fn update_5m(&mut self, ranged_pool_statistics: RangedPoolStatistic) {
        self.price_change_5m = ranged_pool_statistics.price_change;
        self.txns_5m = ranged_pool_statistics.txns;
        self.buy_txns_5m = ranged_pool_statistics.buy_txns;
        self.sell_txns_5m = ranged_pool_statistics.sell_txns;
        self.usd_volume_5m = ranged_pool_statistics.usd_volume;
        self.usd_buy_volume_5m = ranged_pool_statistics.usd_buy_volume;
        self.usd_sell_volume_5m = ranged_pool_statistics.usd_sell_volume;
        self.makers_5m = ranged_pool_statistics.makers;
        self.buyers_5m = ranged_pool_statistics.buyers;
        self.sellers_5m = ranged_pool_statistics.sellers;
    }

    pub fn update_1h(&mut self, ranged_pool_statistics: RangedPoolStatistic) {
        self.price_change_1h = ranged_pool_statistics.price_change;
        self.txns_1h = ranged_pool_statistics.txns;
        self.buy_txns_1h = ranged_pool_statistics.buy_txns;
        self.sell_txns_1h = ranged_pool_statistics.sell_txns;
        self.usd_volume_1h = ranged_pool_statistics.usd_volume;
        self.usd_buy_volume_1h = ranged_pool_statistics.usd_buy_volume;
        self.usd_sell_volume_1h = ranged_pool_statistics.usd_sell_volume;
        self.makers_1h = ranged_pool_statistics.makers;
        self.buyers_1h = ranged_pool_statistics.buyers;
        self.sellers_1h = ranged_pool_statistics.sellers;
    }

    pub fn update_6h(&mut self, ranged_pool_statistics: RangedPoolStatistic) {
        self.price_change_6h = ranged_pool_statistics.price_change;
        self.txns_6h = ranged_pool_statistics.txns;
        self.buy_txns_6h = ranged_pool_statistics.buy_txns;
        self.sell_txns_6h = ranged_pool_statistics.sell_txns;
        self.usd_volume_6h = ranged_pool_statistics.usd_volume;
        self.usd_buy_volume_6h = ranged_pool_statistics.usd_buy_volume;
        self.usd_sell_volume_6h = ranged_pool_statistics.usd_sell_volume;
        self.makers_6h = ranged_pool_statistics.makers;
        self.buyers_6h = ranged_pool_statistics.buyers;
        self.sellers_6h = ranged_pool_statistics.sellers;
    }

    pub fn update_24h(&mut self, ranged_pool_statistics: RangedPoolStatistic) {
        self.price_change_24h = ranged_pool_statistics.price_change;
        self.txns_24h = ranged_pool_statistics.txns;
        self.buy_txns_24h = ranged_pool_statistics.buy_txns;
        self.sell_txns_24h = ranged_pool_statistics.sell_txns;
        self.usd_volume_24h = ranged_pool_statistics.usd_volume;
        self.usd_buy_volume_24h = ranged_pool_statistics.usd_buy_volume;
        self.usd_sell_volume_24h = ranged_pool_statistics.usd_sell_volume;
        self.makers_24h = ranged_pool_statistics.makers;
        self.buyers_24h = ranged_pool_statistics.buyers;
        self.sellers_24h = ranged_pool_statistics.sellers;
    }
}

impl PostgresDatabase {
    pub async fn insert_or_update_pool_statistic(
        &self,
        pool_statistic: &PoolStatistic,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO pool_statistic (
                chain, pool_address,
                pair_label, dex, pool_type,
                create_timestamp_millis,
                token_address, token_decimals,
                base_address, base_decimals,
                is_token_first,
                is_active,
                usd_price, usd_market_cap, usd_liquidity,
                bonding_curve_progress,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m, makers_5m, buyers_5m, sellers_5m,
                price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h, makers_1h, buyers_1h, sellers_1h,
                price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h, makers_6h, buyers_6h, sellers_6h,
                price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h, makers_24h, buyers_24h, sellers_24h,
                update_timestamp_millis
            )
            VALUES (
                $1, $2,
                $3, $4, $5,
                $6,
                $7, $8,
                $9, $10,
                $11,
                $12,
                $13, $14, $15, 
                $16,
                $17, $18, $19,
                $20, $21, $22,
                $23, $24, $25,
                $26, $27, $28, $29, $30, $31, $32, $33, $34, $35,
                $36, $37, $38, $39, $40, $41, $42, $43, $44, $45,
                $46, $47, $48, $49, $50, $51, $52, $53, $54, $55,
                $56, $57, $58, $59, $60, $61, $62, $63, $64, $65,
                $66
            )
            ON CONFLICT (chain, pool_address) DO UPDATE SET
                pair_label = EXCLUDED.pair_label,
                dex = EXCLUDED.dex,
                pool_type = EXCLUDED.pool_type,
                create_timestamp_millis = EXCLUDED.create_timestamp_millis,
                token_address = EXCLUDED.token_address,
                token_decimals = EXCLUDED.token_decimals,
                base_address = EXCLUDED.base_address,
                base_decimals = EXCLUDED.base_decimals,
                is_token_first = EXCLUDED.is_token_first,
                is_active = EXCLUDED.is_active,
                usd_price = EXCLUDED.usd_price,
                usd_market_cap = EXCLUDED.usd_market_cap,
                usd_liquidity = EXCLUDED.usd_liquidity,
                bonding_curve_progress = EXCLUDED.bonding_curve_progress,
                price = EXCLUDED.price,
                market_cap = EXCLUDED.market_cap,
                liquidity = EXCLUDED.liquidity,
                total_volume = EXCLUDED.total_volume,
                total_buy_volume = EXCLUDED.total_buy_volume,
                total_sell_volume = EXCLUDED.total_sell_volume,
                total_txns = EXCLUDED.total_txns,
                total_buy_txns = EXCLUDED.total_buy_txns,
                total_sell_txns = EXCLUDED.total_sell_txns,
                price_change_5m = EXCLUDED.price_change_5m,
                txns_5m = EXCLUDED.txns_5m,
                buy_txns_5m = EXCLUDED.buy_txns_5m,
                sell_txns_5m = EXCLUDED.sell_txns_5m,
                usd_volume_5m = EXCLUDED.usd_volume_5m,
                usd_buy_volume_5m = EXCLUDED.usd_buy_volume_5m,
                usd_sell_volume_5m = EXCLUDED.usd_sell_volume_5m,
                makers_5m = EXCLUDED.makers_5m,
                buyers_5m = EXCLUDED.buyers_5m,
                sellers_5m = EXCLUDED.sellers_5m,
                price_change_1h = EXCLUDED.price_change_1h,
                txns_1h = EXCLUDED.txns_1h,
                buy_txns_1h = EXCLUDED.buy_txns_1h,
                sell_txns_1h = EXCLUDED.sell_txns_1h,
                usd_volume_1h = EXCLUDED.usd_volume_1h,
                usd_buy_volume_1h = EXCLUDED.usd_buy_volume_1h,
                usd_sell_volume_1h = EXCLUDED.usd_sell_volume_1h,
                makers_1h = EXCLUDED.makers_1h, 
                buyers_1h = EXCLUDED.buyers_1h,
                sellers_1h = EXCLUDED.sellers_1h,
                price_change_6h = EXCLUDED.price_change_6h,
                txns_6h = EXCLUDED.txns_6h,
                buy_txns_6h = EXCLUDED.buy_txns_6h,
                sell_txns_6h = EXCLUDED.sell_txns_6h,
                usd_volume_6h = EXCLUDED.usd_volume_6h,
                usd_buy_volume_6h = EXCLUDED.usd_buy_volume_6h,
                usd_sell_volume_6h = EXCLUDED.usd_sell_volume_6h,
                makers_6h = EXCLUDED.makers_6h,
                buyers_6h = EXCLUDED.buyers_6h,
                sellers_6h = EXCLUDED.sellers_6h,
                price_change_24h = EXCLUDED.price_change_24h,
                txns_24h = EXCLUDED.txns_24h,
                buy_txns_24h = EXCLUDED.buy_txns_24h,
                sell_txns_24h = EXCLUDED.sell_txns_24h,
                usd_volume_24h = EXCLUDED.usd_volume_24h,
                usd_buy_volume_24h = EXCLUDED.usd_buy_volume_24h,
                usd_sell_volume_24h = EXCLUDED.usd_sell_volume_24h,
                makers_24h = EXCLUDED.makers_24h,
                buyers_24h = EXCLUDED.buyers_24h,
                sellers_24h = EXCLUDED.sellers_24h,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis
            WHERE pool_statistic.update_timestamp_millis <= EXCLUDED.update_timestamp_millis
            "#,
            pool_statistic.chain as Chain,
            pool_statistic.pool_address,
            pool_statistic.pair_label,
            pool_statistic.dex as Dex,
            pool_statistic.pool_type as PoolType,
            pool_statistic.create_timestamp_millis,
            pool_statistic.token_address,
            pool_statistic.token_decimals as i16,
            pool_statistic.base_address,
            pool_statistic.base_decimals as i16,
            pool_statistic.is_token_first,
            pool_statistic.is_active,
            pool_statistic.usd_price,
            pool_statistic.usd_market_cap,
            pool_statistic.usd_liquidity,
            pool_statistic.bonding_curve_progress,
            pool_statistic.price,
            pool_statistic.market_cap,
            pool_statistic.liquidity,
            pool_statistic.total_volume,
            pool_statistic.total_buy_volume,
            pool_statistic.total_sell_volume,
            pool_statistic.total_txns as i64,
            pool_statistic.total_buy_txns as i64,
            pool_statistic.total_sell_txns as i64,
            pool_statistic.price_change_5m,
            pool_statistic.txns_5m as i64,
            pool_statistic.buy_txns_5m as i64,
            pool_statistic.sell_txns_5m as i64,
            pool_statistic.usd_volume_5m,
            pool_statistic.usd_buy_volume_5m,
            pool_statistic.usd_sell_volume_5m,
            pool_statistic.makers_5m as i64,
            pool_statistic.buyers_5m as i64,
            pool_statistic.sellers_5m as i64,
            pool_statistic.price_change_1h,
            pool_statistic.txns_1h as i64,
            pool_statistic.buy_txns_1h as i64,
            pool_statistic.sell_txns_1h as i64,
            pool_statistic.usd_volume_1h,
            pool_statistic.usd_buy_volume_1h,
            pool_statistic.usd_sell_volume_1h,
            pool_statistic.makers_1h as i64,
            pool_statistic.buyers_1h as i64,
            pool_statistic.sellers_1h as i64,
            pool_statistic.price_change_6h,
            pool_statistic.txns_6h as i64,
            pool_statistic.buy_txns_6h as i64,
            pool_statistic.sell_txns_6h as i64,
            pool_statistic.usd_volume_6h,
            pool_statistic.usd_buy_volume_6h,
            pool_statistic.usd_sell_volume_6h,
            pool_statistic.makers_6h as i64,
            pool_statistic.buyers_6h as i64,
            pool_statistic.sellers_6h as i64,
            pool_statistic.price_change_24h,
            pool_statistic.txns_24h as i64,
            pool_statistic.buy_txns_24h as i64,
            pool_statistic.sell_txns_24h as i64,
            pool_statistic.usd_volume_24h,
            pool_statistic.usd_buy_volume_24h,
            pool_statistic.usd_sell_volume_24h,
            pool_statistic.makers_24h as i64,
            pool_statistic.buyers_24h as i64,
            pool_statistic.sellers_24h as i64,
            pool_statistic.update_timestamp_millis,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn insert_or_update_pool_statistics(
        &self,
        pool_statistics: &[PoolStatistic],
    ) -> Result<(), Error> {
        let len = pool_statistics.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_pool_statistic(&pool_statistics[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = pool_statistics.iter().map(|p| p.chain).collect();
        let pool_addresses: Vec<String> =
            pool_statistics.iter().map(|p| p.pool_address.clone()).collect();
        let pair_labels: Vec<String> =
            pool_statistics.iter().map(|p| p.pair_label.clone()).collect();
        let dexes: Vec<Dex> = pool_statistics.iter().map(|p| p.dex).collect();
        let pool_types: Vec<PoolType> = pool_statistics.iter().map(|p| p.pool_type).collect();
        let create_timestamp_millis: Vec<i64> =
            pool_statistics.iter().map(|p| p.create_timestamp_millis).collect();
        let token_addresses: Vec<String> =
            pool_statistics.iter().map(|p| p.token_address.clone()).collect();
        let token_decimals: Vec<i16> =
            pool_statistics.iter().map(|p| p.token_decimals as i16).collect();
        let base_addresses: Vec<String> =
            pool_statistics.iter().map(|p| p.base_address.clone()).collect();
        let base_decimals: Vec<i16> =
            pool_statistics.iter().map(|p| p.base_decimals as i16).collect();
        let is_token_first: Vec<bool> = pool_statistics.iter().map(|p| p.is_token_first).collect();
        let is_active: Vec<bool> = pool_statistics.iter().map(|p| p.is_active).collect();
        let usd_prices: Vec<f64> = pool_statistics.iter().map(|p| p.usd_price).collect();
        let usd_market_caps: Vec<f64> = pool_statistics.iter().map(|p| p.usd_market_cap).collect();
        let usd_liquidity: Vec<f64> = pool_statistics.iter().map(|p| p.usd_liquidity).collect();
        let bonding_curve_progresses: Vec<Option<f64>> =
            pool_statistics.iter().map(|p| p.bonding_curve_progress).collect();
        let prices: Vec<f64> = pool_statistics.iter().map(|p| p.price).collect();
        let market_caps: Vec<f64> = pool_statistics.iter().map(|p| p.market_cap).collect();
        let liquidity: Vec<f64> = pool_statistics.iter().map(|p| p.liquidity).collect();
        let total_volume: Vec<f64> = pool_statistics.iter().map(|p| p.total_volume).collect();
        let total_buy_volume: Vec<f64> =
            pool_statistics.iter().map(|p| p.total_buy_volume).collect();
        let total_sell_volume: Vec<f64> =
            pool_statistics.iter().map(|p| p.total_sell_volume).collect();
        let total_txns: Vec<i64> = pool_statistics.iter().map(|p| p.total_txns as i64).collect();
        let total_buy_txns: Vec<i64> =
            pool_statistics.iter().map(|p| p.total_buy_txns as i64).collect();
        let total_sell_txns: Vec<i64> =
            pool_statistics.iter().map(|p| p.total_sell_txns as i64).collect();
        let price_change_5m: Vec<f64> = pool_statistics.iter().map(|p| p.price_change_5m).collect();
        let txns_5m: Vec<i64> = pool_statistics.iter().map(|p| p.txns_5m as i64).collect();
        let buy_txns_5m: Vec<i64> = pool_statistics.iter().map(|p| p.buy_txns_5m as i64).collect();
        let sell_txns_5m: Vec<i64> =
            pool_statistics.iter().map(|p| p.sell_txns_5m as i64).collect();
        let usd_volume_5m: Vec<f64> = pool_statistics.iter().map(|p| p.usd_volume_5m).collect();
        let usd_buy_volume_5m: Vec<f64> =
            pool_statistics.iter().map(|p| p.usd_buy_volume_5m).collect();
        let usd_sell_volume_5m: Vec<f64> =
            pool_statistics.iter().map(|p| p.usd_sell_volume_5m).collect();
        let makers_5m: Vec<i64> = pool_statistics.iter().map(|p| p.makers_5m as i64).collect();
        let buyers_5m: Vec<i64> = pool_statistics.iter().map(|p| p.buyers_5m as i64).collect();
        let sellers_5m: Vec<i64> = pool_statistics.iter().map(|p| p.sellers_5m as i64).collect();
        let price_change_1h: Vec<f64> = pool_statistics.iter().map(|p| p.price_change_1h).collect();
        let txns_1h: Vec<i64> = pool_statistics.iter().map(|p| p.txns_1h as i64).collect();
        let buy_txns_1h: Vec<i64> = pool_statistics.iter().map(|p| p.buy_txns_1h as i64).collect();
        let sell_txns_1h: Vec<i64> =
            pool_statistics.iter().map(|p| p.sell_txns_1h as i64).collect();
        let usd_volume_1h: Vec<f64> = pool_statistics.iter().map(|p| p.usd_volume_1h).collect();
        let usd_buy_volume_1h: Vec<f64> =
            pool_statistics.iter().map(|p| p.usd_buy_volume_1h).collect();
        let usd_sell_volume_1h: Vec<f64> =
            pool_statistics.iter().map(|p| p.usd_sell_volume_1h).collect();
        let makers_1h: Vec<i64> = pool_statistics.iter().map(|p| p.makers_1h as i64).collect();
        let buyers_1h: Vec<i64> = pool_statistics.iter().map(|p| p.buyers_1h as i64).collect();
        let sellers_1h: Vec<i64> = pool_statistics.iter().map(|p| p.sellers_1h as i64).collect();
        let price_change_6h: Vec<f64> = pool_statistics.iter().map(|p| p.price_change_6h).collect();
        let txns_6h: Vec<i64> = pool_statistics.iter().map(|p| p.txns_6h as i64).collect();
        let buy_txns_6h: Vec<i64> = pool_statistics.iter().map(|p| p.buy_txns_6h as i64).collect();
        let sell_txns_6h: Vec<i64> =
            pool_statistics.iter().map(|p| p.sell_txns_6h as i64).collect();
        let usd_volume_6h: Vec<f64> = pool_statistics.iter().map(|p| p.usd_volume_6h).collect();
        let usd_buy_volume_6h: Vec<f64> =
            pool_statistics.iter().map(|p| p.usd_buy_volume_6h).collect();
        let usd_sell_volume_6h: Vec<f64> =
            pool_statistics.iter().map(|p| p.usd_sell_volume_6h).collect();
        let makers_6h: Vec<i64> = pool_statistics.iter().map(|p| p.makers_6h as i64).collect();
        let buyers_6h: Vec<i64> = pool_statistics.iter().map(|p| p.buyers_6h as i64).collect();
        let sellers_6h: Vec<i64> = pool_statistics.iter().map(|p| p.sellers_6h as i64).collect();
        let price_change_24h: Vec<f64> =
            pool_statistics.iter().map(|p| p.price_change_24h).collect();
        let txns_24h: Vec<i64> = pool_statistics.iter().map(|p| p.txns_24h as i64).collect();
        let buy_txns_24h: Vec<i64> =
            pool_statistics.iter().map(|p| p.buy_txns_24h as i64).collect();
        let sell_txns_24h: Vec<i64> =
            pool_statistics.iter().map(|p| p.sell_txns_24h as i64).collect();
        let usd_volume_24h: Vec<f64> = pool_statistics.iter().map(|p| p.usd_volume_24h).collect();
        let usd_buy_volume_24h: Vec<f64> =
            pool_statistics.iter().map(|p| p.usd_buy_volume_24h).collect();
        let usd_sell_volume_24h: Vec<f64> =
            pool_statistics.iter().map(|p| p.usd_sell_volume_24h).collect();
        let makers_24h: Vec<i64> = pool_statistics.iter().map(|p| p.makers_24h as i64).collect();
        let buyers_24h: Vec<i64> = pool_statistics.iter().map(|p| p.buyers_24h as i64).collect();
        let sellers_24h: Vec<i64> = pool_statistics.iter().map(|p| p.sellers_24h as i64).collect();
        let update_timestamp_millis: Vec<i64> =
            pool_statistics.iter().map(|p| p.update_timestamp_millis).collect();

        sqlx::query!(
            r#"
            INSERT INTO pool_statistic (
                chain, pool_address,
                pair_label, dex, pool_type,
                create_timestamp_millis,
                token_address, token_decimals,
                base_address, base_decimals,
                is_token_first,
                is_active,
                usd_price, usd_market_cap, usd_liquidity,
                bonding_curve_progress,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m, makers_5m, buyers_5m, sellers_5m,
                price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h, makers_1h, buyers_1h, sellers_1h,
                price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h, makers_6h, buyers_6h, sellers_6h,
                price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h, makers_24h, buyers_24h, sellers_24h,
                update_timestamp_millis
            )
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[],
                $3::varchar[], $4::dex_enum[], $5::pool_type_enum[],
                $6::bigint[],
                $7::varchar[], $8::smallint[],
                $9::varchar[], $10::smallint[],
                $11::boolean[],
                $12::boolean[],
                $13::double precision[], $14::double precision[], $15::double precision[], 
                $16::double precision[],
                $17::double precision[], $18::double precision[], $19::double precision[],
                $20::double precision[], $21::double precision[], $22::double precision[],
                $23::bigint[], $24::bigint[], $25::bigint[],
                $26::double precision[], $27::bigint[], $28::bigint[], $29::bigint[], $30::double precision[], $31::double precision[], $32::double precision[], $33::bigint[], $34::bigint[], $35::bigint[],
                $36::double precision[], $37::bigint[], $38::bigint[], $39::bigint[], $40::double precision[], $41::double precision[], $42::double precision[], $43::bigint[], $44::bigint[], $45::bigint[],
                $46::double precision[], $47::bigint[], $48::bigint[], $49::bigint[], $50::double precision[], $51::double precision[], $52::double precision[], $53::bigint[], $54::bigint[], $55::bigint[],
                $56::double precision[], $57::bigint[], $58::bigint[], $59::bigint[], $60::double precision[], $61::double precision[], $62::double precision[], $63::bigint[], $64::bigint[], $65::bigint[],
                $66::bigint[])
            ON CONFLICT (chain, pool_address) DO UPDATE SET
                pair_label = EXCLUDED.pair_label,
                dex = EXCLUDED.dex,
                pool_type = EXCLUDED.pool_type,
                create_timestamp_millis = EXCLUDED.create_timestamp_millis,
                token_address = EXCLUDED.token_address,
                token_decimals = EXCLUDED.token_decimals,
                base_address = EXCLUDED.base_address,
                base_decimals = EXCLUDED.base_decimals,
                is_token_first = EXCLUDED.is_token_first,
                is_active = EXCLUDED.is_active,
                usd_price = EXCLUDED.usd_price,
                usd_market_cap = EXCLUDED.usd_market_cap,
                usd_liquidity = EXCLUDED.usd_liquidity,
                bonding_curve_progress = EXCLUDED.bonding_curve_progress,
                price = EXCLUDED.price,
                market_cap = EXCLUDED.market_cap,
                liquidity = EXCLUDED.liquidity,
                total_volume = EXCLUDED.total_volume,
                total_buy_volume = EXCLUDED.total_buy_volume,
                total_sell_volume = EXCLUDED.total_sell_volume,
                total_txns = EXCLUDED.total_txns,
                total_buy_txns = EXCLUDED.total_buy_txns,
                total_sell_txns = EXCLUDED.total_sell_txns,
                price_change_5m = EXCLUDED.price_change_5m,
                txns_5m = EXCLUDED.txns_5m,
                buy_txns_5m = EXCLUDED.buy_txns_5m,
                sell_txns_5m = EXCLUDED.sell_txns_5m,
                usd_volume_5m = EXCLUDED.usd_volume_5m,
                usd_buy_volume_5m = EXCLUDED.usd_buy_volume_5m,
                usd_sell_volume_5m = EXCLUDED.usd_sell_volume_5m,
                makers_5m = EXCLUDED.makers_5m,
                buyers_5m = EXCLUDED.buyers_5m,
                sellers_5m = EXCLUDED.sellers_5m,
                price_change_1h = EXCLUDED.price_change_1h,
                txns_1h = EXCLUDED.txns_1h,
                buy_txns_1h = EXCLUDED.buy_txns_1h,
                sell_txns_1h = EXCLUDED.sell_txns_1h,
                usd_volume_1h = EXCLUDED.usd_volume_1h,
                usd_buy_volume_1h = EXCLUDED.usd_buy_volume_1h,
                usd_sell_volume_1h = EXCLUDED.usd_sell_volume_1h,
                makers_1h = EXCLUDED.makers_1h, 
                buyers_1h = EXCLUDED.buyers_1h,
                sellers_1h = EXCLUDED.sellers_1h,
                price_change_6h = EXCLUDED.price_change_6h,
                txns_6h = EXCLUDED.txns_6h,
                buy_txns_6h = EXCLUDED.buy_txns_6h,
                sell_txns_6h = EXCLUDED.sell_txns_6h,
                usd_volume_6h = EXCLUDED.usd_volume_6h,
                usd_buy_volume_6h = EXCLUDED.usd_buy_volume_6h,
                usd_sell_volume_6h = EXCLUDED.usd_sell_volume_6h,
                makers_6h = EXCLUDED.makers_6h,
                buyers_6h = EXCLUDED.buyers_6h,
                sellers_6h = EXCLUDED.sellers_6h,
                price_change_24h = EXCLUDED.price_change_24h,
                txns_24h = EXCLUDED.txns_24h,
                buy_txns_24h = EXCLUDED.buy_txns_24h,
                sell_txns_24h = EXCLUDED.sell_txns_24h,
                usd_volume_24h = EXCLUDED.usd_volume_24h,
                usd_buy_volume_24h = EXCLUDED.usd_buy_volume_24h,
                usd_sell_volume_24h = EXCLUDED.usd_sell_volume_24h,
                makers_24h = EXCLUDED.makers_24h,
                buyers_24h = EXCLUDED.buyers_24h,
                sellers_24h = EXCLUDED.sellers_24h,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis
            WHERE pool_statistic.update_timestamp_millis <= EXCLUDED.update_timestamp_millis
            "#,
            &chains as &[Chain],
            &pool_addresses as &[String],
            &pair_labels as &[String],
            &dexes as &[Dex],
            &pool_types as &[PoolType],
            &create_timestamp_millis as &[i64],
            &token_addresses as &[String],
            &token_decimals as &[i16],
            &base_addresses as &[String],
            &base_decimals as &[i16],
            &is_token_first as &[bool],
            &is_active as &[bool],
            &usd_prices as &[f64],
            &usd_market_caps as &[f64],
            &usd_liquidity as &[f64],
            &bonding_curve_progresses as &[Option<f64>],
            &prices as &[f64],
            &market_caps as &[f64],
            &liquidity as &[f64],
            &total_volume as &[f64],
            &total_buy_volume as &[f64],
            &total_sell_volume as &[f64],
            &total_txns as &[i64],
            &total_buy_txns as &[i64],
            &total_sell_txns as &[i64],
            &price_change_5m as &[f64],
            &txns_5m as &[i64],
            &buy_txns_5m as &[i64],
            &sell_txns_5m as &[i64],
            &usd_volume_5m as &[f64],
            &usd_buy_volume_5m as &[f64],
            &usd_sell_volume_5m as &[f64],
            &makers_5m as &[i64],
            &buyers_5m as &[i64],
            &sellers_5m as &[i64],
            &price_change_1h as &[f64],
            &txns_1h as &[i64],
            &buy_txns_1h as &[i64],
            &sell_txns_1h as &[i64],
            &usd_volume_1h as &[f64],
            &usd_buy_volume_1h as &[f64],
            &usd_sell_volume_1h as &[f64],
            &makers_1h as &[i64],
            &buyers_1h as &[i64],
            &sellers_1h as &[i64],
            &price_change_6h as &[f64],
            &txns_6h as &[i64],
            &buy_txns_6h as &[i64],
            &sell_txns_6h as &[i64],
            &usd_volume_6h as &[f64],
            &usd_buy_volume_6h as &[f64],
            &usd_sell_volume_6h as &[f64],
            &makers_6h as &[i64],
            &buyers_6h as &[i64],
            &sellers_6h as &[i64],
            &price_change_24h as &[f64],
            &txns_24h as &[i64],
            &buy_txns_24h as &[i64],
            &sell_txns_24h as &[i64],
            &usd_volume_24h as &[f64],
            &usd_buy_volume_24h as &[f64],
            &usd_sell_volume_24h as &[f64],
            &makers_24h as &[i64],
            &buyers_24h as &[i64],
            &sellers_24h as &[i64],
            &update_timestamp_millis as &[i64],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_pool_statistics(
        &self,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<PoolStatistic>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain", pool_address,
                pair_label, dex AS "dex: Dex", pool_type AS "pool_type: PoolType",
                create_timestamp_millis,
                token_address, token_decimals,
                base_address, base_decimals,
                is_token_first,
                is_active,
                usd_price, usd_market_cap, usd_liquidity,
                bonding_curve_progress,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m,
    usd_buy_volume_5m, usd_sell_volume_5m, makers_5m, buyers_5m, sellers_5m,             
    price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h,
    usd_sell_volume_1h, makers_1h, buyers_1h, sellers_1h,             price_change_6h,
    txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h,
    makers_6h, buyers_6h, sellers_6h,             price_change_24h, txns_24h, buy_txns_24h,
    sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h, makers_24h,
    buyers_24h, sellers_24h,             update_timestamp_millis
            FROM pool_statistic
            ORDER BY chain, pool_address
            LIMIT $1 OFFSET $2
            "#,
            limit,
            offset,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut pool_statistics = Vec::with_capacity(rows.len());
        for row in rows {
            pool_statistics.push(PoolStatistic {
                chain: row.chain,
                pool_address: row.pool_address,
                pair_label: row.pair_label,
                dex: row.dex,
                pool_type: row.pool_type,
                create_timestamp_millis: row.create_timestamp_millis,
                token_address: row.token_address,
                token_decimals: row.token_decimals as u8,
                base_address: row.base_address,
                base_decimals: row.base_decimals as u8,
                is_token_first: row.is_token_first,
                is_active: row.is_active,
                usd_price: row.usd_price,
                usd_market_cap: row.usd_market_cap,
                usd_liquidity: row.usd_liquidity,
                bonding_curve_progress: row.bonding_curve_progress,
                price: row.price,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                total_volume: row.total_volume,
                total_buy_volume: row.total_buy_volume,
                total_sell_volume: row.total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                price_change_5m: row.price_change_5m,
                txns_5m: row.txns_5m as u64,
                buy_txns_5m: row.buy_txns_5m as u64,
                sell_txns_5m: row.sell_txns_5m as u64,
                usd_volume_5m: row.usd_volume_5m,
                usd_buy_volume_5m: row.usd_buy_volume_5m,
                usd_sell_volume_5m: row.usd_sell_volume_5m,
                makers_5m: row.makers_5m as u64,
                buyers_5m: row.buyers_5m as u64,
                sellers_5m: row.sellers_5m as u64,
                price_change_1h: row.price_change_1h,
                txns_1h: row.txns_1h as u64,
                buy_txns_1h: row.buy_txns_1h as u64,
                sell_txns_1h: row.sell_txns_1h as u64,
                usd_volume_1h: row.usd_volume_1h,
                usd_buy_volume_1h: row.usd_buy_volume_1h,
                usd_sell_volume_1h: row.usd_sell_volume_1h,
                makers_1h: row.makers_1h as u64,
                buyers_1h: row.buyers_1h as u64,
                sellers_1h: row.sellers_1h as u64,
                price_change_6h: row.price_change_6h,
                txns_6h: row.txns_6h as u64,
                buy_txns_6h: row.buy_txns_6h as u64,
                sell_txns_6h: row.sell_txns_6h as u64,
                usd_volume_6h: row.usd_volume_6h,
                usd_buy_volume_6h: row.usd_buy_volume_6h,
                usd_sell_volume_6h: row.usd_sell_volume_6h,
                makers_6h: row.makers_6h as u64,
                buyers_6h: row.buyers_6h as u64,
                sellers_6h: row.sellers_6h as u64,
                price_change_24h: row.price_change_24h,
                txns_24h: row.txns_24h as u64,
                buy_txns_24h: row.buy_txns_24h as u64,
                sell_txns_24h: row.sell_txns_24h as u64,
                usd_volume_24h: row.usd_volume_24h,
                usd_buy_volume_24h: row.usd_buy_volume_24h,
                usd_sell_volume_24h: row.usd_sell_volume_24h,
                makers_24h: row.makers_24h as u64,
                buyers_24h: row.buyers_24h as u64,
                sellers_24h: row.sellers_24h as u64,
                update_timestamp_millis: row.update_timestamp_millis,
            });
        }

        Ok(pool_statistics)
    }
}
