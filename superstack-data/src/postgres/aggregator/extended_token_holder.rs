use serde::{Deserialize, Serialize};

use crate::{postgres::*, Error};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct ExtendedTokenHolder {
    pub chain: Chain,
    pub token_address: String,
    pub holder_address: String,

    pub bought_ui_amount: f64,
    pub sold_ui_amount: f64,
    pub remaining_ui_amount: f64,

    pub bought_txns: u64,
    pub sold_txns: u64,

    pub spent_usd: f64,
    pub received_usd: f64,

    pub pnl_usd: f64,

    pub update_timestamp_millis: i64,
    pub update_block_number: u64,

    pub native_token_balance: f64,

    pub maker_volume_type: MakerVolumeType,
    pub maker_trade_type: MakerTradeType,
}

impl ExtendedTokenHolder {
    pub fn is_newer_than(&self, other: &Self) -> bool {
        if self.bought_txns > other.bought_txns || self.sold_txns > other.sold_txns {
            return true;
        } else if self.bought_txns < other.bought_txns || self.sold_txns < other.sold_txns {
            return false;
        }

        if self.sold_ui_amount > other.sold_ui_amount ||
            self.bought_ui_amount > other.bought_ui_amount
        {
            return true;
        } else if self.sold_ui_amount < other.sold_ui_amount ||
            self.bought_ui_amount < other.bought_ui_amount
        {
            return false;
        }

        false
    }
}

impl PostgresDatabase {
    pub async fn insert_or_update_extended_token_holder(
        &self,
        extended_token_holder: &ExtendedTokenHolder,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO extended_token_holder (
                chain, token_address, holder_address,
                bought_ui_amount, sold_ui_amount, remaining_ui_amount,
                bought_txns, sold_txns,
                spent_usd, received_usd,
                pnl_usd,
                update_timestamp_millis, update_block_number,
                native_token_balance,
                maker_volume_type, maker_trade_type
            )
            VALUES (
                $1, $2, $3,
                $4, $5, $6,
                $7, $8,
                $9, $10,
                $11,
                $12, $13,
                $14,
                $15, $16
            )
            ON CONFLICT (chain, token_address, holder_address) DO UPDATE SET
                bought_ui_amount = EXCLUDED.bought_ui_amount,
                sold_ui_amount = EXCLUDED.sold_ui_amount,
                remaining_ui_amount = EXCLUDED.remaining_ui_amount,
                bought_txns = EXCLUDED.bought_txns,
                sold_txns = EXCLUDED.sold_txns,
                spent_usd = EXCLUDED.spent_usd,
                received_usd = EXCLUDED.received_usd,
                pnl_usd = EXCLUDED.pnl_usd,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis,
                update_block_number = EXCLUDED.update_block_number,
                native_token_balance = EXCLUDED.native_token_balance,
                maker_volume_type = EXCLUDED.maker_volume_type,
                maker_trade_type = EXCLUDED.maker_trade_type
            WHERE extended_token_holder.update_timestamp_millis <= EXCLUDED.update_timestamp_millis
                and extended_token_holder.update_block_number <= EXCLUDED.update_block_number
            "#,
            extended_token_holder.chain as Chain,
            extended_token_holder.token_address,
            extended_token_holder.holder_address,
            extended_token_holder.bought_ui_amount,
            extended_token_holder.sold_ui_amount,
            extended_token_holder.remaining_ui_amount,
            extended_token_holder.bought_txns as i64,
            extended_token_holder.sold_txns as i64,
            extended_token_holder.spent_usd,
            extended_token_holder.received_usd,
            extended_token_holder.pnl_usd,
            extended_token_holder.update_timestamp_millis,
            extended_token_holder.update_block_number as i64,
            extended_token_holder.native_token_balance,
            extended_token_holder.maker_volume_type as MakerVolumeType,
            extended_token_holder.maker_trade_type as MakerTradeType,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_extended_token_holders_ordered_by_pnl_usd(
        &self,
        chain: Chain,
        token_address: &str,
        limit: u64,
    ) -> Result<Vec<ExtendedTokenHolder>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                token_address,
                holder_address,
                bought_ui_amount,
                sold_ui_amount,
                remaining_ui_amount,
                bought_txns,
                sold_txns,
                spent_usd,
                received_usd,
                pnl_usd,
                update_timestamp_millis,
                update_block_number,
                native_token_balance,
                maker_volume_type AS "maker_volume_type: MakerVolumeType",
                maker_trade_type AS "maker_trade_type: MakerTradeType"
            FROM extended_token_holder
            WHERE chain = $1 AND token_address = $2
            ORDER BY pnl_usd DESC
            LIMIT $3
            "#,
            chain as Chain,
            token_address,
            limit as i64,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut candles = Vec::with_capacity(rows.len());
        for row in rows {
            candles.push(ExtendedTokenHolder {
                chain: row.chain,
                token_address: row.token_address,
                holder_address: row.holder_address,
                bought_ui_amount: row.bought_ui_amount,
                sold_ui_amount: row.sold_ui_amount,
                remaining_ui_amount: row.remaining_ui_amount,
                bought_txns: row.bought_txns as u64,
                sold_txns: row.sold_txns as u64,
                spent_usd: row.spent_usd,
                received_usd: row.received_usd,
                pnl_usd: row.pnl_usd,
                update_timestamp_millis: row.update_timestamp_millis,
                update_block_number: row.update_block_number as u64,
                native_token_balance: row.native_token_balance,
                maker_volume_type: row.maker_volume_type,
                maker_trade_type: row.maker_trade_type,
            });
        }

        Ok(candles)
    }

    pub async fn get_extended_token_holders_ordered_by_holding(
        &self,
        chain: Chain,
        token_address: &str,
        limit: u64,
        offset: u64,
    ) -> Result<Vec<ExtendedTokenHolder>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                token_address,
                holder_address,
                bought_ui_amount,
                sold_ui_amount,
                remaining_ui_amount,
                bought_txns,
                sold_txns,
                spent_usd,
                received_usd,
                pnl_usd,
                update_timestamp_millis,
                update_block_number,
                native_token_balance,
                maker_volume_type AS "maker_volume_type: MakerVolumeType",
                maker_trade_type AS "maker_trade_type: MakerTradeType"
            FROM extended_token_holder
            WHERE chain = $1 AND token_address = $2 AND remaining_ui_amount > 0
            ORDER BY remaining_ui_amount DESC
            LIMIT $3 OFFSET $4
            "#,
            chain as Chain,
            token_address,
            limit as i64,
            offset as i64,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut candles = Vec::with_capacity(rows.len());
        for row in rows {
            candles.push(ExtendedTokenHolder {
                chain: row.chain,
                token_address: row.token_address,
                holder_address: row.holder_address,
                bought_ui_amount: row.bought_ui_amount,
                sold_ui_amount: row.sold_ui_amount,
                remaining_ui_amount: row.remaining_ui_amount,
                bought_txns: row.bought_txns as u64,
                sold_txns: row.sold_txns as u64,
                spent_usd: row.spent_usd,
                received_usd: row.received_usd,
                pnl_usd: row.pnl_usd,
                update_timestamp_millis: row.update_timestamp_millis,
                update_block_number: row.update_block_number as u64,
                native_token_balance: row.native_token_balance,
                maker_volume_type: row.maker_volume_type,
                maker_trade_type: row.maker_trade_type,
            });
        }

        Ok(candles)
    }

    pub async fn get_extended_token_holders_count(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<u64, Error> {
        let row = sqlx::query!(
            r#"
            SELECT COUNT(*) as count
            FROM extended_token_holder
            WHERE chain = $1 AND token_address = $2 AND remaining_ui_amount > 0
            "#,
            chain as Chain,
            token_address,
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(row.count.unwrap_or(0) as u64)
    }

    pub async fn insert_or_update_extended_token_holders(
        &self,
        extended_token_holders: &[ExtendedTokenHolder],
    ) -> Result<(), Error> {
        let len = extended_token_holders.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_extended_token_holder(&extended_token_holders[0]).await;
        }

        // Deduplicate holders by (chain, token_address, holder_address)
        // Keep the most recent record based on timestamp and block number
        let mut deduped_holders: std::collections::HashMap<
            (Chain, String, String),
            &ExtendedTokenHolder,
        > = std::collections::HashMap::new();
        for holder in extended_token_holders {
            let key = (holder.chain, holder.token_address.clone(), holder.holder_address.clone());

            match deduped_holders.get(&key) {
                Some(existing) => {
                    // Keep the record with the latest timestamp, or latest block number if
                    // timestamps are equal
                    if holder.update_timestamp_millis > existing.update_timestamp_millis ||
                        (holder.update_timestamp_millis == existing.update_timestamp_millis &&
                            holder.update_block_number > existing.update_block_number)
                    {
                        deduped_holders.insert(key, holder);
                    }
                }
                None => {
                    deduped_holders.insert(key, holder);
                }
            }
        }

        let deduped_holders: Vec<&ExtendedTokenHolder> = deduped_holders.into_values().collect();
        let len = deduped_holders.len();

        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_extended_token_holder(deduped_holders[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = deduped_holders.iter().map(|c| c.chain).collect();
        let token_addresses: Vec<String> =
            deduped_holders.iter().map(|c| c.token_address.clone()).collect();
        let holder_addresses: Vec<String> =
            deduped_holders.iter().map(|c| c.holder_address.clone()).collect();
        let bought_ui_amount: Vec<f64> =
            deduped_holders.iter().map(|c| c.bought_ui_amount).collect();
        let sold_ui_amount: Vec<f64> = deduped_holders.iter().map(|c| c.sold_ui_amount).collect();
        let remaining_ui_amount: Vec<f64> =
            deduped_holders.iter().map(|c| c.remaining_ui_amount).collect();
        let bought_txns: Vec<i64> = deduped_holders.iter().map(|c| c.bought_txns as i64).collect();
        let sold_txns: Vec<i64> = deduped_holders.iter().map(|c| c.sold_txns as i64).collect();
        let spent_usd: Vec<f64> = deduped_holders.iter().map(|c| c.spent_usd).collect();
        let received_usd: Vec<f64> = deduped_holders.iter().map(|c| c.received_usd).collect();
        let pnl_usd: Vec<f64> = deduped_holders.iter().map(|c| c.pnl_usd).collect();
        let update_timestamp_millis: Vec<i64> =
            deduped_holders.iter().map(|c| c.update_timestamp_millis).collect();
        let update_block_number: Vec<i64> =
            deduped_holders.iter().map(|c| c.update_block_number as i64).collect();
        let native_token_balance: Vec<f64> =
            deduped_holders.iter().map(|c| c.native_token_balance).collect();
        let maker_volume_type: Vec<MakerVolumeType> =
            deduped_holders.iter().map(|c| c.maker_volume_type).collect();
        let maker_trade_type: Vec<MakerTradeType> =
            deduped_holders.iter().map(|c| c.maker_trade_type).collect();

        sqlx::query!(
            r#"
            INSERT INTO extended_token_holder (
                chain, token_address, holder_address,
                bought_ui_amount, sold_ui_amount, remaining_ui_amount,
                bought_txns, sold_txns,
                spent_usd, received_usd,
                pnl_usd,
                update_timestamp_millis, update_block_number,
                native_token_balance,
                maker_volume_type, maker_trade_type
            )
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[], $3::varchar[],
                $4::double precision[], $5::double precision[], $6::double precision[],
                $7::bigint[], $8::bigint[],
                $9::double precision[], $10::double precision[],
                $11::double precision[],
                $12::bigint[], $13::bigint[],
                $14::double precision[],
                $15::maker_volume_type_enum[], $16::maker_trade_type_enum[])
            ON CONFLICT (chain, token_address, holder_address) DO UPDATE SET
                bought_ui_amount = EXCLUDED.bought_ui_amount,
                sold_ui_amount = EXCLUDED.sold_ui_amount,
                remaining_ui_amount = EXCLUDED.remaining_ui_amount,
                bought_txns = EXCLUDED.bought_txns,
                sold_txns = EXCLUDED.sold_txns,
                spent_usd = EXCLUDED.spent_usd,
                received_usd = EXCLUDED.received_usd,
                pnl_usd = EXCLUDED.pnl_usd,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis,
                update_block_number = EXCLUDED.update_block_number,
                native_token_balance = EXCLUDED.native_token_balance,
                maker_volume_type = EXCLUDED.maker_volume_type,
                maker_trade_type = EXCLUDED.maker_trade_type
            WHERE extended_token_holder.update_timestamp_millis <= EXCLUDED.update_timestamp_millis
                and extended_token_holder.update_block_number <= EXCLUDED.update_block_number
            "#,
            &chains as &[Chain],
            &token_addresses as &[String],
            &holder_addresses as &[String],
            &bought_ui_amount as &[f64],
            &sold_ui_amount as &[f64],
            &remaining_ui_amount as &[f64],
            &bought_txns as &[i64],
            &sold_txns as &[i64],
            &spent_usd as &[f64],
            &received_usd as &[f64],
            &pnl_usd as &[f64],
            &update_timestamp_millis as &[i64],
            &update_block_number as &[i64],
            &native_token_balance as &[f64],
            &maker_volume_type as &[MakerVolumeType],
            &maker_trade_type as &[MakerTradeType],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}
