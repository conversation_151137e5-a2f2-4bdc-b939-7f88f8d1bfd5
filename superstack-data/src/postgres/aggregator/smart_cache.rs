use std::time::Duration;

use redis::AsyncCommands;

use crate::{
    postgres::{
        aggregator::{
            enhanced_candle::{cache_key, candle_aggregation_lock_key, EnhancedCandle},
            Candle,
        },
        enums::{CandleInterval, Chain},
        indexer::PoolState,
        PostgresDatabase,
    },
    price::NativeTokenPriceManager,
    redis::{distributed_lock::*, RedisClient},
    Error,
};

// Configuration constants
const DEFAULT_CACHE_TTL_SECONDS: u64 = 600;
const DEFAULT_LOCK_TIMEOUT_SECONDS: u64 = 30;
const DEFAULT_MAX_CACHE_AGE_SECONDS: i64 = 1200;

/// Configuration for intelligent candle caching
#[derive(Debug, Clone)]
pub struct SmartCacheConfig {
    /// Maximum age of cached data in seconds
    pub max_cache_age_seconds: i64,
    /// Lock timeout for aggregation operations
    pub lock_timeout_seconds: u64,
    /// Retry interval for lock acquisition
    pub lock_retry_interval: Duration,
    /// Maximum wait time for lock acquisition
    pub max_lock_wait: Duration,
    /// Cache TTL in Redis (seconds)
    pub redis_ttl_seconds: u64,
}

impl Default for SmartCacheConfig {
    fn default() -> Self {
        Self {
            max_cache_age_seconds: DEFAULT_MAX_CACHE_AGE_SECONDS,
            lock_timeout_seconds: DEFAULT_LOCK_TIMEOUT_SECONDS,
            lock_retry_interval: Duration::from_millis(100),
            max_lock_wait: Duration::from_secs(10),
            redis_ttl_seconds: DEFAULT_CACHE_TTL_SECONDS,
        }
    }
}

/// Smart caching manager for candle data
pub struct SmartCacheManager {
    redis_client: RedisClient,
    config: SmartCacheConfig,
}

impl SmartCacheManager {
    pub fn new(redis_client: RedisClient, config: SmartCacheConfig) -> Self {
        Self { redis_client, config }
    }

    /// Get candle data with intelligent caching
    pub async fn get_candles(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_timestamp_seconds: i64,
        end_timestamp_seconds: i64,
    ) -> Result<Vec<EnhancedCandle>, Error> {
        // Adjust timestamps to interval boundaries
        let interval_seconds = interval.as_seconds();
        let adjusted_start_ts = (start_timestamp_seconds / interval_seconds) * interval_seconds;
        let adjusted_end_ts =
            ((end_timestamp_seconds + interval_seconds - 1) / interval_seconds) * interval_seconds;

        // Try to get from cache first
        if let Some(cached_candles) = self
            .get_from_cache(chain, pool_address, interval, adjusted_start_ts, adjusted_end_ts)
            .await?
        {
            if !self.is_cache_stale(&cached_candles) {
                tracing::debug!(
                    "Cache hit for candles: {}:{}:{} [{}, {}]",
                    chain.to_string(),
                    pool_address,
                    interval.as_str(),
                    adjusted_start_ts,
                    adjusted_end_ts
                );
                return Ok(cached_candles);
            }
        }

        // Cache miss or stale data - need to aggregate
        tracing::debug!(
            "Cache miss for candles: {}:{}:{} [{}, {}]",
            chain.to_string(),
            pool_address,
            interval.as_str(),
            adjusted_start_ts,
            adjusted_end_ts
        );

        self.aggregate_and_cache(chain, pool_address, interval, adjusted_start_ts, adjusted_end_ts)
            .await
    }

    /// Get cached candles from Redis
    async fn get_from_cache(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_ts: i64,
        end_ts: i64,
    ) -> Result<Option<Vec<EnhancedCandle>>, Error> {
        let cache_key = cache_key(chain, pool_address, interval, start_ts, end_ts);
        let mut conn = self.redis_client.get_connection();

        let cached_data: Option<String> = conn.get(&cache_key).await.map_err(Error::RedisError)?;

        if let Some(data) = cached_data {
            match serde_json::from_str::<Vec<EnhancedCandle>>(&data) {
                Ok(candles) => Ok(Some(candles)),
                Err(e) => {
                    tracing::warn!("Failed to deserialize cached candles: {}", e);
                    // Remove corrupted cache entry
                    let _: () = conn.del(&cache_key).await.map_err(Error::RedisError)?;
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }

    /// Store candles in cache
    async fn store_in_cache(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_ts: i64,
        end_ts: i64,
        candles: &[EnhancedCandle],
    ) -> Result<(), Error> {
        let cache_key = cache_key(chain, pool_address, interval, start_ts, end_ts);
        let mut conn = self.redis_client.get_connection();

        let serialized = serde_json::to_string(candles).map_err(|e| {
            Error::AnyhowError(anyhow::anyhow!("Failed to serialize candles: {}", e))
        })?;

        let _: () = conn
            .set_ex(&cache_key, &serialized, self.config.redis_ttl_seconds)
            .await
            .map_err(Error::RedisError)?;

        Ok(())
    }

    /// Check if cached data is stale
    fn is_cache_stale(&self, candles: &[EnhancedCandle]) -> bool {
        if candles.is_empty() {
            return true;
        }

        // Check if any candle is stale
        candles.iter().any(|c| c.is_stale(self.config.max_cache_age_seconds))
    }

    /// Aggregate candles with distributed locking
    async fn aggregate_and_cache(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_ts: i64,
        end_ts: i64,
    ) -> Result<Vec<EnhancedCandle>, Error> {
        // Create distributed lock to prevent concurrent aggregation
        let lock_key =
            candle_aggregation_lock_key(&chain.to_string(), pool_address, interval.as_str());

        let lock_guard = DistributedLockGuard::new_with_retry(
            self.redis_client.clone(),
            &lock_key,
            self.config.lock_timeout_seconds,
            self.config.lock_retry_interval,
            self.config.max_lock_wait,
        )
        .await?;

        if !lock_guard.is_acquired() {
            // Failed to acquire lock, try cache again (maybe another process completed)
            if let Some(cached_candles) =
                self.get_from_cache(chain, pool_address, interval, start_ts, end_ts).await?
            {
                return Ok(cached_candles);
            }

            return Err(Error::AnyhowError(anyhow::anyhow!(
                "Failed to acquire lock for candle aggregation and no cached data available"
            )));
        }

        // Check cache again after acquiring lock
        if let Some(cached_candles) =
            self.get_from_cache(chain, pool_address, interval, start_ts, end_ts).await?
        {
            if !self.is_cache_stale(&cached_candles) {
                return Ok(cached_candles);
            }
        }

        // Perform aggregation
        let result =
            self.perform_aggregation(chain, pool_address, interval, start_ts, end_ts).await?;

        // Cache the result
        if !result.is_empty() {
            if let Err(e) =
                self.store_in_cache(chain, pool_address, interval, start_ts, end_ts, &result).await
            {
                tracing::warn!("Failed to cache aggregated candles: {}", e);
            }
        }

        Ok(result)
    }

    /// Perform the actual aggregation from database
    async fn perform_aggregation(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_ts: i64,
        end_ts: i64,
    ) -> Result<Vec<EnhancedCandle>, Error> {
        tracing::debug!(
            "Starting aggregation for pool {} on chain {:?}, interval {:?}, range [{}, {}]",
            pool_address,
            chain,
            interval,
            start_ts,
            end_ts
        );

        // First try to get cached candles of the requested interval from database
        let db_clone = PostgresDatabase::get_indexer_db().await;
        let cached_candles =
            db_clone.get_candles(chain, pool_address, interval, start_ts, end_ts).await?;

        tracing::debug!(
            "Found {} cached candles for pool {} with interval {:?}",
            cached_candles.len(),
            pool_address,
            interval
        );

        if !cached_candles.is_empty() {
            // Check if cached candles cover the full range
            let first_open = cached_candles.first().unwrap().open_timestamp_seconds;
            let last_close = cached_candles.last().unwrap().close_timestamp_seconds;

            tracing::debug!(
                "Cached candles range: [{}, {}], requested range: [{}, {}]",
                first_open,
                last_close,
                start_ts,
                end_ts
            );

            if first_open <= start_ts && last_close >= end_ts {
                // Cached candles fully cover the requested range
                tracing::debug!(
                    "Cached candles fully cover requested range for pool {}",
                    pool_address
                );
                let enhanced_candles: Vec<EnhancedCandle> =
                    cached_candles.into_iter().map(|c| EnhancedCandle::from_candle(c)).collect();

                tracing::debug!(
                    "Returning {} candles from cache for pool {}",
                    enhanced_candles.len(),
                    pool_address
                );
                return Ok(enhanced_candles);
            } else {
                tracing::debug!(
                    "Cached candles do not fully cover requested range for pool {}",
                    pool_address
                );
            }
        }

        // Need to aggregate from 1s candles
        tracing::debug!(
            "Attempting to get 1s candles for pool {} in range [{}, {}]",
            pool_address,
            start_ts,
            end_ts
        );
        let s1_candles =
            db_clone.get_candles(chain, pool_address, CandleInterval::S1, start_ts, end_ts).await?;

        tracing::debug!(
            "Found {} 1s candles for pool {} in range [{}, {}]",
            s1_candles.len(),
            pool_address,
            start_ts,
            end_ts
        );

        // Check if we need fallback - either no candles or insufficient coverage
        let needs_fallback = if s1_candles.is_empty() {
            tracing::info!(
                "No 1s candles found for pool {} in range [{}, {}], using fallback from PoolState",
                pool_address,
                start_ts,
                end_ts
            );
            true
        } else {
            // Calculate data coverage ratio
            let coverage_ratio = self.calculate_coverage_ratio(&s1_candles, start_ts, end_ts);
            let min_coverage = if (end_ts - start_ts) > 86400 { 0.6 } else { 0.7 }; // Lower threshold for long ranges

            if coverage_ratio < min_coverage {
                tracing::info!(
                    "Insufficient data coverage for pool {} in range [{}, {}]: {:.1}% < {:.1}%, using fallback from PoolState",
                    pool_address, start_ts, end_ts, coverage_ratio * 100.0, min_coverage * 100.0
                );
                true
            } else {
                // Check for significant gaps only if coverage is borderline
                let has_gaps = self.has_candle_gaps(&s1_candles, start_ts, end_ts);
                if has_gaps {
                    tracing::info!(
                        "Data gaps detected for pool {} in range [{}, {}] (coverage: {:.1}%), using fallback from PoolState",
                        pool_address, start_ts, end_ts, coverage_ratio * 100.0
                    );
                    true
                } else {
                    false
                }
            }
        };

        if needs_fallback {
            return self
                .fallback_from_pool_state(chain, pool_address, interval, start_ts, end_ts)
                .await;
        }

        // Convert to EnhancedCandle and aggregate
        let s1_candles_v2: Vec<EnhancedCandle> =
            s1_candles.into_iter().map(|c| EnhancedCandle::from_candle(c)).collect();
        let aggregated_candles = if interval == CandleInterval::S1 {
            s1_candles_v2
        } else {
            EnhancedCandle::aggregate(&s1_candles_v2, interval)
        };

        // Asynchronously save aggregated candles to database for future use
        if !aggregated_candles.is_empty() && interval != CandleInterval::S1 {
            let db_clone = PostgresDatabase::get_indexer_db().await;
            let candles_to_save: Vec<Candle> =
                aggregated_candles.iter().map(|c| c.to_candle()).collect();

            tokio::spawn(async move {
                if let Err(e) = db_clone.insert_or_update_candles(&candles_to_save).await {
                    tracing::error!("Failed to save aggregated candles to database: {}", e);
                }
            });
        }

        Ok(aggregated_candles)
    }

    /// Fallback method to generate candles from PoolState data when candles are missing
    async fn fallback_from_pool_state(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_ts: i64,
        end_ts: i64,
    ) -> Result<Vec<EnhancedCandle>, Error> {
        let fallback_start_time = std::time::Instant::now();

        tracing::info!(
            "Fallback: generating candles from PoolState for pool {} on chain {:?} in range [{}, {}] with interval {:?}",
            pool_address, chain, start_ts, end_ts, interval
        );

        let db_clone = PostgresDatabase::get_indexer_db().await;

        // Get PoolState data in the time range (convert to milliseconds)
        let start_ts_millis = start_ts * 1000;
        let end_ts_millis = end_ts * 1000;

        tracing::debug!(
            "Querying pool_states for pool {} in time range: {} to {} (millis: {} to {})",
            pool_address,
            start_ts,
            end_ts,
            start_ts_millis,
            end_ts_millis
        );

        let pool_states = match db_clone
            .get_pool_states_in_time_range(chain, pool_address, start_ts_millis, end_ts_millis)
            .await
        {
            Ok(states) => states,
            Err(e) => {
                tracing::error!(
                    "Failed to query pool_states for pool {} in range [{}, {}] (millis: [{}, {}]): {}",
                    pool_address,
                    start_ts,
                    end_ts,
                    start_ts_millis,
                    end_ts_millis,
                    e
                );
                return Err(e);
            }
        };

        if pool_states.is_empty() {
            let fallback_duration = fallback_start_time.elapsed();
            tracing::warn!(
                "Fallback failed: no PoolState data found for pool {} on chain {:?} in range [{}, {}] (millis: [{}, {}]) (duration: {:?})",
                pool_address, chain, start_ts, end_ts, start_ts_millis, end_ts_millis, fallback_duration
            );
            return Ok(Vec::new());
        }

        tracing::info!(
            "Found {} PoolState records for fallback candle generation for pool {} in range [{}, {}]",
            pool_states.len(),
            pool_address,
            start_ts,
            end_ts
        );

        // Validate and log sample of pool states for debugging
        if !pool_states.is_empty() {
            // Validate pool state data integrity
            let mut valid_states = 0;
            let mut invalid_states = 0;

            for state in &pool_states {
                if self.validate_pool_state(state) {
                    valid_states += 1;
                } else {
                    invalid_states += 1;
                }
            }

            tracing::debug!(
                "Pool state validation: {} valid, {} invalid out of {} total",
                valid_states,
                invalid_states,
                pool_states.len()
            );

            if invalid_states > 0 {
                tracing::warn!(
                    "Found {} invalid pool states for pool {}, proceeding with valid ones",
                    invalid_states,
                    pool_address
                );
            }

            let first_state = &pool_states[0];
            let last_state = &pool_states[pool_states.len() - 1];
            tracing::debug!(
                "PoolState sample - First: timestamp={}, price={}, volume={}, txns={}",
                first_state.timestamp_millis,
                first_state.price,
                first_state.total_volume,
                first_state.total_txns
            );
            tracing::debug!(
                "PoolState sample - Last: timestamp={}, price={}, volume={}, txns={}",
                last_state.timestamp_millis,
                last_state.price,
                last_state.total_volume,
                last_state.total_txns
            );
        }

        // Log sample of pool states for debugging
        if !pool_states.is_empty() {
            let first_state = &pool_states[0];
            let last_state = &pool_states[pool_states.len() - 1];
            tracing::debug!(
                "PoolState sample - First: timestamp={}, price={}, volume={}, txns={}",
                first_state.timestamp_millis,
                first_state.price,
                first_state.total_volume,
                first_state.total_txns
            );
            tracing::debug!(
                "PoolState sample - Last: timestamp={}, price={}, volume={}, txns={}",
                last_state.timestamp_millis,
                last_state.price,
                last_state.total_volume,
                last_state.total_txns
            );
        }

        // Get price data for the time range
        tracing::debug!(
            "Getting native token price for chain {:?} in time range [{}, {}]",
            chain,
            start_ts,
            end_ts
        );
        let price_manager = NativeTokenPriceManager::get().await;
        let start_price = price_manager.get_nearest_price(chain, start_ts).await;
        let end_price = price_manager.get_nearest_price(chain, end_ts).await;

        tracing::debug!("Native token prices - start: {:?}, end: {:?}", start_price, end_price);

        let native_token_usd_price = match (start_price, end_price) {
            (Some(start), Some(end)) => {
                let avg_price = (start + end) / 2.0;
                tracing::debug!(
                    "Using average native token price: {} (from {} and {})",
                    avg_price,
                    start,
                    end
                );
                avg_price
            }
            (Some(price), None) | (None, Some(price)) => {
                tracing::debug!("Using single available native token price: {}", price);
                price
            }
            (None, None) => {
                let mid_timestamp = (start_ts + end_ts) / 2;
                let fallback_price = price_manager.get_nearest_price(chain, mid_timestamp).await.unwrap_or_else(|| {
                    let default_price = match chain {
                        Chain::Solana => 100.0,
                        Chain::Hypercore => 1.0,
                        Chain::HyperEvm => 1.0,
                    };
                    tracing::warn!(
                        "No native token price found for chain {:?} at any timestamp, using default: {}",
                        chain, default_price
                    );
                    default_price
                });
                tracing::debug!(
                    "Using fallback native token price: {} for chain {:?}",
                    fallback_price,
                    chain
                );
                fallback_price
            }
        };

        // Get base token USD price
        let mid_timestamp = (start_ts + end_ts) / 2;
        tracing::debug!(
            "Getting base token USD price for pool {} at timestamp {}",
            pool_address,
            mid_timestamp
        );
        let base_token_usd_price = self
            .get_base_token_usd_price(chain, pool_address, mid_timestamp, native_token_usd_price)
            .await?;

        tracing::debug!(
            "Using prices for candle generation - native_token_usd: {}, base_token_usd: {}",
            native_token_usd_price,
            base_token_usd_price
        );

        tracing::debug!(
            "Generating 1s candles from {} PoolState records using Candle::acc_states",
            pool_states.len()
        );
        let s1_candles =
            match Candle::acc_states(&pool_states, base_token_usd_price, native_token_usd_price) {
                Ok(candles) => {
                    tracing::debug!(
                        "Successfully generated {} 1s candles from PoolState data for pool {}",
                        candles.len(),
                        pool_address
                    );
                    candles
                }
                Err(e) => {
                    tracing::error!(
                        "Failed to generate candles from PoolState data for pool {}: {}",
                        pool_address,
                        e
                    );
                    return Err(e);
                }
            };

        if s1_candles.is_empty() {
            tracing::warn!(
                "Fallback failed: could not generate candles from {} PoolState records for pool {}",
                pool_states.len(),
                pool_address
            );
            return Ok(Vec::new());
        }

        // Validate generated candles before proceeding
        let original_count = s1_candles.len();
        let valid_s1_candles: Vec<_> =
            s1_candles.into_iter().filter(|candle| self.validate_candle_data(candle)).collect();

        if valid_s1_candles.len() != original_count {
            tracing::warn!(
                "Filtered out {} invalid candles, proceeding with {} valid ones",
                original_count - valid_s1_candles.len(),
                valid_s1_candles.len()
            );
        }

        if valid_s1_candles.is_empty() {
            tracing::warn!(
                "No valid candles generated from {} PoolState records for pool {}",
                pool_states.len(),
                pool_address
            );
            return Ok(Vec::new());
        }

        // Convert to EnhancedCandle and aggregate to requested interval
        let s1_candles_enhanced: Vec<EnhancedCandle> =
            valid_s1_candles.into_iter().map(|c| EnhancedCandle::from_candle(c)).collect();

        let aggregated_candles = if interval == CandleInterval::S1 {
            s1_candles_enhanced
        } else {
            EnhancedCandle::aggregate(&s1_candles_enhanced, interval)
        };

        let fallback_duration = fallback_start_time.elapsed();
        tracing::info!(
            "Fallback success: generated {} candles from {} PoolState records for pool {} in {:?}",
            aggregated_candles.len(),
            pool_states.len(),
            pool_address,
            fallback_duration
        );

        // Publish fallback candles to Kafka for WebSocket subscribers
        // Note: Only publish if we have a reasonable number of candles to avoid spam
        if aggregated_candles.len() <= 100 {
            self.publish_fallback_candles_to_kafka(&aggregated_candles).await;
        } else {
            tracing::warn!(
                "Skipping Kafka publish for {} fallback candles (too many)",
                aggregated_candles.len()
            );
        }

        Ok(aggregated_candles)
    }

    /// Calculate dynamic gap tolerance based on time range
    fn calculate_dynamic_gap_tolerance(&self, time_range_seconds: i64) -> i64 {
        // Base tolerance starts at 60 seconds for short ranges
        let base_tolerance = 60i64;

        // Scale factor based on time range
        let scale_factor = if time_range_seconds <= 3600 {
            // 0-1 hour: 1x (60s)
            1.0
        } else if time_range_seconds <= 86400 {
            // 1-24 hours: 1x to 2x (60s to 120s)
            1.0 + (time_range_seconds - 3600) as f64 / 82800.0
        } else if time_range_seconds <= 604800 {
            // 1-7 days: 2x to 5x (120s to 300s)
            2.0 + (time_range_seconds - 86400) as f64 / 172800.0
        } else {
            // >7 days: 5x to 10x (300s to 600s)
            5.0 + ((time_range_seconds - 604800) as f64 / 604800.0).min(5.0)
        };

        let dynamic_tolerance = (base_tolerance as f64 * scale_factor) as i64;

        tracing::debug!(
            "Dynamic gap tolerance: {}s for time range {}s (scale factor: {:.2})",
            dynamic_tolerance,
            time_range_seconds,
            scale_factor
        );

        dynamic_tolerance
    }

    /// Calculate data coverage ratio (0.0 to 1.0)
    fn calculate_coverage_ratio(&self, candles: &[Candle], start_ts: i64, end_ts: i64) -> f64 {
        if candles.is_empty() {
            return 0.0;
        }

        let total_requested_time = end_ts - start_ts;
        if total_requested_time <= 0 {
            return 0.0;
        }

        let mut covered_time = 0i64;
        for candle in candles {
            let candle_start = candle.open_timestamp_seconds.max(start_ts);
            let candle_end = candle.close_timestamp_seconds.min(end_ts);
            if candle_end > candle_start {
                covered_time += candle_end - candle_start;
            }
        }

        (covered_time as f64 / total_requested_time as f64).min(1.0)
    }

    /// Check if candles have gaps or insufficient coverage
    fn has_candle_gaps(&self, candles: &[Candle], start_ts: i64, end_ts: i64) -> bool {
        if candles.is_empty() {
            return true;
        }

        // Sort candles by timestamp for gap detection
        let mut sorted_candles = candles.to_vec();
        sorted_candles.sort_by_key(|c| c.open_timestamp_seconds);

        let first_candle_time = sorted_candles.first().unwrap().open_timestamp_seconds;
        let last_candle_time = sorted_candles.last().unwrap().close_timestamp_seconds;

        // Check if candles cover the requested time range (with dynamic tolerance)
        let time_range_seconds = end_ts - start_ts;
        let gap_tolerance = self.calculate_dynamic_gap_tolerance(time_range_seconds) / 2; // Use half for boundary tolerance
        let has_start_gap = (start_ts - first_candle_time).abs() > gap_tolerance;
        let has_end_gap = (end_ts - last_candle_time).abs() > gap_tolerance;

        if has_start_gap || has_end_gap {
            tracing::debug!(
                "Coverage gap detected: requested=[{}, {}], candles=[{}, {}], start_gap={}, end_gap={}",
                start_ts, end_ts, first_candle_time, last_candle_time, has_start_gap, has_end_gap
            );
            return true;
        }

        // Check for significant gaps between consecutive candles with dynamic tolerance
        let max_allowed_gap = self.calculate_dynamic_gap_tolerance(time_range_seconds);
        let mut gap_count = 0;
        let total_windows = sorted_candles.len().saturating_sub(1);

        for window in sorted_candles.windows(2) {
            let current_end = window[0].close_timestamp_seconds;
            let next_start = window[1].open_timestamp_seconds;
            let gap = next_start - current_end;

            if gap > max_allowed_gap {
                gap_count += 1;
                tracing::debug!(
                    "Gap detected between candles: {} seconds between {} and {}",
                    gap,
                    current_end,
                    next_start
                );
            }
        }

        // Only trigger fallback if more than 30% of intervals have significant gaps
        if total_windows > 0 && (gap_count as f64 / total_windows as f64) > 0.3 {
            tracing::debug!(
                "Too many gaps detected: {}/{} intervals have gaps > {}s",
                gap_count,
                total_windows,
                max_allowed_gap
            );
            return true;
        }

        false
    }

    /// Validate pool state data integrity
    fn validate_pool_state(&self, state: &PoolState) -> bool {
        // Check for reasonable price values
        if state.price <= 0.0 || !state.price.is_finite() {
            tracing::debug!(
                "Invalid price in pool state: {} at timestamp {}",
                state.price,
                state.timestamp_millis
            );
            return false;
        }

        // Check for reasonable market cap values
        if state.market_cap < 0.0 || !state.market_cap.is_finite() {
            tracing::debug!(
                "Invalid market cap in pool state: {} at timestamp {}",
                state.market_cap,
                state.timestamp_millis
            );
            return false;
        }

        // Check for reasonable volume values (should be non-negative and finite)
        if state.total_volume < 0.0 || !state.total_volume.is_finite() {
            tracing::debug!(
                "Invalid total volume in pool state: {} at timestamp {}",
                state.total_volume,
                state.timestamp_millis
            );
            return false;
        }

        // Check timestamp is reasonable (not too far in the past or future)
        let current_time_millis = chrono::Utc::now().timestamp_millis();
        let min_time_millis = 946684800000; // 2000-01-01 in milliseconds
        let max_time_millis = current_time_millis + 86400000; // 1 day in the future

        if state.timestamp_millis < min_time_millis || state.timestamp_millis > max_time_millis {
            tracing::debug!(
                "Invalid timestamp in pool state: {} (current: {})",
                state.timestamp_millis,
                current_time_millis
            );
            return false;
        }

        true
    }

    /// Validate generated candle data
    fn validate_candle_data(&self, candle: &Candle) -> bool {
        // Check for finite price values (allow zero prices for edge cases)
        let prices = [
            candle.usd_open_price,
            candle.usd_close_price,
            candle.usd_high_price,
            candle.usd_low_price,
        ];
        if prices.iter().any(|&p| !p.is_finite() || p < 0.0) {
            tracing::debug!(
                "Invalid prices in candle: open={}, close={}, high={}, low={}",
                candle.usd_open_price,
                candle.usd_close_price,
                candle.usd_high_price,
                candle.usd_low_price
            );
            return false;
        }

        // Check price relationships (high >= low, etc.)
        if candle.usd_high_price < candle.usd_low_price {
            tracing::debug!(
                "Invalid price relationship: high={} < low={}",
                candle.usd_high_price,
                candle.usd_low_price
            );
            return false;
        }

        // Check for reasonable volume (non-negative and finite)
        if candle.usd_volume < 0.0 || !candle.usd_volume.is_finite() {
            tracing::debug!("Invalid volume in candle: {}", candle.usd_volume);
            return false;
        }

        // Check timestamp ordering
        if candle.close_timestamp_seconds <= candle.open_timestamp_seconds {
            tracing::debug!(
                "Invalid timestamp ordering: open={}, close={}",
                candle.open_timestamp_seconds,
                candle.close_timestamp_seconds
            );
            return false;
        }

        true
    }

    /// Get base token USD price for a given chain and timestamp
    async fn get_base_token_usd_price(
        &self,
        chain: Chain,
        pool_address: &str,
        timestamp_seconds: i64,
        native_token_usd_price: f64,
    ) -> Result<f64, Error> {
        tracing::debug!(
            "Getting base token USD price for pool {} on chain {:?} at timestamp {}",
            pool_address,
            chain,
            timestamp_seconds
        );

        // Get pool metadata to determine base token
        let db_clone = PostgresDatabase::get_indexer_db().await;

        // Handle pool metadata query failure gracefully to prevent fallback interruption
        let pool_metadata = match db_clone.get_pool_metadata(chain, pool_address).await {
            Ok(metadata) => {
                tracing::debug!(
                    "Retrieved pool metadata for {}: base_address={:?}",
                    pool_address,
                    metadata.as_ref().map(|m| &m.base_address)
                );
                metadata
            }
            Err(e) => {
                tracing::warn!(
                    "Failed to query pool metadata for {} on chain {:?}: {}. Using default base price for fallback.",
                    pool_address, chain, e
                );
                return Ok(1.0); // Use default price instead of failing
            }
        };

        if let Some(metadata) = pool_metadata {
            tracing::debug!("Using base token address {} for price lookup", metadata.base_address);

            match crate::utils::get_base_token_usd_price(
                chain,
                &metadata.base_address,
                timestamp_seconds,
                Some(native_token_usd_price),
            )
            .await
            {
                Ok(price) => {
                    tracing::debug!(
                        "Successfully retrieved base token USD price: {} for pool {}",
                        price,
                        pool_address
                    );
                    Ok(price)
                }
                Err(e) => {
                    tracing::warn!(
                        "Failed to get base token USD price for {} (base_address={}): {}. Using default price 1.0",
                        pool_address,
                        metadata.base_address,
                        e
                    );
                    Ok(1.0)
                }
            }
        } else {
            tracing::warn!(
                "Pool metadata not found for {} on chain {:?}, using default base price 1.0",
                pool_address,
                chain
            );
            Ok(1.0)
        }
    }

    /// Publish fallback candles to Kafka for WebSocket subscribers
    /// Uses the existing publish_candle_to_kafka method for consistency
    async fn publish_fallback_candles_to_kafka(&self, enhanced_candles: &[EnhancedCandle]) {
        if enhanced_candles.is_empty() {
            return;
        }

        let db_clone = PostgresDatabase::get_indexer_db().await;
        let mut success_count = 0;
        let mut error_count = 0;

        // Convert EnhancedCandle back to Candle and publish each one
        for enhanced_candle in enhanced_candles {
            let candle = enhanced_candle.to_candle();

            match db_clone.publish_candle_to_kafka(&candle).await {
                Ok(_) => {
                    success_count += 1;
                    tracing::debug!(
                        "Published fallback candle to Kafka: pool={}, interval={}, timestamp={}",
                        candle.pool_address,
                        candle.interval.as_str(),
                        candle.open_timestamp_seconds
                    );
                }
                Err(e) => {
                    error_count += 1;
                    tracing::warn!(
                        "Failed to publish fallback candle to Kafka: pool={}, interval={}, timestamp={}, error={}",
                        candle.pool_address,
                        candle.interval.as_str(),
                        candle.open_timestamp_seconds,
                        e
                    );
                }
            }
        }

        if success_count > 0 {
            tracing::info!(
                "Successfully published {} fallback candles to Kafka for WebSocket subscribers (failed: {})",
                success_count,
                error_count
            );
        }

        if error_count > 0 {
            tracing::warn!(
                "Failed to publish {} fallback candles to Kafka. WebSocket subscribers won't receive these updates.",
                error_count
            );
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;

    /// Helper function to create test candles
    fn create_test_candles(
        count: usize,
        start_timestamp: i64,
        interval: CandleInterval,
    ) -> Vec<EnhancedCandle> {
        let mut candles = Vec::with_capacity(count);
        let interval_seconds = interval.as_seconds();

        for i in 0..count {
            candles.push(EnhancedCandle {
                chain: Chain::Solana,
                pool_address: "test_pool".to_string(),
                open_timestamp_seconds: start_timestamp + i as i64 * interval_seconds,
                close_timestamp_seconds: start_timestamp + (i as i64 + 1) * interval_seconds,
                interval,
                native_token_usd_price: 100.0,
                usd_open_price: 1.0 + i as f64 * 0.1,
                usd_close_price: 1.0 + (i + 1) as f64 * 0.1,
                usd_high_price: 1.0 + (i + 1) as f64 * 0.1,
                usd_low_price: 1.0 + i as f64 * 0.1,
                usd_open_market_cap: 1000.0,
                usd_close_market_cap: 1000.0,
                usd_high_market_cap: 1000.0,
                usd_low_market_cap: 1000.0,
                usd_volume: 100.0,
                txns: 1,
                cached_at: Some(chrono::Utc::now().timestamp()),
                access_count: Some(1),
            });
        }

        candles
    }

    #[tokio::test]
    async fn test_smart_cache_manager() {
        // Skip test if Redis is not available
        if std::env::var("REDIS_URL").is_err() {
            return;
        }

        let redis_client = RedisClient::new("redis://localhost:6379").await.unwrap();
        let config = SmartCacheConfig::default();

        let cache_manager = SmartCacheManager::new(redis_client, config);

        let chain = Chain::Solana;
        let pool_address = Pubkey::new_unique().to_string();
        let interval = CandleInterval::M5;
        let start_ts = 1718000000;
        let end_ts = 1718003600; // 1 hour later

        // Test cache miss scenario
        let result =
            cache_manager.get_candles(chain, &pool_address, interval, start_ts, end_ts).await;

        // Should succeed even with no data
        assert!(result.is_ok());
        let candles = result.unwrap();
        assert!(candles.is_empty()); // No data expected for new pool
    }

    #[test]
    fn test_enhanced_candle_aggregation() {
        let candles = create_test_candles(10, 1718000000, CandleInterval::S1);

        // Test aggregation to 5m interval
        let aggregated = EnhancedCandle::aggregate(&candles, CandleInterval::M5);

        // Should aggregate all 10 seconds into one 5-minute candle
        assert_eq!(aggregated.len(), 1);

        let candle = &aggregated[0];
        assert_eq!(candle.interval, CandleInterval::M5);
        assert_eq!(candle.usd_open_price, 1.0); // First candle's open
        assert_eq!(candle.usd_close_price, 2.0); // Last candle's close
        assert_eq!(candle.usd_volume, 1000.0); // Sum of all volumes
        assert_eq!(candle.txns, 10); // Sum of all transactions
    }

    #[test]
    fn test_enhanced_candle_fill_gaps() {
        // Create candles with gaps (5 minutes apart, but 15 minutes between them)
        let mut candles = create_test_candles(1, 1718000000, CandleInterval::M5);
        candles.extend(create_test_candles(1, 1718000900, CandleInterval::M5)); // 15 minutes later

        // Adjust the second candle's prices to be different
        candles[1].usd_open_price = 1.2;
        candles[1].usd_close_price = 1.3;
        candles[1].usd_volume = 150.0;
        candles[1].txns = 8;

        let filled = EnhancedCandle::fill_gaps(candles, CandleInterval::M5);

        // Should have original 2 candles + 2 gap-filling candles
        assert_eq!(filled.len(), 4);

        // Check that gap-filling candles have zero volume
        assert_eq!(filled[1].usd_volume, 0.0);
        assert_eq!(filled[2].usd_volume, 0.0);

        // Check that gap-filling candles use previous close price
        assert_eq!(filled[1].usd_open_price, filled[0].usd_close_price);
        assert_eq!(filled[1].usd_close_price, filled[0].usd_close_price);
    }

    #[tokio::test]
    async fn test_coverage_ratio_calculation() {
        use crate::postgres::{
            aggregator::Candle,
            enums::{CandleInterval, Chain},
        };

        // Skip test if Redis is not available
        let redis_client = match RedisClient::new("redis://localhost:6379").await {
            Ok(client) => client,
            Err(_) => return, // Skip test if Redis not available
        };
        let config = SmartCacheConfig::default();
        let cache_manager = SmartCacheManager::new(redis_client, config);

        // Test case 1: Full coverage
        let candles = vec![
            Candle {
                chain: Chain::Solana,
                pool_address: "test".to_string(),
                open_timestamp_seconds: 1000,
                close_timestamp_seconds: 1010,
                interval: CandleInterval::S1,
                native_token_usd_price: 100.0,
                usd_open_price: 1.0,
                usd_close_price: 1.1,
                usd_high_price: 1.2,
                usd_low_price: 0.9,
                usd_open_market_cap: 1000.0,
                usd_close_market_cap: 1100.0,
                usd_high_market_cap: 1200.0,
                usd_low_market_cap: 900.0,
                usd_volume: 100.0,
                txns: 10,
            },
            Candle {
                chain: Chain::Solana,
                pool_address: "test".to_string(),
                open_timestamp_seconds: 1010,
                close_timestamp_seconds: 1020,
                interval: CandleInterval::S1,
                native_token_usd_price: 100.0,
                usd_open_price: 1.1,
                usd_close_price: 1.2,
                usd_high_price: 1.3,
                usd_low_price: 1.0,
                usd_open_market_cap: 1100.0,
                usd_close_market_cap: 1200.0,
                usd_high_market_cap: 1300.0,
                usd_low_market_cap: 1000.0,
                usd_volume: 150.0,
                txns: 15,
            },
        ];

        let coverage = cache_manager.calculate_coverage_ratio(&candles, 1000, 1020);
        assert_eq!(coverage, 1.0, "Should have 100% coverage");

        // Test case 2: Partial coverage
        let coverage = cache_manager.calculate_coverage_ratio(&candles, 1000, 1030);
        assert_eq!(coverage, 2.0 / 3.0, "Should have 66.7% coverage");

        // Test case 3: No coverage
        let coverage = cache_manager.calculate_coverage_ratio(&[], 1000, 1020);
        assert_eq!(coverage, 0.0, "Should have 0% coverage");

        println!("Coverage ratio tests passed!");
    }

    #[tokio::test]
    async fn test_dynamic_gap_tolerance() {
        // Skip test if Redis is not available
        let redis_client = match RedisClient::new("redis://localhost:6379").await {
            Ok(client) => client,
            Err(_) => return, // Skip test if Redis not available
        };
        let config = SmartCacheConfig::default();
        let cache_manager = SmartCacheManager::new(redis_client, config);

        // Test different time ranges
        let test_cases = vec![
            (1800, 60),     // 30 minutes -> 60s tolerance
            (3600, 60),     // 1 hour -> 60s tolerance
            (43200, 90),    // 12 hours -> ~90s tolerance
            (86400, 120),   // 24 hours -> 120s tolerance
            (259200, 180),  // 3 days -> ~180s tolerance
            (604800, 300),  // 7 days -> 300s tolerance
            (1209600, 450), // 14 days -> ~450s tolerance
        ];

        for (time_range, expected_min) in test_cases {
            let tolerance = cache_manager.calculate_dynamic_gap_tolerance(time_range);
            println!("Time range: {}s -> Tolerance: {}s", time_range, tolerance);

            // Tolerance should be reasonable (between expected_min and expected_min * 2)
            assert!(
                tolerance >= expected_min,
                "Tolerance {}s should be >= {}s for range {}s",
                tolerance,
                expected_min,
                time_range
            );
            assert!(
                tolerance <= expected_min * 2,
                "Tolerance {}s should be <= {}s for range {}s",
                tolerance,
                expected_min * 2,
                time_range
            );
        }

        println!("Dynamic gap tolerance tests passed!");
    }

    #[tokio::test]
    async fn test_fallback_from_pool_state_logic() {
        // This test verifies the fallback logic without requiring database connection
        use crate::postgres::indexer::PoolState;

        // Create test pool states
        let pool_states = vec![
            PoolState {
                chain: Chain::Solana,
                pool_address: "test_pool".to_string(),
                block_number: 100,
                timestamp_millis: 1640995200000, // 2022-01-01 00:00:00
                price: 10.0,
                market_cap: 1000000.0,
                liquidity: 50000.0,
                total_volume: 100000.0,
                total_buy_volume: 60000.0,
                total_sell_volume: 40000.0,
                total_txns: 500,
                total_buy_txns: 300,
                total_sell_txns: 200,
                bonding_curve_progress: Some(0.5),
            },
            PoolState {
                chain: Chain::Solana,
                pool_address: "test_pool".to_string(),
                block_number: 101,
                timestamp_millis: 1640995260000, // 2022-01-01 00:01:00
                price: 11.0,
                market_cap: 1100000.0,
                liquidity: 55000.0,
                total_volume: 110000.0,
                total_buy_volume: 66000.0,
                total_sell_volume: 44000.0,
                total_txns: 550,
                total_buy_txns: 330,
                total_sell_txns: 220,
                bonding_curve_progress: Some(0.6),
            },
        ];

        // Test the acc_states function directly
        let base_usd_price = 100.0; // SOL price
        let native_usd_price = 100.0;

        let result = Candle::acc_states(&pool_states, base_usd_price, native_usd_price);
        assert!(result.is_ok(), "acc_states should succeed with valid pool states");

        let candles = result.unwrap();
        assert!(!candles.is_empty(), "Should generate at least one candle");

        // Verify candle properties
        for candle in &candles {
            assert_eq!(candle.chain, Chain::Solana);
            assert_eq!(candle.pool_address, "test_pool");
            assert_eq!(candle.interval, CandleInterval::S1);
            assert!(candle.usd_volume >= 0.0, "Volume should be non-negative");
            // assert!(candle.txns >= 0, "Transaction count should be non-negative");
        }

        println!("Generated {} candles from {} pool states", candles.len(), pool_states.len());
    }
}
