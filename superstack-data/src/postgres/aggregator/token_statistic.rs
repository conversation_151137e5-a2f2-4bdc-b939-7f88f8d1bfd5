use std::str::FromStr;

use alloy::primitives::U256;
use bigdecimal::BigDecimal;
use serde::{Deserialize, Serialize};

use crate::{
    postgres::{aggregator::TokenState, indexer::TokenMetadata, *},
    Error,
};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, sqlx::FromRow)]
pub struct TokenStatistic {
    pub chain: Chain,
    pub token_address: String,

    pub name: String,
    pub symbol: String,

    pub decimals: u8,
    pub supply: String,

    pub description: Option<String>,
    pub image: Option<String>,
    pub website: Option<String>,
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub dex_paid: DexPaid,

    pub is_trench_token: bool,

    pub create_dex: Dex,
    pub create_block_number: Option<u64>,
    pub create_tx_hash: Option<String>,
    pub create_bonding_curve: Option<String>,
    pub create_dev: Option<String>,
    pub create_timestamp_millis: i64,

    pub migration_pool_address: Option<String>,
    pub migration_timestamp_millis: i64,

    pub update_timestamp_millis: i64,

    pub is_mutable: Option<bool>,
    pub update_authority: Option<String>,
    pub mint_authority: Option<String>,
    pub freeze_authority: Option<String>,

    pub best_pool_address: String,
    pub pool_addresses: String,

    pub usd_price: f64,
    pub usd_market_cap: f64,
    pub usd_liquidity: f64,

    pub bonding_curve_progress: Option<f64>,

    pub usd_total_volume: f64,
    pub usd_total_buy_volume: f64,
    pub usd_total_sell_volume: f64,

    pub total_txns: u64,
    pub total_buy_txns: u64,
    pub total_sell_txns: u64,

    pub total_trend: f64,
    pub total_price_change: f64,

    pub trend_5m: f64,
    pub price_change_5m: f64,
    pub txns_5m: u64,
    pub buy_txns_5m: u64,
    pub sell_txns_5m: u64,
    pub usd_volume_5m: f64,
    pub usd_buy_volume_5m: f64,
    pub usd_sell_volume_5m: f64,

    pub trend_1h: f64,
    pub price_change_1h: f64,
    pub txns_1h: u64,
    pub buy_txns_1h: u64,
    pub sell_txns_1h: u64,
    pub usd_volume_1h: f64,
    pub usd_buy_volume_1h: f64,
    pub usd_sell_volume_1h: f64,

    pub trend_6h: f64,
    pub price_change_6h: f64,
    pub txns_6h: u64,
    pub buy_txns_6h: u64,
    pub sell_txns_6h: u64,
    pub usd_volume_6h: f64,
    pub usd_buy_volume_6h: f64,
    pub usd_sell_volume_6h: f64,

    pub trend_24h: f64,
    pub price_change_24h: f64,
    pub txns_24h: u64,
    pub buy_txns_24h: u64,
    pub sell_txns_24h: u64,
    pub usd_volume_24h: f64,
    pub usd_buy_volume_24h: f64,
    pub usd_sell_volume_24h: f64,

    pub trend_3d: f64,
    pub price_change_3d: f64,
    pub txns_3d: u64,
    pub buy_txns_3d: u64,
    pub sell_txns_3d: u64,
    pub usd_volume_3d: f64,
    pub usd_buy_volume_3d: f64,
    pub usd_sell_volume_3d: f64,

    pub trend_7d: f64,
    pub price_change_7d: f64,
    pub txns_7d: u64,
    pub buy_txns_7d: u64,
    pub sell_txns_7d: u64,
    pub usd_volume_7d: f64,
    pub usd_buy_volume_7d: f64,
    pub usd_sell_volume_7d: f64,

    pub dev_hold_percentage: Option<f64>,
    pub dev_sold_percentage: Option<f64>,
    pub top10_hold_percentage: Option<f64>,
    pub sniper_hold_percentage: Option<f64>,
    pub insider_hold_percentage: Option<f64>,
    pub bot_hold_percentage: Option<f64>,
    pub holder_count: Option<u64>,
    pub sniper_count: Option<u64>,
    pub insider_count: Option<u64>,
    pub bot_count: Option<u64>,

    // New fields
    pub is_active: bool,
    pub best_pool_dex: Dex,
    pub pool_dexes: String,
    pub init_usd_price: Option<f64>,
    pub image_path: Option<String>,
}

#[derive(Debug, Clone, Default)]
pub struct RangedTokenStatistic {
    pub trend: f64,
    pub price_change: f64,
    pub txns: u64,
    pub buy_txns: u64,
    pub sell_txns: u64,
    pub usd_volume: f64,
    pub usd_buy_volume: f64,
    pub usd_sell_volume: f64,
}

impl TokenStatistic {
    pub fn new(
        token_metadata: &TokenMetadata,
        token_state: &TokenState,
        native_token_usd_price: f64,
        init_usd_price: f64,
    ) -> Self {
        Self {
            chain: token_metadata.chain.clone(),
            token_address: token_metadata.address.to_string(),
            name: token_metadata.name.clone(),
            symbol: token_metadata.symbol.clone(),
            decimals: token_metadata.decimals,
            supply: token_metadata.supply.to_string(),
            description: token_metadata.description.clone(),
            image: token_metadata.image.clone(),
            website: token_metadata.website.clone(),
            twitter: token_metadata.twitter.clone(),
            telegram: token_metadata.telegram.clone(),
            dex_paid: token_metadata.dex_paid.clone(),
            is_trench_token: token_metadata.is_trench_token,
            create_dex: token_metadata.create_dex.clone(),
            create_block_number: token_metadata.create_block_number,
            create_tx_hash: token_metadata.create_tx_hash.clone(),
            create_bonding_curve: token_metadata.create_bonding_curve.clone(),
            create_dev: token_metadata.create_dev.clone(),
            create_timestamp_millis: token_metadata.create_timestamp_millis,
            migration_pool_address: token_metadata.migration_pool_address.clone(),
            migration_timestamp_millis: token_metadata.migration_timestamp_millis,
            is_mutable: token_metadata.is_mutable,
            update_authority: token_metadata.update_authority.clone(),
            mint_authority: token_metadata.mint_authority.clone(),
            freeze_authority: token_metadata.freeze_authority.clone(),
            best_pool_address: token_state.best_pool_address.clone(),
            pool_addresses: token_state.best_pool_address.clone(),
            usd_price: token_state.usd_price,
            usd_market_cap: token_state.usd_market_cap,
            usd_liquidity: token_state.usd_token_liquidity +
                token_state.native_token_liquidity * native_token_usd_price,
            bonding_curve_progress: token_state.bonding_curve_progress,
            usd_total_volume: token_state.total_usd_token_volume +
                token_state.total_native_token_volume * native_token_usd_price,
            usd_total_buy_volume: token_state.total_usd_token_buy_volume +
                token_state.total_native_token_buy_volume * native_token_usd_price,
            usd_total_sell_volume: token_state.total_usd_token_sell_volume +
                token_state.total_native_token_sell_volume * native_token_usd_price,
            total_txns: token_state.total_txns,
            total_buy_txns: token_state.total_buy_txns,
            total_sell_txns: token_state.total_sell_txns,
            total_trend: 0.0,
            total_price_change: 0.0,
            trend_5m: 0.0,
            price_change_5m: 0.0,
            txns_5m: 0,
            buy_txns_5m: 0,
            sell_txns_5m: 0,
            usd_volume_5m: 0.0,
            usd_buy_volume_5m: 0.0,
            usd_sell_volume_5m: 0.0,
            trend_1h: 0.0,
            price_change_1h: 0.0,
            txns_1h: 0,
            buy_txns_1h: 0,
            sell_txns_1h: 0,
            usd_volume_1h: 0.0,
            usd_buy_volume_1h: 0.0,
            usd_sell_volume_1h: 0.0,
            trend_6h: 0.0,
            price_change_6h: 0.0,
            txns_6h: 0,
            buy_txns_6h: 0,
            sell_txns_6h: 0,
            usd_volume_6h: 0.0,
            usd_buy_volume_6h: 0.0,
            usd_sell_volume_6h: 0.0,
            trend_24h: 0.0,
            price_change_24h: 0.0,
            txns_24h: 0,
            buy_txns_24h: 0,
            sell_txns_24h: 0,
            usd_volume_24h: 0.0,
            usd_buy_volume_24h: 0.0,
            usd_sell_volume_24h: 0.0,
            trend_3d: 0.0,
            price_change_3d: 0.0,
            txns_3d: 0,
            buy_txns_3d: 0,
            sell_txns_3d: 0,
            usd_volume_3d: 0.0,
            usd_buy_volume_3d: 0.0,
            usd_sell_volume_3d: 0.0,
            trend_7d: 0.0,
            price_change_7d: 0.0,
            txns_7d: 0,
            buy_txns_7d: 0,
            sell_txns_7d: 0,
            usd_volume_7d: 0.0,
            usd_buy_volume_7d: 0.0,
            usd_sell_volume_7d: 0.0,
            dev_hold_percentage: None,
            dev_sold_percentage: None,
            top10_hold_percentage: None,
            sniper_hold_percentage: None,
            insider_hold_percentage: None,
            bot_hold_percentage: None,
            holder_count: None,
            sniper_count: None,
            insider_count: None,
            bot_count: None,
            update_timestamp_millis: token_state.timestamp_millis,
            is_active: token_metadata.is_active,
            best_pool_dex: token_state.best_pool_dex,
            pool_dexes: token_state.best_pool_dex.to_string(),
            init_usd_price: Some(init_usd_price),
            image_path: token_metadata.image_path.clone(),
        }
    }

    pub fn u256_supply(&self) -> Result<U256, Error> {
        let supply = U256::from_str(&self.supply).map_err(|e| Error::U256Error(e.to_string()))?;
        Ok(supply)
    }

    pub fn big_decimal_supply(&self) -> Result<BigDecimal, Error> {
        let supply = BigDecimal::from_str(&self.supply)
            .map_err(|e| Error::BigDecimalError(e.to_string()))?;
        Ok(supply)
    }

    pub fn update_5m(&mut self, ranged_token_statistics: RangedTokenStatistic) {
        self.trend_5m = ranged_token_statistics.trend;
        self.price_change_5m = ranged_token_statistics.price_change;
        self.txns_5m = ranged_token_statistics.txns;
        self.buy_txns_5m = ranged_token_statistics.buy_txns;
        self.sell_txns_5m = ranged_token_statistics.sell_txns;
        self.usd_volume_5m = ranged_token_statistics.usd_volume;
        self.usd_buy_volume_5m = ranged_token_statistics.usd_buy_volume;
        self.usd_sell_volume_5m = ranged_token_statistics.usd_sell_volume;
    }

    pub fn update_1h(&mut self, ranged_token_statistics: RangedTokenStatistic) {
        self.trend_1h = ranged_token_statistics.trend;
        self.price_change_1h = ranged_token_statistics.price_change;
        self.txns_1h = ranged_token_statistics.txns;
        self.buy_txns_1h = ranged_token_statistics.buy_txns;
        self.sell_txns_1h = ranged_token_statistics.sell_txns;
        self.usd_volume_1h = ranged_token_statistics.usd_volume;
        self.usd_buy_volume_1h = ranged_token_statistics.usd_buy_volume;
        self.usd_sell_volume_1h = ranged_token_statistics.usd_sell_volume;
    }

    pub fn update_6h(&mut self, ranged_token_statistics: RangedTokenStatistic) {
        self.trend_6h = ranged_token_statistics.trend;
        self.price_change_6h = ranged_token_statistics.price_change;
        self.txns_6h = ranged_token_statistics.txns;
        self.buy_txns_6h = ranged_token_statistics.buy_txns;
        self.sell_txns_6h = ranged_token_statistics.sell_txns;
        self.usd_volume_6h = ranged_token_statistics.usd_volume;
        self.usd_buy_volume_6h = ranged_token_statistics.usd_buy_volume;
        self.usd_sell_volume_6h = ranged_token_statistics.usd_sell_volume;
    }

    pub fn update_24h(&mut self, ranged_token_statistics: RangedTokenStatistic) {
        self.trend_24h = ranged_token_statistics.trend;
        self.price_change_24h = ranged_token_statistics.price_change;
        self.txns_24h = ranged_token_statistics.txns;
        self.buy_txns_24h = ranged_token_statistics.buy_txns;
        self.sell_txns_24h = ranged_token_statistics.sell_txns;
        self.usd_volume_24h = ranged_token_statistics.usd_volume;
        self.usd_buy_volume_24h = ranged_token_statistics.usd_buy_volume;
        self.usd_sell_volume_24h = ranged_token_statistics.usd_sell_volume;
    }

    pub fn update_3d(&mut self, ranged_token_statistics: RangedTokenStatistic) {
        self.trend_3d = ranged_token_statistics.trend;
        self.price_change_3d = ranged_token_statistics.price_change;
        self.txns_3d = ranged_token_statistics.txns;
        self.buy_txns_3d = ranged_token_statistics.buy_txns;
        self.sell_txns_3d = ranged_token_statistics.sell_txns;
        self.usd_volume_3d = ranged_token_statistics.usd_volume;
        self.usd_buy_volume_3d = ranged_token_statistics.usd_buy_volume;
        self.usd_sell_volume_3d = ranged_token_statistics.usd_sell_volume;
    }

    pub fn update_7d(&mut self, ranged_token_statistics: RangedTokenStatistic) {
        self.trend_7d = ranged_token_statistics.trend;
        self.price_change_7d = ranged_token_statistics.price_change;
        self.txns_7d = ranged_token_statistics.txns;
        self.buy_txns_7d = ranged_token_statistics.buy_txns;
        self.sell_txns_7d = ranged_token_statistics.sell_txns;
        self.usd_volume_7d = ranged_token_statistics.usd_volume;
        self.usd_buy_volume_7d = ranged_token_statistics.usd_buy_volume;
        self.usd_sell_volume_7d = ranged_token_statistics.usd_sell_volume;
    }
}

impl PostgresDatabase {
    pub async fn insert_or_update_token_statistic(
        &self,
        token_statistic: &TokenStatistic,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO token_statistic (
                chain, token_address,
                name, symbol,
                decimals, supply,
                description, image, website, twitter, telegram, dex_paid,
                is_trench_token,
                create_dex, create_block_number, create_tx_hash, create_bonding_curve, create_dev, create_timestamp_millis,
                migration_pool_address, migration_timestamp_millis,
                update_timestamp_millis,
                is_mutable, update_authority, mint_authority, freeze_authority,
                best_pool_address, pool_addresses,
                usd_price, usd_market_cap, usd_liquidity,
                bonding_curve_progress,
                usd_total_volume, usd_total_buy_volume, usd_total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                total_trend, total_price_change,
                trend_5m, price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m,
                trend_1h, price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h,
                trend_6h, price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h,
                trend_24h, price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h,
                trend_3d, price_change_3d, txns_3d, buy_txns_3d, sell_txns_3d, usd_volume_3d, usd_buy_volume_3d, usd_sell_volume_3d,
                trend_7d, price_change_7d, txns_7d, buy_txns_7d, sell_txns_7d, usd_volume_7d, usd_buy_volume_7d, usd_sell_volume_7d,
                dev_hold_percentage, dev_sold_percentage, top10_hold_percentage, sniper_hold_percentage, insider_hold_percentage, bot_hold_percentage,
                holder_count, sniper_count, insider_count, bot_count,
                is_active, best_pool_dex, pool_dexes,
                init_usd_price, image_path
            )
            VALUES (
                $1, $2,
                $3, $4,
                $5, $6,
                $7, $8, $9, $10, $11, $12,
                $13,
                $14, $15, $16, $17, $18, $19,
                $20, $21,
                $22,
                $23, $24, $25, $26,
                $27, $28,
                $29, $30, $31,
                $32,
                $33, $34, $35,
                $36, $37, $38,
                $39, $40,
                $41, $42, $43, $44, $45, $46, $47, $48,
                $49, $50, $51, $52, $53, $54, $55, $56,
                $57, $58, $59, $60, $61, $62, $63, $64,
                $65, $66, $67, $68, $69, $70, $71, $72,
                $73, $74, $75, $76, $77, $78, $79, $80,
                $81, $82, $83, $84, $85, $86, $87, $88,
                $89, $90, $91, $92, $93, $94,
                $95, $96, $97, $98,
                $99, $100, $101,
                $102, $103
            )
            ON CONFLICT (chain, token_address) DO UPDATE SET
                name = EXCLUDED.name,
                symbol = EXCLUDED.symbol,
                decimals = EXCLUDED.decimals,
                supply = EXCLUDED.supply,
                description = EXCLUDED.description,
                image = EXCLUDED.image,
                website = EXCLUDED.website,
                twitter = EXCLUDED.twitter,
                telegram = EXCLUDED.telegram,
                dex_paid = EXCLUDED.dex_paid,
                is_trench_token = EXCLUDED.is_trench_token,
                create_dex = EXCLUDED.create_dex,
                create_block_number = EXCLUDED.create_block_number,
                create_tx_hash = EXCLUDED.create_tx_hash,
                create_bonding_curve = EXCLUDED.create_bonding_curve,
                create_dev = EXCLUDED.create_dev,
                create_timestamp_millis = EXCLUDED.create_timestamp_millis,
                migration_pool_address = EXCLUDED.migration_pool_address,
                migration_timestamp_millis = EXCLUDED.migration_timestamp_millis,
                is_mutable = EXCLUDED.is_mutable,
                update_authority = EXCLUDED.update_authority,
                mint_authority = EXCLUDED.mint_authority,
                freeze_authority = EXCLUDED.freeze_authority,
                best_pool_address = EXCLUDED.best_pool_address,
                pool_addresses = EXCLUDED.pool_addresses,
                usd_price = EXCLUDED.usd_price,
                usd_market_cap = EXCLUDED.usd_market_cap,
                usd_liquidity = EXCLUDED.usd_liquidity,
                bonding_curve_progress = EXCLUDED.bonding_curve_progress,
                usd_total_volume = EXCLUDED.usd_total_volume,
                usd_total_buy_volume = EXCLUDED.usd_total_buy_volume,
                usd_total_sell_volume = EXCLUDED.usd_total_sell_volume,
                total_txns = EXCLUDED.total_txns,
                total_buy_txns = EXCLUDED.total_buy_txns,
                total_sell_txns = EXCLUDED.total_sell_txns,
                total_trend = EXCLUDED.total_trend,
                total_price_change = EXCLUDED.total_price_change,
                trend_5m = EXCLUDED.trend_5m,
                price_change_5m = EXCLUDED.price_change_5m,
                txns_5m = EXCLUDED.txns_5m,
                buy_txns_5m = EXCLUDED.buy_txns_5m,
                sell_txns_5m = EXCLUDED.sell_txns_5m,
                usd_volume_5m = EXCLUDED.usd_volume_5m,
                usd_buy_volume_5m = EXCLUDED.usd_buy_volume_5m,
                usd_sell_volume_5m = EXCLUDED.usd_sell_volume_5m,
                trend_1h = EXCLUDED.trend_1h,
                price_change_1h = EXCLUDED.price_change_1h,
                txns_1h = EXCLUDED.txns_1h,
                buy_txns_1h = EXCLUDED.buy_txns_1h,
                sell_txns_1h = EXCLUDED.sell_txns_1h,
                usd_volume_1h = EXCLUDED.usd_volume_1h,
                usd_buy_volume_1h = EXCLUDED.usd_buy_volume_1h,
                usd_sell_volume_1h = EXCLUDED.usd_sell_volume_1h,
                trend_6h = EXCLUDED.trend_6h,
                price_change_6h = EXCLUDED.price_change_6h,
                txns_6h = EXCLUDED.txns_6h,
                buy_txns_6h = EXCLUDED.buy_txns_6h,
                sell_txns_6h = EXCLUDED.sell_txns_6h,
                usd_volume_6h = EXCLUDED.usd_volume_6h,
                usd_buy_volume_6h = EXCLUDED.usd_buy_volume_6h,
                usd_sell_volume_6h = EXCLUDED.usd_sell_volume_6h,
                trend_24h = EXCLUDED.trend_24h,
                price_change_24h = EXCLUDED.price_change_24h,
                txns_24h = EXCLUDED.txns_24h,
                buy_txns_24h = EXCLUDED.buy_txns_24h,
                sell_txns_24h = EXCLUDED.sell_txns_24h,
                usd_volume_24h = EXCLUDED.usd_volume_24h,
                usd_buy_volume_24h = EXCLUDED.usd_buy_volume_24h,
                usd_sell_volume_24h = EXCLUDED.usd_sell_volume_24h,
                trend_3d = EXCLUDED.trend_3d,
                price_change_3d = EXCLUDED.price_change_3d,
                txns_3d = EXCLUDED.txns_3d,
                buy_txns_3d = EXCLUDED.buy_txns_3d,
                sell_txns_3d = EXCLUDED.sell_txns_3d,
                usd_volume_3d = EXCLUDED.usd_volume_3d,
                usd_buy_volume_3d = EXCLUDED.usd_buy_volume_3d,
                usd_sell_volume_3d = EXCLUDED.usd_sell_volume_3d,
                trend_7d = EXCLUDED.trend_7d,
                price_change_7d = EXCLUDED.price_change_7d,
                txns_7d = EXCLUDED.txns_7d,
                buy_txns_7d = EXCLUDED.buy_txns_7d,
                sell_txns_7d = EXCLUDED.sell_txns_7d,
                usd_volume_7d = EXCLUDED.usd_volume_7d,
                usd_buy_volume_7d = EXCLUDED.usd_buy_volume_7d,
                usd_sell_volume_7d = EXCLUDED.usd_sell_volume_7d,
                dev_hold_percentage = EXCLUDED.dev_hold_percentage,
                dev_sold_percentage = EXCLUDED.dev_sold_percentage,
                top10_hold_percentage = EXCLUDED.top10_hold_percentage,
                sniper_hold_percentage = EXCLUDED.sniper_hold_percentage,
                insider_hold_percentage = EXCLUDED.insider_hold_percentage,
                bot_hold_percentage = EXCLUDED.bot_hold_percentage,
                holder_count = EXCLUDED.holder_count,
                sniper_count = EXCLUDED.sniper_count,
                insider_count = EXCLUDED.insider_count,
                bot_count = EXCLUDED.bot_count,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis,
                is_active = EXCLUDED.is_active,
                best_pool_dex = EXCLUDED.best_pool_dex,
                pool_dexes = EXCLUDED.pool_dexes,
                init_usd_price = EXCLUDED.init_usd_price,
                image_path = EXCLUDED.image_path
            WHERE token_statistic.update_timestamp_millis <= EXCLUDED.update_timestamp_millis
            "#,
            token_statistic.chain as Chain,
            token_statistic.token_address,
            token_statistic.name,
            token_statistic.symbol,
            token_statistic.decimals as i16,
            token_statistic.supply,
            token_statistic.description,
            token_statistic.image,
            token_statistic.website,
            token_statistic.twitter,
            token_statistic.telegram,
            token_statistic.dex_paid as DexPaid,
            token_statistic.is_trench_token,
            token_statistic.create_dex as Dex,
            token_statistic.create_block_number.map(|b| b as i64),
            token_statistic.create_tx_hash,
            token_statistic.create_bonding_curve,
            token_statistic.create_dev,
            token_statistic.create_timestamp_millis,
            token_statistic.migration_pool_address,
            token_statistic.migration_timestamp_millis,
            token_statistic.update_timestamp_millis,
            token_statistic.is_mutable,
            token_statistic.update_authority,
            token_statistic.mint_authority,
            token_statistic.freeze_authority,
            token_statistic.best_pool_address,
            token_statistic.pool_addresses,
            token_statistic.usd_price,
            token_statistic.usd_market_cap,
            token_statistic.usd_liquidity,
            token_statistic.bonding_curve_progress,
            token_statistic.usd_total_volume,
            token_statistic.usd_total_buy_volume,
            token_statistic.usd_total_sell_volume,
            token_statistic.total_txns as i64,
            token_statistic.total_buy_txns as i64,
            token_statistic.total_sell_txns as i64,
            token_statistic.total_trend,
            token_statistic.total_price_change,
            token_statistic.trend_5m,
            token_statistic.price_change_5m,
            token_statistic.txns_5m as i64,
            token_statistic.buy_txns_5m as i64,
            token_statistic.sell_txns_5m as i64,
            token_statistic.usd_volume_5m,
            token_statistic.usd_buy_volume_5m,
            token_statistic.usd_sell_volume_5m,
            token_statistic.trend_1h,
            token_statistic.price_change_1h,
            token_statistic.txns_1h as i64,
            token_statistic.buy_txns_1h as i64,
            token_statistic.sell_txns_1h as i64,
            token_statistic.usd_volume_1h,
            token_statistic.usd_buy_volume_1h,
            token_statistic.usd_sell_volume_1h,
            token_statistic.trend_6h,
            token_statistic.price_change_6h,
            token_statistic.txns_6h as i64,
            token_statistic.buy_txns_6h as i64,
            token_statistic.sell_txns_6h as i64,
            token_statistic.usd_volume_6h,
            token_statistic.usd_buy_volume_6h,
            token_statistic.usd_sell_volume_6h,
            token_statistic.trend_24h,
            token_statistic.price_change_24h,
            token_statistic.txns_24h as i64,
            token_statistic.buy_txns_24h as i64,
            token_statistic.sell_txns_24h as i64,
            token_statistic.usd_volume_24h,
            token_statistic.usd_buy_volume_24h,
            token_statistic.usd_sell_volume_24h,
            token_statistic.trend_3d,
            token_statistic.price_change_3d,
            token_statistic.txns_3d as i64,
            token_statistic.buy_txns_3d as i64,
            token_statistic.sell_txns_3d as i64,
            token_statistic.usd_volume_3d,
            token_statistic.usd_buy_volume_3d,
            token_statistic.usd_sell_volume_3d,
            token_statistic.trend_7d,
            token_statistic.price_change_7d,
            token_statistic.txns_7d as i64,
            token_statistic.buy_txns_7d as i64,
            token_statistic.sell_txns_7d as i64,
            token_statistic.usd_volume_7d,
            token_statistic.usd_buy_volume_7d,
            token_statistic.usd_sell_volume_7d,
            token_statistic.dev_hold_percentage,
            token_statistic.dev_sold_percentage,
            token_statistic.top10_hold_percentage,
            token_statistic.sniper_hold_percentage,
            token_statistic.insider_hold_percentage,
            token_statistic.bot_hold_percentage,
            token_statistic.holder_count.map(|h| h as i64),
            token_statistic.sniper_count.map(|s| s as i64),
            token_statistic.insider_count.map(|i| i as i64),
            token_statistic.bot_count.map(|b| b as i64),
            token_statistic.is_active,
            token_statistic.best_pool_dex as Dex,
            token_statistic.pool_dexes,
            token_statistic.init_usd_price,
            token_statistic.image_path,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn insert_or_update_token_statistics(
        &self,
        token_statistics: &[TokenStatistic],
    ) -> Result<(), Error> {
        let len = token_statistics.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_token_statistic(&token_statistics[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = token_statistics.iter().map(|p| p.chain).collect();
        let token_addresses: Vec<String> =
            token_statistics.iter().map(|p| p.token_address.clone()).collect();
        let names: Vec<String> = token_statistics.iter().map(|p| p.name.clone()).collect();
        let symbols: Vec<String> = token_statistics.iter().map(|p| p.symbol.clone()).collect();
        let decimals: Vec<i16> = token_statistics.iter().map(|p| p.decimals as i16).collect();
        let supplies: Vec<String> = token_statistics.iter().map(|p| p.supply.clone()).collect();
        let descriptions: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.description.clone()).collect();
        let images: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.image.clone()).collect();
        let websites: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.website.clone()).collect();
        let twitters: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.twitter.clone()).collect();
        let telegrams: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.telegram.clone()).collect();
        let dex_paids: Vec<DexPaid> =
            token_statistics.iter().map(|p| p.dex_paid as DexPaid).collect();
        let is_trench_tokens: Vec<bool> =
            token_statistics.iter().map(|p| p.is_trench_token).collect();
        let create_dexes: Vec<Dex> = token_statistics.iter().map(|p| p.create_dex as Dex).collect();
        let create_block_numbers: Vec<Option<i64>> =
            token_statistics.iter().map(|p| p.create_block_number.map(|b| b as i64)).collect();
        let create_tx_hashes: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.create_tx_hash.clone()).collect();
        let create_bonding_curves: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.create_bonding_curve.clone()).collect();
        let create_devs: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.create_dev.clone()).collect();
        let create_timestamp_millis: Vec<i64> =
            token_statistics.iter().map(|p| p.create_timestamp_millis).collect();
        let migration_pool_addresses: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.migration_pool_address.clone()).collect();
        let migration_timestamp_millis: Vec<i64> =
            token_statistics.iter().map(|p| p.migration_timestamp_millis).collect();
        let update_timestamp_millis: Vec<i64> =
            token_statistics.iter().map(|p| p.update_timestamp_millis).collect();
        let is_mutables: Vec<Option<bool>> =
            token_statistics.iter().map(|p| p.is_mutable).collect();
        let update_authorities: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.update_authority.clone()).collect();
        let mint_authorities: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.mint_authority.clone()).collect();
        let freeze_authorities: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.freeze_authority.clone()).collect();
        let best_pool_addresses: Vec<String> =
            token_statistics.iter().map(|p| p.best_pool_address.clone()).collect();
        let pool_addresses: Vec<String> =
            token_statistics.iter().map(|p| p.pool_addresses.clone()).collect();
        let usd_prices: Vec<f64> = token_statistics.iter().map(|p| p.usd_price).collect();
        let usd_market_caps: Vec<f64> = token_statistics.iter().map(|p| p.usd_market_cap).collect();
        let usd_liquidity: Vec<f64> = token_statistics.iter().map(|p| p.usd_liquidity).collect();
        let bonding_curve_progresses: Vec<Option<f64>> =
            token_statistics.iter().map(|p| p.bonding_curve_progress).collect();
        let usd_total_volumes: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_total_volume).collect();
        let usd_total_buy_volumes: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_total_buy_volume).collect();
        let usd_total_sell_volumes: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_total_sell_volume).collect();
        let total_txns: Vec<i64> = token_statistics.iter().map(|p| p.total_txns as i64).collect();
        let total_buy_txns: Vec<i64> =
            token_statistics.iter().map(|p| p.total_buy_txns as i64).collect();
        let total_sell_txns: Vec<i64> =
            token_statistics.iter().map(|p| p.total_sell_txns as i64).collect();
        let total_trends: Vec<f64> = token_statistics.iter().map(|p| p.total_trend).collect();
        let total_price_changes: Vec<f64> =
            token_statistics.iter().map(|p| p.total_price_change).collect();
        let trend_5ms: Vec<f64> = token_statistics.iter().map(|p| p.trend_5m).collect();
        let price_change_5ms: Vec<f64> =
            token_statistics.iter().map(|p| p.price_change_5m).collect();
        let txns_5ms: Vec<i64> = token_statistics.iter().map(|p| p.txns_5m as i64).collect();
        let buy_txns_5ms: Vec<i64> =
            token_statistics.iter().map(|p| p.buy_txns_5m as i64).collect();
        let sell_txns_5ms: Vec<i64> =
            token_statistics.iter().map(|p| p.sell_txns_5m as i64).collect();
        let usd_volume_5ms: Vec<f64> = token_statistics.iter().map(|p| p.usd_volume_5m).collect();
        let usd_buy_volume_5ms: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_buy_volume_5m).collect();
        let usd_sell_volume_5ms: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_sell_volume_5m).collect();
        let trend_1hs: Vec<f64> = token_statistics.iter().map(|p| p.trend_1h).collect();
        let price_change_1hs: Vec<f64> =
            token_statistics.iter().map(|p| p.price_change_1h).collect();
        let txns_1hs: Vec<i64> = token_statistics.iter().map(|p| p.txns_1h as i64).collect();
        let buy_txns_1hs: Vec<i64> =
            token_statistics.iter().map(|p| p.buy_txns_1h as i64).collect();
        let sell_txns_1hs: Vec<i64> =
            token_statistics.iter().map(|p| p.sell_txns_1h as i64).collect();
        let usd_volume_1hs: Vec<f64> = token_statistics.iter().map(|p| p.usd_volume_1h).collect();
        let usd_buy_volume_1hs: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_buy_volume_1h).collect();
        let usd_sell_volume_1hs: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_sell_volume_1h).collect();
        let trend_6hs: Vec<f64> = token_statistics.iter().map(|p| p.trend_6h).collect();
        let price_change_6hs: Vec<f64> =
            token_statistics.iter().map(|p| p.price_change_6h).collect();
        let txns_6hs: Vec<i64> = token_statistics.iter().map(|p| p.txns_6h as i64).collect();
        let buy_txns_6hs: Vec<i64> =
            token_statistics.iter().map(|p| p.buy_txns_6h as i64).collect();
        let sell_txns_6hs: Vec<i64> =
            token_statistics.iter().map(|p| p.sell_txns_6h as i64).collect();
        let usd_volume_6hs: Vec<f64> = token_statistics.iter().map(|p| p.usd_volume_6h).collect();
        let usd_buy_volume_6hs: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_buy_volume_6h).collect();
        let usd_sell_volume_6hs: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_sell_volume_6h).collect();
        let trend_24hs: Vec<f64> = token_statistics.iter().map(|p| p.trend_24h).collect();
        let price_change_24hs: Vec<f64> =
            token_statistics.iter().map(|p| p.price_change_24h).collect();
        let txns_24hs: Vec<i64> = token_statistics.iter().map(|p| p.txns_24h as i64).collect();
        let buy_txns_24hs: Vec<i64> =
            token_statistics.iter().map(|p| p.buy_txns_24h as i64).collect();
        let sell_txns_24hs: Vec<i64> =
            token_statistics.iter().map(|p| p.sell_txns_24h as i64).collect();
        let usd_volume_24hs: Vec<f64> = token_statistics.iter().map(|p| p.usd_volume_24h).collect();
        let usd_buy_volume_24hs: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_buy_volume_24h).collect();
        let usd_sell_volume_24hs: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_sell_volume_24h).collect();
        let trend_3ds: Vec<f64> = token_statistics.iter().map(|p| p.trend_3d).collect();
        let price_change_3ds: Vec<f64> =
            token_statistics.iter().map(|p| p.price_change_3d).collect();
        let txns_3ds: Vec<i64> = token_statistics.iter().map(|p| p.txns_3d as i64).collect();
        let buy_txns_3ds: Vec<i64> =
            token_statistics.iter().map(|p| p.buy_txns_3d as i64).collect();
        let sell_txns_3ds: Vec<i64> =
            token_statistics.iter().map(|p| p.sell_txns_3d as i64).collect();
        let usd_volume_3ds: Vec<f64> = token_statistics.iter().map(|p| p.usd_volume_3d).collect();
        let usd_buy_volume_3ds: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_buy_volume_3d).collect();
        let usd_sell_volume_3ds: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_sell_volume_3d).collect();
        let trend_7ds: Vec<f64> = token_statistics.iter().map(|p| p.trend_7d).collect();
        let price_change_7ds: Vec<f64> =
            token_statistics.iter().map(|p| p.price_change_7d).collect();
        let txns_7ds: Vec<i64> = token_statistics.iter().map(|p| p.txns_7d as i64).collect();
        let buy_txns_7ds: Vec<i64> =
            token_statistics.iter().map(|p| p.buy_txns_7d as i64).collect();
        let sell_txns_7ds: Vec<i64> =
            token_statistics.iter().map(|p| p.sell_txns_7d as i64).collect();
        let usd_volume_7ds: Vec<f64> = token_statistics.iter().map(|p| p.usd_volume_7d).collect();
        let usd_buy_volume_7ds: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_buy_volume_7d).collect();
        let usd_sell_volume_7ds: Vec<f64> =
            token_statistics.iter().map(|p| p.usd_sell_volume_7d).collect();
        let dev_hold_percentages: Vec<Option<f64>> =
            token_statistics.iter().map(|p| p.dev_hold_percentage).collect();
        let dev_sold_percentages: Vec<Option<f64>> =
            token_statistics.iter().map(|p| p.dev_sold_percentage).collect();
        let top10_hold_percentages: Vec<Option<f64>> =
            token_statistics.iter().map(|p| p.top10_hold_percentage).collect();
        let sniper_hold_percentages: Vec<Option<f64>> =
            token_statistics.iter().map(|p| p.sniper_hold_percentage).collect();
        let insider_hold_percentages: Vec<Option<f64>> =
            token_statistics.iter().map(|p| p.insider_hold_percentage).collect();
        let bot_hold_percentages: Vec<Option<f64>> =
            token_statistics.iter().map(|p| p.bot_hold_percentage).collect();
        let holder_counts: Vec<Option<i64>> =
            token_statistics.iter().map(|p| p.holder_count.map(|h| h as i64)).collect();
        let sniper_counts: Vec<Option<i64>> =
            token_statistics.iter().map(|p| p.sniper_count.map(|s| s as i64)).collect();
        let insider_counts: Vec<Option<i64>> =
            token_statistics.iter().map(|p| p.insider_count.map(|i| i as i64)).collect();
        let bot_counts: Vec<Option<i64>> =
            token_statistics.iter().map(|p| p.bot_count.map(|b| b as i64)).collect();

        let is_actives: Vec<bool> = token_statistics.iter().map(|p| p.is_active).collect();
        let best_pool_dexes: Vec<Dex> = token_statistics.iter().map(|p| p.best_pool_dex).collect();
        let pool_dexes: Vec<String> =
            token_statistics.iter().map(|p| p.pool_dexes.clone()).collect();

        let init_usd_prices: Vec<Option<f64>> =
            token_statistics.iter().map(|p| p.init_usd_price).collect();

        let image_paths: Vec<Option<String>> =
            token_statistics.iter().map(|p| p.image_path.clone()).collect();

        sqlx::query!(
            r#"
            INSERT INTO token_statistic (
                chain, token_address,
                name, symbol,
                decimals, supply,
                description, image, website, twitter, telegram, dex_paid,
                is_trench_token,
                create_dex, create_block_number, create_tx_hash, create_bonding_curve, create_dev, create_timestamp_millis,
                migration_pool_address, migration_timestamp_millis,
                update_timestamp_millis,
                is_mutable, update_authority, mint_authority, freeze_authority,
                best_pool_address, pool_addresses,
                usd_price, usd_market_cap, usd_liquidity,
                bonding_curve_progress,
                usd_total_volume, usd_total_buy_volume, usd_total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                total_trend, total_price_change,
                trend_5m, price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m,
                trend_1h, price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h,
                trend_6h, price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h,
                trend_24h, price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h,
                trend_3d, price_change_3d, txns_3d, buy_txns_3d, sell_txns_3d, usd_volume_3d, usd_buy_volume_3d, usd_sell_volume_3d,
                trend_7d, price_change_7d, txns_7d, buy_txns_7d, sell_txns_7d, usd_volume_7d, usd_buy_volume_7d, usd_sell_volume_7d,
                dev_hold_percentage, dev_sold_percentage, top10_hold_percentage, sniper_hold_percentage, insider_hold_percentage, bot_hold_percentage,
                holder_count, sniper_count, insider_count, bot_count,
                is_active, best_pool_dex, pool_dexes,
                init_usd_price, image_path
            )
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[],
                $3::varchar[], $4::varchar[],
                $5::smallint[], $6::varchar[],
                $7::varchar[], $8::varchar[], $9::varchar[], $10::varchar[], $11::varchar[], $12::dex_paid_enum[],
                $13::boolean[],
                $14::dex_enum[], $15::bigint[], $16::varchar[], $17::varchar[], $18::varchar[], $19::bigint[],
                $20::varchar[], $21::bigint[],
                $22::bigint[],
                $23::boolean[], $24::varchar[], $25::varchar[], $26::varchar[],
                $27::varchar[], $28::varchar[],
                $29::double precision[], $30::double precision[], $31::double precision[],
                $32::double precision[],
                $33::double precision[], $34::double precision[], $35::double precision[],
                $36::bigint[], $37::bigint[], $38::bigint[],
                $39::double precision[], $40::double precision[],
                $41::double precision[], $42::double precision[], $43::bigint[], $44::bigint[], $45::bigint[], $46::double precision[], $47::double precision[], $48::double precision[],
                $49::double precision[], $50::double precision[], $51::bigint[], $52::bigint[], $53::bigint[], $54::double precision[], $55::double precision[], $56::double precision[],
                $57::double precision[], $58::double precision[], $59::bigint[], $60::bigint[], $61::bigint[], $62::double precision[], $63::double precision[], $64::double precision[],
                $65::double precision[], $66::double precision[], $67::bigint[], $68::bigint[], $69::bigint[], $70::double precision[], $71::double precision[], $72::double precision[],
                $73::double precision[], $74::double precision[], $75::bigint[], $76::bigint[], $77::bigint[], $78::double precision[], $79::double precision[], $80::double precision[],
                $81::double precision[], $82::double precision[], $83::bigint[], $84::bigint[], $85::bigint[], $86::double precision[], $87::double precision[], $88::double precision[],
                $89::double precision[], $90::double precision[], $91::double precision[], $92::double precision[], $93::double precision[], $94::double precision[],
                $95::bigint[], $96::bigint[], $97::bigint[], $98::bigint[],
                $99::boolean[], $100::dex_enum[], $101::varchar[],
                $102::double precision[], $103::varchar[])
            ON CONFLICT (chain, token_address) DO UPDATE SET
                name = EXCLUDED.name,
                symbol = EXCLUDED.symbol,
                decimals = EXCLUDED.decimals,
                supply = EXCLUDED.supply,
                description = EXCLUDED.description,
                image = EXCLUDED.image,
                website = EXCLUDED.website,
                twitter = EXCLUDED.twitter,
                telegram = EXCLUDED.telegram,
                dex_paid = EXCLUDED.dex_paid,
                is_trench_token = EXCLUDED.is_trench_token,
                create_dex = EXCLUDED.create_dex,
                create_block_number = EXCLUDED.create_block_number,
                create_tx_hash = EXCLUDED.create_tx_hash,
                create_bonding_curve = EXCLUDED.create_bonding_curve,
                create_dev = EXCLUDED.create_dev,
                create_timestamp_millis = EXCLUDED.create_timestamp_millis,
                migration_pool_address = EXCLUDED.migration_pool_address,
                migration_timestamp_millis = EXCLUDED.migration_timestamp_millis,
                is_mutable = EXCLUDED.is_mutable,
                update_authority = EXCLUDED.update_authority,
                mint_authority = EXCLUDED.mint_authority,
                freeze_authority = EXCLUDED.freeze_authority,
                best_pool_address = EXCLUDED.best_pool_address,
                pool_addresses = EXCLUDED.pool_addresses,
                usd_price = EXCLUDED.usd_price,
                usd_market_cap = EXCLUDED.usd_market_cap,
                usd_liquidity = EXCLUDED.usd_liquidity,
                bonding_curve_progress = EXCLUDED.bonding_curve_progress,
                usd_total_volume = EXCLUDED.usd_total_volume,
                usd_total_buy_volume = EXCLUDED.usd_total_buy_volume,
                usd_total_sell_volume = EXCLUDED.usd_total_sell_volume,
                total_txns = EXCLUDED.total_txns,
                total_buy_txns = EXCLUDED.total_buy_txns,
                total_sell_txns = EXCLUDED.total_sell_txns,
                total_trend = EXCLUDED.total_trend,
                total_price_change = EXCLUDED.total_price_change,
                trend_5m = EXCLUDED.trend_5m,
                price_change_5m = EXCLUDED.price_change_5m,
                txns_5m = EXCLUDED.txns_5m,
                buy_txns_5m = EXCLUDED.buy_txns_5m,
                sell_txns_5m = EXCLUDED.sell_txns_5m,
                usd_volume_5m = EXCLUDED.usd_volume_5m,
                usd_buy_volume_5m = EXCLUDED.usd_buy_volume_5m,
                usd_sell_volume_5m = EXCLUDED.usd_sell_volume_5m,
                trend_1h = EXCLUDED.trend_1h,
                price_change_1h = EXCLUDED.price_change_1h,
                txns_1h = EXCLUDED.txns_1h,
                buy_txns_1h = EXCLUDED.buy_txns_1h,
                sell_txns_1h = EXCLUDED.sell_txns_1h,
                usd_volume_1h = EXCLUDED.usd_volume_1h,
                usd_buy_volume_1h = EXCLUDED.usd_buy_volume_1h,
                usd_sell_volume_1h = EXCLUDED.usd_sell_volume_1h,
                trend_6h = EXCLUDED.trend_6h,
                price_change_6h = EXCLUDED.price_change_6h,
                txns_6h = EXCLUDED.txns_6h,
                buy_txns_6h = EXCLUDED.buy_txns_6h,
                sell_txns_6h = EXCLUDED.sell_txns_6h,
                usd_volume_6h = EXCLUDED.usd_volume_6h,
                usd_buy_volume_6h = EXCLUDED.usd_buy_volume_6h,
                usd_sell_volume_6h = EXCLUDED.usd_sell_volume_6h,
                trend_24h = EXCLUDED.trend_24h,
                price_change_24h = EXCLUDED.price_change_24h,
                txns_24h = EXCLUDED.txns_24h,
                buy_txns_24h = EXCLUDED.buy_txns_24h,
                sell_txns_24h = EXCLUDED.sell_txns_24h,
                usd_volume_24h = EXCLUDED.usd_volume_24h,
                usd_buy_volume_24h = EXCLUDED.usd_buy_volume_24h,
                usd_sell_volume_24h = EXCLUDED.usd_sell_volume_24h,
                trend_3d = EXCLUDED.trend_3d,
                price_change_3d = EXCLUDED.price_change_3d,
                txns_3d = EXCLUDED.txns_3d,
                buy_txns_3d = EXCLUDED.buy_txns_3d,
                sell_txns_3d = EXCLUDED.sell_txns_3d,
                usd_volume_3d = EXCLUDED.usd_volume_3d,
                usd_buy_volume_3d = EXCLUDED.usd_buy_volume_3d,
                usd_sell_volume_3d = EXCLUDED.usd_sell_volume_3d,
                trend_7d = EXCLUDED.trend_7d,
                price_change_7d = EXCLUDED.price_change_7d,
                txns_7d = EXCLUDED.txns_7d,
                buy_txns_7d = EXCLUDED.buy_txns_7d,
                sell_txns_7d = EXCLUDED.sell_txns_7d,
                usd_volume_7d = EXCLUDED.usd_volume_7d,
                usd_buy_volume_7d = EXCLUDED.usd_buy_volume_7d,
                usd_sell_volume_7d = EXCLUDED.usd_sell_volume_7d,
                dev_hold_percentage = EXCLUDED.dev_hold_percentage,
                dev_sold_percentage = EXCLUDED.dev_sold_percentage,
                top10_hold_percentage = EXCLUDED.top10_hold_percentage,
                sniper_hold_percentage = EXCLUDED.sniper_hold_percentage,
                insider_hold_percentage = EXCLUDED.insider_hold_percentage,
                bot_hold_percentage = EXCLUDED.bot_hold_percentage,
                holder_count = EXCLUDED.holder_count,
                sniper_count = EXCLUDED.sniper_count,
                insider_count = EXCLUDED.insider_count,
                bot_count = EXCLUDED.bot_count,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis,
                is_active = EXCLUDED.is_active,
                best_pool_dex = EXCLUDED.best_pool_dex,
                pool_dexes = EXCLUDED.pool_dexes,
                init_usd_price = EXCLUDED.init_usd_price,
                image_path = EXCLUDED.image_path
            WHERE token_statistic.update_timestamp_millis <= EXCLUDED.update_timestamp_millis
            "#,
            &chains as &[Chain],
            &token_addresses as &[String],
            &names as &[String],
            &symbols as &[String],
            &decimals as &[i16],
            &supplies as &[String],
            &descriptions as &[Option<String>],
            &images as &[Option<String>],
            &websites as &[Option<String>],
            &twitters as &[Option<String>],
            &telegrams as &[Option<String>],
            &dex_paids as &[DexPaid],
            &is_trench_tokens as &[bool],
            &create_dexes as &[Dex],
            &create_block_numbers as &[Option<i64>],
            &create_tx_hashes as &[Option<String>],
            &create_bonding_curves as &[Option<String>],
            &create_devs as &[Option<String>],
            &create_timestamp_millis as &[i64],
            &migration_pool_addresses as &[Option<String>],
            &migration_timestamp_millis as &[i64],
            &update_timestamp_millis as &[i64],
            &is_mutables as &[Option<bool>],
            &update_authorities as &[Option<String>],
            &mint_authorities as &[Option<String>],
            &freeze_authorities as &[Option<String>],
            &best_pool_addresses as &[String],
            &pool_addresses as &[String],
            &usd_prices as &[f64],
            &usd_market_caps as &[f64],
            &usd_liquidity as &[f64],
            &bonding_curve_progresses as &[Option<f64>],
            &usd_total_volumes as &[f64],
            &usd_total_buy_volumes as &[f64],
            &usd_total_sell_volumes as &[f64],
            &total_txns as &[i64],
            &total_buy_txns as &[i64],
            &total_sell_txns as &[i64],
            &total_trends as &[f64],
            &total_price_changes as &[f64],
            &trend_5ms as &[f64],
            &price_change_5ms as &[f64],
            &txns_5ms as &[i64],
            &buy_txns_5ms as &[i64],
            &sell_txns_5ms as &[i64],
            &usd_volume_5ms as &[f64],
            &usd_buy_volume_5ms as &[f64],
            &usd_sell_volume_5ms as &[f64],
            &trend_1hs as &[f64],
            &price_change_1hs as &[f64],
            &txns_1hs as &[i64],
            &buy_txns_1hs as &[i64],
            &sell_txns_1hs as &[i64],
            &usd_volume_1hs as &[f64],
            &usd_buy_volume_1hs as &[f64],
            &usd_sell_volume_1hs as &[f64],
            &trend_6hs as &[f64],
            &price_change_6hs as &[f64],
            &txns_6hs as &[i64],
            &buy_txns_6hs as &[i64],
            &sell_txns_6hs as &[i64],
            &usd_volume_6hs as &[f64],
            &usd_buy_volume_6hs as &[f64],
            &usd_sell_volume_6hs as &[f64],
            &trend_24hs as &[f64],
            &price_change_24hs as &[f64],
            &txns_24hs as &[i64],
            &buy_txns_24hs as &[i64],
            &sell_txns_24hs as &[i64],
            &usd_volume_24hs as &[f64],
            &usd_buy_volume_24hs as &[f64],
            &usd_sell_volume_24hs as &[f64],
            &trend_3ds as &[f64],
            &price_change_3ds as &[f64],
            &txns_3ds as &[i64],
            &buy_txns_3ds as &[i64],
            &sell_txns_3ds as &[i64],
            &usd_volume_3ds as &[f64],
            &usd_buy_volume_3ds as &[f64],
            &usd_sell_volume_3ds as &[f64],
            &trend_7ds as &[f64],
            &price_change_7ds as &[f64],
            &txns_7ds as &[i64],
            &buy_txns_7ds as &[i64],
            &sell_txns_7ds as &[i64],
            &usd_volume_7ds as &[f64],
            &usd_buy_volume_7ds as &[f64],
            &usd_sell_volume_7ds as &[f64],
            &dev_hold_percentages as &[Option<f64>],
            &dev_sold_percentages as &[Option<f64>],
            &top10_hold_percentages as &[Option<f64>],
            &sniper_hold_percentages as &[Option<f64>],
            &insider_hold_percentages as &[Option<f64>],
            &bot_hold_percentages as &[Option<f64>],
            &holder_counts as &[Option<i64>],
            &sniper_counts as &[Option<i64>],
            &insider_counts as &[Option<i64>],
            &bot_counts as &[Option<i64>],
            &is_actives as &[bool],
            &best_pool_dexes as &[Dex],
            &pool_dexes as &[String],
            &init_usd_prices as &[Option<f64>],
            &image_paths as &[Option<String>],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_token_statistics(
        &self,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<TokenStatistic>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain", token_address,
                name, symbol,
                decimals, supply,
                description, image, website, twitter, telegram, dex_paid AS "dex_paid: DexPaid",
                is_trench_token,
                create_dex AS "create_dex: Dex", create_block_number, create_tx_hash, create_bonding_curve, create_dev, create_timestamp_millis,
                migration_pool_address, migration_timestamp_millis,
                update_timestamp_millis,
                is_mutable, update_authority, mint_authority, freeze_authority,
                best_pool_address, pool_addresses,
                usd_price, usd_market_cap, usd_liquidity,
                bonding_curve_progress,
                usd_total_volume, usd_total_buy_volume, usd_total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                total_trend, total_price_change,
                trend_5m, price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m,
                trend_1h, price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h,
                trend_6h, price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h,
                trend_24h, price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h,
                trend_3d, price_change_3d, txns_3d, buy_txns_3d, sell_txns_3d, usd_volume_3d, usd_buy_volume_3d, usd_sell_volume_3d,
                trend_7d, price_change_7d, txns_7d, buy_txns_7d, sell_txns_7d, usd_volume_7d, usd_buy_volume_7d, usd_sell_volume_7d,
                dev_hold_percentage, dev_sold_percentage, top10_hold_percentage, sniper_hold_percentage, insider_hold_percentage, bot_hold_percentage,
                holder_count, sniper_count, insider_count, bot_count,
                is_active, best_pool_dex AS "best_pool_dex: Dex", pool_dexes,
                init_usd_price, image_path
            FROM token_statistic
            ORDER BY chain, token_address
            LIMIT $1 OFFSET $2"#,
            limit,
            offset,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut token_statistics = Vec::with_capacity(rows.len());
        for row in rows {
            token_statistics.push(TokenStatistic {
                chain: row.chain,
                token_address: row.token_address,
                name: row.name,
                symbol: row.symbol,
                decimals: row.decimals as u8,
                supply: row.supply,
                description: row.description,
                image: row.image,
                website: row.website,
                twitter: row.twitter,
                telegram: row.telegram,
                dex_paid: row.dex_paid,
                is_trench_token: row.is_trench_token,
                create_dex: row.create_dex,
                create_block_number: row.create_block_number.map(|x| x as u64),
                create_tx_hash: row.create_tx_hash,
                create_bonding_curve: row.create_bonding_curve,
                create_dev: row.create_dev,
                create_timestamp_millis: row.create_timestamp_millis,
                migration_pool_address: row.migration_pool_address,
                migration_timestamp_millis: row.migration_timestamp_millis,
                update_timestamp_millis: row.update_timestamp_millis,
                is_mutable: row.is_mutable,
                update_authority: row.update_authority,
                mint_authority: row.mint_authority,
                freeze_authority: row.freeze_authority,
                best_pool_address: row.best_pool_address,
                pool_addresses: row.pool_addresses,
                usd_price: row.usd_price,
                usd_market_cap: row.usd_market_cap,
                usd_liquidity: row.usd_liquidity,
                bonding_curve_progress: row.bonding_curve_progress,
                usd_total_volume: row.usd_total_volume,
                usd_total_buy_volume: row.usd_total_buy_volume,
                usd_total_sell_volume: row.usd_total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                total_trend: row.total_trend,
                total_price_change: row.total_price_change,
                trend_5m: row.trend_5m,
                price_change_5m: row.price_change_5m,
                txns_5m: row.txns_5m as u64,
                buy_txns_5m: row.buy_txns_5m as u64,
                sell_txns_5m: row.sell_txns_5m as u64,
                usd_volume_5m: row.usd_volume_5m,
                usd_buy_volume_5m: row.usd_buy_volume_5m,
                usd_sell_volume_5m: row.usd_sell_volume_5m,
                trend_1h: row.trend_1h,
                price_change_1h: row.price_change_1h,
                txns_1h: row.txns_1h as u64,
                buy_txns_1h: row.buy_txns_1h as u64,
                sell_txns_1h: row.sell_txns_1h as u64,
                usd_volume_1h: row.usd_volume_1h,
                usd_buy_volume_1h: row.usd_buy_volume_1h,
                usd_sell_volume_1h: row.usd_sell_volume_1h,
                trend_6h: row.trend_6h,
                price_change_6h: row.price_change_6h,
                txns_6h: row.txns_6h as u64,
                buy_txns_6h: row.buy_txns_6h as u64,
                sell_txns_6h: row.sell_txns_6h as u64,
                usd_volume_6h: row.usd_volume_6h,
                usd_buy_volume_6h: row.usd_buy_volume_6h,
                usd_sell_volume_6h: row.usd_sell_volume_6h,
                trend_24h: row.trend_24h,
                price_change_24h: row.price_change_24h,
                txns_24h: row.txns_24h as u64,
                buy_txns_24h: row.buy_txns_24h as u64,
                sell_txns_24h: row.sell_txns_24h as u64,
                usd_volume_24h: row.usd_volume_24h,
                usd_buy_volume_24h: row.usd_buy_volume_24h,
                usd_sell_volume_24h: row.usd_sell_volume_24h,
                trend_3d: row.trend_3d,
                price_change_3d: row.price_change_3d,
                txns_3d: row.txns_3d as u64,
                buy_txns_3d: row.buy_txns_3d as u64,
                sell_txns_3d: row.sell_txns_3d as u64,
                usd_volume_3d: row.usd_volume_3d,
                usd_buy_volume_3d: row.usd_buy_volume_3d,
                usd_sell_volume_3d: row.usd_sell_volume_3d,
                trend_7d: row.trend_7d,
                price_change_7d: row.price_change_7d,
                txns_7d: row.txns_7d as u64,
                buy_txns_7d: row.buy_txns_7d as u64,
                sell_txns_7d: row.sell_txns_7d as u64,
                usd_volume_7d: row.usd_volume_7d,
                usd_buy_volume_7d: row.usd_buy_volume_7d,
                usd_sell_volume_7d: row.usd_sell_volume_7d,
                dev_hold_percentage: row.dev_hold_percentage,
                dev_sold_percentage: row.dev_sold_percentage,
                top10_hold_percentage: row.top10_hold_percentage,
                sniper_hold_percentage: row.sniper_hold_percentage,
                insider_hold_percentage: row.insider_hold_percentage,
                bot_hold_percentage: row.bot_hold_percentage,
                holder_count: row.holder_count.map(|x| x as u64),
                sniper_count: row.sniper_count.map(|x| x as u64),
                insider_count: row.insider_count.map(|x| x as u64),
                bot_count: row.bot_count.map(|x| x as u64),
                is_active: row.is_active,
                best_pool_dex: row.best_pool_dex,
                pool_dexes: row.pool_dexes,
                init_usd_price: row.init_usd_price,
                image_path: row.image_path,
            });
        }

        Ok(token_statistics)
    }
}
