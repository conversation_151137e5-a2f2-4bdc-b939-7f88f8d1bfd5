use std::collections::BTreeMap;

use serde::{Deserialize, Serialize};

use crate::postgres::enums::{CandleInterval, Chain};

/// Enhanced Candle structure with intelligent caching and aggregation capabilities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedCandle {
    pub chain: Chain,
    pub pool_address: String,

    pub open_timestamp_seconds: i64,
    pub close_timestamp_seconds: i64,

    pub interval: CandleInterval,

    pub native_token_usd_price: f64,

    pub usd_open_price: f64,
    pub usd_close_price: f64,
    pub usd_high_price: f64,
    pub usd_low_price: f64,

    pub usd_open_market_cap: f64,
    pub usd_close_market_cap: f64,
    pub usd_high_market_cap: f64,
    pub usd_low_market_cap: f64,

    pub usd_volume: f64,
    pub txns: u64,

    // Metadata for caching and monitoring
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cached_at: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub access_count: Option<u64>,
}

impl EnhancedCandle {
    /// Convert from existing Candle structure with caching metadata
    pub fn from_candle(candle: crate::postgres::aggregator::Candle) -> Self {
        let now = chrono::Utc::now().timestamp();
        Self {
            chain: candle.chain,
            pool_address: candle.pool_address,
            open_timestamp_seconds: candle.open_timestamp_seconds,
            close_timestamp_seconds: candle.close_timestamp_seconds,
            interval: candle.interval,
            native_token_usd_price: candle.native_token_usd_price,
            usd_open_price: candle.usd_open_price,
            usd_close_price: candle.usd_close_price,
            usd_high_price: candle.usd_high_price,
            usd_low_price: candle.usd_low_price,
            usd_open_market_cap: candle.usd_open_market_cap,
            usd_close_market_cap: candle.usd_close_market_cap,
            usd_high_market_cap: candle.usd_high_market_cap,
            usd_low_market_cap: candle.usd_low_market_cap,
            usd_volume: candle.usd_volume,
            txns: candle.txns,
            cached_at: Some(now),
            access_count: Some(1),
        }
    }

    /// Convert to existing Candle structure for database operations
    pub fn to_candle(&self) -> crate::postgres::aggregator::Candle {
        crate::postgres::aggregator::Candle {
            chain: self.chain,
            pool_address: self.pool_address.clone(),
            open_timestamp_seconds: self.open_timestamp_seconds,
            close_timestamp_seconds: self.close_timestamp_seconds,
            interval: self.interval,
            native_token_usd_price: self.native_token_usd_price,
            usd_open_price: self.usd_open_price,
            usd_close_price: self.usd_close_price,
            usd_high_price: self.usd_high_price,
            usd_low_price: self.usd_low_price,
            usd_open_market_cap: self.usd_open_market_cap,
            usd_close_market_cap: self.usd_close_market_cap,
            usd_high_market_cap: self.usd_high_market_cap,
            usd_low_market_cap: self.usd_low_market_cap,
            usd_volume: self.usd_volume,
            txns: self.txns,
        }
    }

    /// Fill missing candles between actual candles with appropriate values
    /// Ensures prev.close === next.open continuity
    pub fn fill_gaps(mut actual_candles: Vec<Self>, interval: CandleInterval) -> Vec<Self> {
        if actual_candles.len() <= 1 {
            return actual_candles;
        }

        // Sort actual candles by timestamp
        actual_candles.sort_by_key(|c| c.open_timestamp_seconds);

        let mut result: Vec<Self> = Vec::new();
        let interval_seconds = interval.as_seconds();

        for i in 0..actual_candles.len() {
            let mut current_candle = actual_candles[i].clone();

            // Adjust the open price of current candle to ensure continuity
            if i > 0 && !result.is_empty() {
                let prev_close = result.last().unwrap().usd_close_price;
                current_candle.usd_open_price = prev_close;
            }

            // Add current actual candle (possibly adjusted)
            result.push(current_candle.clone());

            // Check if we need to fill gap to next candle
            if i < actual_candles.len() - 1 {
                let current_close = current_candle.close_timestamp_seconds;
                let next_open = actual_candles[i + 1].open_timestamp_seconds;

                // Fill gaps between current and next candle
                let mut fill_timestamp = current_close;
                while fill_timestamp < next_open {
                    let missing_candle = Self {
                        chain: current_candle.chain,
                        pool_address: current_candle.pool_address.clone(),
                        open_timestamp_seconds: fill_timestamp,
                        close_timestamp_seconds: fill_timestamp + interval_seconds,
                        interval,
                        native_token_usd_price: current_candle.native_token_usd_price,
                        usd_open_price: current_candle.usd_close_price,
                        usd_close_price: current_candle.usd_close_price,
                        usd_high_price: current_candle.usd_close_price,
                        usd_low_price: current_candle.usd_close_price,
                        usd_open_market_cap: current_candle.usd_close_market_cap,
                        usd_close_market_cap: current_candle.usd_close_market_cap,
                        usd_high_market_cap: current_candle.usd_close_market_cap,
                        usd_low_market_cap: current_candle.usd_close_market_cap,
                        usd_volume: 0.0,
                        txns: 0,
                        cached_at: Some(chrono::Utc::now().timestamp()),
                        access_count: Some(1),
                    };

                    result.push(missing_candle);
                    fill_timestamp += interval_seconds;
                }
            }
        }

        result
    }

    /// Aggregate candles to larger intervals with optimized performance
    pub fn aggregate(candles: &[Self], interval: CandleInterval) -> Vec<Self> {
        if candles.is_empty() {
            return Vec::new();
        }

        let interval_seconds = interval.as_seconds();

        // If already at the requested interval, return as is
        if interval == CandleInterval::S1 {
            return candles.to_vec();
        }

        // Group candles by interval bucket
        let mut grouped_candles: BTreeMap<i64, Vec<&Self>> = BTreeMap::new();

        for candle in candles {
            let bucket_timestamp =
                (candle.open_timestamp_seconds / interval_seconds) * interval_seconds;
            grouped_candles.entry(bucket_timestamp).or_default().push(candle);
        }

        // Aggregate candles within each interval
        let mut result = Vec::with_capacity(grouped_candles.len());

        for (bucket_timestamp, interval_candles) in grouped_candles {
            if interval_candles.is_empty() {
                continue;
            }

            // Sort candles by timestamp to ensure correct order
            let mut sorted_candles = interval_candles;
            sorted_candles.sort_by_key(|c| c.open_timestamp_seconds);

            let first = sorted_candles[0];
            let last = sorted_candles[sorted_candles.len() - 1];

            // Calculate aggregated values efficiently
            let (
                avg_native_price,
                high_price,
                low_price,
                high_market_cap,
                low_market_cap,
                total_volume,
                total_trades,
            ) = sorted_candles.iter().fold(
                (
                    0.0,
                    f64::NEG_INFINITY,
                    f64::INFINITY,
                    f64::NEG_INFINITY,
                    f64::INFINITY,
                    0.0,
                    0u64,
                ),
                |(avg_native, high_p, low_p, high_mc, low_mc, vol, trades), candle| {
                    (
                        avg_native + candle.native_token_usd_price,
                        high_p.max(candle.usd_high_price),
                        low_p.min(candle.usd_low_price),
                        high_mc.max(candle.usd_high_market_cap),
                        low_mc.min(candle.usd_low_market_cap),
                        vol + candle.usd_volume,
                        trades + candle.txns,
                    )
                },
            );

            let avg_native_price = avg_native_price / sorted_candles.len() as f64;

            result.push(Self {
                chain: first.chain,
                pool_address: first.pool_address.clone(),
                open_timestamp_seconds: bucket_timestamp,
                close_timestamp_seconds: bucket_timestamp + interval_seconds,
                native_token_usd_price: avg_native_price,
                interval,
                usd_open_price: first.usd_open_price,
                usd_close_price: last.usd_close_price,
                usd_high_price: high_price,
                usd_low_price: low_price,
                usd_volume: total_volume,
                txns: total_trades,
                usd_open_market_cap: first.usd_open_market_cap,
                usd_close_market_cap: last.usd_close_market_cap,
                usd_high_market_cap: high_market_cap,
                usd_low_market_cap: low_market_cap,
                cached_at: Some(chrono::Utc::now().timestamp()),
                access_count: Some(1),
            });
        }

        // Sort result by open timestamp
        result.sort_by_key(|c| c.open_timestamp_seconds);

        result
    }

    /// Increment access count for cache analytics
    pub fn increment_access(&mut self) {
        self.access_count = Some(self.access_count.unwrap_or(0) + 1);
    }

    /// Check if candle data is stale and needs refresh
    pub fn is_stale(&self, max_age_seconds: i64) -> bool {
        if let Some(cached_at) = self.cached_at {
            let now = chrono::Utc::now().timestamp();
            now - cached_at > max_age_seconds
        } else {
            true // No cache timestamp means it's stale
        }
    }
}

/// Cache key generation for Redis
pub fn cache_key(
    chain: Chain,
    pool_address: &str,
    interval: CandleInterval,
    start_ts: i64,
    end_ts: i64,
) -> String {
    format!(
        "candle_cache:{}:{}:{}:{}:{}",
        chain.to_string(),
        pool_address,
        interval.as_str(),
        start_ts,
        end_ts
    )
}

/// Distributed lock key for candle aggregation
pub fn candle_aggregation_lock_key(chain: &str, pool_address: &str, interval: &str) -> String {
    format!("candle_lock:{}:{}:{}", chain, pool_address, interval)
}
