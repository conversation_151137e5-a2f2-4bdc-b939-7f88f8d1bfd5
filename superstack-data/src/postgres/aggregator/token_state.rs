use serde::{Deserialize, Serialize};

use crate::{postgres::*, Erro<PERSON>};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct TokenState {
    pub chain: Chain,
    pub token_address: String,
    pub timestamp_millis: i64,

    pub best_pool_address: String,

    pub usd_price: f64,
    pub usd_market_cap: f64,

    pub native_price: Option<f64>,

    pub native_token_liquidity: f64,
    pub usd_token_liquidity: f64,

    pub total_native_token_volume: f64,
    pub total_usd_token_volume: f64,
    pub total_native_token_buy_volume: f64,
    pub total_usd_token_buy_volume: f64,
    pub total_native_token_sell_volume: f64,
    pub total_usd_token_sell_volume: f64,

    pub total_txns: u64,
    pub total_buy_txns: u64,
    pub total_sell_txns: u64,

    pub bonding_curve_progress: Option<f64>,

    // New fields
    pub best_pool_dex: Dex,
}

impl TokenState {
    pub fn new(chain: Chain, token_address: &str) -> Self {
        Self {
            chain,
            token_address: token_address.to_string(),
            timestamp_millis: 0,
            best_pool_address: "".to_string(),
            usd_price: 0.0,
            usd_market_cap: 0.0,
            native_price: None,
            native_token_liquidity: 0.0,
            usd_token_liquidity: 0.0,
            total_native_token_volume: 0.0,
            total_usd_token_volume: 0.0,
            total_native_token_buy_volume: 0.0,
            total_usd_token_buy_volume: 0.0,
            total_native_token_sell_volume: 0.0,
            total_usd_token_sell_volume: 0.0,
            total_txns: 0,
            total_buy_txns: 0,
            total_sell_txns: 0,
            bonding_curve_progress: None,
            best_pool_dex: Dex::Unknown,
        }
    }

    pub fn empty_total_state(&self) -> Self {
        Self {
            chain: self.chain,
            token_address: self.token_address.clone(),
            timestamp_millis: self.timestamp_millis,
            best_pool_address: self.best_pool_address.clone(),
            usd_price: self.usd_price,
            usd_market_cap: self.usd_market_cap,
            native_price: self.native_price,
            native_token_liquidity: self.native_token_liquidity,
            usd_token_liquidity: self.usd_token_liquidity,
            total_native_token_volume: 0.0,
            total_usd_token_volume: 0.0,
            total_native_token_buy_volume: 0.0,
            total_usd_token_buy_volume: 0.0,
            total_native_token_sell_volume: 0.0,
            total_usd_token_sell_volume: 0.0,
            total_txns: 0,
            total_buy_txns: 0,
            total_sell_txns: 0,
            bonding_curve_progress: self.bonding_curve_progress,
            best_pool_dex: self.best_pool_dex,
        }
    }

    pub fn update_usd_price_and_market_cap(
        &mut self,
        native_token_usd_price: f64,
        supply_ui_amount: f64,
    ) {
        if let Some(native_price) = self.native_price {
            self.usd_price = native_price * native_token_usd_price;
            self.usd_market_cap = supply_ui_amount * self.usd_price;
        }
    }

    pub fn price_change(
        &self,
        old_token_state: &TokenState,
        native_token_usd_price: Option<f64>,
    ) -> f64 {
        if old_token_state.usd_price == 0.0 {
            return 0.0;
        }

        if let Some(native_token_usd_price) = native_token_usd_price {
            let cur_usd_price = if let Some(native_price) = self.native_price {
                native_price * native_token_usd_price
            } else {
                self.usd_price
            };

            (cur_usd_price - old_token_state.usd_price) / old_token_state.usd_price
        } else {
            (self.usd_price - old_token_state.usd_price) / old_token_state.usd_price
        }
    }

    pub fn total_price_change(
        &self,
        init_usd_price: f64,
        native_token_usd_price: Option<f64>,
    ) -> f64 {
        if init_usd_price == 0.0 {
            return 0.0;
        }

        if let Some(native_token_usd_price) = native_token_usd_price {
            let cur_usd_price = if let Some(native_price) = self.native_price {
                native_price * native_token_usd_price
            } else {
                self.usd_price
            };

            (cur_usd_price - init_usd_price) / init_usd_price
        } else {
            (self.usd_price - init_usd_price) / init_usd_price
        }
    }

    pub fn txns(&self, old_token_state: &TokenState) -> u64 {
        self.total_txns.saturating_sub(old_token_state.total_txns)
    }

    pub fn total_txns(&self) -> u64 {
        self.total_txns
    }

    pub fn buy_txns(&self, old_token_state: &TokenState) -> u64 {
        self.total_buy_txns.saturating_sub(old_token_state.total_buy_txns)
    }

    pub fn total_buy_txns(&self) -> u64 {
        self.total_buy_txns
    }

    pub fn sell_txns(&self, old_token_state: &TokenState) -> u64 {
        self.total_sell_txns.saturating_sub(old_token_state.total_sell_txns)
    }

    pub fn total_sell_txns(&self) -> u64 {
        self.total_sell_txns
    }

    pub fn volume(&self, old_token_state: &TokenState, native_token_usd_price: f64) -> f64 {
        (self.total_usd_token_volume - old_token_state.total_usd_token_volume).max(0.0) +
            (self.total_native_token_volume - old_token_state.total_native_token_volume).max(0.0) *
                native_token_usd_price
    }

    pub fn total_volume(&self, native_token_usd_price: f64) -> f64 {
        self.total_usd_token_volume + self.total_native_token_volume * native_token_usd_price
    }

    pub fn total_buy_volume(&self, native_token_usd_price: f64) -> f64 {
        self.total_usd_token_buy_volume +
            self.total_native_token_buy_volume * native_token_usd_price
    }

    pub fn total_sell_volume(&self, native_token_usd_price: f64) -> f64 {
        self.total_usd_token_sell_volume +
            self.total_native_token_sell_volume * native_token_usd_price
    }

    pub fn buy_volume(&self, old_token_state: &TokenState, native_token_usd_price: f64) -> f64 {
        (self.total_usd_token_buy_volume - old_token_state.total_usd_token_buy_volume).max(0.0) +
            (self.total_native_token_buy_volume - old_token_state.total_native_token_buy_volume)
                .max(0.0) *
                native_token_usd_price
    }

    pub fn sell_volume(&self, old_token_state: &TokenState, native_token_usd_price: f64) -> f64 {
        (self.total_usd_token_sell_volume - old_token_state.total_usd_token_sell_volume).max(0.0) +
            (self.total_native_token_sell_volume - old_token_state.total_native_token_sell_volume)
                .max(0.0) *
                native_token_usd_price
    }

    pub fn liquidity(&self, native_token_usd_price: f64) -> f64 {
        self.usd_token_liquidity + self.native_token_liquidity * native_token_usd_price
    }
}

impl PostgresDatabase {
    pub async fn insert_token_state(&self, token_state: &TokenState) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO token_state (
                chain, token_address, timestamp_millis,
                best_pool_address,
                usd_price, usd_market_cap,
                native_price,
                native_token_liquidity, usd_token_liquidity,
                total_native_token_volume, total_usd_token_volume,
                total_native_token_buy_volume, total_usd_token_buy_volume,
                total_native_token_sell_volume, total_usd_token_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress,
                best_pool_dex
            )
            VALUES (
                $1, $2, $3,
                $4,
                $5, $6,
                $7,
                $8, $9,
                $10, $11,
                $12, $13,
                $14, $15,
                $16, $17, $18,
                $19,
                $20
            )
            ON CONFLICT (chain, token_address, timestamp_millis) DO NOTHING
            "#,
            token_state.chain as Chain,
            token_state.token_address,
            token_state.timestamp_millis,
            token_state.best_pool_address,
            token_state.usd_price,
            token_state.usd_market_cap,
            token_state.native_price,
            token_state.native_token_liquidity,
            token_state.usd_token_liquidity,
            token_state.total_native_token_volume,
            token_state.total_usd_token_volume,
            token_state.total_native_token_buy_volume,
            token_state.total_usd_token_buy_volume,
            token_state.total_native_token_sell_volume,
            token_state.total_usd_token_sell_volume,
            token_state.total_txns as i64,
            token_state.total_buy_txns as i64,
            token_state.total_sell_txns as i64,
            token_state.bonding_curve_progress,
            token_state.best_pool_dex as Dex,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_latest_token_state_before_timestamp_millis(
        &self,
        chain: Chain,
        token_address: &str,
        timestamp_millis: i64,
    ) -> Result<Option<TokenState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT 
                chain AS "chain: Chain",
                token_address,
                timestamp_millis,
                best_pool_address,
                usd_price, usd_market_cap,
                native_price,
                native_token_liquidity, usd_token_liquidity,
                total_native_token_volume, total_usd_token_volume,
                total_native_token_buy_volume, total_usd_token_buy_volume,
                total_native_token_sell_volume, total_usd_token_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress,
                best_pool_dex AS "best_pool_dex: Dex"
            FROM token_state
            WHERE chain = $1 AND token_address = $2 AND timestamp_millis <= $3
            ORDER BY timestamp_millis DESC
            LIMIT 1
            "#,
            chain as Chain,
            token_address,
            timestamp_millis as i64,
        )
        .fetch_optional(&self.pool)
        .await?;

        let token_state = match row {
            Some(row) => Some(TokenState {
                chain: row.chain,
                token_address: row.token_address,
                timestamp_millis: row.timestamp_millis,
                best_pool_address: row.best_pool_address,
                usd_price: row.usd_price,
                usd_market_cap: row.usd_market_cap,
                native_price: row.native_price,
                native_token_liquidity: row.native_token_liquidity,
                usd_token_liquidity: row.usd_token_liquidity,
                total_native_token_volume: row.total_native_token_volume,
                total_usd_token_volume: row.total_usd_token_volume,
                total_native_token_buy_volume: row.total_native_token_buy_volume,
                total_usd_token_buy_volume: row.total_usd_token_buy_volume,
                total_native_token_sell_volume: row.total_native_token_sell_volume,
                total_usd_token_sell_volume: row.total_usd_token_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
                best_pool_dex: row.best_pool_dex,
            }),
            None => None,
        };

        Ok(token_state)
    }

    pub async fn get_latest_token_state(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<Option<TokenState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT 
                chain AS "chain: Chain",
                token_address,
                timestamp_millis,
                best_pool_address,
                usd_price, usd_market_cap,
                native_price,
                native_token_liquidity, usd_token_liquidity,
                total_native_token_volume, total_usd_token_volume,
                total_native_token_buy_volume, total_usd_token_buy_volume,
                total_native_token_sell_volume, total_usd_token_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress,
                best_pool_dex AS "best_pool_dex: Dex"
            FROM token_state
            WHERE chain = $1 AND token_address = $2
            ORDER BY timestamp_millis DESC
            LIMIT 1
            "#,
            chain as Chain,
            token_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let token_state = match row {
            Some(row) => Some(TokenState {
                chain: row.chain,
                token_address: row.token_address,
                timestamp_millis: row.timestamp_millis,
                best_pool_address: row.best_pool_address,
                usd_price: row.usd_price,
                usd_market_cap: row.usd_market_cap,
                native_price: row.native_price,
                native_token_liquidity: row.native_token_liquidity,
                usd_token_liquidity: row.usd_token_liquidity,
                total_native_token_volume: row.total_native_token_volume,
                total_usd_token_volume: row.total_usd_token_volume,
                total_native_token_buy_volume: row.total_native_token_buy_volume,
                total_usd_token_buy_volume: row.total_usd_token_buy_volume,
                total_native_token_sell_volume: row.total_native_token_sell_volume,
                total_usd_token_sell_volume: row.total_usd_token_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
                best_pool_dex: row.best_pool_dex,
            }),
            None => None,
        };

        Ok(token_state)
    }

    pub async fn get_first_token_state(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<Option<TokenState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT 
                chain AS "chain: Chain",
                token_address,
                timestamp_millis,
                best_pool_address,
                usd_price, usd_market_cap,
                native_price,
                native_token_liquidity, usd_token_liquidity,
                total_native_token_volume, total_usd_token_volume,
                total_native_token_buy_volume, total_usd_token_buy_volume,
                total_native_token_sell_volume, total_usd_token_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress,
                best_pool_dex AS "best_pool_dex: Dex"
            FROM token_state
            WHERE chain = $1 AND token_address = $2
            ORDER BY timestamp_millis ASC
            LIMIT 1
            "#,
            chain as Chain,
            token_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let token_state = match row {
            Some(row) => Some(TokenState {
                chain: row.chain,
                token_address: row.token_address,
                timestamp_millis: row.timestamp_millis,
                best_pool_address: row.best_pool_address,
                usd_price: row.usd_price,
                usd_market_cap: row.usd_market_cap,
                native_price: row.native_price,
                native_token_liquidity: row.native_token_liquidity,
                usd_token_liquidity: row.usd_token_liquidity,
                total_native_token_volume: row.total_native_token_volume,
                total_usd_token_volume: row.total_usd_token_volume,
                total_native_token_buy_volume: row.total_native_token_buy_volume,
                total_usd_token_buy_volume: row.total_usd_token_buy_volume,
                total_native_token_sell_volume: row.total_native_token_sell_volume,
                total_usd_token_sell_volume: row.total_usd_token_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
                best_pool_dex: row.best_pool_dex,
            }),
            None => None,
        };

        Ok(token_state)
    }

    pub async fn get_first_non_zero_token_state(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<Option<TokenState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                token_address,
                timestamp_millis,
                best_pool_address,
                usd_price, usd_market_cap,
                native_price,
                native_token_liquidity, usd_token_liquidity,
                total_native_token_volume, total_usd_token_volume,
                total_native_token_buy_volume, total_usd_token_buy_volume,
                total_native_token_sell_volume, total_usd_token_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress,
                best_pool_dex AS "best_pool_dex: Dex"
            FROM token_state
            WHERE chain = $1 AND token_address = $2 AND usd_price > 0.0
            ORDER BY timestamp_millis ASC
            LIMIT 1
            "#,
            chain as Chain,
            token_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let token_state = match row {
            Some(row) => Some(TokenState {
                chain: row.chain,
                token_address: row.token_address,
                timestamp_millis: row.timestamp_millis,
                best_pool_address: row.best_pool_address,
                usd_price: row.usd_price,
                usd_market_cap: row.usd_market_cap,
                native_price: row.native_price,
                native_token_liquidity: row.native_token_liquidity,
                usd_token_liquidity: row.usd_token_liquidity,
                total_native_token_volume: row.total_native_token_volume,
                total_usd_token_volume: row.total_usd_token_volume,
                total_native_token_buy_volume: row.total_native_token_buy_volume,
                total_usd_token_buy_volume: row.total_usd_token_buy_volume,
                total_native_token_sell_volume: row.total_native_token_sell_volume,
                total_usd_token_sell_volume: row.total_usd_token_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
                best_pool_dex: row.best_pool_dex,
            }),
            None => None,
        };

        Ok(token_state)
    }

    pub async fn insert_token_states(&self, token_states: &[TokenState]) -> Result<(), Error> {
        let len = token_states.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_token_state(&token_states[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = token_states.iter().map(|p| p.chain).collect();
        let token_addresses: Vec<String> =
            token_states.iter().map(|p| p.token_address.clone()).collect();
        let timestamp_millis: Vec<i64> = token_states.iter().map(|p| p.timestamp_millis).collect();
        let best_pool_addresses: Vec<String> =
            token_states.iter().map(|p| p.best_pool_address.clone()).collect();
        let usd_prices: Vec<f64> = token_states.iter().map(|p| p.usd_price).collect();
        let usd_market_caps: Vec<f64> = token_states.iter().map(|p| p.usd_market_cap).collect();
        let native_prices: Vec<Option<f64>> = token_states.iter().map(|p| p.native_price).collect();
        let native_token_liquidity: Vec<f64> =
            token_states.iter().map(|p| p.native_token_liquidity).collect();
        let usd_token_liquidity: Vec<f64> =
            token_states.iter().map(|p| p.usd_token_liquidity).collect();
        let total_native_token_volumes: Vec<f64> =
            token_states.iter().map(|p| p.total_native_token_volume).collect();
        let total_usd_token_volumes: Vec<f64> =
            token_states.iter().map(|p| p.total_usd_token_volume).collect();
        let total_native_token_buy_volumes: Vec<f64> =
            token_states.iter().map(|p| p.total_native_token_buy_volume).collect();
        let total_usd_token_buy_volumes: Vec<f64> =
            token_states.iter().map(|p| p.total_usd_token_buy_volume).collect();
        let total_native_token_sell_volumes: Vec<f64> =
            token_states.iter().map(|p| p.total_native_token_sell_volume).collect();
        let total_usd_token_sell_volumes: Vec<f64> =
            token_states.iter().map(|p| p.total_usd_token_sell_volume).collect();
        let total_txns: Vec<i64> = token_states.iter().map(|p| p.total_txns as i64).collect();
        let total_buy_txns: Vec<i64> =
            token_states.iter().map(|p| p.total_buy_txns as i64).collect();
        let total_sell_txns: Vec<i64> =
            token_states.iter().map(|p| p.total_sell_txns as i64).collect();
        let bonding_curve_progresses: Vec<Option<f64>> =
            token_states.iter().map(|p| p.bonding_curve_progress).collect();
        let best_pool_dexes: Vec<Dex> = token_states.iter().map(|p| p.best_pool_dex).collect();

        sqlx::query!(
            r#"
            INSERT INTO token_state (
                chain, token_address, timestamp_millis,
                best_pool_address,
                usd_price, usd_market_cap,
                native_price,
                native_token_liquidity, usd_token_liquidity,
                total_native_token_volume, total_usd_token_volume,
                total_native_token_buy_volume, total_usd_token_buy_volume,
                total_native_token_sell_volume, total_usd_token_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress,
                best_pool_dex)
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[], $3::bigint[],
                $4::varchar[],
                $5::double precision[], $6::double precision[],
                $7::double precision[],
                $8::double precision[], $9::double precision[],
                $10::double precision[], $11::double precision[],
                $12::double precision[], $13::double precision[],
                $14::double precision[], $15::double precision[],
                $16::bigint[], $17::bigint[], $18::bigint[],
                $19::double precision[],
                $20::dex_enum[])
            ON CONFLICT (chain, token_address, timestamp_millis) DO NOTHING
            "#,
            &chains as &[Chain],
            &token_addresses as &[String],
            &timestamp_millis as &[i64],
            &best_pool_addresses as &[String],
            &usd_prices as &[f64],
            &usd_market_caps as &[f64],
            &native_prices as &[Option<f64>],
            &native_token_liquidity as &[f64],
            &usd_token_liquidity as &[f64],
            &total_native_token_volumes as &[f64],
            &total_usd_token_volumes as &[f64],
            &total_native_token_buy_volumes as &[f64],
            &total_usd_token_buy_volumes as &[f64],
            &total_native_token_sell_volumes as &[f64],
            &total_usd_token_sell_volumes as &[f64],
            &total_txns as &[i64],
            &total_buy_txns as &[i64],
            &total_sell_txns as &[i64],
            &bonding_curve_progresses as &[Option<f64>],
            &best_pool_dexes as &[Dex],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}
