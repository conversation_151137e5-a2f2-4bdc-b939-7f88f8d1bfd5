use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use sqlx::types::BigDecimal;

use crate::{postgres::*, Error};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct TokenHolder {
    pub chain: Chain,
    pub token_address: String,
    pub holder_address: String,

    pub bought_amount: BigDecimal,
    pub sold_amount: BigDecimal,

    pub bought_txns: u64,
    pub sold_txns: u64,

    pub spent_native_token_ui_amount: f64,
    pub spent_usd_token_ui_amount: f64,
    pub total_spent_usd: f64,

    pub received_native_token_ui_amount: f64,
    pub received_usd_token_ui_amount: f64,
    pub total_received_usd: f64,

    pub update_timestamp_millis: i64,
    pub update_block_number: u64,
}

#[derive(Debug, Clone)]
pub struct TokenHolderDelta {
    pub bought_amount: BigDecimal,
    pub sold_amount: BigDecimal,

    pub bought_txns: u64,
    pub sold_txns: u64,

    pub spent_native_token_ui_amount: f64,
    pub spent_usd_token_ui_amount: f64,
    pub total_spent_usd: f64,

    pub received_native_token_ui_amount: f64,
    pub received_usd_token_ui_amount: f64,
    pub total_received_usd: f64,

    pub update_timestamp_millis: i64,
    pub update_block_number: u64,
}

impl TokenHolderDelta {
    pub fn new(update_timestamp_millis: i64, update_block_number: u64) -> Self {
        Self {
            bought_amount: BigDecimal::from(0),
            sold_amount: BigDecimal::from(0),
            bought_txns: 0,
            sold_txns: 0,
            spent_native_token_ui_amount: 0.0,
            spent_usd_token_ui_amount: 0.0,
            total_spent_usd: 0.0,
            received_native_token_ui_amount: 0.0,
            received_usd_token_ui_amount: 0.0,
            total_received_usd: 0.0,
            update_timestamp_millis,
            update_block_number,
        }
    }

    pub fn merge(&mut self, other: &TokenHolderDelta) {
        self.bought_amount += other.bought_amount.clone();
        self.sold_amount += other.sold_amount.clone();

        self.bought_txns += other.bought_txns;
        self.sold_txns += other.sold_txns;

        self.spent_native_token_ui_amount += other.spent_native_token_ui_amount;
        self.spent_usd_token_ui_amount += other.spent_usd_token_ui_amount;
        self.total_spent_usd += other.total_spent_usd;
        self.received_native_token_ui_amount += other.received_native_token_ui_amount;
        self.received_usd_token_ui_amount += other.received_usd_token_ui_amount;
        self.total_received_usd += other.total_received_usd;

        self.update_timestamp_millis =
            self.update_timestamp_millis.max(other.update_timestamp_millis);
        self.update_block_number = self.update_block_number.max(other.update_block_number);
    }
}

impl TokenHolder {
    pub fn new_from_delta(
        chain: Chain,
        token_address: String,
        holder_address: String,
        delta: TokenHolderDelta,
    ) -> Self {
        Self {
            chain,
            token_address,
            holder_address,
            bought_amount: delta.bought_amount,
            sold_amount: delta.sold_amount,
            bought_txns: delta.bought_txns,
            sold_txns: delta.sold_txns,
            spent_native_token_ui_amount: delta.spent_native_token_ui_amount,
            spent_usd_token_ui_amount: delta.spent_usd_token_ui_amount,
            total_spent_usd: delta.total_spent_usd,
            received_native_token_ui_amount: delta.received_native_token_ui_amount,
            received_usd_token_ui_amount: delta.received_usd_token_ui_amount,
            total_received_usd: delta.total_received_usd,
            update_timestamp_millis: delta.update_timestamp_millis,
            update_block_number: delta.update_block_number,
        }
    }

    pub fn new_solana_token_holder(
        token_address: Pubkey,
        holder_address: Pubkey,
        block_number: u64,
        timestamp_millis: i64,
    ) -> Self {
        Self {
            chain: Chain::Solana,
            token_address: token_address.to_string(),
            holder_address: holder_address.to_string(),
            bought_amount: BigDecimal::from(0),
            sold_amount: BigDecimal::from(0),
            bought_txns: 0,
            sold_txns: 0,
            spent_native_token_ui_amount: 0.0,
            spent_usd_token_ui_amount: 0.0,
            total_spent_usd: 0.0,
            received_native_token_ui_amount: 0.0,
            received_usd_token_ui_amount: 0.0,
            total_received_usd: 0.0,
            update_timestamp_millis: timestamp_millis,
            update_block_number: block_number,
        }
    }

    pub fn merge(&mut self, other: &TokenHolderDelta) {
        self.bought_amount += other.bought_amount.clone();
        self.sold_amount += other.sold_amount.clone();

        self.bought_txns += other.bought_txns;
        self.sold_txns += other.sold_txns;

        self.spent_native_token_ui_amount += other.spent_native_token_ui_amount;
        self.spent_usd_token_ui_amount += other.spent_usd_token_ui_amount;
        self.total_spent_usd += other.total_spent_usd;
        self.received_native_token_ui_amount += other.received_native_token_ui_amount;
        self.received_usd_token_ui_amount += other.received_usd_token_ui_amount;
        self.total_received_usd += other.total_received_usd;

        self.update_timestamp_millis =
            self.update_timestamp_millis.max(other.update_timestamp_millis);
        self.update_block_number = self.update_block_number.max(other.update_block_number);
    }

    pub fn is_newer_than(&self, other: &Self) -> bool {
        if self.bought_txns > other.bought_txns || self.sold_txns > other.sold_txns {
            return true;
        } else if self.bought_txns < other.bought_txns || self.sold_txns < other.sold_txns {
            return false;
        }

        if self.sold_amount > other.sold_amount || self.bought_amount > other.bought_amount {
            return true;
        } else if self.sold_amount < other.sold_amount || self.bought_amount < other.bought_amount {
            return false;
        }

        false
    }
}

impl PostgresDatabase {
    pub async fn insert_or_update_token_holder(
        &self,
        token_holder: &TokenHolder,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO token_holder (
                chain, token_address, holder_address,
                bought_amount, sold_amount,
                bought_txns, sold_txns,
                spent_native_token_ui_amount, spent_usd_token_ui_amount, total_spent_usd,
                received_native_token_ui_amount, received_usd_token_ui_amount, total_received_usd,
                update_timestamp_millis, update_block_number
            )
            VALUES (
                $1, $2, $3,
                $4, $5,
                $6, $7,
                $8, $9, $10,
                $11, $12, $13,
                $14, $15
            )
            ON CONFLICT (chain, token_address, holder_address) DO UPDATE SET
                bought_amount = EXCLUDED.bought_amount,
                sold_amount = EXCLUDED.sold_amount,
                bought_txns = EXCLUDED.bought_txns,
                sold_txns = EXCLUDED.sold_txns,
                spent_native_token_ui_amount = EXCLUDED.spent_native_token_ui_amount,
                spent_usd_token_ui_amount = EXCLUDED.spent_usd_token_ui_amount,
                total_spent_usd = EXCLUDED.total_spent_usd,
                received_native_token_ui_amount = EXCLUDED.received_native_token_ui_amount,
                received_usd_token_ui_amount = EXCLUDED.received_usd_token_ui_amount,
                total_received_usd = EXCLUDED.total_received_usd,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis,
                update_block_number = EXCLUDED.update_block_number
            WHERE token_holder.update_block_number <= EXCLUDED.update_block_number
            "#,
            token_holder.chain as Chain,
            token_holder.token_address,
            token_holder.holder_address,
            token_holder.bought_amount,
            token_holder.sold_amount,
            token_holder.bought_txns as i64,
            token_holder.sold_txns as i64,
            token_holder.spent_native_token_ui_amount,
            token_holder.spent_usd_token_ui_amount,
            token_holder.total_spent_usd,
            token_holder.received_native_token_ui_amount,
            token_holder.received_usd_token_ui_amount,
            token_holder.total_received_usd,
            token_holder.update_timestamp_millis,
            token_holder.update_block_number as i64,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_token_holder(
        &self,
        chain: Chain,
        token_address: &str,
        holder_address: &str,
    ) -> Result<Option<TokenHolder>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT 
                chain AS "chain: Chain",
                token_address, holder_address,
                bought_amount, sold_amount,
                bought_txns, sold_txns,
                spent_native_token_ui_amount, spent_usd_token_ui_amount,
                total_spent_usd,
                received_native_token_ui_amount, received_usd_token_ui_amount,
                total_received_usd,
                update_timestamp_millis, update_block_number
            FROM token_holder
            WHERE chain = $1 AND token_address = $2 AND holder_address = $3
            "#,
            chain as Chain,
            token_address,
            holder_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let pool_state = match row {
            Some(row) => Some(TokenHolder {
                chain: row.chain,
                token_address: row.token_address,
                holder_address: row.holder_address,
                bought_amount: row.bought_amount,
                sold_amount: row.sold_amount,
                bought_txns: row.bought_txns as u64,
                sold_txns: row.sold_txns as u64,
                spent_native_token_ui_amount: row.spent_native_token_ui_amount,
                spent_usd_token_ui_amount: row.spent_usd_token_ui_amount,
                total_spent_usd: row.total_spent_usd,
                received_native_token_ui_amount: row.received_native_token_ui_amount,
                received_usd_token_ui_amount: row.received_usd_token_ui_amount,
                total_received_usd: row.total_received_usd,
                update_timestamp_millis: row.update_timestamp_millis,
                update_block_number: row.update_block_number as u64,
            }),
            None => None,
        };

        Ok(pool_state)
    }

    pub async fn insert_or_update_token_holders(
        &self,
        token_holders: &[TokenHolder],
    ) -> Result<(), Error> {
        let len = token_holders.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_token_holder(&token_holders[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = token_holders.iter().map(|p| p.chain).collect();
        let token_addresses: Vec<String> =
            token_holders.iter().map(|p| p.token_address.clone()).collect();
        let holder_addresses: Vec<String> =
            token_holders.iter().map(|p| p.holder_address.clone()).collect();
        let bought_amounts: Vec<BigDecimal> =
            token_holders.iter().map(|p| p.bought_amount.clone()).collect();
        let sold_amounts: Vec<BigDecimal> =
            token_holders.iter().map(|p| p.sold_amount.clone()).collect();
        let bought_txns: Vec<i64> = token_holders.iter().map(|p| p.bought_txns as i64).collect();
        let sold_txns: Vec<i64> = token_holders.iter().map(|p| p.sold_txns as i64).collect();
        let spent_native_token_ui_amounts: Vec<f64> =
            token_holders.iter().map(|p| p.spent_native_token_ui_amount).collect();
        let spent_usd_token_ui_amounts: Vec<f64> =
            token_holders.iter().map(|p| p.spent_usd_token_ui_amount).collect();
        let total_spent_usds: Vec<f64> = token_holders.iter().map(|p| p.total_spent_usd).collect();
        let received_native_token_ui_amounts: Vec<f64> =
            token_holders.iter().map(|p| p.received_native_token_ui_amount).collect();
        let received_usd_token_ui_amounts: Vec<f64> =
            token_holders.iter().map(|p| p.received_usd_token_ui_amount).collect();
        let total_received_usds: Vec<f64> =
            token_holders.iter().map(|p| p.total_received_usd).collect();
        let update_timestamp_millis: Vec<i64> =
            token_holders.iter().map(|p| p.update_timestamp_millis).collect();
        let update_block_numbers: Vec<i64> =
            token_holders.iter().map(|p| p.update_block_number as i64).collect();

        sqlx::query!(
            r#"
            INSERT INTO token_holder (
                chain, token_address, holder_address,
                bought_amount, sold_amount,
                bought_txns, sold_txns,
                spent_native_token_ui_amount, spent_usd_token_ui_amount, total_spent_usd,
                received_native_token_ui_amount, received_usd_token_ui_amount, total_received_usd,
                update_timestamp_millis, update_block_number)
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[], $3::varchar[],
                $4::numeric[], $5::numeric[],
                $6::bigint[], $7::bigint[],
                $8::double precision[], $9::double precision[], $10::double precision[],
                $11::double precision[], $12::double precision[], $13::double precision[],
                $14::bigint[], $15::bigint[])
            ON CONFLICT (chain, token_address, holder_address) DO UPDATE SET
                bought_amount = EXCLUDED.bought_amount,
                sold_amount = EXCLUDED.sold_amount,
                bought_txns = EXCLUDED.bought_txns,
                sold_txns = EXCLUDED.sold_txns,
                spent_native_token_ui_amount = EXCLUDED.spent_native_token_ui_amount,
                spent_usd_token_ui_amount = EXCLUDED.spent_usd_token_ui_amount,
                total_spent_usd = EXCLUDED.total_spent_usd,
                received_native_token_ui_amount = EXCLUDED.received_native_token_ui_amount,
                received_usd_token_ui_amount = EXCLUDED.received_usd_token_ui_amount,
                total_received_usd = EXCLUDED.total_received_usd,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis,
                update_block_number = EXCLUDED.update_block_number
            WHERE token_holder.update_block_number <= EXCLUDED.update_block_number
            "#,
            &chains as &[Chain],
            &token_addresses as &[String],
            &holder_addresses as &[String],
            &bought_amounts as &[BigDecimal],
            &sold_amounts as &[BigDecimal],
            &bought_txns as &[i64],
            &sold_txns as &[i64],
            &spent_native_token_ui_amounts as &[f64],
            &spent_usd_token_ui_amounts as &[f64],
            &total_spent_usds as &[f64],
            &received_native_token_ui_amounts as &[f64],
            &received_usd_token_ui_amounts as &[f64],
            &total_received_usds as &[f64],
            &update_timestamp_millis as &[i64],
            &update_block_numbers as &[i64],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}
