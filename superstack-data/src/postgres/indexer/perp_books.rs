use serde::{Deserialize, Serialize};

use crate::postgres::PerpExchange;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct PerpBooks {
    pub perp_id: String,
    pub perp_exchange: PerpExchange,
    pub updated_at_millis: i64,

    pub total_sz: f64,
    pub books: [Vec<PerpBook>; 2],
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PerpBook {
    /// Price
    pub px: f64,
    /// Size
    pub sz: f64,
    /// Number of orders
    pub n: u64,
}
