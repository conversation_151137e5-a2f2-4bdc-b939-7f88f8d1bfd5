use bigdecimal::BigDecimal;
use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::{
    postgres::{PerpExchange, PostgresDatabase},
    Error,
};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow, PartialEq, Eq)]
pub struct PerpInfo {
    pub perp_exchange: PerpExchange,
    pub perp_id: String,

    pub is_native_token: bool,
    pub network: Option<String>,
    pub address: Option<String>,

    pub name: String,
    pub symbol: String,

    pub socials: Value,

    pub total_supply: BigDecimal,
    pub circulating_supply: BigDecimal,

    pub update_timestamp_millis: i64,
}

impl PerpInfo {
    pub fn set_x_url(&mut self, x_url: Option<String>) {
        if let Some(x_url) = x_url {
            self.socials["x"] = Value::String(x_url);
        }
    }

    pub fn set_telegram_url(&mut self, telegram_url: Option<String>) {
        if let Some(telegram_url) = telegram_url {
            self.socials["telegram"] = Value::String(telegram_url);
        }
    }

    pub fn set_website_url(&mut self, website_url: Option<String>) {
        if let Some(website_url) = website_url {
            self.socials["website"] = Value::String(website_url);
        }
    }
}

impl PostgresDatabase {
    pub async fn insert_or_update_perp_info(&self, perp_info: &PerpInfo) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO perp_info (
                perp_exchange, perp_id,
                is_native_token, network, address,
                name, symbol,
                socials,
                total_supply, circulating_supply,
                update_timestamp_millis
            )
            VALUES (
                $1, $2,
                $3, $4, $5,
                $6, $7,
                $8,
                $9, $10,
                $11
            )
            ON CONFLICT (perp_exchange, perp_id) DO UPDATE SET
                is_native_token = EXCLUDED.is_native_token,
                network = EXCLUDED.network,
                address = EXCLUDED.address,
                name = EXCLUDED.name,
                symbol = EXCLUDED.symbol,
                socials = EXCLUDED.socials,
                total_supply = EXCLUDED.total_supply,
                circulating_supply = EXCLUDED.circulating_supply,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis
            WHERE perp_info.update_timestamp_millis <= EXCLUDED.update_timestamp_millis
            "#,
            perp_info.perp_exchange as PerpExchange,
            perp_info.perp_id,
            perp_info.is_native_token,
            perp_info.network,
            perp_info.address,
            perp_info.name,
            perp_info.symbol,
            perp_info.socials,
            perp_info.total_supply,
            perp_info.circulating_supply,
            perp_info.update_timestamp_millis,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_perp_info(
        &self,
        perp_exchange: PerpExchange,
        perp_id: &str,
    ) -> Result<Option<PerpInfo>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                perp_exchange AS "perp_exchange: PerpExchange",
                perp_id,
                is_native_token,
                network,
                address,
                name,
                symbol,
                socials,
                total_supply,
                circulating_supply,
                update_timestamp_millis
            FROM perp_info
            WHERE perp_exchange = $1 AND perp_id = $2
            "#,
            perp_exchange as PerpExchange,
            perp_id,
        )
        .fetch_optional(&self.pool)
        .await?;

        let perp_info = match row {
            Some(row) => Some(PerpInfo {
                perp_exchange: row.perp_exchange,
                perp_id: row.perp_id,
                is_native_token: row.is_native_token,
                network: row.network,
                address: row.address,
                name: row.name,
                symbol: row.symbol,
                socials: row.socials,
                total_supply: row.total_supply,
                circulating_supply: row.circulating_supply,
                update_timestamp_millis: row.update_timestamp_millis,
            }),
            None => None,
        };

        Ok(perp_info)
    }

    pub async fn get_perp_infos_by_exchange(
        &self,
        perp_exchange: PerpExchange,
    ) -> Result<Vec<PerpInfo>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                perp_exchange AS "perp_exchange: PerpExchange",
                perp_id,
                is_native_token,
                network,
                address,
                name,
                symbol,
                socials,
                total_supply,
                circulating_supply,
                update_timestamp_millis
            FROM perp_info
            WHERE perp_exchange = $1
            ORDER BY symbol
            "#,
            perp_exchange as PerpExchange,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut perp_infos = Vec::with_capacity(rows.len());
        for row in rows {
            perp_infos.push(PerpInfo {
                perp_exchange: row.perp_exchange,
                perp_id: row.perp_id,
                is_native_token: row.is_native_token,
                network: row.network,
                address: row.address,
                name: row.name,
                symbol: row.symbol,
                socials: row.socials,
                total_supply: row.total_supply,
                circulating_supply: row.circulating_supply,
                update_timestamp_millis: row.update_timestamp_millis,
            });
        }

        Ok(perp_infos)
    }

    pub async fn get_perp_info_by_symbol(&self, symbol: &str) -> Result<Vec<PerpInfo>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                perp_exchange AS "perp_exchange: PerpExchange",
                perp_id,
                is_native_token,
                network,
                address,
                name,
                symbol,
                socials,
                total_supply,
                circulating_supply,
                update_timestamp_millis
            FROM perp_info
            WHERE symbol = $1
            ORDER BY perp_exchange, name
            "#,
            symbol,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut perp_infos = Vec::with_capacity(rows.len());
        for row in rows {
            perp_infos.push(PerpInfo {
                perp_exchange: row.perp_exchange,
                perp_id: row.perp_id,
                is_native_token: row.is_native_token,
                network: row.network,
                address: row.address,
                name: row.name,
                symbol: row.symbol,
                socials: row.socials,
                total_supply: row.total_supply,
                circulating_supply: row.circulating_supply,
                update_timestamp_millis: row.update_timestamp_millis,
            });
        }

        Ok(perp_infos)
    }

    pub async fn search_perp_infos(
        &self,
        search_term: &str,
        limit: i64,
    ) -> Result<Vec<PerpInfo>, Error> {
        let search_pattern = format!("%{}%", search_term);
        let rows = sqlx::query!(
            r#"
            SELECT
                perp_exchange AS "perp_exchange: PerpExchange",
                perp_id,
                is_native_token,
                network,
                address,
                name,
                symbol,
                socials,
                total_supply,
                circulating_supply,
                update_timestamp_millis
            FROM perp_info
            WHERE symbol ILIKE $1 OR name ILIKE $1
            ORDER BY
                CASE WHEN symbol ILIKE $1 THEN 1 ELSE 2 END,
                symbol
            LIMIT $2
            "#,
            search_pattern,
            limit,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut perp_infos = Vec::with_capacity(rows.len());
        for row in rows {
            perp_infos.push(PerpInfo {
                perp_exchange: row.perp_exchange,
                perp_id: row.perp_id,
                is_native_token: row.is_native_token,
                network: row.network,
                address: row.address,
                name: row.name,
                symbol: row.symbol,
                socials: row.socials,
                total_supply: row.total_supply,
                circulating_supply: row.circulating_supply,
                update_timestamp_millis: row.update_timestamp_millis,
            });
        }

        Ok(perp_infos)
    }

    pub async fn insert_or_update_perp_infos(&self, perp_infos: &[PerpInfo]) -> Result<(), Error> {
        let len = perp_infos.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_perp_info(&perp_infos[0]).await;
        }

        // Extract arrays for each column
        let perp_exchanges: Vec<PerpExchange> =
            perp_infos.iter().map(|p| p.perp_exchange).collect();
        let perp_ids: Vec<String> = perp_infos.iter().map(|p| p.perp_id.clone()).collect();
        let is_native_tokens: Vec<bool> = perp_infos.iter().map(|p| p.is_native_token).collect();
        let networks: Vec<Option<String>> = perp_infos.iter().map(|p| p.network.clone()).collect();
        let addresses: Vec<Option<String>> = perp_infos.iter().map(|p| p.address.clone()).collect();
        let names: Vec<String> = perp_infos.iter().map(|p| p.name.clone()).collect();
        let symbols: Vec<String> = perp_infos.iter().map(|p| p.symbol.clone()).collect();
        let socials: Vec<Value> = perp_infos.iter().map(|p| p.socials.clone()).collect();
        let total_supplies: Vec<BigDecimal> =
            perp_infos.iter().map(|p| p.total_supply.clone()).collect();
        let circulating_supplies: Vec<BigDecimal> =
            perp_infos.iter().map(|p| p.circulating_supply.clone()).collect();
        let update_timestamp_millis: Vec<i64> =
            perp_infos.iter().map(|p| p.update_timestamp_millis).collect();

        sqlx::query!(
            r#"
            INSERT INTO perp_info (
                perp_exchange, perp_id,
                is_native_token, network, address,
                name, symbol,
                socials,
                total_supply, circulating_supply,
                update_timestamp_millis)
            SELECT * FROM unnest(
                $1::perp_exchange_enum[], $2::varchar[],
                $3::boolean[], $4::varchar[], $5::varchar[],
                $6::varchar[], $7::varchar[],
                $8::jsonb[],
                $9::numeric[], $10::numeric[],
                $11::bigint[])
            ON CONFLICT (perp_exchange, perp_id) DO UPDATE SET
                is_native_token = EXCLUDED.is_native_token,
                network = EXCLUDED.network,
                address = EXCLUDED.address,
                name = EXCLUDED.name,
                symbol = EXCLUDED.symbol,
                socials = EXCLUDED.socials,
                total_supply = EXCLUDED.total_supply,
                circulating_supply = EXCLUDED.circulating_supply,
                update_timestamp_millis = EXCLUDED.update_timestamp_millis
            WHERE perp_info.update_timestamp_millis <= EXCLUDED.update_timestamp_millis
            "#,
            &perp_exchanges as &[PerpExchange],
            &perp_ids as &[String],
            &is_native_tokens as &[bool],
            &networks as &[Option<String>],
            &addresses as &[Option<String>],
            &names as &[String],
            &symbols as &[String],
            &socials as &[Value],
            &total_supplies as &[BigDecimal],
            &circulating_supplies as &[BigDecimal],
            &update_timestamp_millis as &[i64],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}
