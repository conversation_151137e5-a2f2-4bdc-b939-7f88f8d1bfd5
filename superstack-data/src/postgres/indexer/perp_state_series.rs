use crate::{
    postgres::{PerpExchange, PostgresDatabase},
    Error,
};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, sqlx::FromRow)]
pub struct PerpStateSeries {
    pub perp_exchange: PerpExchange,
    pub perp_id: String,

    pub funding: f64,
    pub open_interest: f64,
    pub mark_px: f64,
    pub market_cap: f64,

    pub long_ntl: f64,
    pub short_ntl: f64,
    pub long_traders: u64,
    pub short_traders: u64,
    pub long_entry: f64,
    pub short_entry: f64,

    pub created_at_millis: i64,
}

impl PostgresDatabase {
    pub async fn insert_perp_state_series(
        &self,
        perp_state_series: &[PerpStateSeries],
    ) -> Result<(), Error> {
        if perp_state_series.is_empty() {
            return Ok(());
        }

        let mut tx = self.pool.begin().await?;

        for v in perp_state_series {
            sqlx::query!(
                r#"
            INSERT INTO perp_state_series (
                perp_exchange, perp_id,
                funding, open_interest, mark_px, market_cap,
                long_ntl, short_ntl, long_traders, short_traders, long_entry, short_entry,
                created_at_millis
            )
            VALUES (
                $1, $2,
                $3, $4, $5, $6,
                $7, $8, $9, $10, $11, $12,
                $13
            )
        "#,
                v.perp_exchange as PerpExchange,
                v.perp_id,
                v.funding,
                v.open_interest,
                v.mark_px,
                v.market_cap,
                v.long_ntl,
                v.short_ntl,
                v.long_traders as i64,
                v.short_traders as i64,
                v.long_entry,
                v.short_entry,
                v.created_at_millis,
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;

        Ok(())
    }

    pub async fn search_perp_state_series_earlier_than(
        &self,
        perp_exchange: PerpExchange,
        earlier_than_millis: i64,
    ) -> Result<Vec<PerpStateSeries>, Error> {
        let rows = sqlx::query!(
            r#"
        SELECT DISTINCT ON (perp_id)
            perp_exchange AS "perp_exchange: PerpExchange",
            perp_id, funding, open_interest, mark_px, market_cap,
            long_ntl, short_ntl, long_traders, short_traders, long_entry, short_entry,
            created_at_millis
        FROM perp_state_series
        WHERE perp_exchange = $1 AND created_at_millis < $2
        ORDER BY perp_id, created_at_millis DESC
        "#,
            perp_exchange as PerpExchange,
            earlier_than_millis,
        )
        .fetch_all(&self.pool)
        .await?;

        let perp_state_series_list: Vec<PerpStateSeries> = rows
            .into_iter()
            .map(|row| PerpStateSeries {
                perp_exchange: row.perp_exchange,
                perp_id: row.perp_id,
                funding: row.funding,
                open_interest: row.open_interest,
                mark_px: row.mark_px,
                market_cap: row.market_cap,
                long_ntl: row.long_ntl,
                short_ntl: row.short_ntl,
                long_traders: row.long_traders as u64,
                short_traders: row.short_traders as u64,
                long_entry: row.long_entry,
                short_entry: row.short_entry,
                created_at_millis: row.created_at_millis,
            })
            .collect();

        Ok(perp_state_series_list)
    }

    pub async fn get_perp_state_series_earlier_than(
        &self,
        perp_exchange: PerpExchange,
        perp_id: &str,
        earlier_than_millis: i64,
    ) -> Result<Option<PerpStateSeries>, Error> {
        let row = sqlx::query!(
            r#"
        SELECT
            perp_exchange AS "perp_exchange: PerpExchange",
            perp_id, funding, open_interest, mark_px, market_cap,
            long_ntl, short_ntl, long_traders, short_traders, long_entry, short_entry,
            created_at_millis
        FROM perp_state_series
        WHERE perp_exchange = $1 AND perp_id = $2 AND created_at_millis < $3
        ORDER BY created_at_millis DESC
        "#,
            perp_exchange as PerpExchange,
            perp_id,
            earlier_than_millis,
        )
        .fetch_optional(&self.pool)
        .await?;

        let perp_state_series = match row {
            Some(row) => Some(PerpStateSeries {
                perp_exchange: row.perp_exchange,
                perp_id: row.perp_id,
                funding: row.funding,
                open_interest: row.open_interest,
                mark_px: row.mark_px,
                market_cap: row.market_cap,
                long_ntl: row.long_ntl,
                short_ntl: row.short_ntl,
                long_traders: row.long_traders as u64,
                short_traders: row.short_traders as u64,
                long_entry: row.long_entry,
                short_entry: row.short_entry,
                created_at_millis: row.created_at_millis,
            }),
            _ => None,
        };

        Ok(perp_state_series)
    }
}
