use alloy::primitives::U256;
use serde::{Deserialize, Serialize};
use sqlx::types::BigDecimal;

use crate::{postgres::*, utils::u256_to_big_decimal, Error};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct DexTrade {
    pub chain: Chain,
    pub tx_hash: String,
    pub ix_idx: u32,
    #[serde(default)]
    pub tx_idx: u32,

    pub pool_address: String,
    pub maker_address: String,

    pub is_buy_token: bool,
    pub in_address: String,
    pub in_amount: BigDecimal,
    pub out_address: String,
    pub out_amount: BigDecimal,

    pub block_number: u64,
    pub timestamp_millis: i64,
}

impl DexTrade {
    pub fn new_solana_dex_trade(
        tx_hash: String,
        ix_idx: u32,
        pool_address: String,
        maker_address: String,
        is_buy_token: bool,
        in_address: String,
        in_amount: u64,
        out_address: String,
        out_amount: u64,
        block_number: u64,
        timestamp_millis: i64,
        tx_idx: u32,
    ) -> Self {
        Self {
            chain: Chain::Solana,
            tx_hash,
            ix_idx,
            tx_idx,
            pool_address,
            maker_address,
            is_buy_token,
            in_address,
            in_amount: in_amount.into(),
            out_address,
            out_amount: out_amount.into(),
            block_number,
            timestamp_millis,
        }
    }

    pub fn new_hyper_evm_dex_trade(
        tx_hash: String,
        tx_idx: u32,
        pool_address: String,
        maker_address: String,
        is_buy_token: bool,
        in_address: String,
        in_amount: U256,
        out_address: String,
        out_amount: U256,
        block_number: u64,
        timestamp_millis: i64,
    ) -> Result<Self, Error> {
        Ok(Self {
            chain: Chain::HyperEvm,
            tx_hash,
            ix_idx: 0,
            tx_idx,
            pool_address,
            maker_address,
            is_buy_token,
            in_address,
            in_amount: u256_to_big_decimal(in_amount)?,
            out_address,
            out_amount: u256_to_big_decimal(out_amount)?,
            block_number,
            timestamp_millis,
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct MakersCounts {
    pub buyers: u64,
    pub sellers: u64,
    pub makers: u64,
}

impl PostgresDatabase {
    pub async fn insert_dex_trade(&self, dex_trade: &DexTrade) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO dex_trade (
                chain, tx_hash, ix_idx,
                pool_address, maker_address,
                is_buy_token,
                in_address, in_amount,
                out_address, out_amount,
                block_number, timestamp_millis,
                tx_idx
            )
            VALUES (
                $1, $2, $3,
                $4, $5,
                $6,
                $7, $8,
                $9, $10,
                $11, $12,
                $13
            )
            ON CONFLICT (chain, tx_hash, ix_idx) DO NOTHING
            "#,
            dex_trade.chain as Chain,
            dex_trade.tx_hash,
            dex_trade.ix_idx as i32,
            dex_trade.pool_address,
            dex_trade.maker_address,
            dex_trade.is_buy_token,
            dex_trade.in_address,
            dex_trade.in_amount,
            dex_trade.out_address,
            dex_trade.out_amount,
            dex_trade.block_number as i64,
            dex_trade.timestamp_millis,
            dex_trade.tx_idx as i32,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn insert_dex_trades(&self, dex_trades: &[DexTrade]) -> Result<(), Error> {
        let len = dex_trades.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_dex_trade(&dex_trades[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = dex_trades.iter().map(|p| p.chain).collect();
        let tx_hashes: Vec<String> = dex_trades.iter().map(|p| p.tx_hash.clone()).collect();
        let ix_idxs: Vec<i32> = dex_trades.iter().map(|p| p.ix_idx as i32).collect();
        let pool_addresses: Vec<String> =
            dex_trades.iter().map(|p| p.pool_address.clone()).collect();
        let maker_addresses: Vec<String> =
            dex_trades.iter().map(|p| p.maker_address.clone()).collect();
        let is_buy_tokens: Vec<bool> = dex_trades.iter().map(|p| p.is_buy_token).collect();
        let in_addresses: Vec<String> = dex_trades.iter().map(|p| p.in_address.clone()).collect();
        let in_amounts: Vec<BigDecimal> = dex_trades.iter().map(|p| p.in_amount.clone()).collect();
        let out_addresses: Vec<String> = dex_trades.iter().map(|p| p.out_address.clone()).collect();
        let out_amounts: Vec<BigDecimal> =
            dex_trades.iter().map(|p| p.out_amount.clone()).collect();
        let block_numbers: Vec<i64> = dex_trades.iter().map(|p| p.block_number as i64).collect();
        let timestamp_millis: Vec<i64> = dex_trades.iter().map(|p| p.timestamp_millis).collect();

        // New column
        let tx_idxs: Vec<i32> = dex_trades.iter().map(|p| p.tx_idx as i32).collect();

        sqlx::query!(
            r#"
            INSERT INTO dex_trade (
                chain, tx_hash, ix_idx,
                pool_address, maker_address,
                is_buy_token,
                in_address, in_amount,
                out_address, out_amount,
                block_number, timestamp_millis,
                tx_idx
            )
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[], $3::integer[],
                $4::varchar[], $5::varchar[],
                $6::boolean[],
                $7::varchar[], $8::numeric[],
                $9::varchar[], $10::numeric[],
                $11::bigint[], $12::bigint[],
                $13::integer[])
            ON CONFLICT (chain, tx_hash, ix_idx) DO NOTHING
            "#,
            &chains as &[Chain],
            &tx_hashes as &[String],
            &ix_idxs as &[i32],
            &pool_addresses as &[String],
            &maker_addresses as &[String],
            &is_buy_tokens as &[bool],
            &in_addresses as &[String],
            &in_amounts as &[BigDecimal],
            &out_addresses as &[String],
            &out_amounts as &[BigDecimal],
            &block_numbers as &[i64],
            &timestamp_millis as &[i64],
            &tx_idxs as &[i32],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_makers_counts(
        &self,
        chain: Chain,
        pool_address: &str,
        start_timestamp_millis: i64,
        end_timestamp_millis: i64,
    ) -> Result<MakersCounts, Error> {
        let row = sqlx::query!(
            r#"
            SELECT COUNT(DISTINCT maker_address) AS makers,
                COUNT(CASE WHEN is_buy_token THEN maker_address END) AS buyers,
                COUNT(CASE WHEN NOT is_buy_token THEN maker_address END) AS sellers
            FROM dex_trade
            WHERE chain = $1 AND pool_address = $2 AND timestamp_millis >= $3 AND timestamp_millis <= $4
            "#,
            chain as Chain,
            pool_address,
            start_timestamp_millis,
            end_timestamp_millis,
        )
        .fetch_optional(&self.pool)
        .await?;

        let count = match row {
            Some(row) => MakersCounts {
                buyers: row.buyers.unwrap_or(0) as u64,
                sellers: row.sellers.unwrap_or(0) as u64,
                makers: row.makers.unwrap_or(0) as u64,
            },
            None => MakersCounts::default(),
        };

        Ok(count)
    }

    pub async fn get_latest_dex_trade(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<DexTrade>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT chain AS "chain: Chain", tx_hash, ix_idx,
                pool_address, maker_address,
                is_buy_token,
                in_address, in_amount,
                out_address, out_amount,
                block_number, timestamp_millis,
                tx_idx
            FROM dex_trade
            WHERE chain = $1 AND pool_address = $2
            ORDER BY block_number DESC, tx_idx DESC, ix_idx DESC
            LIMIT 1
            "#,
            chain as Chain,
            pool_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row.map(|row| DexTrade {
            chain: row.chain,
            tx_hash: row.tx_hash,
            ix_idx: row.ix_idx as u32,
            tx_idx: row.tx_idx as u32,
            pool_address: row.pool_address,
            maker_address: row.maker_address,
            is_buy_token: row.is_buy_token,
            in_address: row.in_address,
            in_amount: row.in_amount,
            out_address: row.out_address,
            out_amount: row.out_amount,
            block_number: row.block_number as u64,
            timestamp_millis: row.timestamp_millis,
        }))
    }
}
