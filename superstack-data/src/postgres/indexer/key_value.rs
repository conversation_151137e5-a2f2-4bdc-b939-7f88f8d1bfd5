use serde::{Deserialize, Serialize};

use crate::{postgres::*, Result};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct KeyValue {
    pub key: String,
    pub value: Option<String>,
}

impl PostgresDatabase {
    pub async fn insert_or_update_key_value(&self, key: &str, value: Option<String>) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO key_value (key, value)
            VALUES ($1, $2)
            ON CONFLICT (key) DO UPDATE SET value = $2
            "#,
            key,
            value,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_key_value(&self, key: &str) -> Result<Option<String>> {
        let row = sqlx::query!(
            r#"
            SELECT
                value
            FROM key_value
            WHERE key = $1
            "#,
            key,
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row.and_then(|row| row.value))
    }
}
