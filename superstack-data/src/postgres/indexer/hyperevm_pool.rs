use serde::{Deserialize, Serialize};

use crate::{postgres::*, Result};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct HyperevmPool {
    pub pool_address: String,
}

impl PostgresDatabase {
    pub async fn insert_hyperevm_pool(&self, pool_address: String) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO hyperevm_pool (pool_address)
            VALUES ($1)
            ON CONFLICT (pool_address) DO NOTHING
            "#,
            pool_address,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_all_hyperevm_pools(&self) -> Result<Vec<String>> {
        let row = sqlx::query!(
            r#"
            SELECT
                pool_address
            FROM hyperevm_pool
            "#,
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(row.into_iter().map(|row| row.pool_address).collect())
    }
}
