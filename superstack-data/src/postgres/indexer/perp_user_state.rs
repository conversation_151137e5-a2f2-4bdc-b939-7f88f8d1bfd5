use serde::{Deserialize, Serialize};

use crate::{
    postgres::{PerpExchange, PostgresDatabase},
    Error,
};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct PerpUserState {
    pub perp_exchange: PerpExchange,
    pub perp_user: String,

    pub perps_account_value: f64,
    pub account_value: f64,

    pub hyperliquid_acc_collateral: f64,
    pub hyperliquid_acc_perps: f64,

    // pub perps_volume: f64,
    // pub account_volume: f64,
    pub timestamp_millis: i64,
}

impl PerpUserState {
    pub fn new(
        perp_exchange: PerpExchange,
        perp_user: String,
        perps_account_value: f64,
        account_value: f64,
        hyperliquid_acc_collateral: f64,
        hyperliquid_acc_perps: f64,
    ) -> Self {
        let now = chrono::Utc::now().timestamp_millis();
        Self {
            perp_exchange,
            perp_user,
            perps_account_value,
            account_value,
            hyperliquid_acc_collateral,
            hyperliquid_acc_perps,
            timestamp_millis: now,
        }
    }

    pub fn with_timestamp(
        perp_exchange: PerpExchange,
        perp_user: String,
        perps_account_value: f64,
        account_value: f64,
        hyperliquid_acc_collateral: f64,
        hyperliquid_acc_perps: f64,
        timestamp_millis: i64,
    ) -> Self {
        Self {
            perp_exchange,
            perp_user,
            perps_account_value,
            account_value,
            hyperliquid_acc_collateral,
            hyperliquid_acc_perps,
            timestamp_millis,
        }
    }
}

impl PostgresDatabase {
    /// Insert a single user state record
    pub async fn insert_perp_user_state(&self, user_state: &PerpUserState) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO perp_user_state (
                perp_exchange, perp_user,
                perps_account_value, account_value,
                hyperliquid_acc_collateral, hyperliquid_acc_perps,
                timestamp_millis
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (perp_exchange, perp_user, timestamp_millis) DO NOTHING
            "#,
            user_state.perp_exchange as PerpExchange,
            user_state.perp_user,
            user_state.perps_account_value,
            user_state.account_value,
            user_state.hyperliquid_acc_collateral,
            user_state.hyperliquid_acc_perps,
            user_state.timestamp_millis,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Batch insert user state records using PostgreSQL's unnest function for efficient bulk
    /// insertion
    pub async fn insert_perp_user_states(
        &self,
        user_states: &[PerpUserState],
    ) -> Result<(), Error> {
        let len = user_states.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_perp_user_state(&user_states[0]).await;
        }

        // Extract data for each column from the array
        let perp_exchanges: Vec<PerpExchange> =
            user_states.iter().map(|s| s.perp_exchange).collect();
        let perp_users: Vec<String> = user_states.iter().map(|s| s.perp_user.clone()).collect();
        let perps_account_values: Vec<f64> =
            user_states.iter().map(|s| s.perps_account_value).collect();
        let account_values: Vec<f64> = user_states.iter().map(|s| s.account_value).collect();
        let hyperliquid_acc_collaterals: Vec<f64> =
            user_states.iter().map(|s| s.hyperliquid_acc_collateral).collect();
        let hyperliquid_acc_perps: Vec<f64> =
            user_states.iter().map(|s| s.hyperliquid_acc_perps).collect();
        let timestamp_millis: Vec<i64> = user_states.iter().map(|s| s.timestamp_millis).collect();

        sqlx::query!(
            r#"
            INSERT INTO perp_user_state (
                perp_exchange, perp_user,
                perps_account_value, account_value,
                hyperliquid_acc_collateral, hyperliquid_acc_perps,
                timestamp_millis
            )
            SELECT * FROM unnest(
                $1::perp_exchange_enum[], $2::varchar[],
                $3::double precision[], $4::double precision[],
                $5::double precision[], $6::double precision[],
                $7::bigint[]
            )
            ON CONFLICT (perp_exchange, perp_user, timestamp_millis) DO NOTHING
            "#,
            &perp_exchanges as &[PerpExchange],
            &perp_users as &[String],
            &perps_account_values as &[f64],
            &account_values as &[f64],
            &hyperliquid_acc_collaterals as &[f64],
            &hyperliquid_acc_perps as &[f64],
            &timestamp_millis as &[i64],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Batch insert or update user state records, update if record already exists
    pub async fn insert_or_update_perp_user_states(
        &self,
        user_states: &[PerpUserState],
    ) -> Result<(), Error> {
        let len = user_states.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_perp_user_state(&user_states[0]).await;
        }

        // Extract data for each column from the array
        let perp_exchanges: Vec<PerpExchange> =
            user_states.iter().map(|s| s.perp_exchange).collect();
        let perp_users: Vec<String> = user_states.iter().map(|s| s.perp_user.clone()).collect();
        let perps_account_values: Vec<f64> =
            user_states.iter().map(|s| s.perps_account_value).collect();
        let account_values: Vec<f64> = user_states.iter().map(|s| s.account_value).collect();
        let hyperliquid_acc_collaterals: Vec<f64> =
            user_states.iter().map(|s| s.hyperliquid_acc_collateral).collect();
        let hyperliquid_acc_perps: Vec<f64> =
            user_states.iter().map(|s| s.hyperliquid_acc_perps).collect();
        let timestamp_millis: Vec<i64> = user_states.iter().map(|s| s.timestamp_millis).collect();

        sqlx::query!(
            r#"
            INSERT INTO perp_user_state (
                perp_exchange, perp_user,
                perps_account_value, account_value,
                hyperliquid_acc_collateral, hyperliquid_acc_perps,
                timestamp_millis
            )
            SELECT * FROM unnest(
                $1::perp_exchange_enum[], $2::varchar[],
                $3::double precision[], $4::double precision[],
                $5::double precision[], $6::double precision[],
                $7::bigint[]
            )
            ON CONFLICT (perp_exchange, perp_user, timestamp_millis) DO UPDATE SET
                perps_account_value = EXCLUDED.perps_account_value,
                account_value = EXCLUDED.account_value,
                hyperliquid_acc_collateral = EXCLUDED.hyperliquid_acc_collateral,
                hyperliquid_acc_perps = EXCLUDED.hyperliquid_acc_perps
            "#,
            &perp_exchanges as &[PerpExchange],
            &perp_users as &[String],
            &perps_account_values as &[f64],
            &account_values as &[f64],
            &hyperliquid_acc_collaterals as &[f64],
            &hyperliquid_acc_perps as &[f64],
            &timestamp_millis as &[i64],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Single insert or update user state record
    pub async fn insert_or_update_perp_user_state(
        &self,
        user_state: &PerpUserState,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO perp_user_state (
                perp_exchange, perp_user,
                perps_account_value, account_value,
                hyperliquid_acc_collateral, hyperliquid_acc_perps,
                timestamp_millis
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (perp_exchange, perp_user, timestamp_millis) DO UPDATE SET
                perps_account_value = EXCLUDED.perps_account_value,
                account_value = EXCLUDED.account_value,
                hyperliquid_acc_collateral = EXCLUDED.hyperliquid_acc_collateral,
                hyperliquid_acc_perps = EXCLUDED.hyperliquid_acc_perps
            "#,
            user_state.perp_exchange as PerpExchange,
            user_state.perp_user,
            user_state.perps_account_value,
            user_state.account_value,
            user_state.hyperliquid_acc_collateral,
            user_state.hyperliquid_acc_perps,
            user_state.timestamp_millis,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Query user state records within specified time range
    pub async fn get_perp_user_states(
        &self,
        perp_exchange: PerpExchange,
        perp_user: &str,
        start_timestamp_millis: Option<i64>,
        end_timestamp_millis: Option<i64>,
        limit: Option<i64>,
    ) -> Result<Vec<PerpUserState>, Error> {
        let limit = limit.unwrap_or(1000);

        let user_states = match (start_timestamp_millis, end_timestamp_millis) {
            (Some(start), Some(end)) => {
                sqlx::query_as!(
                    PerpUserState,
                    r#"
                    SELECT
                        perp_exchange as "perp_exchange: PerpExchange",
                        perp_user,
                        perps_account_value,
                        account_value,
                        hyperliquid_acc_collateral,
                        hyperliquid_acc_perps,
                        timestamp_millis
                    FROM perp_user_state
                    WHERE perp_exchange = $1 AND perp_user = $2
                        AND timestamp_millis >= $3 AND timestamp_millis <= $4
                    ORDER BY timestamp_millis DESC
                    LIMIT $5
                    "#,
                    perp_exchange as PerpExchange,
                    perp_user,
                    start,
                    end,
                    limit
                )
                .fetch_all(&self.pool)
                .await?
            }
            (Some(start), None) => {
                sqlx::query_as!(
                    PerpUserState,
                    r#"
                    SELECT
                        perp_exchange as "perp_exchange: PerpExchange",
                        perp_user,
                        perps_account_value,
                        account_value,
                        hyperliquid_acc_collateral,
                        hyperliquid_acc_perps,
                        timestamp_millis
                    FROM perp_user_state
                    WHERE perp_exchange = $1 AND perp_user = $2 AND timestamp_millis >= $3
                    ORDER BY timestamp_millis DESC
                    LIMIT $4
                    "#,
                    perp_exchange as PerpExchange,
                    perp_user,
                    start,
                    limit
                )
                .fetch_all(&self.pool)
                .await?
            }
            (None, Some(end)) => {
                sqlx::query_as!(
                    PerpUserState,
                    r#"
                    SELECT
                        perp_exchange as "perp_exchange: PerpExchange",
                        perp_user,
                        perps_account_value,
                        account_value,
                        hyperliquid_acc_collateral,
                        hyperliquid_acc_perps,
                        timestamp_millis
                    FROM perp_user_state
                    WHERE perp_exchange = $1 AND perp_user = $2 AND timestamp_millis <= $3
                    ORDER BY timestamp_millis DESC
                    LIMIT $4
                    "#,
                    perp_exchange as PerpExchange,
                    perp_user,
                    end,
                    limit
                )
                .fetch_all(&self.pool)
                .await?
            }
            (None, None) => {
                sqlx::query_as!(
                    PerpUserState,
                    r#"
                    SELECT
                        perp_exchange as "perp_exchange: PerpExchange",
                        perp_user,
                        perps_account_value,
                        account_value,
                        hyperliquid_acc_collateral,
                        hyperliquid_acc_perps,
                        timestamp_millis
                    FROM perp_user_state
                    WHERE perp_exchange = $1 AND perp_user = $2
                    ORDER BY timestamp_millis DESC
                    LIMIT $3
                    "#,
                    perp_exchange as PerpExchange,
                    perp_user,
                    limit
                )
                .fetch_all(&self.pool)
                .await?
            }
        };

        Ok(user_states)
    }

    /// Get the latest state for specified user
    pub async fn get_latest_perp_user_state(
        &self,
        perp_exchange: PerpExchange,
        perp_user: &str,
    ) -> Result<Option<PerpUserState>, Error> {
        let user_state = sqlx::query_as!(
            PerpUserState,
            r#"
            SELECT
                perp_exchange as "perp_exchange: PerpExchange",
                perp_user,
                perps_account_value,
                account_value,
                hyperliquid_acc_collateral,
                hyperliquid_acc_perps,
                timestamp_millis
            FROM perp_user_state
            WHERE perp_exchange = $1 AND perp_user = $2
            ORDER BY timestamp_millis DESC
            LIMIT 1
            "#,
            perp_exchange as PerpExchange,
            perp_user
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user_state)
    }

    /// Get all user states at specified timestamp for given exchange
    pub async fn get_perp_user_states_at_timestamp(
        &self,
        perp_exchange: PerpExchange,
        timestamp_millis: i64,
        limit: Option<i64>,
    ) -> Result<Vec<PerpUserState>, Error> {
        let limit = limit.unwrap_or(1000);

        let user_states = sqlx::query_as!(
            PerpUserState,
            r#"
            SELECT
                perp_exchange as "perp_exchange: PerpExchange",
                perp_user,
                perps_account_value,
                account_value,
                hyperliquid_acc_collateral,
                hyperliquid_acc_perps,
                timestamp_millis
            FROM perp_user_state
            WHERE perp_exchange = $1 AND timestamp_millis = $2
            ORDER BY account_value DESC
            LIMIT $3
            "#,
            perp_exchange as PerpExchange,
            timestamp_millis,
            limit
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(user_states)
    }

    /// Query user state records with sampling interval to reduce data volume
    /// This method performs sampling at the database level for better performance
    pub async fn get_perp_user_states_sampled(
        &self,
        perp_exchange: PerpExchange,
        perp_user: &str,
        start_timestamp_millis: i64,
        end_timestamp_millis: i64,
        sample_interval_seconds: i64,
    ) -> Result<Vec<PerpUserState>, Error> {
        // Use PostgreSQL window functions to sample data efficiently
        // This query groups timestamps into buckets and selects one record per bucket
        let user_states = sqlx::query_as!(
            PerpUserState,
            r#"
            WITH sampled_data AS (
                SELECT DISTINCT ON (bucket)
                    perp_exchange,
                    perp_user,
                    perps_account_value,
                    account_value,
                    hyperliquid_acc_collateral,
                    hyperliquid_acc_perps,
                    timestamp_millis,
                    -- Create time buckets based on sample interval
                    FLOOR(timestamp_millis / ($5 * 1000)) as bucket
                FROM perp_user_state
                WHERE perp_exchange = $1
                    AND perp_user = $2
                    AND timestamp_millis >= $3
                    AND timestamp_millis <= $4
                ORDER BY bucket, timestamp_millis ASC
            )
            SELECT
                perp_exchange as "perp_exchange: PerpExchange",
                perp_user,
                perps_account_value,
                account_value,
                hyperliquid_acc_collateral,
                hyperliquid_acc_perps,
                timestamp_millis
            FROM sampled_data
            ORDER BY timestamp_millis ASC
            "#,
            perp_exchange as PerpExchange,
            perp_user,
            start_timestamp_millis,
            end_timestamp_millis,
            sample_interval_seconds as i32
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(user_states)
    }

    /// Chunked batch insert for large volumes of user state records
    /// Recommended for processing 100K+ records to avoid memory and database issues
    pub async fn insert_perp_user_states_chunked(
        &self,
        user_states: &[PerpUserState],
        chunk_size: Option<usize>,
    ) -> Result<(), Error> {
        let chunk_size = chunk_size.unwrap_or(1000); // Default chunk size

        if user_states.len() <= chunk_size {
            return self.insert_perp_user_states(user_states).await;
        }

        let mut total_processed = 0;
        for chunk in user_states.chunks(chunk_size) {
            self.insert_perp_user_states(chunk).await?;
            total_processed += chunk.len();

            // Log progress for large batches
            if user_states.len() > 10000 && total_processed % (chunk_size * 10) == 0 {
                tracing::info!(
                    "Processed {}/{} perp user states",
                    total_processed,
                    user_states.len()
                );
            }
        }

        tracing::info!("Successfully inserted {} perp user states", user_states.len());
        Ok(())
    }

    /// Chunked batch insert or update for large volumes of user state records
    /// Recommended for processing 100K+ records to avoid memory and database issues
    pub async fn insert_or_update_perp_user_states_chunked(
        &self,
        user_states: &[PerpUserState],
        chunk_size: Option<usize>,
    ) -> Result<(), Error> {
        let chunk_size = chunk_size.unwrap_or(1000); // Default chunk size

        if user_states.len() <= chunk_size {
            return self.insert_or_update_perp_user_states(user_states).await;
        }

        let mut total_processed = 0;
        for chunk in user_states.chunks(chunk_size) {
            self.insert_or_update_perp_user_states(chunk).await?;
            total_processed += chunk.len();

            // Log progress for large batches
            if user_states.len() > 10000 && total_processed % (chunk_size * 10) == 0 {
                tracing::info!(
                    "Processed {}/{} perp user states",
                    total_processed,
                    user_states.len()
                );
            }
        }

        tracing::info!("Successfully upserted {} perp user states", user_states.len());
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::postgres::{database::tests::get_postgres_database, PerpExchange};
    use chrono::DateTime;

    /// Helper function to generate test perp user state data
    fn generate_test_perp_user_states(
        wallet_address: &str,
        start_timestamp_millis: i64,
        interval_seconds: i64,
        count: usize,
        base_account_value: f64,
    ) -> Vec<PerpUserState> {
        let mut user_states = Vec::new();

        for i in 0..count {
            let timestamp_millis = start_timestamp_millis + (i as i64 * interval_seconds * 1000);
            let account_value = base_account_value + (i as f64 * 100.0); // Gradual increase
            let perps_value = account_value * 0.8; // 80% of total account value
            let collateral = account_value * 0.9; // 90% of total account value
            let perps = account_value * 0.75; // 75% of total account value

            user_states.push(PerpUserState::with_timestamp(
                PerpExchange::Hyperliquid,
                wallet_address.to_string(),
                perps_value,
                account_value,
                collateral,
                perps,
                timestamp_millis,
            ));
        }

        user_states
    }

    /// Helper function to get a specific timestamp (2024-01-01 00:00:00 UTC) in milliseconds
    fn get_test_start_timestamp_millis() -> i64 {
        DateTime::parse_from_rfc3339("2024-01-01T00:00:00Z").unwrap().timestamp_millis()
    }

    #[tokio::test]
    async fn test_insert_and_get_perp_user_states() {
        let database = get_postgres_database().await;
        let wallet_address = format!("test_wallet_123_{}", chrono::Utc::now().timestamp_millis());

        // Generate test data - 100 records over 100 minutes (1 per minute)
        let start_timestamp_millis = get_test_start_timestamp_millis();
        let user_states = generate_test_perp_user_states(
            &wallet_address,
            start_timestamp_millis,
            60,      // 1 minute interval
            100,     // 100 records
            10000.0, // Starting at $10,000
        );

        println!(
            "Generated {} user states starting from timestamp {}",
            user_states.len(),
            start_timestamp_millis
        );

        // Insert test data
        database.insert_perp_user_states(&user_states).await.unwrap();

        // Test basic get_perp_user_states
        let end_timestamp_millis = start_timestamp_millis + (100 * 60 * 1000); // 100 minutes later
        let retrieved_states = database
            .get_perp_user_states(
                PerpExchange::Hyperliquid,
                &wallet_address,
                Some(start_timestamp_millis),
                Some(end_timestamp_millis),
                None,
            )
            .await
            .unwrap();

        println!("Retrieved {} states for wallet {}", retrieved_states.len(), wallet_address);
        assert_eq!(retrieved_states.len(), 100);
        assert_eq!(retrieved_states[0].perp_user, wallet_address);
    }

    #[tokio::test]
    async fn test_get_perp_user_states_sampled() {
        let database = get_postgres_database().await;
        let wallet_address =
            format!("test_wallet_sampled_{}", chrono::Utc::now().timestamp_millis());

        // Generate test data - 1440 records over 24 hours (1 per minute)
        let start_timestamp_millis = get_test_start_timestamp_millis();
        let user_states = generate_test_perp_user_states(
            &wallet_address,
            start_timestamp_millis,
            60,      // 1 minute interval
            1440,    // 24 hours worth of data
            15000.0, // Starting at $15,000
        );

        println!("Generated {} user states for sampling test", user_states.len());

        // Insert test data
        database.insert_perp_user_states(&user_states).await.unwrap();

        // Verify data was inserted
        let all_states = database
            .get_perp_user_states(PerpExchange::Hyperliquid, &wallet_address, None, None, None)
            .await
            .unwrap();
        println!("Total states in DB for wallet {}: {}", wallet_address, all_states.len());

        // Test 5-minute sampling (should return ~288 records)
        let end_timestamp_millis = start_timestamp_millis + (24 * 60 * 60 * 1000); // 24 hours later
        let sampled_states = database
            .get_perp_user_states_sampled(
                PerpExchange::Hyperliquid,
                &wallet_address,
                start_timestamp_millis,
                end_timestamp_millis,
                300, // 5 minutes = 300 seconds
            )
            .await
            .unwrap();

        println!("Sampled {} states from {} total states", sampled_states.len(), all_states.len());

        // Should have approximately 24 hours / 5 minutes = 288 samples
        // Be more lenient with the assertion since exact sampling depends on timing
        assert!(
            sampled_states.len() >= 100,
            "Expected at least 100 samples, got {}",
            sampled_states.len()
        );
        assert!(
            sampled_states.len() <= 350,
            "Expected at most 350 samples, got {}",
            sampled_states.len()
        );

        // Verify that samples are properly spaced
        if sampled_states.len() > 1 {
            let time_diff = sampled_states[1].timestamp_millis - sampled_states[0].timestamp_millis;
            // Should be approximately 5 minutes (300,000 ms), with some tolerance
            assert!(
                time_diff >= 250_000 && time_diff <= 350_000,
                "Expected time diff between 250s and 350s, got {}ms",
                time_diff
            );
        }
    }

    #[tokio::test]
    async fn test_get_perp_user_states_sampled_hourly() {
        let database = get_postgres_database().await;
        let wallet_address =
            format!("test_wallet_hourly_{}", chrono::Utc::now().timestamp_millis());

        // Generate test data - 720 records over 12 hours (1 per minute)
        let start_timestamp_millis = get_test_start_timestamp_millis();
        let user_states = generate_test_perp_user_states(
            &wallet_address,
            start_timestamp_millis,
            60,      // 1 minute interval
            720,     // 12 hours worth of data
            20000.0, // Starting at $20,000
        );

        // Insert test data
        database.insert_perp_user_states(&user_states).await.unwrap();

        // Test 1-hour sampling (should return ~12 records)
        let end_timestamp_millis = start_timestamp_millis + (12 * 60 * 60 * 1000); // 12 hours later
        let sampled_states = database
            .get_perp_user_states_sampled(
                PerpExchange::Hyperliquid,
                &wallet_address,
                start_timestamp_millis,
                end_timestamp_millis,
                3600, // 1 hour = 3600 seconds
            )
            .await
            .unwrap();

        println!("Hourly sampling returned {} states", sampled_states.len());

        // Should have approximately 12 hours / 1 hour = 12 samples
        // Be more lenient with expectations
        assert!(
            sampled_states.len() >= 8,
            "Expected at least 8 samples, got {}",
            sampled_states.len()
        );
        assert!(
            sampled_states.len() <= 15,
            "Expected at most 15 samples, got {}",
            sampled_states.len()
        );

        // Verify chronological ordering
        for i in 1..sampled_states.len() {
            assert!(
                sampled_states[i].timestamp_millis > sampled_states[i - 1].timestamp_millis,
                "Results should be in chronological order"
            );
        }
    }

    #[tokio::test]
    async fn test_get_perp_user_states_sampled_edge_cases() {
        let database = get_postgres_database().await;
        let wallet_address = format!("test_wallet_edge_{}", chrono::Utc::now().timestamp_millis());

        // Test with no data
        let start_timestamp_millis = get_test_start_timestamp_millis();
        let empty_result = database
            .get_perp_user_states_sampled(
                PerpExchange::Hyperliquid,
                "nonexistent_wallet_12345",
                start_timestamp_millis,
                start_timestamp_millis + (60 * 60 * 1000), // 1 hour later
                300,
            )
            .await
            .unwrap();

        assert_eq!(empty_result.len(), 0);

        // Generate minimal test data - just 2 records
        let user_states = generate_test_perp_user_states(
            &wallet_address,
            start_timestamp_millis,
            1800, // 30 minutes apart
            2,
            5000.0,
        );

        database.insert_perp_user_states(&user_states).await.unwrap();

        // Test with very large sampling interval (larger than data range)
        let end_timestamp_millis = start_timestamp_millis + (60 * 60 * 1000); // 1 hour later
        let large_interval_result = database
            .get_perp_user_states_sampled(
                PerpExchange::Hyperliquid,
                &wallet_address,
                start_timestamp_millis,
                end_timestamp_millis,
                7200, // 2 hours sampling (larger than data range)
            )
            .await
            .unwrap();

        println!("Large interval sampling returned {} states", large_interval_result.len());

        // Should return at least 1 sample (might get both if they fall in different buckets)
        assert!(
            large_interval_result.len() >= 1,
            "Expected at least 1 sample, got {}",
            large_interval_result.len()
        );
        assert!(
            large_interval_result.len() <= 2,
            "Expected at most 2 samples, got {}",
            large_interval_result.len()
        );
    }

    #[tokio::test]
    async fn test_get_latest_perp_user_state() {
        let database = get_postgres_database().await;
        let wallet_address =
            format!("test_wallet_latest_{}", chrono::Utc::now().timestamp_millis());

        // Generate test data with increasing timestamps
        let start_timestamp_millis = get_test_start_timestamp_millis();
        let user_states = generate_test_perp_user_states(
            &wallet_address,
            start_timestamp_millis,
            3600, // 1 hour apart
            5,
            25000.0,
        );

        database.insert_perp_user_states(&user_states).await.unwrap();

        // Get latest state
        let latest_state = database
            .get_latest_perp_user_state(PerpExchange::Hyperliquid, &wallet_address)
            .await
            .unwrap();

        assert!(latest_state.is_some());
        let latest = latest_state.unwrap();

        // Should be the last inserted record (highest timestamp)
        let expected_timestamp = start_timestamp_millis + (4 * 3600 * 1000); // 4 hours later
        println!(
            "Expected timestamp: {}, Actual timestamp: {}",
            expected_timestamp, latest.timestamp_millis
        );
        assert_eq!(latest.timestamp_millis, expected_timestamp);
        assert_eq!(latest.account_value, 25000.0 + 4.0 * 100.0); // Base + 4 * increment
    }

    #[tokio::test]
    async fn test_insert_or_update_perp_user_state() {
        let database = get_postgres_database().await;
        let wallet_address =
            format!("test_wallet_update_{}", chrono::Utc::now().timestamp_millis());
        let timestamp_millis = get_test_start_timestamp_millis();

        // Insert initial state
        let initial_state = PerpUserState::with_timestamp(
            PerpExchange::Hyperliquid,
            wallet_address.clone(),
            8000.0,
            10000.0,
            9000.0,
            7500.0,
            timestamp_millis,
        );

        database.insert_or_update_perp_user_state(&initial_state).await.unwrap();

        // Update the same record
        let updated_state = PerpUserState::with_timestamp(
            PerpExchange::Hyperliquid,
            wallet_address.clone(),
            12000.0,          // Updated value
            15000.0,          // Updated value
            13500.0,          // Updated value
            11250.0,          // Updated value
            timestamp_millis, // Same timestamp
        );

        database.insert_or_update_perp_user_state(&updated_state).await.unwrap();

        // Retrieve and verify the update
        let retrieved_states = database
            .get_perp_user_states(
                PerpExchange::Hyperliquid,
                &wallet_address,
                Some(timestamp_millis),
                Some(timestamp_millis),
                None,
            )
            .await
            .unwrap();

        assert_eq!(retrieved_states.len(), 1);
        assert_eq!(retrieved_states[0].account_value, 15000.0);
        assert_eq!(retrieved_states[0].perps_account_value, 12000.0);
    }
}
