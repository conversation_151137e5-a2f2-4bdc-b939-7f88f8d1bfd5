use bigdecimal::BigDecimal;
use serde::Serialize;

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, sqlx::FromRow)]
pub struct HypeTokenStatistic {
    pub id: String,

    pub is_native_token: bool,
    pub network: Option<String>,
    pub address: Option<String>,

    pub name: String, // fullname
    pub symbol: String,

    pub website: Option<String>,
    pub twitter: Option<String>,
    pub telegram: Option<String>,

    pub total_supply: BigDecimal,
    pub circulating_supply: BigDecimal,
    pub price_usd: f64,
    pub market_cap_usd: f64,
    pub fdv_usd: f64,
    pub liquidity_usd: f64,

    pub update_timestamp_millis: i64,
}
