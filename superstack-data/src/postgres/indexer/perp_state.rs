use serde::{Deserialize, Serialize};
use sqlx::Row;

use crate::{
    postgres::{PerpExchange, PostgresDatabase},
    Error,
};

#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult, Serialize, Deserialize, sqlx::FromRow)]
pub struct PerpState {
    pub perp_exchange: PerpExchange,
    pub perp_id: String,

    pub max_leverage: u8,
    pub only_isolated: bool,
    pub sz_decimals: u8,

    pub funding: f64,
    pub open_interest: f64,
    pub premium: Option<String>,
    pub oracle_px: f64,
    pub impact_pxs: Option<Vec<f64>>,
    pub day_base_vlm: f64,
    pub day_ntl_vlm: f64,

    pub mark_px: f64,
    pub mid_px: Option<f64>,
    pub prev_day_px: f64,
    pub market_cap: f64,
    pub liquidity: f64,
    pub fdv: f64,

    // HyperDash(Get from Redis)
    pub long_ntl: Option<f64>,
    pub short_ntl: Option<f64>,
    pub long_sz: Option<f64>,
    pub short_sz: Option<f64>,
    pub long_traders: Option<u64>,
    pub short_traders: Option<u64>,
    pub long_entry: Option<f64>,
    pub short_entry: Option<f64>,
    pub long_pnl: Option<f64>,
    pub short_pnl: Option<f64>,
    pub long_liq_dist: Option<f64>,
    pub short_liq_dist: Option<f64>,

    pub updated_at_millis: i64,
}

impl PerpState {
    pub fn new(perp_exchange: PerpExchange, perp_id: String) -> Self {
        let now = chrono::Utc::now().timestamp_millis();
        Self {
            perp_exchange,
            perp_id,
            max_leverage: 1,
            updated_at_millis: now,
            ..Default::default()
        }
    }
}

impl PostgresDatabase {
    pub async fn insert_or_update_perp_state(&self, perp_state: &PerpState) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO perp_state (
                perp_exchange, perp_id,
                max_leverage, only_isolated, sz_decimals,
                funding, open_interest, premium,
                oracle_px, impact_pxs,
                day_base_vlm, day_ntl_vlm,
                mark_px, mid_px, prev_day_px,
                market_cap, liquidity, fdv,
                updated_at_millis
            )
            VALUES (
                $1, $2,
                $3, $4, $5,
                $6, $7, $8,
                $9, $10,
                $11, $12,
                $13, $14, $15,
                $16, $17, $18,
                $19
            )
            ON CONFLICT (perp_exchange, perp_id) DO UPDATE SET
                max_leverage = EXCLUDED.max_leverage,
                only_isolated = EXCLUDED.only_isolated,
                sz_decimals = EXCLUDED.sz_decimals,
                funding = EXCLUDED.funding,
                open_interest = EXCLUDED.open_interest,
                premium = EXCLUDED.premium,
                oracle_px = EXCLUDED.oracle_px,
                impact_pxs = EXCLUDED.impact_pxs,
                day_base_vlm = EXCLUDED.day_base_vlm,
                day_ntl_vlm = EXCLUDED.day_ntl_vlm,
                mark_px = EXCLUDED.mark_px,
                mid_px = EXCLUDED.mid_px,
                prev_day_px = EXCLUDED.prev_day_px,
                market_cap = EXCLUDED.market_cap,
                liquidity = EXCLUDED.liquidity,
                fdv = EXCLUDED.fdv,
                updated_at_millis = EXCLUDED.updated_at_millis
            WHERE perp_state.updated_at_millis <= EXCLUDED.updated_at_millis
            "#,
            perp_state.perp_exchange as PerpExchange,
            perp_state.perp_id,
            perp_state.max_leverage as i16,
            perp_state.only_isolated,
            perp_state.sz_decimals as i16,
            perp_state.funding,
            perp_state.open_interest,
            perp_state.premium,
            perp_state.oracle_px,
            perp_state.impact_pxs.as_deref(),
            perp_state.day_base_vlm,
            perp_state.day_ntl_vlm,
            perp_state.mark_px,
            perp_state.mid_px,
            perp_state.prev_day_px,
            perp_state.market_cap,
            perp_state.liquidity,
            perp_state.fdv,
            perp_state.updated_at_millis,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_perp_state(
        &self,
        perp_exchange: PerpExchange,
        perp_id: &str,
    ) -> Result<Option<PerpState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                perp_exchange AS "perp_exchange: PerpExchange",
                perp_id,
                max_leverage,
                only_isolated,
                sz_decimals,
                funding,
                open_interest,
                premium,
                oracle_px,
                impact_pxs,
                day_base_vlm,
                day_ntl_vlm,
                mark_px,
                mid_px,
                prev_day_px,
                market_cap,
                liquidity,
                fdv,
                updated_at_millis
            FROM perp_state
            WHERE perp_exchange = $1 AND perp_id = $2
            "#,
            perp_exchange as PerpExchange,
            perp_id,
        )
        .fetch_optional(&self.pool)
        .await?;

        let perp_state = match row {
            Some(row) => Some(PerpState {
                perp_exchange: row.perp_exchange,
                perp_id: row.perp_id,
                max_leverage: row.max_leverage as u8,
                only_isolated: row.only_isolated,
                sz_decimals: row.sz_decimals as u8,
                funding: row.funding,
                open_interest: row.open_interest,
                premium: row.premium,
                oracle_px: row.oracle_px,
                impact_pxs: row.impact_pxs,
                day_base_vlm: row.day_base_vlm,
                day_ntl_vlm: row.day_ntl_vlm,
                mark_px: row.mark_px,
                mid_px: row.mid_px,
                prev_day_px: row.prev_day_px,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                fdv: row.fdv,
                updated_at_millis: row.updated_at_millis,
                ..Default::default()
            }),
            None => None,
        };

        Ok(perp_state)
    }

    pub async fn get_perp_states_by_exchange(
        &self,
        perp_exchange: PerpExchange,
    ) -> Result<Vec<PerpState>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                perp_exchange AS "perp_exchange: PerpExchange",
                perp_id,
                max_leverage,
                only_isolated,
                sz_decimals,
                funding,
                open_interest,
                premium,
                oracle_px,
                impact_pxs,
                day_base_vlm,
                day_ntl_vlm,
                mark_px,
                mid_px,
                prev_day_px,
                market_cap,
                liquidity,
                fdv,
                updated_at_millis
            FROM perp_state
            WHERE perp_exchange = $1
            ORDER BY market_cap DESC
            "#,
            perp_exchange as PerpExchange,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut perp_states = Vec::with_capacity(rows.len());
        for row in rows {
            perp_states.push(PerpState {
                perp_exchange: row.perp_exchange,
                perp_id: row.perp_id,
                max_leverage: row.max_leverage as u8,
                only_isolated: row.only_isolated,
                sz_decimals: row.sz_decimals as u8,
                funding: row.funding,
                open_interest: row.open_interest,
                premium: row.premium,
                oracle_px: row.oracle_px,
                impact_pxs: row.impact_pxs,
                day_base_vlm: row.day_base_vlm,
                day_ntl_vlm: row.day_ntl_vlm,
                mark_px: row.mark_px,
                mid_px: row.mid_px,
                prev_day_px: row.prev_day_px,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                fdv: row.fdv,
                updated_at_millis: row.updated_at_millis,
                ..Default::default()
            });
        }

        Ok(perp_states)
    }

    pub async fn insert_or_update_perp_states(
        &self,
        perp_states: &[PerpState],
    ) -> Result<(), Error> {
        if perp_states.is_empty() {
            return Ok(());
        }

        let mut tx = self.pool.begin().await?;

        for perp_state in perp_states {
            sqlx::query!(
                r#"
                INSERT INTO perp_state (
                    perp_exchange, perp_id,
                    max_leverage, only_isolated, sz_decimals,
                    funding, open_interest, premium,
                    oracle_px, impact_pxs,
                    day_base_vlm, day_ntl_vlm,
                    mark_px, mid_px, prev_day_px,
                    market_cap, liquidity, fdv,
                    updated_at_millis
                )
                VALUES (
                    $1, $2,
                    $3, $4, $5,
                    $6, $7, $8,
                    $9, $10,
                    $11, $12,
                    $13, $14, $15,
                    $16, $17, $18,
                    $19
                )
                ON CONFLICT (perp_exchange, perp_id) DO UPDATE SET
                    max_leverage = EXCLUDED.max_leverage,
                    only_isolated = EXCLUDED.only_isolated,
                    sz_decimals = EXCLUDED.sz_decimals,
                    funding = EXCLUDED.funding,
                    open_interest = EXCLUDED.open_interest,
                    premium = EXCLUDED.premium,
                    oracle_px = EXCLUDED.oracle_px,
                    impact_pxs = EXCLUDED.impact_pxs,
                    day_base_vlm = EXCLUDED.day_base_vlm,
                    day_ntl_vlm = EXCLUDED.day_ntl_vlm,
                    mark_px = EXCLUDED.mark_px,
                    mid_px = EXCLUDED.mid_px,
                    prev_day_px = EXCLUDED.prev_day_px,
                    market_cap = EXCLUDED.market_cap,
                    liquidity = EXCLUDED.liquidity,
                    fdv = EXCLUDED.fdv,
                    updated_at_millis = EXCLUDED.updated_at_millis
                WHERE perp_state.updated_at_millis <= EXCLUDED.updated_at_millis
                "#,
                perp_state.perp_exchange as PerpExchange,
                perp_state.perp_id,
                perp_state.max_leverage as i16,
                perp_state.only_isolated,
                perp_state.sz_decimals as i16,
                perp_state.funding,
                perp_state.open_interest,
                perp_state.premium,
                perp_state.oracle_px,
                perp_state.impact_pxs.as_deref(),
                perp_state.day_base_vlm,
                perp_state.day_ntl_vlm,
                perp_state.mark_px,
                perp_state.mid_px,
                perp_state.prev_day_px,
                perp_state.market_cap,
                perp_state.liquidity,
                perp_state.fdv,
                perp_state.updated_at_millis,
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;

        Ok(())
    }

    pub async fn get_filtered_and_sorted_perp_states(
        &self,
        // Range filters (min, max)
        volume_range: Option<(f64, f64)>,
        funding_range: Option<(f64, f64)>,
        open_interest_range: Option<(f64, f64)>,

        // Sort options
        sort_by: Option<&str>,
        sort_desc: bool,

        // Pagination
        limit: i64,
        offset: i64,
    ) -> Result<Vec<PerpState>, Error> {
        // Build dynamic WHERE clause and parameters
        let mut where_conditions = Vec::new();
        let mut param_count = 0;

        // Build WHERE clause dynamically
        if volume_range.is_some() {
            where_conditions.push(format!(
                "day_ntl_vlm >= ${} AND day_ntl_vlm <= ${}",
                param_count + 1,
                param_count + 2
            ));
            param_count += 2;
        }

        if funding_range.is_some() {
            where_conditions.push(format!(
                "funding >= ${} AND funding <= ${}",
                param_count + 1,
                param_count + 2
            ));
            param_count += 2;
        }

        if open_interest_range.is_some() {
            where_conditions.push(format!(
                "open_interest >= ${} AND open_interest <= ${}",
                param_count + 1,
                param_count + 2
            ));
            param_count += 2;
        }

        // Build WHERE clause
        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // Build ORDER BY clause
        let sort_column = match sort_by {
            Some("24h_volume") => "day_ntl_vlm",
            Some("funding") => "funding",
            Some("open_interest") => "open_interest",
            Some("mark_px") => "mark_px",
            _ => "market_cap", // Default sort by market cap
        };

        let sort_direction = if sort_desc { "DESC" } else { "ASC" };

        // Build complete query
        let query = format!(
            r#"
            SELECT
                *
            FROM perp_state
            {}
            ORDER BY {} {}
            LIMIT ${} OFFSET ${}
            "#,
            where_clause,
            sort_column,
            sort_direction,
            param_count + 1,
            param_count + 2
        );

        // Use sqlx::query to execute dynamic query
        let mut query_builder = sqlx::query(&query);

        // Add parameters in order
        if let Some((min_vol, max_vol)) = volume_range {
            query_builder = query_builder.bind(min_vol).bind(max_vol);
        }

        if let Some((min_funding, max_funding)) = funding_range {
            query_builder = query_builder.bind(min_funding).bind(max_funding);
        }

        if let Some((min_oi, max_oi)) = open_interest_range {
            query_builder = query_builder.bind(min_oi).bind(max_oi);
        }

        // Add pagination parameters
        query_builder = query_builder.bind(limit).bind(offset);

        let rows = query_builder.fetch_all(&self.pool).await?;

        // Map rows to PerpState
        let mut perp_states = Vec::with_capacity(rows.len());
        for row in rows {
            perp_states.push(PerpState {
                perp_exchange: row.try_get("perp_exchange")?,
                perp_id: row.try_get("perp_id")?,
                max_leverage: row.try_get("max_leverage").unwrap_or(1i16) as u8,
                only_isolated: row.try_get("only_isolated").unwrap_or(false),
                sz_decimals: row.try_get("sz_decimals").unwrap_or(0i16) as u8,
                funding: row.try_get("funding")?,
                open_interest: row.try_get("open_interest")?,
                premium: row.try_get("premium")?,
                oracle_px: row.try_get("oracle_px")?,
                impact_pxs: row.try_get("impact_pxs")?,
                day_base_vlm: row.try_get("day_base_vlm")?,
                day_ntl_vlm: row.try_get("day_ntl_vlm")?,
                mark_px: row.try_get("mark_px")?,
                mid_px: row.try_get("mid_px")?,
                prev_day_px: row.try_get("prev_day_px")?,
                market_cap: row.try_get("market_cap")?,
                liquidity: row.try_get("liquidity")?,
                fdv: row.try_get("fdv")?,
                updated_at_millis: row.try_get("updated_at_millis")?,
                ..Default::default()
            });
        }

        Ok(perp_states)
    }
}

#[derive(Debug, Clone, Default)]
pub struct PerpBriefState {
    pub perp_id: String,
    pub long_ntl: u128,
    pub short_ntl: u128,
    pub long_sz: f64,
    pub short_sz: f64,
    pub long_traders: u64,
    pub short_traders: u64,
    pub long_entry: f64,
    pub short_entry: f64,
    pub long_pnl: f64,
    pub short_pnl: f64,
    pub long_liq_dist: f64,
    pub short_liq_dist: f64,
    pub updated_at_millis: i64,
}

impl PerpBriefState {
    pub fn new(perp_id: String) -> Self {
        Self { perp_id, ..Default::default() }
    }
}
