use std::collections::HashMap;

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

use crate::{postgres::*, Error};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct PoolState {
    pub chain: Chain,
    pub pool_address: String,

    pub block_number: u64,
    pub timestamp_millis: i64,

    pub price: f64,
    pub market_cap: f64,
    pub liquidity: f64,

    pub total_volume: f64,
    pub total_buy_volume: f64,
    pub total_sell_volume: f64,

    pub total_txns: u64,
    pub total_buy_txns: u64,
    pub total_sell_txns: u64,

    pub bonding_curve_progress: Option<f64>,
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct PoolStateDelta {
    pub block_number: u64,
    pub timestamp_millis: i64,

    pub price: Option<f64>,
    pub market_cap: Option<f64>,
    pub liquidity: f64,

    pub delta_volume: f64,
    pub delta_buy_volume: f64,
    pub delta_sell_volume: f64,
    pub delta_txns: u64,
    pub delta_buy_txns: u64,
    pub delta_sell_txns: u64,

    pub bonding_curve_progress: Option<f64>,
}

impl PoolState {
    pub fn new_from_delta(
        chain: Chain,
        pool_address: String,
        delta: PoolStateDelta,
    ) -> anyhow::Result<Self> {
        if delta.price.is_none() || delta.market_cap.is_none() {
            return Err(anyhow::anyhow!("Price and market cap cannot be None"));
        }

        Ok(Self {
            chain,
            pool_address,
            block_number: delta.block_number,
            timestamp_millis: delta.timestamp_millis,
            price: delta.price.unwrap_or_default(),
            market_cap: delta.market_cap.unwrap_or_default(),
            liquidity: delta.liquidity,
            total_volume: delta.delta_volume,
            total_buy_volume: delta.delta_buy_volume,
            total_sell_volume: delta.delta_sell_volume,
            total_txns: delta.delta_txns,
            total_buy_txns: delta.delta_buy_txns,
            total_sell_txns: delta.delta_sell_txns,
            bonding_curve_progress: delta.bonding_curve_progress,
        })
    }

    pub fn new(
        chain: Chain,
        pool_address: String,
        block_number: u64,
        timestamp_millis: i64,
    ) -> Self {
        Self {
            chain,
            pool_address,
            block_number,
            timestamp_millis,
            price: 0.0,
            market_cap: 0.0,
            liquidity: 0.0,
            total_volume: 0.0,
            total_buy_volume: 0.0,
            total_sell_volume: 0.0,
            total_txns: 0,
            total_buy_txns: 0,
            total_sell_txns: 0,
            bonding_curve_progress: None,
        }
    }

    pub fn new_solana_pool_state(
        pool_address: Pubkey,
        block_number: u64,
        timestamp_millis: i64,
    ) -> Self {
        Self::new(Chain::Solana, pool_address.to_string(), block_number, timestamp_millis)
    }

    pub fn new_hypercore_pool_state(
        pool_address: String,
        block_number: u64,
        timestamp_millis: i64,
    ) -> Self {
        Self::new(Chain::Hypercore, pool_address, block_number, timestamp_millis)
    }

    pub fn new_hyper_evm_pool_state(
        pool_address: String,
        block_number: u64,
        timestamp_millis: i64,
    ) -> Self {
        Self::new(Chain::HyperEvm, pool_address, block_number, timestamp_millis)
    }

    pub fn merge(&mut self, delta: &PoolStateDelta) {
        if delta.block_number >= self.block_number {
            self.block_number = delta.block_number;
            self.timestamp_millis = delta.timestamp_millis;

            self.liquidity = delta.liquidity;
            self.bonding_curve_progress = delta.bonding_curve_progress;

            if let Some(price) = delta.price {
                self.price = price;
            }
            if let Some(market_cap) = delta.market_cap {
                self.market_cap = market_cap;
            }
        }

        self.total_volume += delta.delta_volume;
        self.total_buy_volume += delta.delta_buy_volume;
        self.total_sell_volume += delta.delta_sell_volume;
        self.total_txns += delta.delta_txns;
        self.total_buy_txns += delta.delta_buy_txns;
        self.total_sell_txns += delta.delta_sell_txns;
    }

    pub fn update_bonding_curve_progress(&mut self, bonding_curve_progress: f64) {
        self.bonding_curve_progress = Some(bonding_curve_progress);
    }

    pub fn is_newer_than(&self, other: &Self) -> bool {
        if self.block_number > other.block_number {
            return true;
        } else if self.block_number < other.block_number {
            return false;
        }

        if self.total_txns > other.total_txns {
            return true;
        } else if self.total_txns < other.total_txns {
            return false;
        }

        match (self.bonding_curve_progress, other.bonding_curve_progress) {
            (Some(self_bc), Some(other_bc)) => {
                if self_bc > other_bc {
                    return true;
                }
            }
            (_, _) => {}
        }

        return false;
    }
}

impl PostgresDatabase {
    pub async fn insert_pool_state(&self, pool_state: &PoolState) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO pool_state (
                chain, pool_address,
                block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress
            )
            VALUES (
                $1, $2,
                $3, $4,
                $5, $6, $7,
                $8, $9, $10,
                $11, $12, $13,
                $14
            )
            ON CONFLICT (chain, pool_address, block_number) DO NOTHING
            "#,
            pool_state.chain as Chain,
            pool_state.pool_address,
            pool_state.block_number as i64,
            pool_state.timestamp_millis,
            pool_state.price,
            pool_state.market_cap,
            pool_state.liquidity,
            pool_state.total_volume,
            pool_state.total_buy_volume,
            pool_state.total_sell_volume,
            pool_state.total_txns as i64,
            pool_state.total_buy_txns as i64,
            pool_state.total_sell_txns as i64,
            pool_state.bonding_curve_progress.map(|b| b as f64),
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn insert_or_update_pool_state(&self, pool_state: &PoolState) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO pool_state (
                chain, pool_address, block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress
            )
            VALUES (
                $1, $2, $3, $4,
                $5, $6, $7,
                $8, $9, $10,
                $11, $12, $13,
                $14
            )
            ON CONFLICT (chain, pool_address, block_number) DO UPDATE SET
                price = EXCLUDED.price,
                market_cap = EXCLUDED.market_cap,
                liquidity = EXCLUDED.liquidity,
                total_volume = EXCLUDED.total_volume,
                total_buy_volume = EXCLUDED.total_buy_volume,
                total_sell_volume = EXCLUDED.total_sell_volume,
                total_txns = EXCLUDED.total_txns,
                total_buy_txns = EXCLUDED.total_buy_txns,
                total_sell_txns = EXCLUDED.total_sell_txns,
                bonding_curve_progress = EXCLUDED.bonding_curve_progress
            WHERE pool_state.total_txns < EXCLUDED.total_txns
            "#,
            pool_state.chain as Chain,
            pool_state.pool_address,
            pool_state.block_number as i64,
            pool_state.timestamp_millis,
            pool_state.price,
            pool_state.market_cap,
            pool_state.liquidity,
            pool_state.total_volume,
            pool_state.total_buy_volume,
            pool_state.total_sell_volume,
            pool_state.total_txns as i64,
            pool_state.total_buy_txns as i64,
            pool_state.total_sell_txns as i64,
            pool_state.bonding_curve_progress.map(|b| b as f64),
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_latest_pool_state_before_block_number(
        &self,
        chain: Chain,
        pool_address: &str,
        block_number: u64,
    ) -> Result<Option<PoolState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                pool_address,
                block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress
            FROM pool_state
            WHERE chain = $1 AND pool_address = $2 AND block_number <= $3
            ORDER BY block_number DESC
            LIMIT 1
            "#,
            chain as Chain,
            pool_address,
            block_number as i64,
        )
        .fetch_optional(&self.pool)
        .await?;

        let pool_state = match row {
            Some(row) => Some(PoolState {
                chain: row.chain,
                pool_address: row.pool_address,
                block_number: row.block_number as u64,
                timestamp_millis: row.timestamp_millis,
                price: row.price,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                total_volume: row.total_volume,
                total_buy_volume: row.total_buy_volume,
                total_sell_volume: row.total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
            }),
            None => None,
        };

        Ok(pool_state)
    }

    pub async fn get_latest_pool_state_before_timestamp_millis(
        &self,
        chain: Chain,
        pool_address: &str,
        timestamp_millis: i64,
    ) -> Result<Option<PoolState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                pool_address,
                block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress
            FROM pool_state
            WHERE chain = $1 AND pool_address = $2 AND timestamp_millis <= $3
            ORDER BY timestamp_millis DESC
            LIMIT 1
            "#,
            chain as Chain,
            pool_address,
            timestamp_millis,
        )
        .fetch_optional(&self.pool)
        .await?;

        let pool_state = match row {
            Some(row) => Some(PoolState {
                chain: row.chain,
                pool_address: row.pool_address,
                block_number: row.block_number as u64,
                timestamp_millis: row.timestamp_millis,
                price: row.price,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                total_volume: row.total_volume,
                total_buy_volume: row.total_buy_volume,
                total_sell_volume: row.total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
            }),
            None => None,
        };

        Ok(pool_state)
    }

    pub async fn get_latest_pool_states_from_multiple_ranges(
        &self,
        chain: Chain,
        pool_address: &str,
        timestamp_millis: &[i64],
    ) -> Result<HashMap<i64, PoolState>, Error> {
        if timestamp_millis.is_empty() {
            return Ok(HashMap::new());
        }

        let rows = sqlx::query!(
            r#"
            SELECT
                ps.chain AS "chain: Chain",
                ps.pool_address,
                ps.block_number,
                ps.timestamp_millis,
                ps.price,
                ps.market_cap,
                ps.liquidity,
                ps.total_volume,
                ps.total_buy_volume,
                ps.total_sell_volume,
                ps.total_txns,
                ps.total_buy_txns,
                ps.total_sell_txns,
                ps.bonding_curve_progress,
                tt.target_timestamp
            FROM unnest($1::bigint[]) AS tt(target_timestamp)
            LEFT JOIN LATERAL (
                SELECT *
                FROM pool_state
                WHERE chain = $2
                  AND pool_address = $3
                  AND timestamp_millis <= tt.target_timestamp
                ORDER BY block_number DESC
                LIMIT 1
            ) ps ON true
            ORDER BY tt.target_timestamp
            "#,
            timestamp_millis as &[i64],
            chain as Chain,
            pool_address,
        )
        .fetch_all(&self.pool)
        .await?;

        // Create a map of target_timestamp -> PoolState
        let mut result_map = std::collections::HashMap::new();
        for row in rows {
            let pool_state = PoolState {
                chain: row.chain,
                pool_address: row.pool_address,
                block_number: row.block_number as u64,
                timestamp_millis: row.timestamp_millis,
                price: row.price,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                total_volume: row.total_volume,
                total_buy_volume: row.total_buy_volume,
                total_sell_volume: row.total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress,
            };
            result_map.insert(
                row.target_timestamp.ok_or(anyhow::anyhow!("target_timestamp is None"))?,
                pool_state,
            );
        }

        Ok(result_map)
    }

    pub async fn get_first_pool_state(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                pool_address,
                block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress
            FROM pool_state
            WHERE chain = $1 AND pool_address = $2
            ORDER BY block_number ASC
            LIMIT 1
            "#,
            chain as Chain,
            pool_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let pool_state = match row {
            Some(row) => Some(PoolState {
                chain: row.chain,
                pool_address: row.pool_address,
                block_number: row.block_number as u64,
                timestamp_millis: row.timestamp_millis,
                price: row.price,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                total_volume: row.total_volume,
                total_buy_volume: row.total_buy_volume,
                total_sell_volume: row.total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
            }),
            None => None,
        };

        Ok(pool_state)
    }

    pub async fn get_first_non_zero_pool_state(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                pool_address,
                block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress
            FROM pool_state
            WHERE chain = $1 AND pool_address = $2 AND price > 0
            ORDER BY block_number ASC
            LIMIT 1
            "#,
            chain as Chain,
            pool_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let pool_state = match row {
            Some(row) => Some(PoolState {
                chain: row.chain,
                pool_address: row.pool_address,
                block_number: row.block_number as u64,
                timestamp_millis: row.timestamp_millis,
                price: row.price,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                total_volume: row.total_volume,
                total_buy_volume: row.total_buy_volume,
                total_sell_volume: row.total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
            }),
            None => None,
        };

        Ok(pool_state)
    }

    pub async fn get_latest_pool_state(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolState>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                pool_address,
                block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress
            FROM pool_state
            WHERE chain = $1 AND pool_address = $2
            ORDER BY block_number DESC
            LIMIT 1
            "#,
            chain as Chain,
            pool_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let pool_state = match row {
            Some(row) => Some(PoolState {
                chain: row.chain,
                pool_address: row.pool_address,
                block_number: row.block_number as u64,
                timestamp_millis: row.timestamp_millis,
                price: row.price,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                total_volume: row.total_volume,
                total_buy_volume: row.total_buy_volume,
                total_sell_volume: row.total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress.map(|b| b as f64),
            }),
            None => None,
        };

        Ok(pool_state)
    }

    pub async fn insert_pool_states(&self, pool_states: &[PoolState]) -> Result<(), Error> {
        let len = pool_states.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_pool_state(&pool_states[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = pool_states.iter().map(|p| p.chain).collect();
        let pool_addresses: Vec<String> =
            pool_states.iter().map(|p| p.pool_address.clone()).collect();
        let block_numbers: Vec<i64> = pool_states.iter().map(|p| p.block_number as i64).collect();
        let timestamp_millis: Vec<i64> = pool_states.iter().map(|p| p.timestamp_millis).collect();
        let prices: Vec<f64> = pool_states.iter().map(|p| p.price).collect();
        let market_caps: Vec<f64> = pool_states.iter().map(|p| p.market_cap).collect();
        let liquidity: Vec<f64> = pool_states.iter().map(|p| p.liquidity).collect();
        let total_volumes: Vec<f64> = pool_states.iter().map(|p| p.total_volume).collect();
        let total_buy_volumes: Vec<f64> = pool_states.iter().map(|p| p.total_buy_volume).collect();
        let total_sell_volumes: Vec<f64> =
            pool_states.iter().map(|p| p.total_sell_volume).collect();
        let total_txns: Vec<i64> = pool_states.iter().map(|p| p.total_txns as i64).collect();
        let total_buy_txns: Vec<i64> =
            pool_states.iter().map(|p| p.total_buy_txns as i64).collect();
        let total_sell_txns: Vec<i64> =
            pool_states.iter().map(|p| p.total_sell_txns as i64).collect();
        let bonding_curve_progresses: Vec<Option<f64>> =
            pool_states.iter().map(|p| p.bonding_curve_progress).collect();

        sqlx::query!(
            r#"
            INSERT INTO pool_state (
                chain, pool_address, block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress)
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[], $3::bigint[], $4::bigint[],
                $5::double precision[], $6::double precision[], $7::double precision[],
                $8::double precision[], $9::double precision[], $10::double precision[],
                $11::bigint[], $12::bigint[], $13::bigint[],
                $14::double precision[])
            ON CONFLICT (chain, pool_address, block_number) DO NOTHING
            "#,
            &chains as &[Chain],
            &pool_addresses as &[String],
            &block_numbers as &[i64],
            &timestamp_millis as &[i64],
            &prices as &[f64],
            &market_caps as &[f64],
            &liquidity as &[f64],
            &total_volumes as &[f64],
            &total_buy_volumes as &[f64],
            &total_sell_volumes as &[f64],
            &total_txns as &[i64],
            &total_buy_txns as &[i64],
            &total_sell_txns as &[i64],
            &bonding_curve_progresses as &[Option<f64>],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn insert_or_update_pool_states(
        &self,
        pool_states: &[PoolState],
    ) -> Result<(), Error> {
        let len = pool_states.len();
        if len == 0 {
            return Ok(());
        } else if len == 1 {
            return self.insert_or_update_pool_state(&pool_states[0]).await;
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = pool_states.iter().map(|p| p.chain).collect();
        let pool_addresses: Vec<String> =
            pool_states.iter().map(|p| p.pool_address.clone()).collect();
        let block_numbers: Vec<i64> = pool_states.iter().map(|p| p.block_number as i64).collect();
        let timestamp_millis: Vec<i64> = pool_states.iter().map(|p| p.timestamp_millis).collect();
        let prices: Vec<f64> = pool_states.iter().map(|p| p.price).collect();
        let market_caps: Vec<f64> = pool_states.iter().map(|p| p.market_cap).collect();
        let liquidity: Vec<f64> = pool_states.iter().map(|p| p.liquidity).collect();
        let total_volumes: Vec<f64> = pool_states.iter().map(|p| p.total_volume).collect();
        let total_buy_volumes: Vec<f64> = pool_states.iter().map(|p| p.total_buy_volume).collect();
        let total_sell_volumes: Vec<f64> =
            pool_states.iter().map(|p| p.total_sell_volume).collect();
        let total_txns: Vec<i64> = pool_states.iter().map(|p| p.total_txns as i64).collect();
        let total_buy_txns: Vec<i64> =
            pool_states.iter().map(|p| p.total_buy_txns as i64).collect();
        let total_sell_txns: Vec<i64> =
            pool_states.iter().map(|p| p.total_sell_txns as i64).collect();
        let bonding_curve_progresses: Vec<Option<f64>> =
            pool_states.iter().map(|p| p.bonding_curve_progress).collect();

        sqlx::query!(
            r#"
            INSERT INTO pool_state (
                chain, pool_address, block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress)
            SELECT * FROM unnest(
                $1::chain_enum[], $2::varchar[], $3::bigint[], $4::bigint[],
                $5::double precision[], $6::double precision[], $7::double precision[],
                $8::double precision[], $9::double precision[], $10::double precision[],
                $11::bigint[], $12::bigint[], $13::bigint[],
                $14::double precision[])
            ON CONFLICT (chain, pool_address, block_number) DO UPDATE SET
                price = EXCLUDED.price,
                market_cap = EXCLUDED.market_cap,
                liquidity = EXCLUDED.liquidity,
                total_volume = EXCLUDED.total_volume,
                total_buy_volume = EXCLUDED.total_buy_volume,
                total_sell_volume = EXCLUDED.total_sell_volume,
                total_txns = EXCLUDED.total_txns,
                total_buy_txns = EXCLUDED.total_buy_txns,
                total_sell_txns = EXCLUDED.total_sell_txns,
                bonding_curve_progress = EXCLUDED.bonding_curve_progress
            WHERE pool_state.total_txns < EXCLUDED.total_txns
            "#,
            &chains as &[Chain],
            &pool_addresses as &[String],
            &block_numbers as &[i64],
            &timestamp_millis as &[i64],
            &prices as &[f64],
            &market_caps as &[f64],
            &liquidity as &[f64],
            &total_volumes as &[f64],
            &total_buy_volumes as &[f64],
            &total_sell_volumes as &[f64],
            &total_txns as &[i64],
            &total_buy_txns as &[i64],
            &total_sell_txns as &[i64],
            &bonding_curve_progresses as &[Option<f64>],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Get pool states within a time range for fallback candle generation
    pub async fn get_pool_states_in_time_range(
        &self,
        chain: Chain,
        pool_address: &str,
        start_timestamp_millis: i64,
        end_timestamp_millis: i64,
    ) -> Result<Vec<PoolState>, Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                pool_address,
                block_number, timestamp_millis,
                price, market_cap, liquidity,
                total_volume, total_buy_volume, total_sell_volume,
                total_txns, total_buy_txns, total_sell_txns,
                bonding_curve_progress
            FROM pool_state
            WHERE chain = $1 AND pool_address = $2
                AND timestamp_millis >= $3 AND timestamp_millis <= $4
            ORDER BY timestamp_millis ASC
            "#,
            chain as Chain,
            pool_address,
            start_timestamp_millis,
            end_timestamp_millis,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut pool_states = Vec::with_capacity(rows.len());
        for row in rows {
            pool_states.push(PoolState {
                chain: row.chain,
                pool_address: row.pool_address,
                block_number: row.block_number as u64,
                timestamp_millis: row.timestamp_millis,
                price: row.price,
                market_cap: row.market_cap,
                liquidity: row.liquidity,
                total_volume: row.total_volume,
                total_buy_volume: row.total_buy_volume,
                total_sell_volume: row.total_sell_volume,
                total_txns: row.total_txns as u64,
                total_buy_txns: row.total_buy_txns as u64,
                total_sell_txns: row.total_sell_txns as u64,
                bonding_curve_progress: row.bonding_curve_progress,
            });
        }

        Ok(pool_states)
    }
}
