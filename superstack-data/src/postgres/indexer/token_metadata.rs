use alloy::primitives::U256;
use serde::{Deserialize, Serialize};
use solana_sdk::{pubkey::Pubkey, signature::Signature};

use sqlx::types::BigDecimal;

use crate::{
    constant::solana::{USDC_MINT, USDT_MINT},
    postgres::*,
    token::solana::metadata::fetch_token_metadata_and_mint_info,
    utils::get_confirmed_rpc_client,
    Error,
};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow, PartialEq, Eq)]
pub struct TokenMetadata {
    pub chain: Chain,
    pub address: String,

    pub name: String,
    pub symbol: String,

    pub decimals: u8,
    pub supply: BigDecimal,

    // Additional info
    pub description: Option<String>,
    pub image: Option<String>,
    pub website: Option<String>,
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub dex_paid: DexPaid,

    pub is_trench_token: bool,

    // Creation info
    pub create_dex: Dex,
    pub create_block_number: Option<u64>,
    pub create_tx_hash: Option<String>,
    pub create_bonding_curve: Option<String>,
    pub create_dev: Option<String>,
    pub create_timestamp_millis: i64,

    // Migration info
    pub migration_pool_address: Option<String>,
    pub migration_timestamp_millis: i64,

    pub update_timestamp_millis: i64,

    // Solana specific info
    pub uri: Option<String>,
    pub seller_fee_basis_points: Option<u16>,
    pub creators: Option<Vec<String>>,
    pub primary_sale_happened: Option<bool>,
    pub is_mutable: Option<bool>,
    pub update_authority: Option<String>,
    pub mint_authority: Option<String>,
    pub freeze_authority: Option<String>,

    // New fields
    pub is_active: bool,
    pub image_path: Option<String>,
}

// Old version of TokenMetadata
// Used to be compatible with the old message format
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct TokenMetadataV1 {
    pub chain: Chain,
    pub address: String,

    pub name: String,
    pub symbol: String,

    pub decimals: u8,
    pub supply: BigDecimal,

    // Additional info
    pub description: Option<String>,
    pub image: Option<String>,
    pub website: Option<String>,
    pub twitter: Option<String>,
    pub telegram: Option<String>,
    pub dex_paid: DexPaid,

    pub is_trench_token: bool,

    // Creation info
    pub create_dex: Dex,
    pub create_block_number: Option<u64>,
    pub create_tx_hash: Option<String>,
    pub create_bonding_curve: Option<String>,
    pub create_dev: Option<String>,
    pub create_timestamp_millis: i64,

    // Migration info
    pub migration_pool_address: Option<String>,
    pub migration_timestamp_millis: i64,

    pub update_timestamp_millis: i64,

    // Solana specific info
    pub uri: Option<String>,
    pub seller_fee_basis_points: Option<u16>,
    pub creators: Option<Vec<String>>,
    pub primary_sale_happened: Option<bool>,
    pub is_mutable: Option<bool>,
    pub update_authority: Option<String>,
    pub mint_authority: Option<String>,
    pub freeze_authority: Option<String>,
}

impl From<TokenMetadataV1> for TokenMetadata {
    fn from(v1: TokenMetadataV1) -> Self {
        Self {
            chain: v1.chain,
            address: v1.address,
            name: v1.name,
            symbol: v1.symbol,
            decimals: v1.decimals,
            supply: v1.supply,
            description: v1.description,
            image: v1.image,
            website: v1.website,
            twitter: v1.twitter,
            telegram: v1.telegram,
            dex_paid: v1.dex_paid,
            is_trench_token: v1.is_trench_token,
            create_dex: v1.create_dex,
            create_block_number: v1.create_block_number,
            create_tx_hash: v1.create_tx_hash,
            create_bonding_curve: v1.create_bonding_curve,
            create_dev: v1.create_dev,
            create_timestamp_millis: v1.create_timestamp_millis,
            migration_pool_address: v1.migration_pool_address,
            migration_timestamp_millis: v1.migration_timestamp_millis,
            update_timestamp_millis: v1.update_timestamp_millis,
            uri: v1.uri,
            seller_fee_basis_points: v1.seller_fee_basis_points,
            creators: v1.creators,
            primary_sale_happened: v1.primary_sale_happened,
            is_mutable: v1.is_mutable,
            update_authority: v1.update_authority,
            mint_authority: v1.mint_authority,
            freeze_authority: v1.freeze_authority,
            is_active: true,
            image_path: None,
        }
    }
}

impl TokenMetadata {
    pub async fn construct_solana_token_metadata_from_rpc(
        address: Pubkey,
        create_dex: Dex,
        create_block_number: Option<u64>,
        create_tx_hash: Option<Signature>,
        create_bonding_curve: Option<Pubkey>,
        create_dev: Option<Pubkey>,
        create_timestamp_millis: Option<i64>,
        migration_pool_address: Option<Pubkey>,
        migration_timestamp_millis: Option<i64>,
        update_timestamp_millis: i64,
        max_retries: Option<u8>,
    ) -> Result<Self, Error> {
        let rpc = get_confirmed_rpc_client();
        let (metadata_info, mint_info) =
            fetch_token_metadata_and_mint_info(address, &rpc, max_retries).await?;

        let is_trench_token = create_bonding_curve.is_some() || migration_pool_address.is_some();

        let first_creator = metadata_info
            .creators
            .as_ref()
            .and_then(|creators| creators.first().map(|c| c.address));
        let create_dev = create_dev.or(first_creator);

        let token_metadata = Self {
            chain: Chain::Solana,
            address: address.to_string(),
            name: metadata_info.name,
            symbol: metadata_info.symbol,
            decimals: mint_info.decimals,
            supply: mint_info.supply.into(),
            description: None,
            image: None,
            website: None,
            twitter: None,
            telegram: None,
            dex_paid: DexPaid::Unknown,
            is_trench_token,
            create_dex,
            create_block_number,
            create_tx_hash: create_tx_hash.map(|s| s.to_string()),
            create_bonding_curve: create_bonding_curve.map(|b| b.to_string()),
            create_dev: create_dev.map(|d| d.to_string()),
            create_timestamp_millis: create_timestamp_millis.unwrap_or(0),
            migration_pool_address: migration_pool_address.map(|p| p.to_string()),
            migration_timestamp_millis: migration_timestamp_millis.unwrap_or(0),
            update_timestamp_millis,
            uri: Some(metadata_info.uri),
            seller_fee_basis_points: Some(metadata_info.seller_fee_basis_points),
            creators: metadata_info
                .creators
                .map(|v| v.iter().map(|v| v.address.to_string()).collect::<Vec<String>>()),
            primary_sale_happened: Some(metadata_info.primary_sale_happened),
            is_mutable: Some(metadata_info.is_mutable),
            update_authority: Some(metadata_info.update_authority.to_string()),
            mint_authority: mint_info.mint_authority.map(|p| p.to_string()).into(),
            freeze_authority: mint_info.freeze_authority.map(|p| p.to_string()).into(),
            is_active: true,
            image_path: None,
        };
        Ok(token_metadata)
    }

    pub fn supply_to_u256(&self) -> Result<U256, Error> {
        crate::utils::big_decimal_to_u256(self.supply.clone())
    }

    pub fn is_stable_coin(&self) -> bool {
        match self.chain {
            Chain::Solana => match self.address.as_str() {
                USDC_MINT | USDT_MINT => true,
                _ => false,
            },
            _ => false,
        }
    }

    pub fn is_missing_misc_info(&self) -> bool {
        self.description.is_none() ||
            self.image.is_none() ||
            self.website.is_none() ||
            self.twitter.is_none() ||
            self.telegram.is_none() ||
            self.image_path.is_none()
    }
}

impl PostgresDatabase {
    pub async fn insert_token_metadata(
        &self,
        token_metadata: &TokenMetadata,
    ) -> Result<bool, Error> {
        let result = sqlx::query!(
            r#"
            INSERT INTO token_metadata (
                chain, address,
                name, symbol,
                decimals, supply,
                description, image,
                website, twitter, telegram,
                dex_paid,
                is_trench_token,
                create_dex, create_block_number, create_tx_hash,
                create_bonding_curve, create_dev,
                create_timestamp_millis,
                migration_pool_address, migration_timestamp_millis,
                update_timestamp_millis,
                uri, seller_fee_basis_points,
                creators, primary_sale_happened,
                is_mutable,
                update_authority, mint_authority, freeze_authority,
                is_active, image_path
            )
            VALUES (
                $1, $2,
                $3, $4,
                $5, $6,
                $7, $8,
                $9, $10, $11,
                $12,
                $13,
                $14, $15, $16,
                $17, $18,
                $19,
                $20, $21,
                $22,
                $23, $24,
                $25, $26,
                $27,
                $28, $29, $30,
                $31, $32
            )
            ON CONFLICT (chain, address) DO NOTHING
            "#,
            token_metadata.chain as Chain,
            token_metadata.address,
            token_metadata.name,
            token_metadata.symbol,
            token_metadata.decimals as i16,
            token_metadata.supply,
            token_metadata.description,
            token_metadata.image,
            token_metadata.website,
            token_metadata.twitter,
            token_metadata.telegram,
            token_metadata.dex_paid as DexPaid,
            token_metadata.is_trench_token,
            token_metadata.create_dex as Dex,
            token_metadata.create_block_number.map(|b| b as i64),
            token_metadata.create_tx_hash,
            token_metadata.create_bonding_curve,
            token_metadata.create_dev,
            token_metadata.create_timestamp_millis,
            token_metadata.migration_pool_address,
            token_metadata.migration_timestamp_millis,
            token_metadata.update_timestamp_millis,
            token_metadata.uri,
            token_metadata.seller_fee_basis_points.map(|s| s as i16),
            token_metadata.creators.as_deref(),
            token_metadata.primary_sale_happened,
            token_metadata.is_mutable,
            token_metadata.update_authority,
            token_metadata.mint_authority,
            token_metadata.freeze_authority,
            token_metadata.is_active,
            token_metadata.image_path,
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn insert_token_metadata_or_update_create(
        &self,
        token_metadata: &TokenMetadata,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO token_metadata (
                chain, address,
                name, symbol,
                decimals, supply,
                description, image,
                website, twitter, telegram,
                dex_paid,
                is_trench_token,
                create_dex, create_block_number, create_tx_hash,
                create_bonding_curve, create_dev,
                create_timestamp_millis,
                migration_pool_address, migration_timestamp_millis,
                update_timestamp_millis,
                uri, seller_fee_basis_points,
                creators, primary_sale_happened,
                is_mutable,
                update_authority, mint_authority, freeze_authority,
                is_active, image_path
            )
            VALUES (
                $1, $2,
                $3, $4,
                $5, $6,
                $7, $8,
                $9, $10, $11,
                $12,
                $13,
                $14, $15, $16,
                $17, $18,
                $19,
                $20, $21,
                $22,
                $23, $24,
                $25, $26,
                $27,
                $28, $29, $30,
                $31, $32
            )
            ON CONFLICT (chain, address) DO UPDATE SET
                create_dex = EXCLUDED.create_dex,
                create_block_number = EXCLUDED.create_block_number,
                create_tx_hash = EXCLUDED.create_tx_hash,
                create_bonding_curve = EXCLUDED.create_bonding_curve,
                create_dev = EXCLUDED.create_dev,
                create_timestamp_millis = EXCLUDED.create_timestamp_millis
            WHERE token_metadata.create_timestamp_millis < EXCLUDED.create_timestamp_millis
            "#,
            token_metadata.chain as Chain,
            token_metadata.address,
            token_metadata.name,
            token_metadata.symbol,
            token_metadata.decimals as i16,
            token_metadata.supply,
            token_metadata.description,
            token_metadata.image,
            token_metadata.website,
            token_metadata.twitter,
            token_metadata.telegram,
            token_metadata.dex_paid as DexPaid,
            token_metadata.is_trench_token,
            token_metadata.create_dex as Dex,
            token_metadata.create_block_number.map(|b| b as i64),
            token_metadata.create_tx_hash,
            token_metadata.create_bonding_curve,
            token_metadata.create_dev,
            token_metadata.create_timestamp_millis,
            token_metadata.migration_pool_address,
            token_metadata.migration_timestamp_millis,
            token_metadata.update_timestamp_millis,
            token_metadata.uri,
            token_metadata.seller_fee_basis_points.map(|s| s as i16),
            token_metadata.creators.as_deref(),
            token_metadata.primary_sale_happened,
            token_metadata.is_mutable,
            token_metadata.update_authority,
            token_metadata.mint_authority,
            token_metadata.freeze_authority,
            token_metadata.is_active,
            token_metadata.image_path,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn insert_token_metadata_or_update_migration(
        &self,
        token_metadata: &TokenMetadata,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO token_metadata (
                chain, address,
                name, symbol,
                decimals, supply,
                description, image,
                website, twitter, telegram,
                dex_paid,
                is_trench_token,
                create_dex, create_block_number, create_tx_hash,
                create_bonding_curve, create_dev,
                create_timestamp_millis,
                migration_pool_address, migration_timestamp_millis,
                update_timestamp_millis,
                uri, seller_fee_basis_points,
                creators, primary_sale_happened,
                is_mutable,
                update_authority, mint_authority, freeze_authority,
                is_active, image_path
            )
            VALUES (
                $1, $2,
                $3, $4,
                $5, $6,
                $7, $8,
                $9, $10, $11,
                $12,
                $13,
                $14, $15, $16,
                $17, $18,
                $19,
                $20, $21,
                $22,
                $23, $24,
                $25, $26,
                $27,
                $28, $29, $30,
                $31, $32
            )
            ON CONFLICT (chain, address) DO UPDATE SET
                migration_pool_address = EXCLUDED.migration_pool_address,
                migration_timestamp_millis = EXCLUDED.migration_timestamp_millis
            WHERE token_metadata.migration_timestamp_millis < EXCLUDED.migration_timestamp_millis
            "#,
            token_metadata.chain as Chain,
            token_metadata.address,
            token_metadata.name,
            token_metadata.symbol,
            token_metadata.decimals as i16,
            token_metadata.supply,
            token_metadata.description,
            token_metadata.image,
            token_metadata.website,
            token_metadata.twitter,
            token_metadata.telegram,
            token_metadata.dex_paid as DexPaid,
            token_metadata.is_trench_token,
            token_metadata.create_dex as Dex,
            token_metadata.create_block_number.map(|b| b as i64),
            token_metadata.create_tx_hash,
            token_metadata.create_bonding_curve,
            token_metadata.create_dev,
            token_metadata.create_timestamp_millis,
            token_metadata.migration_pool_address,
            token_metadata.migration_timestamp_millis,
            token_metadata.update_timestamp_millis,
            token_metadata.uri,
            token_metadata.seller_fee_basis_points.map(|s| s as i16),
            token_metadata.creators.as_deref(),
            token_metadata.primary_sale_happened,
            token_metadata.is_mutable,
            token_metadata.update_authority,
            token_metadata.mint_authority,
            token_metadata.freeze_authority,
            token_metadata.is_active,
            token_metadata.image_path,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn update_token_metadata_supply(
        &self,
        chain: Chain,
        address: &str,
        supply: BigDecimal,
        update_timestamp_millis: i64,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            UPDATE token_metadata SET supply = $1, update_timestamp_millis = $2 WHERE chain = $3 AND address = $4"#,
            supply,
            update_timestamp_millis,
            chain as Chain,
            address,
        )
        .execute(&self.pool)
        .await?;
        Ok(())
    }

    pub async fn update_token_metadata_active_status(
        &self,
        chain: Chain,
        address: &str,
        is_active: bool,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            UPDATE token_metadata SET is_active = $1 WHERE chain = $2 AND address = $3"#,
            is_active,
            chain as Chain,
            address,
        )
        .execute(&self.pool)
        .await?;
        Ok(())
    }

    pub async fn insert_token_metadata_or_update_misc(
        &self,
        token_metadata: &TokenMetadata,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO token_metadata (
                chain, address,
                name, symbol,
                decimals, supply,
                description, image,
                website, twitter, telegram,
                dex_paid,
                is_trench_token,
                create_dex, create_block_number, create_tx_hash,
                create_bonding_curve, create_dev,
                create_timestamp_millis,
                migration_pool_address, migration_timestamp_millis,
                update_timestamp_millis,
                uri, seller_fee_basis_points,
                creators, primary_sale_happened,
                is_mutable,
                update_authority, mint_authority, freeze_authority,
                is_active, image_path
            )
            VALUES (
                $1, $2,
                $3, $4,
                $5, $6,
                $7, $8,
                $9, $10, $11,
                $12,
                $13,
                $14, $15, $16,
                $17, $18,
                $19,
                $20, $21,
                $22,
                $23, $24,
                $25, $26,
                $27,
                $28, $29, $30,
                $31, $32
            )
            ON CONFLICT (chain, address) DO UPDATE SET
                description = EXCLUDED.description,
                image = EXCLUDED.image,
                website = EXCLUDED.website,
                twitter = EXCLUDED.twitter,
                telegram = EXCLUDED.telegram,
                image_path = EXCLUDED.image_path
            "#,
            token_metadata.chain as Chain,
            token_metadata.address,
            token_metadata.name,
            token_metadata.symbol,
            token_metadata.decimals as i16,
            token_metadata.supply,
            token_metadata.description,
            token_metadata.image,
            token_metadata.website,
            token_metadata.twitter,
            token_metadata.telegram,
            token_metadata.dex_paid as DexPaid,
            token_metadata.is_trench_token,
            token_metadata.create_dex as Dex,
            token_metadata.create_block_number.map(|b| b as i64),
            token_metadata.create_tx_hash,
            token_metadata.create_bonding_curve,
            token_metadata.create_dev,
            token_metadata.create_timestamp_millis,
            token_metadata.migration_pool_address,
            token_metadata.migration_timestamp_millis,
            token_metadata.update_timestamp_millis,
            token_metadata.uri,
            token_metadata.seller_fee_basis_points.map(|s| s as i16),
            token_metadata.creators.as_deref(),
            token_metadata.primary_sale_happened,
            token_metadata.is_mutable,
            token_metadata.update_authority,
            token_metadata.mint_authority,
            token_metadata.freeze_authority,
            token_metadata.is_active,
            token_metadata.image_path,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn update_token_metadata_migration(
        &self,
        token_metadata: &TokenMetadata,
    ) -> Result<bool, Error> {
        let result = sqlx::query!(
            r#"
            UPDATE token_metadata SET
                migration_pool_address = $1,
                migration_timestamp_millis = $2
            WHERE chain = $3 AND address = $4"#,
            token_metadata.migration_pool_address,
            token_metadata.migration_timestamp_millis,
            token_metadata.chain as Chain,
            token_metadata.address,
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn get_token_metadata(
        &self,
        chain: Chain,
        address: &str,
    ) -> Result<Option<TokenMetadata>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                chain AS "chain: Chain",
                address,
                name,
                symbol,
                decimals,
                supply,
                description,
                image,
                website,
                twitter,
                telegram,
                dex_paid AS "dex_paid: DexPaid",
                is_trench_token,
                create_dex AS "create_dex: Dex",
                create_block_number,
                create_tx_hash,
                create_bonding_curve,
                create_dev,
                create_timestamp_millis,
                migration_pool_address,
                migration_timestamp_millis,
                update_timestamp_millis,
                uri, seller_fee_basis_points,
                creators, primary_sale_happened,
                is_mutable,
                update_authority, mint_authority, freeze_authority,
                is_active,
                image_path
            FROM token_metadata
            WHERE chain = $1 AND address = $2
            "#,
            chain as Chain,
            address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let token_metadata = match row {
            Some(row) => Some(TokenMetadata {
                chain: row.chain,
                address: row.address,
                name: row.name,
                symbol: row.symbol,
                decimals: row.decimals as u8,
                supply: row.supply,
                description: row.description,
                image: row.image,
                website: row.website,
                twitter: row.twitter,
                telegram: row.telegram,
                dex_paid: row.dex_paid,
                is_trench_token: row.is_trench_token,
                create_dex: row.create_dex,
                create_block_number: row.create_block_number.map(|b| b as u64),
                create_tx_hash: row.create_tx_hash,
                create_bonding_curve: row.create_bonding_curve,
                create_dev: row.create_dev,
                create_timestamp_millis: row.create_timestamp_millis,
                migration_pool_address: row.migration_pool_address,
                migration_timestamp_millis: row.migration_timestamp_millis,
                update_timestamp_millis: row.update_timestamp_millis,
                uri: row.uri,
                seller_fee_basis_points: row.seller_fee_basis_points.map(|s| s as u16),
                creators: row.creators,
                primary_sale_happened: row.primary_sale_happened,
                is_mutable: row.is_mutable,
                update_authority: row.update_authority,
                mint_authority: row.mint_authority,
                freeze_authority: row.freeze_authority,
                is_active: row.is_active,
                image_path: row.image_path,
            }),
            None => None,
        };

        Ok(token_metadata)
    }
}
