use serde::{Deserialize, Serialize};

use crate::{postgres::*, Result};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct ChainInfo {
    pub chain: Chain,
    pub block_number: u64,
}

impl PostgresDatabase {
    pub async fn insert_or_update_chain_info(&self, chain: Chain, block_number: u64) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO chain_info (chain, block_number)
            VALUES ($1, $2)
            ON CONFLICT (chain) DO UPDATE SET block_number = $2
            WHERE chain_info.block_number < $2
            "#,
            chain as Chain,
            block_number as i64,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_chain_info(&self, chain: Chain) -> Result<Option<ChainInfo>> {
        let row = sqlx::query!(
            r#"
            SELECT
                chain as "chain: Chain",
                block_number
            FROM chain_info
            WHERE chain = $1
            "#,
            chain as Chain,
        )
        .fetch_optional(&self.pool)
        .await?;

        let chain_info = match row {
            Some(row) => {
                Some(ChainInfo { chain: row.chain, block_number: row.block_number as u64 })
            }
            None => None,
        };

        Ok(chain_info)
    }
}
