use serde::{Deserialize, Serialize};

use crate::{postgres::*, Result};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct NativeTokenPrice {
    pub chain: Chain,
    pub timestamp_5_seconds: i64,
    pub timestamp_seconds: i64,
    pub usd_price: f64,
}

impl NativeTokenPrice {
    pub fn new_solana_native_token_price(timestamp_seconds: i64, usd_price: f64) -> Self {
        let timestamp_5_seconds = timestamp_seconds - timestamp_seconds % 5;
        Self { chain: Chain::Solana, timestamp_seconds, timestamp_5_seconds, usd_price }
    }

    pub fn new_hype_native_token_price(timestamp_seconds: i64, usd_price: f64) -> Self {
        let timestamp_5_seconds = timestamp_seconds - timestamp_seconds % 5;
        Self { chain: Chain::HyperEvm, timestamp_seconds, timestamp_5_seconds, usd_price }
    }
}

impl PostgresDatabase {
    pub async fn insert_native_token_price(
        &self,
        native_token_price: &NativeTokenPrice,
    ) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO native_token_price (
                chain,
                timestamp_5_seconds,
                timestamp_seconds,
                usd_price
            )
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (chain, timestamp_5_seconds) DO NOTHING
            "#,
            native_token_price.chain as Chain,
            native_token_price.timestamp_5_seconds,
            native_token_price.timestamp_seconds,
            native_token_price.usd_price,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_native_token_prices(
        &self,
        chain: Chain,
        start_timestamp_seconds: i64,
        end_timestamp_seconds: i64,
    ) -> Result<Vec<NativeTokenPrice>> {
        let rows = sqlx::query!(
            r#"
            SELECT
                chain as "chain: Chain",
                timestamp_5_seconds,
                timestamp_seconds,
                usd_price
            FROM native_token_price
            WHERE chain = $1
            AND timestamp_5_seconds >= $2
            AND timestamp_5_seconds <= $3
            "#,
            chain as Chain,
            start_timestamp_seconds,
            end_timestamp_seconds,
        )
        .fetch_all(&self.pool)
        .await?;

        let mut native_token_prices = Vec::with_capacity(rows.len());
        for row in rows {
            native_token_prices.push(NativeTokenPrice {
                chain: row.chain,
                timestamp_5_seconds: row.timestamp_5_seconds,
                timestamp_seconds: row.timestamp_seconds,
                usd_price: row.usd_price,
            });
        }

        Ok(native_token_prices)
    }

    pub async fn insert_native_token_prices(
        &self,
        native_token_prices: &[NativeTokenPrice],
    ) -> Result<()> {
        if native_token_prices.is_empty() {
            return Ok(());
        }

        // Extract arrays for each column
        let chains: Vec<Chain> = native_token_prices.iter().map(|p| p.chain).collect();
        let timestamp_5_seconds: Vec<i64> =
            native_token_prices.iter().map(|p| p.timestamp_5_seconds).collect();
        let timestamp_seconds: Vec<i64> =
            native_token_prices.iter().map(|p| p.timestamp_seconds).collect();
        let usd_prices: Vec<f64> = native_token_prices.iter().map(|p| p.usd_price).collect();

        sqlx::query!(
            r#"
            INSERT INTO native_token_price (chain, timestamp_5_seconds, timestamp_seconds, usd_price)
            SELECT * FROM unnest($1::chain_enum[], $2::bigint[], $3::bigint[], $4::double precision[])
            ON CONFLICT (chain, timestamp_5_seconds) DO NOTHING
            "#,
            &chains as &[Chain],
            &timestamp_5_seconds as &[i64],
            &timestamp_seconds as &[i64],
            &usd_prices as &[f64],
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::postgres::database::tests::get_postgres_database;

    #[tokio::test]
    async fn test_insert_native_token_prices() {
        let database = get_postgres_database().await;

        let native_token_prices = vec![
            NativeTokenPrice::new_solana_native_token_price(1719196800, 1.0),
            NativeTokenPrice::new_solana_native_token_price(1719196805, 1.0),
            NativeTokenPrice::new_solana_native_token_price(1719196810, 1.0),
            NativeTokenPrice::new_solana_native_token_price(1719196815, 1.0),
        ];
        database.insert_native_token_prices(&native_token_prices).await.unwrap();

        let native_token_prices =
            database.get_native_token_prices(Chain::Solana, 1719196800, 1719196815).await.unwrap();
        tracing::info!("native_token_prices: {:?}", native_token_prices);

        let native_token_prices = vec![
            NativeTokenPrice::new_solana_native_token_price(1719196810, 2.0),
            NativeTokenPrice::new_solana_native_token_price(1719196815, 2.0),
            NativeTokenPrice::new_solana_native_token_price(1719196820, 2.0),
            NativeTokenPrice::new_solana_native_token_price(1719196825, 2.0),
        ];
        database.insert_native_token_prices(&native_token_prices).await.unwrap();

        let native_token_prices =
            database.get_native_token_prices(Chain::Solana, 1719196800, 1719196825).await.unwrap();
        tracing::info!("native_token_prices: {:?}", native_token_prices);
    }
}
