use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

use crate::{postgres::*, Erro<PERSON>};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow, PartialEq, Eq)]
pub struct PoolMetadata {
    pub chain: Chain,
    pub pool_address: String,

    pub pair_label: String,
    pub dex: Dex,
    pub pool_type: PoolType,
    pub create_timestamp_millis: i64,
    pub update_timestamp_millis: i64,

    pub token_address: String,
    pub token_decimals: u8,
    pub base_address: String,
    pub base_decimals: u8,
    pub is_token_first: bool,

    pub is_active: bool,

    pub bin_step: Option<u16>,
}

impl PoolMetadata {
    pub fn new_solana_pool(
        pool_address: Pubkey,
        pair_label: String,
        dex: Dex,
        pool_type: PoolType,
        create_timestamp_millis: Option<i64>,
        update_timestamp_millis: i64,
        token_address: Pubkey,
        token_decimals: u8,
        base_address: Pubkey,
        base_decimals: u8,
        is_token_first: bool,
        bin_step: Option<u16>,
    ) -> Self {
        Self {
            chain: Chain::Solana,
            pool_address: pool_address.to_string(),
            pair_label,
            dex,
            pool_type,
            create_timestamp_millis: create_timestamp_millis.unwrap_or(0),
            update_timestamp_millis,
            token_address: token_address.to_string(),
            token_decimals,
            base_address: base_address.to_string(),
            base_decimals,
            is_token_first,
            is_active: true,
            bin_step,
        }
    }
}

impl PostgresDatabase {
    pub async fn insert_pool_metadata(&self, pool_metadata: &PoolMetadata) -> Result<bool, Error> {
        let result = sqlx::query!(
            r#"
            INSERT INTO pool_metadata (
                chain, pool_address,
                pair_label, dex, pool_type,
                create_timestamp_millis, update_timestamp_millis,
                token_address, token_decimals,
                base_address, base_decimals,
                is_token_first,
                is_active,
                bin_step
            )
            VALUES (
                $1, $2,
                $3, $4, $5,
                $6, $7,
                $8, $9,
                $10, $11,
                $12,
                $13,
                $14
            )
            ON CONFLICT (chain, pool_address) DO NOTHING
            "#,
            pool_metadata.chain as Chain,
            pool_metadata.pool_address,
            pool_metadata.pair_label,
            pool_metadata.dex as Dex,
            pool_metadata.pool_type as PoolType,
            pool_metadata.create_timestamp_millis,
            pool_metadata.update_timestamp_millis,
            pool_metadata.token_address,
            pool_metadata.token_decimals as i16,
            pool_metadata.base_address,
            pool_metadata.base_decimals as i16,
            pool_metadata.is_token_first,
            pool_metadata.is_active,
            pool_metadata.bin_step.map(|s| s as i16),
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn insert_pool_metadata_or_update_create_timestamp(
        &self,
        pool_metadata: &PoolMetadata,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO pool_metadata (
                chain, pool_address,
                pair_label, dex, pool_type,
                create_timestamp_millis, update_timestamp_millis,
                token_address, token_decimals,
                base_address, base_decimals,
                is_token_first,
                is_active,
                bin_step
            )
            VALUES (
                $1, $2,
                $3, $4, $5,
                $6, $7,
                $8, $9,
                $10, $11,
                $12,
                $13,
                $14
            )
            ON CONFLICT (chain, pool_address) DO UPDATE SET
                create_timestamp_millis = EXCLUDED.create_timestamp_millis
            WHERE pool_metadata.create_timestamp_millis < EXCLUDED.create_timestamp_millis
            "#,
            pool_metadata.chain as Chain,
            pool_metadata.pool_address,
            pool_metadata.pair_label,
            pool_metadata.dex as Dex,
            pool_metadata.pool_type as PoolType,
            pool_metadata.create_timestamp_millis,
            pool_metadata.update_timestamp_millis,
            pool_metadata.token_address,
            pool_metadata.token_decimals as i16,
            pool_metadata.base_address,
            pool_metadata.base_decimals as i16,
            pool_metadata.is_token_first,
            pool_metadata.is_active,
            pool_metadata.bin_step.map(|s| s as i16),
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn insert_pool_metadata_or_update_is_active(
        &self,
        pool_metadata: &PoolMetadata,
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"
            INSERT INTO pool_metadata (
                chain, pool_address,
                pair_label, dex, pool_type,
                create_timestamp_millis, update_timestamp_millis,
                token_address, token_decimals,
                base_address, base_decimals,
                is_token_first,
                is_active,
                bin_step
            )
            VALUES (
                $1, $2,
                $3, $4, $5,
                $6, $7,
                $8, $9,
                $10, $11,
                $12,
                $13,
                $14
            )
            ON CONFLICT (chain, pool_address) DO UPDATE SET
                update_timestamp_millis = EXCLUDED.update_timestamp_millis,
                is_active = EXCLUDED.is_active
            WHERE pool_metadata.is_active != EXCLUDED.is_active
            "#,
            pool_metadata.chain as Chain,
            pool_metadata.pool_address,
            pool_metadata.pair_label,
            pool_metadata.dex as Dex,
            pool_metadata.pool_type as PoolType,
            pool_metadata.create_timestamp_millis,
            pool_metadata.update_timestamp_millis,
            pool_metadata.token_address,
            pool_metadata.token_decimals as i16,
            pool_metadata.base_address,
            pool_metadata.base_decimals as i16,
            pool_metadata.is_token_first,
            pool_metadata.is_active,
            pool_metadata.bin_step.map(|s| s as i16),
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn update_pool_metadata_is_active(
        &self,
        chain: Chain,
        pool_address: &str,
        is_active: bool,
        update_timestamp_millis: i64,
    ) -> Result<bool, Error> {
        let result = sqlx::query!(
            r#"
            UPDATE pool_metadata SET 
                is_active = $1, 
                update_timestamp_millis = $2 
            WHERE chain = $3 AND pool_address = $4"#,
            is_active,
            update_timestamp_millis,
            chain as Chain,
            pool_address,
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn get_pool_metadata(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolMetadata>, Error> {
        let row = sqlx::query!(
            r#"
            SELECT 
                chain AS "chain: Chain",
                pool_address,
                pair_label,
                dex AS "dex: Dex",
                pool_type AS "pool_type: PoolType",
                create_timestamp_millis,
                update_timestamp_millis,
                token_address,
                token_decimals,
                base_address,
                base_decimals,
                is_token_first,
                is_active,
                bin_step
            FROM pool_metadata
            WHERE chain = $1 AND pool_address = $2
            "#,
            chain as Chain,
            pool_address,
        )
        .fetch_optional(&self.pool)
        .await?;

        let pool_metadata = match row {
            Some(row) => Some(PoolMetadata {
                chain: row.chain,
                pool_address: row.pool_address,
                pair_label: row.pair_label,
                dex: row.dex,
                pool_type: row.pool_type,
                create_timestamp_millis: row.create_timestamp_millis,
                update_timestamp_millis: row.update_timestamp_millis,
                token_address: row.token_address,
                token_decimals: row.token_decimals as u8,
                base_address: row.base_address,
                base_decimals: row.base_decimals as u8,
                is_token_first: row.is_token_first,
                is_active: row.is_active,
                bin_step: row.bin_step.map(|s| s as u16),
            }),
            None => None,
        };

        Ok(pool_metadata)
    }
}
