use super::*;
use crate::postgres::{<PERSON>, <PERSON>};

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct TokensFilter {
    pub is_active: Option<bool>,

    pub limit: u64,
    pub offset: u64,

    pub chain: Option<Vec<Chain>>,

    pub market_cap_min: Option<f64>,
    pub market_cap_max: Option<f64>,
    pub fdv_min: Option<f64>,
    pub fdv_max: Option<f64>,
    pub liquidity_min: Option<f64>,
    pub liquidity_max: Option<f64>,
    pub volume_min: Option<f64>,
    pub volume_max: Option<f64>,
    pub txns_min: Option<u64>,
    pub txns_max: Option<u64>,
    pub buys_min: Option<u64>,
    pub buys_max: Option<u64>,
    pub sells_min: Option<u64>,
    pub sells_max: Option<u64>,
    pub change_min: Option<f64>,
    pub change_max: Option<f64>,

    // market filter
    pub time: Option<Time>,
    pub trend: Option<Trend>,
    pub best_pool_dex: Option<Vec<Dex>>,
    pub pool_dexes: Option<Vec<Dex>>,

    // trench filter
    pub trench: Option<Trench>,
    pub create_dex: Option<Vec<Dex>>,
    pub has_social: Option<bool>,
    pub has_twitter: Option<bool>,
    pub has_telegram: Option<bool>,
    pub has_website: Option<bool>,
    pub dex_paid: Option<bool>,
    pub creation_time_min: Option<u64>,
    pub creation_time_max: Option<u64>,
    pub bonding_curve_min: Option<f64>,
    pub bonding_curve_max: Option<f64>,
    pub dev_holdings_min: Option<f64>,
    pub dev_holdings_max: Option<f64>,
    pub top10_holdings_min: Option<f64>,
    pub top10_holdings_max: Option<f64>,
    pub holders_min: Option<u64>,
    pub holders_max: Option<u64>,
    pub sniper_min: Option<u64>,
    pub sniper_max: Option<u64>,
    pub insider_min: Option<u64>,
    pub insider_max: Option<u64>,
    pub bot_min: Option<u64>,
    pub bot_max: Option<u64>,
}
