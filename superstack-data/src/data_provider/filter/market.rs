use serde::Deserialize;

use crate::postgres::{<PERSON>, Dex};

#[derive(Debug, <PERSON><PERSON>, Deserialize, PartialEq)]
pub enum Time {
    #[serde(rename = "All")]
    All,
    #[serde(rename = "5m")]
    Minute5,
    #[serde(rename = "1h")]
    Hour1,
    #[serde(rename = "6h")]
    Hour6,
    #[serde(rename = "24h")]
    Hour24,
    #[serde(rename = "3d")]
    Day3,
    #[serde(rename = "7d")]
    Day7,
}

#[derive(Debug, Clone, Deserialize)]
pub enum Trend {
    Trending,
    New,
    Popular,
    Gainers,
    Losers,
    Top,
}

#[derive(Debug, Clone)]
pub struct MarketTokensFilter {
    pub limit: u64,
    pub offset: u64,
    pub time: Time,
    pub trend: Trend,
    pub best_pool_dex: Option<Vec<Dex>>,
    pub pool_dexes: Option<Vec<Dex>>,
    pub chain: Option<Vec<Chain>>,
    pub market_cap_min: Option<f64>,
    pub market_cap_max: Option<f64>,
    pub fdv_min: Option<f64>,
    pub fdv_max: Option<f64>,
    pub liquidity_min: Option<f64>,
    pub liquidity_max: Option<f64>,
    pub volume_min: Option<f64>,
    pub volume_max: Option<f64>,
    pub txns_min: Option<u64>,
    pub txns_max: Option<u64>,
    pub buys_min: Option<u64>,
    pub buys_max: Option<u64>,
    pub sells_min: Option<u64>,
    pub sells_max: Option<u64>,
    pub change_min: Option<f64>,
    pub change_max: Option<f64>,
}
