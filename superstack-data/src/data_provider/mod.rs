pub mod filter;

use self::filter::{MarketTokensFilter, TokensFilter, TrenchTokensFilter};
use crate::{
    error::Error,
    postgres::{
        aggregator::*,
        enums::*,
        indexer::{PoolMetadata, TokenMetadata},
        PostgresDatabase,
    },
    redis::RedisClient,
};

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct TokenMetadataWithPrice {
    pub token_metadata: TokenMetadata,
    pub price: f64,
}

pub struct IndexerDataProvider {
    db: &'static PostgresDatabase,
    redis_client: &'static RedisClient,
}

impl IndexerDataProvider {
    pub async fn new() -> Self {
        let db = PostgresDatabase::get_indexer_db().await;
        let redis_client = RedisClient::get_instance().await;
        Self { db, redis_client }
    }

    pub async fn get_tokens(&self, filter: &TokensFilter) -> Result<Vec<TokenStatistic>, Error> {
        // Use Redis for complex filtering as it has better search capabilities
        self.redis_client.search_tokens_by_filter(&filter).await
    }

    pub async fn get_market_tokens(
        &self,
        filter: &MarketTokensFilter,
    ) -> Result<Vec<TokenStatistic>, Error> {
        // Convert MarketTokensFilter to TokensFilter for Redis search
        let tokens_filter = TokensFilter {
            is_active: Some(true),
            limit: filter.limit,
            offset: filter.offset,
            chain: filter.chain.clone(),
            market_cap_min: filter.market_cap_min,
            market_cap_max: filter.market_cap_max,
            fdv_min: filter.fdv_min,
            fdv_max: filter.fdv_max,
            liquidity_min: filter.liquidity_min,
            liquidity_max: filter.liquidity_max,
            volume_min: filter.volume_min,
            volume_max: filter.volume_max,
            txns_min: filter.txns_min,
            txns_max: filter.txns_max,
            buys_min: filter.buys_min,
            buys_max: filter.buys_max,
            sells_min: filter.sells_min,
            sells_max: filter.sells_max,
            change_min: filter.change_min,
            change_max: filter.change_max,
            time: Some(filter.time.clone()),
            trend: Some(filter.trend.clone()),
            best_pool_dex: filter.best_pool_dex.clone(),
            pool_dexes: filter.pool_dexes.clone(),
            // Market tokens are not trench tokens
            trench: None,
            create_dex: None,
            has_social: None,
            has_twitter: None,
            has_telegram: None,
            has_website: None,
            dex_paid: None,
            creation_time_min: None,
            creation_time_max: None,
            bonding_curve_min: None,
            bonding_curve_max: None,
            dev_holdings_min: None,
            dev_holdings_max: None,
            top10_holdings_min: None,
            top10_holdings_max: None,
            holders_min: None,
            holders_max: None,
            sniper_min: None,
            sniper_max: None,
            insider_min: None,
            insider_max: None,
            bot_min: None,
            bot_max: None,
        };

        self.get_tokens(&tokens_filter).await
    }

    pub async fn get_trench_tokens(
        &self,
        filter: &TrenchTokensFilter,
    ) -> Result<Vec<TokenStatistic>, Error> {
        // Convert TrenchTokensFilter to TokensFilter for Redis search
        let tokens_filter = TokensFilter {
            is_active: Some(true),
            limit: filter.limit,
            offset: filter.offset,
            chain: filter.chain.clone(),
            // No market cap filters for trench tokens typically
            market_cap_min: None,
            market_cap_max: None,
            fdv_min: None,
            fdv_max: None,
            liquidity_min: None,
            liquidity_max: None,
            volume_min: None,
            volume_max: None,
            txns_min: None,
            txns_max: None,
            buys_min: None,
            buys_max: None,
            sells_min: None,
            sells_max: None,
            change_min: None,
            change_max: None,
            time: None,
            trend: None,
            best_pool_dex: None,
            pool_dexes: None,
            // Trench-specific filters
            trench: Some(filter.trench.clone()),
            create_dex: filter.create_dex.clone(),
            has_social: filter.has_social,
            has_twitter: filter.has_twitter,
            has_telegram: filter.has_telegram,
            has_website: filter.has_website,
            dex_paid: filter.dex_paid,
            creation_time_min: filter.creation_time_min,
            creation_time_max: filter.creation_time_max,
            bonding_curve_min: filter.bonding_curve_min,
            bonding_curve_max: filter.bonding_curve_max,
            dev_holdings_min: filter.dev_holdings_min,
            dev_holdings_max: filter.dev_holdings_max,
            top10_holdings_min: filter.top10_holdings_min,
            top10_holdings_max: filter.top10_holdings_max,
            holders_min: filter.holders_min,
            holders_max: filter.holders_max,
            sniper_min: filter.sniper_min,
            sniper_max: filter.sniper_max,
            insider_min: filter.insider_min,
            insider_max: filter.insider_max,
            bot_min: filter.bot_min,
            bot_max: filter.bot_max,
        };

        self.get_tokens(&tokens_filter).await
    }

    pub async fn get_token(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<Option<TokenStatistic>, Error> {
        self.redis_client.get_token(chain, token_address).await
    }

    pub async fn get_pool(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolStatistic>, Error> {
        self.redis_client.get_pool(chain, pool_address).await
    }

    pub async fn get_pools(
        &self,
        chain: Chain,
        pool_addresses: &[&str],
    ) -> Result<Vec<PoolStatistic>, Error> {
        self.redis_client.get_pools(chain, pool_addresses).await
    }

    pub async fn search_tokens_by_keyword(
        &self,
        chain: Chain,
        keyword: &str,
        limit: u64,
    ) -> Result<Vec<TokenStatistic>, Error> {
        self.redis_client.search_tokens_by_keyword(chain, keyword, limit as u32).await
    }

    pub async fn search_tokens_by_address(
        &self,
        token_address: &str,
    ) -> Result<Vec<TokenStatistic>, Error> {
        self.redis_client.get_tokens_by_address(token_address).await
    }

    pub async fn search_pools_by_address(
        &self,
        pool_address: &str,
    ) -> Result<Vec<PoolStatistic>, Error> {
        self.redis_client.get_pools_by_address(pool_address).await
    }

    /// Returns trades for a given pool in descending order of timestamp (max 30 days)
    pub async fn get_extended_dex_trade(
        &self,
        chain: Chain,
        pool_address: &str,
        limit: u64,
    ) -> Result<Vec<ExtendedDexTrade>, Error> {
        let end_time = chrono::Utc::now().timestamp_millis();
        let start_time = end_time - 30 * 24 * 60 * 60 * 1000; // 30 days ago
        self.db.get_extended_dex_trades(chain, pool_address, start_time, end_time, limit).await
    }

    pub async fn get_holders(
        &self,
        chain: Chain,
        token_address: &str,
        limit: u64,
        offset: u64,
    ) -> Result<Vec<ExtendedTokenHolder>, Error> {
        self.db
            .get_extended_token_holders_ordered_by_holding(chain, token_address, limit, offset)
            .await
    }

    pub async fn get_top_traders(
        &self,
        chain: Chain,
        token_address: &str,
        limit: u64,
    ) -> Result<Vec<ExtendedTokenHolder>, Error> {
        self.db.get_extended_token_holders_ordered_by_pnl_usd(chain, token_address, limit).await
    }

    pub async fn get_holders_count(&self, chain: Chain, token_address: &str) -> Result<u64, Error> {
        self.db.get_extended_token_holders_count(chain, token_address).await
    }

    pub async fn get_candles(
        &self,
        chain: Chain,
        pool_address: &str,
        interval: CandleInterval,
        start_time_seconds: i64,
        end_time_seconds: i64,
    ) -> Result<Vec<Candle>, Error> {
        self.db
            .get_candles(chain, pool_address, interval, start_time_seconds, end_time_seconds)
            .await
    }

    pub async fn get_pools_by_token_address(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<Vec<PoolStatistic>, Error> {
        let token = self
            .get_token(chain, token_address)
            .await?
            .ok_or(anyhow::anyhow!("Token not found"))?;
        let pool_addresses = token.pool_addresses.split(',').collect::<Vec<&str>>();
        self.redis_client.get_pools(chain, &pool_addresses).await
    }

    pub async fn get_trending_pools(
        &self,
        chain: Option<Chain>,
        limit: u64,
    ) -> Result<Vec<PoolStatistic>, Error> {
        self.redis_client.get_trending_pools(chain, limit).await
    }

    pub async fn get_token_metadata(
        &self,
        chain: Chain,
        token_address: &str,
    ) -> Result<Option<TokenMetadata>, Error> {
        self.redis_client.try_get_token_metadata_or_update_from_db(chain, token_address).await
    }

    pub async fn get_pool_metadata(
        &self,
        chain: Chain,
        pool_address: &str,
    ) -> Result<Option<PoolMetadata>, Error> {
        self.redis_client.try_get_pool_metadata_or_update_from_db(chain, pool_address).await
    }
}
