-- Add tx_idx column, which is the index of the transaction in the block
ALTER TABLE extended_dex_trade ADD COLUMN tx_idx INTEGER NOT NULL DEFAULT 0;

-- Create new index with tx_idx and ix_idx
CREATE INDEX idx_extended_dex_trade_chain_pool_timestamp_block_idx ON extended_dex_trade (chain, pool_address, timestamp_millis DESC, block_number DESC, tx_idx DESC, ix_idx DESC);

-- Drop old index
DROP INDEX IF EXISTS idx_extended_dex_trade_timestamp;

-- Create new index with maker_address
CREATE INDEX idx_extended_dex_trade_maker_address_timestamp_block_idx ON extended_dex_trade (chain, pool_address, maker_address, timestamp_millis DESC, block_number DESC, tx_idx DESC, ix_idx DESC);

-- Drop old index
DROP INDEX IF EXISTS idx_extended_dex_trade_maker_address;