CREATE TABLE IF NOT EXISTS pool_metadata (
    chain chain_enum NOT NULL,
    pool_address VARCHAR NOT NULL,

    pair_label VARCHAR NOT NULL,
    dex dex_enum NOT NULL,
    pool_type pool_type_enum NOT NULL,
    create_timestamp_millis BIGINT NOT NULL DEFAULT 0,

    update_timestamp_millis BIGINT NOT NULL,
    
    token_address VARCHAR NOT NULL,
    token_decimals SMALLINT NOT NULL CHECK (token_decimals >= 0 AND token_decimals <= 255),
    base_address VARCHAR NOT NULL,
    base_decimals SMALLINT NOT NULL CHECK (base_decimals >= 0 AND base_decimals <= 255),
    is_token_first BOOLEAN NOT NULL,
    
    is_active BOOLEAN NOT NULL,
    
    bin_step SMALLINT,
    
    PRIMARY KEY (chain, pool_address)
);
