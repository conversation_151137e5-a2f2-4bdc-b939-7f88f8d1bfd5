CREATE TABLE IF NOT EXISTS dex_trade (
    chain chain_enum NOT NULL,
    tx_hash VARCHAR NOT NULL,
    ix_idx INTEGER NOT NULL,

    pool_address VARCHAR NOT NULL,
    maker_address VARCHAR NOT NULL,

    is_buy_token BOOLEAN NOT NULL,
    in_address VARCHAR NOT NULL,
    in_amount NUMERIC(78,0) NOT NULL, -- UInt256 equivalent
    out_address VARCHAR NOT NULL,
    out_amount NUMERIC(78,0) NOT NULL, -- UInt256 equivalent

    block_number BIGINT NOT NULL,
    timestamp_millis BIGINT NOT NULL,

    PRIMARY KEY (chain, tx_hash, ix_idx)
);

CREATE INDEX idx_dex_trade_chain_pool_timestamp ON dex_trade (chain, pool_address, timestamp_millis DESC);
