CREATE TABLE IF NOT EXISTS pool_state (
    chain chain_enum NOT NULL,
    pool_address VARCHAR NOT NULL,

    block_number BIGINT NOT NULL,
    timestamp_millis BIGINT NOT NULL,

    price DOUBLE PRECISION NOT NULL,
    market_cap DOUBLE PRECISION NOT NULL,
    liquidity DOUBLE PRECISION NOT NULL,

    total_volume DOUBLE PRECISION NOT NULL,
    total_buy_volume DOUBLE PRECISION NOT NULL,
    total_sell_volume DOUBLE PRECISION NOT NULL,

    total_txns BIGINT NOT NULL,
    total_buy_txns BIGINT NOT NULL,
    total_sell_txns BIGINT NOT NULL,

    bonding_curve_progress DOUBLE PRECISION,

    PRIMARY KEY (chain, pool_address, block_number)
);

CREATE INDEX idx_pool_state_chain_pool_block_desc ON pool_state (chain, pool_address, block_number DESC);
CREATE INDEX idx_pool_state_chain_pool_timestamp_desc ON pool_state (chain, pool_address, timestamp_millis DESC);
