CREATE TABLE IF NOT EXISTS extended_dex_trade (
    chain chain_enum NOT NULL,
    tx_hash VARCHAR NOT NULL,
    ix_idx INTEGER NOT NULL,

    pool_address VARCHAR NOT NULL,
    maker_address VARCHAR NOT NULL,

    is_buy BOOLEAN NOT NULL,
    token_ui_amount DOUBLE PRECISION NOT NULL,
    base_ui_amount DOUBLE PRECISION NOT NULL,

    usd DOUBLE PRECISION NOT NULL,
    usd_price DOUBLE PRECISION NOT NULL,
    usd_market_cap DOUBLE PRECISION NOT NULL,

    block_number BIGINT NOT NULL,
    timestamp_millis BIGINT NOT NULL,

    maker_volume_type maker_volume_type_enum NOT NULL,

    PRIMARY KEY (chain, tx_hash, ix_idx)
);

CREATE INDEX idx_extended_dex_trade_maker_address ON extended_dex_trade (chain, pool_address, maker_address, timestamp_millis DESC, block_number DESC);
CREATE INDEX idx_extended_dex_trade_timestamp ON extended_dex_trade (chain, pool_address, timestamp_millis DESC, block_number DESC);
