CREATE TABLE IF NOT EXISTS perp_user_state (
    perp_exchange perp_exchange_enum NOT NULL,
    perp_user VARCHAR NOT NULL,

    perps_account_value DOUBLE PRECISION NOT NULL,
    account_value DOUBLE PRECISION NOT NULL,

    hyperliquid_acc_collateral DOUBLE PRECISION NOT NULL,
    hyperliquid_acc_perps DOUBLE PRECISION NOT NULL,

    timestamp_millis BIGINT NOT NULL,

    PRIMARY KEY (perp_exchange, perp_user, timestamp_millis)
);

-- Indexes for query performance optimization
CREATE INDEX idx_perp_user_state_exchange ON perp_user_state (perp_exchange);
CREATE INDEX idx_perp_user_state_perp_user ON perp_user_state (perp_user);
CREATE INDEX idx_perp_user_state_timestamp ON perp_user_state (timestamp_millis DESC);
CREATE INDEX idx_perp_user_state_account_value ON perp_user_state (account_value DESC);
CREATE INDEX idx_perp_user_state_perps_account_value ON perp_user_state (perps_account_value DESC);
CREATE INDEX idx_perp_user_state_hyperliquid_acc_collateral ON perp_user_state (hyperliquid_acc_collateral DESC);
CREATE INDEX idx_perp_user_state_hyperliquid_acc_perps ON perp_user_state (hyperliquid_acc_perps DESC);

-- Composite index for time series queries
CREATE INDEX idx_perp_user_state_perp_user_time ON perp_user_state (perp_exchange, perp_user, timestamp_millis DESC);
