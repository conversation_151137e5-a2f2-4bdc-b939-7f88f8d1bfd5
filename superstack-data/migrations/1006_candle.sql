CREATE TABLE IF NOT EXISTS candle (
    chain chain_enum NOT NULL,
    pool_address VARCHAR NOT NULL,

    open_timestamp_seconds BIGINT NOT NULL,
    close_timestamp_seconds BIGINT NOT NULL,

    interval candle_interval_enum NOT NULL,

    native_token_usd_price DOUBLE PRECISION NOT NULL,

    usd_open_price DOUBLE PRECISION NOT NULL,
    usd_close_price DOUBLE PRECISION NOT NULL,
    usd_high_price DOUBLE PRECISION NOT NULL,
    usd_low_price DOUBLE PRECISION NOT NULL,

    usd_open_market_cap DOUBLE PRECISION NOT NULL,
    usd_close_market_cap DOUBLE PRECISION NOT NULL,
    usd_high_market_cap DOUBLE PRECISION NOT NULL,
    usd_low_market_cap DOUBLE PRECISION NOT NULL,

    usd_volume DOUBLE PRECISION NOT NULL,
    txns BIGINT NOT NULL,

    PRIMARY KEY (chain, pool_address, interval, open_timestamp_seconds)
);

CREATE INDEX IF NOT EXISTS idx_candle_interval_open_timestamp ON candle (chain, pool_address, interval, open_timestamp_seconds DESC);
