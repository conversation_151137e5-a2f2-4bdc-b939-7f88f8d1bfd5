CREATE TABLE IF NOT EXISTS extended_token_holder (
    chain chain_enum NOT NULL,
    token_address VARCHAR NOT NULL,
    holder_address VARCHAR NOT NULL,

    bought_ui_amount DOUBLE PRECISION NOT NULL,
    sold_ui_amount DOUBLE PRECISION NOT NULL,
    remaining_ui_amount DOUBLE PRECISION NOT NULL,

    bought_txns BIGINT NOT NULL,
    sold_txns BIGINT NOT NULL,

    spent_usd DOUBLE PRECISION NOT NULL,
    received_usd DOUBLE PRECISION NOT NULL,

    pnl_usd DOUBLE PRECISION NOT NULL,

    update_timestamp_millis BIGINT NOT NULL,
    update_block_number BIGINT NOT NULL,

    native_token_balance DOUBLE PRECISION NOT NULL,

    maker_volume_type maker_volume_type_enum NOT NULL,
    maker_trade_type maker_trade_type_enum NOT NULL,

    PRIMARY KEY (chain, token_address, holder_address)
);

CREATE INDEX idx_extended_token_holder_remaining_ui_amount ON extended_token_holder (chain, token_address, remaining_ui_amount DESC);
CREATE INDEX idx_extended_token_holder_pnl_usd ON extended_token_holder (chain, token_address, pnl_usd DESC);
