CREATE TABLE IF NOT EXISTS token_holder (
    chain chain_enum NOT NULL,
    token_address VARCHAR NOT NULL,
    holder_address VARCHAR NOT NULL,

    bought_amount NUMERIC(78,0) NOT NULL, -- UInt256 equivalent
    sold_amount NUMERIC(78,0) NOT NULL, -- UInt256 equivalent

    bought_txns BIGINT NOT NULL,
    sold_txns BIGINT NOT NULL,

    spent_native_token_ui_amount DOUBLE PRECISION NOT NULL,
    spent_usd_token_ui_amount DOUBLE PRECISION NOT NULL,
    total_spent_usd DOUBLE PRECISION NOT NULL,

    received_native_token_ui_amount DOUBLE PRECISION NOT NULL,
    received_usd_token_ui_amount DOUBLE PRECISION NOT NULL,
    total_received_usd DOUBLE PRECISION NOT NULL,

    update_timestamp_millis BIGINT NOT NULL,
    update_block_number BIGINT NOT NULL,

    PRIMARY KEY (chain, token_address, holder_address)
);