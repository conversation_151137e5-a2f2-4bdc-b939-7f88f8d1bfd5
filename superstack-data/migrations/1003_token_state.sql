CREATE TABLE IF NOT EXISTS token_state (
    chain chain_enum NOT NULL,
    token_address VARCHAR NOT NULL,
    timestamp_millis BIGINT NOT NULL,

    best_pool_address VARCHAR NOT NULL,

    usd_price DOUBLE PRECISION NOT NULL,
    usd_market_cap DOUBLE PRECISION NOT NULL,

    native_price DOUBLE PRECISION,

    native_token_liquidity DOUBLE PRECISION NOT NULL,
    usd_token_liquidity DOUBLE PRECISION NOT NULL,

    total_native_token_volume DOUBLE PRECISION NOT NULL,
    total_usd_token_volume DOUBLE PRECISION NOT NULL,
    total_native_token_buy_volume DOUBLE PRECISION NOT NULL,
    total_usd_token_buy_volume DOUBLE PRECISION NOT NULL,
    total_native_token_sell_volume DOUBLE PRECISION NOT NULL,
    total_usd_token_sell_volume DOUBLE PRECISION NOT NULL,

    total_txns BIGINT NOT NULL,
    total_buy_txns BIGINT NOT NULL,
    total_sell_txns BIGINT NOT NULL,

    bonding_curve_progress DOUBLE PRECISION,

    PRIMARY KEY (chain, token_address, timestamp_millis)
);

CREATE INDEX idx_token_state_timestamp ON token_state (chain, token_address, timestamp_millis DESC);
