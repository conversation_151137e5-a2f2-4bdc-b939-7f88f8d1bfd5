-- Add migration script here
CREATE TABLE IF NOT EXISTS perp_state_series (
    perp_exchange perp_exchange_enum NOT NULL,
    perp_id VARCHAR NOT NULL,

    funding DOUBLE PRECISION NOT NULL,
    open_interest DOUBLE PRECISION NOT NULL,
    mark_px DOUBLE PRECISION NOT NULL,

    long_ntl DOUBLE PRECISION NOT NULL,
    short_ntl DOUBLE PRECISION NOT NULL,
    long_traders BIGINT NOT NULL,
    short_traders BIGINT NOT NULL,
    long_entry DOUBLE PRECISION NOT NULL,
    short_entry DOUBLE PRECISION NOT NULL,

    created_at_millis BIGINT NOT NULL,

    PRIMARY KEY (perp_exchange, perp_id, created_at_millis)
);

CREATE INDEX idx_perp_state_series_exchange ON perp_state_series (perp_exchange);
CREATE INDEX idx_perp_state_series_perp_id ON perp_state_series (perp_id);
CREATE INDEX idx_perp_state_series_funding ON perp_state_series (funding);
CREATE INDEX idx_perp_state_series_open_interest ON perp_state_series (open_interest);
CREATE INDEX idx_perp_state_series_mark_px ON perp_state_series (mark_px);
CREATE INDEX idx_perp_state_series_created_at ON perp_state_series (created_at_millis DESC);
CREATE INDEX idx_perp_state_series_for_latest ON perp_state_series (perp_exchange, perp_id, created_at_millis DESC);