CREATE TABLE IF NOT EXISTS perp_info (
    perp_exchange perp_exchange_enum NOT NULL,
    perp_id VARCHAR NOT NULL,

    is_native_token BOOLEAN NOT NULL,
    network VARCHAR,
    address VARCHAR,

    name VARCHAR NOT NULL,
    symbol VARCHAR NOT NULL,

    socials JSONB NOT NULL DEFAULT '{}',

    total_supply NUMERIC NOT NULL,
    circulating_supply NUMERIC NOT NULL,

    update_timestamp_millis BIGINT NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    PRIMARY KEY (perp_exchange, perp_id)
);

CREATE INDEX idx_perp_info_exchange ON perp_info (perp_exchange);
CREATE INDEX idx_perp_info_symbol ON perp_info (symbol);
CREATE INDEX idx_perp_info_name ON perp_info (name);
CREATE INDEX idx_perp_info_updated_at ON perp_info (updated_at DESC);
CREATE INDEX idx_perp_info_socials ON perp_info USING GIN (socials);
