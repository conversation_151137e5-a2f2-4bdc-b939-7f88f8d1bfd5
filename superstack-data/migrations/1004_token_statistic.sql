CREATE TABLE IF NOT EXISTS token_statistic (
    chain chain_enum NOT NULL,
    token_address VARCHAR NOT NULL,

    name VA<PERSON>HAR NOT NULL,
    symbol VARCHAR NOT NULL,

    decimals SMALLINT NOT NULL,
    supply VARCHAR NOT NULL,

    description VARCHAR,
    image VARCHAR,
    website VARCHAR,
    twitter VARCHAR,
    telegram VARCHAR,
    dex_paid dex_paid_enum NOT NULL,

    is_trench_token BOOLEAN NOT NULL,

    create_dex dex_enum NOT NULL DEFAULT 'unknown',
    create_block_number BIGINT,
    create_tx_hash VARCHAR,
    create_bonding_curve VARCHAR,
    create_dev VARCHAR,
    create_timestamp_millis BIGINT NOT NULL DEFAULT 0,

    migration_pool_address VARCHAR,
    migration_timestamp_millis BIGINT NOT NULL DEFAULT 0,

    update_timestamp_millis BIGINT NOT NULL DEFAULT 0,

    is_mutable BOOLEAN,
    update_authority VARCHAR,
    mint_authority VARCHAR,
    freeze_authority VARCHAR,

    best_pool_address VARCHAR NOT NULL,
    pool_addresses VARCHAR NOT NULL,

    usd_price DOUBLE PRECISION NOT NULL,
    usd_market_cap DOUBLE PRECISION NOT NULL,
    usd_liquidity DOUBLE PRECISION NOT NULL,

    bonding_curve_progress DOUBLE PRECISION,

    -- Total statistics
    usd_total_volume DOUBLE PRECISION NOT NULL,
    usd_total_buy_volume DOUBLE PRECISION NOT NULL,
    usd_total_sell_volume DOUBLE PRECISION NOT NULL,

    total_txns BIGINT NOT NULL,
    total_buy_txns BIGINT NOT NULL,
    total_sell_txns BIGINT NOT NULL,

    total_trend DOUBLE PRECISION NOT NULL,
    total_price_change DOUBLE PRECISION NOT NULL,

    -- 5m statistics
    trend_5m DOUBLE PRECISION NOT NULL,
    price_change_5m DOUBLE PRECISION NOT NULL,
    txns_5m BIGINT NOT NULL,
    buy_txns_5m BIGINT NOT NULL,
    sell_txns_5m BIGINT NOT NULL,
    usd_volume_5m DOUBLE PRECISION NOT NULL,
    usd_buy_volume_5m DOUBLE PRECISION NOT NULL,
    usd_sell_volume_5m DOUBLE PRECISION NOT NULL,

    -- 1h statistics
    trend_1h DOUBLE PRECISION NOT NULL,
    price_change_1h DOUBLE PRECISION NOT NULL,
    txns_1h BIGINT NOT NULL,
    buy_txns_1h BIGINT NOT NULL,
    sell_txns_1h BIGINT NOT NULL,
    usd_volume_1h DOUBLE PRECISION NOT NULL,
    usd_buy_volume_1h DOUBLE PRECISION NOT NULL,
    usd_sell_volume_1h DOUBLE PRECISION NOT NULL,

    -- 6h statistics
    trend_6h DOUBLE PRECISION NOT NULL,
    price_change_6h DOUBLE PRECISION NOT NULL,
    txns_6h BIGINT NOT NULL,
    buy_txns_6h BIGINT NOT NULL,
    sell_txns_6h BIGINT NOT NULL,
    usd_volume_6h DOUBLE PRECISION NOT NULL,
    usd_buy_volume_6h DOUBLE PRECISION NOT NULL,
    usd_sell_volume_6h DOUBLE PRECISION NOT NULL,

    -- 24h statistics
    trend_24h DOUBLE PRECISION NOT NULL,
    price_change_24h DOUBLE PRECISION NOT NULL,
    txns_24h BIGINT NOT NULL,
    buy_txns_24h BIGINT NOT NULL,
    sell_txns_24h BIGINT NOT NULL,
    usd_volume_24h DOUBLE PRECISION NOT NULL,
    usd_buy_volume_24h DOUBLE PRECISION NOT NULL,
    usd_sell_volume_24h DOUBLE PRECISION NOT NULL,

    -- 3d statistics
    trend_3d DOUBLE PRECISION NOT NULL,
    price_change_3d DOUBLE PRECISION NOT NULL,
    txns_3d BIGINT NOT NULL,
    buy_txns_3d BIGINT NOT NULL,
    sell_txns_3d BIGINT NOT NULL,
    usd_volume_3d DOUBLE PRECISION NOT NULL,
    usd_buy_volume_3d DOUBLE PRECISION NOT NULL,
    usd_sell_volume_3d DOUBLE PRECISION NOT NULL,

    -- 7d statistics
    trend_7d DOUBLE PRECISION NOT NULL,
    price_change_7d DOUBLE PRECISION NOT NULL,
    txns_7d BIGINT NOT NULL,
    buy_txns_7d BIGINT NOT NULL,
    sell_txns_7d BIGINT NOT NULL,
    usd_volume_7d DOUBLE PRECISION NOT NULL,
    usd_buy_volume_7d DOUBLE PRECISION NOT NULL,
    usd_sell_volume_7d DOUBLE PRECISION NOT NULL,

    -- meme statistics
    dev_hold_percentage DOUBLE PRECISION,
    dev_sold_percentage DOUBLE PRECISION,
    top10_hold_percentage DOUBLE PRECISION,
    sniper_hold_percentage DOUBLE PRECISION,
    insider_hold_percentage DOUBLE PRECISION,
    bot_hold_percentage DOUBLE PRECISION,
    holder_count BIGINT,
    sniper_count BIGINT,
    insider_count BIGINT,
    bot_count BIGINT,

    PRIMARY KEY (chain, token_address)
);
