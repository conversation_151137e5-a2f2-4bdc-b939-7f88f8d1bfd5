CREATE TABLE IF NOT EXISTS perp_state (
    perp_exchange perp_exchange_enum NOT NULL,
    perp_id VARCHAR NOT NULL,

    funding DOUBLE PRECISION NOT NULL,
    open_interest DOUBLE PRECISION NOT NULL,
    premium VARCHAR,
    oracle_px DOUBLE PRECISION NOT NULL,
    impact_pxs DOUBLE PRECISION[],
    day_base_vlm DOUBLE PRECISION NOT NULL,
    day_ntl_vlm DOUBLE PRECISION NOT NULL,

    mark_px DOUBLE PRECISION NOT NULL,
    mid_px DOUBLE PRECISION,
    market_cap DOUBLE PRECISION NOT NULL,
    liquidity DOUBLE PRECISION NOT NULL,
    fdv DOUBLE PRECISION NOT NULL,

    updated_at_millis BIGINT NOT NULL,

    PRIMARY KEY (perp_exchange, perp_id)
);

CREATE INDEX idx_perp_state_exchange ON perp_state (perp_exchange);
CREATE INDEX idx_perp_state_perp_id ON perp_state (perp_id);
CREATE INDEX idx_perp_state_mark_px ON perp_state (mark_px);
CREATE INDEX idx_perp_state_market_cap ON perp_state (market_cap);
CREATE INDEX idx_perp_state_liquidity ON perp_state (liquidity);
CREATE INDEX idx_perp_state_fdv ON perp_state (fdv);
CREATE INDEX idx_perp_state_updated_at ON perp_state (updated_at_millis DESC);
