CREATE TABLE IF NOT EXISTS token_metadata (
    chain chain_enum NOT NULL,
    address VARCHAR NOT NULL,

    name <PERSON><PERSON><PERSON>R NOT NULL,
    symbol VARCHAR NOT NULL,

    decimals SMALLINT NOT NULL CHECK (decimals >= 0 AND decimals <= 255),
    supply NUMERIC(78,0) NOT NULL, -- UInt256 equivalent

    -- Additional info
    description VARCHAR, -- nullable
    image VARCHAR, -- nullable
    website VARCHAR, -- nullable
    twitter VARCHAR, -- nullable
    telegram VARCHAR, -- nullable
    dex_paid dex_paid_enum NOT NULL,

    is_trench_token BOOLEAN NOT NULL,

    -- Creation info
    create_dex dex_enum NOT NULL DEFAULT 'unknown',
    create_block_number BIGINT,
    create_tx_hash VARCHAR,
    create_bonding_curve VARCHAR,
    create_dev VARCHAR,
    create_timestamp_millis BIGINT NOT NULL DEFAULT 0,

    -- Migration info
    migration_pool_address VARCHAR,
    migration_timestamp_millis BIGINT NOT NULL DEFAULT 0,

    update_timestamp_millis BIGINT NOT NULL,

    -- Solana specific info
    uri VARCHAR,
    seller_fee_basis_points SMALLINT,
    creators VARCHAR[],
    primary_sale_happened BOOLEAN,
    is_mutable BOOLEAN,
    update_authority VARCHAR,
    mint_authority VARCHAR,
    freeze_authority VARCHAR,

    PRIMARY KEY (chain, address)
);