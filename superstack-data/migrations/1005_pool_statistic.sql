CREATE TABLE IF NOT EXISTS pool_statistic (
    chain chain_enum NOT NULL,
    pool_address VARCHAR NOT NULL,

    pair_label VARCHAR NOT NULL,
    dex dex_enum NOT NULL,
    pool_type pool_type_enum NOT NULL,

    create_timestamp_millis BIGINT NOT NULL DEFAULT 0,

    token_address VARCHAR NOT NULL,
    token_decimals SMALLINT NOT NULL CHECK (token_decimals >= 0 AND token_decimals <= 255),
    base_address VARCHAR NOT NULL,
    base_decimals SMALLINT NOT NULL CHECK (base_decimals >= 0 AND base_decimals <= 255),
    is_token_first BOOLEAN NOT NULL,

    is_active BOOLEAN NOT NULL,

    -- latest pool state info based on usd
    usd_price DOUBLE PRECISION NOT NULL,
    usd_market_cap DOUBLE PRECISION NOT NULL,
    usd_liquidity DOUBLE PRECISION NOT NULL,

    bonding_curve_progress DOUBLE PRECISION,

    -- original pool state info used for token state
    price DOUBLE PRECISION NOT NULL,
    market_cap DOUBLE PRECISION NOT NULL,
    liquidity DOUBLE PRECISION NOT NULL,
    total_volume DOUBLE PRECISION NOT NULL,
    total_buy_volume DOUBLE PRECISION NOT NULL,
    total_sell_volume DOUBLE PRECISION NOT NULL,
    total_txns BIGINT NOT NULL,
    total_buy_txns BIGINT NOT NULL,
    total_sell_txns BIGINT NOT NULL,

    -- 5m statistics
    price_change_5m DOUBLE PRECISION NOT NULL,
    txns_5m BIGINT NOT NULL,
    buy_txns_5m BIGINT NOT NULL,
    sell_txns_5m BIGINT NOT NULL,
    usd_volume_5m DOUBLE PRECISION NOT NULL,
    usd_buy_volume_5m DOUBLE PRECISION NOT NULL,
    usd_sell_volume_5m DOUBLE PRECISION NOT NULL,
    makers_5m BIGINT NOT NULL,
    buyers_5m BIGINT NOT NULL,
    sellers_5m BIGINT NOT NULL,

    -- 1h statistics
    price_change_1h DOUBLE PRECISION NOT NULL,
    txns_1h BIGINT NOT NULL,
    buy_txns_1h BIGINT NOT NULL,
    sell_txns_1h BIGINT NOT NULL,
    usd_volume_1h DOUBLE PRECISION NOT NULL,
    usd_buy_volume_1h DOUBLE PRECISION NOT NULL,
    usd_sell_volume_1h DOUBLE PRECISION NOT NULL,
    makers_1h BIGINT NOT NULL,
    buyers_1h BIGINT NOT NULL,
    sellers_1h BIGINT NOT NULL,

    -- 6h statistics
    price_change_6h DOUBLE PRECISION NOT NULL,
    txns_6h BIGINT NOT NULL,
    buy_txns_6h BIGINT NOT NULL,
    sell_txns_6h BIGINT NOT NULL,
    usd_volume_6h DOUBLE PRECISION NOT NULL,
    usd_buy_volume_6h DOUBLE PRECISION NOT NULL,
    usd_sell_volume_6h DOUBLE PRECISION NOT NULL,
    makers_6h BIGINT NOT NULL,
    buyers_6h BIGINT NOT NULL,
    sellers_6h BIGINT NOT NULL,

    -- 24h statistics
    price_change_24h DOUBLE PRECISION NOT NULL,
    txns_24h BIGINT NOT NULL,
    buy_txns_24h BIGINT NOT NULL,
    sell_txns_24h BIGINT NOT NULL,
    usd_volume_24h DOUBLE PRECISION NOT NULL,
    usd_buy_volume_24h DOUBLE PRECISION NOT NULL,
    usd_sell_volume_24h DOUBLE PRECISION NOT NULL,
    makers_24h BIGINT NOT NULL,
    buyers_24h BIGINT NOT NULL,
    sellers_24h BIGINT NOT NULL,

    -- update info
    update_timestamp_millis BIGINT NOT NULL,

    PRIMARY KEY (chain, pool_address)
);
