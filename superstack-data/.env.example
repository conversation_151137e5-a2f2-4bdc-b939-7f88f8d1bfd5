SOLANA_RPC_URL=https://rpc.test.superstack.xyz

POSTGRES_INDEXER_DATABASE_URL=postgresql://superstack-indexer:password@127.0.0.1:5432/superstack-indexer
POSTGRES_INDEXER_MAX_CONNECTIONS=300
POSTGRES_INDEXER_NEED_MIGRATE=true

# Only needed for dev to prepare sql, ignore it in prod
DATABASE_URL=postgresql://superstack-indexer:password@127.0.0.1:5432/superstack-indexer

STORE_PRICE_HISTORY=true

REDIS_URL=rediss://default:<EMAIL>:14053

KAFKA_BOOTSTRAP_SERVERS=pkc-ldvr1.asia-southeast1.gcp.confluent.cloud:9092
KAFKA_API_KEY=KV7EDZDBGVNCBR32
KAFKA_API_SECRET=FZNQulI7Rs77h+XaHPylbXdWLZzC2FMiYoUG58vGuUemIiHAAN7UVBqeIRFWz5jq
# KAFKA_INDEXER_TOPIC_PARTITION=12
# KAFKA_AGGREGATOR_TOPIC_PARTITION=1

COINGECKO_API_KEY=CG-T2Dk49gHP6uEZE1cotuJCezy
