{"db_name": "PostgreSQL", "query": "\n            SELECT\n                chain AS \"chain: Chain\",\n                pool_address,\n                block_number, timestamp_millis,\n                price, market_cap, liquidity,\n                total_volume, total_buy_volume, total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress\n            FROM pool_state\n            WHERE chain = $1 AND pool_address = $2\n                AND timestamp_millis >= $3 AND timestamp_millis <= $4\n            ORDER BY timestamp_millis ASC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "block_number", "type_info": "Int8"}, {"ordinal": 3, "name": "timestamp_millis", "type_info": "Int8"}, {"ordinal": 4, "name": "price", "type_info": "Float8"}, {"ordinal": 5, "name": "market_cap", "type_info": "Float8"}, {"ordinal": 6, "name": "liquidity", "type_info": "Float8"}, {"ordinal": 7, "name": "total_volume", "type_info": "Float8"}, {"ordinal": 8, "name": "total_buy_volume", "type_info": "Float8"}, {"ordinal": 9, "name": "total_sell_volume", "type_info": "Float8"}, {"ordinal": 10, "name": "total_txns", "type_info": "Int8"}, {"ordinal": 11, "name": "total_buy_txns", "type_info": "Int8"}, {"ordinal": 12, "name": "total_sell_txns", "type_info": "Int8"}, {"ordinal": 13, "name": "bonding_curve_progress", "type_info": "Float8"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text", "Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, true]}, "hash": "214b9ba58e6d2ec65f35d6a18fb5f20b1685481775263ffdac5a1a418429c4bc"}