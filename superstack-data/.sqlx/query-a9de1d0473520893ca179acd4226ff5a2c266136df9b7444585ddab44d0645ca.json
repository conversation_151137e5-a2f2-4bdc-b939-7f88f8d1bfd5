{"db_name": "PostgreSQL", "query": "\n            INSERT INTO token_metadata (\n                chain, address,\n                name, symbol,\n                decimals, supply,\n                description, image,\n                website, twitter, telegram,\n                dex_paid,\n                is_trench_token,\n                create_dex, create_block_number, create_tx_hash,\n                create_bonding_curve, create_dev,\n                create_timestamp_millis,\n                migration_pool_address, migration_timestamp_millis,\n                update_timestamp_millis,\n                uri, seller_fee_basis_points,\n                creators, primary_sale_happened,\n                is_mutable,\n                update_authority, mint_authority, freeze_authority,\n                is_active, image_path\n            )\n            VALUES (\n                $1, $2,\n                $3, $4,\n                $5, $6,\n                $7, $8,\n                $9, $10, $11,\n                $12,\n                $13,\n                $14, $15, $16,\n                $17, $18,\n                $19,\n                $20, $21,\n                $22,\n                $23, $24,\n                $25, $26,\n                $27,\n                $28, $29, $30,\n                $31, $32\n            )\n            ON CONFLICT (chain, address) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Numeric", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", {"Custom": {"name": "dex_paid_enum", "kind": {"Enum": ["unknown", "paid", "unpaid"]}}}, "Bool", {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}, "Int8", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Varchar<PERSON><PERSON>y", "Bool", "Bool", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bool", "<PERSON><PERSON><PERSON><PERSON>"]}, "nullable": []}, "hash": "a9de1d0473520893ca179acd4226ff5a2c266136df9b7444585ddab44d0645ca"}