{"db_name": "PostgreSQL", "query": "\n            SELECT\n                perp_exchange AS \"perp_exchange: PerpExchange\",\n                perp_id,\n                max_leverage,\n                only_isolated,\n                sz_decimals,\n                funding,\n                open_interest,\n                premium,\n                oracle_px,\n                impact_pxs,\n                day_base_vlm,\n                day_ntl_vlm,\n                mark_px,\n                mid_px,\n                prev_day_px,\n                market_cap,\n                liquidity,\n                fdv,\n                updated_at_millis\n            FROM perp_state\n            WHERE perp_exchange = $1\n            ORDER BY market_cap DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "perp_exchange: PerpExchange", "type_info": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}, {"ordinal": 1, "name": "perp_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "max_leverage", "type_info": "Int2"}, {"ordinal": 3, "name": "only_isolated", "type_info": "Bool"}, {"ordinal": 4, "name": "sz_decimals", "type_info": "Int2"}, {"ordinal": 5, "name": "funding", "type_info": "Float8"}, {"ordinal": 6, "name": "open_interest", "type_info": "Float8"}, {"ordinal": 7, "name": "premium", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "oracle_px", "type_info": "Float8"}, {"ordinal": 9, "name": "impact_pxs", "type_info": "Float8Array"}, {"ordinal": 10, "name": "day_base_vlm", "type_info": "Float8"}, {"ordinal": 11, "name": "day_ntl_vlm", "type_info": "Float8"}, {"ordinal": 12, "name": "mark_px", "type_info": "Float8"}, {"ordinal": 13, "name": "mid_px", "type_info": "Float8"}, {"ordinal": 14, "name": "prev_day_px", "type_info": "Float8"}, {"ordinal": 15, "name": "market_cap", "type_info": "Float8"}, {"ordinal": 16, "name": "liquidity", "type_info": "Float8"}, {"ordinal": 17, "name": "fdv", "type_info": "Float8"}, {"ordinal": 18, "name": "updated_at_millis", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}]}, "nullable": [false, false, false, false, false, false, false, true, false, true, false, false, false, true, false, false, false, false, false]}, "hash": "d467047d5f4217400ae5aaa63477bd22f04d445dbcf7fa5b48898cefaf7863c3"}