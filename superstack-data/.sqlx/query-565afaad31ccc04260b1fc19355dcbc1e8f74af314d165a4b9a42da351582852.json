{"db_name": "PostgreSQL", "query": "\n            SELECT \n                chain AS \"chain: Chain\",\n                token_address,\n                timestamp_millis,\n                best_pool_address,\n                usd_price, usd_market_cap,\n                native_price,\n                native_token_liquidity, usd_token_liquidity,\n                total_native_token_volume, total_usd_token_volume,\n                total_native_token_buy_volume, total_usd_token_buy_volume,\n                total_native_token_sell_volume, total_usd_token_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress,\n                best_pool_dex AS \"best_pool_dex: Dex\"\n            FROM token_state\n            WHERE chain = $1 AND token_address = $2\n            ORDER BY timestamp_millis ASC\n            LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "token_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "timestamp_millis", "type_info": "Int8"}, {"ordinal": 3, "name": "best_pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "usd_price", "type_info": "Float8"}, {"ordinal": 5, "name": "usd_market_cap", "type_info": "Float8"}, {"ordinal": 6, "name": "native_price", "type_info": "Float8"}, {"ordinal": 7, "name": "native_token_liquidity", "type_info": "Float8"}, {"ordinal": 8, "name": "usd_token_liquidity", "type_info": "Float8"}, {"ordinal": 9, "name": "total_native_token_volume", "type_info": "Float8"}, {"ordinal": 10, "name": "total_usd_token_volume", "type_info": "Float8"}, {"ordinal": 11, "name": "total_native_token_buy_volume", "type_info": "Float8"}, {"ordinal": 12, "name": "total_usd_token_buy_volume", "type_info": "Float8"}, {"ordinal": 13, "name": "total_native_token_sell_volume", "type_info": "Float8"}, {"ordinal": 14, "name": "total_usd_token_sell_volume", "type_info": "Float8"}, {"ordinal": 15, "name": "total_txns", "type_info": "Int8"}, {"ordinal": 16, "name": "total_buy_txns", "type_info": "Int8"}, {"ordinal": 17, "name": "total_sell_txns", "type_info": "Int8"}, {"ordinal": 18, "name": "bonding_curve_progress", "type_info": "Float8"}, {"ordinal": 19, "name": "best_pool_dex: <PERSON>", "type_info": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text"]}, "nullable": [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, true, false]}, "hash": "565afaad31ccc04260b1fc19355dcbc1e8f74af314d165a4b9a42da351582852"}