{"db_name": "PostgreSQL", "query": "\n            INSERT INTO pool_state (\n                chain, pool_address, block_number, timestamp_millis,\n                price, market_cap, liquidity,\n                total_volume, total_buy_volume, total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress)\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[], $3::bigint[], $4::bigint[],\n                $5::double precision[], $6::double precision[], $7::double precision[],\n                $8::double precision[], $9::double precision[], $10::double precision[],\n                $11::bigint[], $12::bigint[], $13::bigint[],\n                $14::double precision[])\n            ON CONFLICT (chain, pool_address, block_number) DO UPDATE SET\n                price = EXCLUDED.price,\n                market_cap = EXCLUDED.market_cap,\n                liquidity = EXCLUDED.liquidity,\n                total_volume = EXCLUDED.total_volume,\n                total_buy_volume = EXCLUDED.total_buy_volume,\n                total_sell_volume = EXCLUDED.total_sell_volume,\n                total_txns = EXCLUDED.total_txns,\n                total_buy_txns = EXCLUDED.total_buy_txns,\n                total_sell_txns = EXCLUDED.total_sell_txns,\n                bonding_curve_progress = EXCLUDED.bonding_curve_progress\n            WHERE pool_state.total_txns < EXCLUDED.total_txns\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array"]}, "nullable": []}, "hash": "89dfa84df8b24a892aaeb38849a45c882faf85cbd0bcc33a9daf954cb4df3acd"}