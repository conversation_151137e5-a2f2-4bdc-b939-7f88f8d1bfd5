{"db_name": "PostgreSQL", "query": "\n            INSERT INTO perp_info (\n                perp_exchange, perp_id,\n                is_native_token, network, address,\n                name, symbol,\n                socials,\n                total_supply, circulating_supply,\n                update_timestamp_millis)\n            SELECT * FROM unnest(\n                $1::perp_exchange_enum[], $2::varchar[],\n                $3::boolean[], $4::varchar[], $5::varchar[],\n                $6::varchar[], $7::varchar[],\n                $8::jsonb[],\n                $9::numeric[], $10::numeric[],\n                $11::bigint[])\n            ON CONFLICT (perp_exchange, perp_id) DO UPDATE SET\n                is_native_token = EXCLUDED.is_native_token,\n                network = EXCLUDED.network,\n                address = EXCLUDED.address,\n                name = EXCLUDED.name,\n                symbol = EXCLUDED.symbol,\n                socials = EXCLUDED.socials,\n                total_supply = EXCLUDED.total_supply,\n                circulating_supply = EXCLUDED.circulating_supply,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis\n            WHERE perp_info.update_timestamp_millis <= EXCLUDED.update_timestamp_millis\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum[]", "kind": {"Array": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}}}, "Varchar<PERSON><PERSON>y", "BoolArray", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "JsonbArray", "NumericArray", "NumericArray", "Int8Array"]}, "nullable": []}, "hash": "179432b89701beadebd657f46b0561b3008f7aff9c0e7b56e8389f4ef7a05cff"}