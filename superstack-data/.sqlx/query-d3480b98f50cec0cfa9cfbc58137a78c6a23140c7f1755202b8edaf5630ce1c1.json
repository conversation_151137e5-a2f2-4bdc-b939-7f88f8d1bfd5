{"db_name": "PostgreSQL", "query": "\n            INSERT INTO pool_statistic (\n                chain, pool_address,\n                pair_label, dex, pool_type,\n                create_timestamp_millis,\n                token_address, token_decimals,\n                base_address, base_decimals,\n                is_token_first,\n                is_active,\n                usd_price, usd_market_cap, usd_liquidity,\n                bonding_curve_progress,\n                price, market_cap, liquidity,\n                total_volume, total_buy_volume, total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m, makers_5m, buyers_5m, sellers_5m,\n                price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h, makers_1h, buyers_1h, sellers_1h,\n                price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h, makers_6h, buyers_6h, sellers_6h,\n                price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h, makers_24h, buyers_24h, sellers_24h,\n                update_timestamp_millis\n            )\n            VALUES (\n                $1, $2,\n                $3, $4, $5,\n                $6,\n                $7, $8,\n                $9, $10,\n                $11,\n                $12,\n                $13, $14, $15, \n                $16,\n                $17, $18, $19,\n                $20, $21, $22,\n                $23, $24, $25,\n                $26, $27, $28, $29, $30, $31, $32, $33, $34, $35,\n                $36, $37, $38, $39, $40, $41, $42, $43, $44, $45,\n                $46, $47, $48, $49, $50, $51, $52, $53, $54, $55,\n                $56, $57, $58, $59, $60, $61, $62, $63, $64, $65,\n                $66\n            )\n            ON CONFLICT (chain, pool_address) DO UPDATE SET\n                pair_label = EXCLUDED.pair_label,\n                dex = EXCLUDED.dex,\n                pool_type = EXCLUDED.pool_type,\n                create_timestamp_millis = EXCLUDED.create_timestamp_millis,\n                token_address = EXCLUDED.token_address,\n                token_decimals = EXCLUDED.token_decimals,\n                base_address = EXCLUDED.base_address,\n                base_decimals = EXCLUDED.base_decimals,\n                is_token_first = EXCLUDED.is_token_first,\n                is_active = EXCLUDED.is_active,\n                usd_price = EXCLUDED.usd_price,\n                usd_market_cap = EXCLUDED.usd_market_cap,\n                usd_liquidity = EXCLUDED.usd_liquidity,\n                bonding_curve_progress = EXCLUDED.bonding_curve_progress,\n                price = EXCLUDED.price,\n                market_cap = EXCLUDED.market_cap,\n                liquidity = EXCLUDED.liquidity,\n                total_volume = EXCLUDED.total_volume,\n                total_buy_volume = EXCLUDED.total_buy_volume,\n                total_sell_volume = EXCLUDED.total_sell_volume,\n                total_txns = EXCLUDED.total_txns,\n                total_buy_txns = EXCLUDED.total_buy_txns,\n                total_sell_txns = EXCLUDED.total_sell_txns,\n                price_change_5m = EXCLUDED.price_change_5m,\n                txns_5m = EXCLUDED.txns_5m,\n                buy_txns_5m = EXCLUDED.buy_txns_5m,\n                sell_txns_5m = EXCLUDED.sell_txns_5m,\n                usd_volume_5m = EXCLUDED.usd_volume_5m,\n                usd_buy_volume_5m = EXCLUDED.usd_buy_volume_5m,\n                usd_sell_volume_5m = EXCLUDED.usd_sell_volume_5m,\n                makers_5m = EXCLUDED.makers_5m,\n                buyers_5m = EXCLUDED.buyers_5m,\n                sellers_5m = EXCLUDED.sellers_5m,\n                price_change_1h = EXCLUDED.price_change_1h,\n                txns_1h = EXCLUDED.txns_1h,\n                buy_txns_1h = EXCLUDED.buy_txns_1h,\n                sell_txns_1h = EXCLUDED.sell_txns_1h,\n                usd_volume_1h = EXCLUDED.usd_volume_1h,\n                usd_buy_volume_1h = EXCLUDED.usd_buy_volume_1h,\n                usd_sell_volume_1h = EXCLUDED.usd_sell_volume_1h,\n                makers_1h = EXCLUDED.makers_1h, \n                buyers_1h = EXCLUDED.buyers_1h,\n                sellers_1h = EXCLUDED.sellers_1h,\n                price_change_6h = EXCLUDED.price_change_6h,\n                txns_6h = EXCLUDED.txns_6h,\n                buy_txns_6h = EXCLUDED.buy_txns_6h,\n                sell_txns_6h = EXCLUDED.sell_txns_6h,\n                usd_volume_6h = EXCLUDED.usd_volume_6h,\n                usd_buy_volume_6h = EXCLUDED.usd_buy_volume_6h,\n                usd_sell_volume_6h = EXCLUDED.usd_sell_volume_6h,\n                makers_6h = EXCLUDED.makers_6h,\n                buyers_6h = EXCLUDED.buyers_6h,\n                sellers_6h = EXCLUDED.sellers_6h,\n                price_change_24h = EXCLUDED.price_change_24h,\n                txns_24h = EXCLUDED.txns_24h,\n                buy_txns_24h = EXCLUDED.buy_txns_24h,\n                sell_txns_24h = EXCLUDED.sell_txns_24h,\n                usd_volume_24h = EXCLUDED.usd_volume_24h,\n                usd_buy_volume_24h = EXCLUDED.usd_buy_volume_24h,\n                usd_sell_volume_24h = EXCLUDED.usd_sell_volume_24h,\n                makers_24h = EXCLUDED.makers_24h,\n                buyers_24h = EXCLUDED.buyers_24h,\n                sellers_24h = EXCLUDED.sellers_24h,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis\n            WHERE pool_statistic.update_timestamp_millis <= EXCLUDED.update_timestamp_millis\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}, {"Custom": {"name": "pool_type_enum", "kind": {"Enum": ["none", "dlmm"]}}}, "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Bool", "Bool", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Int8"]}, "nullable": []}, "hash": "d3480b98f50cec0cfa9cfbc58137a78c6a23140c7f1755202b8edaf5630ce1c1"}