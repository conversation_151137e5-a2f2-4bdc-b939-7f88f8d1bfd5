{"db_name": "PostgreSQL", "query": "\n            INSERT INTO perp_user_state (\n                perp_exchange, perp_user,\n                perps_account_value, account_value,\n                hyperliquid_acc_collateral, hyperliquid_acc_perps,\n                timestamp_millis\n            )\n            VALUES ($1, $2, $3, $4, $5, $6, $7)\n            ON CONFLICT (perp_exchange, perp_user, timestamp_millis) DO UPDATE SET\n                perps_account_value = EXCLUDED.perps_account_value,\n                account_value = EXCLUDED.account_value,\n                hyperliquid_acc_collateral = EXCLUDED.hyperliquid_acc_collateral,\n                hyperliquid_acc_perps = EXCLUDED.hyperliquid_acc_perps\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Float8", "Float8", "Float8", "Float8", "Int8"]}, "nullable": []}, "hash": "b0cc27bb5890288b3acbe816c46587a2dacde0bfdc1ad2fce69cea472d00eea3"}