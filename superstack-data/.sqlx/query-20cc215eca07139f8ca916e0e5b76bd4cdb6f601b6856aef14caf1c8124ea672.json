{"db_name": "PostgreSQL", "query": "\n            INSERT INTO token_state (\n                chain, token_address, timestamp_millis,\n                best_pool_address,\n                usd_price, usd_market_cap,\n                native_price,\n                native_token_liquidity, usd_token_liquidity,\n                total_native_token_volume, total_usd_token_volume,\n                total_native_token_buy_volume, total_usd_token_buy_volume,\n                total_native_token_sell_volume, total_usd_token_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress,\n                best_pool_dex\n            )\n            VALUES (\n                $1, $2, $3,\n                $4,\n                $5, $6,\n                $7,\n                $8, $9,\n                $10, $11,\n                $12, $13,\n                $14, $15,\n                $16, $17, $18,\n                $19,\n                $20\n            )\n            ON CONFLICT (chain, token_address, timestamp_millis) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}]}, "nullable": []}, "hash": "20cc215eca07139f8ca916e0e5b76bd4cdb6f601b6856aef14caf1c8124ea672"}