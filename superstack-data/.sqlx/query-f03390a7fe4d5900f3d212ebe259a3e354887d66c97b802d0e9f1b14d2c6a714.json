{"db_name": "PostgreSQL", "query": "\n            INSERT INTO pool_metadata (\n                chain, pool_address,\n                pair_label, dex, pool_type,\n                create_timestamp_millis, update_timestamp_millis,\n                token_address, token_decimals,\n                base_address, base_decimals,\n                is_token_first,\n                is_active,\n                bin_step\n            )\n            VALUES (\n                $1, $2,\n                $3, $4, $5,\n                $6, $7,\n                $8, $9,\n                $10, $11,\n                $12,\n                $13,\n                $14\n            )\n            ON CONFLICT (chain, pool_address) DO UPDATE SET\n                create_timestamp_millis = EXCLUDED.create_timestamp_millis\n            WHERE pool_metadata.create_timestamp_millis < EXCLUDED.create_timestamp_millis\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}, {"Custom": {"name": "pool_type_enum", "kind": {"Enum": ["none", "dlmm"]}}}, "Int8", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Bool", "Bool", "Int2"]}, "nullable": []}, "hash": "f03390a7fe4d5900f3d212ebe259a3e354887d66c97b802d0e9f1b14d2c6a714"}