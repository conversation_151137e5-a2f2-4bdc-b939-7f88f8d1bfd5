{"db_name": "PostgreSQL", "query": "\n            SELECT\n                chain AS \"chain: Chain\",\n                token_address,\n                holder_address,\n                bought_ui_amount,\n                sold_ui_amount,\n                remaining_ui_amount,\n                bought_txns,\n                sold_txns,\n                spent_usd,\n                received_usd,\n                pnl_usd,\n                update_timestamp_millis,\n                update_block_number,\n                native_token_balance,\n                maker_volume_type AS \"maker_volume_type: MakerVolumeType\",\n                maker_trade_type AS \"maker_trade_type: MakerTradeType\"\n            FROM extended_token_holder\n            WHERE chain = $1 AND token_address = $2 AND remaining_ui_amount > 0\n            ORDER BY remaining_ui_amount DESC\n            LIMIT $3 OFFSET $4\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "token_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "holder_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "bought_ui_amount", "type_info": "Float8"}, {"ordinal": 4, "name": "sold_ui_amount", "type_info": "Float8"}, {"ordinal": 5, "name": "remaining_ui_amount", "type_info": "Float8"}, {"ordinal": 6, "name": "bought_txns", "type_info": "Int8"}, {"ordinal": 7, "name": "sold_txns", "type_info": "Int8"}, {"ordinal": 8, "name": "spent_usd", "type_info": "Float8"}, {"ordinal": 9, "name": "received_usd", "type_info": "Float8"}, {"ordinal": 10, "name": "pnl_usd", "type_info": "Float8"}, {"ordinal": 11, "name": "update_timestamp_millis", "type_info": "Int8"}, {"ordinal": 12, "name": "update_block_number", "type_info": "Int8"}, {"ordinal": 13, "name": "native_token_balance", "type_info": "Float8"}, {"ordinal": 14, "name": "maker_volume_type: MakerVolumeType", "type_info": {"Custom": {"name": "maker_volume_type_enum", "kind": {"Enum": ["plankton", "fish", "shrimp", "dolphin", "whale"]}}}}, {"ordinal": 15, "name": "maker_trade_type: MakerTradeType", "type_info": {"Custom": {"name": "maker_trade_type_enum", "kind": {"Enum": ["none", "dev", "sniper", "bot", "insider"]}}}}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text", "Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "a8463fb46a915fcfe4210a8ae3764422032ee9b3721d1283241b7b0b0c7d3d76"}