{"db_name": "PostgreSQL", "query": "\n            INSERT INTO perp_state_series (\n                perp_exchange, perp_id,\n                funding, open_interest, mark_px, market_cap,\n                long_ntl, short_ntl, long_traders, short_traders, long_entry, short_entry,\n                created_at_millis\n            )\n            VALUES (\n                $1, $2,\n                $3, $4, $5, $6,\n                $7, $8, $9, $10, $11, $12,\n                $13\n            )\n        ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Float8", "Float8", "Int8"]}, "nullable": []}, "hash": "83c02cb7c22d17eb0277ca419bbb099e798dc5da5d2f534d75d184f7395b49bf"}