{"db_name": "PostgreSQL", "query": "\n                    SELECT\n                        perp_exchange as \"perp_exchange: PerpExchange\",\n                        perp_user,\n                        perps_account_value,\n                        account_value,\n                        hyperliquid_acc_collateral,\n                        hyperliquid_acc_perps,\n                        timestamp_millis\n                    FROM perp_user_state\n                    WHERE perp_exchange = $1 AND perp_user = $2\n                        AND timestamp_millis >= $3 AND timestamp_millis <= $4\n                    ORDER BY timestamp_millis DESC\n                    LIMIT $5\n                    ", "describe": {"columns": [{"ordinal": 0, "name": "perp_exchange: PerpExchange", "type_info": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}, {"ordinal": 1, "name": "perp_user", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "perps_account_value", "type_info": "Float8"}, {"ordinal": 3, "name": "account_value", "type_info": "Float8"}, {"ordinal": 4, "name": "hyperliquid_acc_collateral", "type_info": "Float8"}, {"ordinal": 5, "name": "hyperliquid_acc_perps", "type_info": "Float8"}, {"ordinal": 6, "name": "timestamp_millis", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "Text", "Int8", "Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false]}, "hash": "386dc3352f0f03c8d2c02403eb1039b1f5153076c7b28bc153a0fa93b73e463c"}