{"db_name": "PostgreSQL", "query": "\n            INSERT INTO pool_state (\n                chain, pool_address,\n                block_number, timestamp_millis,\n                price, market_cap, liquidity,\n                total_volume, total_buy_volume, total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress\n            )\n            VALUES (\n                $1, $2,\n                $3, $4,\n                $5, $6, $7,\n                $8, $9, $10,\n                $11, $12, $13,\n                $14\n            )\n            ON CONFLICT (chain, pool_address, block_number) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8"]}, "nullable": []}, "hash": "988a0a4a9447fc555f23557fc0c56ab0893e3fcb61ecc221c6dc2182764eac48"}