{"db_name": "PostgreSQL", "query": "\n            SELECT\n                chain as \"chain: Chain\",\n                block_number\n            FROM chain_info\n            WHERE chain = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "block_number", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}]}, "nullable": [false, false]}, "hash": "8a1296a88c7923f7d823790111e86c449f35d2fb66b92075cce0cfd782c78cb2"}