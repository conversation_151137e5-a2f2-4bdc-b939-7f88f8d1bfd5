{"db_name": "PostgreSQL", "query": "\n            INSERT INTO perp_info (\n                perp_exchange, perp_id,\n                is_native_token, network, address,\n                name, symbol,\n                socials,\n                total_supply, circulating_supply,\n                update_timestamp_millis\n            )\n            VALUES (\n                $1, $2,\n                $3, $4, $5,\n                $6, $7,\n                $8,\n                $9, $10,\n                $11\n            )\n            ON CONFLICT (perp_exchange, perp_id) DO UPDATE SET\n                is_native_token = EXCLUDED.is_native_token,\n                network = EXCLUDED.network,\n                address = EXCLUDED.address,\n                name = EXCLUDED.name,\n                symbol = EXCLUDED.symbol,\n                socials = EXCLUDED.socials,\n                total_supply = EXCLUDED.total_supply,\n                circulating_supply = EXCLUDED.circulating_supply,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis\n            WHERE perp_info.update_timestamp_millis <= EXCLUDED.update_timestamp_millis\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Bool", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jsonb", "Numeric", "Numeric", "Int8"]}, "nullable": []}, "hash": "19faeaeb8b64bcf6875a1b966cbb7d06538325dda89bab2f430863edcd6700cb"}