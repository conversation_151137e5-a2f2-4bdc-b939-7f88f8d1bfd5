{"db_name": "PostgreSQL", "query": "\n            INSERT INTO extended_token_holder (\n                chain, token_address, holder_address,\n                bought_ui_amount, sold_ui_amount, remaining_ui_amount,\n                bought_txns, sold_txns,\n                spent_usd, received_usd,\n                pnl_usd,\n                update_timestamp_millis, update_block_number,\n                native_token_balance,\n                maker_volume_type, maker_trade_type\n            )\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[], $3::varchar[],\n                $4::double precision[], $5::double precision[], $6::double precision[],\n                $7::bigint[], $8::bigint[],\n                $9::double precision[], $10::double precision[],\n                $11::double precision[],\n                $12::bigint[], $13::bigint[],\n                $14::double precision[],\n                $15::maker_volume_type_enum[], $16::maker_trade_type_enum[])\n            ON CONFLICT (chain, token_address, holder_address) DO UPDATE SET\n                bought_ui_amount = EXCLUDED.bought_ui_amount,\n                sold_ui_amount = EXCLUDED.sold_ui_amount,\n                remaining_ui_amount = EXCLUDED.remaining_ui_amount,\n                bought_txns = EXCLUDED.bought_txns,\n                sold_txns = EXCLUDED.sold_txns,\n                spent_usd = EXCLUDED.spent_usd,\n                received_usd = EXCLUDED.received_usd,\n                pnl_usd = EXCLUDED.pnl_usd,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis,\n                update_block_number = EXCLUDED.update_block_number,\n                native_token_balance = EXCLUDED.native_token_balance,\n                maker_volume_type = EXCLUDED.maker_volume_type,\n                maker_trade_type = EXCLUDED.maker_trade_type\n            WHERE extended_token_holder.update_timestamp_millis <= EXCLUDED.update_timestamp_millis\n                and extended_token_holder.update_block_number <= EXCLUDED.update_block_number\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Float8Array", {"Custom": {"name": "maker_volume_type_enum[]", "kind": {"Array": {"Custom": {"name": "maker_volume_type_enum", "kind": {"Enum": ["plankton", "fish", "shrimp", "dolphin", "whale"]}}}}}}, {"Custom": {"name": "maker_trade_type_enum[]", "kind": {"Array": {"Custom": {"name": "maker_trade_type_enum", "kind": {"Enum": ["none", "dev", "sniper", "bot", "insider"]}}}}}}]}, "nullable": []}, "hash": "e02016c12bd8ab32dbe2f24eb1f41aaa8f8494c272c79493794c7839af5e62c0"}