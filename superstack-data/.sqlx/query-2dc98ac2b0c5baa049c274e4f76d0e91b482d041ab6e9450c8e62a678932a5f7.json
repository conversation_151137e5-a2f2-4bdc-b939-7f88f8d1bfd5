{"db_name": "PostgreSQL", "query": "\n            INSERT INTO pool_state (\n                chain, pool_address, block_number, timestamp_millis,\n                price, market_cap, liquidity,\n                total_volume, total_buy_volume, total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress\n            )\n            VALUES (\n                $1, $2, $3, $4,\n                $5, $6, $7,\n                $8, $9, $10,\n                $11, $12, $13,\n                $14\n            )\n            ON CONFLICT (chain, pool_address, block_number) DO UPDATE SET\n                price = EXCLUDED.price,\n                market_cap = EXCLUDED.market_cap,\n                liquidity = EXCLUDED.liquidity,\n                total_volume = EXCLUDED.total_volume,\n                total_buy_volume = EXCLUDED.total_buy_volume,\n                total_sell_volume = EXCLUDED.total_sell_volume,\n                total_txns = EXCLUDED.total_txns,\n                total_buy_txns = EXCLUDED.total_buy_txns,\n                total_sell_txns = EXCLUDED.total_sell_txns,\n                bonding_curve_progress = EXCLUDED.bonding_curve_progress\n            WHERE pool_state.total_txns < EXCLUDED.total_txns\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8"]}, "nullable": []}, "hash": "2dc98ac2b0c5baa049c274e4f76d0e91b482d041ab6e9450c8e62a678932a5f7"}