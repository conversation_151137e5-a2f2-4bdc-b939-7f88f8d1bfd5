{"db_name": "PostgreSQL", "query": "\n            INSERT INTO pool_state (\n                chain, pool_address, block_number, timestamp_millis,\n                price, market_cap, liquidity,\n                total_volume, total_buy_volume, total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress)\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[], $3::bigint[], $4::bigint[],\n                $5::double precision[], $6::double precision[], $7::double precision[],\n                $8::double precision[], $9::double precision[], $10::double precision[],\n                $11::bigint[], $12::bigint[], $13::bigint[],\n                $14::double precision[])\n            ON CONFLICT (chain, pool_address, block_number) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array"]}, "nullable": []}, "hash": "6a90ef7b9257262936c1462f909370793c27efe4ff42dc85763d7f438e7a058a"}