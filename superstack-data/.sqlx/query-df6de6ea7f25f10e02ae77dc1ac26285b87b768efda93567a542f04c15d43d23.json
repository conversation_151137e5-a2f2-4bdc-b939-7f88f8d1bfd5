{"db_name": "PostgreSQL", "query": "\n            SELECT \n                chain AS \"chain: Chain\",\n                token_address, holder_address,\n                bought_amount, sold_amount,\n                bought_txns, sold_txns,\n                spent_native_token_ui_amount, spent_usd_token_ui_amount,\n                total_spent_usd,\n                received_native_token_ui_amount, received_usd_token_ui_amount,\n                total_received_usd,\n                update_timestamp_millis, update_block_number\n            FROM token_holder\n            WHERE chain = $1 AND token_address = $2 AND holder_address = $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "token_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "holder_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "bought_amount", "type_info": "Numeric"}, {"ordinal": 4, "name": "sold_amount", "type_info": "Numeric"}, {"ordinal": 5, "name": "bought_txns", "type_info": "Int8"}, {"ordinal": 6, "name": "sold_txns", "type_info": "Int8"}, {"ordinal": 7, "name": "spent_native_token_ui_amount", "type_info": "Float8"}, {"ordinal": 8, "name": "spent_usd_token_ui_amount", "type_info": "Float8"}, {"ordinal": 9, "name": "total_spent_usd", "type_info": "Float8"}, {"ordinal": 10, "name": "received_native_token_ui_amount", "type_info": "Float8"}, {"ordinal": 11, "name": "received_usd_token_ui_amount", "type_info": "Float8"}, {"ordinal": 12, "name": "total_received_usd", "type_info": "Float8"}, {"ordinal": 13, "name": "update_timestamp_millis", "type_info": "Int8"}, {"ordinal": 14, "name": "update_block_number", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text", "Text"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "df6de6ea7f25f10e02ae77dc1ac26285b87b768efda93567a542f04c15d43d23"}