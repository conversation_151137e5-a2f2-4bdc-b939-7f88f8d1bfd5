{"db_name": "PostgreSQL", "query": "\n            SELECT \n                chain AS \"chain: Chain\",\n                tx_hash,\n                ix_idx,\n                pool_address,\n                maker_address,\n                is_buy,\n                token_ui_amount,\n                base_ui_amount,\n                usd,\n                usd_price,\n                usd_market_cap,\n                block_number,\n                timestamp_millis,\n                maker_volume_type AS \"maker_volume_type: MakerVolumeType\",\n                tx_idx\n            FROM extended_dex_trade\n            WHERE chain = $1 AND pool_address = $2\n            AND timestamp_millis >= $3 AND timestamp_millis <= $4\n            ORDER BY timestamp_millis DESC, block_number DESC, tx_idx DESC, ix_idx DESC\n            LIMIT $5\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "ix_idx", "type_info": "Int4"}, {"ordinal": 3, "name": "pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "maker_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "is_buy", "type_info": "Bool"}, {"ordinal": 6, "name": "token_ui_amount", "type_info": "Float8"}, {"ordinal": 7, "name": "base_ui_amount", "type_info": "Float8"}, {"ordinal": 8, "name": "usd", "type_info": "Float8"}, {"ordinal": 9, "name": "usd_price", "type_info": "Float8"}, {"ordinal": 10, "name": "usd_market_cap", "type_info": "Float8"}, {"ordinal": 11, "name": "block_number", "type_info": "Int8"}, {"ordinal": 12, "name": "timestamp_millis", "type_info": "Int8"}, {"ordinal": 13, "name": "maker_volume_type: MakerVolumeType", "type_info": {"Custom": {"name": "maker_volume_type_enum", "kind": {"Enum": ["plankton", "fish", "shrimp", "dolphin", "whale"]}}}}, {"ordinal": 14, "name": "tx_idx", "type_info": "Int4"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text", "Int8", "Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "88beb9da88139033763807c61f693bdcd7b46da5478a706972c6af1514b47a9b"}