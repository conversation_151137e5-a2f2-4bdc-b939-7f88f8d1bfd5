{"db_name": "PostgreSQL", "query": "\n            SELECT \n                chain AS \"chain: Chain\",\n                pool_address,\n                open_timestamp_seconds, close_timestamp_seconds,\n                interval AS \"interval: CandleInterval\",\n                native_token_usd_price,\n                usd_open_price, usd_close_price, usd_high_price, usd_low_price,\n                usd_open_market_cap, usd_close_market_cap, usd_high_market_cap, usd_low_market_cap,\n                usd_volume, txns\n            FROM candle\n            WHERE chain = $1 AND pool_address = $2 AND interval = $3 AND open_timestamp_seconds >= $4 AND open_timestamp_seconds <= $5\n            ORDER BY open_timestamp_seconds DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "open_timestamp_seconds", "type_info": "Int8"}, {"ordinal": 3, "name": "close_timestamp_seconds", "type_info": "Int8"}, {"ordinal": 4, "name": "interval: CandleInterval", "type_info": {"Custom": {"name": "candle_interval_enum", "kind": {"Enum": ["1s", "5s", "15s", "30s", "1m", "5m", "15m", "30m", "1h", "4h", "8h", "12h", "24h", "3d", "7d", "30d"]}}}}, {"ordinal": 5, "name": "native_token_usd_price", "type_info": "Float8"}, {"ordinal": 6, "name": "usd_open_price", "type_info": "Float8"}, {"ordinal": 7, "name": "usd_close_price", "type_info": "Float8"}, {"ordinal": 8, "name": "usd_high_price", "type_info": "Float8"}, {"ordinal": 9, "name": "usd_low_price", "type_info": "Float8"}, {"ordinal": 10, "name": "usd_open_market_cap", "type_info": "Float8"}, {"ordinal": 11, "name": "usd_close_market_cap", "type_info": "Float8"}, {"ordinal": 12, "name": "usd_high_market_cap", "type_info": "Float8"}, {"ordinal": 13, "name": "usd_low_market_cap", "type_info": "Float8"}, {"ordinal": 14, "name": "usd_volume", "type_info": "Float8"}, {"ordinal": 15, "name": "txns", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text", {"Custom": {"name": "candle_interval_enum", "kind": {"Enum": ["1s", "5s", "15s", "30s", "1m", "5m", "15m", "30m", "1h", "4h", "8h", "12h", "24h", "3d", "7d", "30d"]}}}, "Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "dd384c19840f25baa582f7830abbf0746dce05c58586b0e906cf76701c2c90de"}