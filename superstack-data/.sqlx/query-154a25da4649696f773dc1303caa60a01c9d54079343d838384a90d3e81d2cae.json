{"db_name": "PostgreSQL", "query": "\n            INSERT INTO perp_user_state (\n                perp_exchange, perp_user,\n                perps_account_value, account_value,\n                hyperliquid_acc_collateral, hyperliquid_acc_perps,\n                timestamp_millis\n            )\n            SELECT * FROM unnest(\n                $1::perp_exchange_enum[], $2::varchar[],\n                $3::double precision[], $4::double precision[],\n                $5::double precision[], $6::double precision[],\n                $7::bigint[]\n            )\n            ON CONFLICT (perp_exchange, perp_user, timestamp_millis) DO UPDATE SET\n                perps_account_value = EXCLUDED.perps_account_value,\n                account_value = EXCLUDED.account_value,\n                hyperliquid_acc_collateral = EXCLUDED.hyperliquid_acc_collateral,\n                hyperliquid_acc_perps = EXCLUDED.hyperliquid_acc_perps\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum[]", "kind": {"Array": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}}}, "Varchar<PERSON><PERSON>y", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array"]}, "nullable": []}, "hash": "154a25da4649696f773dc1303caa60a01c9d54079343d838384a90d3e81d2cae"}