{"db_name": "PostgreSQL", "query": "\n            WITH sampled_data AS (\n                SELECT DISTINCT ON (bucket)\n                    perp_exchange,\n                    perp_user,\n                    perps_account_value,\n                    account_value,\n                    hyperliquid_acc_collateral,\n                    hyperliquid_acc_perps,\n                    timestamp_millis,\n                    -- Create time buckets based on sample interval\n                    FLOOR(timestamp_millis / ($5 * 1000)) as bucket\n                FROM perp_user_state\n                WHERE perp_exchange = $1\n                    AND perp_user = $2\n                    AND timestamp_millis >= $3\n                    AND timestamp_millis <= $4\n                ORDER BY bucket, timestamp_millis ASC\n            )\n            SELECT\n                perp_exchange as \"perp_exchange: PerpExchange\",\n                perp_user,\n                perps_account_value,\n                account_value,\n                hyperliquid_acc_collateral,\n                hyperliquid_acc_perps,\n                timestamp_millis\n            FROM sampled_data\n            ORDER BY timestamp_millis ASC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "perp_exchange: PerpExchange", "type_info": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}, {"ordinal": 1, "name": "perp_user", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "perps_account_value", "type_info": "Float8"}, {"ordinal": 3, "name": "account_value", "type_info": "Float8"}, {"ordinal": 4, "name": "hyperliquid_acc_collateral", "type_info": "Float8"}, {"ordinal": 5, "name": "hyperliquid_acc_perps", "type_info": "Float8"}, {"ordinal": 6, "name": "timestamp_millis", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "Text", "Int8", "Int8", "Int4"]}, "nullable": [false, false, false, false, false, false, false]}, "hash": "a1c775e3a65c997749fb580821fccc9154eb152ead13a0f0045989f92e054b4e"}