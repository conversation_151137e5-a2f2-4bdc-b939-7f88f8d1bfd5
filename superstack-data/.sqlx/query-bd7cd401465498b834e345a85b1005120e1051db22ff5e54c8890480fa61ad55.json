{"db_name": "PostgreSQL", "query": "\n                INSERT INTO perp_state (\n                    perp_exchange, perp_id,\n                    max_leverage, only_isolated, sz_decimals,\n                    funding, open_interest, premium,\n                    oracle_px, impact_pxs,\n                    day_base_vlm, day_ntl_vlm,\n                    mark_px, mid_px, prev_day_px,\n                    market_cap, liquidity, fdv,\n                    updated_at_millis\n                )\n                VALUES (\n                    $1, $2,\n                    $3, $4, $5,\n                    $6, $7, $8,\n                    $9, $10,\n                    $11, $12,\n                    $13, $14, $15,\n                    $16, $17, $18,\n                    $19\n                )\n                ON CONFLICT (perp_exchange, perp_id) DO UPDATE SET\n                    max_leverage = EXCLUDED.max_leverage,\n                    only_isolated = EXCLUDED.only_isolated,\n                    sz_decimals = EXCLUDED.sz_decimals,\n                    funding = EXCLUDED.funding,\n                    open_interest = EXCLUDED.open_interest,\n                    premium = EXCLUDED.premium,\n                    oracle_px = EXCLUDED.oracle_px,\n                    impact_pxs = EXCLUDED.impact_pxs,\n                    day_base_vlm = EXCLUDED.day_base_vlm,\n                    day_ntl_vlm = EXCLUDED.day_ntl_vlm,\n                    mark_px = EXCLUDED.mark_px,\n                    mid_px = EXCLUDED.mid_px,\n                    prev_day_px = EXCLUDED.prev_day_px,\n                    market_cap = EXCLUDED.market_cap,\n                    liquidity = EXCLUDED.liquidity,\n                    fdv = EXCLUDED.fdv,\n                    updated_at_millis = EXCLUDED.updated_at_millis\n                WHERE perp_state.updated_at_millis <= EXCLUDED.updated_at_millis\n                ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Bool", "Int2", "Float8", "Float8", "<PERSON><PERSON><PERSON><PERSON>", "Float8", "Float8Array", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8"]}, "nullable": []}, "hash": "bd7cd401465498b834e345a85b1005120e1051db22ff5e54c8890480fa61ad55"}