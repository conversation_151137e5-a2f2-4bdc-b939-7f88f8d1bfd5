{"db_name": "PostgreSQL", "query": "\n            SELECT\n                chain AS \"chain: Chain\", token_address,\n                name, symbol,\n                decimals, supply,\n                description, image, website, twitter, telegram, dex_paid AS \"dex_paid: DexPaid\",\n                is_trench_token,\n                create_dex AS \"create_dex: Dex\", create_block_number, create_tx_hash, create_bonding_curve, create_dev, create_timestamp_millis,\n                migration_pool_address, migration_timestamp_millis,\n                update_timestamp_millis,\n                is_mutable, update_authority, mint_authority, freeze_authority,\n                best_pool_address, pool_addresses,\n                usd_price, usd_market_cap, usd_liquidity,\n                bonding_curve_progress,\n                usd_total_volume, usd_total_buy_volume, usd_total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                total_trend, total_price_change,\n                trend_5m, price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m,\n                trend_1h, price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h,\n                trend_6h, price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h,\n                trend_24h, price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h,\n                trend_3d, price_change_3d, txns_3d, buy_txns_3d, sell_txns_3d, usd_volume_3d, usd_buy_volume_3d, usd_sell_volume_3d,\n                trend_7d, price_change_7d, txns_7d, buy_txns_7d, sell_txns_7d, usd_volume_7d, usd_buy_volume_7d, usd_sell_volume_7d,\n                dev_hold_percentage, dev_sold_percentage, top10_hold_percentage, sniper_hold_percentage, insider_hold_percentage, bot_hold_percentage,\n                holder_count, sniper_count, insider_count, bot_count,\n                is_active, best_pool_dex AS \"best_pool_dex: Dex\", pool_dexes,\n                init_usd_price, image_path\n            FROM token_statistic\n            ORDER BY chain, token_address\n            LIMIT $1 OFFSET $2", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "token_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "symbol", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "decimals", "type_info": "Int2"}, {"ordinal": 5, "name": "supply", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "description", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 7, "name": "image", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "website", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "twitter", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 10, "name": "telegram", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "dex_paid: DexPaid", "type_info": {"Custom": {"name": "dex_paid_enum", "kind": {"Enum": ["unknown", "paid", "unpaid"]}}}}, {"ordinal": 12, "name": "is_trench_token", "type_info": "Bool"}, {"ordinal": 13, "name": "create_dex: Dex", "type_info": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}, {"ordinal": 14, "name": "create_block_number", "type_info": "Int8"}, {"ordinal": 15, "name": "create_tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 16, "name": "create_bonding_curve", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 17, "name": "create_dev", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 18, "name": "create_timestamp_millis", "type_info": "Int8"}, {"ordinal": 19, "name": "migration_pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 20, "name": "migration_timestamp_millis", "type_info": "Int8"}, {"ordinal": 21, "name": "update_timestamp_millis", "type_info": "Int8"}, {"ordinal": 22, "name": "is_mutable", "type_info": "Bool"}, {"ordinal": 23, "name": "update_authority", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 24, "name": "mint_authority", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 25, "name": "freeze_authority", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 26, "name": "best_pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 27, "name": "pool_addresses", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 28, "name": "usd_price", "type_info": "Float8"}, {"ordinal": 29, "name": "usd_market_cap", "type_info": "Float8"}, {"ordinal": 30, "name": "usd_liquidity", "type_info": "Float8"}, {"ordinal": 31, "name": "bonding_curve_progress", "type_info": "Float8"}, {"ordinal": 32, "name": "usd_total_volume", "type_info": "Float8"}, {"ordinal": 33, "name": "usd_total_buy_volume", "type_info": "Float8"}, {"ordinal": 34, "name": "usd_total_sell_volume", "type_info": "Float8"}, {"ordinal": 35, "name": "total_txns", "type_info": "Int8"}, {"ordinal": 36, "name": "total_buy_txns", "type_info": "Int8"}, {"ordinal": 37, "name": "total_sell_txns", "type_info": "Int8"}, {"ordinal": 38, "name": "total_trend", "type_info": "Float8"}, {"ordinal": 39, "name": "total_price_change", "type_info": "Float8"}, {"ordinal": 40, "name": "trend_5m", "type_info": "Float8"}, {"ordinal": 41, "name": "price_change_5m", "type_info": "Float8"}, {"ordinal": 42, "name": "txns_5m", "type_info": "Int8"}, {"ordinal": 43, "name": "buy_txns_5m", "type_info": "Int8"}, {"ordinal": 44, "name": "sell_txns_5m", "type_info": "Int8"}, {"ordinal": 45, "name": "usd_volume_5m", "type_info": "Float8"}, {"ordinal": 46, "name": "usd_buy_volume_5m", "type_info": "Float8"}, {"ordinal": 47, "name": "usd_sell_volume_5m", "type_info": "Float8"}, {"ordinal": 48, "name": "trend_1h", "type_info": "Float8"}, {"ordinal": 49, "name": "price_change_1h", "type_info": "Float8"}, {"ordinal": 50, "name": "txns_1h", "type_info": "Int8"}, {"ordinal": 51, "name": "buy_txns_1h", "type_info": "Int8"}, {"ordinal": 52, "name": "sell_txns_1h", "type_info": "Int8"}, {"ordinal": 53, "name": "usd_volume_1h", "type_info": "Float8"}, {"ordinal": 54, "name": "usd_buy_volume_1h", "type_info": "Float8"}, {"ordinal": 55, "name": "usd_sell_volume_1h", "type_info": "Float8"}, {"ordinal": 56, "name": "trend_6h", "type_info": "Float8"}, {"ordinal": 57, "name": "price_change_6h", "type_info": "Float8"}, {"ordinal": 58, "name": "txns_6h", "type_info": "Int8"}, {"ordinal": 59, "name": "buy_txns_6h", "type_info": "Int8"}, {"ordinal": 60, "name": "sell_txns_6h", "type_info": "Int8"}, {"ordinal": 61, "name": "usd_volume_6h", "type_info": "Float8"}, {"ordinal": 62, "name": "usd_buy_volume_6h", "type_info": "Float8"}, {"ordinal": 63, "name": "usd_sell_volume_6h", "type_info": "Float8"}, {"ordinal": 64, "name": "trend_24h", "type_info": "Float8"}, {"ordinal": 65, "name": "price_change_24h", "type_info": "Float8"}, {"ordinal": 66, "name": "txns_24h", "type_info": "Int8"}, {"ordinal": 67, "name": "buy_txns_24h", "type_info": "Int8"}, {"ordinal": 68, "name": "sell_txns_24h", "type_info": "Int8"}, {"ordinal": 69, "name": "usd_volume_24h", "type_info": "Float8"}, {"ordinal": 70, "name": "usd_buy_volume_24h", "type_info": "Float8"}, {"ordinal": 71, "name": "usd_sell_volume_24h", "type_info": "Float8"}, {"ordinal": 72, "name": "trend_3d", "type_info": "Float8"}, {"ordinal": 73, "name": "price_change_3d", "type_info": "Float8"}, {"ordinal": 74, "name": "txns_3d", "type_info": "Int8"}, {"ordinal": 75, "name": "buy_txns_3d", "type_info": "Int8"}, {"ordinal": 76, "name": "sell_txns_3d", "type_info": "Int8"}, {"ordinal": 77, "name": "usd_volume_3d", "type_info": "Float8"}, {"ordinal": 78, "name": "usd_buy_volume_3d", "type_info": "Float8"}, {"ordinal": 79, "name": "usd_sell_volume_3d", "type_info": "Float8"}, {"ordinal": 80, "name": "trend_7d", "type_info": "Float8"}, {"ordinal": 81, "name": "price_change_7d", "type_info": "Float8"}, {"ordinal": 82, "name": "txns_7d", "type_info": "Int8"}, {"ordinal": 83, "name": "buy_txns_7d", "type_info": "Int8"}, {"ordinal": 84, "name": "sell_txns_7d", "type_info": "Int8"}, {"ordinal": 85, "name": "usd_volume_7d", "type_info": "Float8"}, {"ordinal": 86, "name": "usd_buy_volume_7d", "type_info": "Float8"}, {"ordinal": 87, "name": "usd_sell_volume_7d", "type_info": "Float8"}, {"ordinal": 88, "name": "dev_hold_percentage", "type_info": "Float8"}, {"ordinal": 89, "name": "dev_sold_percentage", "type_info": "Float8"}, {"ordinal": 90, "name": "top10_hold_percentage", "type_info": "Float8"}, {"ordinal": 91, "name": "sniper_hold_percentage", "type_info": "Float8"}, {"ordinal": 92, "name": "insider_hold_percentage", "type_info": "Float8"}, {"ordinal": 93, "name": "bot_hold_percentage", "type_info": "Float8"}, {"ordinal": 94, "name": "holder_count", "type_info": "Int8"}, {"ordinal": 95, "name": "sniper_count", "type_info": "Int8"}, {"ordinal": 96, "name": "insider_count", "type_info": "Int8"}, {"ordinal": 97, "name": "bot_count", "type_info": "Int8"}, {"ordinal": 98, "name": "is_active", "type_info": "Bool"}, {"ordinal": 99, "name": "best_pool_dex: <PERSON>", "type_info": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}, {"ordinal": 100, "name": "pool_dexes", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 101, "name": "init_usd_price", "type_info": "Float8"}, {"ordinal": 102, "name": "image_path", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, true, true, true, true, true, false, false, false, true, true, true, true, false, true, false, false, true, true, true, true, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, true, true, true, true, true, true, true, false, false, false, true, true]}, "hash": "4f036bbb6c8dc571e85a27ec642c3cd88b2c7401df08d2c2120656faa1a58ff1"}