{"db_name": "PostgreSQL", "query": "\n            INSERT INTO native_token_price (chain, timestamp_5_seconds, timestamp_seconds, usd_price)\n            SELECT * FROM unnest($1::chain_enum[], $2::bigint[], $3::bigint[], $4::double precision[])\n            ON CONFLICT (chain, timestamp_5_seconds) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Int8Array", "Int8Array", "Float8Array"]}, "nullable": []}, "hash": "f5b0713e960f1b0c3d60747509e9f1c9c6c707d19cfa4d109ae81a73d8059101"}