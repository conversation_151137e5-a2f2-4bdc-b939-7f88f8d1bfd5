{"db_name": "PostgreSQL", "query": "\n            INSERT INTO extended_dex_trade (\n                chain, tx_hash, ix_idx,\n                pool_address, maker_address,\n                is_buy, token_ui_amount, base_ui_amount,\n                usd, usd_price, usd_market_cap,\n                block_number, timestamp_millis,\n                maker_volume_type,\n                tx_idx\n            )\n            VALUES (\n                $1, $2, $3,\n                $4, $5,\n                $6, $7, $8,\n                $9, $10, $11,\n                $12, $13,\n                $14,\n                $15\n            )\n            ON CONFLICT (chain, tx_hash, ix_idx) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Int4", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bool", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", {"Custom": {"name": "maker_volume_type_enum", "kind": {"Enum": ["plankton", "fish", "shrimp", "dolphin", "whale"]}}}, "Int4"]}, "nullable": []}, "hash": "4e926b8ef6e0856beca753e9c5bb2e63066ffcd7441efe5e1b4bbec6aee334a5"}