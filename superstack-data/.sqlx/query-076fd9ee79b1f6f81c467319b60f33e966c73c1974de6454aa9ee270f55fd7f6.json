{"db_name": "PostgreSQL", "query": "\n            INSERT INTO perp_user_state (\n                perp_exchange, perp_user,\n                perps_account_value, account_value,\n                hyperliquid_acc_collateral, hyperliquid_acc_perps,\n                timestamp_millis\n            )\n            VALUES ($1, $2, $3, $4, $5, $6, $7)\n            ON CONFLICT (perp_exchange, perp_user, timestamp_millis) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Float8", "Float8", "Float8", "Float8", "Int8"]}, "nullable": []}, "hash": "076fd9ee79b1f6f81c467319b60f33e966c73c1974de6454aa9ee270f55fd7f6"}