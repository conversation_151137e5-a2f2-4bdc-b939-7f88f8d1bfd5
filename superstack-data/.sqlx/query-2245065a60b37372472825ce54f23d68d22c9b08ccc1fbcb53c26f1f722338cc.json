{"db_name": "PostgreSQL", "query": "\n            SELECT\n                chain AS \"chain: Chain\", pool_address,\n                pair_label, dex AS \"dex: Dex\", pool_type AS \"pool_type: PoolType\",\n                create_timestamp_millis,\n                token_address, token_decimals,\n                base_address, base_decimals,\n                is_token_first,\n                is_active,\n                usd_price, usd_market_cap, usd_liquidity,\n                bonding_curve_progress,\n                price, market_cap, liquidity,\n                total_volume, total_buy_volume, total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m,\n    usd_buy_volume_5m, usd_sell_volume_5m, makers_5m, buyers_5m, sellers_5m,             \n    price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h,\n    usd_sell_volume_1h, makers_1h, buyers_1h, sellers_1h,             price_change_6h,\n    txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h,\n    makers_6h, buyers_6h, sellers_6h,             price_change_24h, txns_24h, buy_txns_24h,\n    sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h, makers_24h,\n    buyers_24h, sellers_24h,             update_timestamp_millis\n            FROM pool_statistic\n            ORDER BY chain, pool_address\n            LIMIT $1 OFFSET $2\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "pair_label", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "dex: <PERSON>", "type_info": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}, {"ordinal": 4, "name": "pool_type: PoolType", "type_info": {"Custom": {"name": "pool_type_enum", "kind": {"Enum": ["none", "dlmm"]}}}}, {"ordinal": 5, "name": "create_timestamp_millis", "type_info": "Int8"}, {"ordinal": 6, "name": "token_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 7, "name": "token_decimals", "type_info": "Int2"}, {"ordinal": 8, "name": "base_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "base_decimals", "type_info": "Int2"}, {"ordinal": 10, "name": "is_token_first", "type_info": "Bool"}, {"ordinal": 11, "name": "is_active", "type_info": "Bool"}, {"ordinal": 12, "name": "usd_price", "type_info": "Float8"}, {"ordinal": 13, "name": "usd_market_cap", "type_info": "Float8"}, {"ordinal": 14, "name": "usd_liquidity", "type_info": "Float8"}, {"ordinal": 15, "name": "bonding_curve_progress", "type_info": "Float8"}, {"ordinal": 16, "name": "price", "type_info": "Float8"}, {"ordinal": 17, "name": "market_cap", "type_info": "Float8"}, {"ordinal": 18, "name": "liquidity", "type_info": "Float8"}, {"ordinal": 19, "name": "total_volume", "type_info": "Float8"}, {"ordinal": 20, "name": "total_buy_volume", "type_info": "Float8"}, {"ordinal": 21, "name": "total_sell_volume", "type_info": "Float8"}, {"ordinal": 22, "name": "total_txns", "type_info": "Int8"}, {"ordinal": 23, "name": "total_buy_txns", "type_info": "Int8"}, {"ordinal": 24, "name": "total_sell_txns", "type_info": "Int8"}, {"ordinal": 25, "name": "price_change_5m", "type_info": "Float8"}, {"ordinal": 26, "name": "txns_5m", "type_info": "Int8"}, {"ordinal": 27, "name": "buy_txns_5m", "type_info": "Int8"}, {"ordinal": 28, "name": "sell_txns_5m", "type_info": "Int8"}, {"ordinal": 29, "name": "usd_volume_5m", "type_info": "Float8"}, {"ordinal": 30, "name": "usd_buy_volume_5m", "type_info": "Float8"}, {"ordinal": 31, "name": "usd_sell_volume_5m", "type_info": "Float8"}, {"ordinal": 32, "name": "makers_5m", "type_info": "Int8"}, {"ordinal": 33, "name": "buyers_5m", "type_info": "Int8"}, {"ordinal": 34, "name": "sellers_5m", "type_info": "Int8"}, {"ordinal": 35, "name": "price_change_1h", "type_info": "Float8"}, {"ordinal": 36, "name": "txns_1h", "type_info": "Int8"}, {"ordinal": 37, "name": "buy_txns_1h", "type_info": "Int8"}, {"ordinal": 38, "name": "sell_txns_1h", "type_info": "Int8"}, {"ordinal": 39, "name": "usd_volume_1h", "type_info": "Float8"}, {"ordinal": 40, "name": "usd_buy_volume_1h", "type_info": "Float8"}, {"ordinal": 41, "name": "usd_sell_volume_1h", "type_info": "Float8"}, {"ordinal": 42, "name": "makers_1h", "type_info": "Int8"}, {"ordinal": 43, "name": "buyers_1h", "type_info": "Int8"}, {"ordinal": 44, "name": "sellers_1h", "type_info": "Int8"}, {"ordinal": 45, "name": "price_change_6h", "type_info": "Float8"}, {"ordinal": 46, "name": "txns_6h", "type_info": "Int8"}, {"ordinal": 47, "name": "buy_txns_6h", "type_info": "Int8"}, {"ordinal": 48, "name": "sell_txns_6h", "type_info": "Int8"}, {"ordinal": 49, "name": "usd_volume_6h", "type_info": "Float8"}, {"ordinal": 50, "name": "usd_buy_volume_6h", "type_info": "Float8"}, {"ordinal": 51, "name": "usd_sell_volume_6h", "type_info": "Float8"}, {"ordinal": 52, "name": "makers_6h", "type_info": "Int8"}, {"ordinal": 53, "name": "buyers_6h", "type_info": "Int8"}, {"ordinal": 54, "name": "sellers_6h", "type_info": "Int8"}, {"ordinal": 55, "name": "price_change_24h", "type_info": "Float8"}, {"ordinal": 56, "name": "txns_24h", "type_info": "Int8"}, {"ordinal": 57, "name": "buy_txns_24h", "type_info": "Int8"}, {"ordinal": 58, "name": "sell_txns_24h", "type_info": "Int8"}, {"ordinal": 59, "name": "usd_volume_24h", "type_info": "Float8"}, {"ordinal": 60, "name": "usd_buy_volume_24h", "type_info": "Float8"}, {"ordinal": 61, "name": "usd_sell_volume_24h", "type_info": "Float8"}, {"ordinal": 62, "name": "makers_24h", "type_info": "Int8"}, {"ordinal": 63, "name": "buyers_24h", "type_info": "Int8"}, {"ordinal": 64, "name": "sellers_24h", "type_info": "Int8"}, {"ordinal": 65, "name": "update_timestamp_millis", "type_info": "Int8"}], "parameters": {"Left": ["Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "2245065a60b37372472825ce54f23d68d22c9b08ccc1fbcb53c26f1f722338cc"}