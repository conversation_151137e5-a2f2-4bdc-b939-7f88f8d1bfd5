{"db_name": "PostgreSQL", "query": "\n        SELECT DISTINCT ON (perp_id)\n            perp_exchange AS \"perp_exchange: PerpExchange\",\n            perp_id, funding, open_interest, mark_px, market_cap,\n            long_ntl, short_ntl, long_traders, short_traders, long_entry, short_entry,\n            created_at_millis\n        FROM perp_state_series\n        WHERE perp_exchange = $1 AND created_at_millis < $2\n        ORDER BY perp_id, created_at_millis DESC\n        ", "describe": {"columns": [{"ordinal": 0, "name": "perp_exchange: PerpExchange", "type_info": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}, {"ordinal": 1, "name": "perp_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "funding", "type_info": "Float8"}, {"ordinal": 3, "name": "open_interest", "type_info": "Float8"}, {"ordinal": 4, "name": "mark_px", "type_info": "Float8"}, {"ordinal": 5, "name": "market_cap", "type_info": "Float8"}, {"ordinal": 6, "name": "long_ntl", "type_info": "Float8"}, {"ordinal": 7, "name": "short_ntl", "type_info": "Float8"}, {"ordinal": 8, "name": "long_traders", "type_info": "Int8"}, {"ordinal": 9, "name": "short_traders", "type_info": "Int8"}, {"ordinal": 10, "name": "long_entry", "type_info": "Float8"}, {"ordinal": 11, "name": "short_entry", "type_info": "Float8"}, {"ordinal": 12, "name": "created_at_millis", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "f890bae720a82ec7b7e16eb4d2d7eb268f898285d612637da16c97cd16444da4"}