{"db_name": "PostgreSQL", "query": "\n            SELECT\n                chain AS \"chain: Chain\",\n                pool_address,\n                block_number, timestamp_millis,\n                price, market_cap, liquidity,\n                total_volume, total_buy_volume, total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress\n            FROM pool_state\n            WHERE chain = $1 AND pool_address = $2\n            ORDER BY block_number DESC\n            LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "block_number", "type_info": "Int8"}, {"ordinal": 3, "name": "timestamp_millis", "type_info": "Int8"}, {"ordinal": 4, "name": "price", "type_info": "Float8"}, {"ordinal": 5, "name": "market_cap", "type_info": "Float8"}, {"ordinal": 6, "name": "liquidity", "type_info": "Float8"}, {"ordinal": 7, "name": "total_volume", "type_info": "Float8"}, {"ordinal": 8, "name": "total_buy_volume", "type_info": "Float8"}, {"ordinal": 9, "name": "total_sell_volume", "type_info": "Float8"}, {"ordinal": 10, "name": "total_txns", "type_info": "Int8"}, {"ordinal": 11, "name": "total_buy_txns", "type_info": "Int8"}, {"ordinal": 12, "name": "total_sell_txns", "type_info": "Int8"}, {"ordinal": 13, "name": "bonding_curve_progress", "type_info": "Float8"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, true]}, "hash": "17f62ed8a14fc8067e3ca29b2feeaf9ff67da0d45d2751ea95a1b80ffb7bb857"}