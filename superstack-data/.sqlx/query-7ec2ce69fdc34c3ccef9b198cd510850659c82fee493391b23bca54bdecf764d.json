{"db_name": "PostgreSQL", "query": "\n            INSERT INTO chain_info (chain, block_number)\n            VALUES ($1, $2)\n            ON CONFLICT (chain) DO UPDATE SET block_number = $2\n            WHERE chain_info.block_number < $2\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Int8"]}, "nullable": []}, "hash": "7ec2ce69fdc34c3ccef9b198cd510850659c82fee493391b23bca54bdecf764d"}