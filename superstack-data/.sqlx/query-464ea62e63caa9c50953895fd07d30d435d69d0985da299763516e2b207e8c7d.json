{"db_name": "PostgreSQL", "query": "\n            INSERT INTO dex_trade (\n                chain, tx_hash, ix_idx,\n                pool_address, maker_address,\n                is_buy_token,\n                in_address, in_amount,\n                out_address, out_amount,\n                block_number, timestamp_millis,\n                tx_idx\n            )\n            VALUES (\n                $1, $2, $3,\n                $4, $5,\n                $6,\n                $7, $8,\n                $9, $10,\n                $11, $12,\n                $13\n            )\n            ON CONFLICT (chain, tx_hash, ix_idx) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Int4", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bool", "<PERSON><PERSON><PERSON><PERSON>", "Numeric", "<PERSON><PERSON><PERSON><PERSON>", "Numeric", "Int8", "Int8", "Int4"]}, "nullable": []}, "hash": "464ea62e63caa9c50953895fd07d30d435d69d0985da299763516e2b207e8c7d"}