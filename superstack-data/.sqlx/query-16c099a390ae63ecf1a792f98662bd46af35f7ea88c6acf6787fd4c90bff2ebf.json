{"db_name": "PostgreSQL", "query": "\n            INSERT INTO native_token_price (\n                chain,\n                timestamp_5_seconds,\n                timestamp_seconds,\n                usd_price\n            )\n            VALUES ($1, $2, $3, $4)\n            ON CONFLICT (chain, timestamp_5_seconds) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Int8", "Int8", "Float8"]}, "nullable": []}, "hash": "16c099a390ae63ecf1a792f98662bd46af35f7ea88c6acf6787fd4c90bff2ebf"}