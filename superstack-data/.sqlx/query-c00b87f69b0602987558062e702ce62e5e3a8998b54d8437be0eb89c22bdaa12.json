{"db_name": "PostgreSQL", "query": "\n            SELECT chain AS \"chain: Chain\", tx_hash, ix_idx,\n                pool_address, maker_address,\n                is_buy_token,\n                in_address, in_amount,\n                out_address, out_amount,\n                block_number, timestamp_millis,\n                tx_idx\n            FROM dex_trade\n            WHERE chain = $1 AND pool_address = $2\n            ORDER BY block_number DESC, tx_idx DESC, ix_idx DESC\n            LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "ix_idx", "type_info": "Int4"}, {"ordinal": 3, "name": "pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "maker_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "is_buy_token", "type_info": "Bool"}, {"ordinal": 6, "name": "in_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 7, "name": "in_amount", "type_info": "Numeric"}, {"ordinal": 8, "name": "out_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "out_amount", "type_info": "Numeric"}, {"ordinal": 10, "name": "block_number", "type_info": "Int8"}, {"ordinal": 11, "name": "timestamp_millis", "type_info": "Int8"}, {"ordinal": 12, "name": "tx_idx", "type_info": "Int4"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false]}, "hash": "c00b87f69b0602987558062e702ce62e5e3a8998b54d8437be0eb89c22bdaa12"}