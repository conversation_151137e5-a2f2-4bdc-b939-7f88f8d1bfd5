{"db_name": "PostgreSQL", "query": "\n            INSERT INTO pool_metadata (\n                chain, pool_address,\n                pair_label, dex, pool_type,\n                create_timestamp_millis, update_timestamp_millis,\n                token_address, token_decimals,\n                base_address, base_decimals,\n                is_token_first,\n                is_active,\n                bin_step\n            )\n            VALUES (\n                $1, $2,\n                $3, $4, $5,\n                $6, $7,\n                $8, $9,\n                $10, $11,\n                $12,\n                $13,\n                $14\n            )\n            ON CONFLICT (chain, pool_address) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}, {"Custom": {"name": "pool_type_enum", "kind": {"Enum": ["none", "dlmm"]}}}, "Int8", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Bool", "Bool", "Int2"]}, "nullable": []}, "hash": "2f5bfd8330be45891c7352ef6fd2fc6ca8cbed153af545dbe0cfe17e7770572a"}