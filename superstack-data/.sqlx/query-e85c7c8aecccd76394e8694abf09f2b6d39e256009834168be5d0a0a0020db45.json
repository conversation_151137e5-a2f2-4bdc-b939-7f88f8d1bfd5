{"db_name": "PostgreSQL", "query": "\n            INSERT INTO dex_trade (\n                chain, tx_hash, ix_idx,\n                pool_address, maker_address,\n                is_buy_token,\n                in_address, in_amount,\n                out_address, out_amount,\n                block_number, timestamp_millis,\n                tx_idx\n            )\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[], $3::integer[],\n                $4::varchar[], $5::varchar[],\n                $6::boolean[],\n                $7::varchar[], $8::numeric[],\n                $9::varchar[], $10::numeric[],\n                $11::bigint[], $12::bigint[],\n                $13::integer[])\n            ON CONFLICT (chain, tx_hash, ix_idx) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Int4Array", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "BoolArray", "Varchar<PERSON><PERSON>y", "NumericArray", "Varchar<PERSON><PERSON>y", "NumericArray", "Int8Array", "Int8Array", "Int4Array"]}, "nullable": []}, "hash": "e85c7c8aecccd76394e8694abf09f2b6d39e256009834168be5d0a0a0020db45"}