{"db_name": "PostgreSQL", "query": "\n            INSERT INTO candle (\n                chain, pool_address,\n                open_timestamp_seconds, close_timestamp_seconds,\n                interval,\n                native_token_usd_price,\n                usd_open_price, usd_close_price, usd_high_price, usd_low_price,\n                usd_open_market_cap, usd_close_market_cap, usd_high_market_cap, usd_low_market_cap,\n                usd_volume, txns\n            )\n            VALUES (\n                $1, $2,\n                $3, $4,\n                $5,\n                $6,\n                $7, $8, $9, $10,\n                $11, $12, $13, $14,\n                $15, $16\n            )\n            ON CONFLICT (chain, pool_address, interval, open_timestamp_seconds) DO UPDATE SET\n                native_token_usd_price = EXCLUDED.native_token_usd_price,\n                usd_open_price = EXCLUDED.usd_open_price,\n                usd_close_price = EXCLUDED.usd_close_price,\n                usd_high_price = EXCLUDED.usd_high_price,\n                usd_low_price = EXCLUDED.usd_low_price,\n                usd_open_market_cap = EXCLUDED.usd_open_market_cap,\n                usd_close_market_cap = EXCLUDED.usd_close_market_cap,\n                usd_high_market_cap = EXCLUDED.usd_high_market_cap,\n                usd_low_market_cap = EXCLUDED.usd_low_market_cap,\n                usd_volume = EXCLUDED.usd_volume,\n                txns = EXCLUDED.txns\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", {"Custom": {"name": "candle_interval_enum", "kind": {"Enum": ["1s", "5s", "15s", "30s", "1m", "5m", "15m", "30m", "1h", "4h", "8h", "12h", "24h", "3d", "7d", "30d"]}}}, "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8"]}, "nullable": []}, "hash": "93641f6c1838aa2c23b9d6da689d3fe113e4f1d8af34e9178acfe8aa53484e73"}