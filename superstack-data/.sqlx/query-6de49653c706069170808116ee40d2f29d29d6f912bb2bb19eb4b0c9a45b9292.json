{"db_name": "PostgreSQL", "query": "\n            INSERT INTO token_statistic (\n                chain, token_address,\n                name, symbol,\n                decimals, supply,\n                description, image, website, twitter, telegram, dex_paid,\n                is_trench_token,\n                create_dex, create_block_number, create_tx_hash, create_bonding_curve, create_dev, create_timestamp_millis,\n                migration_pool_address, migration_timestamp_millis,\n                update_timestamp_millis,\n                is_mutable, update_authority, mint_authority, freeze_authority,\n                best_pool_address, pool_addresses,\n                usd_price, usd_market_cap, usd_liquidity,\n                bonding_curve_progress,\n                usd_total_volume, usd_total_buy_volume, usd_total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                total_trend, total_price_change,\n                trend_5m, price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m,\n                trend_1h, price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h,\n                trend_6h, price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h,\n                trend_24h, price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h,\n                trend_3d, price_change_3d, txns_3d, buy_txns_3d, sell_txns_3d, usd_volume_3d, usd_buy_volume_3d, usd_sell_volume_3d,\n                trend_7d, price_change_7d, txns_7d, buy_txns_7d, sell_txns_7d, usd_volume_7d, usd_buy_volume_7d, usd_sell_volume_7d,\n                dev_hold_percentage, dev_sold_percentage, top10_hold_percentage, sniper_hold_percentage, insider_hold_percentage, bot_hold_percentage,\n                holder_count, sniper_count, insider_count, bot_count,\n                is_active, best_pool_dex, pool_dexes,\n                init_usd_price, image_path\n            )\n            VALUES (\n                $1, $2,\n                $3, $4,\n                $5, $6,\n                $7, $8, $9, $10, $11, $12,\n                $13,\n                $14, $15, $16, $17, $18, $19,\n                $20, $21,\n                $22,\n                $23, $24, $25, $26,\n                $27, $28,\n                $29, $30, $31,\n                $32,\n                $33, $34, $35,\n                $36, $37, $38,\n                $39, $40,\n                $41, $42, $43, $44, $45, $46, $47, $48,\n                $49, $50, $51, $52, $53, $54, $55, $56,\n                $57, $58, $59, $60, $61, $62, $63, $64,\n                $65, $66, $67, $68, $69, $70, $71, $72,\n                $73, $74, $75, $76, $77, $78, $79, $80,\n                $81, $82, $83, $84, $85, $86, $87, $88,\n                $89, $90, $91, $92, $93, $94,\n                $95, $96, $97, $98,\n                $99, $100, $101,\n                $102, $103\n            )\n            ON CONFLICT (chain, token_address) DO UPDATE SET\n                name = EXCLUDED.name,\n                symbol = EXCLUDED.symbol,\n                decimals = EXCLUDED.decimals,\n                supply = EXCLUDED.supply,\n                description = EXCLUDED.description,\n                image = EXCLUDED.image,\n                website = EXCLUDED.website,\n                twitter = EXCLUDED.twitter,\n                telegram = EXCLUDED.telegram,\n                dex_paid = EXCLUDED.dex_paid,\n                is_trench_token = EXCLUDED.is_trench_token,\n                create_dex = EXCLUDED.create_dex,\n                create_block_number = EXCLUDED.create_block_number,\n                create_tx_hash = EXCLUDED.create_tx_hash,\n                create_bonding_curve = EXCLUDED.create_bonding_curve,\n                create_dev = EXCLUDED.create_dev,\n                create_timestamp_millis = EXCLUDED.create_timestamp_millis,\n                migration_pool_address = EXCLUDED.migration_pool_address,\n                migration_timestamp_millis = EXCLUDED.migration_timestamp_millis,\n                is_mutable = EXCLUDED.is_mutable,\n                update_authority = EXCLUDED.update_authority,\n                mint_authority = EXCLUDED.mint_authority,\n                freeze_authority = EXCLUDED.freeze_authority,\n                best_pool_address = EXCLUDED.best_pool_address,\n                pool_addresses = EXCLUDED.pool_addresses,\n                usd_price = EXCLUDED.usd_price,\n                usd_market_cap = EXCLUDED.usd_market_cap,\n                usd_liquidity = EXCLUDED.usd_liquidity,\n                bonding_curve_progress = EXCLUDED.bonding_curve_progress,\n                usd_total_volume = EXCLUDED.usd_total_volume,\n                usd_total_buy_volume = EXCLUDED.usd_total_buy_volume,\n                usd_total_sell_volume = EXCLUDED.usd_total_sell_volume,\n                total_txns = EXCLUDED.total_txns,\n                total_buy_txns = EXCLUDED.total_buy_txns,\n                total_sell_txns = EXCLUDED.total_sell_txns,\n                total_trend = EXCLUDED.total_trend,\n                total_price_change = EXCLUDED.total_price_change,\n                trend_5m = EXCLUDED.trend_5m,\n                price_change_5m = EXCLUDED.price_change_5m,\n                txns_5m = EXCLUDED.txns_5m,\n                buy_txns_5m = EXCLUDED.buy_txns_5m,\n                sell_txns_5m = EXCLUDED.sell_txns_5m,\n                usd_volume_5m = EXCLUDED.usd_volume_5m,\n                usd_buy_volume_5m = EXCLUDED.usd_buy_volume_5m,\n                usd_sell_volume_5m = EXCLUDED.usd_sell_volume_5m,\n                trend_1h = EXCLUDED.trend_1h,\n                price_change_1h = EXCLUDED.price_change_1h,\n                txns_1h = EXCLUDED.txns_1h,\n                buy_txns_1h = EXCLUDED.buy_txns_1h,\n                sell_txns_1h = EXCLUDED.sell_txns_1h,\n                usd_volume_1h = EXCLUDED.usd_volume_1h,\n                usd_buy_volume_1h = EXCLUDED.usd_buy_volume_1h,\n                usd_sell_volume_1h = EXCLUDED.usd_sell_volume_1h,\n                trend_6h = EXCLUDED.trend_6h,\n                price_change_6h = EXCLUDED.price_change_6h,\n                txns_6h = EXCLUDED.txns_6h,\n                buy_txns_6h = EXCLUDED.buy_txns_6h,\n                sell_txns_6h = EXCLUDED.sell_txns_6h,\n                usd_volume_6h = EXCLUDED.usd_volume_6h,\n                usd_buy_volume_6h = EXCLUDED.usd_buy_volume_6h,\n                usd_sell_volume_6h = EXCLUDED.usd_sell_volume_6h,\n                trend_24h = EXCLUDED.trend_24h,\n                price_change_24h = EXCLUDED.price_change_24h,\n                txns_24h = EXCLUDED.txns_24h,\n                buy_txns_24h = EXCLUDED.buy_txns_24h,\n                sell_txns_24h = EXCLUDED.sell_txns_24h,\n                usd_volume_24h = EXCLUDED.usd_volume_24h,\n                usd_buy_volume_24h = EXCLUDED.usd_buy_volume_24h,\n                usd_sell_volume_24h = EXCLUDED.usd_sell_volume_24h,\n                trend_3d = EXCLUDED.trend_3d,\n                price_change_3d = EXCLUDED.price_change_3d,\n                txns_3d = EXCLUDED.txns_3d,\n                buy_txns_3d = EXCLUDED.buy_txns_3d,\n                sell_txns_3d = EXCLUDED.sell_txns_3d,\n                usd_volume_3d = EXCLUDED.usd_volume_3d,\n                usd_buy_volume_3d = EXCLUDED.usd_buy_volume_3d,\n                usd_sell_volume_3d = EXCLUDED.usd_sell_volume_3d,\n                trend_7d = EXCLUDED.trend_7d,\n                price_change_7d = EXCLUDED.price_change_7d,\n                txns_7d = EXCLUDED.txns_7d,\n                buy_txns_7d = EXCLUDED.buy_txns_7d,\n                sell_txns_7d = EXCLUDED.sell_txns_7d,\n                usd_volume_7d = EXCLUDED.usd_volume_7d,\n                usd_buy_volume_7d = EXCLUDED.usd_buy_volume_7d,\n                usd_sell_volume_7d = EXCLUDED.usd_sell_volume_7d,\n                dev_hold_percentage = EXCLUDED.dev_hold_percentage,\n                dev_sold_percentage = EXCLUDED.dev_sold_percentage,\n                top10_hold_percentage = EXCLUDED.top10_hold_percentage,\n                sniper_hold_percentage = EXCLUDED.sniper_hold_percentage,\n                insider_hold_percentage = EXCLUDED.insider_hold_percentage,\n                bot_hold_percentage = EXCLUDED.bot_hold_percentage,\n                holder_count = EXCLUDED.holder_count,\n                sniper_count = EXCLUDED.sniper_count,\n                insider_count = EXCLUDED.insider_count,\n                bot_count = EXCLUDED.bot_count,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis,\n                is_active = EXCLUDED.is_active,\n                best_pool_dex = EXCLUDED.best_pool_dex,\n                pool_dexes = EXCLUDED.pool_dexes,\n                init_usd_price = EXCLUDED.init_usd_price,\n                image_path = EXCLUDED.image_path\n            WHERE token_statistic.update_timestamp_millis <= EXCLUDED.update_timestamp_millis\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", {"Custom": {"name": "dex_paid_enum", "kind": {"Enum": ["unknown", "paid", "unpaid"]}}}, "Bool", {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}, "Int8", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", "Bool", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8", "Int8", "Int8", "Bool", {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "Float8", "<PERSON><PERSON><PERSON><PERSON>"]}, "nullable": []}, "hash": "6de49653c706069170808116ee40d2f29d29d6f912bb2bb19eb4b0c9a45b9292"}