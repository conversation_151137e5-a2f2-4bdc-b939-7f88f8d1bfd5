{"db_name": "PostgreSQL", "query": "\n            INSERT INTO extended_token_holder (\n                chain, token_address, holder_address,\n                bought_ui_amount, sold_ui_amount, remaining_ui_amount,\n                bought_txns, sold_txns,\n                spent_usd, received_usd,\n                pnl_usd,\n                update_timestamp_millis, update_block_number,\n                native_token_balance,\n                maker_volume_type, maker_trade_type\n            )\n            VALUES (\n                $1, $2, $3,\n                $4, $5, $6,\n                $7, $8,\n                $9, $10,\n                $11,\n                $12, $13,\n                $14,\n                $15, $16\n            )\n            ON CONFLICT (chain, token_address, holder_address) DO UPDATE SET\n                bought_ui_amount = EXCLUDED.bought_ui_amount,\n                sold_ui_amount = EXCLUDED.sold_ui_amount,\n                remaining_ui_amount = EXCLUDED.remaining_ui_amount,\n                bought_txns = EXCLUDED.bought_txns,\n                sold_txns = EXCLUDED.sold_txns,\n                spent_usd = EXCLUDED.spent_usd,\n                received_usd = EXCLUDED.received_usd,\n                pnl_usd = EXCLUDED.pnl_usd,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis,\n                update_block_number = EXCLUDED.update_block_number,\n                native_token_balance = EXCLUDED.native_token_balance,\n                maker_volume_type = EXCLUDED.maker_volume_type,\n                maker_trade_type = EXCLUDED.maker_trade_type\n            WHERE extended_token_holder.update_timestamp_millis <= EXCLUDED.update_timestamp_millis\n                and extended_token_holder.update_block_number <= EXCLUDED.update_block_number\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Float8", "Float8", "Float8", "Int8", "Int8", "Float8", "Float8", "Float8", "Int8", "Int8", "Float8", {"Custom": {"name": "maker_volume_type_enum", "kind": {"Enum": ["plankton", "fish", "shrimp", "dolphin", "whale"]}}}, {"Custom": {"name": "maker_trade_type_enum", "kind": {"Enum": ["none", "dev", "sniper", "bot", "insider"]}}}]}, "nullable": []}, "hash": "ea002109bae09c596c2810f3650f9bc0d73ca25991d6af62ee17278bfbdedb58"}