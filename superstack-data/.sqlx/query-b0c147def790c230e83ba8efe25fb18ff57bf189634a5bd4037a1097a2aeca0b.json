{"db_name": "PostgreSQL", "query": "\n            SELECT\n                chain AS \"chain: Chain\",\n                address,\n                name,\n                symbol,\n                decimals,\n                supply,\n                description,\n                image,\n                website,\n                twitter,\n                telegram,\n                dex_paid AS \"dex_paid: DexPaid\",\n                is_trench_token,\n                create_dex AS \"create_dex: Dex\",\n                create_block_number,\n                create_tx_hash,\n                create_bonding_curve,\n                create_dev,\n                create_timestamp_millis,\n                migration_pool_address,\n                migration_timestamp_millis,\n                update_timestamp_millis,\n                uri, seller_fee_basis_points,\n                creators, primary_sale_happened,\n                is_mutable,\n                update_authority, mint_authority, freeze_authority,\n                is_active,\n                image_path\n            FROM token_metadata\n            WHERE chain = $1 AND address = $2\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "symbol", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "decimals", "type_info": "Int2"}, {"ordinal": 5, "name": "supply", "type_info": "Numeric"}, {"ordinal": 6, "name": "description", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 7, "name": "image", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "website", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "twitter", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 10, "name": "telegram", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "dex_paid: DexPaid", "type_info": {"Custom": {"name": "dex_paid_enum", "kind": {"Enum": ["unknown", "paid", "unpaid"]}}}}, {"ordinal": 12, "name": "is_trench_token", "type_info": "Bool"}, {"ordinal": 13, "name": "create_dex: Dex", "type_info": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}, {"ordinal": 14, "name": "create_block_number", "type_info": "Int8"}, {"ordinal": 15, "name": "create_tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 16, "name": "create_bonding_curve", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 17, "name": "create_dev", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 18, "name": "create_timestamp_millis", "type_info": "Int8"}, {"ordinal": 19, "name": "migration_pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 20, "name": "migration_timestamp_millis", "type_info": "Int8"}, {"ordinal": 21, "name": "update_timestamp_millis", "type_info": "Int8"}, {"ordinal": 22, "name": "uri", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 23, "name": "seller_fee_basis_points", "type_info": "Int2"}, {"ordinal": 24, "name": "creators", "type_info": "Varchar<PERSON><PERSON>y"}, {"ordinal": 25, "name": "primary_sale_happened", "type_info": "Bool"}, {"ordinal": 26, "name": "is_mutable", "type_info": "Bool"}, {"ordinal": 27, "name": "update_authority", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 28, "name": "mint_authority", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 29, "name": "freeze_authority", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 30, "name": "is_active", "type_info": "Bool"}, {"ordinal": 31, "name": "image_path", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text"]}, "nullable": [false, false, false, false, false, false, true, true, true, true, true, false, false, false, true, true, true, true, false, true, false, false, true, true, true, true, true, true, true, true, false, true]}, "hash": "b0c147def790c230e83ba8efe25fb18ff57bf189634a5bd4037a1097a2aeca0b"}