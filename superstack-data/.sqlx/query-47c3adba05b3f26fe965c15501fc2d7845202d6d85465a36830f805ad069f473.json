{"db_name": "PostgreSQL", "query": "\n            SELECT COUNT(DISTINCT maker_address) AS makers,\n                COUNT(CASE WHEN is_buy_token THEN maker_address END) AS buyers,\n                COUNT(CASE WHEN NOT is_buy_token THEN maker_address END) AS sellers\n            FROM dex_trade\n            WHERE chain = $1 AND pool_address = $2 AND timestamp_millis >= $3 AND timestamp_millis <= $4\n            ", "describe": {"columns": [{"ordinal": 0, "name": "makers", "type_info": "Int8"}, {"ordinal": 1, "name": "buyers", "type_info": "Int8"}, {"ordinal": 2, "name": "sellers", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text", "Int8", "Int8"]}, "nullable": [null, null, null]}, "hash": "47c3adba05b3f26fe965c15501fc2d7845202d6d85465a36830f805ad069f473"}