{"db_name": "PostgreSQL", "query": "\n            UPDATE token_metadata SET\n                migration_pool_address = $1,\n                migration_timestamp_millis = $2\n            WHERE chain = $3 AND address = $4", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "Int8", {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text"]}, "nullable": []}, "hash": "a6815ed0ddb5bafe12e93dbf1eefea3545f55893cda6a6ba484a429c125a351e"}