{"db_name": "PostgreSQL", "query": "\n            SELECT\n                perp_exchange AS \"perp_exchange: PerpExchange\",\n                perp_id,\n                is_native_token,\n                network,\n                address,\n                name,\n                symbol,\n                socials,\n                total_supply,\n                circulating_supply,\n                update_timestamp_millis\n            FROM perp_info\n            WHERE perp_exchange = $1 AND perp_id = $2\n            ", "describe": {"columns": [{"ordinal": 0, "name": "perp_exchange: PerpExchange", "type_info": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}, {"ordinal": 1, "name": "perp_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "is_native_token", "type_info": "Bool"}, {"ordinal": 3, "name": "network", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "symbol", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 7, "name": "socials", "type_info": "Jsonb"}, {"ordinal": 8, "name": "total_supply", "type_info": "Numeric"}, {"ordinal": 9, "name": "circulating_supply", "type_info": "Numeric"}, {"ordinal": 10, "name": "update_timestamp_millis", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "Text"]}, "nullable": [false, false, false, true, true, false, false, false, false, false, false]}, "hash": "88f53b1614013ce0a4a165412e0f5749bbaebf0e5bf00aa61bf68c8e058ac0a3"}