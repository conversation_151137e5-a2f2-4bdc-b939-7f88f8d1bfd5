{"db_name": "PostgreSQL", "query": "\n            INSERT INTO candle (\n                chain, pool_address,\n                open_timestamp_seconds, close_timestamp_seconds,\n                interval,\n                native_token_usd_price,\n                usd_open_price, usd_close_price, usd_high_price, usd_low_price,\n                usd_open_market_cap, usd_close_market_cap, usd_high_market_cap, usd_low_market_cap,\n                usd_volume, txns\n            )\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[],\n                $3::bigint[], $4::bigint[],\n                $5::candle_interval_enum[],\n                $6::double precision[],\n                $7::double precision[], $8::double precision[],\n                $9::double precision[], $10::double precision[],\n                $11::double precision[], $12::double precision[],\n                $13::double precision[], $14::double precision[],\n                $15::double precision[], $16::bigint[])\n            ON CONFLICT (chain, pool_address, interval, open_timestamp_seconds) DO UPDATE SET\n                native_token_usd_price = EXCLUDED.native_token_usd_price,\n                usd_open_price = EXCLUDED.usd_open_price,\n                usd_close_price = EXCLUDED.usd_close_price,\n                usd_high_price = EXCLUDED.usd_high_price,\n                usd_low_price = EXCLUDED.usd_low_price,\n                usd_open_market_cap = EXCLUDED.usd_open_market_cap,\n                usd_close_market_cap = EXCLUDED.usd_close_market_cap,\n                usd_high_market_cap = EXCLUDED.usd_high_market_cap,\n                usd_low_market_cap = EXCLUDED.usd_low_market_cap,\n                usd_volume = EXCLUDED.usd_volume,\n                txns = EXCLUDED.txns\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Int8Array", "Int8Array", {"Custom": {"name": "candle_interval_enum[]", "kind": {"Array": {"Custom": {"name": "candle_interval_enum", "kind": {"Enum": ["1s", "5s", "15s", "30s", "1m", "5m", "15m", "30m", "1h", "4h", "8h", "12h", "24h", "3d", "7d", "30d"]}}}}}}, "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array"]}, "nullable": []}, "hash": "1a15ae2ff86f37fbba400dae328bfb7e49f6e531a8b6d8d261c225d95aef4326"}