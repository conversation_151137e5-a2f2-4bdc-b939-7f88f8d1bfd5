{"db_name": "PostgreSQL", "query": "\n            SELECT\n                perp_exchange as \"perp_exchange: PerpExchange\",\n                perp_user,\n                perps_account_value,\n                account_value,\n                hyperliquid_acc_collateral,\n                hyperliquid_acc_perps,\n                timestamp_millis\n            FROM perp_user_state\n            WHERE perp_exchange = $1 AND perp_user = $2\n            ORDER BY timestamp_millis DESC\n            LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "perp_exchange: PerpExchange", "type_info": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}, {"ordinal": 1, "name": "perp_user", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "perps_account_value", "type_info": "Float8"}, {"ordinal": 3, "name": "account_value", "type_info": "Float8"}, {"ordinal": 4, "name": "hyperliquid_acc_collateral", "type_info": "Float8"}, {"ordinal": 5, "name": "hyperliquid_acc_perps", "type_info": "Float8"}, {"ordinal": 6, "name": "timestamp_millis", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}, "Text"]}, "nullable": [false, false, false, false, false, false, false]}, "hash": "7676bf5e4ddfeb506b5c90b0c295c5229fbed000a4de3148391f2d9edd450395"}