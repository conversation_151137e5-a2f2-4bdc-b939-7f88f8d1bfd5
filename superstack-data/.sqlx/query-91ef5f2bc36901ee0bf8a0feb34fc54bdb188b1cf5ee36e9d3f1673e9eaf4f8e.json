{"db_name": "PostgreSQL", "query": "\n            INSERT INTO extended_dex_trade (\n                chain, tx_hash, ix_idx,\n                pool_address, maker_address,\n                is_buy, token_ui_amount, base_ui_amount,\n                usd, usd_price, usd_market_cap,\n                block_number, timestamp_millis,\n                maker_volume_type,\n                tx_idx\n            )\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[], $3::integer[],\n                $4::varchar[], $5::varchar[],\n                $6::boolean[], $7::double precision[], $8::double precision[],\n                $9::double precision[], $10::double precision[], $11::double precision[],\n                $12::bigint[], $13::bigint[],\n                $14::maker_volume_type_enum[],\n                $15::integer[])\n            ON CONFLICT (chain, tx_hash, ix_idx) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Int4Array", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "BoolArray", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", {"Custom": {"name": "maker_volume_type_enum[]", "kind": {"Array": {"Custom": {"name": "maker_volume_type_enum", "kind": {"Enum": ["plankton", "fish", "shrimp", "dolphin", "whale"]}}}}}}, "Int4Array"]}, "nullable": []}, "hash": "91ef5f2bc36901ee0bf8a0feb34fc54bdb188b1cf5ee36e9d3f1673e9eaf4f8e"}