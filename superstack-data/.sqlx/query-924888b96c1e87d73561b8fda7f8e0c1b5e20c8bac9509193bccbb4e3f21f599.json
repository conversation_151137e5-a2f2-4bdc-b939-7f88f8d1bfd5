{"db_name": "PostgreSQL", "query": "\n            INSERT INTO token_state (\n                chain, token_address, timestamp_millis,\n                best_pool_address,\n                usd_price, usd_market_cap,\n                native_price,\n                native_token_liquidity, usd_token_liquidity,\n                total_native_token_volume, total_usd_token_volume,\n                total_native_token_buy_volume, total_usd_token_buy_volume,\n                total_native_token_sell_volume, total_usd_token_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                bonding_curve_progress,\n                best_pool_dex)\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[], $3::bigint[],\n                $4::varchar[],\n                $5::double precision[], $6::double precision[],\n                $7::double precision[],\n                $8::double precision[], $9::double precision[],\n                $10::double precision[], $11::double precision[],\n                $12::double precision[], $13::double precision[],\n                $14::double precision[], $15::double precision[],\n                $16::bigint[], $17::bigint[], $18::bigint[],\n                $19::double precision[],\n                $20::dex_enum[])\n            ON CONFLICT (chain, token_address, timestamp_millis) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Int8Array", "Varchar<PERSON><PERSON>y", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array", {"Custom": {"name": "dex_enum[]", "kind": {"Array": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}}}]}, "nullable": []}, "hash": "924888b96c1e87d73561b8fda7f8e0c1b5e20c8bac9509193bccbb4e3f21f599"}