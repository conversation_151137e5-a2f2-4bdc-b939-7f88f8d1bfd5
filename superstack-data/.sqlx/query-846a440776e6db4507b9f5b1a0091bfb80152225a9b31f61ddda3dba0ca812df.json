{"db_name": "PostgreSQL", "query": "\n            SELECT \n                chain AS \"chain: Chain\",\n                pool_address,\n                pair_label,\n                dex AS \"dex: Dex\",\n                pool_type AS \"pool_type: PoolType\",\n                create_timestamp_millis,\n                update_timestamp_millis,\n                token_address,\n                token_decimals,\n                base_address,\n                base_decimals,\n                is_token_first,\n                is_active,\n                bin_step\n            FROM pool_metadata\n            WHERE chain = $1 AND pool_address = $2\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "pair_label", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "dex: <PERSON>", "type_info": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}, {"ordinal": 4, "name": "pool_type: PoolType", "type_info": {"Custom": {"name": "pool_type_enum", "kind": {"Enum": ["none", "dlmm"]}}}}, {"ordinal": 5, "name": "create_timestamp_millis", "type_info": "Int8"}, {"ordinal": 6, "name": "update_timestamp_millis", "type_info": "Int8"}, {"ordinal": 7, "name": "token_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "token_decimals", "type_info": "Int2"}, {"ordinal": 9, "name": "base_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 10, "name": "base_decimals", "type_info": "Int2"}, {"ordinal": 11, "name": "is_token_first", "type_info": "Bool"}, {"ordinal": 12, "name": "is_active", "type_info": "Bool"}, {"ordinal": 13, "name": "bin_step", "type_info": "Int2"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, true]}, "hash": "846a440776e6db4507b9f5b1a0091bfb80152225a9b31f61ddda3dba0ca812df"}