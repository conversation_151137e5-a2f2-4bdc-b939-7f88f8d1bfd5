{"db_name": "PostgreSQL", "query": "\n            INSERT INTO token_holder (\n                chain, token_address, holder_address,\n                bought_amount, sold_amount,\n                bought_txns, sold_txns,\n                spent_native_token_ui_amount, spent_usd_token_ui_amount, total_spent_usd,\n                received_native_token_ui_amount, received_usd_token_ui_amount, total_received_usd,\n                update_timestamp_millis, update_block_number)\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[], $3::varchar[],\n                $4::numeric[], $5::numeric[],\n                $6::bigint[], $7::bigint[],\n                $8::double precision[], $9::double precision[], $10::double precision[],\n                $11::double precision[], $12::double precision[], $13::double precision[],\n                $14::bigint[], $15::bigint[])\n            ON CONFLICT (chain, token_address, holder_address) DO UPDATE SET\n                bought_amount = EXCLUDED.bought_amount,\n                sold_amount = EXCLUDED.sold_amount,\n                bought_txns = EXCLUDED.bought_txns,\n                sold_txns = EXCLUDED.sold_txns,\n                spent_native_token_ui_amount = EXCLUDED.spent_native_token_ui_amount,\n                spent_usd_token_ui_amount = EXCLUDED.spent_usd_token_ui_amount,\n                total_spent_usd = EXCLUDED.total_spent_usd,\n                received_native_token_ui_amount = EXCLUDED.received_native_token_ui_amount,\n                received_usd_token_ui_amount = EXCLUDED.received_usd_token_ui_amount,\n                total_received_usd = EXCLUDED.total_received_usd,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis,\n                update_block_number = EXCLUDED.update_block_number\n            WHERE token_holder.update_block_number <= EXCLUDED.update_block_number\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "NumericArray", "NumericArray", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array"]}, "nullable": []}, "hash": "aa48c7b9ad21242b9f67d866af45c029f8ef0970924cd364f01c3a3d14be2902"}