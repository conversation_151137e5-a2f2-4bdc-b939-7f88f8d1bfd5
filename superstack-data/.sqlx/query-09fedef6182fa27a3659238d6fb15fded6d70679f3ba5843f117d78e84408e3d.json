{"db_name": "PostgreSQL", "query": "\n            INSERT INTO perp_user_state (\n                perp_exchange, perp_user,\n                perps_account_value, account_value,\n                hyperliquid_acc_collateral, hyperliquid_acc_perps,\n                timestamp_millis\n            )\n            SELECT * FROM unnest(\n                $1::perp_exchange_enum[], $2::varchar[],\n                $3::double precision[], $4::double precision[],\n                $5::double precision[], $6::double precision[],\n                $7::bigint[]\n            )\n            ON CONFLICT (perp_exchange, perp_user, timestamp_millis) DO NOTHING\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "perp_exchange_enum[]", "kind": {"Array": {"Custom": {"name": "perp_exchange_enum", "kind": {"Enum": ["hyperliquid"]}}}}}}, "Varchar<PERSON><PERSON>y", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array"]}, "nullable": []}, "hash": "09fedef6182fa27a3659238d6fb15fded6d70679f3ba5843f117d78e84408e3d"}