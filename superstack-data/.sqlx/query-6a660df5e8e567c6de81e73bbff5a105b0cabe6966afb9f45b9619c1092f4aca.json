{"db_name": "PostgreSQL", "query": "\n            SELECT\n                chain as \"chain: Chain\",\n                timestamp_5_seconds,\n                timestamp_seconds,\n                usd_price\n            FROM native_token_price\n            WHERE chain = $1\n            AND timestamp_5_seconds >= $2\n            AND timestamp_5_seconds <= $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "timestamp_5_seconds", "type_info": "Int8"}, {"ordinal": 2, "name": "timestamp_seconds", "type_info": "Int8"}, {"ordinal": 3, "name": "usd_price", "type_info": "Float8"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Int8", "Int8"]}, "nullable": [false, false, false, false]}, "hash": "6a660df5e8e567c6de81e73bbff5a105b0cabe6966afb9f45b9619c1092f4aca"}