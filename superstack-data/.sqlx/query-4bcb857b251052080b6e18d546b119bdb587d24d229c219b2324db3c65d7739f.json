{"db_name": "PostgreSQL", "query": "\n            SELECT COUNT(*) as count\n            FROM extended_token_holder\n            WHERE chain = $1 AND token_address = $2 AND remaining_ui_amount > 0\n            ", "describe": {"columns": [{"ordinal": 0, "name": "count", "type_info": "Int8"}], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text"]}, "nullable": [null]}, "hash": "4bcb857b251052080b6e18d546b119bdb587d24d229c219b2324db3c65d7739f"}