{"db_name": "PostgreSQL", "query": "\n            INSERT INTO token_statistic (\n                chain, token_address,\n                name, symbol,\n                decimals, supply,\n                description, image, website, twitter, telegram, dex_paid,\n                is_trench_token,\n                create_dex, create_block_number, create_tx_hash, create_bonding_curve, create_dev, create_timestamp_millis,\n                migration_pool_address, migration_timestamp_millis,\n                update_timestamp_millis,\n                is_mutable, update_authority, mint_authority, freeze_authority,\n                best_pool_address, pool_addresses,\n                usd_price, usd_market_cap, usd_liquidity,\n                bonding_curve_progress,\n                usd_total_volume, usd_total_buy_volume, usd_total_sell_volume,\n                total_txns, total_buy_txns, total_sell_txns,\n                total_trend, total_price_change,\n                trend_5m, price_change_5m, txns_5m, buy_txns_5m, sell_txns_5m, usd_volume_5m, usd_buy_volume_5m, usd_sell_volume_5m,\n                trend_1h, price_change_1h, txns_1h, buy_txns_1h, sell_txns_1h, usd_volume_1h, usd_buy_volume_1h, usd_sell_volume_1h,\n                trend_6h, price_change_6h, txns_6h, buy_txns_6h, sell_txns_6h, usd_volume_6h, usd_buy_volume_6h, usd_sell_volume_6h,\n                trend_24h, price_change_24h, txns_24h, buy_txns_24h, sell_txns_24h, usd_volume_24h, usd_buy_volume_24h, usd_sell_volume_24h,\n                trend_3d, price_change_3d, txns_3d, buy_txns_3d, sell_txns_3d, usd_volume_3d, usd_buy_volume_3d, usd_sell_volume_3d,\n                trend_7d, price_change_7d, txns_7d, buy_txns_7d, sell_txns_7d, usd_volume_7d, usd_buy_volume_7d, usd_sell_volume_7d,\n                dev_hold_percentage, dev_sold_percentage, top10_hold_percentage, sniper_hold_percentage, insider_hold_percentage, bot_hold_percentage,\n                holder_count, sniper_count, insider_count, bot_count,\n                is_active, best_pool_dex, pool_dexes,\n                init_usd_price, image_path\n            )\n            SELECT * FROM unnest(\n                $1::chain_enum[], $2::varchar[],\n                $3::varchar[], $4::varchar[],\n                $5::smallint[], $6::varchar[],\n                $7::varchar[], $8::varchar[], $9::varchar[], $10::varchar[], $11::varchar[], $12::dex_paid_enum[],\n                $13::boolean[],\n                $14::dex_enum[], $15::bigint[], $16::varchar[], $17::varchar[], $18::varchar[], $19::bigint[],\n                $20::varchar[], $21::bigint[],\n                $22::bigint[],\n                $23::boolean[], $24::varchar[], $25::varchar[], $26::varchar[],\n                $27::varchar[], $28::varchar[],\n                $29::double precision[], $30::double precision[], $31::double precision[],\n                $32::double precision[],\n                $33::double precision[], $34::double precision[], $35::double precision[],\n                $36::bigint[], $37::bigint[], $38::bigint[],\n                $39::double precision[], $40::double precision[],\n                $41::double precision[], $42::double precision[], $43::bigint[], $44::bigint[], $45::bigint[], $46::double precision[], $47::double precision[], $48::double precision[],\n                $49::double precision[], $50::double precision[], $51::bigint[], $52::bigint[], $53::bigint[], $54::double precision[], $55::double precision[], $56::double precision[],\n                $57::double precision[], $58::double precision[], $59::bigint[], $60::bigint[], $61::bigint[], $62::double precision[], $63::double precision[], $64::double precision[],\n                $65::double precision[], $66::double precision[], $67::bigint[], $68::bigint[], $69::bigint[], $70::double precision[], $71::double precision[], $72::double precision[],\n                $73::double precision[], $74::double precision[], $75::bigint[], $76::bigint[], $77::bigint[], $78::double precision[], $79::double precision[], $80::double precision[],\n                $81::double precision[], $82::double precision[], $83::bigint[], $84::bigint[], $85::bigint[], $86::double precision[], $87::double precision[], $88::double precision[],\n                $89::double precision[], $90::double precision[], $91::double precision[], $92::double precision[], $93::double precision[], $94::double precision[],\n                $95::bigint[], $96::bigint[], $97::bigint[], $98::bigint[],\n                $99::boolean[], $100::dex_enum[], $101::varchar[],\n                $102::double precision[], $103::varchar[])\n            ON CONFLICT (chain, token_address) DO UPDATE SET\n                name = EXCLUDED.name,\n                symbol = EXCLUDED.symbol,\n                decimals = EXCLUDED.decimals,\n                supply = EXCLUDED.supply,\n                description = EXCLUDED.description,\n                image = EXCLUDED.image,\n                website = EXCLUDED.website,\n                twitter = EXCLUDED.twitter,\n                telegram = EXCLUDED.telegram,\n                dex_paid = EXCLUDED.dex_paid,\n                is_trench_token = EXCLUDED.is_trench_token,\n                create_dex = EXCLUDED.create_dex,\n                create_block_number = EXCLUDED.create_block_number,\n                create_tx_hash = EXCLUDED.create_tx_hash,\n                create_bonding_curve = EXCLUDED.create_bonding_curve,\n                create_dev = EXCLUDED.create_dev,\n                create_timestamp_millis = EXCLUDED.create_timestamp_millis,\n                migration_pool_address = EXCLUDED.migration_pool_address,\n                migration_timestamp_millis = EXCLUDED.migration_timestamp_millis,\n                is_mutable = EXCLUDED.is_mutable,\n                update_authority = EXCLUDED.update_authority,\n                mint_authority = EXCLUDED.mint_authority,\n                freeze_authority = EXCLUDED.freeze_authority,\n                best_pool_address = EXCLUDED.best_pool_address,\n                pool_addresses = EXCLUDED.pool_addresses,\n                usd_price = EXCLUDED.usd_price,\n                usd_market_cap = EXCLUDED.usd_market_cap,\n                usd_liquidity = EXCLUDED.usd_liquidity,\n                bonding_curve_progress = EXCLUDED.bonding_curve_progress,\n                usd_total_volume = EXCLUDED.usd_total_volume,\n                usd_total_buy_volume = EXCLUDED.usd_total_buy_volume,\n                usd_total_sell_volume = EXCLUDED.usd_total_sell_volume,\n                total_txns = EXCLUDED.total_txns,\n                total_buy_txns = EXCLUDED.total_buy_txns,\n                total_sell_txns = EXCLUDED.total_sell_txns,\n                total_trend = EXCLUDED.total_trend,\n                total_price_change = EXCLUDED.total_price_change,\n                trend_5m = EXCLUDED.trend_5m,\n                price_change_5m = EXCLUDED.price_change_5m,\n                txns_5m = EXCLUDED.txns_5m,\n                buy_txns_5m = EXCLUDED.buy_txns_5m,\n                sell_txns_5m = EXCLUDED.sell_txns_5m,\n                usd_volume_5m = EXCLUDED.usd_volume_5m,\n                usd_buy_volume_5m = EXCLUDED.usd_buy_volume_5m,\n                usd_sell_volume_5m = EXCLUDED.usd_sell_volume_5m,\n                trend_1h = EXCLUDED.trend_1h,\n                price_change_1h = EXCLUDED.price_change_1h,\n                txns_1h = EXCLUDED.txns_1h,\n                buy_txns_1h = EXCLUDED.buy_txns_1h,\n                sell_txns_1h = EXCLUDED.sell_txns_1h,\n                usd_volume_1h = EXCLUDED.usd_volume_1h,\n                usd_buy_volume_1h = EXCLUDED.usd_buy_volume_1h,\n                usd_sell_volume_1h = EXCLUDED.usd_sell_volume_1h,\n                trend_6h = EXCLUDED.trend_6h,\n                price_change_6h = EXCLUDED.price_change_6h,\n                txns_6h = EXCLUDED.txns_6h,\n                buy_txns_6h = EXCLUDED.buy_txns_6h,\n                sell_txns_6h = EXCLUDED.sell_txns_6h,\n                usd_volume_6h = EXCLUDED.usd_volume_6h,\n                usd_buy_volume_6h = EXCLUDED.usd_buy_volume_6h,\n                usd_sell_volume_6h = EXCLUDED.usd_sell_volume_6h,\n                trend_24h = EXCLUDED.trend_24h,\n                price_change_24h = EXCLUDED.price_change_24h,\n                txns_24h = EXCLUDED.txns_24h,\n                buy_txns_24h = EXCLUDED.buy_txns_24h,\n                sell_txns_24h = EXCLUDED.sell_txns_24h,\n                usd_volume_24h = EXCLUDED.usd_volume_24h,\n                usd_buy_volume_24h = EXCLUDED.usd_buy_volume_24h,\n                usd_sell_volume_24h = EXCLUDED.usd_sell_volume_24h,\n                trend_3d = EXCLUDED.trend_3d,\n                price_change_3d = EXCLUDED.price_change_3d,\n                txns_3d = EXCLUDED.txns_3d,\n                buy_txns_3d = EXCLUDED.buy_txns_3d,\n                sell_txns_3d = EXCLUDED.sell_txns_3d,\n                usd_volume_3d = EXCLUDED.usd_volume_3d,\n                usd_buy_volume_3d = EXCLUDED.usd_buy_volume_3d,\n                usd_sell_volume_3d = EXCLUDED.usd_sell_volume_3d,\n                trend_7d = EXCLUDED.trend_7d,\n                price_change_7d = EXCLUDED.price_change_7d,\n                txns_7d = EXCLUDED.txns_7d,\n                buy_txns_7d = EXCLUDED.buy_txns_7d,\n                sell_txns_7d = EXCLUDED.sell_txns_7d,\n                usd_volume_7d = EXCLUDED.usd_volume_7d,\n                usd_buy_volume_7d = EXCLUDED.usd_buy_volume_7d,\n                usd_sell_volume_7d = EXCLUDED.usd_sell_volume_7d,\n                dev_hold_percentage = EXCLUDED.dev_hold_percentage,\n                dev_sold_percentage = EXCLUDED.dev_sold_percentage,\n                top10_hold_percentage = EXCLUDED.top10_hold_percentage,\n                sniper_hold_percentage = EXCLUDED.sniper_hold_percentage,\n                insider_hold_percentage = EXCLUDED.insider_hold_percentage,\n                bot_hold_percentage = EXCLUDED.bot_hold_percentage,\n                holder_count = EXCLUDED.holder_count,\n                sniper_count = EXCLUDED.sniper_count,\n                insider_count = EXCLUDED.insider_count,\n                bot_count = EXCLUDED.bot_count,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis,\n                is_active = EXCLUDED.is_active,\n                best_pool_dex = EXCLUDED.best_pool_dex,\n                pool_dexes = EXCLUDED.pool_dexes,\n                init_usd_price = EXCLUDED.init_usd_price,\n                image_path = EXCLUDED.image_path\n            WHERE token_statistic.update_timestamp_millis <= EXCLUDED.update_timestamp_millis\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum[]", "kind": {"Array": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}}}, "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Int2Array", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", {"Custom": {"name": "dex_paid_enum[]", "kind": {"Array": {"Custom": {"name": "dex_paid_enum", "kind": {"Enum": ["unknown", "paid", "unpaid"]}}}}}}, "BoolArray", {"Custom": {"name": "dex_enum[]", "kind": {"Array": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}}}, "Int8Array", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Int8Array", "Varchar<PERSON><PERSON>y", "Int8Array", "Int8Array", "BoolArray", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Varchar<PERSON><PERSON>y", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Float8Array", "Int8Array", "Int8Array", "Int8Array", "Int8Array", "BoolArray", {"Custom": {"name": "dex_enum[]", "kind": {"Array": {"Custom": {"name": "dex_enum", "kind": {"Enum": ["unknown", "pumpfun", "pumpswap", "meteora", "hypercore", "hyperswapv3"]}}}}}}, "Varchar<PERSON><PERSON>y", "Float8Array", "Varchar<PERSON><PERSON>y"]}, "nullable": []}, "hash": "9412cca784c16923fb22096ccd96a97884aec61de92e1a18f0da913e9e8febce"}