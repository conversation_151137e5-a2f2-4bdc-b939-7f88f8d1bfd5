{"db_name": "PostgreSQL", "query": "\n            SELECT\n                ps.chain AS \"chain: Chain\",\n                ps.pool_address,\n                ps.block_number,\n                ps.timestamp_millis,\n                ps.price,\n                ps.market_cap,\n                ps.liquidity,\n                ps.total_volume,\n                ps.total_buy_volume,\n                ps.total_sell_volume,\n                ps.total_txns,\n                ps.total_buy_txns,\n                ps.total_sell_txns,\n                ps.bonding_curve_progress,\n                tt.target_timestamp\n            FROM unnest($1::bigint[]) AS tt(target_timestamp)\n            LEFT JOIN LATERAL (\n                SELECT *\n                FROM pool_state\n                WHERE chain = $2\n                  AND pool_address = $3\n                  AND timestamp_millis <= tt.target_timestamp\n                ORDER BY block_number DESC\n                LIMIT 1\n            ) ps ON true\n            ORDER BY tt.target_timestamp\n            ", "describe": {"columns": [{"ordinal": 0, "name": "chain: Chain", "type_info": {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}}, {"ordinal": 1, "name": "pool_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "block_number", "type_info": "Int8"}, {"ordinal": 3, "name": "timestamp_millis", "type_info": "Int8"}, {"ordinal": 4, "name": "price", "type_info": "Float8"}, {"ordinal": 5, "name": "market_cap", "type_info": "Float8"}, {"ordinal": 6, "name": "liquidity", "type_info": "Float8"}, {"ordinal": 7, "name": "total_volume", "type_info": "Float8"}, {"ordinal": 8, "name": "total_buy_volume", "type_info": "Float8"}, {"ordinal": 9, "name": "total_sell_volume", "type_info": "Float8"}, {"ordinal": 10, "name": "total_txns", "type_info": "Int8"}, {"ordinal": 11, "name": "total_buy_txns", "type_info": "Int8"}, {"ordinal": 12, "name": "total_sell_txns", "type_info": "Int8"}, {"ordinal": 13, "name": "bonding_curve_progress", "type_info": "Float8"}, {"ordinal": 14, "name": "target_timestamp", "type_info": "Int8"}], "parameters": {"Left": ["Int8Array", {"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "Text"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, false, true, null]}, "hash": "4c651f7d4c1f02bc232d64ad55c60bed96616aef27d69ce7b50965fa9178c526"}