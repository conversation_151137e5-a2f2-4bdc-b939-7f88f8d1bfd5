{"db_name": "PostgreSQL", "query": "\n            INSERT INTO token_holder (\n                chain, token_address, holder_address,\n                bought_amount, sold_amount,\n                bought_txns, sold_txns,\n                spent_native_token_ui_amount, spent_usd_token_ui_amount, total_spent_usd,\n                received_native_token_ui_amount, received_usd_token_ui_amount, total_received_usd,\n                update_timestamp_millis, update_block_number\n            )\n            VALUES (\n                $1, $2, $3,\n                $4, $5,\n                $6, $7,\n                $8, $9, $10,\n                $11, $12, $13,\n                $14, $15\n            )\n            ON CONFLICT (chain, token_address, holder_address) DO UPDATE SET\n                bought_amount = EXCLUDED.bought_amount,\n                sold_amount = EXCLUDED.sold_amount,\n                bought_txns = EXCLUDED.bought_txns,\n                sold_txns = EXCLUDED.sold_txns,\n                spent_native_token_ui_amount = EXCLUDED.spent_native_token_ui_amount,\n                spent_usd_token_ui_amount = EXCLUDED.spent_usd_token_ui_amount,\n                total_spent_usd = EXCLUDED.total_spent_usd,\n                received_native_token_ui_amount = EXCLUDED.received_native_token_ui_amount,\n                received_usd_token_ui_amount = EXCLUDED.received_usd_token_ui_amount,\n                total_received_usd = EXCLUDED.total_received_usd,\n                update_timestamp_millis = EXCLUDED.update_timestamp_millis,\n                update_block_number = EXCLUDED.update_block_number\n            WHERE token_holder.update_block_number <= EXCLUDED.update_block_number\n            ", "describe": {"columns": [], "parameters": {"Left": [{"Custom": {"name": "chain_enum", "kind": {"Enum": ["solana", "hypercore", "hyperevm"]}}}, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Numeric", "Numeric", "Int8", "Int8", "Float8", "Float8", "Float8", "Float8", "Float8", "Float8", "Int8", "Int8"]}, "nullable": []}, "hash": "652a4f471567636230f652026f999d1933226a80b1127fbec63516e11a073d24"}