[package]
name = "superstack-data"
version.workspace = true
edition.workspace = true

[features]
default = []
local_storage = ["rocksdb", "sha2", "parking_lot"]

[dependencies]
solana-client = { workspace = true }
solana-sdk = { workspace = true }
spl-token = { workspace = true }
spl-token-2022 = { workspace = true }
mpl-token-metadata = { workspace = true }

alloy = { workspace = true }

reqwest = { workspace = true }
backon = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
serde_with = { workspace = true }

anyhow = { workspace = true }
futures = { workspace = true }
tokio-stream = { workspace = true }
tokio = { workspace = true }
thiserror = { workspace = true }
chrono = { workspace = true }
bincode = { workspace = true }
rustls = { workspace = true }
bigdecimal = { workspace = true }
ciborium = { workspace = true }

tracing = { workspace = true }
tracing-subscriber = { workspace = true }

sqlx = { workspace = true }
redis = { workspace = true }
rdkafka = { workspace = true }

# Local Storage
rocksdb = { version = "0.22", default-features = false, features = ["lz4"], optional = true }
sha2 = { version = "0.10.8", optional = true }
parking_lot = { version = "0.12", optional = true }

[dev-dependencies]
dotenv = { workspace = true }
