-- 修复transfer记录的分类问题
-- 基于metadata中的from_address和to_address重新分类internalTransfer类型的记录

-- 首先查看当前的transfer记录情况
SELECT 
    wallet_address,
    activity_type,
    metadata->>'transfer_type' as transfer_type,
    metadata->>'from_address' as from_address,
    metadata->>'to_address' as to_address,
    COUNT(*) as count
FROM wallet_activity 
WHERE activity_type = 'transfer' 
    AND chain = 1 
    AND metadata->>'source' = 'nonfunding_ledger'
GROUP BY wallet_address, activity_type, metadata->>'transfer_type', metadata->>'from_address', metadata->>'to_address'
ORDER BY wallet_address, count DESC;

-- 修复internalTransfer类型的记录分类
-- 情况1: destination = wallet_address -> 应该是deposit
UPDATE wallet_activity 
SET activity_type = 'deposit'
WHERE activity_type = 'transfer'
    AND chain = 1
    AND metadata->>'source' = 'nonfunding_ledger'
    AND metadata->>'transfer_type' IN ('receive', 'internal_transfer')
    AND metadata->>'to_address' = wallet_address
    AND metadata->>'from_address' != wallet_address;

-- 情况2: from_address = wallet_address -> 应该是withdraw  
UPDATE wallet_activity 
SET activity_type = 'withdraw'
WHERE activity_type = 'transfer'
    AND chain = 1
    AND metadata->>'source' = 'nonfunding_ledger'
    AND metadata->>'transfer_type' IN ('send', 'internal_transfer')
    AND metadata->>'from_address' = wallet_address
    AND metadata->>'to_address' != wallet_address;

-- 保持accountClassTransfer类型为transfer（spot_to_perp, perp_to_spot）
-- 这些记录应该保持不变，因为它们是内部账户转账

-- 验证修复结果
SELECT 
    'After Fix' as status,
    activity_type,
    metadata->>'transfer_type' as transfer_type,
    COUNT(*) as count
FROM wallet_activity 
WHERE chain = 1 
    AND metadata->>'source' = 'nonfunding_ledger'
GROUP BY activity_type, metadata->>'transfer_type'
ORDER BY activity_type, count DESC;

-- 详细验证：检查特定钱包的分类结果
SELECT 
    wallet_address,
    tx_signature,
    activity_type,
    metadata->>'transfer_type' as transfer_type,
    metadata->>'from_address' as from_address,
    metadata->>'to_address' as to_address,
    token_amount,
    usd_value,
    timestamp
FROM wallet_activity 
WHERE wallet_address = '******************************************'
    AND chain = 1
    AND metadata->>'source' = 'nonfunding_ledger'
    AND activity_type IN ('deposit', 'withdraw', 'transfer')
ORDER BY timestamp DESC
LIMIT 10;
