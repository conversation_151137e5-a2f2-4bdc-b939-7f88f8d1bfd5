#!/bin/bash

# Redis Cluster Clear Script
# This script clears all data from Redis cluster

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Check if REDIS_URL is set
if [ -z "$REDIS_URL" ]; then
    echo -e "${RED}❌ Error: REDIS_URL environment variable is not set${NC}"
    echo "Please set REDIS_URL in your .env file or environment"
    exit 1
fi

# Extract connection details from Redis URL
# Format: rediss://username:password@host:port
if [[ $REDIS_URL =~ ^rediss?://([^:]+):([^@]+)@([^:]+):([0-9]+)$ ]]; then
    USERNAME="${BASH_REMATCH[1]}"
    PASSWORD="${BASH_REMATCH[2]}"
    HOST="${BASH_REMATCH[3]}"
    PORT="${BASH_REMATCH[4]}"
    
    # Determine if TLS is needed
    if [[ $REDIS_URL == rediss://* ]]; then
        TLS_OPTION="--tls"
    else
        TLS_OPTION=""
    fi
else
    echo -e "${RED}❌ Error: Invalid REDIS_URL format${NC}"
    echo "Expected format: redis://username:password@host:port or rediss://username:password@host:port"
    exit 1
fi

# Mask password for display
MASKED_URL=$(echo "$REDIS_URL" | sed 's/:'"$PASSWORD"'@/:****@/')

echo -e "${BLUE}🔗 Redis URL: $MASKED_URL${NC}"
echo -e "${BLUE}🏠 Host: $HOST:$PORT${NC}"
echo -e "${BLUE}👤 Username: $USERNAME${NC}"

# Check if redis-cli is available
if ! command -v redis-cli &> /dev/null; then
    echo -e "${RED}❌ Error: redis-cli is not installed${NC}"
    echo "Please install redis-cli:"
    echo "  Ubuntu/Debian: sudo apt-get install redis-tools"
    echo "  macOS: brew install redis"
    echo "  CentOS/RHEL: sudo yum install redis"
    exit 1
fi

# Warning and confirmation
echo -e "${YELLOW}⚠️  WARNING: This will delete ALL data in the Redis cluster!${NC}"
echo -e "${YELLOW}⚠️  This action cannot be undone!${NC}"
echo ""
read -p "Do you want to continue? (yes/no): " -r
echo ""

if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo -e "${RED}❌ Operation cancelled${NC}"
    exit 0
fi

echo -e "${BLUE}🚀 Connecting to Redis cluster...${NC}"

# Test connection first
echo -e "${BLUE}🔍 Testing connection...${NC}"
if redis-cli -h "$HOST" -p "$PORT" -a "$PASSWORD" $TLS_OPTION ping > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Connection successful${NC}"
else
    echo -e "${RED}❌ Connection failed${NC}"
    echo "Please check your Redis URL and network connectivity"
    exit 1
fi

# Get cluster info
echo -e "${BLUE}📊 Getting cluster information...${NC}"
CLUSTER_INFO=$(redis-cli -h "$HOST" -p "$PORT" -a "$PASSWORD" $TLS_OPTION cluster info 2>/dev/null | head -3 || echo "Could not get cluster info")
echo -e "${BLUE}ℹ️  $CLUSTER_INFO${NC}"

# Final confirmation
echo ""
echo -e "${RED}🔥 FINAL WARNING: All data will be permanently deleted!${NC}"
read -p "Type 'FLUSH' to proceed: " -r
echo ""

if [[ $REPLY != "FLUSH" ]]; then
    echo -e "${RED}❌ Operation cancelled${NC}"
    exit 0
fi

echo -e "${BLUE}🧹 Executing FLUSHALL command...${NC}"

# Execute FLUSHALL
if redis-cli -h "$HOST" -p "$PORT" -a "$PASSWORD" $TLS_OPTION flushall; then
    echo -e "${GREEN}✅ Redis cluster cleared successfully!${NC}"
else
    echo -e "${RED}❌ Failed to clear Redis cluster${NC}"
    exit 1
fi

# Verify the operation
echo -e "${BLUE}🔍 Verifying operation...${NC}"
DBSIZE=$(redis-cli -h "$HOST" -p "$PORT" -a "$PASSWORD" $TLS_OPTION dbsize 2>/dev/null || echo "unknown")

if [[ $DBSIZE == "0" ]]; then
    echo -e "${GREEN}✅ Verification successful: Database is empty (size: $DBSIZE)${NC}"
elif [[ $DBSIZE == "unknown" ]]; then
    echo -e "${YELLOW}⚠️  Could not verify database size${NC}"
else
    echo -e "${YELLOW}⚠️  Warning: Database size is $DBSIZE (expected 0)${NC}"
fi

echo -e "${GREEN}🎉 Operation completed!${NC}"
