# superstack backend

## Overview

This repository contains the source code for the superstack backend. It is split into the following crates:

- `superstack-data`: Contains data models, database schemas, message queue support, and memory database support for the superstack-indexer and superstack-aggregator.
- `superstack-indexer`: Indexes blockchain data, stores it in the database, and sends it to superstack-aggregator via message queue.
- `superstack-aggregator`: Aggregates data from the superstack-indexer via message queue, stores it in the database, and sends it to superstack-api via message queue.
- `superstack-api`: Serves the REST API for the frontend.
- `superstack-websocket`: Serves the websocket API for the frontend.


## Dependencies

- Kafka (Confluent Cloud)
- Redis (Redis Cloud)

```bash
# install dependencies for Kafka Confluent Cloud
sudo apt-get update
sudo apt-get install libssl-dev libsasl2-dev pkg-config cmake clang libclang-dev build-essential -y
```