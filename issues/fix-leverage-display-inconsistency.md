# 修复ETH开仓和平仓杠杆显示不一致问题

## 问题描述

用户报告ETH开仓和平仓显示的杠杆数不同：
- ETH开仓显示为18x Long
- ETH平仓显示为11x Long
- 但数据库中存储的原始杠杆数据都是25x

## 根本原因

问题出现在`superstack-api/src/models/hyperliquid_activity.rs`文件中的`HyperliquidFill::calculate_leverage()`函数：

```rust
fn calculate_leverage(start_position: &str, trade_size: &str) -> Option<f64> {
    let start_pos = start_position.parse::<f64>().ok()?;
    let trade_sz = trade_size.parse::<f64>().ok()?;

    if start_pos.abs() < 1e-8 {
        return Some(1.0);
    }

    // 错误的计算公式！
    let leverage = (start_pos.abs() + trade_sz.abs()) / trade_sz.abs();
    
    let capped_leverage = leverage.min(100.0).max(1.0);
    Some((capped_leverage * 10.0).round() / 10.0)
}
```

这个函数使用了错误的杠杆计算公式：`(仓位大小 + 交易大小) / 交易大小`，这不是杠杆的正确计算方式。

### 具体例子

- 如果start_position是0.0137 ETH，trade_size是0.0057 ETH
- 错误计算：(0.0137 + 0.0057) / 0.0057 = 3.4x
- 但实际杠杆应该是25x（来自Hyperliquid的原始数据）

## 解决方案

### 方案1：移除错误的杠杆计算逻辑（已实施）

1. **移除`calculate_leverage()`函数**
2. **移除在`HyperliquidFill::to_db_activity()`中的错误杠杆计算调用**
3. **直接使用Hyperliquid提供的原始杠杆数据**

### 修改内容

1. 删除了`HyperliquidFill::calculate_leverage()`函数
2. 修改了`HyperliquidFill::to_db_activity()`方法，移除了错误的杠杆计算
3. 添加了注释说明杠杆信息应该由数据源（PerpsOrder）提供

## 数据流说明

- **HyperliquidFill**: 不包含杠杆字段，不应该尝试计算杠杆
- **PerpsOrder**: 包含正确的leverage字段，直接使用原始数据
- **数据合并**: 在合并过程中保留正确的杠杆信息

## 预期结果

修复后：
- ETH开仓和平仓都显示正确的25x杠杆
- 前端显示的杠杆数据与数据库中存储的一致
- 移除了错误的杠杆计算逻辑，避免未来的类似问题

## 测试验证

添加了测试用例`test_hyperliquid_fill_no_leverage_calculation`来验证：
- HyperliquidFill不再添加错误的杠杆计算到metadata中
- position_action字段正确计算
- 其他字段不受影响

## 影响范围

- 仅影响HyperliquidFill数据类型的杠杆显示
- PerpsOrder和其他数据类型的杠杆处理保持不变
- 不影响其他功能
