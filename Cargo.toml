[workspace]
members = [
    "superstack-hypurr",
    "superstack-aggregator",
    "superstack-api",
    "superstack-data",
    "superstack-indexer",
    "superstack-websocket",
    "superstack-bot",
]
exclude = []
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"


[workspace.dependencies]
# Solana Dependencies
solana-client = "2.2"
solana-sdk = "=2.2.1"
solana-transaction-status-client-types = "=2.2.1"
solana-transaction-status = "2.2"
solana-instruction = "2.2"
solana-message = "2.2"
solana-account-decoder-client-types = "2.2"
spl-associated-token-account = "6.0.0"
spl-token = "8.0.0"
spl-token-2022 = "9.0.0"
spl-token-metadata-interface = "0.7.0"
mpl-token-metadata = "5.1.0"
borsh = "1.5"

# EVM
alloy = { version = "1.0", features = [
    "sol-types",
    "k256",
    "serde",
    "signers",
    "signer-local",
    "reqwest",
    "providers",
    "rpc",
    "contract",
    "rpc-types",
    "consensus",
    "rlp",
], default-features = false }

tokio = { version = "1", features = ["full"] }
tokio-tungstenite = "0.27"
axum = { version = "0.8.1", features = ["multipart"] }
tower-http = { version = "0.6", features = [
    "cors",
    "trace",
    "fs",
    "set-header",
] }
tower = "0.5.2"
reqwest = { version = "0.12", features = ["json"] }
rustls = { version = "0.23" }


serde = { version = "1.0.218", features = ["derive"] }
serde_json = "1.0"
serde_repr = "0.1.7"
serde_with = { version = "3.0", features = ["base64"] }

tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

sqlx = { version = "0.8", features = [
    "postgres",
    "runtime-tokio",
    "tls-rustls-aws-lc-rs",
    "migrate",
    "uuid",
    "time",
    "bigdecimal",
] }

time = { version = "0.3", features = ["serde"] }
chrono = { version = "0.4", features = ["serde"] }

anyhow = "1.0"
thiserror = "2.0"
hex = "0.4"
dotenv = "0.15"
base64 = "0.22.1"
bincode = "1.3.3"
hmac-sha256 = "1.1.8"
sha2 = "0.10.8"
rust_decimal = { version = "1.32.0", features = ["maths"] }
bigdecimal = { version = "0.4", features = ["serde-json"] }
num-traits = "0.2"
rand = "0.8"
async-trait = "0.1"
futures-util = "0.3"
futures = "0.3"
itertools = "0.14"
tokio-stream = "0.1"
async-stream = "0.3"
url = "2.5"
urlencoding = "2.1.3"
backon = "1.5"
ciborium = "0.2.2"

indexmap = "2.10.0"
dashmap = "6.1.0"
quick_cache = "0.6.13"
moka = { version = "0.12.10", features = ["future"] }
lru = "0.12"
uuid = { version = "1.0", features = ["v4"] }
sha3 = "0.10"

redis = { version = "0.32.0", features = [
    "tokio-rustls-comp",
    "cluster-async",
    "tls-rustls-insecure",
] }
rdkafka = { version = "0.37", features = ["cmake-build", "ssl", "sasl"] }

coingecko = "1.1.3"
cmc = { version = "0.4.4", features = ["async", "cryptocurrency"] }

teloxide = { version = "0.17.0", features = ["macros"] }
