use std::net::SocketAddr;

use axum::routing::{get, Router};
use tokio::{net::TcpListener, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};

async fn health() -> &'static str {
    "OK"
}

pub fn spawn_server(port: u16, router_path: &'static str) -> <PERSON><PERSON><PERSON><PERSON><PERSON><()> {
    tokio::spawn(async move {
        let app = Router::new().route(router_path, get(health));
        let addr = SocketAddr::from(([0, 0, 0, 0], port));
        let listener = TcpListener::bind(addr).await.unwrap();

        tracing::info!("Telegram bot server starting on {}", addr);

        match axum::serve(listener, app).await {
            Ok(_) => {}
            Err(e) => tracing::error!("Server error: {}", e),
        }
    })
}
