mod bot;
mod config;
mod healthz;

use std::sync::OnceLock;

use superstack_api::models::ApiDatabase;

pub static API_DB: OnceLock<ApiDatabase> = OnceLock::new();

#[tokio::main]
async fn main() {
    superstack_api::utils::setup_tracing();

    let config = crate::config::Config::get();

    let bot = crate::bot::TelegramBot::get();
    let bot_username = bot.bot_username().await.expect("Failed to get bot username");
    tracing::info!("Superstack bot(@{}) is running...", bot_username);

    // bot.set_bot_name("Superstack Bot").await.expect("Failed to set bot name");

    let port = config.port;
    let server_handle = crate::healthz::spawn_server(port, "/hz");

    let api_db = ApiDatabase::new(&config.api_database_url, config.max_db_connections, false)
        .await
        .expect("Failed to initialize API database");
    API_DB.set(api_db).expect("Failed to set API database instance");

    let bot_handle = crate::bot::TelegramBot::run_bot();

    tokio::select! {
        _ = server_handle => {
            tracing::info!("Server handle finished");
        }
        _ = bot_handle => {
            tracing::info!("Bot handle finished");
        }
    }
}
