use std::sync::OnceLock;

use anyhow::Result;
use teloxide::{
    prelude::*,
    types::{InlineKeyboardButton, InlineKeyboardMarkup},
    utils::command::BotCommands,
};
use tokio::{sync::OnceCell, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};
use uuid::Uuid;

use superstack_api::models::TelegramUser;

use crate::{config::Config, API_DB};

#[derive(Bo<PERSON><PERSON>ommands, Clone)]
#[command(rename_rule = "lowercase", description = "These commands are supported:")]
enum Command {
    #[command(description = "display this text.")]
    Help,
    #[command(description = "start verifying your Telegram account on Superstack.")]
    Start,
}

pub struct TelegramBot {
    bot: Bot,
}

impl TelegramBot {
    const TELEGRAM_BOT_LOCK_KEY: &str = "superstack_api_telegram_bot_lock";

    pub fn get() -> &'static TelegramBot {
        static INSTANCE: OnceLock<TelegramBot> = OnceLock::new();
        INSTANCE.get_or_init(|| {
            let config = Config::get();
            let bot_token = config.telegram_bot_token.clone();
            let bot = Bot::new(bot_token);
            TelegramBot { bot }
        })
    }

    pub async fn bot_username(&self) -> Result<String> {
        static BOT_USERNAME: OnceCell<String> = OnceCell::const_new();

        let bot_username = BOT_USERNAME
            .get_or_try_init(|| async {
                let me = self
                    .bot
                    .get_me()
                    .await
                    .map_err(|e| anyhow::anyhow!("Failed to get bot me: {}", e))?;
                let bot_username =
                    me.user.username.ok_or(anyhow::anyhow!("Bot username is not set"))?;
                Ok::<_, anyhow::Error>(bot_username)
            })
            .await?;

        Ok(bot_username.clone())
    }

    pub async fn set_bot_name(&self, name: &str) -> Result<()> {
        let bot_name = self.bot.get_my_name().await?;
        tracing::info!("Before set bot name: {:?}", bot_name);

        self.bot.set_my_name().name(name).send().await?;

        let bot_name = self.bot.get_my_name().await?;
        tracing::info!("After set bot name: {:?}", bot_name);

        Ok(())
    }

    fn generate_auth_code(user_id: u64, timestamp_millis: u64) -> String {
        let uuid_left = Uuid::from_u64_pair(user_id, timestamp_millis);
        let uuid_right = Uuid::new_v4();
        format!("{}{}", uuid_left.simple(), uuid_right.simple())
    }

    fn generate_bind_url(user: &TelegramUser) -> String {
        let config = Config::get();
        let frontend_url = config.telegram_bind_frontend_url.trim_end_matches('/');

        let mut bind_url = String::from(frontend_url);

        let state = user.auth_code.clone().unwrap_or_else(|| {
            Self::generate_auth_code(user.id, chrono::Utc::now().timestamp_millis() as u64)
        });
        bind_url.push_str("?state=");
        bind_url.push_str(&urlencoding::encode(&state));

        bind_url.push_str("&telegram_id=");
        bind_url.push_str(&user.id.to_string());

        bind_url.push_str("&telegram_firstname=");
        bind_url.push_str(&urlencoding::encode(&user.first_name));

        if let Some(username) = user.username.as_ref() {
            bind_url.push_str("&telegram_username=");
            bind_url.push_str(&urlencoding::encode(username));
        }

        bind_url
    }

    async fn answer(bot: Bot, msg: Message, cmd: Command) -> ResponseResult<()> {
        match cmd {
            Command::Help => {
                bot.send_message(msg.chat.id, Command::descriptions().to_string()).await?
            }
            Command::Start => {
                if let Some(user) = msg.from {
                    let api_db = API_DB.get().expect("API database is not initialized");
                    let now_millis = chrono::Utc::now().timestamp_millis();
                    let now_seconds = now_millis / 1000;
                    let auth_code = Self::generate_auth_code(user.id.0, now_millis as u64);
                    let telegram_user = TelegramUser {
                        id: user.id.0,
                        username: user.username,
                        first_name: user.first_name,
                        last_name: user.last_name,
                        is_bot: user.is_bot,
                        is_premium: user.is_premium,
                        created_at: now_seconds,
                        updated_at: now_seconds,
                        bind_account: None,
                        bind_at: None,
                        auth_code: Some(auth_code),
                        auth_code_created_at: Some(now_seconds),
                    };

                    tracing::info!("Received user start command: UserId={}, Username={:?}, FirstName={:?}, LastName={:?}, IsBot={}, IsPremium={}",
                        telegram_user.id,
                        telegram_user.username,
                        telegram_user.first_name,
                        telegram_user.last_name,
                        telegram_user.is_bot,
                        telegram_user.is_premium);

                    match api_db.insert_telegram_user_or_update_init_info(&telegram_user).await {
                        Ok(bind_account) => {
                            if bind_account.is_some() {
                                bot.send_message(
                                    msg.chat.id,
                                    "Your Telegram account has been verified on Superstack",
                                )
                                .await?;
                                return Ok(());
                            }
                        }
                        Err(e) => {
                            tracing::error!("Failed to record telegram user: {}", e);
                            bot.send_message(
                                msg.chat.id,
                                "Failed to get verification link, please try again later.",
                            )
                            .await?;
                            return Ok(());
                        }
                    }

                    let bind_url = Self::generate_bind_url(&telegram_user);
                    let keyboard =
                        InlineKeyboardMarkup::new(vec![vec![InlineKeyboardButton::url(
                            "Verify",
                            reqwest::Url::parse(&bind_url).unwrap(),
                        )]]);

                    bot.send_message(
                        msg.chat.id,
                        "Click the button below to unlock your invite codes!",
                    )
                    .reply_markup(keyboard)
                    .await?
                } else {
                    bot.send_message(msg.chat.id, "Unexpected error: Can not find user info")
                        .await?
                }
            }
        };

        Ok(())
    }

    async fn run_command_loop(&self) {
        Command::repl(self.bot.clone(), Self::answer).await;
    }

    pub fn run_bot() -> JoinHandle<()> {
        tokio::spawn(async move {
            let bot = TelegramBot::get();
            bot.run_command_loop().await;
        })
    }

    // pub fn run_bot_with_distributed_lock(storage_state: StorageState) -> JoinHandle<()> {
    //     let lock_ttl_secs = 10;
    //     let sleep_secs = 5;
    //     tokio::spawn(async move {
    //         loop {
    //             let redis_client = storage_state.redis_client.clone();

    //             let lock = match DistributedLockGuard::new(
    //                 redis_client.clone(),
    //                 Self::TELEGRAM_BOT_LOCK_KEY,
    //                 lock_ttl_secs,
    //             )
    //             .await
    //             {
    //                 Ok(lock) => lock,
    //                 Err(e) => {
    //                     tracing::error!("Failed to acquire lock: {}", e);
    //                     tokio::time::sleep(std::time::Duration::from_secs(sleep_secs)).await;
    //                     continue;
    //                 }
    //             };
    //             // If the lock is not acquired, it means that there is another instance of bot
    //             // running
    //             if !lock.is_acquired() {
    //                 tokio::time::sleep(std::time::Duration::from_secs(sleep_secs)).await;
    //                 continue;
    //             }

    //             // Run the bot in a separate thread
    //             let join_handle = tokio::spawn(async move {
    //                 tracing::info!("Get the lock, telegram bot is running");
    //                 let bot = TelegramBot::get();
    //                 bot.run_command_loop().await;
    //             });

    //             while let Ok(is_held) = lock.is_held().await {
    //                 if !is_held {
    //                     tracing::warn!("The lock is not held, stop the bot");
    //                     break;
    //                 }
    //                 if let Ok(extended) = lock.extend(0).await {
    //                     if !extended {
    //                         tracing::warn!("Failed to extend the lock, stop the bot");
    //                         break;
    //                     }
    //                 }
    //                 tokio::time::sleep(std::time::Duration::from_secs(sleep_secs)).await;
    //             }

    //             join_handle.abort();
    //         }
    //     })
    // }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_telegram_auth_code() {
        superstack_api::utils::setup_tracing();

        let now = chrono::Utc::now().timestamp_millis();
        let code = TelegramBot::generate_auth_code(1234567890, now as u64);
        tracing::info!("Generated auth code: {}", code);
    }
}
