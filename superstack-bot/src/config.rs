use std::{env, sync::OnceLock};

use dotenv::dotenv;

pub struct Config {
    pub port: u16,
    pub api_database_url: String,
    pub max_db_connections: u32,

    pub telegram_bot_token: String,
    pub telegram_bind_frontend_url: String,
}

impl Config {
    pub fn get() -> &'static Config {
        static INSTANCE: OnceLock<Config> = OnceLock::new();

        INSTANCE.get_or_init(|| {
            dotenv().ok();

            let port = env::var("PORT")
                .unwrap_or_else(|_| "3000".to_string())
                .parse::<u16>()
                .expect("PORT is not a valid number");

            let api_database_url = env::var("DATABASE_URL").expect("DATABASE_URL is not set");

            let max_db_connections = env::var("MAX_DB_CONNECTIONS")
                .unwrap_or_else(|_| "30".to_string())
                .parse::<u32>()
                .expect("MAX_DB_CONNECTIONS is not a valid number");

            let telegram_bot_token =
                env::var("TELEGRAM_BOT_TOKEN").expect("TELEGRAM_BOT_TOKEN is not set");

            let telegram_bind_frontend_url = env::var("TELEGRAM_BIND_FRONTEND_URL")
                .expect("TELEGRAM_BIND_FRONTEND_URL is not set");

            Config {
                port,
                api_database_url,
                max_db_connections,
                telegram_bot_token,
                telegram_bind_frontend_url,
            }
        })
    }
}
