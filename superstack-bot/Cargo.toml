[package]
name = "bot"
version.workspace = true
edition.workspace = true

[dependencies]
superstack-api = { path = "../superstack-api" }

tokio = { workspace = true }
axum = { workspace = true }
reqwest = { workspace = true }

anyhow = { workspace = true }
uuid = { workspace = true }
dotenv = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
urlencoding = { workspace = true }
chrono = { workspace = true }

teloxide = { workspace = true }
