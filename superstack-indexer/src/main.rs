use backon::{ExponentialBuilder, Retryable};
use superstack_data::{
    kafka::KafkaProducer, postgres::PostgresDatabase, price::NativeTokenPriceManager,
    redis::RedisClient,
};
use superstack_indexer::{
    config::Config,
    healthz,
    hypercore::{client::HypercoreClient, HypercoreIndexer},
    hyperevm::indexer::HyperEvmIndexer,
    solana::indexer::SolanaIndexer,
    store_task::StoreTask,
};

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();

    // Setup tracing
    superstack_data::utils::setup_tracing();

    // Initialize indexer db
    tracing::info!("Initializing indexer db");
    let _db = PostgresDatabase::get_indexer_db().await;

    // Initialize price manager
    tracing::info!("Initializing price manager");
    let _price_manager = NativeTokenPriceManager::get().await;

    // Initialize kafka producer for indexer topics
    tracing::info!("Initializing kafka producer for indexer topics");
    let ft = move || async move {
        let producer = KafkaProducer::get();
        if let Err(e) = producer.create_indexer_topics_if_not_exists().await {
            tracing::error!("Failed to create indexer topics: {}", e);
        }
        Ok::<(), anyhow::Error>(())
    };
    ft.retry(ExponentialBuilder::default()).await.expect("Failed to initialize indexer topics");

    // Initialize store task
    tracing::info!("Initializing store task");
    let config = Config::get();
    let store_channel_size = config.solana_task_concurrency.unwrap_or(1000) * 10;
    let (store_channel_sender, store_join_handle) =
        StoreTask::run(store_channel_size).expect("Failed to initialize store task");

    // Start healthz server
    let healthz_server = healthz::spawn_server(Config::get().port, "/hz");

    // Initialize redis indexes
    tracing::info!("Initializing redis indexes");
    let redis_client = RedisClient::get_instance().await;

    // Create search indexes first
    tracing::info!("Creating Redis search indexes...");
    if let Err(e) = redis_client.create_token_statistic_index().await {
        tracing::error!("Failed to create token statistic index: {}", e);
    }
    if let Err(e) = redis_client.create_pool_statistic_index().await {
        tracing::error!("Failed to create pool statistic index: {}", e);
    }

    // Initialize redis with database if not initialized
    redis_client
        .init_redis_with_database()
        .await
        .expect("Failed to initialize redis with database");

    // Start hyperevm indexer
    let hyperevm =
        tokio::spawn(async move { HyperEvmIndexer::init(Config::get()).await.run().await });

    // Start hypercore indexer
    let hypercore = HypercoreIndexer::new(HypercoreClient::new(
        Config::get().hypercore_base_url.clone(),
        Config::get().hypercore_ws_url.clone(),
        Config::get().hypercore_is_testnet,
    ))
    .run()
    .await
    .expect("Failed to run hypercore indexer");

    // Start solana indexer
    let solana = SolanaIndexer::init(Config::get(), store_channel_sender)
        .expect("Failed to initialize solana indexer");

    tokio::select!(
        _ = healthz_server => {
            tracing::info!("Healthz server exited");
        },
        _ = solana.run() => {
            tracing::info!("Solana indexer exited");
        },
        _ = hypercore => {
            tracing::info!("Hypercore indexer exited");
        },
        _ = hyperevm => {
            tracing::info!("HyperEvm indexer exited");
        },
    );

    tracing::info!("Waiting for store task to exit");
    let _ = store_join_handle.await;
    tracing::info!("Store task exited, exiting indexer");
}
