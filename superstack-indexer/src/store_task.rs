use tokio::{sync::mpsc, task::<PERSON><PERSON><PERSON><PERSON><PERSON>, time::Duration};

use anyhow::Result;
use superstack_data::postgres::indexer::TokenHolder;

use crate::cache::UnsyncDatabaseBuf;

pub type StoreTaskMessage = Vec<TokenHolder>;

pub struct StoreTask {}

impl StoreTask {
    pub fn run(
        store_channel_size: usize,
    ) -> Result<(mpsc::Sender<StoreTaskMessage>, Jo<PERSON><PERSON><PERSON><PERSON><()>)> {
        let (store_tx, store_rx) = mpsc::channel::<StoreTaskMessage>(store_channel_size);

        let join_handle = tokio::spawn(Self::store(store_rx));

        Ok((store_tx, join_handle))
    }

    async fn store(mut store_rx: mpsc::Receiver<Vec<TokenHolder>>) {
        let mut unsync_database_buf = UnsyncDatabaseBuf::new();

        let mut interval = tokio::time::interval(Duration::from_millis(100));
        loop {
            interval.tick().await;

            unsync_database_buf.save_and_publish_token_holders().await;

            loop {
                match store_rx.try_recv() {
                    Ok(token_holders) => {
                        for token_holder in token_holders {
                            unsync_database_buf.insert_token_holder(token_holder);
                        }
                        if unsync_database_buf.should_save().await {
                            unsync_database_buf.save_and_publish_token_holders().await;
                        }
                    }
                    Err(mpsc::error::TryRecvError::Empty) => {
                        break;
                    }
                    Err(mpsc::error::TryRecvError::Disconnected) => {
                        tracing::error!("Store task channel disconnected");
                        tracing::info!("Saving all unsynced data before exiting");
                        unsync_database_buf.save_and_publish_token_holders().await;
                        tracing::info!("All unsynced data saved, exiting store task");

                        std::process::exit(1);
                    }
                }
            }
        }
    }
}
