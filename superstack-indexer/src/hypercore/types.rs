use serde::{Deserialize, Serialize};

pub const TESTNET_BASE_URL: &str = "https://api.hyperliquid-testnet.xyz";
pub const MAINNET_BASE_URL: &str = "https://api.hyperliquid.xyz";

pub const MAINNET_WS_URL: &str = "wss://rpc.hyperliquid.xyz/ws";
pub const MAINNET_WS_EXPLORER_URL: &str = "wss://rpc.hyperliquid.xyz/ws";
pub const TESTNET_WS_URL: &str = "wss://rpc.hyperliquid-testnet.xyz/ws";
pub const TESTNET_WS_EXPLORER_URL: &str = "wss://rpc.hyperliquid-testnet.xyz/ws";

#[derive(Clone, Debug, Deserialize)]
pub struct CoinGeckoCoin {
    pub id: String,
    pub name: String,
    pub symbol: String,
}

#[derive(Debug, Deserialize)]
pub struct CoinGeckoSearchResult {
    pub coins: Vec<CoinGeckoCoin>,
}

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct CMCToken {
    pub id: u64,
    pub name: String,
    pub symbol: String,
}

#[derive(Debug, Deserialize)]
pub struct CMCSearchResult {
    pub data: Vec<CMCToken>,
}
