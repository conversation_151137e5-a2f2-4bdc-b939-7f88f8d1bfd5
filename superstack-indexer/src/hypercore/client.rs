#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct HypercoreClient {
    base_url: String,
    ws_url: String,
    client: reqwest::Client,
    is_testnet: bool,
}

impl HypercoreClient {
    pub fn new(base_url: String, ws_url: String, is_testnet: bool) -> Self {
        Self { base_url, ws_url, client: reqwest::Client::new(), is_testnet }
    }

    pub fn ws_url(&self) -> String {
        self.ws_url.clone()
    }

    pub fn info_url(&self) -> String {
        format!("{}/info", self.base_url)
    }

    pub fn exchange_url(&self) -> String {
        format!("{}/exchange", self.base_url)
    }

    pub fn client(&self) -> &reqwest::Client {
        &self.client
    }

    pub fn is_testnet(&self) -> bool {
        self.is_testnet
    }
}
