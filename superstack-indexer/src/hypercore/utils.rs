use bigdecimal::BigDecimal;
use chrono::{DateTime, Utc};
use num_traits::FromPrimitive;

pub fn value_to_x_url(v: &serde_json::Value) -> Option<String> {
    v.as_str().map(|s| format!("https://x.com/{}", s))
}

pub fn value_to_tg_url(v: &serde_json::Value) -> Option<String> {
    v.as_str().map(|s| format!("https://t.me/{}", s))
}

pub fn value_float_to_decimal(v: &serde_json::Value) -> Option<BigDecimal> {
    v.as_f64().and_then(BigDecimal::from_f64)
}

pub fn value_to_timestamp_millis(v: &serde_json::Value) -> Option<i64> {
    let dt: DateTime<Utc> = serde_json::from_value(v.clone()).ok()?;
    Some(dt.timestamp_millis())
}

pub fn str_to_timestamp_millis(v: &str) -> Option<i64> {
    let dt: DateTime<Utc> = v.parse().ok()?;
    Some(dt.timestamp_millis())
}
