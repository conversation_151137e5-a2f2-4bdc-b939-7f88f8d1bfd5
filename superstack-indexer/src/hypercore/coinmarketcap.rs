use anyhow::{<PERSON>rro<PERSON>, Result};
use backon::{ExponentialBuilder, Retryable};
use cmc::async_api::Cmc;
use superstack_data::postgres::{indexer::perp_info::PerpInfo, PerpExchange};

use crate::{
    config::Config,
    hypercore::{
        types::{CMCSearchResult, CMCToken},
        utils,
    },
};

/// Search for a token by symbol.
pub async fn search_token(
    client: &reqwest::Client,
    symbol: &str,
) -> Result<Option<(CMCToken, bool)>> {
    // remove hyperliquid memecoin symbol prefix
    let (symbol, is_k_memecoin) = if symbol.starts_with("k") {
        // remove the k
        (&symbol[1..], true)
    } else {
        (symbol, false)
    };

    // cmc did not provide query with symbol
    const SEARCH_URL: &'static str = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/map";
    let fut = || async {
        let cfg = Config::get();
        let resp = client
            .get(SEARCH_URL)
            .header("X-CMC_PRO_API_KEY", &cfg.cmc_api_key)
            .query(&[("symbol", symbol)])
            .send()
            .await?
            .json::<CMCSearchResult>()
            .await?;
        Ok::<_, Error>(resp)
    };
    let res = fut.retry(ExponentialBuilder::default()).await?.data;
    let best =
        res.iter().find(|c| c.symbol.eq_ignore_ascii_case(symbol)).or_else(|| res.first()).cloned();
    Ok(best.map(|c| (c, is_k_memecoin)))
}

/// Get token metadata by id.
/// client should config pass with Pass::Id
pub async fn get_token_info(
    client: &Cmc,
    id: u64,
    perp_id: &str,
    is_k_memecoin: bool,
) -> Result<PerpInfo> {
    let fut = || async {
        let resp = client.metadata(id.to_string()).await?;
        Ok::<_, Error>(resp)
    };
    let metadata = fut.retry(ExponentialBuilder::default()).await?;

    let fut = || async {
        let mut resp = client.quotes_latest_by_id(id.to_string()).await?;
        anyhow::ensure!(resp.status.error_code == 0, "cmc error: {}", resp.status.error_message);
        resp.data.remove(&id.to_string()).ok_or(anyhow::anyhow!("cmc token not found"))
    };
    let quotes = fut.retry(ExponentialBuilder::default()).await?;

    let (is_native_token, network, address) = if let Some(platform) = metadata.platform {
        (false, Some(platform.name), Some(platform.token_address))
    } else {
        (true, None, None)
    };

    let mut res = PerpInfo {
        perp_exchange: PerpExchange::Hyperliquid,
        perp_id: perp_id.to_string(),
        is_native_token,
        network,
        address,
        name: metadata.name,
        symbol: metadata.symbol,
        socials: serde_json::json!({}),
        total_supply: utils::value_float_to_decimal(&quotes.total_supply).unwrap_or_default(),
        circulating_supply: utils::value_float_to_decimal(&quotes.circulating_supply)
            .unwrap_or_default(),
        update_timestamp_millis: utils::str_to_timestamp_millis(&quotes.last_updated)
            .unwrap_or_default(),
    };

    if is_k_memecoin {
        res.circulating_supply /= 1000.0;
        res.total_supply /= 1000.0;
    }

    let urls = metadata.urls;
    res.set_website_url(urls.website.first().and_then(|v| v.as_str()).map(|s| s.to_string()));
    res.set_x_url(urls.twitter.first().and_then(|v| v.as_str()).map(|s| s.to_string()));
    res.set_telegram_url(
        urls.chat
            .iter()
            .flat_map(|v| v.as_str())
            .find(|v| v.starts_with("https://t.me/"))
            .map(|s| s.to_string()),
    );
    Ok(res)
}

#[cfg(test)]
mod tests {
    use cmc::async_api::CmcBuilder;

    use super::*;

    #[tokio::test]
    async fn test_search_token() {
        dotenv::dotenv().ok();
        let client = reqwest::Client::new();
        let res = search_token(&client, "kBONK").await.unwrap();
        println!("{:?}", res);

        let (token, is_k_memecoin) = res.unwrap();
        let id = token.id;
        let client = CmcBuilder::new(&Config::get().cmc_api_key).pass(cmc::Pass::Id).build();
        let res = get_token_info(&client, id, "kBONK", is_k_memecoin).await.unwrap();
        println!("{}", res.circulating_supply.to_string());
        println!("{}", serde_json::to_string_pretty(&res).unwrap());
    }
}
