use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TxDetails {
    pub action: Value,
    pub block: u64,
    pub error: Option<String>,
    pub hash: String,
    pub time: u64,
    pub user: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BlockDetails {
    pub block_time: u64,
    pub hash: String,
    pub height: u64,
    pub num_txs: u64,
    pub proposer: String,
}
