use futures_util::{SinkExt, StreamExt};
use serde_json::json;
use tokio::sync::mpsc::Receiver;
use tokio_tungstenite::connect_async;

use crate::hypercore::{client::HypercoreClient, subscribe::txs::BlockDetails};

pub mod txs;

impl HypercoreClient {
    pub async fn get_latest_block_number(&self) -> anyhow::Result<u64> {
        let (stream, _) = connect_async(self.ws_url()).await?;

        let payload = json!({
            "method": "subscribe",
            "subscription": {
                "type": "explorerBlock",
            }
        });

        let post = json!({
            "method": "post",
            "id": 1,
            "request": {
                "type": "explorerBlock",
            }
        });

        let unsub = json!({
            "method": "unsubscribe",
            "subscription": {
                "type": "explorerBlock",
            }
        });

        let (mut tx, mut rx) = stream.split();

        tx.send(tokio_tungstenite::tungstenite::Message::Text(
            serde_json::to_string(&payload)?.into(),
        ))
        .await?;

        let _ = rx.next().await.ok_or(anyhow::anyhow!("No message received"))??;

        tx.send(tokio_tungstenite::tungstenite::Message::Text(
            serde_json::to_string(&post)?.into(),
        ))
        .await?;

        let msg = rx.next().await.ok_or(anyhow::anyhow!("No message received"))??;
        let msg = msg.to_text()?;

        let block_details: Vec<BlockDetails> = serde_json::from_str(msg)?;

        let latest = block_details.iter().map(|b| b.height).max();
        // unsub websocket
        tx.send(tokio_tungstenite::tungstenite::Message::Text(
            serde_json::to_string(&unsub)?.into(),
        ))
        .await?;

        Ok(latest.unwrap_or(0))
    }

    pub async fn subscribe_blocks(&self) -> anyhow::Result<Receiver<BlockDetails>> {
        let (tx, rx) = tokio::sync::mpsc::channel::<BlockDetails>(1000);

        let ws_url = self.ws_url();

        tokio::spawn(async move {
            let mut reconnect_attempts = 0;
            const MAX_RECONNECT_ATTEMPTS: u32 = 5;
            const RECONNECT_DELAY: u64 = 1000; // 1 second

            loop {
                match Self::connect_and_subscribe(ws_url.clone(), tx.clone()).await {
                    Ok(_) => {
                        reconnect_attempts = 0; // Reset on successful connection
                    }
                    Err(e) => {
                        eprintln!("WebSocket connection error: {:?}", e);
                        reconnect_attempts += 1;

                        if reconnect_attempts >= MAX_RECONNECT_ATTEMPTS {
                            eprintln!("Max reconnection attempts reached, stopping...");
                            break;
                        }

                        tokio::time::sleep(tokio::time::Duration::from_millis(
                            RECONNECT_DELAY * reconnect_attempts as u64,
                        ))
                        .await;
                    }
                }
            }
        });

        Ok(rx)
    }

    async fn connect_and_subscribe(
        ws_url: String,
        tx: tokio::sync::mpsc::Sender<BlockDetails>,
    ) -> anyhow::Result<()> {
        let (stream, _) = connect_async(&ws_url).await?;
        let (mut ws_tx, mut ws_rx) = stream.split();

        // Send subscription message
        let payload = json!({
            "method": "subscribe",
            "subscription": {
                "type": "explorerBlock",
            }
        });

        ws_tx
            .send(tokio_tungstenite::tungstenite::Message::Text(
                serde_json::to_string(&payload)?.into(),
            ))
            .await?;

        // Consume the initial subscription confirmation message
        let _ = ws_rx.next().await.ok_or(anyhow::anyhow!("No message received"))??;

        // Start listening for block messages
        while let Some(msg) = ws_rx.next().await {
            match msg {
                Ok(tokio_tungstenite::tungstenite::Message::Text(text)) => {
                    match serde_json::from_str::<Vec<BlockDetails>>(&text) {
                        Ok(block_details_vec) => {
                            for block_detail in block_details_vec {
                                if let Err(_) = tx.send(block_detail).await {
                                    // Receiver is closed, exit the loop
                                    return Ok(());
                                }
                            }
                        }
                        Err(e) => {
                            eprintln!("Failed to parse block details: {:?}, message: {}", e, text);
                        }
                    }
                }
                Ok(tokio_tungstenite::tungstenite::Message::Close(_)) => {
                    return Err(anyhow::anyhow!("WebSocket connection closed by server"));
                }
                Err(e) => {
                    return Err(anyhow::anyhow!("WebSocket error: {:?}", e));
                }
                _ => {
                    // Ignore other message types (binary, ping, pong)
                }
            }
        }

        Ok(())
    }
}

#[tokio::test]
async fn test_get_latest_block_number() {
    use crate::hypercore::types::{MAINNET_BASE_URL, MAINNET_WS_EXPLORER_URL};

    let client = HypercoreClient::new(
        MAINNET_BASE_URL.to_string(),
        MAINNET_WS_EXPLORER_URL.to_string(),
        false,
    );

    let block_details = client.get_latest_block_number().await.unwrap();
    println!("block_details: {:#?}", block_details);

    let block2 = client.get_latest_block_number().await.unwrap();
    println!("block2: {:#?}", block2);

    let block3 = client.get_latest_block_number().await.unwrap();
    println!("block3: {:#?}", block3);
}

#[tokio::test]
async fn test_subscribe_blocks() {
    use crate::hypercore::types::{MAINNET_BASE_URL, MAINNET_WS_EXPLORER_URL};

    dotenv::dotenv().ok();
    tracing_subscriber::fmt::init();

    let client = HypercoreClient::new(
        MAINNET_BASE_URL.to_string(),
        MAINNET_WS_EXPLORER_URL.to_string(),
        false,
    );

    let mut rx = client.subscribe_blocks().await.unwrap();

    tracing::info!("Starting to listen for blocks...");

    // Listen for the first 3 blocks
    while let Some(block_details) = rx.recv().await {
        tracing::info!("block_details: {:#?}", block_details);
    }

    tracing::info!("Test completed");
}
