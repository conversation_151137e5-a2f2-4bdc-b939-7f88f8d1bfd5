use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenDetails {
    pub name: String,
    pub max_supply: String,
    pub total_supply: String,
    pub circulating_supply: String,
    pub sz_decimals: u8,
    pub wei_decimals: u8,
    pub mid_px: String,
    pub mark_px: String,
    pub prev_day_px: String,
    pub genesis: Option<Value>,
    pub deployer: Option<String>,
    pub deploy_gas: Option<String>,
    pub deploy_time: Option<String>,
    pub seeded_usdc: String,
    pub non_circulating_user_balances: Vec<Value>,
    pub future_emissions: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SpotToken {
    pub name: String,
    pub sz_decimals: u8,
    pub wei_decimals: u8,
    pub index: u64,
    pub token_id: String,
    pub is_canonical: bool,
    pub evm_contract: Option<EvmContract>,
    pub full_name: Option<String>,
    pub deployer_trading_fee_share: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct EvmContract {
    pub address: String,
    pub evm_extra_wei_decimals: i8,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SpotUniverse {
    pub tokens: [usize; 2], // pair of tokens
    pub name: String,
    pub index: usize,
    pub is_canonical: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SpotMeta {
    pub universe: Vec<SpotUniverse>,
    pub tokens: Vec<SpotToken>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SpotAssetCtx {
    // base
    pub prev_day_px: String,
    pub day_ntl_vlm: String,
    pub mark_px: String,
    pub mid_px: Option<String>,
    // spot
    pub circulating_supply: String,
    pub coin: String,
    pub total_supply: String,
    pub day_base_vlm: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SpotMetaAndAssetCtxs {
    pub meta: SpotMeta,
    pub asset_ctxs: Vec<SpotAssetCtx>,
}

#[cfg(test)]
mod test {
    use crate::hypercore::{
        client::HypercoreClient,
        types::{
            MAINNET_BASE_URL, MAINNET_WS_EXPLORER_URL, TESTNET_BASE_URL, TESTNET_WS_EXPLORER_URL,
        },
    };
    use chrono::NaiveDateTime;

    #[tokio::test]
    async fn test_get_spots_and_ctxs() {
        let client = HypercoreClient::new(
            TESTNET_BASE_URL.to_string(),
            TESTNET_WS_EXPLORER_URL.to_string(),
            true,
        );
        let spots_and_ctxs = client.get_spots_and_ctxs().await.unwrap();
        let universe = spots_and_ctxs.meta.universe;
        let tokens = spots_and_ctxs.meta.tokens;
        // println!("{:#?}", universe);
        // println!("ctxs len: {}", spots_and_ctxs.asset_ctxs.len());
        // println!("tokens len: {}", spots_and_ctxs.meta.tokens.len());
        let mut has_evm = 0;
        println!("universe len: {}", universe.len());
        for u in &universe {
            let token = &tokens[u.tokens[0]];
            if let Some(evm_contract) = &token.evm_contract {
                has_evm += 1;
            } else {
                if token.name != "USDC" {
                    println!("{:#?}", token);
                }
            }
        }
        println!("has_evm: {}", has_evm);
        println!("tokens len: {}", tokens.len());
    }

    #[tokio::test]
    async fn test_get_token_details() {
        let client = HypercoreClient::new(
            MAINNET_BASE_URL.to_string(),
            MAINNET_WS_EXPLORER_URL.to_string(),
            false,
        );
        let token_details =
            client.get_token_details("0x8f254b963e8468305d409b33aa137c67").await.unwrap();
        println!("{:#?}", token_details);

        let time_str = "2025-01-27T16:54:50.747772155";

        let naive_time = NaiveDateTime::parse_from_str(time_str, "%Y-%m-%dT%H:%M:%S%.f").unwrap();
        let time_with_utc = naive_time.and_utc();
        println!("{}", time_with_utc.timestamp_millis());
    }
}
