pub mod candle;
pub mod perp;
pub mod spot;

use crate::hypercore::{
    client::HypercoreClient,
    info::{
        candle::{CandleSnapshotRequest, HyperCandle},
        perp::PerpsMetaAndAssetCtxs,
        spot::{SpotMetaAndAssetCtxs, TokenDetails},
    },
};
use serde_json::json;

impl HypercoreClient {
    pub async fn get_spots_and_ctxs(&self) -> anyhow::Result<SpotMetaAndAssetCtxs> {
        let url = self.info_url();
        let request = json!({
            "type": "spotMetaAndAssetCtxs",
        });

        let response = self.client().post(url).json(&request).send().await?;
        let response_body = response.json::<SpotMetaAndAssetCtxs>().await?;

        Ok(response_body)
    }

    pub async fn get_token_details(&self, token_id: &str) -> anyhow::Result<TokenDetails> {
        let url = self.info_url();
        let request = json!({
            "tokenId": token_id,
            "type": "tokenDetails",
        });
        let response = self.client().post(url).json(&request).send().await?;
        let response_body = response.json::<TokenDetails>().await?;

        Ok(response_body)
    }

    pub async fn get_perps_and_ctxs(&self) -> anyhow::Result<PerpsMetaAndAssetCtxs> {
        let url = self.info_url();
        let request = json!({
            "type": "metaAndAssetCtxs",
        });
        let response = self.client().post(url).json(&request).send().await?;
        let response_body = response.json::<PerpsMetaAndAssetCtxs>().await?;

        Ok(response_body)
    }

    pub async fn get_candle_snapshot(
        &self,
        req: CandleSnapshotRequest,
    ) -> anyhow::Result<Vec<HyperCandle>> {
        let url = self.info_url();
        let request = json!({
            "type": "candleSnapshot",
            "req": req,
        });
        let response = self.client().post(url).json(&request).send().await?;
        let response_body = response.json::<Vec<HyperCandle>>().await?;

        Ok(response_body)
    }

    pub async fn get_prices_from_candles(
        &self,
        coin: &str,
    ) -> anyhow::Result<(f64, f64, f64, f64)> {
        // 5m 1h 6h 24h
        let start_time =
            std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis()
                as u64 -
                24 * 60 * 60 * 1000; // 24 hours ago in milliseconds

        // Get 1h candles for 1h, 6h, 24h calculations
        let candles_1h = self
            .get_candle_snapshot(CandleSnapshotRequest {
                coin: coin.to_string(),
                interval: "1h".to_string(),
                start_time,
                end_time: None,
            })
            .await?;

        // Get 5m candles for 5m calculation (only need last 10 minutes of data)
        let start_time_5m =
            std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis()
                as u64 -
                10 * 60 * 1000; // 10 minutes ago in milliseconds

        let candles_5m = self
            .get_candle_snapshot(CandleSnapshotRequest {
                coin: coin.to_string(),
                interval: "5m".to_string(),
                start_time: start_time_5m,
                end_time: None,
            })
            .await?;

        if candles_1h.is_empty() {
            return Err(anyhow::anyhow!("No 1h candle data found for {}", coin));
        }

        // Sort candles by timestamp (most recent first)
        let mut sorted_candles_1h = candles_1h;
        sorted_candles_1h.sort_by(|a, b| b.t.cmp(&a.t));

        let mut sorted_candles_5m = candles_5m;
        sorted_candles_5m.sort_by(|a, b| b.t.cmp(&a.t));

        let current_time =
            std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis()
                as i64;

        // Get the most recent price (current price)
        let current_price = sorted_candles_1h
            .first()
            .and_then(|c| c.c.parse::<f64>().ok())
            .ok_or_else(|| anyhow::anyhow!("Unable to parse current price"))?;

        // Helper function to find price at a specific time ago from 1h candles
        let find_price_at_time_1h = |minutes_ago: i64| -> Option<f64> {
            let target_time = current_time - minutes_ago * 60 * 1000;

            // Find the candle closest to the target time
            sorted_candles_1h
                .iter()
                .min_by_key(|candle| (candle.t - target_time).abs())
                .and_then(|candle| candle.c.parse::<f64>().ok())
        };

        // Helper function to find price at a specific time ago from 5m candles
        let find_price_at_time_5m = |minutes_ago: i64| -> Option<f64> {
            let target_time = current_time - minutes_ago * 60 * 1000;

            // Find the candle closest to the target time
            sorted_candles_5m
                .iter()
                .min_by_key(|candle| (candle.t - target_time).abs())
                .and_then(|candle| candle.c.parse::<f64>().ok())
        };

        // Calculate price changes
        let price_5m_ago = find_price_at_time_5m(5).unwrap_or(current_price);
        let price_1h_ago = find_price_at_time_1h(60).unwrap_or(current_price);
        let price_6h_ago = find_price_at_time_1h(6 * 60).unwrap_or(current_price);
        let price_24h_ago = find_price_at_time_1h(24 * 60).unwrap_or(current_price);

        Ok((price_5m_ago, price_1h_ago, price_6h_ago, price_24h_ago))
    }
}

#[cfg(test)]
mod tests {
    use crate::hypercore::{
        client::HypercoreClient,
        info::candle::CandleSnapshotRequest,
        types::{MAINNET_BASE_URL, MAINNET_WS_URL},
    };

    #[tokio::test]
    async fn test_get_perps_and_ctxs() {
        let client =
            HypercoreClient::new(MAINNET_BASE_URL.to_string(), MAINNET_WS_URL.to_string(), false);
        let perps_and_ctxs = client.get_perps_and_ctxs().await.unwrap();
        println!("{:#?}", perps_and_ctxs);
    }

    #[tokio::test]
    async fn test_get_candle_snapshot() {
        let client =
            HypercoreClient::new(MAINNET_BASE_URL.to_string(), MAINNET_WS_URL.to_string(), false);
        let start_time =
            std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis()
                as u64 -
                25 * 60 * 60 * 1000;
        let req = CandleSnapshotRequest {
            coin: "BTC".to_string(),
            interval: "5m".to_string(),
            start_time,
            end_time: None,
        };
        let candles = client.get_candle_snapshot(req).await.unwrap();
        println!("{:#?}", candles);
        // let (volumn_5m, volumn_1h, volumn_6h, volumn_24h) =
        //     client.get_volumn_from_candles("BTC").await.unwrap();
        // println!("volumn_5m: {}", volumn_5m);
        // println!("volumn_1h: {}", volumn_1h);
        // println!("volumn_6h: {}", volumn_6h);
        // println!("volumn_24h: {}", volumn_24h);
    }

    #[tokio::test]
    async fn test_get_prices_from_candles() {
        let client =
            HypercoreClient::new(MAINNET_BASE_URL.to_string(), MAINNET_WS_URL.to_string(), false);
        let (price_5m, price_1h, price_6h, price_24h) =
            client.get_prices_from_candles("BTC").await.unwrap();
        println!("Price changes:");
        println!("5m: {:.2}", price_5m);
        println!("1h: {:.2}", price_1h);
        println!("6h: {:.2}", price_6h);
        println!("24h: {:.2}", price_24h);
    }
}
