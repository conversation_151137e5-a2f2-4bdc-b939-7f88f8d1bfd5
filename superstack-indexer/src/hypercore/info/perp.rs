use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PerpsMetaAndAssetCtxs {
    pub meta: PerpsMeta,
    pub asset_ctxs: Vec<PerpAssetCtx>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PerpsMeta {
    pub universe: Vec<PerpsUniverse>,
    pub margin_tables: Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PerpsUniverse {
    pub sz_decimals: u8,
    pub name: String,
    pub max_leverage: u8,
    pub margin_table_id: u8,
    pub only_isolated: Option<bool>,
    pub is_delisted: Option<bool>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PerpAssetCtx {
    pub funding: String,
    pub open_interest: String,
    pub premium: Option<String>,
    pub oracle_px: String,
    pub impact_pxs: Option<Vec<String>>,
    pub day_base_vlm: String,

    pub prev_day_px: String,
    pub day_ntl_vlm: String,
    pub mark_px: String,
    pub mid_px: Option<String>,
}
