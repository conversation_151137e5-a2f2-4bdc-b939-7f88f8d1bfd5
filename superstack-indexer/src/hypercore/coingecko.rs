use anyhow::{<PERSON>rro<PERSON>, Result};
use backon::{ExponentialBuilder, Retryable};
use coingecko::CoinGeckoClient;
use serde_json::Value;
use superstack_data::postgres::{indexer::perp_info::PerpInfo, PerpExchange};

use crate::{
    config::Config,
    hypercore::types::{CoinGeckoCoin, CoinGeckoSearchResult},
};

use super::utils;

/// Search for a token by symbol.
pub async fn search_token(client: &reqwest::Client, symbol: &str) -> Result<Option<CoinGeckoCoin>> {
    // coingecko client not provide search api
    const SEARCH_URL: &'static str = "https://api.coingecko.com/api/v3/search";
    let fut = || async {
        let cfg = Config::get().data_config;
        let resp = client
            .get(SEARCH_URL)
            .header("x_cg_demo_api_key", &cfg.coingecko_api_key)
            .query(&[("query", symbol)])
            .send()
            .await?
            .json::<CoinGeckoSearchResult>()
            .await?;
        Ok::<_, Error>(resp)
    };
    let coins = fut.retry(ExponentialBuilder::default()).await?.coins;
    let best = coins
        .iter()
        .find(|c| c.symbol.eq_ignore_ascii_case(symbol))
        .or_else(|| coins.first())
        .cloned();
    Ok(best)
}

/// Get token info from CoinGecko.
pub async fn get_token_info(client: &CoinGeckoClient, id: &str) -> Result<PerpInfo> {
    let fut = || async {
        let resp = client.coin(id, false, false, true, false, false, false).await?;
        Ok::<_, Error>(resp)
    };
    let res = fut.retry(ExponentialBuilder::default()).await?;

    let (is_native_token, network, address) = match res.platforms.iter().next() {
        Some((k, v)) if !k.is_empty() && !v.is_empty() => {
            (false, Some(k.to_owned()), Some(v.to_owned()))
        }
        _ => (true, None, None),
    };

    let (total_supply, circulating_supply) = if let Some(market_data) = res.market_data {
        (
            utils::value_float_to_decimal(&market_data.total_supply),
            utils::value_float_to_decimal(&market_data.circulating_supply),
        )
    } else {
        (None, None)
    };

    let mut hype_token_stat = PerpInfo {
        perp_exchange: PerpExchange::Hyperliquid,
        perp_id: res.id,
        is_native_token,
        network,
        address,
        name: res.name,
        symbol: res.symbol,
        socials: Value::Null,
        total_supply: total_supply.unwrap_or_default(),
        circulating_supply: circulating_supply.unwrap_or_default(),
        update_timestamp_millis: utils::value_to_timestamp_millis(&res.last_updated)
            .unwrap_or_default(),
    };

    hype_token_stat.set_x_url(utils::value_to_x_url(&res.links.twitter_screen_name));
    hype_token_stat
        .set_telegram_url(utils::value_to_tg_url(&res.links.telegram_channel_identifier));
    hype_token_stat.set_website_url(res.links.homepage.first().cloned());

    Ok(hype_token_stat)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_search_token() {
        dotenv::dotenv().ok();
        let client = reqwest::Client::new();
        let res = search_token(&client, "SOL").await.unwrap();
        println!("{:?}", res);

        let id = res.unwrap().id;
        let cg_client =
            CoinGeckoClient::new_with_demo_api_key(&Config::get().data_config.coingecko_api_key);
        let res = get_token_info(&cg_client, &id).await.unwrap();
        println!("{:?}", res);
    }
}
