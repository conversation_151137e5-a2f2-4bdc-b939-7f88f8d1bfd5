use std::{str::FromStr, sync::OnceLock};

use alloy::{
    primitives::{Address, U160, U256},
    providers::{DynProvider, Provider, ProviderBuilder},
    rpc::types::{Block, Filter, Log, TransactionReceipt},
};
use backon::{ExponentialBuilder, Retryable};
use superstack_data::{
    hyperevm::erc20::IERC20,
    postgres::{
        enums::{Chain, Dex, DexPaid},
        indexer::TokenMetadata,
    },
    utils::u256_to_big_decimal,
};
use uniswap_v3_sdk::prelude::sdk_core::{
    prelude::{BigInt, BigUint},
    utils::ToBig,
};

use crate::{cache::ChainCache, config::Config, hyperevm::hyperswap::events::HyperswapV3};

#[allow(dead_code)]
pub(crate) const Q192_BIG_INT: BigInt =
    BigInt::from_bits(BigUint::from_digits([0, 0, 0, 1, 0, 0, 0, 0]));

pub(crate) const HYPER_EVM_CHAIN_ID: u64 = 999;

pub fn get_provider() -> &'static DynProvider {
    static PROVIDER: OnceLock<DynProvider> = OnceLock::new();

    PROVIDER.get_or_init(|| {
        let config = Config::get();
        let url = config.hyperevm_rpc_url.clone();
        let p = ProviderBuilder::new().connect_http(url.parse().unwrap());
        p.erased()
    })
}

pub fn get_backup_provider() -> &'static DynProvider {
    static PROVIDER: OnceLock<DynProvider> = OnceLock::new();

    PROVIDER.get_or_init(|| {
        let config = Config::get();
        let url = config.hyperevm_backup_rpc_url.clone();
        let p = ProviderBuilder::new().connect_http(url.parse().unwrap());
        p.erased()
    })
}

pub async fn get_logs<const USE_BACKUP: bool>(filter: &Filter) -> anyhow::Result<Vec<Log>> {
    let fut = || async move {
        let provider = if USE_BACKUP { get_backup_provider() } else { get_provider() };
        let logs = match provider.get_logs(filter).await {
            Ok(logs) => logs,
            Err(e) => {
                tracing::error!("get_logs: error: {:?}", e);
                return Err(anyhow::anyhow!("get_logs: error: {:?}", e));
            }
        };
        Ok::<_, anyhow::Error>(logs)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_block(block: u64) -> anyhow::Result<Block> {
    let fut = || async move {
        let provider = get_provider();
        let block =
            provider.get_block(block.into()).await?.ok_or(anyhow::anyhow!("Block not found"))?;
        Ok::<_, anyhow::Error>(block)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_block_receipts(block: u64) -> anyhow::Result<Vec<TransactionReceipt>> {
    let fut = || async move {
        let provider = get_provider();
        let receipts = provider
            .get_block_receipts(block.into())
            .await?
            .ok_or(anyhow::anyhow!("Block not found"))?;
        Ok::<_, anyhow::Error>(receipts)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_block_timestamp(block: u64) -> anyhow::Result<u64> {
    let fut = || async move {
        let provider = get_provider();
        let block =
            provider.get_block(block.into()).await?.ok_or(anyhow::anyhow!("Block not found"))?;
        Ok::<_, anyhow::Error>(block.header.timestamp)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_pool_active_liquidity(pool: &Address) -> anyhow::Result<u128> {
    let fut = || async move {
        let pool = HyperswapV3::new(*pool, get_provider());
        let liquidity = pool.liquidity().call().await?;
        Ok::<_, anyhow::Error>(liquidity)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_factory_address(log_address: &Address) -> anyhow::Result<Address> {
    let fut = || async move {
        let pool = HyperswapV3::new(*log_address, get_provider());
        let factory = pool.factory().call().await?;
        Ok::<_, anyhow::Error>(factory)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_pool_total_liquidity(
    pool: &Address,
    token0: &TokenMetadata,
    token1: &TokenMetadata,
    price: f64,
) -> anyhow::Result<f64> {
    let fut = || async move {
        let token0_address = Address::from_str(&token0.address)?;
        let token1_address = Address::from_str(&token1.address)?;
        let token0_contract = IERC20::new(token0_address, get_provider());
        let token1_contract = IERC20::new(token1_address, get_provider());
        let token0_balance = token0_contract.balanceOf(*pool).call().await?;
        let token1_balance = token1_contract.balanceOf(*pool).call().await?;
        let total_liquidity = to_ui_amount(token0_balance, token0.decimals) * price +
            to_ui_amount(token1_balance, token1.decimals);
        Ok::<_, anyhow::Error>(total_liquidity)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_current_sqrt_price_x96(pool: &Address) -> anyhow::Result<U160> {
    let fut = || async move {
        let pool = HyperswapV3::new(*pool, get_provider());
        let slot0 = pool.slot0().call().await?;
        Ok::<_, anyhow::Error>(slot0.sqrtPriceX96)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_current_lock(pool: &Address) -> anyhow::Result<bool> {
    let fut = || async move {
        let pool = HyperswapV3::new(*pool, get_provider());
        let slot0 = pool.slot0().call().await?;
        Ok::<_, anyhow::Error>(slot0.unlocked)
    };

    fut.retry(ExponentialBuilder::default()).await
}

pub async fn get_or_fetch_token_metadata(address: &Address) -> anyhow::Result<TokenMetadata> {
    let chain_cache = ChainCache::get_cache(Chain::HyperEvm);
    let token_metadata =
        chain_cache.get_token_metadata_or_update_from_db(&address.to_string()).await?;
    if let Some(token_metadata) = token_metadata {
        return Ok(token_metadata);
    }

    let fut = || async move {
        let erc20 = IERC20::new(*address, get_provider());
        let supply = erc20.totalSupply().call().await?;

        Ok::<_, anyhow::Error>(TokenMetadata {
            chain: Chain::HyperEvm,
            address: address.to_string(),
            name: erc20.name().call().await?,
            symbol: erc20.symbol().call().await?,
            decimals: erc20.decimals().call().await?,
            supply: u256_to_big_decimal(supply)?,
            description: None,
            image: None,
            website: None,
            twitter: None,
            telegram: None,
            dex_paid: DexPaid::Unpaid,
            is_trench_token: false,
            create_dex: Dex::HyperSwapV3,
            create_block_number: None,
            create_tx_hash: None,
            create_bonding_curve: None,
            create_dev: None,
            create_timestamp_millis: 0,
            migration_pool_address: None,
            migration_timestamp_millis: 0,
            update_timestamp_millis: 0,
            uri: None,
            seller_fee_basis_points: None,
            creators: None,
            primary_sale_happened: None,
            is_mutable: None,
            update_authority: None,
            mint_authority: None,
            freeze_authority: None,
            is_active: true,
            image_path: None,
        })
    };

    let token_metadata = fut.retry(ExponentialBuilder::default()).await?;

    chain_cache.insert_token_metadata_and_publish_if_not_exists(token_metadata).await
}

pub fn align_token_pair<T>(
    token0: Address,
    token1: Address,
    t1: T,
    t2: T,
) -> anyhow::Result<(T, T)> {
    if token0 == hype_token::address() {
        Ok((t2, t1))
    } else if token1 == hype_token::address() {
        Ok((t1, t2))
    } else {
        Err(anyhow::anyhow!("Token0 or token1 is not hype token"))
    }
}

pub fn to_ui_amount(amount: U256, decimals: u8) -> f64 {
    let big_int = amount.to_big_uint();
    let mut decimal_value = big_int.to_big_decimal();

    if decimals > 0 {
        let divisor = BigInt::from(10).pow(decimals as u32).to_big_decimal();
        decimal_value /= divisor;
    }

    decimal_value.to_string().parse::<f64>().unwrap()
}

pub mod hype_token {
    use std::str::FromStr;

    use alloy::primitives::Address;

    pub fn address() -> Address {
        Address::from_str("0x5555555555555555555555555555555555555555").unwrap()
    }

    pub const fn decimals() -> u8 {
        18
    }
}

#[cfg(test)]
mod tests {
    use superstack_data::utils::big_decimal_to_u256;
    use uniswap_v3_sdk::prelude::sqrt_ratio_x96_to_price;

    use crate::hyperevm::hyperswap::to_token;

    use super::*;

    #[tokio::test]
    async fn test_get_block_receipts() {
        dotenv::dotenv().ok();

        let provider = get_provider();
        let _latest = provider.get_block_number().await.unwrap();

        let block = get_block(500000).await.unwrap();

        println!("{:?}", block);
    }

    #[tokio::test]
    async fn test_get_logs() {
        dotenv::dotenv().ok();
        let provider = get_provider();
        let receipts = provider.get_block_receipts(500000.into()).await.unwrap().unwrap();
        println!("{:?}", receipts.len());
    }

    #[tokio::test]
    async fn test_token_fetch() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let erc20 = IERC20::new(
            Address::from_str("0x91309D0d2fb5fc403a26cB4b528deDFc1c81136f").unwrap(),
            get_provider(),
        );
        let supply = erc20.totalSupply().call().await.unwrap();
        tracing::info!("supply: {:?}", supply);

        let token = get_or_fetch_token_metadata(
            &Address::from_str("0x91309D0d2fb5fc403a26cB4b528deDFc1c81136f").unwrap(),
        )
        .await
        .unwrap();
        tracing::info!("{:?}", token);

        let supply_u256 = token.supply_to_u256().unwrap();
        tracing::info!("supply_u256: {:?}", supply_u256);
    }

    #[tokio::test]
    async fn test_get_current_sqrt_price_x96() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let price = get_current_sqrt_price_x96(
            &Address::from_str("0x88a1C0bC48c5493514E21F692B0e0EcBD03b8807").unwrap(),
        )
        .await
        .unwrap();
        println!("{:?}", price);

        let locked = get_current_lock(
            &Address::from_str("0x88a1C0bC48c5493514E21F692B0e0EcBD03b8807").unwrap(),
        )
        .await
        .unwrap();
        println!("{:?}", locked);
    }

    #[tokio::test]
    async fn test_get_pool_total_liquidity() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let token0 = get_or_fetch_token_metadata(
            &Address::from_str("0x5555555555555555555555555555555555555555").unwrap(),
        )
        .await
        .unwrap();
        let token1 = get_or_fetch_token_metadata(
            &Address::from_str("0x5748ae796AE46A4F1348a1693de4b50560485562").unwrap(),
        )
        .await
        .unwrap();

        let price = get_pool_total_liquidity(
            &Address::from_str("0x7Db294F26c753ce4FA54A1577aeF7f837ea91fDC").unwrap(),
            &token0,
            &token1,
            1.0,
        )
        .await
        .unwrap();
        println!("{:?}", price);
    }

    #[tokio::test]
    async fn test_get_pool_ui_price() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let token0 = get_or_fetch_token_metadata(
            &Address::from_str("0x5555555555555555555555555555555555555555").unwrap(),
        )
        .await
        .unwrap();
        let token1 = get_or_fetch_token_metadata(
            &Address::from_str("0x9FDBdA0A5e284c32744D2f17Ee5c74B284993463").unwrap(),
        )
        .await
        .unwrap();

        let token0_token = to_token(&token0);
        let token1_token = to_token(&token1);

        let sqrt_price_x96 = get_current_sqrt_price_x96(
            &Address::from_str("0x3A36B04bcC1D5E2E303981eF643D2668E00b43e7").unwrap(),
        )
        .await
        .unwrap();

        println!("sqrt_price_x96: {:?}", sqrt_price_x96);
        let price = sqrt_ratio_x96_to_price(sqrt_price_x96, token1_token, token0_token).unwrap();
        println!("price: {:?}", price);
        let price_f64 = price.to_significant(5, None).unwrap();
        let price_f64 = price_f64.parse::<f64>().unwrap();
        println!("{:?}", price_f64);

        let supply = token1.supply_to_u256().unwrap();
        let market_cap = to_ui_amount(supply, token1.decimals) * price_f64;
        println!("token1.supply: {:?}", token1.supply);
        println!("token1.decimals: {:?}", token1.decimals);
        println!("market_cap: {:?}", market_cap);
    }

    #[tokio::test]
    async fn test_u256_to_big_decimal() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let erc20 = IERC20::new(
            Address::from_str("0x91309D0d2fb5fc403a26cB4b528deDFc1c81136f").unwrap(),
            get_provider(),
        );
        let supply = erc20.totalSupply().call().await.unwrap();

        let supply_big_decimal = u256_to_big_decimal(supply).unwrap();

        println!("supply_big_decimal: {:?}", supply_big_decimal);

        let supply_u256 = big_decimal_to_u256(supply_big_decimal).unwrap();
        println!("supply_u256: {:?}", supply_u256);
    }
}
