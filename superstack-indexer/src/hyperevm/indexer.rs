use crate::{
    config::Config,
    hyperevm::{
        hyperswap::HyperSwapV3,
        utils::{get_block_receipts, get_block_timestamp, get_logs, get_provider},
    },
};
use alloy::{
    primitives::Address,
    providers::Provider,
    rpc::types::{Filter, Log, TransactionReceipt},
};
use superstack_data::postgres::{enums::Chain, PostgresDatabase};
use tokio::task::JoinSet;

#[derive(Clone)]
pub struct HyperEvmIndexer {
    pub hyperswap: HyperSwapV3,
    pub from_block: Option<u64>,
    pub block_limit: usize,
}

impl HyperEvmIndexer {
    const BLOCK_LIMIT: usize = 25;
    const FROM_BLOCK: u64 = 6446239;

    pub async fn init(config: &Config) -> Self {
        let hyperswap =
            HyperSwapV3::init::<true>(config.hyperswapv3_factory_contract).await.unwrap();
        Self {
            hyperswap,
            from_block: config.hyperevm_from_block,
            block_limit: config.hyperevm_block_limit.unwrap_or(Self::BLOCK_LIMIT),
        }
    }

    pub async fn run(self) -> anyhow::Result<()> {
        let db = PostgresDatabase::get_indexer_db().await;
        let db_latest_block = db
            .get_chain_info(Chain::HyperEvm)
            .await
            .expect("Failed to get chain info from db")
            .map(|chain_info| chain_info.block_number)
            .unwrap_or(Self::FROM_BLOCK);

        let from_block = self.from_block.unwrap_or(Self::FROM_BLOCK);

        let hyperswap_pool_init_block = self.hyperswap.get_pool_init_block().await?;
        let mut cur = from_block.max(db_latest_block).min(hyperswap_pool_init_block);

        tracing::info!("Starting hyperevm indexer from block {cur}");
        let provider = get_provider();
        loop {
            let latest = match provider.get_block_number().await {
                Ok(height) => height,
                Err(e) => {
                    tracing::error!("Error getting block height: {}", e);
                    tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
                    continue;
                }
            };

            if latest <= cur {
                tracing::debug!("No new blocks to index {}", latest);
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
                continue;
            }

            tracing::debug!("Getting blocks receipts from {} to {}", cur, latest);
            for st in (cur..latest).step_by(self.block_limit) {
                let ed = (st + self.block_limit as u64).min(latest);
                let mut blocks = match Self::get_blocks_receipts(st, ed).await {
                    Ok(blocks) => blocks,
                    Err(e) => {
                        tracing::error!("Error getting blocks receipts: {}", e);
                        continue;
                    }
                };
                blocks.sort_by_key(|(block_number, _, _)| *block_number);
                self.handle_blocks(blocks).await?;
                cur = ed;
                tracing::info!("handled blocks from {} to {}", st, ed);
            }
            tracing::debug!("Indexed to {}", latest);
        }
    }

    pub async fn get_blocks_receipts(
        st: u64,
        ed: u64,
    ) -> anyhow::Result<Vec<(u64, Vec<TransactionReceipt>, u64)>> {
        let mut result = Vec::new();

        let mut tasks = JoinSet::new();
        tracing::debug!("from {} to {}", st, ed);
        for i in st..ed {
            let fut = async move {
                let txs = get_block_receipts(i).await?;
                let timestamp = get_block_timestamp(i).await?;
                Ok::<_, anyhow::Error>((i, txs, timestamp))
            };

            tasks.spawn(fut);
        }

        let blocks = tasks.join_all().await.into_iter().collect::<Result<Vec<_>, _>>()?;
        result.extend(blocks);

        Ok(result)
    }

    pub async fn get_logs(
        from_block: u64,
        to_block: u64,
        address: Address,
        events: &[&'static str],
    ) -> anyhow::Result<Vec<Log>> {
        const RANGE: u64 = 100000;
        let mut result = Vec::new();
        tracing::warn!("Syncing logs from {} to {}", from_block, to_block);
        for st in (from_block..=to_block).step_by(RANGE as usize) {
            let ed = (st + RANGE).min(to_block);
            let filter = Filter::new().from_block(st).to_block(ed).address(address).events(events);
            let logs = get_logs::<true>(&filter).await?;
            tracing::warn!("Got {} logs from {} to {}", logs.len(), st, ed);
            result.extend(logs);
        }

        Ok(result)
    }

    pub async fn handle_blocks(
        &self,
        blocks: Vec<(u64, Vec<TransactionReceipt>, u64)>,
    ) -> anyhow::Result<()> {
        if blocks.is_empty() {
            return Ok(());
        }

        let min_block_number =
            blocks.iter().map(|(block_number, _, _)| *block_number).min().unwrap_or(1);
        let max_block_number =
            blocks.iter().map(|(block_number, _, _)| *block_number).max().unwrap_or(1);

        let mut tasks = JoinSet::new();
        for (block_number, receipts, timestamp) in blocks {
            let hyperswap = self.hyperswap.clone();
            tasks.spawn(async move {
                hyperswap.handle_receipts(block_number, timestamp, &receipts).await?;

                Ok::<_, anyhow::Error>(())
            });
        }

        let db = PostgresDatabase::get_indexer_db().await;
        if let Err(e) = tasks.join_all().await.into_iter().collect::<Result<Vec<_>, _>>() {
            tracing::error!("Error handling blocks: {:?}", e);
            self.hyperswap.set_pool_init_block(min_block_number).await?;
            db.insert_or_update_chain_info(Chain::HyperEvm, min_block_number).await?;
        } else {
            self.hyperswap.set_pool_init_block(max_block_number).await?;
            db.insert_or_update_chain_info(Chain::HyperEvm, max_block_number).await?;
        }

        Ok(())
    }
}
