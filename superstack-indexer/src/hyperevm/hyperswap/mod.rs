use std::{collections::HashSet, str::FromStr, sync::Arc, time::Duration};

use alloy::{
    primitives::{Address, I256, U256},
    providers::Provider,
    rpc::types::{Log, TransactionReceipt},
    sol_types::SolEvent,
};
use superstack_data::{
    postgres::{
        enums::{Chain, Dex, PoolType},
        indexer::{DexTrade, PoolMetadata, PoolStateDelta, TokenHolderDelta, TokenMetadata},
        PostgresDatabase,
    },
    price::NativeTokenPriceManager,
    utils::u256_to_big_decimal,
};
use tokio::sync::RwLock;
use uniswap_v3_sdk::prelude::{sdk_core::prelude::Token, sqrt_ratio_x96_to_price};

use crate::{
    cache::{ChainCache, UnsyncDatabaseBuf},
    config::Config,
    hyperevm::{
        hyperswap::events::{
            HyperSwapV3Event,
            HyperswapV3::{self, Burn, Initialize, Mint, Swap},
        },
        indexer::HyperEvmIndexer,
        utils::{
            get_current_sqrt_price_x96, get_or_fetch_token_metadata, get_pool_total_liquidity,
            get_provider,
            hype_token::{self},
            to_ui_amount, HYPER_EVM_CHAIN_ID,
        },
    },
};

pub mod events;

#[derive(Clone)]
pub struct HyperSwapV3 {
    pub factory: Address,
    pub pools: Arc<RwLock<HashSet<Address>>>,
}

impl HyperSwapV3 {
    const POOL_INIT_BLOCK_KEY: &str = "hyperswapv3_pool_init_block";

    pub async fn get_pool_init_block(&self) -> anyhow::Result<u64> {
        let db = PostgresDatabase::get_indexer_db().await;
        let pool_init_block_str = db.get_key_value(Self::POOL_INIT_BLOCK_KEY).await?;
        if let Some(pool_init_block_str) = pool_init_block_str {
            let pool_init_block = pool_init_block_str.parse::<u64>()?;
            Ok(pool_init_block)
        } else {
            Ok(1)
        }
    }

    pub async fn set_pool_init_block(&self, block_number: u64) -> anyhow::Result<()> {
        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_or_update_key_value(Self::POOL_INIT_BLOCK_KEY, Some(block_number.to_string()))
            .await?;
        Ok(())
    }

    async fn add_pool(&self, pool: &Address) -> anyhow::Result<()> {
        self.pools.write().await.insert(*pool);

        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_hyperevm_pool(pool.to_string()).await?;
        Ok(())
    }

    async fn contain_pool(&self, pool: &Address) -> bool {
        let guard = self.pools.read().await;
        guard.contains(pool)
    }

    async fn get_pools_len(&self) -> usize {
        let guard = self.pools.read().await;
        guard.len()
    }

    async fn sync_pools_from_db(&self) -> anyhow::Result<usize> {
        let db = PostgresDatabase::get_indexer_db().await;
        let pool_addresses = db.get_all_hyperevm_pools().await?;
        tracing::info!("sync_pools_from_db: pool_addresses length: {}", pool_addresses.len());

        let mut guard = self.pools.write().await;
        for pool_address in pool_addresses {
            guard.insert(Address::from_str(&pool_address)?);
        }
        tracing::info!("sync_pools_from_db: current pools length: {}", guard.len());
        Ok(guard.len())
    }

    async fn init_pools(&self) -> anyhow::Result<()> {
        let pools_len = self.sync_pools_from_db().await?;

        let mut cur_block = self.get_pool_init_block().await?;
        tracing::info!("init_pools: pools_latest_block: {}", cur_block);

        if pools_len == 0 {
            tracing::info!("init_pools: no pools, initializing from beginning");
            cur_block = 1;
        }

        let latest_block = get_provider().get_block_number().await?;
        tracing::info!("init_pools: latest_block: {}", latest_block);

        let step: usize = Config::get().hyperevm_init_step.unwrap_or(100000);
        for st in (cur_block..latest_block).step_by(step) {
            let ed = (st + step as u64).min(latest_block);

            tracing::info!("init_pools... st {}, ed {}", st, ed);
            let logs = HyperEvmIndexer::get_logs(
                st,
                ed,
                self.factory,
                &[HyperswapV3::PoolCreated::SIGNATURE],
            )
            .await?;
            for log in logs {
                let event = match log.log_decode::<HyperswapV3::PoolCreated>() {
                    Ok(event) => event,
                    Err(e) => {
                        tracing::error!("Error decoding hyperswap v3 pool created event: {:?}", e);
                        continue;
                    }
                };

                let pool = event.inner.pool;
                self.add_pool(&pool).await?;
            }

            self.set_pool_init_block(ed).await?;
            tracing::info!(
                "init_pools: saved pools_latest: {}, current pools number: {}",
                ed,
                self.get_pools_len().await
            );
        }

        Ok(())
    }

    pub async fn init<const CACHE_POOLS: bool>(factory_contract: Address) -> anyhow::Result<Self> {
        let pools = Arc::new(RwLock::new(HashSet::new()));
        let _self = Self { factory: factory_contract, pools };

        tracing::info!("Initializing hyperswap v3 pools");
        if CACHE_POOLS {
            let mut retry_count = 0;
            loop {
                match _self.init_pools().await {
                    Ok(_) => break,
                    Err(e) => {
                        tracing::error!("Error initializing hyperswap v3 pools: {:?}", e);
                        retry_count += 1;
                        if retry_count > 10 {
                            tracing::error!(
                                "Failed to initialize hyperswap v3 pools after {} retries",
                                retry_count
                            );
                            return Err(e);
                        }
                        tokio::time::sleep(Duration::from_secs(10)).await;
                    }
                }
            }
        }

        Ok(_self)
    }

    pub async fn parse_log(log: &Log) -> anyhow::Result<Option<events::HyperSwapV3Event>> {
        let topic0 = log.topic0().ok_or(anyhow::anyhow!("topic0 not found"))?;
        let event = match *topic0 {
            HyperswapV3::PoolCreated::SIGNATURE_HASH => {
                let event = log.log_decode::<HyperswapV3::PoolCreated>()?;
                events::HyperSwapV3Event::PoolCreated(event.inner.data)
            }
            HyperswapV3::Initialize::SIGNATURE_HASH => {
                let event = log.log_decode::<HyperswapV3::Initialize>()?;
                events::HyperSwapV3Event::Initialize(event.inner.data)
            }
            HyperswapV3::Mint::SIGNATURE_HASH => {
                let event = log.log_decode::<HyperswapV3::Mint>()?;
                events::HyperSwapV3Event::Mint(event.inner.data)
            }
            HyperswapV3::Burn::SIGNATURE_HASH => {
                let event = log.log_decode::<HyperswapV3::Burn>()?;
                events::HyperSwapV3Event::Burn(event.inner.data)
            }
            HyperswapV3::Swap::SIGNATURE_HASH => {
                let event = log.log_decode::<HyperswapV3::Swap>()?;
                events::HyperSwapV3Event::Swap(event.inner.data)
            }
            _ => return Ok(None),
        };

        Ok(Some(event))
    }

    pub async fn handle_receipts(
        &self,
        block_number: u64,
        block_timestamp: u64,
        logs: &Vec<TransactionReceipt>,
    ) -> anyhow::Result<()> {
        let block_timestamp_millis = block_timestamp as i64 * 1000;
        let mut database_buf = UnsyncDatabaseBuf::new();

        let logs = logs.iter().flat_map(|receipt| receipt.logs()).collect::<Vec<_>>();
        for (tx_idx, log) in logs.iter().enumerate() {
            if !self.contain_pool(&log.address()).await && log.address() != self.factory {
                tracing::debug!("handle_receipts: log not in pools, ignore: {:?}", log);
                continue;
            }

            let event = match Self::parse_log(log).await {
                Ok(Some(event)) => event,
                Ok(None) => continue,
                Err(e) => {
                    tracing::error!("Error parsing hyperswap v3 log: {:?}", e);
                    continue;
                }
            };

            let pool = log.address();
            let tx_hash = log.transaction_hash.unwrap_or_default().to_string();
            if let Err(e) = self
                .handle_event(
                    &pool,
                    &event,
                    tx_hash,
                    tx_idx as u32,
                    block_number,
                    block_timestamp_millis,
                    &mut database_buf,
                )
                .await
            {
                tracing::error!("Error handling hyperswap v3 event: {:?}", e);
            }
        }

        // Can spawn it if needed
        database_buf.save_and_publish_all().await;

        Ok(())
    }

    pub(crate) async fn get_or_fetch_metadatas(
        &self,
        pool: &Address,
        create_timestamp: Option<i64>,
    ) -> anyhow::Result<(PoolMetadata, TokenMetadata, TokenMetadata, bool)> {
        let chain_cache = ChainCache::get_cache(Chain::HyperEvm);

        let pool_metadata =
            match chain_cache.get_pool_metadata_or_update_from_db(&pool.to_string()).await? {
                Some(pool_metadata) => pool_metadata,
                None => {
                    // Fetch token addresses from pool contract
                    let pool_contract = HyperswapV3::new(*pool, get_provider());
                    let token0_address = pool_contract.token0().call().await?;
                    let token1_address = pool_contract.token1().call().await?;

                    tracing::debug!("token0_address: {:?}", token0_address);
                    tracing::debug!("token1_address: {:?}", token1_address);

                    // Fetch token metadata
                    let token0_metadata = get_or_fetch_token_metadata(&token0_address).await?;
                    let token1_metadata = get_or_fetch_token_metadata(&token1_address).await?;

                    // Align tokens (token0 < token1 in hyperswap)
                    let (token_metadata, base_metadata) = crate::hyperevm::utils::align_token_pair(
                        token0_address,
                        token1_address,
                        token0_metadata.clone(),
                        token1_metadata.clone(),
                    )?;
                    let is_token_first = token1_address == hype_token::address();

                    let update_timestamp_millis = chrono::Utc::now().timestamp_millis();

                    // Create pool metadata
                    let pool_metadata = PoolMetadata {
                        chain: Chain::HyperEvm,
                        pool_address: pool.to_string(),
                        pair_label: format!("{}/{}", token_metadata.symbol, base_metadata.symbol),
                        dex: Dex::HyperSwapV3,
                        pool_type: PoolType::None,
                        create_timestamp_millis: create_timestamp.unwrap_or(0),
                        update_timestamp_millis,
                        token_address: token_metadata.address.clone(),
                        token_decimals: token_metadata.decimals,
                        base_address: base_metadata.address.clone(),
                        base_decimals: base_metadata.decimals,
                        is_token_first,
                        is_active: true,
                        bin_step: None,
                    };

                    let pool_metadata = chain_cache
                        .insert_pool_metadata_and_publish_if_not_exists(pool_metadata)
                        .await?;
                    self.add_pool(pool).await?;

                    return Ok((pool_metadata, token_metadata, base_metadata, is_token_first));
                }
            };

        // If pool metadata exists, get token metadata from database
        let token_metadata = chain_cache
            .get_token_metadata_or_update_from_db(&pool_metadata.token_address)
            .await?
            .ok_or(anyhow::anyhow!("Token metadata not found: {}", pool_metadata.token_address))?;
        let base_metadata = chain_cache
            .get_token_metadata_or_update_from_db(&pool_metadata.base_address)
            .await?
            .ok_or(anyhow::anyhow!(
                "Base token metadata not found: {}",
                pool_metadata.base_address
            ))?;
        let is_token_first = pool_metadata.is_token_first;

        Ok((pool_metadata, token_metadata, base_metadata, is_token_first))
    }

    pub(crate) async fn handle_event(
        &self,
        pool: &Address,
        event: &HyperSwapV3Event,
        tx_hash: String,
        tx_idx: u32,
        block_number: u64,
        block_timestamp_millis: i64,
        unsync_database_buf: &mut UnsyncDatabaseBuf,
    ) -> anyhow::Result<()> {
        let chain_cache = ChainCache::get_cache(Chain::HyperEvm);
        let hype_price = NativeTokenPriceManager::get()
            .await
            .get_latest_price(Chain::HyperEvm)
            .await
            .ok_or(anyhow::anyhow!("Failed to get hype price"))?;

        match event {
            HyperSwapV3Event::PoolCreated(_event) => {
                tracing::info!("PoolCreated: {:?}", _event);
                let (mut pool_metadata, _, _, _) =
                    self.get_or_fetch_metadatas(pool, Some(block_timestamp_millis)).await?;
                if pool_metadata.create_timestamp_millis != block_timestamp_millis {
                    pool_metadata.create_timestamp_millis = block_timestamp_millis;
                    chain_cache
                        .update_pool_metadata_create_info_and_publish(pool_metadata.clone())
                        .await?;
                }
            }
            HyperSwapV3Event::Initialize(event) => {
                let Initialize { sqrtPriceX96, .. } = event;
                let (_, token_metadata, base_metadata, _is_token_first) =
                    self.get_or_fetch_metadatas(pool, None).await?;

                let token = to_token(&token_metadata);
                let base = to_token(&base_metadata);
                let price = sqrt_ratio_x96_to_price(*sqrtPriceX96, token, base)?;
                let price_f64 = price.to_significant(5, None)?;
                let price_f64 = price_f64.parse::<f64>()?;

                // PoolState
                let price = price_f64;
                let liquidity =
                    get_pool_total_liquidity(pool, &token_metadata, &base_metadata, price_f64)
                        .await?;

                let market_cap =
                    to_ui_amount(token_metadata.supply_to_u256()?, token_metadata.decimals) *
                        price_f64;

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis: block_timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 1,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };

                let pool_state = chain_cache
                    .update_pool_state(&pool.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);
            }
            HyperSwapV3Event::Mint(event) => {
                let Mint { sender, amount0, amount1, .. } = event;

                let (pool_metadata, token_metadata, base_metadata, ..) =
                    self.get_or_fetch_metadatas(pool, None).await?;

                let token = to_token(&token_metadata);
                let base = to_token(&base_metadata);

                let sqrt_price_x96 = get_current_sqrt_price_x96(pool).await?;
                let price = sqrt_ratio_x96_to_price(sqrt_price_x96, token, base)?;
                let price_f64 = price.to_significant(5, None)?;
                let price_f64 = price_f64.parse::<f64>()?;

                let liquidity =
                    get_pool_total_liquidity(pool, &token_metadata, &base_metadata, price_f64)
                        .await?;

                let price = price_f64;
                let market_cap =
                    to_ui_amount(token_metadata.supply_to_u256()?, token_metadata.decimals) *
                        price_f64;

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis: block_timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 1,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };
                let pool_state = chain_cache
                    .update_pool_state(&pool.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);
            }
            HyperSwapV3Event::Burn(event) => {
                let Burn { owner, amount0, amount1, .. } = event;

                let (pool_metadata, token_metadata, base_metadata, ..) =
                    self.get_or_fetch_metadatas(pool, None).await?;

                let token = to_token(&token_metadata);
                let base = to_token(&base_metadata);

                let sqrt_price_x96 = get_current_sqrt_price_x96(pool).await?;
                let price = sqrt_ratio_x96_to_price(sqrt_price_x96, token, base)?;
                let price_f64 = price.to_significant(5, None)?;
                let price_f64 = price_f64.parse::<f64>()?;

                let liquidity =
                    get_pool_total_liquidity(pool, &token_metadata, &base_metadata, price_f64)
                        .await?;
                let price = price_f64;

                let market_cap =
                    to_ui_amount(token_metadata.supply_to_u256()?, token_metadata.decimals) *
                        price_f64;

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis: block_timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 1,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };

                let pool_state = chain_cache
                    .update_pool_state(&pool.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);
            }
            HyperSwapV3Event::Swap(event) => {
                let Swap { sender, amount0, amount1, .. } = event;

                let (pool_metadata, token_metadata, base_metadata, ..) =
                    self.get_or_fetch_metadatas(pool, None).await?;

                let token = to_token(&token_metadata);
                let base = to_token(&base_metadata);
                let sqrt_price_x96 = get_current_sqrt_price_x96(pool).await?;
                let price = sqrt_ratio_x96_to_price(sqrt_price_x96, token, base)?;
                let price_f64 = price.to_significant(5, None)?;
                let price_f64 = price_f64.parse::<f64>()?;

                let is_buy = if pool_metadata.is_token_first {
                    *amount0 > I256::ZERO
                } else {
                    *amount1 > I256::ZERO
                };

                // Trade
                let trade = DexTrade::new_hyper_evm_dex_trade(
                    tx_hash.to_string(),
                    tx_idx,
                    pool.to_string(),
                    sender.to_string(),
                    is_buy,
                    token_metadata.address.to_string(),
                    U256::from(amount0.abs()),
                    base_metadata.address.to_string(),
                    U256::from(amount1.abs()),
                    block_number,
                    block_timestamp_millis,
                )?;
                unsync_database_buf.insert_dex_trade(trade);

                // PoolState
                let price = price_f64;
                let liquidity =
                    get_pool_total_liquidity(pool, &token_metadata, &base_metadata, price_f64)
                        .await?;
                let market_cap =
                    to_ui_amount(token_metadata.supply_to_u256()?, token_metadata.decimals) *
                        price_f64;

                let delta_volume = if pool_metadata.is_token_first {
                    to_ui_amount(U256::from(amount1.abs()), base_metadata.decimals)
                } else {
                    to_ui_amount(U256::from(amount0.abs()), base_metadata.decimals)
                };

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis: block_timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume,
                    delta_buy_volume: if is_buy { delta_volume } else { 0.0 },
                    delta_sell_volume: if is_buy { 0.0 } else { delta_volume },
                    delta_txns: 1,
                    delta_buy_txns: if is_buy { 1 } else { 0 },
                    delta_sell_txns: if is_buy { 0 } else { 1 },
                    bonding_curve_progress: None,
                };

                let pool_state = chain_cache
                    .update_pool_state(&pool.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);

                let mut delta = TokenHolderDelta::new(block_timestamp_millis, block_number);

                if pool_metadata.is_token_first {
                    if *amount0 < I256::ZERO {
                        delta.bought_txns = 1;
                        delta.bought_amount = u256_to_big_decimal(U256::from(amount0.abs()))?;
                        delta.spent_native_token_ui_amount =
                            to_ui_amount(U256::from(amount0.abs()), token_metadata.decimals);
                        delta.total_spent_usd = delta.spent_native_token_ui_amount * hype_price;
                    } else {
                        delta.sold_txns = 1;
                        delta.sold_amount = u256_to_big_decimal(U256::from(amount0.abs()))?;
                        delta.received_native_token_ui_amount =
                            to_ui_amount(U256::from(amount0.abs()), token_metadata.decimals);
                        delta.total_received_usd =
                            delta.received_native_token_ui_amount * hype_price;
                    }
                } else if *amount1 < I256::ZERO {
                    delta.bought_txns = 1;
                    delta.bought_amount = u256_to_big_decimal(U256::from(amount1.abs()))?;
                    delta.spent_native_token_ui_amount =
                        to_ui_amount(U256::from(amount1.abs()), token_metadata.decimals);
                    delta.total_spent_usd = delta.spent_native_token_ui_amount * hype_price;
                } else {
                    delta.sold_txns = 1;
                    delta.sold_amount = u256_to_big_decimal(U256::from(amount1.abs()))?;
                    delta.received_native_token_ui_amount =
                        to_ui_amount(U256::from(amount1.abs()), token_metadata.decimals);
                    delta.total_received_usd = delta.received_native_token_ui_amount * hype_price;
                }

                let token_holder = chain_cache
                    .update_token_holder(&token_metadata.address, &sender.to_string(), delta)
                    .await?;
                unsync_database_buf.insert_token_holder(token_holder);
            }
        }

        Ok(())
    }
}

pub(crate) fn to_token(metadata: &TokenMetadata) -> Token {
    Token::new(
        HYPER_EVM_CHAIN_ID,
        Address::from_str(&metadata.address).unwrap(),
        metadata.decimals,
        None,
        None,
        0,
        0,
    )
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use alloy::{primitives::Address, providers::Provider, sol_types::SolEvent};

    use crate::hyperevm::{
        hyperswap::events::HyperswapV3, indexer::HyperEvmIndexer, utils::get_provider,
    };

    #[tokio::test]
    async fn test_factory() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let factory = Address::from_str("0xB1c0fa0B789320044A6F623cFe5eBda9562602E3").unwrap();
        let p = get_provider();
        let latest = p.get_block_number().await.unwrap();
        println!("sig hash: {:?}", HyperswapV3::PoolCreated::SIGNATURE_HASH);
        let logs = HyperEvmIndexer::get_logs(
            4517001,
            4527001,
            factory,
            &[HyperswapV3::PoolCreated::SIGNATURE],
        )
        .await
        .unwrap();
        println!("{:?}", logs);
    }

    #[tokio::test]
    async fn test_get_address_tx() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let p = get_provider();
        let receipts = p.get_block_receipts(100.into()).await.unwrap().unwrap();

        println!("{:?}", receipts);
    }
}
