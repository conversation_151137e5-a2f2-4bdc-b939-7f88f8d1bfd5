use alloy::sol;

use crate::hyperevm::hyperswap::events::HyperswapV3::{Burn, Initialize, Mint, PoolCreated, Swap};

sol!(
    #[sol(rpc)]
    interface HyperswapV3 {
        /// @notice The currently in range liquidity available to the pool
        /// @dev This value has no relationship to the total liquidity across all ticks
        function liquidity() external view returns (uint128);

        /// @notice The contract that deployed the pool, which must adhere to the IUniswapV3Factory interface
        /// @return The contract address
        function factory() external view returns (address);

        /// @notice The first of the two tokens of the pool, sorted by address
        /// @return The token contract address
        function token0() external view returns (address);

        /// @notice The second of the two tokens of the pool, sorted by address
        /// @return The token contract address
        function token1() external view returns (address);

        /// @notice The pool's fee in hundredths of a bip, i.e. 1e-6
        /// @return The fee
        function fee() external view returns (uint24);

        /// @notice The pool tick spacing
        /// @dev Ticks can only be used at multiples of this value, minimum of 1 and always positive
        /// e.g.: a tickSpacing of 3 means ticks can be initialized every 3rd tick, i.e., ..., -6, -3, 0, 3, 6, ...
        /// This value is an int24 to avoid casting even though it is always positive.
        /// @return The tick spacing
        function tickSpacing() external view returns (int24);

        function slot0()
        external
        view
        returns (
            uint160 sqrtPriceX96,
            int24 tick,
            uint16 observationIndex,
            uint16 observationCardinality,
            uint16 observationCardinalityNext,
            uint8 feeProtocol,
            bool unlocked
        );

        #[derive(Debug)]
        event PoolCreated(
            address indexed token0,
            address indexed token1,
            uint24 indexed fee,
            int24 tickSpacing,
            address pool
        );

        #[derive(Debug)]
        event Initialize(uint160 sqrtPriceX96, int24 tick);

        #[derive(Debug)]
        event Mint(
            address sender,
            address indexed owner,
            int24 indexed tickLower,
            int24 indexed tickUpper,
            uint128 amount,
            uint256 amount0,
            uint256 amount1
        );

        #[derive(Debug)]
        event Collect(
            address sender,
            address indexed owner,
            int24 indexed tickLower,
            int24 indexed tickUpper,
            uint128 amount,
            uint256 amount0,
            uint256 amount1
        );

        #[derive(Debug)]
        event Burn(
            address indexed owner,
            int24 indexed tickLower,
            int24 indexed tickUpper,
            uint128 amount,
            uint256 amount0,
            uint256 amount1
        );

        #[derive(Debug)]
        event Swap(
            address indexed sender,
            address indexed recipient,
            int256 amount0,
            int256 amount1,
            uint160 sqrtPriceX96, // limit price
            uint128 liquidity,
            int24 tick
        );
    }
);

#[derive(Debug)]
pub enum HyperSwapV3Event {
    PoolCreated(PoolCreated),
    Initialize(Initialize),
    Mint(Mint),
    Burn(Burn),
    Swap(Swap),
}

#[cfg(test)]
mod tests {
    use std::{collections::HashSet, str::FromStr, sync::Arc};

    use alloy::{primitives::Address, providers::Provider};
    use tokio::sync::RwLock;

    use crate::{
        cache::UnsyncDatabaseBuf,
        hyperevm::{
            hyperswap::HyperSwapV3,
            utils::{get_factory_address, get_provider},
        },
    };

    #[tokio::test]
    async fn test_parse_pool_created() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let provider = get_provider();
        let tx_hash = "0x0969921c773843064257b1f0f14601a1f3355664d1cd76ae3c2300e7e8468868";
        let receipt =
            provider.get_transaction_receipt(tx_hash.parse().unwrap()).await.unwrap().unwrap();
        let logs = receipt.logs();
        for log in logs {
            let event = HyperSwapV3::parse_log(log).await.unwrap();
            println!("{:?}", event);
        }
    }

    #[tokio::test]
    async fn test_parse_initialize() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let provider = get_provider();
        let tx_hash = "0xcd0e4a9325f6d5e46faff3612eeb25cf2f84d4465f60faf7cf0277b76dc1b740";
        let receipt =
            provider.get_transaction_receipt(tx_hash.parse().unwrap()).await.unwrap().unwrap();
        let logs = receipt.logs();
        for log in logs {
            let event = HyperSwapV3::parse_log(log).await.unwrap();
            println!("{:?}", event);
        }
    }

    #[tokio::test]
    async fn test_parse_swap() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let provider = get_provider();
        let tx_hash = "0x2aaad76e5606a80f85170e861d8ef3e6706ef26d0de076163a4e28ddc70247a3";

        let receipt =
            provider.get_transaction_receipt(tx_hash.parse().unwrap()).await.unwrap().unwrap();
        let logs = receipt.logs();
        for log in logs {
            let event = HyperSwapV3::parse_log(log).await.unwrap();
            println!("{:?}", event);
        }
    }

    #[tokio::test]
    async fn test_parse_mint() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let providers = get_provider();
        let factory = Address::from_str("0xb1c0fa0b789320044a6f623cfe5ebda9562602e3").unwrap();

        let block_number = 7058725;

        let receipts = providers.get_block_receipts(block_number.into()).await.unwrap().unwrap();
        tracing::info!("got receipts");

        let hyperswap = HyperSwapV3 { factory, pools: Arc::new(RwLock::new(HashSet::new())) };

        for receipt in receipts {
            let logs = receipt.logs();
            for (tx_idx, log) in logs.iter().enumerate() {
                let log_address = log.address();
                tracing::info!("log_address: {:?}", log_address);
                let log_factory = get_factory_address(&log_address).await.unwrap_or_default();
                tracing::info!("log_factory: {:?}", log_factory);
                if log_factory != factory {
                    continue;
                }

                let event = HyperSwapV3::parse_log(log).await.unwrap();

                tracing::info!("tx_hash: {:?}, event: {:?}", receipt.transaction_hash, event);
                if let Some(event) = event {
                    if let Err(e) = hyperswap
                        .handle_event(
                            &log_address,
                            &event,
                            receipt.transaction_hash.to_string(),
                            tx_idx as u32,
                            block_number,
                            0,
                            &mut UnsyncDatabaseBuf::new(),
                        )
                        .await
                    {
                        tracing::error!("error: {:?}", e);
                    }
                }
            }
        }
    }

    #[tokio::test]
    async fn test_process_receipts() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let provider = get_provider();
        let tx_hash = "0x18778641ca86c8fdc267e4b8c08f6e2db3406fc07b0d04fa19cbd94add4d5496";
        let factory = Address::from_str("0xb1c0fa0b789320044a6f623cfe5ebda9562602e3").unwrap();
        let hyperswap = HyperSwapV3 { factory, pools: Arc::new(RwLock::new(HashSet::new())) };
        let receipts =
            provider.get_transaction_receipt(tx_hash.parse().unwrap()).await.unwrap().unwrap();

        let logs = receipts.logs();
        for (tx_idx, log) in logs.iter().enumerate() {
            let event = HyperSwapV3::parse_log(log).await.unwrap();

            if let Some(event) = event {
                if let Err(e) = hyperswap
                    .handle_event(
                        &log.address(),
                        &event,
                        tx_hash.to_string(),
                        tx_idx as u32,
                        0,
                        0,
                        &mut UnsyncDatabaseBuf::new(),
                    )
                    .await
                {
                    tracing::error!("error: {:?}", e);
                }
            }
        }
    }
}
