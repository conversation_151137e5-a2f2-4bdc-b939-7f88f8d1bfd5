use std::{collections::HashMap, time::Duration};

use tokio::sync::mpsc;

use superstack_data::{
    kafka::{
        topics::{IndexerBlockMsg, TokenHoldersMsg},
        KafkaProducer,
    },
    postgres::{
        indexer::{DexTrade, PoolState, TokenHolder},
        PostgresDatabase,
    },
};

#[derive(Debug, Clone)]
pub struct UnsyncDatabaseBuf {
    pool_states: HashMap<String, PoolState>,
    dex_trades: Vec<DexTrade>,
    token_holders: HashMap<(String, String), TokenHolder>,
}

impl Default for UnsyncDatabaseBuf {
    fn default() -> Self {
        Self::new()
    }
}

impl UnsyncDatabaseBuf {
    pub fn new() -> Self {
        Self { pool_states: HashMap::new(), dex_trades: Vec::new(), token_holders: HashMap::new() }
    }

    pub fn insert_pool_state(&mut self, pool_state: PoolState) {
        let pool_address = pool_state.pool_address.clone();
        // Use entry API for efficient insert-or-update
        match self.pool_states.entry(pool_address) {
            std::collections::hash_map::Entry::Occupied(mut entry) => {
                // Compare and keep the new one
                let existing = entry.get();
                if pool_state.is_newer_than(existing) {
                    entry.insert(pool_state);
                }
                // If equal or less, keep the existing one (no-op)
            }
            std::collections::hash_map::Entry::Vacant(entry) => {
                // Insert new pool state
                entry.insert(pool_state);
            }
        }
    }

    pub fn insert_dex_trade(&mut self, dex_trade: DexTrade) {
        self.dex_trades.push(dex_trade);
    }

    pub fn insert_token_holder(&mut self, token_holder: TokenHolder) {
        let token_address = token_holder.token_address.clone();
        let holder_address = token_holder.holder_address.clone();

        // Use entry API for efficient insert-or-update
        match self.token_holders.entry((token_address, holder_address)) {
            std::collections::hash_map::Entry::Occupied(mut entry) => {
                // Compare and keep the new one
                let existing = entry.get();
                if token_holder.is_newer_than(existing) {
                    entry.insert(token_holder);
                }
                // If equal or less, keep the existing one (no-op)
            }
            std::collections::hash_map::Entry::Vacant(entry) => {
                // Insert new token holder
                entry.insert(token_holder);
            }
        }
    }

    pub async fn save_and_publish_trades_and_pool_states(
        &mut self,
        mpsc_tx: mpsc::Sender<Vec<TokenHolder>>,
    ) {
        // Drain all data atomically
        let (pool_states, dex_trades, token_holders) = {
            let pool_states = self.pool_states.drain().map(|(_, state)| state).collect::<Vec<_>>();
            let dex_trades = self.dex_trades.drain(..).collect::<Vec<_>>();
            let token_holders =
                self.token_holders.drain().map(|(_, holder)| holder).collect::<Vec<_>>();

            (pool_states, dex_trades, token_holders)
        };

        // Early return if no data
        if pool_states.is_empty() && dex_trades.is_empty() && token_holders.is_empty() {
            return;
        }

        // Spawn a task to publish the block msg, not publishing token holders here
        let publish_handle = tokio::spawn(Self::publish(
            pool_states.clone(),
            dex_trades.clone(),
            token_holders.clone(),
            false,
        ));

        // Save all dex trades and pool states immediately
        Self::save_all(dex_trades, pool_states, vec![]).await;

        // Send token holders to the mpsc channel to save and publish later
        if let Err(e) = mpsc_tx.send(token_holders).await {
            tracing::error!("Error sending token holders to mpsc channel: {:?}", e);
            std::process::exit(1);
        }

        // Wait for the publish task to complete
        let _ = publish_handle.await;
    }

    pub async fn save_and_publish_all(&mut self) {
        // Drain all data atomically
        let (pool_states, dex_trades, token_holders) = {
            let pool_states = self.pool_states.drain().map(|(_, state)| state).collect::<Vec<_>>();
            let dex_trades = self.dex_trades.drain(..).collect::<Vec<_>>();
            let token_holders =
                self.token_holders.drain().map(|(_, holder)| holder).collect::<Vec<_>>();

            (pool_states, dex_trades, token_holders)
        };

        // Early return if no data
        if pool_states.is_empty() && dex_trades.is_empty() && token_holders.is_empty() {
            return;
        }

        let publish_handle = tokio::spawn(Self::publish(
            pool_states.clone(),
            dex_trades.clone(),
            token_holders.clone(),
            true,
        ));

        Self::save_all(dex_trades, pool_states, token_holders).await;

        let _ = publish_handle.await;
    }

    pub async fn should_save(&self) -> bool {
        let pool_states_len = self.pool_states.len();
        let dex_trades_len = self.dex_trades.len();
        let token_holders_len = self.token_holders.len();

        let total_len = pool_states_len + dex_trades_len + token_holders_len;
        total_len > 200
    }

    pub async fn save_and_publish_token_holders(&mut self) {
        // Drain all data atomically
        let token_holders =
            self.token_holders.drain().map(|(_, holder)| holder).collect::<Vec<_>>();

        // Early return if no data
        if token_holders.is_empty() {
            return;
        }

        let kafka_producer = KafkaProducer::get();
        let token_holders_msg = TokenHoldersMsg::new(token_holders.clone());

        // Retry 2 times with 100ms delay, allow failure here since the token holders are not
        // critical
        let mut retry_count = 0;
        while let Err(e) = kafka_producer.send::<TokenHoldersMsg>(&token_holders_msg).await {
            tracing::error!("Error publishing token holders msg: {:?}", e);
            retry_count += 1;
            if retry_count > 2 {
                tracing::error!("Exceeded max retries for publishing token holders msg: {:?}", e);
                break;
            }
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        Self::save_all(vec![], vec![], token_holders).await;
    }

    async fn save_all(
        dex_trades: Vec<DexTrade>,
        pool_states: Vec<PoolState>,
        token_holders: Vec<TokenHolder>,
    ) {
        let db = PostgresDatabase::get_indexer_db().await;

        if !dex_trades.is_empty() {
            let mut retry_count = 0;
            while let Err(e) = db.insert_dex_trades(&dex_trades).await {
                tracing::error!("Error inserting dex trades: {:?}", e);
                retry_count += 1;
                tokio::time::sleep(Duration::from_secs(1)).await;
                if retry_count > 10 {
                    tracing::error!("Exceeded max retries for inserting dex trades: {:?}", e);
                    std::process::exit(1);
                }
            }
        }

        if !pool_states.is_empty() {
            let mut retry_count = 0;
            while let Err(e) = db.insert_or_update_pool_states(&pool_states).await {
                tracing::error!("Error inserting or updating pool states: {:?}", e);
                retry_count += 1;
                tokio::time::sleep(Duration::from_secs(1)).await;
                if retry_count > 10 {
                    tracing::error!(
                        "Exceeded max retries for inserting or updating pool states: {:?}",
                        e
                    );
                    std::process::exit(1);
                }
            }
        }

        if !token_holders.is_empty() {
            let mut retry_count = 0;
            while let Err(e) = db.insert_or_update_token_holders(&token_holders).await {
                tracing::error!("Error inserting or updating token holders: {:?}", e);
                retry_count += 1;
                tokio::time::sleep(Duration::from_secs(1)).await;
                if retry_count > 10 {
                    tracing::error!(
                        "Exceeded max retries for inserting or updating token holders: {:?}",
                        e
                    );
                    std::process::exit(1);
                }
            }
        }
    }

    async fn publish(
        pool_states: Vec<PoolState>,
        dex_trades: Vec<DexTrade>,
        token_holders: Vec<TokenHolder>,
        publish_holders: bool,
    ) {
        let (chain, block_number) = if let Some(trade) = dex_trades.first() {
            (trade.chain, trade.block_number)
        } else if let Some(state) = pool_states.first() {
            (state.chain, state.block_number)
        } else if let Some(holder) = token_holders.first() {
            (holder.chain, holder.update_block_number)
        } else {
            return;
        };

        let indexer_block_msg = IndexerBlockMsg::new(
            chain,
            block_number,
            dex_trades,
            pool_states,
            token_holders.clone(),
        );
        let kafka_producer = KafkaProducer::get();
        let mut retry_count = 0;
        while let Err(e) = kafka_producer.send::<IndexerBlockMsg>(&indexer_block_msg).await {
            tracing::error!("Error publishing indexer block msg: {:?}", e);
            retry_count += 1;
            tokio::time::sleep(Duration::from_secs(1)).await;
            if retry_count > 10 {
                tracing::error!("Exceeded max retries for publishing indexer block msg: {:?}", e);
                std::process::exit(1);
            }
        }

        if publish_holders && !token_holders.is_empty() {
            let token_holders_msg = TokenHoldersMsg::new(token_holders);
            // Allow failure here since the token holders are not critical
            if let Err(e) = kafka_producer.send::<TokenHoldersMsg>(&token_holders_msg).await {
                tracing::error!("Error publishing token holders msg: {:?}", e);
            }
        }
    }

    #[cfg(test)]
    pub async fn save_pool_states_for_test(&mut self) -> anyhow::Result<()> {
        // Drain all data atomically
        let pool_states = self.pool_states.drain().map(|(_, state)| state).collect::<Vec<_>>();

        // Early return if no data
        if pool_states.is_empty() {
            return Ok(());
        }

        let start_time = std::time::Instant::now();
        let db = PostgresDatabase::get_indexer_db().await;

        let mut retry_count = 0;
        while let Err(e) = db.insert_or_update_pool_states(&pool_states).await {
            tracing::error!("Error inserting pool states: {:?}", e);
            retry_count += 1;
            tokio::time::sleep(Duration::from_secs(1)).await;
            if retry_count > 10 {
                tracing::error!("Error inserting pool states: {:?}", e);
                std::process::exit(1);
            }
        }

        tracing::info!("Saved {} pool states in {:?}", pool_states.len(), start_time.elapsed());

        Ok(())
    }
}
