use std::sync::{Arc, OnceLock};

use anyhow::Result;
use quick_cache::sync::Cache;
use superstack_data::{
    kafka::KafkaProducer,
    postgres::{
        enums::Chain,
        indexer::{
            PoolMetadata, PoolState, PoolStateDelta, TokenHolder, TokenHolderDelta, TokenMetadata,
        },
        PostgresDatabase,
    },
    redis::RedisClient,
};
use tokio::sync::RwLock;

pub struct ChainCache {
    chain: Chain,
    token_metadata: Cache<String, TokenMetadata>,
    pool_metadata: Cache<String, PoolMetadata>,
    pool_state: Cache<String, Arc<RwLock<PoolState>>>,
    token_holder: Cache<(String, String), Arc<RwLock<TokenHolder>>>,
}

impl ChainCache {
    pub fn new(chain: Chain, base_size: usize) -> Self {
        let token_metadata_size = base_size;
        let pool_metadata_size = base_size * 4;
        let pool_state_size = base_size * 4;
        let token_holder_size = base_size * 8;

        Self {
            chain,
            token_metadata: Cache::new(token_metadata_size),
            pool_metadata: Cache::new(pool_metadata_size),
            pool_state: Cache::new(pool_state_size),
            token_holder: Cache::new(token_holder_size),
        }
    }

    pub fn get_cache(chain: Chain) -> &'static Self {
        static SOLANA_CACHE: OnceLock<ChainCache> = OnceLock::new();
        static HYPERCORE_CACHE: OnceLock<ChainCache> = OnceLock::new();
        static HYPEREVM_CACHE: OnceLock<ChainCache> = OnceLock::new();

        match chain {
            Chain::Solana => SOLANA_CACHE.get_or_init(|| Self::new(chain, 10000)),
            Chain::Hypercore => HYPERCORE_CACHE.get_or_init(|| Self::new(chain, 5000)),
            Chain::HyperEvm => HYPEREVM_CACHE.get_or_init(|| Self::new(chain, 5000)),
        }
    }

    pub async fn get_token_metadata_or_update_from_db(
        &self,
        token_address: &str,
    ) -> Result<Option<TokenMetadata>> {
        match self.token_metadata.get_value_or_guard_async(token_address).await {
            Ok(value) => Ok(Some(value)),
            Err(guard) => {
                let db = PostgresDatabase::get_indexer_db().await;
                let token_metadata = db.get_token_metadata(self.chain, token_address).await?;
                if let Some(token_metadata) = token_metadata {
                    guard
                        .insert(token_metadata.clone())
                        .map_err(|_| anyhow::anyhow!("Failed to insert token metadata to guard"))?;
                    Ok(Some(token_metadata))
                } else {
                    Ok(None)
                }
            }
        }
    }

    pub async fn get_pool_metadata_or_update_from_db(
        &self,
        pool_address: &str,
    ) -> Result<Option<PoolMetadata>> {
        match self.pool_metadata.get_value_or_guard_async(pool_address).await {
            Ok(value) => Ok(Some(value)),
            Err(guard) => {
                let db = PostgresDatabase::get_indexer_db().await;
                let pool_metadata = db.get_pool_metadata(self.chain, pool_address).await?;
                if let Some(pool_metadata) = pool_metadata {
                    guard
                        .insert(pool_metadata.clone())
                        .map_err(|_| anyhow::anyhow!("Failed to insert pool metadata to guard"))?;
                    Ok(Some(pool_metadata))
                } else {
                    Ok(None)
                }
            }
        }
    }

    pub async fn get_pool_state_or_update_from_db(
        &self,
        pool_address: &str,
        before_block_number: u64,
    ) -> Result<Option<Arc<RwLock<PoolState>>>> {
        match self.pool_state.get_value_or_guard_async(pool_address).await {
            Ok(value) => Ok(Some(value)),
            Err(guard) => {
                let db = PostgresDatabase::get_indexer_db().await;
                let pool_state = db
                    .get_latest_pool_state_before_block_number(
                        self.chain,
                        pool_address,
                        before_block_number,
                    )
                    .await?;
                if let Some(pool_state) = pool_state {
                    let pool_state = Arc::new(RwLock::new(pool_state));
                    guard
                        .insert(pool_state.clone())
                        .map_err(|_| anyhow::anyhow!("Failed to insert pool state to guard"))?;
                    Ok(Some(pool_state))
                } else {
                    Ok(None)
                }
            }
        }
    }

    async fn get_token_holder_or_update_from_db(
        &self,
        token_address: &str,
        holder_address: &str,
    ) -> Result<Option<Arc<RwLock<TokenHolder>>>> {
        match self
            .token_holder
            .get_value_or_guard_async(&(token_address.to_string(), holder_address.to_string()))
            .await
        {
            Ok(value) => Ok(Some(value)),
            Err(guard) => {
                let db = PostgresDatabase::get_indexer_db().await;
                let token_holder =
                    db.get_token_holder(self.chain, token_address, holder_address).await?;
                if let Some(token_holder) = token_holder {
                    let token_holder = Arc::new(RwLock::new(token_holder));
                    guard
                        .insert(token_holder.clone())
                        .map_err(|_| anyhow::anyhow!("Failed to insert token holder to guard"))?;
                    Ok(Some(token_holder))
                } else {
                    Ok(None)
                }
            }
        }
    }

    pub async fn update_token_metadata_create_info_and_publish(
        &self,
        token_metadata: TokenMetadata,
    ) -> Result<TokenMetadata> {
        self.token_metadata.insert(token_metadata.address.to_string(), token_metadata.clone());

        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_token_metadata_or_update_create(&token_metadata).await?;

        let redis_client = RedisClient::get_instance().await;
        let token_metadata_in_redis = redis_client
            .get_token_metadata_or_update_from_db(token_metadata.chain, &token_metadata.address)
            .await?;
        if token_metadata.create_dex != token_metadata_in_redis.create_dex ||
            token_metadata.create_block_number != token_metadata_in_redis.create_block_number ||
            token_metadata.create_tx_hash != token_metadata_in_redis.create_tx_hash ||
            token_metadata.create_bonding_curve != token_metadata_in_redis.create_bonding_curve ||
            token_metadata.create_dev != token_metadata_in_redis.create_dev ||
            token_metadata.create_timestamp_millis !=
                token_metadata_in_redis.create_timestamp_millis
        {
            redis_client
                .set_token_metadata_create_info(
                    token_metadata.chain,
                    &token_metadata.address,
                    token_metadata.create_dex,
                    token_metadata.create_block_number.clone(),
                    token_metadata.create_tx_hash.clone(),
                    token_metadata.create_bonding_curve.clone(),
                    token_metadata.create_dev.clone(),
                    token_metadata.create_timestamp_millis,
                )
                .await?;
        }

        let kafka_producer = KafkaProducer::get();
        kafka_producer.send::<TokenMetadata>(&token_metadata).await?;

        Ok(token_metadata)
    }

    pub async fn update_token_metadata_migration_info_and_publish(
        &self,
        token_metadata: TokenMetadata,
    ) -> Result<TokenMetadata> {
        self.token_metadata.insert(token_metadata.address.to_string(), token_metadata.clone());

        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_token_metadata_or_update_migration(&token_metadata).await?;

        if token_metadata.migration_pool_address.is_none() {
            tracing::error!(
                "[FIXME] Token metadata migration pool address is none when updating token metadata migration info: {}",
                token_metadata.address
            );
            return Ok(token_metadata);
        }

        let redis_client = RedisClient::get_instance().await;
        let token_metadata_in_redis = redis_client
            .get_token_metadata_or_update_from_db(token_metadata.chain, &token_metadata.address)
            .await?;
        if token_metadata.migration_timestamp_millis !=
            token_metadata_in_redis.migration_timestamp_millis ||
            token_metadata.migration_pool_address !=
                token_metadata_in_redis.migration_pool_address
        {
            if let Some(migration_pool_address) = token_metadata.migration_pool_address.as_ref() {
                redis_client
                    .set_token_metadata_migration_info(
                        token_metadata.chain,
                        &token_metadata.address,
                        migration_pool_address.to_string(),
                        token_metadata.migration_timestamp_millis,
                    )
                    .await?;
            }
        }

        let kafka_producer = KafkaProducer::get();
        kafka_producer.send::<TokenMetadata>(&token_metadata).await?;

        Ok(token_metadata)
    }

    pub async fn update_pool_metadata_create_info_and_publish(
        &self,
        pool_metadata: PoolMetadata,
    ) -> Result<PoolMetadata> {
        self.pool_metadata.insert(pool_metadata.pool_address.to_string(), pool_metadata.clone());

        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_pool_metadata_or_update_create_timestamp(&pool_metadata).await?;

        let redis_client = RedisClient::get_instance().await;
        let pool_metadata_in_redis = redis_client
            .get_pool_metadata_or_update_from_db(pool_metadata.chain, &pool_metadata.pool_address)
            .await?;
        if pool_metadata.create_timestamp_millis > pool_metadata_in_redis.create_timestamp_millis {
            redis_client
                .set_pool_metadata_create_timestamp(
                    pool_metadata.chain,
                    &pool_metadata.pool_address,
                    pool_metadata.create_timestamp_millis,
                )
                .await?;
        }

        let kafka_producer = KafkaProducer::get();
        kafka_producer.send::<PoolMetadata>(&pool_metadata).await?;

        Ok(pool_metadata)
    }

    pub async fn update_pool_metadata_active_info_and_publish(
        &self,
        pool_metadata: PoolMetadata,
    ) -> Result<PoolMetadata> {
        self.pool_metadata.insert(pool_metadata.pool_address.to_string(), pool_metadata.clone());

        let db = PostgresDatabase::get_indexer_db().await;
        db.insert_pool_metadata_or_update_is_active(&pool_metadata).await?;

        let redis_client = RedisClient::get_instance().await;
        let pool_metadata_in_redis = redis_client
            .get_pool_metadata_or_update_from_db(pool_metadata.chain, &pool_metadata.pool_address)
            .await?;
        if pool_metadata_in_redis.is_active != pool_metadata.is_active {
            redis_client
                .set_pool_metadata_is_active(
                    pool_metadata.chain,
                    &pool_metadata.pool_address,
                    pool_metadata.is_active,
                    pool_metadata.update_timestamp_millis,
                )
                .await?;
        }

        let kafka_producer = KafkaProducer::get();
        kafka_producer.send::<PoolMetadata>(&pool_metadata).await?;

        Ok(pool_metadata)
    }

    pub async fn insert_token_metadata_and_publish_if_not_exists(
        &self,
        token_metadata: TokenMetadata,
    ) -> Result<TokenMetadata> {
        match self
            .token_metadata
            .get_value_or_guard_async(&token_metadata.address.to_string())
            .await
        {
            Ok(value) => Ok(value),
            Err(guard) => {
                guard
                    .insert(token_metadata.clone())
                    .map_err(|_| anyhow::anyhow!("Failed to insert token metadata to guard"))?;

                let db = PostgresDatabase::get_indexer_db().await;
                if db.insert_token_metadata(&token_metadata).await? {
                    let kafka_producer = KafkaProducer::get();
                    kafka_producer.send::<TokenMetadata>(&token_metadata).await?;
                }

                Ok(token_metadata)
            }
        }
    }

    pub async fn insert_pool_metadata_and_publish_if_not_exists(
        &self,
        pool_metadata: PoolMetadata,
    ) -> Result<PoolMetadata> {
        match self
            .pool_metadata
            .get_value_or_guard_async(&pool_metadata.pool_address.to_string())
            .await
        {
            Ok(value) => Ok(value),
            Err(guard) => {
                guard
                    .insert(pool_metadata.clone())
                    .map_err(|_| anyhow::anyhow!("Failed to insert pool metadata to guard"))?;

                let db = PostgresDatabase::get_indexer_db().await;
                if db.insert_pool_metadata(&pool_metadata).await? {
                    let kafka_producer = KafkaProducer::get();
                    kafka_producer.send::<PoolMetadata>(&pool_metadata).await?;
                }

                Ok(pool_metadata)
            }
        }
    }

    async fn insert_pool_state_or_merge(
        &self,
        pool_address: &str,
        pool_state_delta: PoolStateDelta,
    ) -> Result<PoolState> {
        match self.pool_state.get_value_or_guard_async(pool_address).await {
            Ok(value) => {
                let mut value = value.write().await;
                value.merge(&pool_state_delta);
                Ok(value.clone())
            }
            Err(guard) => {
                let pool_state = PoolState::new_from_delta(
                    self.chain,
                    pool_address.to_string(),
                    pool_state_delta,
                )?;
                let pool_state_arc = Arc::new(RwLock::new(pool_state.clone()));
                guard
                    .insert(pool_state_arc)
                    .map_err(|_| anyhow::anyhow!("Failed to insert pool state to guard"))?;
                Ok(pool_state)
            }
        }
    }

    async fn insert_token_holder_or_merge(
        &self,
        token_address: &str,
        holder_address: &str,
        token_holder_delta: TokenHolderDelta,
    ) -> Result<TokenHolder> {
        match self
            .token_holder
            .get_value_or_guard_async(&(token_address.to_string(), holder_address.to_string()))
            .await
        {
            Ok(value) => {
                let mut value = value.write().await;
                value.merge(&token_holder_delta);
                Ok(value.clone())
            }
            Err(guard) => {
                let token_holder = TokenHolder::new_from_delta(
                    self.chain,
                    token_address.to_string(),
                    holder_address.to_string(),
                    token_holder_delta,
                );
                let token_holder_arc = Arc::new(RwLock::new(token_holder.clone()));
                guard
                    .insert(token_holder_arc)
                    .map_err(|_| anyhow::anyhow!("Failed to insert token holder to guard"))?;
                Ok(token_holder)
            }
        }
    }

    pub async fn update_pool_state(
        &self,
        pool_address: &str,
        pool_state_delta: PoolStateDelta,
        before_block_number: u64,
    ) -> Result<PoolState> {
        if let Some(pool_state) =
            self.get_pool_state_or_update_from_db(pool_address, before_block_number).await?
        {
            let mut guard = pool_state.write().await;
            guard.merge(&pool_state_delta);
            Ok(guard.clone())
        } else {
            let pool_state =
                self.insert_pool_state_or_merge(pool_address, pool_state_delta).await?;
            Ok(pool_state)
        }
    }

    pub async fn update_token_holder(
        &self,
        token_address: &str,
        holder_address: &str,
        token_holder_delta: TokenHolderDelta,
    ) -> Result<TokenHolder> {
        if let Some(token_holder) =
            self.get_token_holder_or_update_from_db(token_address, holder_address).await?
        {
            let mut guard = token_holder.write().await;
            guard.merge(&token_holder_delta);
            Ok(guard.clone())
        } else {
            let token_holder = self
                .insert_token_holder_or_merge(token_address, holder_address, token_holder_delta)
                .await?;
            Ok(token_holder)
        }
    }
}
