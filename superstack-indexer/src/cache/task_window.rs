use std::{collections::HashMap, sync::Arc, time::Instant};

use anyhow::Result;
use tokio::sync::Mutex;

use superstack_data::postgres::{Chain, PostgresDatabase};

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct TaskWindow {
    chain: Chain,
    inner: Arc<Mutex<Inner>>,
}

#[derive(Debug)]
struct Inner {
    block_number_to_task_count: HashMap<u64, (u64, Instant)>,
}

impl TaskWindow {
    pub fn new(chain: Chain) -> Self {
        Self {
            chain,
            inner: Arc::new(Mutex::new(Inner { block_number_to_task_count: HashMap::new() })),
        }
    }

    pub async fn start_task(&self, block_number: u64) {
        let mut inner = self.inner.lock().await;
        let (task_count, _) =
            inner.block_number_to_task_count.entry(block_number).or_insert((0, Instant::now()));
        *task_count += 1;
    }

    pub async fn end_task(&self, block_number: u64) -> Result<()> {
        let mut inner = self.inner.lock().await;
        let mut try_move_window = false;
        if let Some((task_count, _)) = inner.block_number_to_task_count.get_mut(&block_number) {
            *task_count = task_count.checked_sub(1).unwrap_or(0);
            if *task_count == 0 {
                try_move_window = true;
            }
        } else {
            tracing::error!("Task count for block {} not found", block_number);
        }

        if try_move_window {
            let mut block_numbers =
                inner.block_number_to_task_count.keys().copied().collect::<Vec<_>>();
            block_numbers.sort_unstable();

            if block_numbers.is_empty() {
                return Ok(());
            }

            let mut finish_block_number = None;
            for block_number in block_numbers {
                let (task_count, start_time) = inner.block_number_to_task_count[&block_number];
                if task_count == 0 {
                    finish_block_number = Some((block_number, start_time));
                    inner.block_number_to_task_count.remove(&block_number);
                } else {
                    break;
                }
            }
            drop(inner);

            if let Some(finish_block_number) = finish_block_number {
                let db = PostgresDatabase::get_indexer_db().await;
                db.insert_or_update_chain_info(self.chain, finish_block_number.0).await?;
                tracing::info!(
                    "Finished task window for {:?} block {} in {:?}",
                    self.chain,
                    finish_block_number.0,
                    finish_block_number.1.elapsed()
                );
            }
        }

        Ok(())
    }
}
