use std::{collections::HashMap, sync::Arc};

use anyhow::Result;
use tokio::{sync::Mutex, task::JoinSet};

use superstack_data::{
    kafka::{
        topics::{IndexerBlockMsg, TokenHoldersMsg},
        KafkaProducer,
    },
    postgres::{
        indexer::{dex_trade::DexTrade, pool_state::PoolState, token_holder::TokenHolder},
        PostgresDatabase,
    },
};

#[derive(Debug, Clone)]
pub struct SyncDatabaseBuf {
    pool_states: Arc<Mutex<HashMap<String, PoolState>>>,
    dex_trades: Arc<Mutex<Vec<DexTrade>>>,
    token_holders: Arc<Mutex<HashMap<(String, String), TokenHolder>>>,
}

impl SyncDatabaseBuf {
    pub fn new() -> Arc<Self> {
        let buf = Self {
            pool_states: Arc::new(Mutex::new(HashMap::new())),
            dex_trades: Arc::new(Mutex::new(Vec::new())),
            token_holders: Arc::new(Mutex::new(HashMap::new())),
        };
        Arc::new(buf)
    }

    pub async fn insert_pool_state(&self, pool_state: PoolState) {
        let pool_address = pool_state.pool_address.clone();
        let mut guard = self.pool_states.lock().await;

        // Use entry API for efficient insert-or-update
        match guard.entry(pool_address) {
            std::collections::hash_map::Entry::Occupied(mut entry) => {
                // Compare and keep the new one
                let existing = entry.get();
                if pool_state.is_newer_than(existing) {
                    entry.insert(pool_state);
                }
                // If equal or less, keep the existing one (no-op)
            }
            std::collections::hash_map::Entry::Vacant(entry) => {
                // Insert new pool state
                entry.insert(pool_state);
            }
        }
        // Guard is automatically dropped here, releasing the lock
    }

    pub async fn insert_dex_trade(&self, dex_trade: DexTrade) {
        let mut guard = self.dex_trades.lock().await;
        guard.push(dex_trade);
    }

    pub async fn insert_token_holder(&self, token_holder: TokenHolder) {
        let token_address = token_holder.token_address.clone();
        let holder_address = token_holder.holder_address.clone();
        let mut guard = self.token_holders.lock().await;

        // Use entry API for efficient insert-or-update
        match guard.entry((token_address, holder_address)) {
            std::collections::hash_map::Entry::Occupied(mut entry) => {
                // Compare and keep the new one
                let existing = entry.get();
                if token_holder.is_newer_than(existing) {
                    entry.insert(token_holder);
                }
                // If equal or less, keep the existing one (no-op)
            }
            std::collections::hash_map::Entry::Vacant(entry) => {
                // Insert new token holder
                entry.insert(token_holder);
            }
        }
        // Guard is automatically dropped here, releasing the lock
    }

    pub async fn save_and_publish(&self) -> Result<()> {
        // Drain all data atomically to minimize lock time
        let (pool_states, dex_trades, token_holders) = {
            let mut pool_guard = self.pool_states.lock().await;
            let mut dex_guard = self.dex_trades.lock().await;
            let mut holder_guard = self.token_holders.lock().await;

            let pool_states = pool_guard.drain().map(|(_, state)| state).collect::<Vec<_>>();
            let dex_trades = dex_guard.drain(..).collect::<Vec<_>>();
            let token_holders = holder_guard.drain().map(|(_, holder)| holder).collect::<Vec<_>>();

            (pool_states, dex_trades, token_holders)
        };

        // Early return if no data
        if pool_states.is_empty() && dex_trades.is_empty() && token_holders.is_empty() {
            return Ok(());
        }

        // Spawn concurrent tasks for maximum performance
        let mut tasks = JoinSet::new();
        // Database save tasks
        tasks.spawn(Self::save_pool_states_task(pool_states.clone()));
        tasks.spawn(Self::save_dex_trades_task(dex_trades.clone()));
        tasks.spawn(Self::save_token_holders_task(token_holders.clone()));
        // Kafka publish task
        tasks.spawn(Self::publish_to_kafka_task(pool_states, dex_trades, token_holders));

        // Wait for all tasks and collect results
        let _results = tasks.join_all().await.into_iter().collect::<Result<Vec<_>, _>>()?;

        Ok(())
    }

    // Individual task implementations for better error handling
    async fn save_pool_states_task(pool_states: Vec<PoolState>) -> Result<()> {
        if pool_states.is_empty() {
            return Ok(());
        }

        let db = PostgresDatabase::get_indexer_db().await;

        let save_pool_states_start_time = std::time::Instant::now();
        db.insert_pool_states(&pool_states).await?;
        let save_pool_states_time = save_pool_states_start_time.elapsed().as_millis();

        tracing::debug!("Saved {} pool states in {}ms", pool_states.len(), save_pool_states_time);
        Ok(())
    }

    async fn save_dex_trades_task(dex_trades: Vec<DexTrade>) -> Result<()> {
        if dex_trades.is_empty() {
            return Ok(());
        }

        let db = PostgresDatabase::get_indexer_db().await;

        db.insert_dex_trades(&dex_trades).await?;
        Ok(())
    }

    async fn save_token_holders_task(token_holders: Vec<TokenHolder>) -> Result<()> {
        if token_holders.is_empty() {
            return Ok(());
        }

        let db = PostgresDatabase::get_indexer_db().await;

        db.insert_or_update_token_holders(&token_holders).await?;

        Ok(())
    }

    async fn publish_to_kafka_task(
        pool_states: Vec<PoolState>,
        dex_trades: Vec<DexTrade>,
        token_holders: Vec<TokenHolder>,
    ) -> Result<()> {
        let (chain, block_number) = if let Some(trade) = dex_trades.first() {
            (trade.chain, trade.block_number)
        } else if let Some(state) = pool_states.first() {
            (state.chain, state.block_number)
        } else if let Some(holder) = token_holders.first() {
            (holder.chain, holder.update_block_number)
        } else {
            return Ok(());
        };

        let indexer_block_msg = IndexerBlockMsg::new(
            chain,
            block_number,
            dex_trades,
            pool_states,
            token_holders.clone(),
        );
        let kafka_producer = KafkaProducer::get();
        kafka_producer.send::<IndexerBlockMsg>(&indexer_block_msg).await?;

        if !token_holders.is_empty() {
            let token_holders_msg = TokenHoldersMsg::new(token_holders);
            kafka_producer.send::<TokenHoldersMsg>(&token_holders_msg).await?;
        }

        Ok(())
    }
}
