use std::{env, str::FromStr, sync::OnceLock};

use alloy::primitives::Address;
use dotenv::dotenv;
use solana_sdk::pubkey::Pubkey;
use superstack_data::config::Config as DataConfig;

pub struct Config {
    pub port: u16,

    // Solana Config
    pub solana_from_slot: Option<u64>,
    pub solana_block_limit: Option<usize>,
    pub solana_task_concurrency: Option<usize>,
    pub pumpfun_program_id: Option<Pubkey>,
    pub pumpswap_program_id: Option<Pubkey>,
    pub meteora_dlmm_program_id: Option<Pubkey>,

    // Hypercore Config
    pub hypercore_base_url: String,
    pub hypercore_ws_url: String,
    pub hypercore_is_testnet: bool,

    // Hyperevm Config
    pub hyperevm_rpc_url: String,
    pub hyperevm_backup_rpc_url: String,
    pub hyperswapv3_factory_contract: Address,
    pub hyperevm_from_block: Option<u64>,
    pub hyperevm_block_limit: Option<usize>,
    pub hyperevm_init_step: Option<usize>,

    pub enable_meteora_indexer: bool,
    pub enable_pumpswap_indexer: bool,
    pub enable_pumpfun_indexer: bool,

    pub cmc_api_key: String,

    pub data_config: &'static DataConfig,
}

impl Config {
    pub fn get() -> &'static Config {
        static INSTANCE: OnceLock<Config> = OnceLock::new();

        INSTANCE.get_or_init(|| {
            dotenv().ok();

            let port = env::var("PORT").unwrap_or_else(|_| "3000".to_string());

            let pumpfun_program_id = match env::var("PUMPFUN_PROGRAM_ID") {
                Ok(s) => Pubkey::from_str(&s).ok(),
                Err(_) => None,
            };
            let pumpswap_program_id = match env::var("PUMPFUN_PROGRAM_ID") {
                Ok(s) => Pubkey::from_str(&s).ok(),
                Err(_) => None,
            };
            let meteora_dlmm_program_id = match env::var("METEORA_DLMM_PROGRAM_ID") {
                Ok(s) => Pubkey::from_str(&s).ok(),
                Err(_) => None,
            };

            let solana_from_slot = match env::var("SOLANA_FROM_SLOT") {
                Ok(s) => s.parse().ok(),
                Err(_) => None,
            };
            let solana_block_limit = match env::var("SOLANA_BLOCK_LIMIT") {
                Ok(s) => s.parse().ok(),
                Err(_) => None,
            };
            let solana_task_concurrency = match env::var("SOLANA_TASK_CONCURRENCY") {
                Ok(s) => s.parse().ok(),
                Err(_) => None,
            };

            // Hypercore Indexer Config
            let hypercore_is_testnet = match env::var("HYPERCORE_IS_TESTNET") {
                Ok(s) => s.parse().unwrap_or(false),
                Err(_) => false,
            };
            let hypercore_ws_url =
                env::var("HYPERCORE_WS_URL").unwrap_or(if hypercore_is_testnet {
                    crate::hypercore::types::TESTNET_WS_EXPLORER_URL.to_string()
                } else {
                    crate::hypercore::types::MAINNET_WS_EXPLORER_URL.to_string()
                });
            let hypercore_base_url =
                env::var("HYPERCORE_BASE_URL").unwrap_or(if hypercore_is_testnet {
                    crate::hypercore::types::TESTNET_BASE_URL.to_string()
                } else {
                    crate::hypercore::types::MAINNET_BASE_URL.to_string()
                });

            // Hyperevm Indexer Config
            let hyperevm_rpc_url =
                env::var("HYPEREVM_RPC_URL").expect("HYPEREV_RPC_URL is not set");

            let hyperevm_backup_rpc_url =
                env::var("HYPEREVM_BACKUP_RPC_URL").expect("HYPEREVM_BACKUP_RPC_URL is not set");

            let hyperswapv3_factory_contract =
                match env::var("HYPEREVM_HYPERSWAPV3_FACTORY_CONTRACT") {
                    Ok(s) => Address::from_str(&s).unwrap(),
                    Err(_) => {
                        Address::from_str("0xB1c0fa0B789320044A6F623cFe5eBda9562602E3").unwrap()
                    }
                };

            let hyperevm_from_block = match env::var("HYPEREVM_FROM_BLOCK") {
                Ok(s) => s.parse().ok(),
                Err(_) => None,
            };

            let hyperevm_block_limit = match env::var("HYPEREVM_BLOCK_LIMIT") {
                Ok(s) => s.parse().ok(),
                Err(_) => None,
            };

            let hyperevm_init_step = match env::var("HYPEREVM_INIT_STEP") {
                Ok(s) => s.parse().ok(),
                Err(_) => None,
            };

            let enable_meteora_indexer = match env::var("ENABLE_METEORA_INDEXER") {
                Ok(s) => s.parse().expect("ENABLE_METEORA_INDEXER must be a boolean"),
                Err(_) => true,
            };
            let enable_pumpswap_indexer = match env::var("ENABLE_PUMPSWAP_INDEXER") {
                Ok(s) => s.parse().expect("ENABLE_PUMPSWAP_INDEXER must be a boolean"),
                Err(_) => true,
            };
            let enable_pumpfun_indexer = match env::var("ENABLE_PUMPFUN_INDEXER") {
                Ok(s) => s.parse().expect("ENABLE_PUMPFUN_INDEXER must be a boolean"),
                Err(_) => true,
            };

            let cmc_api_key = env::var("CMC_API_KEY").expect("CMC_API_KEY is not set");

            tracing::info!("Enable Meteora Indexer: {}", enable_meteora_indexer);
            tracing::info!("Enable Pumpswap Indexer: {}", enable_pumpswap_indexer);
            tracing::info!("Enable Pumpfun Indexer: {}", enable_pumpfun_indexer);

            let data_config = DataConfig::get();

            Config {
                port: port.parse().unwrap(),
                pumpfun_program_id,
                pumpswap_program_id,
                meteora_dlmm_program_id,
                solana_from_slot,
                solana_block_limit,
                solana_task_concurrency,
                hypercore_base_url,
                hypercore_ws_url,
                hypercore_is_testnet,
                hyperevm_rpc_url,
                hyperevm_backup_rpc_url,
                hyperswapv3_factory_contract,
                hyperevm_from_block,
                hyperevm_block_limit,
                hyperevm_init_step,
                enable_meteora_indexer,
                enable_pumpswap_indexer,
                enable_pumpfun_indexer,
                cmc_api_key,
                data_config,
            }
        })
    }
}
