pub mod helper;
pub mod meteora_dlmm;
pub mod pumpfun;
pub mod pumpswap;

use std::{collections::HashMap, sync::Arc};

use solana_transaction_status_client_types::UiConfirmedBlock;
use superstack_data::{
    postgres::Chain, price::NativeTokenPriceManager, utils::get_confirmed_rpc_client,
};
use tokio::sync::{mpsc, Semaphore};

use self::pumpfun::Pumpfun;
use crate::{
    cache::{TaskWindow, UnsyncDatabaseBuf},
    config::Config,
    solana::dex::{meteora_dlmm::MeteoraDLMM, pumpswap::Pumpswap},
    store_task::StoreTaskMessage,
};

#[derive(Debug)]
pub struct SolanaDexConsumer {
    pumpfun: Pumpfun,
    pumpswap: Pumpswap,
    meteora_dlmm: MeteoraDLMM,
}

impl SolanaDexConsumer {
    const TASK_CONCURRENCY: usize = 500;

    pub fn init(
        channel_size: usize,
        store_channel_sender: mpsc::Sender<StoreTaskMessage>,
    ) -> anyhow::Result<mpsc::Sender<UiConfirmedBlock>> {
        let (block_tx, block_rx) = mpsc::channel(channel_size);
        let consumer = Self {
            pumpfun: Pumpfun::new(Config::get().pumpfun_program_id.unwrap_or(pumpfun::ID)),
            pumpswap: Pumpswap::new(Config::get().pumpswap_program_id.unwrap_or(pumpswap::ID)),
            meteora_dlmm: MeteoraDLMM::new(
                Config::get().meteora_dlmm_program_id.unwrap_or(meteora_dlmm::ID),
            ),
        };

        tracing::info!(
            "🔍 DexConsumer initialized with pumpfun: {:?}, pumpswap: {:?}, meteora_dlmm: {:?}",
            consumer.pumpfun.program_id,
            consumer.pumpswap.program_id,
            consumer.meteora_dlmm.program_id
        );

        tokio::spawn(consumer.run(block_rx, store_channel_sender));

        Ok(block_tx)
    }

    async fn run(
        self,
        mut block_rx: mpsc::Receiver<UiConfirmedBlock>,
        store_tx: mpsc::Sender<StoreTaskMessage>,
    ) -> anyhow::Result<()> {
        let config = Config::get();
        let task_concurrency = config.solana_task_concurrency.unwrap_or(Self::TASK_CONCURRENCY);
        let semaphore = Arc::new(Semaphore::new(task_concurrency));

        let task_window = TaskWindow::new(Chain::Solana);

        while let Some(block) = block_rx.recv().await {
            // Existing issue: the block_number we used here is the real block number - 1
            let block_number = block.parent_slot;
            let block_timestamp_seconds = match block.block_time {
                Some(timestamp) => timestamp,
                None => {
                    let client = get_confirmed_rpc_client();
                    client.get_block_time(block_number + 1).await?
                }
            };

            if let Some(txs) = block.transactions {
                let price_manager = NativeTokenPriceManager::get().await;
                let sol_price = price_manager
                    .get_nearest_price(Chain::Solana, block_timestamp_seconds)
                    .await
                    .ok_or(anyhow::anyhow!("Failed to get solana price"))?;

                let mut pumpfun_events = HashMap::new();
                let mut pumpswap_events = HashMap::new();
                let mut meteora_dlmm_events = HashMap::new();
                for (tx_idx, tx) in txs.into_iter().enumerate() {
                    if let (Some(meta), Some(decoded)) = (&tx.meta, tx.transaction.decode()) {
                        if meta.err.is_some() {
                            continue;
                        }
                        let tx_hash = decoded.signatures[0];
                        let tx_idx = tx_idx as u32;

                        if config.enable_pumpfun_indexer {
                            let events = self.pumpfun.parse(&meta)?;
                            for event in events {
                                pumpfun_events
                                    .entry(event.1.key())
                                    .or_insert(vec![])
                                    .push((event, tx_hash, tx_idx));
                            }
                        }

                        if config.enable_pumpswap_indexer {
                            let events = self.pumpswap.parse(&meta)?;
                            for event in events {
                                pumpswap_events
                                    .entry(event.1.key())
                                    .or_insert(vec![])
                                    .push((event, tx_hash, tx_idx));
                            }
                        }

                        if config.enable_meteora_indexer {
                            let events = self.meteora_dlmm.parse(&meta)?;
                            for event in events {
                                meteora_dlmm_events
                                    .entry(event.1.key())
                                    .or_insert(vec![])
                                    .push((event, tx_hash, tx_idx));
                            }
                        }
                    }
                }

                for (_, events) in meteora_dlmm_events.into_iter() {
                    let permit = semaphore.clone().acquire_owned().await?;
                    let task_window_clone = task_window.clone();
                    task_window_clone.start_task(block_number).await;

                    let store_tx_clone = store_tx.clone();

                    tokio::spawn(async move {
                        let mut database_buf = UnsyncDatabaseBuf::new();
                        for (event, tx_hash, tx_idx) in events {
                            if let Err(e) = self
                                .meteora_dlmm
                                .process_event(
                                    &event,
                                    &tx_hash,
                                    tx_idx,
                                    block_number,
                                    sol_price,
                                    block_timestamp_seconds,
                                    &mut database_buf,
                                )
                                .await
                            {
                                let err_msg = e.to_string();
                                if err_msg.contains("Unsupported token pair") ||
                                    err_msg.contains("Unsupport token2022 metadata")
                                {
                                    tracing::debug!(
                                        "Skipping unsupported meteora_dlmm event: {:?}, tx: {:?}, event: {:?}",
                                        e,
                                        tx_hash,
                                        event
                                    );
                                } else {
                                    tracing::error!(
                                        "Error processing meteora_dlmm event: {:?}, tx: {:?}, event: {:?}",
                                        e,
                                        tx_hash,
                                        event
                                    );
                                }
                            }
                        }

                        database_buf.save_and_publish_trades_and_pool_states(store_tx_clone).await;

                        // Drop the permit to release the semaphore
                        drop(permit);

                        if let Err(e) = task_window_clone.end_task(block_number).await {
                            tracing::error!(
                                "Error ending task for meteora_dlmm block {}: {:?}",
                                block_number,
                                e
                            );
                        }
                    });
                }

                for (_, events) in pumpfun_events.into_iter() {
                    let permit = semaphore.clone().acquire_owned().await?;
                    let task_window_clone = task_window.clone();
                    task_window_clone.start_task(block_number).await;

                    let store_tx_clone = store_tx.clone();

                    tokio::spawn(async move {
                        let mut database_buf = UnsyncDatabaseBuf::new();
                        for (event, tx_hash, tx_idx) in events {
                            if let Err(e) = self
                                .pumpfun
                                .process_event(
                                    &event,
                                    &tx_hash,
                                    tx_idx,
                                    block_number,
                                    sol_price,
                                    &mut database_buf,
                                )
                                .await
                            {
                                tracing::error!(
                                    "Error processing pumpfun event: {:?}, tx: {:?}, event: {:?}",
                                    e,
                                    tx_hash,
                                    event
                                );
                            }
                        }

                        database_buf.save_and_publish_trades_and_pool_states(store_tx_clone).await;

                        // Drop the permit to release the semaphore
                        drop(permit);

                        if let Err(e) = task_window_clone.end_task(block_number).await {
                            tracing::error!(
                                "Error ending task for pumpfun block {}: {:?}",
                                block_number,
                                e
                            );
                        }
                    });
                }

                for (_, events) in pumpswap_events.into_iter() {
                    let permit = semaphore.clone().acquire_owned().await?;
                    let task_window_clone = task_window.clone();
                    task_window_clone.start_task(block_number).await;

                    let store_tx_clone = store_tx.clone();

                    tokio::spawn(async move {
                        let mut database_buf = UnsyncDatabaseBuf::new();
                        for (event, tx_hash, tx_idx) in events {
                            if let Err(e) = self
                                .pumpswap
                                .process_event(
                                    &event,
                                    &tx_hash,
                                    tx_idx,
                                    block_number,
                                    sol_price,
                                    &mut database_buf,
                                )
                                .await
                            {
                                let err_msg = e.to_string();
                                if err_msg.contains("Unsupported token pair") ||
                                    err_msg.contains("Unsupport token2022 metadata")
                                {
                                    tracing::debug!(
                                        "Skipping unsupported pumpswap event: {:?}, tx: {:?}, event: {:?}",
                                        e,
                                        tx_hash,
                                        event
                                    );
                                } else {
                                    tracing::error!(
                                        "Error processing pumpswap event: {:?}, tx: {:?}, event: {:?}",
                                        e,
                                        tx_hash,
                                        event
                                    );
                                }
                            }
                        }

                        database_buf.save_and_publish_trades_and_pool_states(store_tx_clone).await;

                        // Drop the permit to release the semaphore
                        drop(permit);

                        if let Err(e) = task_window_clone.end_task(block_number).await {
                            tracing::error!(
                                "Error ending task for pumpswap block {}: {:?}",
                                block_number,
                                e
                            );
                        }
                    });
                }

                tracing::info!("🕸️ Solana DexConsumer processing block {}", block_number);
            }
        }

        tracing::error!("DexConsumer exited");
        std::process::exit(1);
    }
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use solana_sdk::signature::Signature;

    use crate::solana::utils::{get_block_config, get_transaction_config};

    use super::*;

    #[tokio::test]
    async fn test_replay_transaction() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let client = get_confirmed_rpc_client();
        let tx_sig = Signature::from_str("2ihndc8a5hVU9fwUBDgigQSPvLNcSkVZTPqeYSRSBwnBLeMLsETYXrwyhDG4EMzMNrmkp9wQgfkzwfJXoWSejboH").unwrap();
        let slot = 340984508;
        let tx_idx = 0;
        let sol_price = 150.0;
        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();
        let tx_meta = tx.transaction.meta.unwrap();

        let pumpswap = Pumpswap::new(pumpswap::ID);
        let events = pumpswap.parse(&tx_meta).unwrap();
        tracing::info!("events: {:#?}", events);
        for event in events {
            pumpswap
                .process_event(
                    &event,
                    &tx_sig,
                    slot - 1,
                    tx_idx,
                    sol_price,
                    &mut UnsyncDatabaseBuf::new(),
                )
                .await
                .unwrap();
        }
    }

    #[tokio::test]
    async fn test_replay_block() -> anyhow::Result<()> {
        dotenv::dotenv().ok();
        superstack_data::utils::setup_tracing();

        let block_number = 350780907;
        let client = get_confirmed_rpc_client();
        let block = client.get_block_with_config(block_number, get_block_config()).await.unwrap();
        let block_timestamp_seconds = block.block_time.unwrap();
        let block_number = block.parent_slot;
        tracing::info!("replay block: {}", block_number);

        if let Some(txs) = block.transactions {
            let price_manager = NativeTokenPriceManager::get().await;
            let sol_price = price_manager
                .get_nearest_price(Chain::Solana, block_timestamp_seconds)
                .await
                .ok_or(anyhow::anyhow!("Failed to get solana price"))
                .unwrap();

            let mut pumpfun_events = HashMap::new();
            let mut pumpswap_events = HashMap::new();
            let mut meteora_dlmm_events = HashMap::new();
            let pumpfun = Pumpfun::new(pumpfun::ID);
            let pumpswap = Pumpswap::new(pumpswap::ID);
            let meteora_dlmm = MeteoraDLMM::new(meteora_dlmm::ID);

            for (tx_idx, tx) in txs.into_iter().enumerate() {
                if let (Some(meta), Some(decoded)) = (&tx.meta, tx.transaction.decode()) {
                    if meta.err.is_some() {
                        continue;
                    }
                    let tx_hash = decoded.signatures[0];
                    let tx_idx = tx_idx as u32;

                    let events = pumpfun.parse(&meta).unwrap();
                    for event in events {
                        pumpfun_events
                            .entry(event.1.key())
                            .or_insert(vec![])
                            .push((event, tx_hash, tx_idx));
                    }

                    let events = pumpswap.parse(&meta).unwrap();
                    for event in events {
                        pumpswap_events
                            .entry(event.1.key())
                            .or_insert(vec![])
                            .push((event, tx_hash, tx_idx));
                    }

                    let events = meteora_dlmm.parse(&meta).unwrap();
                    for event in events {
                        meteora_dlmm_events
                            .entry(event.1.key())
                            .or_insert(vec![])
                            .push((event, tx_hash, tx_idx));
                    }
                }
            }

            tracing::info!("pumpswap_events: {:#?}", pumpswap_events.len());
            tracing::info!("pumpfun_events: {:#?}", pumpfun_events.len());
            tracing::info!("meteora_dlmm_events: {:#?}", meteora_dlmm_events.len());

            for (_, events) in pumpswap_events.into_iter() {
                let mut database_buf = UnsyncDatabaseBuf::new();
                for (event, tx_hash, tx_idx) in events {
                    if let Err(e) = pumpswap
                        .process_event(
                            &event,
                            &tx_hash,
                            tx_idx,
                            block_number,
                            sol_price,
                            &mut database_buf,
                        )
                        .await
                    {
                        tracing::error!(
                            "Error processing pumpswap event: {:?}, tx: {:?}, event: {:?}",
                            e,
                            tx_hash,
                            event
                        );
                    }
                }

                database_buf.save_pool_states_for_test().await.unwrap();
            }
        }

        Ok(())
    }
}

// #[cfg(test)]
// mod tests {
//     use std::str::FromStr;

//     use crate::{
//         dex::{
//             meteora_dlmm::{self, MeteoraDLMM},
//             pumpfun::{self, Pumpfun},
//             pumpswap::{self, Pumpswap},
//         },
//         utils::{get_block_config, get_transaction_config},
//     };
//     use solana_sdk::{pubkey::Pubkey, signature::Signature};
//     use superstack_data::{token::metadata::fetch_token_metadata,
// utils::get_confirmed_rpc_client};

//     #[tokio::test]
//     async fn test_parse_pumpswap_create() {
//         dotenv::dotenv().ok();
//         superstack_data::utils::setup_tracing();

//         let tx_sig =
// Signature::from_str("
// 4fVJxsHCMpsS2o2kTeLT9yJag79cASqDEb2wA4XYuhqHPPFjRxsK76XsKTanGT6N7kpqacdBmMsLc8wr9YGauHBx").
// unwrap();         let tx_sig =
// Signature::from_str("
// 2fUkLXSULPpV4M2fbkGorLvjFU75t29U8DwJiQdqsjPHP6cU3s53xby98Au43CjAASHT4cYius259B1prPYpdfto").
// unwrap();         let client = get_confirmed_rpc_client();
//         let tx =
//             client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();
//         let tx_meta = tx.transaction.meta.unwrap();

//         let pumpswap = Pumpswap::new(pumpswap::ID);
//         let pumpfun = Pumpfun::new(pumpfun::ID);
//         let meteora_dlmm = MeteoraDLMM::new(meteora_dlmm::ID);

//         let events = pumpswap.parse(&tx_meta).unwrap();
//         let pumpfun_events = pumpfun.parse(&tx_meta).unwrap();
//         let meteora_dlmm_events = meteora_dlmm.parse(&tx_meta).unwrap();
//         println!("{:#?}", events);
//         println!("{:#?}", pumpfun_events);
//         println!("{:#?}", meteora_dlmm_events);

//         for event in events {
//             pumpswap.process_event(&event, &tx_sig, 1).await.unwrap();
//         }

//         for event in pumpfun_events {
//             pumpfun.process_event(&event, &tx_sig, 1).await.unwrap();
//         }

//         for event in meteora_dlmm_events {
//             meteora_dlmm.process_event(&event, &tx_sig, 340984508).await.unwrap();
//         }
//     }

//     #[tokio::test]
//     async fn test_create_after_create() {
//         let client = get_confirmed_rpc_client();
//         let token_mint =
// Pubkey::from_str("67cP6ME5ia4sYd3fzAciohtHZhEG8RqTmBfu6r2Upump").unwrap();         // let
// base_mint = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();         let
// token_metadata = fetch_token_metadata(token_mint, client).await.unwrap();         println!("{:#?
// }", token_metadata);

//         let pumpswap = Pumpswap::new(pumpswap::ID);

//         // let curve_account = Pumpfun::get_bonding_curve_address(&token_mint, &pumpfun::ID);

//         let tx_sig =
// Signature::from_str("
// 3qbhqpmHmBQqdzv7x4CEPAJf43kkXLL8DovA4jxHZrUHhE9uz5pnsj7PAWG1fLfu41GDmMAhMSnguu3FSfXVAbiJ").
// unwrap();

//         let tx =
//             client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();
//         let tx_meta = tx.transaction.meta.unwrap();

//         let events = pumpswap.parse(&tx_meta).unwrap();
//         println!("{:#?}", events);

//         for event in events {
//             pumpswap.process_event(&event, &tx_sig, 1).await.unwrap();
//         }
//     }

//     #[tokio::test]
//     async fn test_pumpamm_token() {
//         std::env::set_var(
//             "DATABASE_URL",
//             "postgresql://postgres:postgres@localhost:5432/superstack_data",
//         );
//         tracing_subscriber::fmt::init();
//         let client = get_confirmed_rpc_client();
//         let pumpswap = Pumpswap::new(pumpswap::ID);

//         let tx_sig =
// Signature::from_str("
// 3UQqo5UX4guDntkmFYQ1d4o4VYQn4BPY3efq5cpnjNvdi6pE4HitvGxdVY7xfTBKeX4Yj3bUW7szWBagiSVQnXAG").
// unwrap();

//         let tx =
//             client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();
//         let tx_meta = tx.transaction.meta.unwrap();

//         let events = pumpswap.parse(&tx_meta).unwrap();
//         println!("{:#?}", events);

//         for event in events {
//             pumpswap.process_event(&event, &tx_sig, 1).await.unwrap();
//         }
//     }

//     #[tokio::test]
//     async fn test_recaptured_same_trade_event() {
//         let client = get_confirmed_rpc_client();

//         let slot = 336361729;
//         let block = client.get_block_with_config(slot, get_block_config()).await.unwrap();

//         let txs = block.transactions.unwrap();
//         let pumpfun = Pumpfun::new(pumpfun::ID);
//         for tx in txs {
//             let decoded = tx.transaction.decode().unwrap();
//             let events = pumpfun.parse(&tx.meta.unwrap()).unwrap();
//             if !events.is_empty() {
//                 dbg!(decoded.signatures[0]);
//             }
//         }
//     }
// }
