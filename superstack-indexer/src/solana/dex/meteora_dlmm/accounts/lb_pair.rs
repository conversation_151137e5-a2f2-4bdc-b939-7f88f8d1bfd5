use crate::solana::dex::meteora_dlmm::helper::{
    pow, price_per_lamport_to_price_per_token, q64x64_price_to_decimal, BASIS_POINT_MAX, ONE,
    SCALE_OFFSET,
};
use anyhow::Context;
use backon::{ExponentialBuilder, Retryable};
use borsh::{BorshDeserialize, BorshSerialize};
use rust_decimal::prelude::ToPrimitive;
use solana_sdk::{program_pack::Pack, pubkey::Pubkey};
use spl_token::state::Account as TokenAccount;
use superstack_data::utils::{get_confirmed_rpc_client, to_ui_amount};

pub const LB_PAIR_ACCOUNT_DISCM: [u8; 8] = [33, 11, 49, 98, 181, 101, 177, 13];

pub const MAX_RETRY_TIMES: usize = 1;

#[repr(C)]
#[derive(<PERSON><PERSON>, Debug, <PERSON>rshDeserialize, Bo<PERSON>hSerialize, PartialEq, Copy)]
pub struct LbPair {
    pub parameters: StaticParameters,
    pub v_parameters: VariableParameters,
    pub bump_seed: [u8; 1],
    pub bin_step_seed: [u8; 2],
    pub pair_type: u8,
    pub active_id: i32,
    pub bin_step: u16,
    pub status: u8,
    pub require_base_factor_seed: u8,
    pub base_factor_seed: [u8; 2],
    pub activation_type: u8,
    pub creator_pool_on_off_control: u8,
    pub token_x_mint: Pubkey,
    pub token_y_mint: Pubkey,
    pub reserve_x: Pubkey,
    pub reserve_y: Pubkey,
    pub protocol_fee: ProtocolFee,
    pub padding1: [u8; 32],
    pub reward_infos: [RewardInfo; 2],
    pub oracle: Pubkey,
    pub bin_array_bitmap: [u64; 16],
    pub last_updated_at: i64,
    pub padding2: [u8; 32],
    pub pre_activation_swap_address: Pubkey,
    pub base_key: Pubkey,
    pub activation_point: u64,
    pub pre_activation_duration: u64,
    pub padding3: [u8; 8],
    pub padding4: u64,
    pub creator: Pubkey,
    pub token_mint_x_program_flag: u8,
    pub token_mint_y_program_flag: u8,
    pub reserved: [u8; 22],
}

impl LbPair {
    pub async fn fetch(pubkey: &Pubkey) -> anyhow::Result<Self> {
        let fetch = || async {
            let client = get_confirmed_rpc_client();
            let account = client.get_account(pubkey).await?;
            let lb_pair = LbPair::try_from_slice(account.data.as_ref())?;
            Ok::<_, anyhow::Error>(lb_pair)
        };

        let lb_pair =
            fetch.retry(ExponentialBuilder::default().with_max_times(MAX_RETRY_TIMES)).await?;
        Ok(lb_pair)
    }

    pub async fn fetch_two_token_accounts_balances(
        pubkey1: Pubkey,
        pubkey2: Pubkey,
    ) -> anyhow::Result<(u64, u64)> {
        let balances = LbPair::fetch_token_accounts_balances(&[pubkey1, pubkey2]).await?;
        if balances.len() != 2 {
            return Err(anyhow::anyhow!(
                "token accounts length mismatch for {:?}",
                [pubkey1, pubkey2]
            ));
        }
        Ok((balances[0], balances[1]))
    }

    pub async fn fetch_token_accounts_balances(pubkeys: &[Pubkey]) -> anyhow::Result<Vec<u64>> {
        if pubkeys.is_empty() {
            return Ok(vec![]);
        }

        let fetch = || async {
            let client = get_confirmed_rpc_client();
            let len = pubkeys.len();
            let data = client.get_multiple_accounts(pubkeys).await?;
            if data.len() != len {
                return Err(anyhow::anyhow!("Invalid token account data for {:?}", pubkeys));
            }
            let mut balances = Vec::with_capacity(len);
            for data in data {
                if let Some(data) = data {
                    let balance = TokenAccount::unpack(&data.data[0..165])?.amount;
                    balances.push(balance);
                } else {
                    return Err(anyhow::anyhow!("The token account is not found for {:?}", pubkeys));
                }
            }
            Ok::<_, anyhow::Error>(balances)
        };

        let balances =
            fetch.retry(ExponentialBuilder::default().with_max_times(MAX_RETRY_TIMES)).await?;
        Ok(balances)
    }

    pub async fn fetch_token_account_balance(pubkey: &Pubkey) -> anyhow::Result<u64> {
        let fetch = || async {
            let client = get_confirmed_rpc_client();
            let data = client.get_account_data(pubkey).await?;
            let balance = TokenAccount::unpack(&data[0..165])?.amount;
            Ok::<_, anyhow::Error>(balance)
        };

        let balance =
            fetch.retry(ExponentialBuilder::default().with_max_times(MAX_RETRY_TIMES)).await?;
        Ok(balance)
    }

    pub fn try_from_slice(buf: &[u8]) -> std::io::Result<Self> {
        use std::io::Read;
        let mut reader = buf;
        let mut maybe_discm = [0u8; 8];
        reader.read_exact(&mut maybe_discm)?;
        if maybe_discm != LB_PAIR_ACCOUNT_DISCM {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!(
                    "discm does not match. Expected: {:?}. Received: {:?}",
                    LB_PAIR_ACCOUNT_DISCM, maybe_discm
                ),
            ));
        }
        Self::deserialize(&mut reader)
    }

    pub fn write_to_slice<W: std::io::Write>(&self, mut writer: W) -> std::io::Result<()> {
        writer.write_all(&LB_PAIR_ACCOUNT_DISCM)?;
        self.serialize(&mut writer)
    }

    pub fn try_to_vec(&self) -> std::io::Result<Vec<u8>> {
        let mut data = Vec::new();
        self.write_to_slice(&mut data)?;
        Ok(data)
    }

    pub fn get_price_from_id(active_id: i32, bin_step: u16) -> anyhow::Result<u128> {
        let bps = u128::from(bin_step)
            .checked_shl(SCALE_OFFSET.into())
            .context("overflow")?
            .checked_div(BASIS_POINT_MAX as u128)
            .context("overflow")?;

        let base = ONE.checked_add(bps).context("overflow")?;

        pow(base, active_id).context("overflow")
    }

    pub fn get_price_per_token(
        active_id: i32,
        bin_step: u16,
        base_token_decimal: u8,
        quote_token_decimal: u8,
        zero_or_one: bool,
    ) -> Option<f64> {
        let price = LbPair::get_price_from_id(active_id, bin_step).ok()?;
        let price_per_token = q64x64_price_to_decimal(price)?;
        let price_per_token = price_per_lamport_to_price_per_token(
            price_per_token.to_f64()?,
            base_token_decimal,
            quote_token_decimal,
        )?;
        if zero_or_one {
            Some(price_per_token.to_f64()?)
        } else {
            Some(1.0 / price_per_token.to_f64()?)
        }
    }

    pub fn get_market_cap(
        supply: u64,
        active_id: i32,
        bin_step: u16,
        base_token_decimal: u8,
        quote_token_decimal: u8,
        zero_or_one: bool,
    ) -> Option<f64> {
        let price_per_token = LbPair::get_price_per_token(
            active_id,
            bin_step,
            base_token_decimal,
            quote_token_decimal,
            zero_or_one,
        )?;

        if zero_or_one {
            Some(to_ui_amount(supply, base_token_decimal) * price_per_token)
        } else {
            Some(to_ui_amount(supply, quote_token_decimal) * price_per_token)
        }
    }

    pub fn get_total_liquidity(
        base_token_reserves: u64,
        quote_token_reserves: u64,
        base_token_decimal: u8,
        quote_token_decimal: u8,
        active_id: i32,
        bin_step: u16,
        zero_or_one: bool,
    ) -> Option<f64> {
        let price_per_token = LbPair::get_price_per_token(
            active_id,
            bin_step,
            base_token_decimal,
            quote_token_decimal,
            zero_or_one,
        )?;

        let base_ui_amount = to_ui_amount(base_token_reserves, base_token_decimal);
        let quote_ui_amount = to_ui_amount(quote_token_reserves, quote_token_decimal);

        if zero_or_one {
            Some(base_ui_amount * price_per_token + quote_ui_amount)
        } else {
            Some(quote_ui_amount * price_per_token + base_ui_amount)
        }
    }
}

#[repr(C)]
#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq, Copy)]
pub struct StaticParameters {
    pub base_factor: u16,
    pub filter_period: u16,
    pub decay_period: u16,
    pub reduction_factor: u16,
    pub variable_fee_control: u32,
    pub max_volatility_accumulator: u32,
    pub min_bin_id: i32,
    pub max_bin_id: i32,
    pub protocol_share: u16,
    pub base_fee_power_factor: u8,
    pub padding: [u8; 5],
}

#[repr(C)]
#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq, Copy)]
pub struct VariableParameters {
    pub volatility_accumulator: u32,
    pub volatility_reference: u32,
    pub index_reference: i32,
    pub padding: [u8; 4],
    pub last_update_timestamp: i64,
    pub padding1: [u8; 8],
}

#[repr(C)]
#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq, Copy)]
pub struct ProtocolFee {
    pub amount_x: u64,
    pub amount_y: u64,
}

#[repr(C)]
#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq, Copy)]
pub struct RewardInfo {
    pub mint: Pubkey,
    pub vault: Pubkey,
    pub funder: Pubkey,
    pub reward_duration: u64,
    pub reward_duration_end: u64,
    pub reward_rate: u128,
    pub last_update_time: u64,
    pub cumulative_seconds_with_empty_liquidity_reward: u64,
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use crate::solana::dex::meteora_dlmm::{
        accounts::lb_pair::LbPair,
        helper::{price_per_lamport_to_price_per_token, q64x64_price_to_decimal},
    };
    use backon::{ExponentialBuilder, Retryable};
    use rust_decimal::prelude::ToPrimitive;
    use solana_sdk::{program_pack::Pack, pubkey::Pubkey};
    use spl_token_2022::extension::{
        metadata_pointer::MetadataPointer, BaseStateWithExtensions, StateWithExtensions,
    };
    use superstack_data::utils::get_confirmed_rpc_client;

    #[tokio::test]
    async fn test_lb_pair() {
        dotenv::dotenv().ok();
        let account = "AjM8Qn62EhR4ikJ1rvyeezB1NyvrSsb4zwJiFUFs9ycs";
        // let account = "7GhJZtsuCBpYdJgTQ4ACBCLPgqXBjan5xY5MGmwK4vRr";
        let client = get_confirmed_rpc_client();

        let account = client.get_account(&Pubkey::from_str(account).unwrap()).await.unwrap();

        let lb_pair = LbPair::try_from_slice(account.data.as_ref()).unwrap();
        println!("{:#?}", lb_pair);
        // println!("{:#?}", lb_pair);

        let price = LbPair::get_price_from_id(dbg!(lb_pair.active_id), lb_pair.bin_step).unwrap();
        // println!("{:?}", price);

        let price_decimal = q64x64_price_to_decimal(price).unwrap();
        // println!("{:?}", price_decimal);

        let price_per_token =
            price_per_lamport_to_price_per_token(price_decimal.to_f64().unwrap(), 6, 9).unwrap();
        println!("{:?}", price_per_token.to_f64().unwrap());

        let base_token_reserves =
            LbPair::fetch_token_account_balance(&lb_pair.reserve_x).await.unwrap();
        let quote_token_reserves =
            LbPair::fetch_token_account_balance(&lb_pair.reserve_y).await.unwrap();
        println!("{:?}", base_token_reserves);
        println!("{:?}", quote_token_reserves);

        let total_liquidity = LbPair::get_total_liquidity(
            base_token_reserves,
            quote_token_reserves,
            6,
            9,
            lb_pair.active_id,
            lb_pair.bin_step,
            true,
        )
        .unwrap();

        println!("{:?}", total_liquidity);

        let x_mint = client.get_account(&lb_pair.token_x_mint).await.unwrap();
        let y_mint = client.get_account(&lb_pair.token_y_mint).await.unwrap();
        println!("{:#?}", x_mint);
        println!("{:#?}", y_mint);
    }

    #[tokio::test]
    async fn test_token_account() {
        dotenv::dotenv().ok();

        let pubkey = "DfWWLJvVHDM9byp6y7Rpw5Rx4mGizSwB5GEoUMegi3z8";
        let client = get_confirmed_rpc_client();
        let account_data =
            client.get_account_data(&Pubkey::from_str(pubkey).unwrap()).await.unwrap();
        let token_account =
            spl_token_2022::state::Account::unpack_unchecked(&account_data[0..165]).unwrap();
        println!("{:#?}", token_account.amount / 10u64.pow(9));

        let mint_pubkey = Pubkey::from_str("Ey59PH7Z4BFU4HjyKnyMdWt5GGN76KazTAwQihoUXRnk").unwrap();
        let mint = client.get_account(&mint_pubkey).await.unwrap();
        println!("{:#?}", mint);

        let state = StateWithExtensions::<spl_token_2022::state::Mint>::unpack(&mint.data).unwrap();
        println!("{:#?}", state);
        let pointer = state.get_extension::<MetadataPointer>().unwrap();
        println!("{:#?}", pointer);

        let _metadata_account = client.get_account(&pointer.metadata_address.0).await.unwrap();
        // let metadata = fetch_token_metadata_from_extension(&mint_pubkey, &client).await.unwrap();
        // println!("{:#?}", metadata);
    }

    #[tokio::test]
    async fn test_get_block_time() {
        dotenv::dotenv().ok();
        let client = get_confirmed_rpc_client();
        let block_time = client.get_block_time(*********).await.unwrap();
        println!("{:#?}", block_time);

        let block_time = client.get_block_time(*********).await.unwrap();
        println!("{:#?}", block_time);
    }

    #[tokio::test]
    async fn test_backon() {
        dotenv::dotenv().ok();

        let fetch = || async {
            println!("run once");
            Err(anyhow::anyhow!("test"))
        };

        let _: () = fetch.retry(ExponentialBuilder::default().with_max_times(1)).await.unwrap();
    }
}
