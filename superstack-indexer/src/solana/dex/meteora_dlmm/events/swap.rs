use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

pub const SWAP_EVENT_DISCM: [u8; 8] = [81, 108, 227, 190, 205, 208, 10, 196];

#[derive(<PERSON><PERSON>, Debug, <PERSON>ialEq, BorshDeserialize, BorshSerialize)]
pub struct Swap {
    pub lb_pair: Pubkey,
    pub from: Pubkey,
    pub start_bin_id: i32,
    pub end_bin_id: i32,
    pub amount_in: u64,
    pub amount_out: u64,
    pub swap_for_y: bool,
    pub fee: u64,
    pub protocol_fee: u64,
    pub fee_bps: u128,
    pub host_fee: u64,
}

#[derive(<PERSON><PERSON>, Debug, PartialEq)]
pub struct SwapEvent(pub Swap);

impl BorshSerialize for SwapEvent {
    fn serialize<W: std::io::Write>(&self, writer: &mut W) -> std::io::Result<()> {
        SWAP_EVENT_DISCM.serialize(writer)?;
        self.0.serialize(writer)
    }
}

impl SwapEvent {
    pub fn deserialize(buf: &mut &[u8]) -> std::io::Result<Self> {
        let maybe_discm = <[u8; 8]>::deserialize(buf)?;
        if maybe_discm != SWAP_EVENT_DISCM {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!(
                    "discm does not match. Expected: {:?}. Received: {:?}",
                    SWAP_EVENT_DISCM, maybe_discm
                ),
            ));
        }
        Ok(Self(Swap::deserialize(buf)?))
    }
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    use crate::solana::{
        dex::meteora_dlmm::{self, MeteoraDLMM},
        utils::get_transaction_config,
    };

    #[tokio::test]
    async fn test_swap_event() {
        let tx = "2hGT9PYKsTgL3Pmhtj1c4635fztEKKvZEfxpXnKMuVPbE6LzXGBos8NiPtafJFXuYDwNo1WP6NHvQPzRaR4EMwzu";
        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();
        println!("{:?}", tx);
        let meta = tx.transaction.meta.unwrap();
        let meteora_dlmm = MeteoraDLMM::new(meteora_dlmm::ID);

        let event = meteora_dlmm.parse(&meta).unwrap();

        println!("{:?}", event);
    }
}
