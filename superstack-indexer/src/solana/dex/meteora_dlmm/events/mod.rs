use add_liquidity::AddLiquidityEvent;
use lb_pair_create::LbPairCreateEvent;
use remove_liquidity::RemoveLiquidityEvent;
use solana_sdk::pubkey::Pubkey;
use swap::SwapEvent;

pub mod add_liquidity;
pub mod lb_pair_create;
pub mod remove_liquidity;
pub mod swap;

#[derive(<PERSON>bu<PERSON>, Clone)]
pub enum MeteoraDlmmEvent {
    LbPairCreate(LbPairCreateEvent),
    Swap(SwapEvent),
    AddLiquidity(AddLiquidityEvent),
    RemoveLiquidity(RemoveLiquidityEvent),
}

impl MeteoraDlmmEvent {
    pub fn from_data(data: &[u8]) -> anyhow::Result<Self> {
        if data.len() < 8 {
            return Err(anyhow::anyhow!("Data is too short"));
        }

        let mut data = &data[8..];
        match data {
            x if x.starts_with(&lb_pair_create::LB_PAIR_CREATE_EVENT_DISCM) => {
                Ok(Self::LbPairCreate(LbPairCreateEvent::deserialize(&mut data)?))
            }
            x if x.starts_with(&swap::SWAP_EVENT_DISCM) => {
                Ok(Self::Swap(SwapEvent::deserialize(&mut data)?))
            }
            x if x.starts_with(&add_liquidity::ADD_LIQUIDITY_EVENT_DISCM) => {
                Ok(Self::AddLiquidity(AddLiquidityEvent::deserialize(&mut data)?))
            }
            x if x.starts_with(&remove_liquidity::REMOVE_LIQUIDITY_EVENT_DISCM) => {
                Ok(Self::RemoveLiquidity(RemoveLiquidityEvent::deserialize(&mut data)?))
            }
            _ => Err(anyhow::anyhow!("Unknown event {:?}", data)),
        }
    }

    pub fn key(&self) -> Pubkey {
        match self {
            Self::LbPairCreate(lb_pair_create) => lb_pair_create.0.lb_pair,
            Self::Swap(swap) => swap.0.lb_pair,
            Self::AddLiquidity(add_liquidity) => add_liquidity.0.lb_pair,
            Self::RemoveLiquidity(remove_liquidity) => remove_liquidity.0.lb_pair,
        }
    }
}
