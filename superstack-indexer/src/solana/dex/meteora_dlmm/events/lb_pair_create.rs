use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

pub const LB_PAIR_CREATE_EVENT_DISCM: [u8; 8] = [185, 74, 252, 125, 27, 215, 188, 111];

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq, BorshDeserialize, BorshSerialize)]
pub struct LbPairCreate {
    pub lb_pair: Pubkey,
    pub bin_step: u16,
    pub token_x: Pubkey,
    pub token_y: Pubkey,
}

#[derive(Clone, Debug, PartialEq)]
pub struct LbPairCreateEvent(pub LbPairCreate);
impl BorshSerialize for LbPairCreateEvent {
    fn serialize<W: std::io::Write>(&self, writer: &mut W) -> std::io::Result<()> {
        LB_PAIR_CREATE_EVENT_DISCM.serialize(writer)?;
        self.0.serialize(writer)
    }
}

impl LbPairCreateEvent {
    pub fn deserialize(buf: &mut &[u8]) -> std::io::Result<Self> {
        let maybe_discm = <[u8; 8]>::deserialize(buf)?;
        if maybe_discm != LB_PAIR_CREATE_EVENT_DISCM {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!(
                    "discm does not match. Expected: {:?}. Received: {:?}",
                    LB_PAIR_CREATE_EVENT_DISCM, maybe_discm
                ),
            ));
        }
        Ok(Self(LbPairCreate::deserialize(buf)?))
    }
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    use crate::solana::{
        dex::meteora_dlmm::{self, MeteoraDLMM},
        utils::get_transaction_config,
    };

    #[tokio::test]
    async fn test_lb_pair_create_event() {
        let tx = "53MfUTy3HUPpuyEX8zCc6grKpw3QbQe8RYaBEruD5ZTBX52RYQN7wApsqNJ8q4DUjgWZXVq8sob1UpyRxjbvJJ2v";

        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();

        let meta = tx.transaction.meta.unwrap();
        let meteora_dlmm = MeteoraDLMM::new(meteora_dlmm::ID);

        let event = meteora_dlmm.parse(&meta).unwrap();
        println!("{:?}", event);
    }
}
