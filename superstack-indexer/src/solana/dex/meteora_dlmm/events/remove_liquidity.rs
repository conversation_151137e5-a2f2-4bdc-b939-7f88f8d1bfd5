use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

pub const REMOVE_LIQUIDITY_EVENT_DISCM: [u8; 8] = [116, 244, 97, 232, 103, 31, 152, 58];

#[derive(<PERSON><PERSON>, Debug, PartialEq, BorshDeserialize, BorshSerialize)]
pub struct RemoveLiquidity {
    pub lb_pair: Pubkey,
    pub from: Pubkey,
    pub position: Pubkey,
    pub amounts: [u64; 2],
    pub active_bin_id: i32,
}

#[derive(Clone, Debug, PartialEq)]
pub struct RemoveLiquidityEvent(pub RemoveLiquidity);
impl BorshSerialize for RemoveLiquidityEvent {
    fn serialize<W: std::io::Write>(&self, writer: &mut W) -> std::io::Result<()> {
        REMOVE_LIQUIDITY_EVENT_DISCM.serialize(writer)?;
        self.0.serialize(writer)
    }
}

impl RemoveLiquidityEvent {
    pub fn deserialize(buf: &mut &[u8]) -> std::io::Result<Self> {
        let maybe_discm = <[u8; 8]>::deserialize(buf)?;
        if maybe_discm != REMOVE_LIQUIDITY_EVENT_DISCM {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!(
                    "discm does not match. Expected: {:?}. Received: {:?}",
                    REMOVE_LIQUIDITY_EVENT_DISCM, maybe_discm
                ),
            ));
        }
        Ok(Self(RemoveLiquidity::deserialize(buf)?))
    }
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    use crate::solana::{
        dex::meteora_dlmm::{self, MeteoraDLMM},
        utils::get_transaction_config,
    };

    #[tokio::test]
    async fn test_remove_liquidity_event() {
        let tx = "3vtayQ6iZYqBQiEwMSs4VhCJ8xYT6HJfRZRuebn882ZhwqLhr4K5W9FG8dxMQLnVeudF8mQX8WDevCipzjEAwZnt";
        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();

        let meta = tx.transaction.meta.unwrap();
        let meteora_dlmm = MeteoraDLMM::new(meteora_dlmm::ID);

        let event = meteora_dlmm.parse(&meta).unwrap();
        println!("{:?}", event);
    }
}
