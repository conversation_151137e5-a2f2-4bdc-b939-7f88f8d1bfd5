use std::str::FromStr;

use accounts::lb_pair::LbPair;
use events::{
    add_liquidity::AddLiquidityEvent, lb_pair_create::LbPairCreateEvent,
    remove_liquidity::RemoveLiquidityEvent, swap::SwapEvent, MeteoraDlmmEvent,
};
use num_traits::ToPrimitive;
use solana_sdk::{bs58, declare_id, pubkey::Pubkey, signature::Signature};
use solana_transaction_status_client_types::{
    option_serializer::OptionSerializer, UiInstruction, UiTransactionStatusMeta,
};
use superstack_data::{
    postgres::{
        enums::{Chain, Dex, PoolType},
        indexer::{DexTrade, PoolMetadata, PoolStateDelta, TokenHolderDelta, TokenMetadata},
    },
    utils::to_ui_amount,
};

use crate::{
    cache::{ChainCache, UnsyncDatabaseBuf},
    solana::dex::helper::{
        align_token_pair, get_or_fetch_token_metadata, is_native_token, is_usd_token, zero_or_one,
    },
};

pub mod accounts;
pub mod events;
pub mod helper;

declare_id!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");

#[derive(Debug, Clone, Copy)]
pub struct MeteoraDLMM {
    pub program_id: Pubkey,
}

impl MeteoraDLMM {
    pub fn new(program_id: Pubkey) -> Self {
        Self { program_id }
    }

    pub fn parse(
        &self,
        tx_meta: &UiTransactionStatusMeta,
    ) -> anyhow::Result<Vec<(usize, MeteoraDlmmEvent)>> {
        let mut events = Vec::new();
        if let OptionSerializer::Some(ixs) = &tx_meta.inner_instructions {
            let ixs = ixs.iter().flat_map(|ix| ix.instructions.iter());
            for (ix_idx, ix) in ixs.enumerate() {
                if let UiInstruction::Compiled(ix) = ix {
                    let data = match bs58::decode(ix.data.clone()).into_vec() {
                        Ok(data) => data,
                        Err(_) => continue,
                    };

                    let event = match MeteoraDlmmEvent::from_data(&data) {
                        Ok(event) => event,
                        Err(_) => continue,
                    };

                    events.push((ix_idx, event));
                }
            }
        }

        Ok(events)
    }

    pub async fn get_or_fetch_metadatas(
        &self,
        pool: &Pubkey,
        create_timestamp: Option<i64>,
    ) -> anyhow::Result<(PoolMetadata, TokenMetadata, TokenMetadata, bool)> {
        let chain_cache = ChainCache::get_cache(Chain::Solana);
        let update_timestamp_millis = chrono::Utc::now().timestamp_millis();

        let pool_metadata =
            match chain_cache.get_pool_metadata_or_update_from_db(&pool.to_string()).await? {
                Some(pool_metadata) => pool_metadata,
                None => {
                    let create_timestamp_millis = create_timestamp.map(|ts| ts * 1000);
                    let lb_pair = LbPair::fetch(pool).await?;

                    let x_metadata_handle = tokio::spawn(async move {
                        get_or_fetch_token_metadata(
                            lb_pair.token_x_mint,
                            update_timestamp_millis,
                            Dex::Unknown,
                            None,
                        )
                        .await
                    });
                    let y_metadata_handle = tokio::spawn(async move {
                        get_or_fetch_token_metadata(
                            lb_pair.token_y_mint,
                            update_timestamp_millis,
                            Dex::Unknown,
                            None,
                        )
                        .await
                    });
                    let x_metadata = x_metadata_handle.await??;
                    let y_metadata = y_metadata_handle.await??;

                    let (token_metadata, base_metadata) = align_token_pair(
                        &lb_pair.token_x_mint,
                        &lb_pair.token_y_mint,
                        x_metadata.clone(),
                        y_metadata.clone(),
                    )?;
                    let is_token_first = zero_or_one(&lb_pair.token_x_mint, &lb_pair.token_y_mint);

                    let pool_metadata = PoolMetadata::new_solana_pool(
                        *pool,
                        format!("{}/{}", x_metadata.symbol, y_metadata.symbol),
                        Dex::Meteora,
                        PoolType::Dlmm,
                        create_timestamp_millis,
                        update_timestamp_millis,
                        Pubkey::from_str(&token_metadata.address)?,
                        token_metadata.decimals,
                        Pubkey::from_str(&base_metadata.address)?,
                        base_metadata.decimals,
                        is_token_first,
                        Some(lb_pair.bin_step),
                    );

                    let pool_metadata = chain_cache
                        .insert_pool_metadata_and_publish_if_not_exists(pool_metadata)
                        .await?;

                    return Ok((pool_metadata, token_metadata, base_metadata, is_token_first));
                }
            };

        let token_metadata = get_or_fetch_token_metadata(
            Pubkey::from_str(&pool_metadata.token_address)?,
            update_timestamp_millis,
            Dex::Unknown,
            None,
        )
        .await?;
        let base_metadata = get_or_fetch_token_metadata(
            Pubkey::from_str(&pool_metadata.base_address)?,
            update_timestamp_millis,
            Dex::Unknown,
            None,
        )
        .await?;
        let is_token_first = pool_metadata.is_token_first;

        Ok((pool_metadata, token_metadata, base_metadata, is_token_first))
    }

    pub async fn process_event(
        &self,
        event: &(usize, MeteoraDlmmEvent),
        tx_hash: &Signature,
        tx_idx: u32,
        block_number: u64,
        sol_usd_price: f64,
        block_timestamp_seconds: i64,
        unsync_database_buf: &mut UnsyncDatabaseBuf,
    ) -> anyhow::Result<()> {
        let chain_cache = ChainCache::get_cache(Chain::Solana);

        let (ix_idx, event) = event;

        match event {
            MeteoraDlmmEvent::LbPairCreate(LbPairCreateEvent(create)) => {
                tracing::info!("Meteora DLMM LbPairCreate: {:?}", create);
                let (mut pool_metadata, token_metadata, base_metadata, is_token_first) = self
                    .get_or_fetch_metadatas(&create.lb_pair, Some(block_timestamp_seconds))
                    .await?;

                let create_timestamp_millis = block_timestamp_seconds * 1000;
                if pool_metadata.create_timestamp_millis != create_timestamp_millis {
                    pool_metadata.create_timestamp_millis = create_timestamp_millis;
                    chain_cache
                        .update_pool_metadata_create_info_and_publish(pool_metadata.clone())
                        .await?;
                }

                let ((_, base_decimals), (_, quote_decimals)) = if is_token_first {
                    (
                        (&token_metadata.address, token_metadata.decimals),
                        (&base_metadata.address, base_metadata.decimals),
                    )
                } else {
                    (
                        (&base_metadata.address, base_metadata.decimals),
                        (&token_metadata.address, token_metadata.decimals),
                    )
                };

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;
                let lb_pair = LbPair::fetch(&create.lb_pair).await?;
                let (base_token_reserves, quote_token_reserves) =
                    LbPair::fetch_two_token_accounts_balances(lb_pair.reserve_x, lb_pair.reserve_y)
                        .await?;

                // PoolState
                let price = LbPair::get_price_per_token(
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    base_decimals,
                    quote_decimals,
                    is_token_first,
                )
                .unwrap_or_default();
                let market_cap = LbPair::get_market_cap(
                    token_metadata
                        .supply
                        .to_u64()
                        .ok_or(anyhow::anyhow!("Token supply is too large"))?,
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    base_decimals,
                    quote_decimals,
                    is_token_first,
                )
                .unwrap_or_default();
                let liquidity = LbPair::get_total_liquidity(
                    base_token_reserves,
                    quote_token_reserves,
                    base_decimals,
                    quote_decimals,
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    is_token_first,
                )
                .unwrap_or_default();
                let timestamp_millis = block_timestamp_seconds * 1000;

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 0,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };
                let pool_state = chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);
            }

            MeteoraDlmmEvent::Swap(SwapEvent(swap)) => {
                let (pool_metadata, token_metadata, base_metadata, is_token_x) =
                    self.get_or_fetch_metadatas(&swap.lb_pair, None).await?;

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;

                let ((_, x_decimals), (_, y_decimals)) = if is_token_x {
                    (
                        (Pubkey::from_str(&token_metadata.address)?, token_metadata.decimals),
                        (Pubkey::from_str(&base_metadata.address)?, base_metadata.decimals),
                    )
                } else {
                    (
                        (Pubkey::from_str(&base_metadata.address)?, base_metadata.decimals),
                        (Pubkey::from_str(&token_metadata.address)?, token_metadata.decimals),
                    )
                };

                let is_buy = match (swap.swap_for_y, is_token_x) {
                    (true, true) => false,   // Sell token
                    (true, false) => true,   // Buy token
                    (false, true) => true,   // Buy token
                    (false, false) => false, // Sell token
                };

                // Trade
                let timestamp_millis = block_timestamp_seconds * 1000;
                let trade = DexTrade::new_solana_dex_trade(
                    tx_hash.to_string(),
                    *ix_idx as u32,
                    pool_metadata.pool_address,
                    swap.from.to_string(),
                    is_buy,
                    if is_buy {
                        base_metadata.address.clone()
                    } else {
                        token_metadata.address.clone()
                    },
                    swap.amount_in,
                    if is_buy {
                        token_metadata.address.clone()
                    } else {
                        base_metadata.address.clone()
                    },
                    swap.amount_out,
                    block_number,
                    timestamp_millis,
                    tx_idx,
                );
                unsync_database_buf.insert_dex_trade(trade);

                // PoolState
                let lb_pair = LbPair::fetch(&swap.lb_pair).await?;
                let (x_reserves, y_reserves) =
                    LbPair::fetch_two_token_accounts_balances(lb_pair.reserve_x, lb_pair.reserve_y)
                        .await?;

                let bin_step = pool_metadata.bin_step.unwrap_or(lb_pair.bin_step);

                let price = LbPair::get_price_per_token(
                    swap.end_bin_id,
                    bin_step,
                    x_decimals,
                    y_decimals,
                    is_token_x,
                )
                .unwrap_or_default();
                let market_cap = LbPair::get_market_cap(
                    token_metadata
                        .supply
                        .to_u64()
                        .ok_or(anyhow::anyhow!("Token supply is too large"))?,
                    swap.end_bin_id,
                    bin_step,
                    x_decimals,
                    y_decimals,
                    is_token_x,
                )
                .unwrap_or_default();
                let liquidity = LbPair::get_total_liquidity(
                    x_reserves,
                    y_reserves,
                    x_decimals,
                    y_decimals,
                    swap.end_bin_id,
                    bin_step,
                    is_token_x,
                )
                .unwrap_or_default();

                let delta_volume = if is_buy {
                    to_ui_amount(swap.amount_in, base_metadata.decimals)
                } else {
                    to_ui_amount(swap.amount_out, base_metadata.decimals)
                };

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume,
                    delta_buy_volume: if is_buy { delta_volume } else { 0.0 },
                    delta_sell_volume: if is_buy { 0.0 } else { delta_volume },
                    delta_txns: 1,
                    delta_buy_txns: if is_buy { 1 } else { 0 },
                    delta_sell_txns: if is_buy { 0 } else { 1 },
                    bonding_curve_progress: None,
                };
                let pool_state = chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);

                // Token holder delta
                {
                    let mut token_holder_delta =
                        TokenHolderDelta::new(timestamp_millis, block_number);
                    if is_buy {
                        token_holder_delta.bought_amount = swap.amount_out.into();
                        token_holder_delta.bought_txns = 1;
                        let spent_token_ui_amount =
                            to_ui_amount(swap.amount_in, base_metadata.decimals);
                        if is_native_token(&base_metadata.address) {
                            token_holder_delta.spent_native_token_ui_amount = spent_token_ui_amount;
                        } else if is_usd_token(&base_metadata.address) {
                            token_holder_delta.spent_usd_token_ui_amount = spent_token_ui_amount;
                        } else {
                            tracing::warn!("Ignoring unsupported spent token: {:?}", swap);
                        }
                        token_holder_delta.total_spent_usd =
                            token_holder_delta.spent_native_token_ui_amount * sol_usd_price +
                                token_holder_delta.spent_usd_token_ui_amount;
                    } else {
                        token_holder_delta.sold_amount = swap.amount_in.into();
                        token_holder_delta.sold_txns = 1;
                        let received_token_ui_amount =
                            to_ui_amount(swap.amount_out, base_metadata.decimals);
                        if is_native_token(&base_metadata.address) {
                            token_holder_delta.received_native_token_ui_amount =
                                received_token_ui_amount;
                        } else if is_usd_token(&base_metadata.address) {
                            token_holder_delta.received_usd_token_ui_amount =
                                received_token_ui_amount;
                        } else {
                            tracing::warn!("Ignoring unsupported received token: {:?}", swap);
                        }
                        token_holder_delta.total_received_usd =
                            token_holder_delta.received_native_token_ui_amount * sol_usd_price +
                                token_holder_delta.received_usd_token_ui_amount;
                    }
                    let token_holder = chain_cache
                        .update_token_holder(
                            &token_metadata.address.to_string(),
                            &swap.from.to_string(),
                            token_holder_delta,
                        )
                        .await?;
                    unsync_database_buf.insert_token_holder(token_holder);
                }
            }
            MeteoraDlmmEvent::AddLiquidity(AddLiquidityEvent(add_liquidity)) => {
                let (pool_metadata, token_metadata, base_metadata, is_token_first) =
                    self.get_or_fetch_metadatas(&add_liquidity.lb_pair, None).await?;

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;
                let lb_pair = LbPair::fetch(&add_liquidity.lb_pair).await?;
                let (x_reserves, y_reserves) =
                    LbPair::fetch_two_token_accounts_balances(lb_pair.reserve_x, lb_pair.reserve_y)
                        .await?;

                let ((_, base_decimals), (_, quote_decimals)) = if is_token_first {
                    (
                        (&token_metadata.address, token_metadata.decimals),
                        (&base_metadata.address, base_metadata.decimals),
                    )
                } else {
                    (
                        (&base_metadata.address, base_metadata.decimals),
                        (&token_metadata.address, token_metadata.decimals),
                    )
                };

                // PoolState
                let timestamp_millis = block_timestamp_seconds * 1000;

                let liquidity = LbPair::get_total_liquidity(
                    x_reserves,
                    y_reserves,
                    base_decimals,
                    quote_decimals,
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    is_token_first,
                )
                .unwrap_or_default();
                let price = LbPair::get_price_per_token(
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    base_decimals,
                    quote_decimals,
                    is_token_first,
                )
                .unwrap_or_default();
                let market_cap = LbPair::get_market_cap(
                    token_metadata
                        .supply
                        .to_u64()
                        .ok_or(anyhow::anyhow!("Token supply is too large"))?,
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    base_decimals,
                    quote_decimals,
                    is_token_first,
                )
                .unwrap_or_default();

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 1,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };
                let pool_state = chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);

                // Token holder delta
                {
                    let mut token_holder_delta =
                        TokenHolderDelta::new(timestamp_millis, block_number);
                    token_holder_delta.sold_amount = if is_token_first {
                        add_liquidity.amounts[0]
                    } else {
                        add_liquidity.amounts[1]
                    }
                    .into();
                    let token_holder = chain_cache
                        .update_token_holder(
                            &token_metadata.address.to_string(),
                            &add_liquidity.from.to_string(),
                            token_holder_delta,
                        )
                        .await?;
                    unsync_database_buf.insert_token_holder(token_holder);
                }
            }
            MeteoraDlmmEvent::RemoveLiquidity(RemoveLiquidityEvent(remove_liquidity)) => {
                let (pool_metadata, token_metadata, base_metadata, is_token_first) =
                    self.get_or_fetch_metadatas(&remove_liquidity.lb_pair, None).await?;

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;
                let lb_pair = LbPair::fetch(&remove_liquidity.lb_pair).await?;
                let (x_reserves, y_reserves) =
                    LbPair::fetch_two_token_accounts_balances(lb_pair.reserve_x, lb_pair.reserve_y)
                        .await?;

                let ((_, base_decimals), (_, quote_decimals)) = if is_token_first {
                    (
                        (&token_metadata.address, token_metadata.decimals),
                        (&base_metadata.address, base_metadata.decimals),
                    )
                } else {
                    (
                        (&base_metadata.address, base_metadata.decimals),
                        (&token_metadata.address, token_metadata.decimals),
                    )
                };

                // PoolState
                let timestamp_millis = block_timestamp_seconds * 1000;

                let liquidity = LbPair::get_total_liquidity(
                    x_reserves,
                    y_reserves,
                    base_decimals,
                    quote_decimals,
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    is_token_first,
                )
                .unwrap_or_default();
                let price = LbPair::get_price_per_token(
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    base_decimals,
                    quote_decimals,
                    is_token_first,
                )
                .unwrap_or_default();
                let market_cap = LbPair::get_market_cap(
                    token_metadata
                        .supply
                        .to_u64()
                        .ok_or(anyhow::anyhow!("Token supply is too large"))?,
                    lb_pair.active_id,
                    lb_pair.bin_step,
                    base_decimals,
                    quote_decimals,
                    is_token_first,
                )
                .unwrap_or_default();

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 1,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };
                let pool_state = chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);

                // Token holder delta
                {
                    let mut token_holder_delta =
                        TokenHolderDelta::new(timestamp_millis, block_number);
                    token_holder_delta.bought_amount = if is_token_first {
                        remove_liquidity.amounts[0]
                    } else {
                        remove_liquidity.amounts[1]
                    }
                    .into();
                    let token_holder = chain_cache
                        .update_token_holder(
                            &token_metadata.address.to_string(),
                            &remove_liquidity.from.to_string(),
                            token_holder_delta,
                        )
                        .await?;
                    unsync_database_buf.insert_token_holder(token_holder);
                }
            }
        }

        Ok(())
    }
}
