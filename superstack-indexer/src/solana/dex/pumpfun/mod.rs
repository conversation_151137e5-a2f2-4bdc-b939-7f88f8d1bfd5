pub mod accounts;
pub mod events;

use accounts::bonding_curve::BondingCurveAccount;
use events::{
    complete::{CompleteEvent, CompletePumpAmmMigrationEvent},
    create::CreateEvent,
    trade::TradeEvent,
    PumpfunEvent,
};
use num_traits::ToPrimitive;
use solana_sdk::{
    bs58, declare_id, native_token::lamports_to_sol, pubkey::Pubkey, signature::Signature,
};
use solana_transaction_status_client_types::{
    option_serializer::OptionSerializer, UiInstruction, UiTransactionStatusMeta,
};
use superstack_data::{
    postgres::{
        enums::{Chain, Dex, PoolType},
        indexer::{DexTrade, PoolMetadata, PoolStateDelta, TokenHolderDelta, TokenMetadata},
    },
    utils::to_ui_amount,
};

use crate::{
    cache::{ChainCache, UnsyncDatabaseBuf},
    solana::{dex::helper::get_or_fetch_token_metadata, utils::sol_mint},
};

declare_id!("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P");

#[derive(Debug, <PERSON><PERSON>, Copy)]
pub struct Pumpfun {
    pub program_id: Pubkey,
}

impl Pumpfun {
    pub fn new(program_id: Pubkey) -> Self {
        Self { program_id }
    }

    pub fn get_bonding_curve_address(mint: &Pubkey, program_id: &Pubkey) -> Pubkey {
        Pubkey::find_program_address(&[b"bonding-curve", mint.as_array()], program_id).0
    }

    pub fn parse(
        &self,
        tx_meta: &UiTransactionStatusMeta,
    ) -> anyhow::Result<Vec<(usize, PumpfunEvent)>> {
        let mut events = Vec::new();
        if let OptionSerializer::Some(ixs) = &tx_meta.inner_instructions {
            let ixs = ixs.iter().flat_map(|ix| ix.instructions.iter());
            for (ix_idx, ix) in ixs.enumerate() {
                if let UiInstruction::Compiled(ix) = ix {
                    let data = match bs58::decode(ix.data.clone()).into_vec() {
                        Ok(data) => data,
                        Err(_) => continue,
                    };

                    let event = match PumpfunEvent::from_data(&data) {
                        Ok(event) => event,
                        Err(_) => continue,
                    };

                    events.push((ix_idx, event));
                }
            }
        }

        Ok(events)
    }

    pub fn trade_event_to_bonding_curve(event: &TradeEvent) -> anyhow::Result<BondingCurveAccount> {
        // ten billion tokens
        const TOKEN_TOTAL_SUPPLY: u64 = 10000000000000000000;
        let bonding_curve = BondingCurveAccount {
            discriminator: 0, // not necessary for calculation
            virtual_token_reserves: event.virtual_token_reserves,
            virtual_sol_reserves: event.virtual_sol_reserves,
            real_token_reserves: event.real_token_reserves,
            real_sol_reserves: event.real_sol_reserves,
            token_total_supply: TOKEN_TOTAL_SUPPLY,
            complete: false,
        };

        Ok(bonding_curve)
    }

    pub fn create_event_to_pool_metadata(
        event: &CreateEvent,
    ) -> anyhow::Result<BondingCurveAccount> {
        let bonding_curve = BondingCurveAccount {
            discriminator: 0, // not necessary for calculation
            virtual_token_reserves: event.virtual_token_reserves,
            virtual_sol_reserves: event.virtual_sol_reserves,
            real_token_reserves: event.real_token_reserves,
            real_sol_reserves: 0,
            token_total_supply: event.token_total_supply,
            complete: false,
        };

        Ok(bonding_curve)
    }

    pub async fn get_or_fetch_metadatas(
        &self,
        mint: &Pubkey,
        pool: &Pubkey,
        update_timestamp_millis: i64,
    ) -> anyhow::Result<(PoolMetadata, TokenMetadata)> {
        let chain_cache = ChainCache::get_cache(Chain::Solana);
        let create_dex = Dex::Pumpfun;
        let create_bonding_curve = Some(*pool);

        let pool_metadata =
            match chain_cache.get_pool_metadata_or_update_from_db(&pool.to_string()).await? {
                Some(pool_metadata) => pool_metadata,
                None => {
                    let token_metadata = get_or_fetch_token_metadata(
                        *mint,
                        update_timestamp_millis,
                        create_dex,
                        create_bonding_curve,
                    )
                    .await?;
                    let pair_label = format!("{}/SOL", token_metadata.symbol);
                    let pool_metadata = PoolMetadata::new_solana_pool(
                        *pool,
                        pair_label,
                        create_dex,
                        PoolType::None,
                        None,
                        update_timestamp_millis,
                        *mint,
                        token_metadata.decimals,
                        sol_mint::ID,
                        sol_mint::DECIMALS,
                        true,
                        None,
                    );
                    let pool_metadata = chain_cache
                        .insert_pool_metadata_and_publish_if_not_exists(pool_metadata)
                        .await?;

                    return Ok((pool_metadata, token_metadata));
                }
            };

        let token_metadata = get_or_fetch_token_metadata(
            *mint,
            update_timestamp_millis,
            create_dex,
            create_bonding_curve,
        )
        .await?;

        Ok((pool_metadata, token_metadata))
    }

    pub async fn process_event(
        &self,
        event: &(usize, PumpfunEvent),
        tx_hash: &Signature,
        tx_idx: u32,
        slot: u64,
        sol_usd_price: f64,
        unsync_database_buf: &mut UnsyncDatabaseBuf,
    ) -> anyhow::Result<()> {
        let chain_cache = ChainCache::get_cache(Chain::Solana);

        let (ix_idx, event) = event;

        match event {
            PumpfunEvent::Trade(event) => {
                let timestamp_millis = event.timestamp * 1000;
                let block_number = slot;
                let pool_address = Self::get_bonding_curve_address(&event.mint, &self.program_id);

                let (_pool_metadata, token_metadata) = self
                    .get_or_fetch_metadatas(&event.mint, &pool_address, timestamp_millis)
                    .await?;

                let (in_mint, out_mint, in_amount, out_amount) = if event.is_buy {
                    (sol_mint::ID, event.mint, event.sol_amount, event.token_amount)
                } else {
                    (event.mint, sol_mint::ID, event.token_amount, event.sol_amount)
                };

                // Trade
                let trade = DexTrade {
                    chain: Chain::Solana,
                    tx_hash: tx_hash.to_string(),
                    ix_idx: *ix_idx as u32,
                    pool_address: pool_address.to_string(),
                    maker_address: event.user.to_string(),
                    is_buy_token: event.is_buy,
                    in_address: in_mint.to_string(),
                    out_address: out_mint.to_string(),
                    in_amount: in_amount.into(),
                    out_amount: out_amount.into(),
                    block_number,
                    timestamp_millis,
                    tx_idx,
                };
                unsync_database_buf.insert_dex_trade(trade);

                // PoolState
                {
                    let bonding_curve = Self::trade_event_to_bonding_curve(event)?;
                    let sol_per_token = bonding_curve
                        .get_sell_price_without_fee(10u64.pow(token_metadata.decimals as u32))
                        .map_err(|_| anyhow::anyhow!("Bonding curve is complete"))?;
                    let total_liquidity = bonding_curve.get_liquidity_in_sol()?;
                    let supply = token_metadata
                        .supply
                        .to_u64()
                        .ok_or(anyhow::anyhow!("Token supply is too large"))?;

                    let price = lamports_to_sol(sol_per_token);
                    let market_cap = price * to_ui_amount(supply, token_metadata.decimals);
                    let liquidity = lamports_to_sol(total_liquidity);
                    let delta_volume = lamports_to_sol(event.sol_amount);
                    let bonding_curve_progress = Some(bonding_curve.get_progress());

                    let pool_state_delta = PoolStateDelta {
                        timestamp_millis,
                        block_number,
                        price: Some(price),
                        market_cap: Some(market_cap),
                        liquidity,
                        delta_volume,
                        delta_buy_volume: if event.is_buy { delta_volume } else { 0.0 },
                        delta_sell_volume: if event.is_buy { 0.0 } else { delta_volume },
                        delta_txns: 1,
                        delta_buy_txns: if event.is_buy { 1 } else { 0 },
                        delta_sell_txns: if event.is_buy { 0 } else { 1 },
                        bonding_curve_progress,
                    };

                    let pool_state = chain_cache
                        .update_pool_state(
                            &pool_address.to_string(),
                            pool_state_delta,
                            block_number,
                        )
                        .await?;
                    unsync_database_buf.insert_pool_state(pool_state);
                };

                // Token Holder
                {
                    let mut token_holder_delta =
                        TokenHolderDelta::new(timestamp_millis, block_number);
                    if event.is_buy {
                        token_holder_delta.bought_amount = event.token_amount.into();
                        token_holder_delta.spent_native_token_ui_amount =
                            lamports_to_sol(event.sol_amount);
                        token_holder_delta.total_spent_usd =
                            token_holder_delta.spent_native_token_ui_amount * sol_usd_price;
                        token_holder_delta.bought_txns = 1;
                    } else {
                        token_holder_delta.sold_amount = event.token_amount.into();
                        token_holder_delta.received_native_token_ui_amount =
                            lamports_to_sol(event.sol_amount);
                        token_holder_delta.total_received_usd =
                            token_holder_delta.received_native_token_ui_amount * sol_usd_price;
                        token_holder_delta.sold_txns = 1;
                    }

                    let token_holder = chain_cache
                        .update_token_holder(
                            &event.mint.to_string(),
                            &event.user.to_string(),
                            token_holder_delta,
                        )
                        .await?;
                    unsync_database_buf.insert_token_holder(token_holder);
                }
            }
            PumpfunEvent::Create(create) => {
                tracing::info!("Pumpfun Create: {:?}", create);
                let CreateEvent { symbol, mint, bonding_curve, creator, timestamp, .. } = create;

                // Token Metadata
                let token_address = *mint;
                let dex = Dex::Pumpfun;
                let create_block_number = Some(slot);
                let create_tx_hash = Some(*tx_hash);
                let create_bonding_curve = Some(*bonding_curve);
                let create_dev = Some(*creator);
                let update_timestamp_millis = timestamp * 1000;
                let create_timestamp_millis = Some(update_timestamp_millis);
                let token_metadata = TokenMetadata::construct_solana_token_metadata_from_rpc(
                    token_address,
                    dex,
                    create_block_number,
                    create_tx_hash,
                    create_bonding_curve,
                    create_dev,
                    create_timestamp_millis,
                    None,
                    None,
                    update_timestamp_millis,
                    Some(1),
                )
                .await?;
                chain_cache
                    .update_token_metadata_create_info_and_publish(token_metadata.clone())
                    .await?;

                // Pool Metadata
                let pool_address = *bonding_curve;
                let pair_label = format!("{}/SOL", symbol);
                let pool_type = PoolType::None;
                let token_decimals = token_metadata.decimals;
                let base_address = sol_mint::ID;
                let base_decimals = sol_mint::DECIMALS;
                let is_token_first = true;
                let pool_metadata = PoolMetadata::new_solana_pool(
                    pool_address,
                    pair_label,
                    dex,
                    pool_type,
                    create_timestamp_millis,
                    update_timestamp_millis,
                    token_address,
                    token_decimals,
                    base_address,
                    base_decimals,
                    is_token_first,
                    None,
                );
                chain_cache.update_pool_metadata_create_info_and_publish(pool_metadata).await?;

                // PoolState
                let bonding_curve_account = Self::create_event_to_pool_metadata(create)?;

                let sol_per_token = bonding_curve_account
                    .get_sell_price_without_fee(10u64.pow(token_metadata.decimals as u32))
                    .map_err(|_| anyhow::anyhow!("Bonding curve is complete"))?;
                let total_liquidity = bonding_curve_account.get_liquidity_in_sol()?;
                let supply = token_metadata
                    .supply
                    .to_u64()
                    .ok_or(anyhow::anyhow!("Token supply is too large"))?;

                let price = lamports_to_sol(sol_per_token);
                let market_cap = price * to_ui_amount(supply, token_metadata.decimals);
                let liquidity = lamports_to_sol(total_liquidity);

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis: update_timestamp_millis,
                    block_number: slot,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 0,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: Some(0.0),
                };

                let pool_state = chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, slot)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);
            }
            PumpfunEvent::Complete(complete) => {
                let CompleteEvent { bonding_curve, timestamp, mint, .. } = complete;
                tracing::info!("Pumpfun Complete: {:?}", complete);

                let pool_address = *bonding_curve;
                let timestamp_millis = timestamp * 1000;
                let block_number = slot;
                let bonding_curve_progress = 1.0;

                let (_pool_metadata, _token_metadata) =
                    self.get_or_fetch_metadatas(mint, &pool_address, timestamp_millis).await?;

                if let Some(pool_state) = chain_cache
                    .get_pool_state_or_update_from_db(&pool_address.to_string(), block_number)
                    .await?
                {
                    let mut guard = pool_state.write().await;
                    guard.update_bonding_curve_progress(bonding_curve_progress);
                    unsync_database_buf.insert_pool_state(guard.clone());
                } else {
                    tracing::warn!(
                        "[FIXME] Ignoring complete event since pool state is not found: {:?}",
                        complete
                    );
                    return Ok(());
                }
            }
            PumpfunEvent::CompletePumpAmmMigration(complete_pump_amm_migration) => {
                tracing::info!("Pumpfun Complete Migration: {:?}", complete_pump_amm_migration);
                let CompletePumpAmmMigrationEvent { timestamp, bonding_curve, mint, pool, .. } =
                    complete_pump_amm_migration;

                let migration_pool_address = Some(pool.to_string());
                let pool_address = *bonding_curve;
                let timestamp_millis = timestamp * 1000;
                let block_number = slot;

                let (mut pool_metadata, mut token_metadata) =
                    self.get_or_fetch_metadatas(mint, &pool_address, timestamp_millis).await?;

                if pool_metadata.is_active {
                    pool_metadata.is_active = false;
                    pool_metadata.update_timestamp_millis =
                        pool_metadata.update_timestamp_millis.max(timestamp_millis);
                    chain_cache.update_pool_metadata_active_info_and_publish(pool_metadata).await?;
                }

                token_metadata.migration_pool_address = migration_pool_address;
                token_metadata.migration_timestamp_millis = timestamp_millis;
                token_metadata.update_timestamp_millis =
                    token_metadata.update_timestamp_millis.max(timestamp_millis);
                chain_cache
                    .update_token_metadata_migration_info_and_publish(token_metadata)
                    .await?;

                let bonding_curve_progress = 1.0;
                if let Some(pool_state) = chain_cache
                    .get_pool_state_or_update_from_db(&pool_address.to_string(), block_number)
                    .await?
                {
                    let mut guard = pool_state.write().await;
                    guard.update_bonding_curve_progress(bonding_curve_progress);
                    unsync_database_buf.insert_pool_state(guard.clone());
                } else {
                    tracing::warn!(
                        "[FIXME] Ignoring complete migration event since pool state is not found: {:?}",
                        complete_pump_amm_migration
                    );
                    return Ok(());
                }
            }
            _ => {}
        }

        Ok(())
    }
}

// #[cfg(test)]
// pub mod test {
//     use std::str::FromStr;

//     use solana_sdk::{pubkey::Pubkey, signature::Signature};

//     use super::*;
//     use crate::solana::utils::get_transaction_config;

//     #[tokio::test]
//     async fn test_migrate_event() {
//         let tx_sig =
// Signature::from_str("
// 2EVPYd8p3ZgETHmEyL8gbenQhbF7x2Q5359erQCgkuNUaKi9ccDQsNYB9N5ApMNQF5egCTkkf5aXzsWNffnEeYsW").
// unwrap();

//         let client = get_confirmed_rpc_client();
//         let tx =
//             client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();
//         let tx_meta = tx.transaction.meta.unwrap();
//         let decoded = tx.transaction.transaction.decode().unwrap();
//         let program_ids = decoded.message.static_account_keys();
//         println!("{:?}", program_ids);
//         dbg!(program_ids.len());

//         let dex = Pumpfun::new(pumpfun::ID);
//         let events = dex.parse(&tx_meta).unwrap();
//         println!("{:?}", events);
//     }

//     #[tokio::test]
//     async fn test_create_event() {
//         let tx_sig =
// Signature::from_str("
// 4YvvMAC7gEgbhGdmuCDg9oextJrbWtzR2geqgVzR1qAJWkY9oNaxLxvQoBeLZbdFwHw35LnKM37qwqBBNwB5yfG6").
// unwrap();         let client = get_confirmed_rpc_client();
//         let tx =
//             client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();
//         let tx_meta = tx.transaction.meta.unwrap();

//         let dex = Pumpfun::new(pumpfun::ID);
//         let events = dex.parse(&tx_meta).unwrap();

//         for event in events {
//             dex.process_event(&event, &tx_sig, 1).await.unwrap();
//         }
//     }
//     #[tokio::test]
//     async fn test_pda_pool_address() {
//         let bonding_curve =
//             Pubkey::from_str("4wMe9bi6s7aMC9wjraaqj5F2P1e1aYCKsoi9465E3KDu").unwrap();
//         let mint = Pubkey::from_str("DdYLryv29fjyNATy2qCDEkkHigD8WZ8ibiLiPYX3STuM").unwrap();
//         let pool_address =
//             Pubkey::find_program_address(&[b"bonding-curve", mint.as_array()], &pumpfun::ID).0;

//         println!("{:?}", pool_address);
//         println!("{:?}", bonding_curve);
//     }

//     #[tokio::test]
//     async fn test_pub_key_parse() {
//         let pubkey = Pubkey::from_str("TSLvdd1pWpHVjahSpsvCXUbgwsL3JAcvokwaKt1eokM").unwrap();

//         for c in pubkey.to_string().chars() {
//             if c.is_whitespace() {
//                 println!("{}", c);
//             }
//         }
//     }
// }
