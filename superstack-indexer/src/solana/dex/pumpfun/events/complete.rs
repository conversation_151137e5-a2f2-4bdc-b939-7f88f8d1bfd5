use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

#[derive(Debug, <PERSON><PERSON>, BorshSerialize, BorshDeserialize)]
pub struct CompleteEvent {
    pub user: Pubkey,
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub timestamp: i64,
}

// 05/13/2025
// {
//     "user": "39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg",
//     "mint": "6NVM9Yo3upgh8HgmbWthvUNixoAGtMvwbQuZNvm4UAYX",
//     "mintAmount": "206900000000000",
//     "solAmount": "84990359169",
//     "poolMigrationFee": "15000001",
//     "bondingCurve": "2RzA43tm8ETVFM1FszuTUP8APFJzHLD2BTMEYbynbiyr",
//     "timestamp": "1747113282",
//     "pool": "3iKS8quJkExsMREyzfJm9uhn97cSSBrdHc6Hjim4CD4J"
// }
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>Serialize, BorshDeserialize)]
pub struct CompletePumpAmmMigrationEvent {
    pub user: Pubkey,
    pub mint: Pubkey,
    pub mint_amount: u64,
    pub sol_amount: u64,
    pub pool_migration_fee: u64,
    pub bonding_curve: Pubkey,
    pub timestamp: i64,
    pub pool: Pubkey,
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    use crate::solana::{
        dex::pumpfun::{self, Pumpfun},
        utils::get_transaction_config,
    };

    #[tokio::test]
    async fn test_parse_migrate() {
        let tx = "3z3TMqxmXgYbtYffdydWZZsCCupwPt8F87EFWnfjR1NHKCA8rSJmeV8nstXor8dLJJwkWC9RifPeMvMREfpQGuef";
        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();

        let meta = tx.transaction.meta.unwrap();
        let pumpfun = Pumpfun::new(pumpfun::ID);

        let event = pumpfun.parse(&meta).unwrap();

        println!("{:?}", event);
    }
}
