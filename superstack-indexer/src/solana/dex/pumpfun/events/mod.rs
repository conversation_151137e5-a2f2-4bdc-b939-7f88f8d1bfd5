use borsh::BorshDeserialize;
use complete::{CompleteEvent, CompletePumpAmmMigrationEvent};
use create::CreateEvent;
use set_params::SetParamsEvent;
use solana_sdk::pubkey::Pubkey;
use trade::TradeEvent;

pub mod complete;
pub mod create;
pub mod set_params;
pub mod trade;
pub mod discriminator {
    pub const TRADE_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 189, 219, 127, 211, 78, 230, 97, 238];
    pub const CREATE_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 27, 114, 169, 77, 222, 235, 99, 118];
    pub const COMPLETE_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 95, 114, 97, 156, 212, 46, 152, 8];
    pub const SET_PARAMS_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 223, 195, 159, 246, 62, 48, 143, 131];
    pub const COMPLETE_PUMP_AMM_MIGRATION_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 189, 233, 93, 185, 92, 148, 234, 148];
}

#[derive(Debug, Clone)]
pub enum PumpfunEvent {
    Trade(TradeEvent),
    Create(CreateEvent),
    Complete(CompleteEvent),
    SetParams(SetParamsEvent),
    CompletePumpAmmMigration(CompletePumpAmmMigrationEvent),
}

impl PumpfunEvent {
    pub fn from_data(data: &[u8]) -> anyhow::Result<Self> {
        match data {
            x if x.starts_with(&discriminator::TRADE_EVENT) => {
                Ok(Self::Trade(TradeEvent::try_from_slice(&x[16..])?))
            }
            x if x.starts_with(&discriminator::CREATE_EVENT) => {
                Ok(Self::Create(CreateEvent::try_from_slice(&x[16..])?))
            }
            x if x.starts_with(&discriminator::COMPLETE_EVENT) => {
                Ok(Self::Complete(CompleteEvent::try_from_slice(&x[16..])?))
            }
            x if x.starts_with(&discriminator::SET_PARAMS_EVENT) => {
                Ok(Self::SetParams(SetParamsEvent::try_from_slice(&x[16..])?))
            }
            x if x.starts_with(&discriminator::COMPLETE_PUMP_AMM_MIGRATION_EVENT) => {
                Ok(Self::CompletePumpAmmMigration(CompletePumpAmmMigrationEvent::try_from_slice(
                    &x[16..],
                )?))
            }
            _ => Err(anyhow::anyhow!("Unknown event {:?}", data)),
        }
    }

    pub fn key(&self) -> Pubkey {
        match self {
            // Core events
            Self::Trade(trade) => trade.mint,
            Self::Create(create) => create.mint,
            Self::Complete(complete) => complete.mint,
            Self::CompletePumpAmmMigration(complete_pump_amm_migration) => {
                complete_pump_amm_migration.mint
            }
            // Other events
            Self::SetParams(set_params) => set_params.withdraw_authority,
        }
    }
}
