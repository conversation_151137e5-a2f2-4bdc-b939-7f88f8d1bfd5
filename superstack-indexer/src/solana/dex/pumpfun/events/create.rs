use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

// 05/13/2025
// {
//     "name": "Bichi GF",
//     "symbol": "<PERSON><PERSON>",
//     "uri": "https://ipfs.io/ipfs/QmULDquPYVqf55ezHW8kpj3woSVSP5meqLTxJNZiu3jCSM",
//     "mint": "AvwXvmxffcTZCt2RMcisrkmmMvgnZhK9p5LMugmbpump",
//     "bondingCurve": "5ek9xPEr2Mk1v5ByNxDxnqbTQW19Tkfe8suHrC5sMNG6",
//     "user": "GSoQPZH5gqpjUgD89TJrZmwPrvsAa66L3tKhpBZdMt8A",
//     "creator": "GSoQPZH5gqpjUgD89TJrZmwPrvsAa66L3tKhpBZdMt8A",
//     "timestamp": "1747114783",
//     "virtualTokenReserves": "1073000000000000",
//     "virtualSolReserves": "30000000000",
//     "realTokenReserves": "793100000000000",
//     "tokenTotalSupply": "1000000000000000"
//   }
#[derive(Debug, Clone, BorshSerialize, BorshDeserialize)]
pub struct CreateEvent {
    pub name: String,
    pub symbol: String,
    pub uri: String,
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub user: Pubkey,
    pub creator: Pubkey,
    pub timestamp: i64,
    // 5/13/2025
    pub virtual_token_reserves: u64,
    pub virtual_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub token_total_supply: u64,
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use crate::solana::{
        dex::pumpfun::{self, Pumpfun},
        utils::get_transaction_config,
    };
    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    #[tokio::test]
    async fn test_parse_create() {
        let tx = "4Ny2ZMSpc3teGfoB5frjRMcfhdNBmBh2y6TH81FTxyaen1Y8YeJpAGohtx74wgviTrv9zvrcLCKPy4vhaSKkEG1y";
        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();

        let meta = tx.transaction.meta.unwrap();
        let pumpfun = Pumpfun::new(pumpfun::ID);

        let event = pumpfun.parse(&meta).unwrap();

        println!("{:?}", event);
    }
}
