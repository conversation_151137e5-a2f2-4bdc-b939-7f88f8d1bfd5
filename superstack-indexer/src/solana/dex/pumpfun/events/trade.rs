use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

// 05/13/2025
// {
//     "mint": "HR7oFL28nwvkgXK8Eb85HCbRwbjHGNTMwspxTUNjpump",
//     "solAmount": "14248400",
//     "tokenAmount": "413061714302",
//     "isBuy": false,
//     "user": "2hqSnAGxttWnsz4puBQJn3uNfM6KyJ5YicNocrGGMDpz",
//     "timestamp": "1747114702",
//     "virtualSolReserves": "33315261509",
//     "virtualTokenReserves": "966223845486271",
//     "realSolReserves": "3315261509",
//     "realTokenReserves": "686323845486271",
//     "feeRecipient": "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV",
//     "feeBasisPoints": "95",
//     "fee": "135360",
//     "creator": "DKpDPSYUyKskgN2YSUxiTmwnjNCx1UQ6jkHgJeqNm4gY",
//     "creatorFeeBasisPoints": "5",
//     "creatorFee": "7125"
//   }
#[derive(Debug, Clone, BorshSerialize, BorshDeserialize)]
pub struct TradeEvent {
    pub mint: Pubkey,
    pub sol_amount: u64,
    pub token_amount: u64,
    pub is_buy: bool,
    pub user: Pubkey,
    pub timestamp: i64,
    pub virtual_sol_reserves: u64,
    pub virtual_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub real_token_reserves: u64,
    // 5/13/2025
    pub fee_recipient: Pubkey,
    pub fee_basis_points: u64,
    pub fee: u64,
    pub creator: Pubkey,
    pub creator_fee_basis_points: u64,
    pub creator_fee: u64,
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use crate::solana::{
        dex::pumpfun::{self, Pumpfun},
        utils::get_transaction_config,
    };
    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    #[tokio::test]
    async fn test_parse_trade() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let tx = "n37qrQkPDdHzCMYYvH69SiEookUPg7yNNmBvcXFHQ4GAcUZK9rmaPnLEVDqSV6AN493o8DNAkDyFCcKRmmsh9gk";
        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();

        let meta = tx.transaction.meta.unwrap();
        let pumpfun = Pumpfun::new(pumpfun::ID);

        let event = pumpfun.parse(&meta).unwrap();

        println!("{:?}", event);
    }
}
