use backon::{ExponentialBuilder, Retryable};
use borsh::{BorshDeserialize, BorshSerialize};
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

use superstack_data::utils::{get_confirmed_rpc_client, to_ui_amount};

/// Represents a bonding curve for token pricing and liquidity management
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, BorshSerialize, BorshDeserialize, Serialize, Deserialize)]
pub struct BondingCurveAccount {
    /// Unique identifier for the bonding curve
    pub discriminator: u64,
    /// Virtual token reserves used for price calculations
    pub virtual_token_reserves: u64,
    /// Virtual SOL reserves used for price calculations
    pub virtual_sol_reserves: u64,
    /// Actual token reserves available for trading
    pub real_token_reserves: u64,
    /// Actual SOL reserves available for trading
    pub real_sol_reserves: u64,
    /// Total supply of tokens
    pub token_total_supply: u64,
    /// Whether the bonding curve is complete/finalized
    pub complete: bool,
}

impl BondingCurveAccount {
    pub async fn fetch(pubkey: &Pubkey) -> anyhow::Result<Self> {
        let fetch = || async {
            let client = get_confirmed_rpc_client();
            let account = client.get_account(pubkey).await?;
            let curve = account.deserialize_data::<BondingCurveAccount>()?;
            Ok::<_, anyhow::Error>(curve)
        };

        let account = fetch.retry(ExponentialBuilder::default()).await?;
        Ok(account)
    }

    pub fn get_liquidity_in_sol(&self) -> anyhow::Result<u64> {
        let one_token = 1_000_000;
        let sol_amount = self
            .get_sell_price_without_fee(one_token)
            .map_err(|_| anyhow::anyhow!("Curve is complete"))?;
        let a = to_ui_amount(self.real_token_reserves, 6) * sol_amount as f64;
        let b = self.real_sol_reserves as f64;
        let liquidity = a + b;
        Ok(liquidity as u64)
    }
    /// Creates a new bonding curve instance
    ///
    /// # Arguments
    /// * `discriminator` - Unique identifier for the curve
    /// * `virtual_token_reserves` - Virtual token reserves for price calculations
    /// * `virtual_sol_reserves` - Virtual SOL reserves for price calculations
    /// * `real_token_reserves` - Actual token reserves available
    /// * `real_sol_reserves` - Actual SOL reserves available
    /// * `token_total_supply` - Total supply of tokens
    /// * `complete` - Whether the curve is complete
    pub fn new(
        discriminator: u64,
        virtual_token_reserves: u64,
        virtual_sol_reserves: u64,
        real_token_reserves: u64,
        real_sol_reserves: u64,
        token_total_supply: u64,
        complete: bool,
    ) -> Self {
        Self {
            discriminator,
            virtual_token_reserves,
            virtual_sol_reserves,
            real_token_reserves,
            real_sol_reserves,
            token_total_supply,
            complete,
        }
    }

    pub fn get_progress(&self) -> f64 {
        if self.complete {
            return 100.0;
        }
        const INITIAL_REAL_TOKEN_RESERVES: u64 = 793_100_000_000_000;

        let progress =
            100.0 - (self.real_token_reserves * 100 / INITIAL_REAL_TOKEN_RESERVES) as f64;

        progress.min(100.0) / 100.0
    }

    /// Calculates the amount of tokens received for a given SOL amount
    ///
    /// # Arguments
    /// * `amount` - Amount of SOL to spend
    ///
    /// # Returns
    /// * `Ok(u64)` - Amount of tokens that would be received
    /// * `Err(&str)` - Error message if curve is complete
    pub fn get_buy_price(&self, amount: u64) -> Result<u64, &'static str> {
        if self.complete {
            return Err("Curve is complete");
        }

        if amount == 0 {
            return Ok(0);
        }

        // Calculate the product of virtual reserves using u128 to avoid overflow
        let n: u128 = (self.virtual_sol_reserves as u128) * (self.virtual_token_reserves as u128);

        // Calculate the new virtual sol reserves after the purchase
        let i: u128 = (self.virtual_sol_reserves as u128) + (amount as u128);

        // Calculate the new virtual token reserves after the purchase
        let r: u128 = n / i + 1;

        // Calculate the amount of tokens to be purchased
        let s: u128 = (self.virtual_token_reserves as u128) - r;

        // Convert back to u64 and return the minimum of calculated tokens and real reserves
        let s_u64 = s as u64;
        Ok(if s_u64 < self.real_token_reserves { s_u64 } else { self.real_token_reserves })
    }

    /// Calculates the amount of SOL received for selling tokens
    ///
    /// # Arguments
    /// * `amount` - Amount of tokens to sell
    /// * `fee_basis_points` - Fee in basis points (1/100th of a percent)
    ///
    /// # Returns
    /// * `Ok(u64)` - Amount of SOL that would be received after fees
    /// * `Err(&str)` - Error message if curve is complete
    pub fn get_sell_price(&self, amount: u64, fee_basis_points: u64) -> Result<u64, &'static str> {
        if self.complete {
            return Err("Curve is complete");
        }

        if amount == 0 {
            return Ok(0);
        }

        // Calculate the proportional amount of virtual sol reserves to be received using u128
        let n: u128 = ((amount as u128) * (self.virtual_sol_reserves as u128)) /
            ((self.virtual_token_reserves as u128) + (amount as u128));

        // Calculate the fee amount in the same units
        let a: u128 = (n * (fee_basis_points as u128)) / 10000;

        // Return the net amount after deducting the fee, converting back to u64
        Ok((n - a) as u64)
    }

    pub fn get_sell_price_without_fee(&self, amount: u64) -> Result<u64, &'static str> {
        if self.complete {
            return Err("Curve is complete");
        }

        if amount == 0 {
            return Ok(0);
        }

        Ok((amount as u128 * self.virtual_sol_reserves as u128 /
            (self.virtual_token_reserves as u128 + amount as u128)) as u64)
    }

    /// market cap in sol
    /// Calculates the current market cap in SOL
    pub fn get_market_cap_sol(&self) -> u64 {
        if self.virtual_token_reserves == 0 {
            return 0;
        }

        ((self.token_total_supply as u128) * (self.virtual_sol_reserves as u128) /
            (self.virtual_token_reserves as u128)) as u64
    }

    /// fdv in sol
    /// Calculates the final market cap in SOL after all tokens are sold
    ///
    /// # Arguments
    /// * `fee_basis_points` - Fee in basis points (1/100th of a percent)
    pub fn get_final_market_cap_sol(&self, fee_basis_points: u64) -> u64 {
        let total_sell_value: u128 =
            self.get_buy_out_price(self.real_token_reserves, fee_basis_points) as u128;
        let total_virtual_value: u128 = (self.virtual_sol_reserves as u128) + total_sell_value;
        let total_virtual_tokens: u128 =
            (self.virtual_token_reserves as u128) - (self.real_token_reserves as u128);

        if total_virtual_tokens == 0 {
            return 0;
        }

        ((self.token_total_supply as u128) * total_virtual_value / total_virtual_tokens) as u64
    }

    /// Calculates the price to buy out all remaining tokens
    ///
    /// # Arguments
    /// * `amount` - Amount of tokens to buy
    /// * `fee_basis_points` - Fee in basis points (1/100th of a percent)
    pub fn get_buy_out_price(&self, amount: u64, fee_basis_points: u64) -> u64 {
        // Get the effective amount of sol tokens
        let sol_tokens: u128 = if amount < self.real_sol_reserves {
            self.real_sol_reserves as u128
        } else {
            amount as u128
        };

        // Calculate total sell value
        let total_sell_value: u128 = (sol_tokens * (self.virtual_sol_reserves as u128)) /
            ((self.virtual_token_reserves as u128) - sol_tokens) +
            1;

        // Calculate fee
        let fee: u128 = (total_sell_value * (fee_basis_points as u128)) / 10000;

        // Return total including fee, converting back to u64
        (total_sell_value + fee) as u64
    }
}

#[cfg(test)]
mod test {
    use std::str::FromStr;

    use solana_sdk::pubkey::Pubkey;
    use superstack_data::utils::{get_confirmed_rpc_client, to_ui_amount};

    use crate::solana::dex::pumpfun::{
        self, accounts::bonding_curve::BondingCurveAccount, Pumpfun,
    };

    #[tokio::test]
    async fn test_get_sell_price() {
        dotenv::dotenv().ok();
        // /  return (Number(curveState.virtualSolReserves) / web3.LAMPORTS_PER_SOL) /
        // (Number(curveState.virtualTokenReserves) / 10 ** PUMP_CURVE_TOKEN_DECIMALS);

        let _curve = BondingCurveAccount::fetch(
            &Pubkey::from_str("ABJtshEEv5ULE8ioCDtrctjzSKiyGm2RDFK6hwpL88fD").unwrap(),
        )
        .await
        .unwrap();

        let curve = BondingCurveAccount::new(
            0,
            ****************,
            ***********,
            ***************,
            1864033,
            ****************,
            false,
        );

        println!("curve: {:?}", curve);

        // let price = curve.virtual_sol_reserves as f64 / curve.virtual_token_reserves as f64;
        let one_token = 1_000_000;
        // let one_sol = 1_000_000_000;

        let sol_amount = curve.get_sell_price_without_fee(one_token).unwrap();
        let price = to_ui_amount(sol_amount, 9);
        // let price =
        //     to_ui_amount(curve.virtual_sol_reserves, 9) /
        // to_ui_amount(curve.virtual_token_reserves, 6);

        let liquidity = curve.get_liquidity_in_sol().unwrap();

        println!("price: {}", price * 152.0);
        println!("market cap: {}", curve.get_market_cap_sol() as f64 / 1_000_000_000.0 * 152.0);
        println!("progress: {}", curve.get_progress());
        println!("liquidity: {}", liquidity as f64 / 1_000_000_000.0 * 152.0);
    }

    #[tokio::test]
    async fn test_get_bonding_curve_address() {
        let mint = Pubkey::from_str("CUSejdA8TFS4RkLyTp3NEAgV82rN6gsymHV67tMxpmvw").unwrap();
        let bonding_curve = Pumpfun::get_bonding_curve_address(&mint, &pumpfun::ID);
        println!("bonding curve: {}", bonding_curve);

        let client = get_confirmed_rpc_client();

        let account = client.get_account(&bonding_curve).await;
        println!("account: {:?}", account);
    }
}
