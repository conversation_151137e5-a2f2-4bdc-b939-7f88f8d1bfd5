use solana_sdk::{pubkey::Pubkey, signature::Signature};
use superstack_data::postgres::{indexer::TokenMetadata, Chain, Dex};

use crate::{
    cache::ChainCache,
    solana::utils::{sol_mint, usdc_mint},
};

pub const TOTAL_SUPPLY: u64 = 1_000_000_000;
pub const SOL_DEC_FACTOR: i64 = 1_000_000_000;

/// 1. if exist usdc, usdc must be y,
/// 2. if exist sol but no usdc, sol must be y,
/// 3. if no sol and no usdc, return error
pub fn align_token_pair<T>(
    x_mint: &Pubkey,
    y_mint: &Pubkey,
    x_value: T,
    y_value: T,
) -> anyhow::Result<(T, T)> {
    // 0. if x,y is usdc sol or sol usdc, return (usdc, sol)
    if *x_mint == usdc_mint::ID && *y_mint == sol_mint::ID {
        return Ok((x_value, y_value));
    }
    if *x_mint == sol_mint::ID && *y_mint == usdc_mint::ID {
        return Ok((y_value, x_value));
    }

    // 1. usdc
    if *x_mint == usdc_mint::ID {
        return Ok((y_value, x_value));
    }
    if *y_mint == usdc_mint::ID {
        return Ok((x_value, y_value));
    }

    // 2. sol
    if *x_mint == sol_mint::ID {
        return Ok((y_value, x_value));
    }
    if *y_mint == sol_mint::ID {
        return Ok((x_value, y_value));
    }

    Err(anyhow::anyhow!("Unsupported token pair {:?}/{:?}", x_mint, y_mint))
}

pub fn zero_or_one(token1: &Pubkey, token2: &Pubkey) -> bool {
    if *token1 == usdc_mint::ID && *token2 == sol_mint::ID {
        return true;
    }
    if *token1 == sol_mint::ID && *token2 == usdc_mint::ID {
        return false;
    }

    if *token1 == usdc_mint::ID {
        return false; // usdc must be y
    }
    if *token2 == usdc_mint::ID {
        return true; // usdc must be y
    }

    if *token1 == sol_mint::ID {
        return false; // sol must be y
    }
    if *token2 == sol_mint::ID {
        return true; // sol must be y
    }

    true
}

pub fn use_native_price(x_mint: &Pubkey, y_mint: &Pubkey) -> bool {
    if *x_mint == sol_mint::ID || *y_mint == sol_mint::ID {
        return true;
    }

    false
}

pub async fn construct_token_metadata_and_store(
    chain_cache: &ChainCache,
    address: Pubkey,
    create_dex: Dex,
    create_block_number: Option<u64>,
    create_tx_hash: Option<Signature>,
    create_bonding_curve: Option<Pubkey>,
    create_dev: Option<Pubkey>,
    create_timestamp_millis: Option<i64>,
    migration_pool_address: Option<Pubkey>,
    migration_timestamp_millis: Option<i64>,
    update_timestamp_millis: i64,
) -> anyhow::Result<TokenMetadata> {
    let token_metadata = TokenMetadata::construct_solana_token_metadata_from_rpc(
        address,
        create_dex,
        create_block_number,
        create_tx_hash,
        create_bonding_curve,
        create_dev,
        create_timestamp_millis,
        migration_pool_address,
        migration_timestamp_millis,
        update_timestamp_millis,
        Some(1),
    )
    .await?;

    chain_cache.insert_token_metadata_and_publish_if_not_exists(token_metadata).await
}

pub async fn get_or_fetch_token_metadata(
    address: Pubkey,
    update_timestamp_millis: i64,
    create_dex: Dex,
    create_bonding_curve: Option<Pubkey>,
) -> anyhow::Result<TokenMetadata> {
    let chain_cache = ChainCache::get_cache(Chain::Solana);
    let token_metadata =
        chain_cache.get_token_metadata_or_update_from_db(&address.to_string()).await?;

    if let Some(token_metadata) = token_metadata {
        return Ok(token_metadata);
    }

    construct_token_metadata_and_store(
        chain_cache,
        address,
        create_dex,
        None,
        None,
        create_bonding_curve,
        None,
        None,
        None,
        None,
        update_timestamp_millis,
    )
    .await
}

pub fn is_native_token(mint: &str) -> bool {
    mint == sol_mint::ID.to_string()
}

pub fn is_usd_token(mint: &str) -> bool {
    mint == usdc_mint::ID.to_string()
}
