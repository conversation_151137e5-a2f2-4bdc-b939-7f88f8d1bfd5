pub mod accounts;
pub mod events;

use std::str::FromStr;

use accounts::pool::Pool;
use events::{common::PoolCache, PumpswapEvent};
use num_traits::ToPrimitive;
use solana_sdk::{bs58, declare_id, pubkey::Pubkey, signature::Signature};
use solana_transaction_status_client_types::{
    option_serializer::OptionSerializer, UiInstruction, UiTransactionStatusMeta,
};
use superstack_data::{
    postgres::{
        enums::{Chain, Dex, PoolType},
        indexer::{DexTrade, PoolMetadata, PoolStateDelta, TokenHolderDelta, TokenMetadata},
    },
    utils::to_ui_amount,
};

use crate::cache::{ChainCache, UnsyncDatabaseBuf};

use super::helper::{
    align_token_pair, get_or_fetch_token_metadata, is_native_token, is_usd_token, zero_or_one,
};

declare_id!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");

#[derive(Debug, <PERSON><PERSON>, Copy)]
pub struct Pumpswap {
    pub program_id: Pubkey,
}

impl Pumpswap {
    pub fn new(program_id: Pubkey) -> Self {
        Self { program_id }
    }

    pub fn parse(
        &self,
        tx_meta: &UiTransactionStatusMeta,
    ) -> anyhow::Result<Vec<(usize, PumpswapEvent)>> {
        let mut events = Vec::new();
        if let OptionSerializer::Some(ixs) = &tx_meta.inner_instructions {
            let ixs = ixs.iter().flat_map(|ix| ix.instructions.iter());
            for (ix_idx, ix) in ixs.enumerate() {
                if let UiInstruction::Compiled(ix) = ix {
                    let data = match bs58::decode(ix.data.clone()).into_vec() {
                        Ok(data) => data,
                        Err(_) => continue,
                    };

                    if data.len() > 16 {
                        let event = match PumpswapEvent::from_data(&data) {
                            Ok(event) => event,
                            Err(_) => continue,
                        };

                        events.push((ix_idx, event));
                    }
                }
            }
        }

        Ok(events)
    }

    pub async fn get_or_fetch_metadatas(
        &self,
        pool: &Pubkey,
        create_timestamp_secs: Option<i64>,
    ) -> anyhow::Result<(PoolMetadata, TokenMetadata, TokenMetadata, bool)> {
        let chain_cache = ChainCache::get_cache(Chain::Solana);
        let update_timestamp_millis = chrono::Utc::now().timestamp_millis();

        let pool_metadata =
            match chain_cache.get_pool_metadata_or_update_from_db(&pool.to_string()).await? {
                Some(pool_metadata) => pool_metadata,
                None => {
                    let create_timestamp_millis = create_timestamp_secs.map(|ts| ts * 1000);
                    let account = Pool::fetch(pool).await?;

                    let base_metadata_handle = tokio::spawn(async move {
                        get_or_fetch_token_metadata(
                            account.base_mint,
                            update_timestamp_millis,
                            Dex::Unknown,
                            None,
                        )
                        .await
                    });
                    let quote_metadata_handle = tokio::spawn(async move {
                        get_or_fetch_token_metadata(
                            account.quote_mint,
                            update_timestamp_millis,
                            Dex::Unknown,
                            None,
                        )
                        .await
                    });
                    let base_metadata = base_metadata_handle.await??;
                    let quote_metadata = quote_metadata_handle.await??;

                    let (token_metadata, base_metadata) = align_token_pair(
                        &account.base_mint,
                        &account.quote_mint,
                        base_metadata,
                        quote_metadata,
                    )?;
                    let is_token_first = zero_or_one(&account.base_mint, &account.quote_mint);

                    let pair_label = if is_token_first {
                        format!("{}/{}", token_metadata.symbol, base_metadata.symbol)
                    } else {
                        format!("{}/{}", base_metadata.symbol, token_metadata.symbol)
                    };
                    let pool_metadata = PoolMetadata::new_solana_pool(
                        *pool,
                        pair_label,
                        Dex::Pumpswap,
                        PoolType::None,
                        create_timestamp_millis,
                        update_timestamp_millis,
                        Pubkey::from_str(&token_metadata.address)?,
                        token_metadata.decimals,
                        Pubkey::from_str(&base_metadata.address)?,
                        base_metadata.decimals,
                        is_token_first,
                        None,
                    );
                    let pool_metadata = chain_cache
                        .insert_pool_metadata_and_publish_if_not_exists(pool_metadata)
                        .await?;

                    return Ok((pool_metadata, token_metadata, base_metadata, is_token_first));
                }
            };

        let token_metadata = get_or_fetch_token_metadata(
            Pubkey::from_str(&pool_metadata.token_address)?,
            update_timestamp_millis,
            Dex::Unknown,
            None,
        )
        .await?;
        let base_metadata = get_or_fetch_token_metadata(
            Pubkey::from_str(&pool_metadata.base_address)?,
            update_timestamp_millis,
            Dex::Unknown,
            None,
        )
        .await?;

        let is_token_first = pool_metadata.is_token_first;
        Ok((pool_metadata, token_metadata, base_metadata, is_token_first))
    }

    pub async fn process_event(
        &self,
        event: &(usize, PumpswapEvent),
        tx_hash: &Signature,
        tx_idx: u32,
        slot: u64,
        sol_usd_price: f64,
        unsync_database_buf: &mut UnsyncDatabaseBuf,
    ) -> anyhow::Result<()> {
        let chain_cache = ChainCache::get_cache(Chain::Solana);

        let (ix_idx, event) = event;

        match event {
            // use quote to buy base
            PumpswapEvent::Buy(buy) => {
                let (pool_metadata, token_metadata, base_metadata, is_token_first) =
                    self.get_or_fetch_metadatas(&buy.pool, None).await?;

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;
                let ((base_address, base_decimals), (quote_address, quote_decimals)) =
                    if is_token_first {
                        (
                            (&token_metadata.address, token_metadata.decimals),
                            (&base_metadata.address, base_metadata.decimals),
                        )
                    } else {
                        (
                            (&base_metadata.address, base_metadata.decimals),
                            (&token_metadata.address, token_metadata.decimals),
                        )
                    };

                // Trade
                let is_buy_token = is_token_first;
                let timestamp_millis = buy.timestamp * 1000;
                let trade = DexTrade::new_solana_dex_trade(
                    tx_hash.to_string(),
                    *ix_idx as u32,
                    pool_address.to_string(),
                    buy.user.to_string(),
                    is_buy_token,
                    quote_address.to_string(),
                    buy.quote_amount_in,
                    base_address.to_string(),
                    buy.base_amount_out,
                    slot,
                    timestamp_millis,
                    tx_idx,
                );
                unsync_database_buf.insert_dex_trade(trade);

                // PoolState
                let pool_cache = PoolCache::from(buy);
                let block_number = slot;

                let price = pool_cache.get_swap_price(
                    base_decimals,
                    buy.base_amount_out,
                    quote_decimals,
                    buy.quote_amount_in,
                    is_token_first,
                );

                let market_cap = pool_cache.get_swap_market_cap(
                    token_metadata
                        .supply
                        .to_u64()
                        .ok_or(anyhow::anyhow!("Token supply is too large"))?,
                    base_decimals,
                    buy.base_amount_out,
                    quote_decimals,
                    buy.quote_amount_in,
                    is_token_first,
                );
                let liquidity = pool_cache.get_swap_liquidity(
                    base_decimals,
                    buy.base_amount_out,
                    quote_decimals,
                    buy.quote_amount_in,
                    is_token_first,
                );

                let delta_volume = if is_token_first {
                    to_ui_amount(buy.quote_amount_in, base_metadata.decimals)
                } else {
                    to_ui_amount(buy.base_amount_out, base_metadata.decimals)
                };

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume,
                    delta_buy_volume: if is_token_first { delta_volume } else { 0.0 },
                    delta_sell_volume: if is_token_first { 0.0 } else { delta_volume },
                    delta_txns: 1,
                    delta_buy_txns: if is_token_first { 1 } else { 0 },
                    delta_sell_txns: if is_token_first { 0 } else { 1 },
                    bonding_curve_progress: None,
                };

                let pool_state = chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);

                // Token holder delta
                {
                    let mut token_holder_delta =
                        TokenHolderDelta::new(timestamp_millis, block_number);
                    // if is_token_first, means buy token with quote, quote is sol or usdc
                    if is_token_first {
                        token_holder_delta.bought_amount = buy.base_amount_out.into();
                        token_holder_delta.bought_txns = 1;
                        // spent
                        let spent_token_ui_amount =
                            to_ui_amount(buy.quote_amount_in, quote_decimals);
                        if is_native_token(quote_address) {
                            token_holder_delta.spent_native_token_ui_amount = spent_token_ui_amount;
                        } else if is_usd_token(quote_address) {
                            token_holder_delta.spent_usd_token_ui_amount = spent_token_ui_amount;
                        } else {
                            tracing::warn!("Ignoring unsupported spent token: {:?}", buy);
                        }
                        token_holder_delta.total_spent_usd =
                            token_holder_delta.spent_native_token_ui_amount * sol_usd_price +
                                token_holder_delta.spent_usd_token_ui_amount;
                    } else {
                        // means sell token to buy base, base is sol or usdc
                        token_holder_delta.sold_amount = buy.quote_amount_in.into();
                        token_holder_delta.sold_txns = 1;
                        // received
                        let received_token_ui_amount =
                            to_ui_amount(buy.base_amount_out, base_decimals);
                        if is_native_token(base_address) {
                            token_holder_delta.received_native_token_ui_amount =
                                received_token_ui_amount;
                        } else if is_usd_token(base_address) {
                            token_holder_delta.received_usd_token_ui_amount =
                                received_token_ui_amount;
                        } else {
                            tracing::warn!("Ignoring unsupported received token: {:?}", buy);
                        }
                        token_holder_delta.total_received_usd =
                            token_holder_delta.received_native_token_ui_amount * sol_usd_price +
                                token_holder_delta.received_usd_token_ui_amount;
                    }

                    let token_holder = chain_cache
                        .update_token_holder(
                            &token_metadata.address.to_string(),
                            &buy.user.to_string(),
                            token_holder_delta,
                        )
                        .await?;
                    unsync_database_buf.insert_token_holder(token_holder);
                }
            }
            PumpswapEvent::Sell(sell) => {
                let (pool_metadata, token_metadata, base_metadata, is_token_first) =
                    self.get_or_fetch_metadatas(&sell.pool, None).await?;

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;
                let ((base_address, base_decimals), (quote_address, quote_decimals)) =
                    if is_token_first {
                        (
                            (&token_metadata.address, token_metadata.decimals),
                            (&base_metadata.address, base_metadata.decimals),
                        )
                    } else {
                        (
                            (&base_metadata.address, base_metadata.decimals),
                            (&token_metadata.address, token_metadata.decimals),
                        )
                    };

                // Trade
                let is_buy_token = !is_token_first;
                let trade = DexTrade::new_solana_dex_trade(
                    tx_hash.to_string(),
                    *ix_idx as u32,
                    pool_metadata.pool_address,
                    sell.user.to_string(),
                    is_buy_token,
                    base_address.to_string(),
                    sell.base_amount_in,
                    quote_address.to_string(),
                    sell.quote_amount_out,
                    slot,
                    sell.timestamp * 1000,
                    tx_idx,
                );
                unsync_database_buf.insert_dex_trade(trade);

                // PoolState
                let pool_cache = PoolCache::from(sell);
                let block_number = slot;
                let timestamp_millis = sell.timestamp * 1000;

                let price = pool_cache.get_swap_price(
                    base_decimals,
                    sell.base_amount_in,
                    quote_decimals,
                    sell.quote_amount_out,
                    is_token_first,
                );
                let market_cap = pool_cache.get_swap_market_cap(
                    token_metadata
                        .supply
                        .to_u64()
                        .ok_or(anyhow::anyhow!("Token supply is too large"))?,
                    base_decimals,
                    sell.base_amount_in,
                    quote_decimals,
                    sell.quote_amount_out,
                    is_token_first,
                );
                let liquidity = pool_cache.get_swap_liquidity(
                    base_decimals,
                    sell.base_amount_in,
                    quote_decimals,
                    sell.quote_amount_out,
                    is_token_first,
                );

                let delta_volume = if is_token_first {
                    to_ui_amount(sell.quote_amount_out, base_metadata.decimals)
                } else {
                    to_ui_amount(sell.base_amount_in, base_metadata.decimals)
                };

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume,
                    delta_buy_volume: if is_token_first { 0.0 } else { delta_volume },
                    delta_sell_volume: if is_token_first { delta_volume } else { 0.0 },
                    delta_txns: 1,
                    delta_buy_txns: if is_token_first { 0 } else { 1 },
                    delta_sell_txns: if is_token_first { 1 } else { 0 },
                    bonding_curve_progress: None,
                };

                let pool_state = chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);

                // Token holder delta
                {
                    let mut token_holder_delta =
                        TokenHolderDelta::new(timestamp_millis, block_number);
                    // if is_token_first, means sell token to get quote, quote is sol or usdc
                    if is_token_first {
                        token_holder_delta.sold_amount = sell.base_amount_in.into();
                        token_holder_delta.sold_txns = 1;
                        // received
                        let received_token_ui_amount =
                            to_ui_amount(sell.quote_amount_out, quote_decimals);
                        if is_native_token(quote_address) {
                            token_holder_delta.received_native_token_ui_amount =
                                received_token_ui_amount;
                        } else if is_usd_token(quote_address) {
                            token_holder_delta.received_usd_token_ui_amount =
                                received_token_ui_amount;
                        } else {
                            tracing::warn!("Ignoring unsupported received token: {:?}", sell);
                        }
                        token_holder_delta.total_received_usd =
                            token_holder_delta.received_native_token_ui_amount * sol_usd_price +
                                token_holder_delta.received_usd_token_ui_amount;
                    } else {
                        // means buy token with base, base is sol or usdc
                        token_holder_delta.bought_amount = sell.quote_amount_out.into();
                        token_holder_delta.bought_txns = 1;
                        // spent
                        let spent_token_ui_amount =
                            to_ui_amount(sell.base_amount_in, base_decimals);
                        if is_native_token(base_address) {
                            token_holder_delta.spent_native_token_ui_amount = spent_token_ui_amount;
                        } else if is_usd_token(base_address) {
                            token_holder_delta.spent_usd_token_ui_amount = spent_token_ui_amount;
                        } else {
                            tracing::warn!("Ignoring unsupported spent token: {:?}", sell);
                        }
                        token_holder_delta.total_spent_usd =
                            token_holder_delta.spent_native_token_ui_amount * sol_usd_price +
                                token_holder_delta.spent_usd_token_ui_amount;
                    }
                    let token_holder = chain_cache
                        .update_token_holder(
                            &token_metadata.address.to_string(),
                            &sell.user.to_string(),
                            token_holder_delta,
                        )
                        .await?;
                    unsync_database_buf.insert_token_holder(token_holder);
                }
            }
            PumpswapEvent::CreatePool(create) => {
                tracing::info!("Pumpswap Create Pool: {:?}", create);
                let (mut pool_metadata, token_metadata, base_metadata, is_token_first) =
                    self.get_or_fetch_metadatas(&create.pool, Some(create.timestamp)).await?;

                let create_timestamp_millis = create.timestamp * 1000;
                if pool_metadata.create_timestamp_millis != create_timestamp_millis {
                    pool_metadata.create_timestamp_millis = create_timestamp_millis;
                    chain_cache
                        .update_pool_metadata_create_info_and_publish(pool_metadata.clone())
                        .await?;
                }

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;
                let ((_, base_decimals), (_, quote_decimals)) = if is_token_first {
                    (
                        (token_metadata.address, token_metadata.decimals),
                        (base_metadata.address, base_metadata.decimals),
                    )
                } else {
                    (
                        (base_metadata.address, base_metadata.decimals),
                        (token_metadata.address, token_metadata.decimals),
                    )
                };

                // PoolState
                let pool_cache = PoolCache::from(create);
                let block_number = slot;
                let create_timestamp_millis = create.timestamp * 1000;

                let price =
                    pool_cache.base_per_token(base_decimals, quote_decimals, is_token_first);
                let market_cap = pool_cache.market_cap(
                    token_metadata
                        .supply
                        .to_u64()
                        .ok_or(anyhow::anyhow!("Token supply is too large"))?,
                    base_decimals,
                    quote_decimals,
                    is_token_first,
                );
                let liquidity =
                    pool_cache.total_liquidity(base_decimals, quote_decimals, is_token_first);

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis: create_timestamp_millis,
                    block_number,
                    price: Some(price),
                    market_cap: Some(market_cap),
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 0,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };
                let pool_state = chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await?;
                unsync_database_buf.insert_pool_state(pool_state);
            }
            PumpswapEvent::Deposit(deposit) => {
                let (pool_metadata, token_metadata, base_metadata, is_token_first) =
                    self.get_or_fetch_metadatas(&deposit.pool, None).await?;

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;
                let ((_, base_decimals), (_, quote_decimals)) = if is_token_first {
                    (
                        (&token_metadata.address, token_metadata.decimals),
                        (&base_metadata.address, base_metadata.decimals),
                    )
                } else {
                    (
                        (&base_metadata.address, base_metadata.decimals),
                        (&token_metadata.address, token_metadata.decimals),
                    )
                };

                // PoolState
                let pool_cache = PoolCache::from(deposit);

                let liquidity =
                    pool_cache.total_liquidity(base_decimals, quote_decimals, is_token_first);
                let timestamp_millis = deposit.timestamp * 1000;
                let block_number = slot;

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis,
                    block_number,
                    price: None,
                    market_cap: None,
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 1,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };
                match chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await
                {
                    Ok(pool_state) => {
                        unsync_database_buf.insert_pool_state(pool_state);
                    }
                    Err(e) => {
                        tracing::error!("Error updating pool state for event {:?}: {:?}", event, e);
                    }
                }

                // Token holder delta
                {
                    let mut token_holder_delta =
                        TokenHolderDelta::new(timestamp_millis, block_number);
                    // Deposit reduces token balance but doesn't directly count as buy/sell
                    if is_token_first {
                        token_holder_delta.sold_amount = deposit.base_amount_in.into();
                    } else {
                        token_holder_delta.sold_amount = deposit.quote_amount_in.into();
                    }
                    let token_holder = chain_cache
                        .update_token_holder(
                            &token_metadata.address.to_string(),
                            &deposit.user.to_string(),
                            token_holder_delta,
                        )
                        .await?;
                    unsync_database_buf.insert_token_holder(token_holder);
                }
            }
            PumpswapEvent::Withdraw(withdraw) => {
                let (pool_metadata, token_metadata, base_metadata, is_token_first) =
                    self.get_or_fetch_metadatas(&withdraw.pool, None).await?;

                let pool_address = Pubkey::from_str(&pool_metadata.pool_address)?;
                let ((_, base_decimals), (_, quote_decimals)) = if is_token_first {
                    (
                        (&token_metadata.address, token_metadata.decimals),
                        (&base_metadata.address, base_metadata.decimals),
                    )
                } else {
                    (
                        (&base_metadata.address, base_metadata.decimals),
                        (&token_metadata.address, token_metadata.decimals),
                    )
                };

                // PoolState
                let pool_cache = PoolCache::from(withdraw);
                let liquidity =
                    pool_cache.total_liquidity(base_decimals, quote_decimals, is_token_first);
                let timestamp_millis = withdraw.timestamp * 1000;
                let block_number = slot;

                let pool_state_delta = PoolStateDelta {
                    timestamp_millis,
                    block_number,
                    price: None,
                    market_cap: None,
                    liquidity,
                    delta_volume: 0.0,
                    delta_buy_volume: 0.0,
                    delta_sell_volume: 0.0,
                    delta_txns: 1,
                    delta_buy_txns: 0,
                    delta_sell_txns: 0,
                    bonding_curve_progress: None,
                };
                match chain_cache
                    .update_pool_state(&pool_address.to_string(), pool_state_delta, block_number)
                    .await
                {
                    Ok(pool_state) => {
                        unsync_database_buf.insert_pool_state(pool_state);
                    }
                    Err(e) => {
                        tracing::error!("Error updating pool state for event {:?}: {:?}", event, e);
                    }
                }

                // Token holder delta
                {
                    let mut token_holder_delta =
                        TokenHolderDelta::new(timestamp_millis, block_number);
                    // Withdraw increases token balance but doesn't directly count as buy/sell
                    if is_token_first {
                        token_holder_delta.bought_amount = withdraw.quote_amount_out.into();
                    } else {
                        token_holder_delta.bought_amount = withdraw.base_amount_out.into();
                    }
                    let token_holder = chain_cache
                        .update_token_holder(
                            &token_metadata.address.to_string(),
                            &withdraw.user.to_string(),
                            token_holder_delta,
                        )
                        .await?;
                    unsync_database_buf.insert_token_holder(token_holder);
                }
            }
            _ => {}
        }

        Ok(())
    }
}
