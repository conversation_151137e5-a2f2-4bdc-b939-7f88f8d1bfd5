use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

// 5/13/2025
// {
//     "timestamp": "1747120650",
//     "baseAmountOut": "1537775978",
//     "maxQuoteAmountIn": "295234162218",
//     "userBaseTokenReserves": "0",
//     "userQuoteTokenReserves": "4130790459197",
//     "poolBaseTokenReserves": "************",
//     "poolQuoteTokenReserves": "**************",
//     "quoteAmountIn": "************",
//     "lpFeeBasisPoints": "20",
//     "lpFee": "*********",
//     "protocolFeeBasisPoints": "5",
//     "protocolFee": "********",
//     "quoteAmountInWithLpFee": "************",
//     "userQuoteAmountIn": "************",
//     "pool": "HKGCzF9tfHtcjv2jrxeZvMRNQQeWxG2nT5uw3VW2TGKr",
//     "user": "devNA26Wd3hezzcLEhJ4PtZGcBzy1KYh5Wn3sfnuXkh",
//     "userBaseTokenAccount": "AFNhSMvqi5ZKktkD8jdpeuQsQezzj7BwrDNi6y7pMJf5",
//     "userQuoteTokenAccount": "5WHitdWaFaToNhudxo1rYrtiiC2CG84o9xv8oRpHm7NL",
//     "protocolFeeRecipient": "JCRGumoE9Qi5BBgULTgdgTLjSgkCMSbF62ZZfGs84JeU",
//     "protocolFeeRecipientTokenAccount": "82RyurNbzHQNZ1UTRS9eLAvoR37SZ9FRpyYAiuyTT9Ai",
//     "coinCreator": "11111111111111111111111111111111",
//     "coinCreatorFeeBasisPoints": "5",
//     "coinCreatorFee": "0"
// }
#[derive(Debug, Clone, BorshDeserialize, BorshSerialize)]
pub struct BuyEvent {
    pub timestamp: i64,
    pub base_amount_out: u64,
    pub max_quote_amount_in: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub quote_amount_in: u64,
    pub lp_fee_basis_points: u64,
    pub lp_fee: u64,
    pub protocol_fee_basis_points: u64,
    pub protocol_fee: u64,
    pub quote_amount_in_with_lp_fee: u64,
    pub user_quote_amount_in: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    // 5/13/2025
    pub coin_creator: Pubkey,
    pub coin_creator_fee_basis_points: u64,
    pub coin_creator_fee: u64,
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    use crate::solana::{
        dex::pumpswap::{self, Pumpswap},
        utils::get_transaction_config,
    };

    #[tokio::test]
    async fn test_parse_buy() {
        let tx = "gTuA6FjRvdueYWLDp9wcwdwQn8gpBGKjzR7TYBhYKmhViF9wTPSNMTKDkKe2uBWSSVh5r2dARgoP6En7DQYJmyi";
        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();

        let meta = tx.transaction.meta.unwrap();
        let pumpswap = Pumpswap::new(pumpswap::ID);

        let event = pumpswap.parse(&meta).unwrap();

        println!("{:?}", event);
    }
}
