use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

// 5/13/2025
// {
//     "timestamp": "1747119909",
//     "index": 0,
//     "creator": "2frNEiwJW2zAuyr8xsNJH22itv76DeB3UwkSoPqqGA6X",
//     "baseMint": "Bttvgwq9PZmujg937KSjbf1NcbmHN3EcUxMQHSbfpump",
//     "quoteMint": "So11111111111111111111111111111111111111112",
//     "baseMintDecimals": 6,
//     "quoteMintDecimals": 9,
//     "baseAmountIn": "***************",
//     "quoteAmountIn": "***********",
//     "poolBaseAmount": "***************",
//     "poolQuoteAmount": "***********",
//     "minimumLiquidity": "100",
//     "initialLiquidity": "*************",
//     "lpTokenAmountOut": "*************",
//     "poolBump": 255,
//     "pool": "9YUx6xqkwYnmB3dVVh1e5H6gh4LAbr3PdovKb2usHUgs",
//     "lpMint": "8c5fErBEUT6cxH4DpLneJeHvtpMxzyhxxSAt5otvoXc1",
//     "userBaseTokenAccount": "4AgSARjnVUP16HJBjGUbZ27pMYYNBz7GqDSeFrYfCMiE",
//     "userQuoteTokenAccount": "BF1W7BaZgvwHioRQNTAwrRwgcR98xMzpcsz3ekGRd4Mt",
//     "coinCreator": "EBqqLY4RKBLhy3jwPWF2pGxwpum1PE2jsEe9VnPWFckM"
// }
#[derive(Debug, Clone, BorshDeserialize, BorshSerialize)]
pub struct CreatePoolEvent {
    pub timestamp: i64,
    pub index: u16,
    pub creator: Pubkey,
    pub base_mint: Pubkey,
    pub quote_mint: Pubkey,
    pub base_mint_decimals: u8,
    pub quote_mint_decimals: u8,
    pub base_amount_in: u64,
    pub quote_amount_in: u64,
    pub pool_base_amount: u64,
    pub pool_quote_amount: u64,
    pub minimum_liquidity: u64,
    pub initial_liquidity: u64,
    pub lp_token_amount_out: u64,
    pub pool_bump: u8,
    pub pool: Pubkey,
    pub lp_mint: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    // 5/13/2025
    pub coin_creator: Pubkey,
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    use crate::solana::{
        dex::pumpswap::{self, Pumpswap},
        utils::get_transaction_config,
    };

    #[tokio::test]
    async fn test_parse_create_pool() {
        let tx = "4R7yfNvRNhcTKqCqcVGZrK4GBeARHESzAo27Si7y9rqn1nLsVncM6Rt7zqfXPJHtyrVFrrZFM236iVSwys5j7gTW";
        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();

        let meta = tx.transaction.meta.unwrap();
        let pumpswap = Pumpswap::new(pumpswap::ID);

        let event = pumpswap.parse(&meta).unwrap();

        println!("{:?}", event);
    }
}
