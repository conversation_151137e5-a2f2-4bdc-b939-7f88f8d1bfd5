use borsh::BorshDeserialize;
use solana_sdk::pubkey::Pubkey;

pub mod buy;
pub mod common;
pub mod create_config;
pub mod create_pool;
pub mod deposit;
pub mod disable;
pub mod extend_account;
pub mod sell;
pub mod update_admin;
pub mod update_fee_config;
pub mod withdraw;

use buy::BuyEvent;
use create_config::CreateConfigEvent;
use create_pool::CreatePoolEvent;
use deposit::DepositEvent;
use disable::DisableEvent;
use extend_account::ExtendAccountEvent;
use sell::SellEvent;
use update_admin::UpdateAdminEvent;
use update_fee_config::UpdateFeeConfigEvent;
use withdraw::WithdrawEvent;

pub mod discriminator {
    pub const BUY_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 103, 244, 82, 31, 44, 245, 119, 119];
    pub const CREATE_CONFIG_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 107, 52, 89, 129, 55, 226, 81, 22];
    pub const CREATE_POOL_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 177, 49, 12, 210, 160, 118, 167, 116];
    pub const DEPOSIT_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 120, 248, 61, 83, 31, 142, 107, 144];
    pub const DISABLE_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 107, 253, 193, 76, 228, 202, 27, 104];
    pub const EXTEND_ACCOUNT_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 97, 97, 215, 144, 93, 146, 22, 124];
    pub const SELL_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 62, 47, 55, 10, 165, 3, 220, 42];
    pub const UPDATE_ADMIN_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 225, 152, 171, 87, 246, 63, 66, 234];
    pub const UPDATE_FEE_CONFIG_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 90, 23, 65, 35, 62, 244, 188, 208];
    pub const WITHDRAW_EVENT: [u8; 16] =
        [228, 69, 165, 46, 81, 203, 154, 29, 22, 9, 133, 26, 160, 44, 71, 192];
}

#[derive(Debug, Clone)]
pub enum PumpswapEvent {
    Buy(BuyEvent),
    CreateConfig(CreateConfigEvent),
    CreatePool(CreatePoolEvent),
    Deposit(DepositEvent),
    Disable(DisableEvent),
    ExtendAccount(ExtendAccountEvent),
    Sell(SellEvent),
    UpdateAdmin(UpdateAdminEvent),
    UpdateFeeConfig(UpdateFeeConfigEvent),
    Withdraw(WithdrawEvent),
}

impl PumpswapEvent {
    pub fn from_data(data: &[u8]) -> anyhow::Result<Self> {
        let event_discriminator = &data[0..16];
        let event_data = &data[16..];

        match event_discriminator {
            x if x == discriminator::BUY_EVENT => {
                Ok(Self::Buy(BuyEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::CREATE_CONFIG_EVENT => {
                Ok(Self::CreateConfig(CreateConfigEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::CREATE_POOL_EVENT => {
                Ok(Self::CreatePool(CreatePoolEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::DEPOSIT_EVENT => {
                Ok(Self::Deposit(DepositEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::DISABLE_EVENT => {
                Ok(Self::Disable(DisableEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::EXTEND_ACCOUNT_EVENT => {
                Ok(Self::ExtendAccount(ExtendAccountEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::SELL_EVENT => {
                Ok(Self::Sell(SellEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::UPDATE_ADMIN_EVENT => {
                Ok(Self::UpdateAdmin(UpdateAdminEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::UPDATE_FEE_CONFIG_EVENT => {
                Ok(Self::UpdateFeeConfig(UpdateFeeConfigEvent::try_from_slice(event_data)?))
            }
            x if x == discriminator::WITHDRAW_EVENT => {
                Ok(Self::Withdraw(WithdrawEvent::try_from_slice(event_data)?))
            }
            _ => Err(anyhow::anyhow!("Unknown event {:?}", event_discriminator)),
        }
    }

    pub fn key(&self) -> Pubkey {
        match self {
            // Core events
            Self::CreatePool(create_pool) => create_pool.pool,
            Self::Buy(buy) => buy.pool,
            Self::Sell(sell) => sell.pool,
            Self::Deposit(deposit) => deposit.pool,
            Self::Withdraw(withdraw) => withdraw.pool,
            // Other events
            Self::CreateConfig(create_config) => create_config.admin,
            Self::Disable(disable) => disable.admin,
            Self::ExtendAccount(extend_account) => extend_account.account,
            Self::UpdateAdmin(update_admin) => update_admin.admin,
            Self::UpdateFeeConfig(update_fee_config) => update_fee_config.admin,
        }
    }
}
