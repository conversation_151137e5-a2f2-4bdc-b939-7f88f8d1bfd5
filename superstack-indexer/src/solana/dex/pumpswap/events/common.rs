use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

use superstack_data::utils::to_ui_amount;

use super::{
    buy::BuyEvent, create_pool::CreatePoolEvent, deposit::DepositEvent, sell::SellEvent,
    withdraw::WithdrawEvent,
};

#[derive(Debug, <PERSON>lone, BorshDeserialize, BorshSerialize)]
pub struct PoolCache {
    pub timestamp: i64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub lp_fee_basis_points: Option<u64>,
    pub lp_fee: Option<u64>,
    pub protocol_fee_basis_points: Option<u64>,
    pub protocol_fee: Option<u64>,
    pub pool: Pubkey,
    pub protocol_fee_recipient: Option<Pubkey>,
    pub protocol_fee_recipient_token_account: Option<Pubkey>,
}

impl PoolCache {
    pub fn get_swap_price(
        &self,
        base_decimals: u8,
        base_amount: u64,
        quote_decimals: u8,
        quote_amount: u64,
        is_token_first: bool,
    ) -> f64 {
        let base_amount_ui = to_ui_amount(base_amount, base_decimals);
        let quote_amount_ui = to_ui_amount(quote_amount, quote_decimals);

        if is_token_first {
            quote_amount_ui / base_amount_ui
        } else {
            base_amount_ui / quote_amount_ui
        }
    }

    pub fn get_swap_market_cap(
        &self,
        supply: u64,
        base_decimals: u8,
        base_amount: u64,
        quote_decimals: u8,
        quote_amount: u64,
        is_token_first: bool,
    ) -> f64 {
        let price = self.get_swap_price(
            base_decimals,
            base_amount,
            quote_decimals,
            quote_amount,
            is_token_first,
        );
        if is_token_first {
            to_ui_amount(supply, base_decimals) * price
        } else {
            to_ui_amount(supply, quote_decimals) * price
        }
    }

    pub fn get_swap_liquidity(
        &self,
        base_decimals: u8,
        base_amount: u64,
        quote_decimals: u8,
        quote_amount: u64,
        is_token_first: bool,
    ) -> f64 {
        let price = self.get_swap_price(
            base_decimals,
            base_amount,
            quote_decimals,
            quote_amount,
            is_token_first,
        );
        if is_token_first {
            to_ui_amount(self.pool_base_token_reserves, base_decimals) * price +
                to_ui_amount(self.pool_quote_token_reserves, quote_decimals)
        } else {
            to_ui_amount(self.pool_quote_token_reserves, quote_decimals) * price +
                to_ui_amount(self.pool_base_token_reserves, base_decimals)
        }
    }

    pub fn base_per_token(&self, base_decimals: u8, quote_decimals: u8, zero_or_one: bool) -> f64 {
        let pool_base_token_reserves_ui =
            to_ui_amount(self.pool_base_token_reserves, base_decimals);
        let pool_quote_token_reserves_ui =
            to_ui_amount(self.pool_quote_token_reserves, quote_decimals);

        if zero_or_one {
            pool_quote_token_reserves_ui / pool_base_token_reserves_ui
        } else {
            pool_base_token_reserves_ui / pool_quote_token_reserves_ui
        }
    }
    // sol amount => token amount
    pub fn get_buy_price(&self, amount: u64) -> u64 {
        if amount == 0 {
            return 0;
        }

        let n: u128 =
            (self.pool_base_token_reserves as u128) * (self.pool_quote_token_reserves as u128);
        let i: u128 = (self.pool_base_token_reserves as u128) + (amount as u128);
        let r: u128 = n / i + 1;
        let s: u128 = (self.pool_quote_token_reserves as u128) - r;

        let s_u64 = s as u64;
        if s_u64 < self.pool_base_token_reserves {
            s_u64
        } else {
            self.pool_base_token_reserves
        }
    }

    // token amount => sol amount
    pub fn get_sell_price(&self, amount: u64) -> u64 {
        if amount == 0 {
            return 0;
        }

        let n: u128 = (amount as u128) * (self.pool_quote_token_reserves as u128) /
            (self.pool_base_token_reserves as u128 + amount as u128);

        let n = match self.lp_fee_basis_points {
            Some(fee) => {
                let a = (n * (fee as u128)) / 10000;
                n - a
            }
            None => n,
        };

        n as u64
    }

    pub fn market_cap(
        &self,
        supply: u64,
        base_decimals: u8,
        quote_decimals: u8,
        zero_or_one: bool,
    ) -> f64 {
        let price = self.base_per_token(base_decimals, quote_decimals, zero_or_one);
        if zero_or_one {
            to_ui_amount(supply, base_decimals) * price
        } else {
            to_ui_amount(supply, quote_decimals) * price
        }
    }

    pub fn total_liquidity(&self, base_decimals: u8, quote_decimals: u8, zero_or_one: bool) -> f64 {
        let price = self.base_per_token(base_decimals, quote_decimals, zero_or_one);

        if zero_or_one {
            to_ui_amount(self.pool_base_token_reserves, base_decimals) * price +
                to_ui_amount(self.pool_quote_token_reserves, quote_decimals)
        } else {
            to_ui_amount(self.pool_quote_token_reserves, base_decimals) * price +
                to_ui_amount(self.pool_base_token_reserves, quote_decimals)
        }
    }
}

impl From<&BuyEvent> for PoolCache {
    fn from(event: &BuyEvent) -> Self {
        Self {
            timestamp: event.timestamp,
            pool_base_token_reserves: event.pool_base_token_reserves - event.base_amount_out,
            pool_quote_token_reserves: event.pool_quote_token_reserves + event.quote_amount_in,
            lp_fee_basis_points: Some(event.lp_fee_basis_points),
            lp_fee: Some(event.lp_fee),
            protocol_fee_basis_points: Some(event.protocol_fee_basis_points),
            protocol_fee: Some(event.protocol_fee),
            pool: event.pool,
            protocol_fee_recipient: Some(event.protocol_fee_recipient),
            protocol_fee_recipient_token_account: Some(event.protocol_fee_recipient_token_account),
        }
    }
}

impl From<&SellEvent> for PoolCache {
    fn from(event: &SellEvent) -> Self {
        Self {
            timestamp: event.timestamp,
            pool_base_token_reserves: event.pool_base_token_reserves + event.base_amount_in,
            pool_quote_token_reserves: event.pool_quote_token_reserves - event.quote_amount_out,
            lp_fee_basis_points: Some(event.lp_fee_basis_points),
            lp_fee: Some(event.lp_fee),
            protocol_fee_basis_points: Some(event.protocol_fee_basis_points),
            protocol_fee: Some(event.protocol_fee),
            pool: event.pool,
            protocol_fee_recipient: Some(event.protocol_fee_recipient),
            protocol_fee_recipient_token_account: Some(event.protocol_fee_recipient_token_account),
        }
    }
}

impl From<&WithdrawEvent> for PoolCache {
    fn from(event: &WithdrawEvent) -> Self {
        Self {
            timestamp: event.timestamp,
            pool_base_token_reserves: event.pool_base_token_reserves - event.base_amount_out,
            pool_quote_token_reserves: event.pool_quote_token_reserves - event.quote_amount_out,
            pool: event.pool,
            lp_fee_basis_points: None,
            lp_fee: None,
            protocol_fee_basis_points: None,
            protocol_fee: None,
            protocol_fee_recipient: None,
            protocol_fee_recipient_token_account: None,
        }
    }
}

impl From<&DepositEvent> for PoolCache {
    fn from(value: &DepositEvent) -> Self {
        Self {
            timestamp: value.timestamp,
            pool_base_token_reserves: value.pool_base_token_reserves + value.base_amount_in,
            pool_quote_token_reserves: value.pool_quote_token_reserves + value.quote_amount_in,
            lp_fee_basis_points: None,
            lp_fee: None,
            protocol_fee_basis_points: None,
            protocol_fee: None,
            pool: value.pool,
            protocol_fee_recipient: None,
            protocol_fee_recipient_token_account: None,
        }
    }
}

impl From<&CreatePoolEvent> for PoolCache {
    fn from(event: &CreatePoolEvent) -> Self {
        Self {
            timestamp: event.timestamp,
            pool_base_token_reserves: event.pool_base_amount,
            pool_quote_token_reserves: event.pool_quote_amount,
            lp_fee_basis_points: None,
            lp_fee: None,
            protocol_fee_basis_points: None,
            protocol_fee: None,
            pool: event.pool,
            protocol_fee_recipient: None,
            protocol_fee_recipient_token_account: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use solana_sdk::pubkey::Pubkey;

    use crate::solana::dex::{helper::TOTAL_SUPPLY, pumpswap::events::common::PoolCache};

    #[test]
    fn test_sol_price_2() {
        let pool = PoolCache {
            timestamp: **********,
            pool_base_token_reserves: ***************,
            pool_quote_token_reserves: ***********,
            lp_fee_basis_points: Some(20),
            lp_fee: Some(*********),
            protocol_fee_basis_points: Some(5),
            protocol_fee: Some(********),
            pool: Pubkey::default(),
            protocol_fee_recipient: None,
            protocol_fee_recipient_token_account: None,
        };

        let test_price = pool.base_per_token(6, 9, true);
        println!("test_price: {}", test_price);
        let market_cap = pool.market_cap(TOTAL_SUPPLY, 6, 9, true);
        println!("market_cap: {}", market_cap);
        let total_liquidity = pool.total_liquidity(6, 9, true);
        println!("total_liquidity: {}", total_liquidity);
    }

    #[test]
    fn test_sol_price() {
        let pool = PoolCache {
            timestamp: **********,
            pool_base_token_reserves: **********,
            pool_quote_token_reserves: ***************,
            lp_fee_basis_points: Some(20),
            lp_fee: Some(*********),
            protocol_fee_basis_points: Some(5),
            protocol_fee: Some(********),
            pool: Pubkey::default(),
            protocol_fee_recipient: None,
            protocol_fee_recipient_token_account: None,
        };

        let test_price = pool.base_per_token(9, 6, false);
        println!("test_price: {}", test_price * 170.0);
        let market_cap = pool.market_cap(TOTAL_SUPPLY, 9, 6, false);
        println!("market_cap: {}", market_cap * 170.0);
        let total_liquidity = pool.total_liquidity(9, 6, false);
        println!("total_liquidity: {}", total_liquidity * 170.0);

        println!("--------------------------------");

        let pool = PoolCache {
            timestamp: **********,
            pool_base_token_reserves: ***************,
            pool_quote_token_reserves: **********,
            lp_fee_basis_points: Some(20),
            lp_fee: Some(*********),
            protocol_fee_basis_points: Some(5),
            protocol_fee: Some(********),
            pool: Pubkey::default(),
            protocol_fee_recipient: None,
            protocol_fee_recipient_token_account: None,
        };

        let test_price = pool.base_per_token(6, 9, true);
        println!("test_price: {}", test_price * 170.0);
        let market_cap = pool.market_cap(TOTAL_SUPPLY, 6, 9, true);
        println!("market_cap: {}", market_cap * 170.0);
        let total_liquidity = pool.total_liquidity(6, 9, true);
        println!("total_liquidity: {}", total_liquidity * 170.0);
    }
}
