use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

// 05/13/2025
// {
//     "timestamp": "1747120717",
//     "baseAmountIn": "130749483673",
//     "minQuoteAmountOut": "104605831",
//     "userBaseTokenReserves": "130749483673",
//     "userQuoteTokenReserves": "0",
//     "poolBaseTokenReserves": "***************",
//     "poolQuoteTokenReserves": "************",
//     "quoteAmountOut": "*********",
//     "lpFeeBasisPoints": "20",
//     "lpFee": "216599",
//     "protocolFeeBasisPoints": "5",
//     "protocolFee": "54150",
//     "quoteAmountOutWithoutLpFee": "*********",
//     "userQuoteAmountOut": "*********",
//     "pool": "9nm364YMfG6SdEw8QeMYTUTR9eRKj9WAxsTUwkqa98Dn",
//     "user": "13cJLBRjMLkdVNXF4XK3EeWRucUW6Jnmse15PRAwCfuR",
//     "userBaseTokenAccount": "2C4tf84FmXiHVKwv2Gcks2TGBvkgox9pFyrj1oDTncCn",
//     "userQuoteTokenAccount": "2o7cP3TQzt5xwHgtPM7hqfWRtpF8L8Ln8VcAVHQtaYia",
//     "protocolFeeRecipient": "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV",
//     "protocolFeeRecipientTokenAccount": "94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb",
//     "coinCreator": "F9YywaYftfgVe1d63EsCQwk2TCLMQyuKYK2SpXSgFbDa",
//     "coinCreatorFeeBasisPoints": "5",
//     "coinCreatorFee": "54150"
//   }
#[derive(Debug, Clone, BorshDeserialize, BorshSerialize)]
pub struct SellEvent {
    pub timestamp: i64,
    pub base_amount_in: u64,
    pub min_quote_amount_out: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub quote_amount_out: u64,
    pub lp_fee_basis_points: u64,
    pub lp_fee: u64,
    pub protocol_fee_basis_points: u64,
    pub protocol_fee: u64,
    pub quote_amount_out_without_lp_fee: u64,
    pub user_quote_amount_out: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    // 5/13/2025
    pub coin_creator: Pubkey,
    pub coin_creator_fee_basis_points: u64,
    pub coin_creator_fee: u64,
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use solana_sdk::signature::Signature;
    use superstack_data::utils::get_confirmed_rpc_client;

    use crate::solana::{
        dex::pumpswap::{self, Pumpswap},
        utils::get_transaction_config,
    };

    #[tokio::test]
    async fn test_parse_sell() {
        let tx = "HBpxdP3ZckHuNqCJ1k4Fyv9YC6oy5ADRXoK3STWY5ceSS2of5R25qSUpJfesKzcnMrEehaEgpmgYqzMzuYNCW1D";
        let tx_sig = Signature::from_str(tx).unwrap();

        let client = get_confirmed_rpc_client();

        let tx =
            client.get_transaction_with_config(&tx_sig, get_transaction_config()).await.unwrap();

        let meta = tx.transaction.meta.unwrap();
        let pumpswap = Pumpswap::new(pumpswap::ID);

        let event = pumpswap.parse(&meta).unwrap();

        println!("{:?}", event);
    }
}
