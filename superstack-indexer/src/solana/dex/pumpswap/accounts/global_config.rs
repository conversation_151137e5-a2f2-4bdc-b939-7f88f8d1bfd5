use borsh::{<PERSON>rshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

/// Global configuration account for PumpSwap
#[derive(Debug, <PERSON><PERSON>, BorshSerialize, BorshDeserialize)]
pub struct GlobalConfig {
    /// The admin pubkey
    pub admin: Pubkey,
    /// The lp fee in basis points (0.01%)
    pub lp_fee_basis_points: u64,
    /// The protocol fee in basis points (0.01%)
    pub protocol_fee_basis_points: u64,
    /// Flags to disable certain functionality
    /// bit 0 - Disable create pool
    /// bit 1 - Disable deposit
    /// bit 2 - Disable withdraw
    /// bit 3 - Disable buy
    /// bit 4 - Disable sell
    pub disable_flags: u8,
    /// Addresses of the protocol fee recipients
    pub protocol_fee_recipients: [Pubkey; 8],
}

impl GlobalConfig {
    pub const DISCRIMINATOR: [u8; 8] = [149, 8, 156, 202, 160, 252, 176, 217];

    pub fn is_create_pool_disabled(&self) -> bool {
        (self.disable_flags & (1 << 0)) != 0
    }

    pub fn is_deposit_disabled(&self) -> bool {
        (self.disable_flags & (1 << 1)) != 0
    }

    pub fn is_withdraw_disabled(&self) -> bool {
        (self.disable_flags & (1 << 2)) != 0
    }

    pub fn is_buy_disabled(&self) -> bool {
        (self.disable_flags & (1 << 3)) != 0
    }

    pub fn is_sell_disabled(&self) -> bool {
        (self.disable_flags & (1 << 4)) != 0
    }
}
