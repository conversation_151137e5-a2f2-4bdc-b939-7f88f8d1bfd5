use backon::{ExponentialBuilder, Retryable};
use borsh::{BorshDeserialize, BorshSerialize};
use serde::{Deserialize, Serialize};
use solana_sdk::{program_pack::Pack, pubkey::Pubkey};
use spl_token::state::Account as TokenAccount;
use superstack_data::utils::get_confirmed_rpc_client;

/// Pool account for PumpSwap
#[derive(Debug, Clone, BorshSerialize, BorshDeserialize, Default, Serialize, Deserialize)]
pub struct Pool {
    pub discriminator: u64,
    /// Bump seed for pool PDA
    pub pool_bump: u8,
    /// Pool index
    pub index: u16,
    /// Creator of the pool
    pub creator: Pubkey,
    /// Base token mint
    pub base_mint: Pubkey,
    /// Quote token mint
    pub quote_mint: Pubkey,
    /// LP token mint
    pub lp_mint: Pubkey,
    /// Pool base token account
    pub pool_base_token_account: Pubkey,
    /// Pool quote token account
    pub pool_quote_token_account: Pubkey,
    /// True circulating supply without burns and lock-ups
    pub lp_supply: u64,
}

// holder pumpfun
impl Pool {
    pub const DISCRIMINATOR: [u8; 8] = [241, 154, 109, 4, 17, 177, 109, 188];

    pub async fn fetch(pubkey: &Pubkey) -> anyhow::Result<Self> {
        let fetch = || async {
            let client = get_confirmed_rpc_client();

            let account = client.get_account(pubkey).await?;
            let pool = account.deserialize_data::<Pool>()?;

            Ok::<_, anyhow::Error>(pool)
        };

        let pool = fetch.retry(ExponentialBuilder::default()).await?;
        Ok(pool)
    }

    pub async fn fetch_token_account_balance(pubkey: &Pubkey) -> anyhow::Result<u64> {
        let fetch = || async {
            let client = get_confirmed_rpc_client();
            let data = client.get_account_data(pubkey).await?;
            let balance = TokenAccount::unpack(&data[0..165])?.amount;
            Ok::<_, anyhow::Error>(balance)
        };

        let balance = fetch.retry(ExponentialBuilder::default()).await?;
        Ok(balance)
    }

    /// Gets the seeds for the pool account PDA
    pub fn get_seeds<'a>(
        index: &'a [u8],
        creator: &'a Pubkey,
        base_mint: &'a Pubkey,
        quote_mint: &'a Pubkey,
    ) -> [&'a [u8]; 5] {
        [b"pool", index, creator.as_ref(), base_mint.as_ref(), quote_mint.as_ref()]
    }

    // const getPriceAndLiquidity = async (pool: Pool) => {
    //     const wsolAddress = pool.poolData.poolQuoteTokenAccount;
    //     const tokenAddress = pool.poolData.poolBaseTokenAccount;

    //     const wsolBalance = await connection.getTokenAccountBalance(wsolAddress);
    //     const tokenBalance = await connection.getTokenAccountBalance(tokenAddress);

    //     const price = wsolBalance.value.uiAmount! / tokenBalance.value.uiAmount!;

    //     return {
    //         ...pool,
    //         price,
    //         reserves: {
    //             native: wsolBalance.value.uiAmount!,
    //             token: tokenBalance.value.uiAmount!
    //         }
    //     } as PoolWithPrice;
    // }
}

#[tokio::test]
async fn test_fetch_pool() {
    use std::str::FromStr;
    dotenv::dotenv().ok();

    let pool_address = Pubkey::from_str("61DtTvjeTuwcAdtGsiQLvBRqrJ56FdiNdhNcEun9c1ci").unwrap();

    let pool = Pool::fetch(&pool_address).await.unwrap();
    println!("{:#?}", pool);
}
