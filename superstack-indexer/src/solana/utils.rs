use std::time::Duration;

use solana_client::rpc_config::{RpcBlockConfig, RpcTransactionConfig};
use solana_sdk::commitment_config::CommitmentConfig;
use solana_transaction_status_client_types::UiTransactionEncoding;

pub fn eta(block_limit: usize, start: u64, latest: u64, elapsed: Duration) -> Duration {
    let remaining = latest - start;
    let avg_time = elapsed.as_secs_f64() / block_limit as f64;
    let remaining_time = remaining as f64 * avg_time;
    Duration::from_secs_f64(remaining_time)
}

pub fn get_block_config() -> RpcBlockConfig {
    RpcBlockConfig {
        max_supported_transaction_version: Some(0),
        encoding: Some(UiTransactionEncoding::Base64),
        commitment: Some(CommitmentConfig::confirmed()),
        ..Default::default()
    }
}

pub fn get_transaction_config() -> RpcTransactionConfig {
    RpcTransactionConfig {
        max_supported_transaction_version: Some(0),
        encoding: Some(UiTransactionEncoding::Base64),
        commitment: Some(CommitmentConfig::confirmed()),
        ..Default::default()
    }
}

pub mod sol_mint {
    use solana_sdk::declare_id;
    declare_id!("So11111111111111111111111111111111111111112");

    pub const DECIMALS: u8 = 9;
    pub const LAMPORT_PER_TOKEN: u64 = 10_u64.pow(DECIMALS as u32);

    pub const fn to_ui_amount(amount: u64) -> f64 {
        amount as f64 / 10_u64.pow(DECIMALS as u32) as f64
    }
}

pub mod usdc_mint {
    use solana_sdk::declare_id;
    declare_id!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");

    pub const DECIMALS: u8 = 6;
    pub const LAMPORT_PER_TOKEN: u64 = 10_u64.pow(DECIMALS as u32);

    pub const fn to_ui_amount(amount: u64) -> f64 {
        amount as f64 / 10_u64.pow(DECIMALS as u32) as f64
    }
}

pub mod void_mint {
    use solana_sdk::declare_id;
    declare_id!("9qLKQ58eWtRFV9SxAQ5MpU91KunYxdtkgUzRvAcJ9Rwj");
}
