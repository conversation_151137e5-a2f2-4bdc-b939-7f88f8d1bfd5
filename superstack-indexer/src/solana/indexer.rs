use std::str::FromStr;

use backon::{ExponentialBuilder, Retryable};
use solana_client::rpc_client::GetConfirmedSignaturesForAddress2Config;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use solana_transaction_status_client_types::{EncodedTransactionWithStatusMeta, UiConfirmedBlock};
use superstack_data::{
    postgres::{Chain, PostgresDatabase},
    utils::get_confirmed_rpc_client,
};
use tokio::{sync::mpsc, task::JoinSet};

use crate::{config::Config, store_task::StoreTaskMessage};

use super::{
    dex::SolanaDexConsumer,
    utils::{eta, get_block_config, get_transaction_config},
};

pub type SolanaConsumer = mpsc::Sender<UiConfirmedBlock>;

pub struct SolanaIndexer {
    pub consumer: SolanaConsumer,
    pub from_slot: Option<u64>,
    pub block_limit: usize,
}

impl SolanaIndexer {
    const BLOCK_LIMIT: usize = 100;
    const START_SLOT: u64 = 341329800; // May 20, 2025 18:59:00 +UTC

    pub fn init(
        config: &Config,
        store_channel_sender: mpsc::Sender<StoreTaskMessage>,
    ) -> anyhow::Result<Self> {
        let from_slot = config.solana_from_slot;
        let block_limit = config.solana_block_limit.unwrap_or(Self::BLOCK_LIMIT);
        let channel_size = block_limit;
        let consumer = SolanaDexConsumer::init(channel_size, store_channel_sender)?;

        Ok(Self { consumer, from_slot, block_limit })
    }

    async fn handle_blocks(
        &self,
        blocks: Vec<UiConfirmedBlock>,
        handled_slot: &mut u64,
    ) -> anyhow::Result<()> {
        for b in blocks {
            if b.parent_slot <= *handled_slot {
                continue;
            }
            *handled_slot = b.parent_slot;
            self.consumer.send(b).await?;
        }

        Ok(())
    }

    pub async fn run(self) {
        let client = get_confirmed_rpc_client();

        let db = PostgresDatabase::get_indexer_db().await;
        let db_latest_slot = db
            .get_chain_info(Chain::Solana)
            .await
            .expect("Failed to get chain info from database")
            .map(|c| c.block_number);

        let mut cur = match (db_latest_slot, self.from_slot) {
            (Some(db_latest_slot), Some(from_slot)) => from_slot.max(db_latest_slot),
            (Some(db_latest_slot), None) => db_latest_slot + 1,
            (None, Some(from_slot)) => from_slot,
            (None, None) => Self::START_SLOT,
        };

        tracing::info!("Starting solana indexer from slot {cur}");
        let mut handled_slot = 0;
        loop {
            let latest = match client.get_slot_with_commitment(CommitmentConfig::confirmed()).await
            {
                Ok(height) => height,
                Err(e) => {
                    tracing::error!("Error getting block height (getSlot): {}", e);
                    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                    continue;
                }
            };

            if latest <= cur {
                tracing::debug!("No new blocks to index (latest: {}, cur: {})", latest, cur);
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                continue;
            }

            let (start, end) = (cur, latest);

            tracing::info!("🚀 Indexing with {start} to {end}");
            for st in (start..end).step_by(self.block_limit) {
                let now = std::time::Instant::now();
                let blocks = match self.get_blocks(st, self.block_limit).await {
                    Ok(blocks) => blocks,
                    Err(e) => {
                        tracing::error!("Error getting blocks: {}", e);
                        break;
                    }
                };
                let block_len = blocks.len();
                tracing::info!(
                    "🕸️ Got {} blocks from {} to {} in {:?} , eta {:?}",
                    block_len,
                    st,
                    (st + self.block_limit as u64).min(end),
                    now.elapsed(),
                    eta(self.block_limit, st, latest, now.elapsed())
                );

                self.handle_blocks(blocks, &mut handled_slot).await.unwrap();

                cur = (st + self.block_limit as u64).min(latest);
            }
        }
    }

    pub async fn get_blocks(
        &self,
        start: u64,
        limit: usize,
    ) -> anyhow::Result<Vec<UiConfirmedBlock>> {
        let client = get_confirmed_rpc_client();

        let slots = client
            .get_blocks_with_limit_and_commitment(start, limit, CommitmentConfig::confirmed())
            .await
            .map_err(|e| anyhow::anyhow!("Error getting blocks (getBlocksWithLimit): {}", e))?;

        tracing::info!("🕸️ Got slots: {slots:?}");

        let mut tasks = JoinSet::new();
        for s in slots {
            let fut = move || async move {
                let b = get_confirmed_rpc_client()
                    .get_block_with_config(s, get_block_config())
                    .await
                    .map_err(|e| anyhow::anyhow!("Error getting block (getBlock): {}", e))?;
                tracing::debug!("🕸️ Got block: {s} {:?}", b.parent_slot);
                Ok::<_, anyhow::Error>(b)
            };
            tasks.spawn(fut.retry(ExponentialBuilder::default()));
        }

        let mut blocks = tasks.join_all().await.into_iter().collect::<Result<Vec<_>, _>>()?;
        blocks.sort_by_key(|b| b.parent_slot);

        Ok(blocks)
    }

    pub async fn get_transactions_by_program_id(
        &self,
        program_id: &Pubkey,
        start: &Signature,
    ) -> anyhow::Result<(Signature, Vec<EncodedTransactionWithStatusMeta>)> {
        let client = get_confirmed_rpc_client();
        let mut first = *start;
        let sigs = client
            .get_signatures_for_address_with_config(
                program_id,
                GetConfirmedSignaturesForAddress2Config {
                    before: Some(*start),
                    until: None,
                    limit: Some(50),
                    commitment: Some(CommitmentConfig::confirmed()),
                },
            )
            .await?;

        let mut txs = Vec::new();

        if let Some(first_sig) = sigs.first() {
            let sig = Signature::from_str(&first_sig.signature)?;
            first = sig;
        }

        for sig in sigs {
            let sig = Signature::from_str(&sig.signature)?;
            let tx = client
                .get_transaction_with_config(&sig, get_transaction_config())
                .await?
                .transaction;
            txs.push(tx);
        }

        Ok((first, txs))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_blocks() {
        dotenv::dotenv().ok();
        tracing_subscriber::fmt::init();

        let indexer = SolanaIndexer::init(Config::get(), mpsc::channel(100).0).unwrap();
        let blocks = indexer.get_blocks(340984508, 10).await.unwrap();

        for b in blocks {
            println!("block: {:?}", b.block_height);
        }
    }
}
