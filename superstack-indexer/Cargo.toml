[package]
name = "superstack-indexer"
version.workspace = true
edition.workspace = true

[dependencies]
# Solana
solana-client = { workspace = true }
solana-sdk = { workspace = true }
solana-transaction-status-client-types = { workspace = true }
spl-token = { workspace = true }
spl-token-2022 = { workspace = true }
borsh = { workspace = true }

# EVM
alloy = { workspace = true, features = [
    "provider-ws",
    "rpc-types",
    "contract",
] }
uniswap-v3-sdk = { version = "*", features = ["parse_price"] }

tokio = { workspace = true }
tokio-tungstenite = { workspace = true, features = ["native-tls"] }
futures-util = { workspace = true }
axum = { workspace = true, features = ["macros"] }
reqwest = { workspace = true }
serde = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
anyhow = { workspace = true }
dotenv = { workspace = true }
backon = { workspace = true }
rust_decimal = { workspace = true }
num-traits = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
quick_cache = { workspace = true }
bigdecimal = { workspace = true }

coingecko = { workspace = true }
cmc = { workspace = true }

[dependencies.superstack-data]
path = "../superstack-data"

[[bin]]
name = "indexer"
path = "src/main.rs"
