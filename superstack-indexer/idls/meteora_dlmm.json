{"address": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "metadata": {"name": "lb_clmm", "version": "0.8.2", "spec": "0.1.0"}, "instructions": [{"name": "initialize_lb_pair", "discriminator": [45, 154, 237, 210, 221, 15, 166, 92], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "token_mint_x"}, {"name": "token_mint_y"}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "oracle", "writable": true}, {"name": "preset_parameter"}, {"name": "funder", "writable": true, "signer": true}, {"name": "token_program"}, {"name": "system_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "active_id", "type": "i32"}, {"name": "bin_step", "type": "u16"}]}, {"name": "initialize_permission_lb_pair", "discriminator": [108, 102, 213, 85, 251, 3, 53, 21], "accounts": [{"name": "base", "signer": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "token_mint_x"}, {"name": "token_mint_y"}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "oracle", "writable": true}, {"name": "admin", "writable": true, "signer": true}, {"name": "token_program"}, {"name": "system_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "ix_data", "type": {"defined": {"name": "InitPermissionPairIx"}}}]}, {"name": "initialize_customizable_permissionless_lb_pair", "discriminator": [46, 39, 41, 135, 111, 183, 200, 64], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "token_mint_x"}, {"name": "token_mint_y"}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "oracle", "writable": true}, {"name": "user_token_x"}, {"name": "funder", "writable": true, "signer": true}, {"name": "token_program"}, {"name": "system_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "CustomizableParams"}}}]}, {"name": "initialize_bin_array_bitmap_extension", "discriminator": [47, 157, 226, 180, 12, 240, 33, 71], "accounts": [{"name": "lb_pair"}, {"name": "bin_array_bitmap_extension", "docs": ["Initialize an account to store if a bin array is initialized."], "writable": true}, {"name": "funder", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "rent"}], "args": []}, {"name": "initialize_bin_array", "discriminator": [35, 86, 19, 185, 78, 212, 75, 211], "accounts": [{"name": "lb_pair"}, {"name": "bin_array", "writable": true}, {"name": "funder", "writable": true, "signer": true}, {"name": "system_program"}], "args": [{"name": "index", "type": "i64"}]}, {"name": "add_liquidity", "discriminator": [181, 157, 89, 67, 143, 182, 52, 72], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token_x", "writable": true}, {"name": "user_token_y", "writable": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "liquidity_parameter", "type": {"defined": {"name": "LiquidityParameter"}}}]}, {"name": "add_liquidity_by_weight", "discriminator": [28, 140, 238, 99, 231, 162, 21, 149], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token_x", "writable": true}, {"name": "user_token_y", "writable": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "liquidity_parameter", "type": {"defined": {"name": "LiquidityParameterByWeight"}}}]}, {"name": "add_liquidity_by_strategy", "discriminator": [7, 3, 150, 127, 148, 40, 61, 200], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token_x", "writable": true}, {"name": "user_token_y", "writable": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "liquidity_parameter", "type": {"defined": {"name": "LiquidityParameterByStrategy"}}}]}, {"name": "add_liquidity_by_strategy_one_side", "discriminator": [41, 5, 238, 175, 100, 225, 6, 205], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token", "writable": true}, {"name": "reserve", "writable": true}, {"name": "token_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "liquidity_parameter", "type": {"defined": {"name": "LiquidityParameterByStrategyOneSide"}}}]}, {"name": "add_liquidity_one_side", "discriminator": [94, 155, 103, 151, 70, 95, 220, 165], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token", "writable": true}, {"name": "reserve", "writable": true}, {"name": "token_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "liquidity_parameter", "type": {"defined": {"name": "LiquidityOneSideParameter"}}}]}, {"name": "remove_liquidity", "discriminator": [80, 85, 209, 72, 24, 206, 177, 108], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token_x", "writable": true}, {"name": "user_token_y", "writable": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "bin_liquidity_removal", "type": {"vec": {"defined": {"name": "BinLiquidityReduction"}}}}]}, {"name": "initialize_position", "discriminator": [219, 192, 234, 71, 190, 191, 102, 80], "accounts": [{"name": "payer", "writable": true, "signer": true}, {"name": "position", "writable": true, "signer": true}, {"name": "lb_pair"}, {"name": "owner", "signer": true}, {"name": "system_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "lower_bin_id", "type": "i32"}, {"name": "width", "type": "i32"}]}, {"name": "initialize_position_pda", "discriminator": [46, 82, 125, 146, 85, 141, 228, 153], "accounts": [{"name": "payer", "writable": true, "signer": true}, {"name": "base", "signer": true}, {"name": "position", "writable": true}, {"name": "lb_pair"}, {"name": "owner", "docs": ["owner"], "signer": true}, {"name": "system_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "lower_bin_id", "type": "i32"}, {"name": "width", "type": "i32"}]}, {"name": "initialize_position_by_operator", "discriminator": [251, 189, 190, 244, 117, 254, 35, 148], "accounts": [{"name": "payer", "writable": true, "signer": true}, {"name": "base", "signer": true}, {"name": "position", "writable": true}, {"name": "lb_pair"}, {"name": "owner"}, {"name": "operator", "docs": ["operator"], "signer": true}, {"name": "operator_token_x"}, {"name": "owner_token_x"}, {"name": "system_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "lower_bin_id", "type": "i32"}, {"name": "width", "type": "i32"}, {"name": "fee_owner", "type": "pubkey"}, {"name": "lock_release_point", "type": "u64"}]}, {"name": "update_position_operator", "discriminator": [202, 184, 103, 143, 180, 191, 116, 217], "accounts": [{"name": "position", "writable": true}, {"name": "owner", "signer": true}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "operator", "type": "pubkey"}]}, {"name": "swap", "discriminator": [248, 198, 158, 145, 225, 117, 135, 200], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "optional": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "user_token_in", "writable": true}, {"name": "user_token_out", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "oracle", "writable": true}, {"name": "host_fee_in", "writable": true, "optional": true}, {"name": "user", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "amount_in", "type": "u64"}, {"name": "min_amount_out", "type": "u64"}]}, {"name": "swap_exact_out", "discriminator": [250, 73, 101, 33, 38, 207, 75, 184], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "optional": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "user_token_in", "writable": true}, {"name": "user_token_out", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "oracle", "writable": true}, {"name": "host_fee_in", "writable": true, "optional": true}, {"name": "user", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "max_in_amount", "type": "u64"}, {"name": "out_amount", "type": "u64"}]}, {"name": "swap_with_price_impact", "discriminator": [56, 173, 230, 208, 173, 228, 156, 205], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "optional": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "user_token_in", "writable": true}, {"name": "user_token_out", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "oracle", "writable": true}, {"name": "host_fee_in", "writable": true, "optional": true}, {"name": "user", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "amount_in", "type": "u64"}, {"name": "active_id", "type": {"option": "i32"}}, {"name": "max_price_impact_bps", "type": "u16"}]}, {"name": "withdraw_protocol_fee", "discriminator": [158, 201, 158, 189, 33, 93, 162, 103], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "receiver_token_x", "writable": true}, {"name": "receiver_token_y", "writable": true}, {"name": "token_x_program"}, {"name": "token_y_program"}], "args": [{"name": "amount_x", "type": "u64"}, {"name": "amount_y", "type": "u64"}]}, {"name": "initialize_reward", "discriminator": [95, 135, 192, 196, 242, 129, 230, 68], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "reward_vault", "writable": true}, {"name": "reward_mint"}, {"name": "admin", "writable": true, "signer": true}, {"name": "token_program"}, {"name": "system_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u64"}, {"name": "reward_duration", "type": "u64"}, {"name": "funder", "type": "pubkey"}]}, {"name": "fund_reward", "discriminator": [188, 50, 249, 165, 93, 151, 38, 63], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "reward_vault", "writable": true}, {"name": "reward_mint"}, {"name": "funder_token_account", "writable": true}, {"name": "funder", "signer": true}, {"name": "bin_array", "writable": true}, {"name": "token_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u64"}, {"name": "amount", "type": "u64"}, {"name": "carry_forward", "type": "bool"}]}, {"name": "update_reward_funder", "discriminator": [211, 28, 48, 32, 215, 160, 35, 23], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "admin", "signer": true}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u64"}, {"name": "new_funder", "type": "pubkey"}]}, {"name": "update_reward_duration", "discriminator": [138, 174, 196, 169, 213, 235, 254, 107], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "admin", "signer": true}, {"name": "bin_array", "writable": true}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u64"}, {"name": "new_duration", "type": "u64"}]}, {"name": "claim_reward", "discriminator": [149, 95, 181, 242, 94, 90, 158, 162], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "position", "writable": true}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "reward_vault", "writable": true}, {"name": "reward_mint"}, {"name": "user_token_account", "writable": true}, {"name": "token_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u64"}]}, {"name": "claim_fee", "discriminator": [169, 32, 79, 137, 136, 232, 70, 137], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "position", "writable": true}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "user_token_x", "writable": true}, {"name": "user_token_y", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "token_program"}, {"name": "event_authority"}, {"name": "program"}], "args": []}, {"name": "close_position", "discriminator": [123, 134, 81, 0, 49, 68, 98, 98], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "rent_receiver", "writable": true}, {"name": "event_authority"}, {"name": "program"}], "args": []}, {"name": "update_fee_parameters", "discriminator": [128, 128, 208, 91, 246, 53, 31, 176], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "admin", "signer": true}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "fee_parameter", "type": {"defined": {"name": "FeeParameter"}}}]}, {"name": "increase_oracle_length", "discriminator": [190, 61, 125, 87, 103, 79, 158, 173], "accounts": [{"name": "oracle", "writable": true}, {"name": "funder", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "length_to_add", "type": "u64"}]}, {"name": "initialize_preset_parameter", "discriminator": [66, 188, 71, 211, 98, 109, 14, 186], "accounts": [{"name": "preset_parameter", "writable": true}, {"name": "admin", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "rent"}], "args": [{"name": "ix", "type": {"defined": {"name": "InitPresetParametersIx"}}}]}, {"name": "close_preset_parameter", "discriminator": [4, 148, 145, 100, 134, 26, 181, 61], "accounts": [{"name": "preset_parameter", "writable": true}, {"name": "admin", "writable": true, "signer": true}, {"name": "rent_receiver", "writable": true}], "args": []}, {"name": "remove_all_liquidity", "discriminator": [10, 51, 61, 35, 112, 105, 24, 85], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token_x", "writable": true}, {"name": "user_token_y", "writable": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": []}, {"name": "toggle_pair_status", "discriminator": [61, 115, 52, 23, 46, 13, 31, 144], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "admin", "signer": true}], "args": []}, {"name": "migrate_position", "discriminator": [15, 132, 59, 50, 199, 6, 251, 46], "accounts": [{"name": "position_v2", "writable": true, "signer": true}, {"name": "position_v1", "writable": true}, {"name": "lb_pair"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "owner", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "rent_receiver", "writable": true}, {"name": "event_authority"}, {"name": "program"}], "args": []}, {"name": "migrate_bin_array", "discriminator": [17, 23, 159, 211, 101, 184, 41, 241], "accounts": [{"name": "lb_pair"}], "args": []}, {"name": "update_fees_and_rewards", "discriminator": [154, 230, 250, 13, 236, 209, 75, 223], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "owner", "signer": true}], "args": []}, {"name": "withdraw_ineligible_reward", "discriminator": [148, 206, 42, 195, 247, 49, 103, 8], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "reward_vault", "writable": true}, {"name": "reward_mint"}, {"name": "funder_token_account", "writable": true}, {"name": "funder", "signer": true}, {"name": "bin_array", "writable": true}, {"name": "token_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u64"}]}, {"name": "set_activation_point", "discriminator": [91, 249, 15, 165, 26, 129, 254, 125], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "admin", "writable": true, "signer": true}], "args": [{"name": "activation_point", "type": "u64"}]}, {"name": "remove_liquidity_by_range", "discriminator": [26, 82, 102, 152, 240, 74, 105, 26], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token_x", "writable": true}, {"name": "user_token_y", "writable": true}, {"name": "reserve_x", "writable": true}, {"name": "reserve_y", "writable": true}, {"name": "token_x_mint"}, {"name": "token_y_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_x_program"}, {"name": "token_y_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "from_bin_id", "type": "i32"}, {"name": "to_bin_id", "type": "i32"}, {"name": "bps_to_remove", "type": "u16"}]}, {"name": "add_liquidity_one_side_precise", "discriminator": [161, 194, 103, 84, 171, 71, 250, 154], "accounts": [{"name": "position", "writable": true}, {"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "writable": true, "optional": true}, {"name": "user_token", "writable": true}, {"name": "reserve", "writable": true}, {"name": "token_mint"}, {"name": "bin_array_lower", "writable": true}, {"name": "bin_array_upper", "writable": true}, {"name": "sender", "signer": true}, {"name": "token_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "parameter", "type": {"defined": {"name": "AddLiquiditySingleSidePreciseParameter"}}}]}, {"name": "go_to_a_bin", "discriminator": [146, 72, 174, 224, 40, 253, 84, 174], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "bin_array_bitmap_extension", "optional": true}, {"name": "from_bin_array", "optional": true}, {"name": "to_bin_array", "optional": true}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "bin_id", "type": "i32"}]}, {"name": "set_pre_activation_duration", "discriminator": [165, 61, 201, 244, 130, 159, 22, 100], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "creator", "signer": true}], "args": [{"name": "pre_activation_duration", "type": "u64"}]}, {"name": "set_pre_activation_swap_address", "discriminator": [57, 139, 47, 123, 216, 80, 223, 10], "accounts": [{"name": "lb_pair", "writable": true}, {"name": "creator", "signer": true}], "args": [{"name": "pre_activation_swap_address", "type": "pubkey"}]}], "accounts": [{"name": "BinArrayBitmapExtension", "discriminator": [80, 111, 124, 113, 55, 237, 18, 5]}, {"name": "BinArray", "discriminator": [92, 142, 92, 220, 5, 148, 70, 181]}, {"name": "LbPair", "discriminator": [33, 11, 49, 98, 181, 101, 177, 13]}, {"name": "Oracle", "discriminator": [139, 194, 131, 179, 140, 179, 229, 244]}, {"name": "Position", "discriminator": [170, 188, 143, 228, 122, 64, 247, 208]}, {"name": "PositionV2", "discriminator": [117, 176, 212, 199, 245, 180, 133, 182]}, {"name": "PresetParameter", "discriminator": [242, 62, 244, 34, 181, 112, 58, 170]}], "events": [{"name": "CompositionFee", "discriminator": [128, 151, 123, 106, 17, 102, 113, 142]}, {"name": "AddLiquidity", "discriminator": [31, 94, 125, 90, 227, 52, 61, 186]}, {"name": "RemoveLiquidity", "discriminator": [116, 244, 97, 232, 103, 31, 152, 58]}, {"name": "<PERSON><PERSON><PERSON>", "discriminator": [81, 108, 227, 190, 205, 208, 10, 196]}, {"name": "<PERSON>laim<PERSON>eward", "discriminator": [148, 116, 134, 204, 22, 171, 85, 95]}, {"name": "FundReward", "discriminator": [246, 228, 58, 130, 145, 170, 79, 204]}, {"name": "InitializeReward", "discriminator": [211, 153, 88, 62, 149, 60, 177, 70]}, {"name": "UpdateRewardDuration", "discriminator": [223, 245, 224, 153, 49, 29, 163, 172]}, {"name": "UpdateRewardFunder", "discriminator": [224, 178, 174, 74, 252, 165, 85, 180]}, {"name": "PositionClose", "discriminator": [255, 196, 16, 107, 28, 202, 53, 128]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discriminator": [75, 122, 154, 48, 140, 74, 123, 163]}, {"name": "LbPairCreate", "discriminator": [185, 74, 252, 125, 27, 215, 188, 111]}, {"name": "PositionCreate", "discriminator": [144, 142, 252, 84, 157, 53, 37, 121]}, {"name": "FeeParameterUpdate", "discriminator": [48, 76, 241, 117, 144, 215, 242, 44]}, {"name": "IncreaseObservation", "discriminator": [99, 249, 17, 121, 166, 156, 207, 215]}, {"name": "WithdrawIneligibleReward", "discriminator": [231, 189, 65, 149, 102, 215, 154, 244]}, {"name": "UpdatePositionOperator", "discriminator": [39, 115, 48, 204, 246, 47, 66, 57]}, {"name": "UpdatePositionLockReleasePoint", "discriminator": [133, 214, 66, 224, 64, 12, 7, 191]}, {"name": "GoToABin", "discriminator": [59, 138, 76, 68, 138, 131, 176, 67]}], "errors": [{"code": 6000, "name": "InvalidStartBinIndex", "msg": "Invalid start bin index"}, {"code": 6001, "name": "InvalidBinId", "msg": "<PERSON><PERSON><PERSON> bin id"}, {"code": 6002, "name": "InvalidInput", "msg": "Invalid input data"}, {"code": 6003, "name": "ExceededAmountSlippageTolerance", "msg": "Exceeded amount slippage tolerance"}, {"code": 6004, "name": "ExceededBinSlippageTolerance", "msg": "Exceeded bin slippage tolerance"}, {"code": 6005, "name": "CompositionFactorFlawed", "msg": "Composition factor flawed"}, {"code": 6006, "name": "NonPresetBinStep", "msg": "Non preset bin step"}, {"code": 6007, "name": "ZeroLiquidity", "msg": "Zero liquidity"}, {"code": 6008, "name": "InvalidPosition", "msg": "Invalid position"}, {"code": 6009, "name": "BinArrayNotFound", "msg": "Bin array not found"}, {"code": 6010, "name": "InvalidTokenMint", "msg": "Invalid token mint"}, {"code": 6011, "name": "InvalidAccountForSingleDeposit", "msg": "Invalid account for single deposit"}, {"code": 6012, "name": "PairInsufficientLiquidity", "msg": "Pair insufficient liquidity"}, {"code": 6013, "name": "InvalidFeeOwner", "msg": "Invalid fee owner"}, {"code": 6014, "name": "InvalidFeeWithdrawAmount", "msg": "Invalid fee withdraw amount"}, {"code": 6015, "name": "InvalidAdmin", "msg": "Invalid admin"}, {"code": 6016, "name": "Identical<PERSON><PERSON><PERSON><PERSON><PERSON>", "msg": "Identical fee owner"}, {"code": 6017, "name": "InvalidBps", "msg": "Invalid basis point"}, {"code": 6018, "name": "MathOverflow", "msg": "Math operation overflow"}, {"code": 6019, "name": "TypeCastFailed", "msg": "Type cast error"}, {"code": 6020, "name": "InvalidRewardIndex", "msg": "Invalid reward index"}, {"code": 6021, "name": "InvalidRewardDuration", "msg": "Invalid reward duration"}, {"code": 6022, "name": "RewardInitialized", "msg": "<PERSON><PERSON> already initialized"}, {"code": 6023, "name": "RewardUninitialized", "msg": "Reward not initialized"}, {"code": 6024, "name": "IdenticalFunder", "msg": "Identical funder"}, {"code": 6025, "name": "RewardCampaignInProgress", "msg": "Reward campaign in progress"}, {"code": 6026, "name": "IdenticalRewardDuration", "msg": "Reward duration is the same"}, {"code": 6027, "name": "InvalidBinArray", "msg": "Invalid bin array"}, {"code": 6028, "name": "NonContinuousBinArrays", "msg": "Bin arrays must be continuous"}, {"code": 6029, "name": "InvalidRewardVault", "msg": "Invalid reward vault"}, {"code": 6030, "name": "NonEmptyPosition", "msg": "Position is not empty"}, {"code": 6031, "name": "UnauthorizedAccess", "msg": "Unauthorized access"}, {"code": 6032, "name": "InvalidFeeParameter", "msg": "Invalid fee parameter"}, {"code": 6033, "name": "<PERSON><PERSON><PERSON><PERSON>", "msg": "Missing oracle account"}, {"code": 6034, "name": "InsufficientSample", "msg": "Insufficient observation sample"}, {"code": 6035, "name": "InvalidLookupTimestamp", "msg": "Invalid lookup timestamp"}, {"code": 6036, "name": "BitmapExtensionAccountIsNotProvided", "msg": "Bitmap extension account is not provided"}, {"code": 6037, "name": "CannotFindNonZeroLiquidityBinArrayId", "msg": "Cannot find non-zero liquidity binArrayId"}, {"code": 6038, "name": "BinIdOutOfBound", "msg": "Bin id out of bound"}, {"code": 6039, "name": "InsufficientOutAmount", "msg": "Insufficient amount in for minimum out"}, {"code": 6040, "name": "InvalidPositionWidth", "msg": "Invalid position width"}, {"code": 6041, "name": "ExcessiveFeeUpdate", "msg": "Excessive fee update"}, {"code": 6042, "name": "PoolDisabled", "msg": "Pool disabled"}, {"code": 6043, "name": "InvalidPoolType", "msg": "Invalid pool type"}, {"code": 6044, "name": "ExceedMaxWhitelist", "msg": "Whitelist for wallet is full"}, {"code": 6045, "name": "InvalidIndex", "msg": "Invalid index"}, {"code": 6046, "name": "RewardNotEnded", "msg": "<PERSON><PERSON> not ended"}, {"code": 6047, "name": "MustWithdrawnIneligibleReward", "msg": "Must withdraw ineligible reward"}, {"code": 6048, "name": "Unauthorized<PERSON>ddress", "msg": "Unauthorized address"}, {"code": 6049, "name": "OperatorsAreTheSame", "msg": "Cannot update because operators are the same"}, {"code": 6050, "name": "WithdrawToWrongTokenAccount", "msg": "Withdraw to wrong token account"}, {"code": 6051, "name": "WrongRentReceiver", "msg": "Wrong rent receiver"}, {"code": 6052, "name": "AlreadyPassActivationPoint", "msg": "Already activated"}, {"code": 6053, "name": "ExceedMaxSwappedAmount", "msg": "Swapped amount is exceeded max swapped amount"}, {"code": 6054, "name": "InvalidStrategyParameters", "msg": "Invalid strategy parameters"}, {"code": 6055, "name": "LiquidityLocked", "msg": "Liquidity locked"}, {"code": 6056, "name": "BinRangeIsNotEmpty", "msg": "Bin range is not empty"}, {"code": 6057, "name": "NotExactAmountOut", "msg": "Amount out is not matched with exact amount out"}, {"code": 6058, "name": "InvalidActivationType", "msg": "Invalid activation type"}, {"code": 6059, "name": "InvalidActivationDuration", "msg": "Invalid activation duration"}, {"code": 6060, "name": "MissingTokenAmountAsTokenLaunchProof", "msg": "Missing token amount as token launch owner proof"}, {"code": 6061, "name": "InvalidQuoteToken", "msg": "Quote token must be SOL or USDC"}, {"code": 6062, "name": "InvalidBinStep", "msg": "Invalid bin step"}, {"code": 6063, "name": "InvalidBaseFee", "msg": "Invalid base fee"}, {"code": 6064, "name": "InvalidPreActivationDuration", "msg": "Invalid pre-activation duration"}, {"code": 6065, "name": "AlreadyPassPreActivationSwapPoint", "msg": "Already pass pre-activation swap point"}], "types": [{"name": "InitPresetParametersIx", "type": {"kind": "struct", "fields": [{"name": "bin_step", "docs": ["Bin step. Represent the price increment / decrement."], "type": "u16"}, {"name": "base_factor", "docs": ["Used for base fee calculation. base_fee_rate = base_factor * bin_step"], "type": "u16"}, {"name": "filter_period", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decay_period", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "reduction_factor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "variable_fee_control", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "max_volatility_accumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}, {"name": "min_bin_id", "docs": ["Min bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "max_bin_id", "docs": ["Max bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "protocol_share", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}]}}, {"name": "FeeParameter", "type": {"kind": "struct", "fields": [{"name": "protocol_share", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}, {"name": "base_factor", "docs": ["Base factor for base fee rate"], "type": "u16"}]}}, {"name": "LiquidityParameterByStrategyOneSide", "type": {"kind": "struct", "fields": [{"name": "amount", "docs": ["Amount of X token or Y token to deposit"], "type": "u64"}, {"name": "active_id", "docs": ["Active bin that integrator observe off-chain"], "type": "i32"}, {"name": "max_active_bin_slippage", "docs": ["max active bin slippage allowed"], "type": "i32"}, {"name": "strategy_parameters", "docs": ["strategy parameters"], "type": {"defined": {"name": "StrategyParameters"}}}]}}, {"name": "LiquidityParameterByStrategy", "type": {"kind": "struct", "fields": [{"name": "amount_x", "docs": ["Amount of X token to deposit"], "type": "u64"}, {"name": "amount_y", "docs": ["Amount of Y token to deposit"], "type": "u64"}, {"name": "active_id", "docs": ["Active bin that integrator observe off-chain"], "type": "i32"}, {"name": "max_active_bin_slippage", "docs": ["max active bin slippage allowed"], "type": "i32"}, {"name": "strategy_parameters", "docs": ["strategy parameters"], "type": {"defined": {"name": "StrategyParameters"}}}]}}, {"name": "StrategyParameters", "type": {"kind": "struct", "fields": [{"name": "min_bin_id", "docs": ["min bin id"], "type": "i32"}, {"name": "max_bin_id", "docs": ["max bin id"], "type": "i32"}, {"name": "strategy_type", "docs": ["strategy type"], "type": {"defined": {"name": "StrategyType"}}}, {"name": "parameteres", "docs": ["parameters"], "type": {"array": ["u8", 64]}}]}}, {"name": "LiquidityOneSideParameter", "type": {"kind": "struct", "fields": [{"name": "amount", "docs": ["Amount of X token or Y token to deposit"], "type": "u64"}, {"name": "active_id", "docs": ["Active bin that integrator observe off-chain"], "type": "i32"}, {"name": "max_active_bin_slippage", "docs": ["max active bin slippage allowed"], "type": "i32"}, {"name": "bin_liquidity_dist", "docs": ["Liquidity distribution to each bins"], "type": {"vec": {"defined": {"name": "BinLiquidityDistributionByWeight"}}}}]}}, {"name": "BinLiquidityDistributionByWeight", "type": {"kind": "struct", "fields": [{"name": "bin_id", "docs": ["Define the bin ID wish to deposit to."], "type": "i32"}, {"name": "weight", "docs": ["weight of liquidity distributed for this bin id"], "type": "u16"}]}}, {"name": "LiquidityParameterByWeight", "type": {"kind": "struct", "fields": [{"name": "amount_x", "docs": ["Amount of X token to deposit"], "type": "u64"}, {"name": "amount_y", "docs": ["Amount of Y token to deposit"], "type": "u64"}, {"name": "active_id", "docs": ["Active bin that integrator observe off-chain"], "type": "i32"}, {"name": "max_active_bin_slippage", "docs": ["max active bin slippage allowed"], "type": "i32"}, {"name": "bin_liquidity_dist", "docs": ["Liquidity distribution to each bins"], "type": {"vec": {"defined": {"name": "BinLiquidityDistributionByWeight"}}}}]}}, {"name": "AddLiquiditySingleSidePreciseParameter", "type": {"kind": "struct", "fields": [{"name": "bins", "type": {"vec": {"defined": {"name": "CompressedBinDepositAmount"}}}}, {"name": "decompress_multiplier", "type": "u64"}]}}, {"name": "CompressedBinDepositAmount", "type": {"kind": "struct", "fields": [{"name": "bin_id", "type": "i32"}, {"name": "amount", "type": "u32"}]}}, {"name": "BinLiquidityDistribution", "type": {"kind": "struct", "fields": [{"name": "bin_id", "docs": ["Define the bin ID wish to deposit to."], "type": "i32"}, {"name": "distribution_x", "docs": ["DistributionX (or distributionY) is the percentages of amountX (or amountY) you want to add to each bin."], "type": "u16"}, {"name": "distribution_y", "docs": ["DistributionX (or distributionY) is the percentages of amountX (or amountY) you want to add to each bin."], "type": "u16"}]}}, {"name": "LiquidityParameter", "type": {"kind": "struct", "fields": [{"name": "amount_x", "docs": ["Amount of X token to deposit"], "type": "u64"}, {"name": "amount_y", "docs": ["Amount of Y token to deposit"], "type": "u64"}, {"name": "bin_liquidity_dist", "docs": ["Liquidity distribution to each bins"], "type": {"vec": {"defined": {"name": "BinLiquidityDistribution"}}}}]}}, {"name": "CustomizableParams", "type": {"kind": "struct", "fields": [{"name": "active_id", "docs": ["Pool price"], "type": "i32"}, {"name": "bin_step", "docs": ["Bin step"], "type": "u16"}, {"name": "base_factor", "docs": ["Base factor"], "type": "u16"}, {"name": "activation_type", "docs": ["Activation type. 0 = Slot, 1 = Time. Check ActivationType enum"], "type": "u8"}, {"name": "has_alpha_vault", "docs": ["Whether the pool has an alpha vault"], "type": "bool"}, {"name": "activation_point", "docs": ["Decide when does the pool start trade. None = Now"], "type": {"option": "u64"}}, {"name": "padding", "docs": ["Padding, for future use"], "type": {"array": ["u8", 64]}}]}}, {"name": "InitPermissionPairIx", "type": {"kind": "struct", "fields": [{"name": "active_id", "type": "i32"}, {"name": "bin_step", "type": "u16"}, {"name": "base_factor", "type": "u16"}, {"name": "min_bin_id", "type": "i32"}, {"name": "max_bin_id", "type": "i32"}, {"name": "activation_type", "type": "u8"}]}}, {"name": "BinLiquidityReduction", "type": {"kind": "struct", "fields": [{"name": "bin_id", "type": "i32"}, {"name": "bps_to_remove", "type": "u16"}]}}, {"name": "Bin", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "amount_x", "docs": ["Amount of token X in the bin. This already excluded protocol fees."], "type": "u64"}, {"name": "amount_y", "docs": ["Amount of token Y in the bin. This already excluded protocol fees."], "type": "u64"}, {"name": "price", "docs": ["Bin price"], "type": "u128"}, {"name": "liquidity_supply", "docs": ["Liquidities of the bin. This is the same as LP mint supply. q-number"], "type": "u128"}, {"name": "reward_per_token_stored", "docs": ["reward_a_per_token_stored"], "type": {"array": ["u128", 2]}}, {"name": "fee_amount_x_per_token_stored", "docs": ["Swap fee amount of token X per liquidity deposited."], "type": "u128"}, {"name": "fee_amount_y_per_token_stored", "docs": ["Swap fee amount of token Y per liquidity deposited."], "type": "u128"}, {"name": "amount_x_in", "docs": ["Total token X swap into the bin. Only used for tracking purpose."], "type": "u128"}, {"name": "amount_y_in", "docs": ["Total token Y swap into he bin. Only used for tracking purpose."], "type": "u128"}]}}, {"name": "ProtocolFee", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "amount_x", "type": "u64"}, {"name": "amount_y", "type": "u64"}]}}, {"name": "RewardInfo", "serialization": "bytemuck", "docs": ["Stores the state relevant for tracking liquidity mining rewards"], "type": {"kind": "struct", "fields": [{"name": "mint", "docs": ["Reward token mint."], "type": "pubkey"}, {"name": "vault", "docs": ["Reward vault token account."], "type": "pubkey"}, {"name": "funder", "docs": ["Authority account that allows to fund rewards"], "type": "pubkey"}, {"name": "reward_duration", "docs": ["TODO check whether we need to store it in pool"], "type": "u64"}, {"name": "reward_duration_end", "docs": ["TODO check whether we need to store it in pool"], "type": "u64"}, {"name": "reward_rate", "docs": ["TODO check whether we need to store it in pool"], "type": "u128"}, {"name": "last_update_time", "docs": ["The last time reward states were updated."], "type": "u64"}, {"name": "cumulative_seconds_with_empty_liquidity_reward", "docs": ["Accumulated seconds where when farm distribute rewards, but the bin is empty. The reward will be accumulated for next reward time window."], "type": "u64"}]}}, {"name": "Observation", "type": {"kind": "struct", "fields": [{"name": "cumulative_active_bin_id", "docs": ["Cumulative active bin ID"], "type": "i128"}, {"name": "created_at", "docs": ["Observation sample created timestamp"], "type": "i64"}, {"name": "last_updated_at", "docs": ["Observation sample last updated timestamp"], "type": "i64"}]}}, {"name": "StaticParameters", "serialization": "bytemuck", "docs": ["Parameter that set by the protocol"], "type": {"kind": "struct", "fields": [{"name": "base_factor", "docs": ["Used for base fee calculation. base_fee_rate = base_factor * bin_step"], "type": "u16"}, {"name": "filter_period", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decay_period", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "reduction_factor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "variable_fee_control", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "max_volatility_accumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}, {"name": "min_bin_id", "docs": ["Min bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "max_bin_id", "docs": ["Max bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "protocol_share", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}, {"name": "padding", "docs": ["Padding for bytemuck safe alignment"], "type": {"array": ["u8", 6]}}]}}, {"name": "VariableParameters", "serialization": "bytemuck", "docs": ["Parameters that changes based on dynamic of the market"], "type": {"kind": "struct", "fields": [{"name": "volatility_accumulator", "docs": ["Volatility accumulator measure the number of bin crossed since reference bin ID. Normally (without filter period taken into consideration), reference bin ID is the active bin of last swap.", "It affects the variable fee rate"], "type": "u32"}, {"name": "volatility_reference", "docs": ["Volatility reference is decayed volatility accumulator. It is always <= volatility_accumulator"], "type": "u32"}, {"name": "index_reference", "docs": ["Active bin id of last swap."], "type": "i32"}, {"name": "padding", "docs": ["Padding for bytemuck safe alignment"], "type": {"array": ["u8", 4]}}, {"name": "last_update_timestamp", "docs": ["Last timestamp the variable parameters was updated"], "type": "i64"}, {"name": "padding1", "docs": ["Padding for bytemuck safe alignment"], "type": {"array": ["u8", 8]}}]}}, {"name": "FeeInfo", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "fee_x_per_token_complete", "type": "u128"}, {"name": "fee_y_per_token_complete", "type": "u128"}, {"name": "fee_x_pending", "type": "u64"}, {"name": "fee_y_pending", "type": "u64"}]}}, {"name": "UserRewardInfo", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "reward_per_token_completes", "type": {"array": ["u128", 2]}}, {"name": "reward_pendings", "type": {"array": ["u64", 2]}}]}}, {"name": "StrategyType", "type": {"kind": "enum", "variants": [{"name": "SpotOneSide"}, {"name": "CurveOneSide"}, {"name": "BidAskOneSide"}, {"name": "SpotBalanced"}, {"name": "CurveBalanced"}, {"name": "BidAskBalanced"}, {"name": "SpotImBalanced"}, {"name": "CurveImBalanced"}, {"name": "BidAskImBalanced"}]}}, {"name": "Rounding", "type": {"kind": "enum", "variants": [{"name": "Up"}, {"name": "Down"}]}}, {"name": "ActivationType", "docs": ["Type of the activation"], "type": {"kind": "enum", "variants": [{"name": "Slot"}, {"name": "Timestamp"}]}}, {"name": "LayoutVersion", "docs": ["Layout version"], "type": {"kind": "enum", "variants": [{"name": "V0"}, {"name": "V1"}]}}, {"name": "PairType", "docs": ["Type of the Pair. 0 = Permissionless, 1 = Permission, 2 = CustomizablePermissionless. Putting 0 as permissionless for backward compatibility."], "type": {"kind": "enum", "variants": [{"name": "Permissionless"}, {"name": "Permission"}, {"name": "CustomizablePermissionless"}]}}, {"name": "PairStatus", "docs": ["Pair status. 0 = Enabled, 1 = Disabled. Putting 0 as enabled for backward compatibility."], "type": {"kind": "enum", "variants": [{"name": "Enabled"}, {"name": "Disabled"}]}}, {"name": "BinArrayBitmapExtension", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "positive_bin_array_bitmap", "docs": ["Packed initialized bin array state for start_bin_index is positive"], "type": {"array": [{"array": ["u64", 8]}, 12]}}, {"name": "negative_bin_array_bitmap", "docs": ["Packed initialized bin array state for start_bin_index is negative"], "type": {"array": [{"array": ["u64", 8]}, 12]}}]}}, {"name": "BinArray", "serialization": "bytemuck", "docs": ["An account to contain a range of bin. For example: Bin 100 <-> 200.", "For example:", "BinArray index: 0 contains bin 0 <-> 599", "index: 2 contains bin 600 <-> 1199, ..."], "type": {"kind": "struct", "fields": [{"name": "index", "type": "i64"}, {"name": "version", "docs": ["Version of binArray"], "type": "u8"}, {"name": "padding", "type": {"array": ["u8", 7]}}, {"name": "lb_pair", "type": "pubkey"}, {"name": "bins", "type": {"array": [{"defined": {"name": "Bin"}}, 70]}}]}}, {"name": "LbPair", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "parameters", "type": {"defined": {"name": "StaticParameters"}}}, {"name": "v_parameters", "type": {"defined": {"name": "VariableParameters"}}}, {"name": "bump_seed", "type": {"array": ["u8", 1]}}, {"name": "bin_step_seed", "docs": ["Bin step signer seed"], "type": {"array": ["u8", 2]}}, {"name": "pair_type", "docs": ["Type of the pair"], "type": "u8"}, {"name": "active_id", "docs": ["Active bin id"], "type": "i32"}, {"name": "bin_step", "docs": ["Bin step. Represent the price increment / decrement."], "type": "u16"}, {"name": "status", "docs": ["Status of the pair. Check PairStatus enum."], "type": "u8"}, {"name": "require_base_factor_seed", "docs": ["Require base factor seed"], "type": "u8"}, {"name": "base_factor_seed", "docs": ["Base factor seed"], "type": {"array": ["u8", 2]}}, {"name": "activation_type", "docs": ["Activation type"], "type": "u8"}, {"name": "padding0", "docs": ["padding 0"], "type": "u8"}, {"name": "token_x_mint", "docs": ["Token X mint"], "type": "pubkey"}, {"name": "token_y_mint", "docs": ["Token Y mint"], "type": "pubkey"}, {"name": "reserve_x", "docs": ["LB token X vault"], "type": "pubkey"}, {"name": "reserve_y", "docs": ["LB token Y vault"], "type": "pubkey"}, {"name": "protocol_fee", "docs": ["Uncollected protocol fee"], "type": {"defined": {"name": "ProtocolFee"}}}, {"name": "padding1", "docs": ["_padding_1, previous Fee owner, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": {"array": ["u8", 32]}}, {"name": "reward_infos", "docs": ["Farming reward information"], "type": {"array": [{"defined": {"name": "RewardInfo"}}, 2]}}, {"name": "oracle", "docs": ["Oracle pubkey"], "type": "pubkey"}, {"name": "bin_array_bitmap", "docs": ["Packed initialized bin array state"], "type": {"array": ["u64", 16]}}, {"name": "last_updated_at", "docs": ["Last time the pool fee parameter was updated"], "type": "i64"}, {"name": "padding2", "docs": ["_padding_2, previous whitelisted_wallet, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": {"array": ["u8", 32]}}, {"name": "pre_activation_swap_address", "docs": ["Address allowed to swap when the current point is greater than or equal to the pre-activation point. The pre-activation point is calculated as `activation_point - pre_activation_duration`."], "type": "pubkey"}, {"name": "base_key", "docs": ["Base keypair. Only required for permission pair"], "type": "pubkey"}, {"name": "activation_point", "docs": ["Time point to enable the pair. Only applicable for permission pair."], "type": "u64"}, {"name": "pre_activation_duration", "docs": ["Duration before activation activation_point. Used to calculate pre-activation time point for pre_activation_swap_address"], "type": "u64"}, {"name": "padding3", "docs": ["_padding 3 is reclaimed free space from swap_cap_deactivate_point and swap_cap_amount before, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": {"array": ["u8", 8]}}, {"name": "padding4", "docs": ["_padding_4, previous lock_duration, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": "u64"}, {"name": "creator", "docs": ["Pool creator"], "type": "pubkey"}, {"name": "reserved", "docs": ["Reserved space for future use"], "type": {"array": ["u8", 24]}}]}}, {"name": "Oracle", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "idx", "docs": ["Index of latest observation"], "type": "u64"}, {"name": "active_size", "docs": ["Size of active sample. Active sample is initialized observation."], "type": "u64"}, {"name": "length", "docs": ["Number of observations"], "type": "u64"}]}}, {"name": "Position", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "docs": ["The LB pair of this position"], "type": "pubkey"}, {"name": "owner", "docs": ["Owner of the position. Client rely on this to to fetch their positions."], "type": "pubkey"}, {"name": "liquidity_shares", "docs": ["Liquidity shares of this position in bins (lower_bin_id <-> upper_bin_id). This is the same as LP concept."], "type": {"array": ["u64", 70]}}, {"name": "reward_infos", "docs": ["Farming reward information"], "type": {"array": [{"defined": {"name": "UserRewardInfo"}}, 70]}}, {"name": "fee_infos", "docs": ["Swap fee to claim information"], "type": {"array": [{"defined": {"name": "FeeInfo"}}, 70]}}, {"name": "lower_bin_id", "docs": ["Lower bin ID"], "type": "i32"}, {"name": "upper_bin_id", "docs": ["Upper bin ID"], "type": "i32"}, {"name": "last_updated_at", "docs": ["Last updated timestamp"], "type": "i64"}, {"name": "total_claimed_fee_x_amount", "docs": ["Total claimed token fee X"], "type": "u64"}, {"name": "total_claimed_fee_y_amount", "docs": ["Total claimed token fee Y"], "type": "u64"}, {"name": "total_claimed_rewards", "docs": ["Total claimed rewards"], "type": {"array": ["u64", 2]}}, {"name": "reserved", "docs": ["Reserved space for future use"], "type": {"array": ["u8", 160]}}]}}, {"name": "PositionV2", "serialization": "bytemuck", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "docs": ["The LB pair of this position"], "type": "pubkey"}, {"name": "owner", "docs": ["Owner of the position. Client rely on this to to fetch their positions."], "type": "pubkey"}, {"name": "liquidity_shares", "docs": ["Liquidity shares of this position in bins (lower_bin_id <-> upper_bin_id). This is the same as LP concept."], "type": {"array": ["u128", 70]}}, {"name": "reward_infos", "docs": ["Farming reward information"], "type": {"array": [{"defined": {"name": "UserRewardInfo"}}, 70]}}, {"name": "fee_infos", "docs": ["Swap fee to claim information"], "type": {"array": [{"defined": {"name": "FeeInfo"}}, 70]}}, {"name": "lower_bin_id", "docs": ["Lower bin ID"], "type": "i32"}, {"name": "upper_bin_id", "docs": ["Upper bin ID"], "type": "i32"}, {"name": "last_updated_at", "docs": ["Last updated timestamp"], "type": "i64"}, {"name": "total_claimed_fee_x_amount", "docs": ["Total claimed token fee X"], "type": "u64"}, {"name": "total_claimed_fee_y_amount", "docs": ["Total claimed token fee Y"], "type": "u64"}, {"name": "total_claimed_rewards", "docs": ["Total claimed rewards"], "type": {"array": ["u64", 2]}}, {"name": "operator", "docs": ["Operator of position"], "type": "pubkey"}, {"name": "lock_release_point", "docs": ["Time point which the locked liquidity can be withdraw"], "type": "u64"}, {"name": "padding0", "docs": ["_padding_0, previous subjected_to_bootstrap_liquidity_locking, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": "u8"}, {"name": "fee_owner", "docs": ["Address is able to claim fee in this position, only valid for bootstrap_liquidity_position"], "type": "pubkey"}, {"name": "reserved", "docs": ["Reserved space for future use"], "type": {"array": ["u8", 87]}}]}}, {"name": "PresetParameter", "type": {"kind": "struct", "fields": [{"name": "bin_step", "docs": ["Bin step. Represent the price increment / decrement."], "type": "u16"}, {"name": "base_factor", "docs": ["Used for base fee calculation. base_fee_rate = base_factor * bin_step"], "type": "u16"}, {"name": "filter_period", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decay_period", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "reduction_factor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "variable_fee_control", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "max_volatility_accumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}, {"name": "min_bin_id", "docs": ["Min bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "max_bin_id", "docs": ["Max bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "protocol_share", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}]}}, {"name": "CompositionFee", "type": {"kind": "struct", "fields": [{"name": "from", "type": "pubkey"}, {"name": "bin_id", "type": "i16"}, {"name": "token_x_fee_amount", "type": "u64"}, {"name": "token_y_fee_amount", "type": "u64"}, {"name": "protocol_token_x_fee_amount", "type": "u64"}, {"name": "protocol_token_y_fee_amount", "type": "u64"}]}}, {"name": "AddLiquidity", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "from", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "amounts", "type": {"array": ["u64", 2]}}, {"name": "active_bin_id", "type": "i32"}]}}, {"name": "RemoveLiquidity", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "from", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "amounts", "type": {"array": ["u64", 2]}}, {"name": "active_bin_id", "type": "i32"}]}}, {"name": "<PERSON><PERSON><PERSON>", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "from", "type": "pubkey"}, {"name": "start_bin_id", "type": "i32"}, {"name": "end_bin_id", "type": "i32"}, {"name": "amount_in", "type": "u64"}, {"name": "amount_out", "type": "u64"}, {"name": "swap_for_y", "type": "bool"}, {"name": "fee", "type": "u64"}, {"name": "protocol_fee", "type": "u64"}, {"name": "fee_bps", "type": "u128"}, {"name": "host_fee", "type": "u64"}]}}, {"name": "<PERSON>laim<PERSON>eward", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "reward_index", "type": "u64"}, {"name": "total_reward", "type": "u64"}]}}, {"name": "FundReward", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "funder", "type": "pubkey"}, {"name": "reward_index", "type": "u64"}, {"name": "amount", "type": "u64"}]}}, {"name": "InitializeReward", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "reward_mint", "type": "pubkey"}, {"name": "funder", "type": "pubkey"}, {"name": "reward_index", "type": "u64"}, {"name": "reward_duration", "type": "u64"}]}}, {"name": "UpdateRewardDuration", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "reward_index", "type": "u64"}, {"name": "old_reward_duration", "type": "u64"}, {"name": "new_reward_duration", "type": "u64"}]}}, {"name": "UpdateRewardFunder", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "reward_index", "type": "u64"}, {"name": "old_funder", "type": "pubkey"}, {"name": "new_funder", "type": "pubkey"}]}}, {"name": "PositionClose", "type": {"kind": "struct", "fields": [{"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}]}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "fee_x", "type": "u64"}, {"name": "fee_y", "type": "u64"}]}}, {"name": "LbPairCreate", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "bin_step", "type": "u16"}, {"name": "token_x", "type": "pubkey"}, {"name": "token_y", "type": "pubkey"}]}}, {"name": "PositionCreate", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}]}}, {"name": "FeeParameterUpdate", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "protocol_share", "type": "u16"}, {"name": "base_factor", "type": "u16"}]}}, {"name": "IncreaseObservation", "type": {"kind": "struct", "fields": [{"name": "oracle", "type": "pubkey"}, {"name": "new_observation_length", "type": "u64"}]}}, {"name": "WithdrawIneligibleReward", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "reward_mint", "type": "pubkey"}, {"name": "amount", "type": "u64"}]}}, {"name": "UpdatePositionOperator", "type": {"kind": "struct", "fields": [{"name": "position", "type": "pubkey"}, {"name": "old_operator", "type": "pubkey"}, {"name": "new_operator", "type": "pubkey"}]}}, {"name": "UpdatePositionLockReleasePoint", "type": {"kind": "struct", "fields": [{"name": "position", "type": "pubkey"}, {"name": "current_point", "type": "u64"}, {"name": "new_lock_release_point", "type": "u64"}, {"name": "old_lock_release_point", "type": "u64"}, {"name": "sender", "type": "pubkey"}]}}, {"name": "GoToABin", "type": {"kind": "struct", "fields": [{"name": "lb_pair", "type": "pubkey"}, {"name": "from_bin_id", "type": "i32"}, {"name": "to_bin_id", "type": "i32"}]}}], "constants": [{"name": "BASIS_POINT_MAX", "type": "i32", "value": "10000"}, {"name": "MAX_BIN_PER_ARRAY", "type": {"defined": {"name": "usize"}}, "value": "70"}, {"name": "MAX_BIN_PER_POSITION", "type": {"defined": {"name": "usize"}}, "value": "70"}, {"name": "MIN_BIN_ID", "type": "i32", "value": "- 443636"}, {"name": "MAX_BIN_ID", "type": "i32", "value": "443636"}, {"name": "MAX_FEE_RATE", "type": "u64", "value": "100_000_000"}, {"name": "FEE_PRECISION", "type": "u64", "value": "1_000_000_000"}, {"name": "MAX_PROTOCOL_SHARE", "type": "u16", "value": "2_500"}, {"name": "HOST_FEE_BPS", "type": "u16", "value": "2_000"}, {"name": "NUM_REWARDS", "type": {"defined": {"name": "usize"}}, "value": "2"}, {"name": "MIN_REWARD_DURATION", "type": "u64", "value": "1"}, {"name": "MAX_REWARD_DURATION", "type": "u64", "value": "31536000"}, {"name": "EXTENSION_BINARRAY_BITMAP_SIZE", "type": {"defined": {"name": "usize"}}, "value": "12"}, {"name": "BIN_ARRAY_BITMAP_SIZE", "type": "i32", "value": "512"}, {"name": "MAX_REWARD_BIN_SPLIT", "type": {"defined": {"name": "usize"}}, "value": "15"}, {"name": "MAX_BIN_STEP", "type": "u16", "value": "400"}, {"name": "MAX_BASE_FEE", "type": "u128", "value": "100_000_000"}, {"name": "MIN_BASE_FEE", "type": "u128", "value": "100_000"}, {"name": "BIN_ARRAY", "type": "bytes", "value": "[98, 105, 110, 95, 97, 114, 114, 97, 121]"}, {"name": "ORACLE", "type": "bytes", "value": "[111, 114, 97, 99, 108, 101]"}, {"name": "BIN_ARRAY_BITMAP_SEED", "type": "bytes", "value": "[98, 105, 116, 109, 97, 112]"}, {"name": "PRESET_PARAMETER", "type": "bytes", "value": "[112, 114, 101, 115, 101, 116, 95, 112, 97, 114, 97, 109, 101, 116, 101, 114]"}, {"name": "POSITION", "type": "bytes", "value": "[112, 111, 115, 105, 116, 105, 111, 110]"}]}