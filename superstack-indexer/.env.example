PORT=3002

# Solana Config
SOLANA_RPC_URL=https://rpc.test.superstack.xyz
# Solana Indexer Config
SOLANA_FROM_SLOT=341329800
SOLANA_BLOCK_LIMIT=100
SOLANA_TASK_CONCURRENCY=2000

ENABLE_METEORA_INDEXER=true
ENABLE_PUMPSWAP_INDEXER=false
ENABLE_PUMPFUN_INDEXER=false

# Hyperevm Config
HYPEREVM_RPC_URL=https://rpc.hyperliquid.xyz/evm
HYPEREVM_BACKUP_RPC_URL=https://rpc.hyperliquid.xyz/evm
HYPEREVM_FROM_BLOCK=6385049
HYPEREVM_BLOCK_LIMIT=100
HYPEREVM_INIT_STEP=100000
CMC_API_KEY=01085820-ad4b-421a-baa8-2c382ff7fb63


POSTGRES_INDEXER_DATABASE_URL=postgresql://superstack-indexer:password@127.0.0.1:5432/superstack-indexer
POSTGRES_INDEXER_MAX_CONNECTIONS=300
POSTGRES_INDEXER_NEED_MIGRATE=true

STORE_PRICE_HISTORY=true

KAFKA_BOOTSTRAP_SERVERS=pkc-ldvr1.asia-southeast1.gcp.confluent.cloud:9092
KAFKA_API_KEY=KV7EDZDBGVNCBR32
KAFKA_API_SECRET=FZNQulI7Rs77h+XaHPylbXdWLZzC2FMiYoUG58vGuUemIiHAAN7UVBqeIRFWz5jq

REDIS_URL=

COINGECKO_API_KEY=CG-T2Dk49gHP6uEZE1cotuJCezy
