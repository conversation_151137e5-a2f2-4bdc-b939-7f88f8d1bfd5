# SuperStack WebSocket API Documentation

## Overview

The SuperStack WebSocket API provides a real-time data stream for cryptocurrency trading, including token updates, transaction information, price changes, and pool statistics. The service consumes data from Kafka topics and broadcasts it to connected WebSocket clients in real-time.

## ✅ Current Status

**Fully Operational** - End-to-end testing completed successfully!

- ✅ Kafka integration working
- ✅ **Real-time message broadcasting (FIXED 2025-06-23)**
- ✅ WebSocket client connections
- ✅ All message types supported
- ✅ Auto-subscription to all updates
- ✅ Health monitoring active
- ✅ Authentication system (JWT + Development mode)
- ✅ Connection management and cleanup
- ✅ Message filtering and deduplication

### 🔧 Recent Updates

**Latest Version**: v0.1.0

**Key Features**:
- ✅ Unified WebSocket message format with optional parameters
- ✅ API-compatible data structures (shared with superstack-api)
- ✅ Advanced subscription filtering with validation
- ✅ Automatic connection cleanup and heartbeat monitoring
- ✅ Comprehensive error handling and logging
- ✅ Development and production authentication modes
- ✅ Real-time statistics and health monitoring

## 🚀 Quick Start

### Prerequisites
- Kafka running on `localhost:9092`
- API service running on `localhost:8080` (for authentication)

### Development Setup
```bash
# 1. Start the WebSocket service
cd superstack-websocket
DEV_MODE=true KAFKA_BOOTSTRAP_SERVERS="localhost:9092" WEBSOCKET_PORT="8081" RUST_LOG="info" cargo run --release

# 2. Connect with test client
# Open websocket-frontend.html in browser or use simple-test.js

# 3. Check health
curl http://localhost:8081/health
```

### Production Setup
```bash
# Set environment variables
export KAFKA_BOOTSTRAP_SERVERS="your-kafka-servers:9092"
export API_SERVICE_URL="http://your-api-service:8080"
export WEBSOCKET_PORT="8081"
export RUST_LOG="info"

# Start service
./target/release/superstack-websocket
```

## Connection Information

### Basic Connection
- **WebSocket URL**: `ws://localhost:8081/ws` (Development Environment)
- **Production Environment**: `wss://your-domain.com/ws`
- **Protocol**: WebSocket (RFC 6455)
- **Health Check**: `http://localhost:8081/health`

### Data Sources
- **Kafka Topics**: `candles`, `extended_dex_trades`, `extended_token_holders`, `pool_statistics`, `token_statistics`, `new_token`
- **Kafka Bootstrap Servers**: `localhost:9092` (configurable via `KAFKA_BOOTSTRAP_SERVERS`)
- **Consumer Group**: `superstack_websocket_consumer` (configurable via `KAFKA_GROUP_ID`)
- **Auto-scaling**: Set `AUTO_SCALABLE=true` for Cloud Run environments to generate unique consumer group IDs

### Authentication

#### Development Mode
When `DEV_MODE=true`, no authentication is required to connect. Clients are automatically assigned a `dev_user` identity and subscribed to all updates.

#### Production Mode
A JWT token must be provided in the connection URL:
```
ws://localhost:8081/ws?token=your-jwt-token
```

The token is validated against the API service at `/internal/validate-token`.

### Environment Variables
```bash
# Required
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
WEBSOCKET_PORT=8081

# Optional
DEV_MODE=true                    # Disable authentication for development
API_SERVICE_URL=http://localhost:8080  # For production authentication
RUST_LOG=debug                   # Enable debug logging
WEBSOCKET_PING_INTERVAL_SECONDS=30    # Heartbeat interval
WEBSOCKET_BUFFER_SIZE=10000      # Message buffer size
```

## Connection Examples

### JavaScript
```javascript
// Development mode connection
const ws = new WebSocket('ws://localhost:8081/ws');

// Production mode connection (with token)
const token = 'your-jwt-token';
const ws = new WebSocket(`ws://localhost:8081/ws?token=${token}`);

ws.onopen = function(event) {
    console.log('WebSocket connection established');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Message received:', data);
};

ws.onclose = function(event) {
    console.log('WebSocket connection closed:', event.code, event.reason);
};

ws.onerror = function(error) {
    console.error('WebSocket error:', error);
};
```

### Python
```python
import asyncio
import websockets
import json

async def connect_websocket():
    uri = "ws://localhost:8081/ws"
    
    async with websockets.connect(uri) as websocket:
        print("WebSocket connection established")
        
        # Subscribe to token updates
        subscribe_msg = {
            "type": "Subscribe",
            "subscription_type": "token_updates"
        }
        await websocket.send(json.dumps(subscribe_msg))
        
        # Listen for messages
        async for message in websocket:
            data = json.loads(message)
            print(f"Message received: {data}")

# Run
asyncio.run(connect_websocket())
```

## Message Format

All messages use JSON format with a unified structure:

```json
{
    "type": "message_type",
    "timestamp": 1640995200,
    "data": { ... },
    "params": { ... }  // Optional: subscription parameters for context
}
```

Where:
- `type`: Message type identifier (e.g., "token_update", "trade_update")
- `timestamp`: Unix timestamp when the message was created
- `data`: The actual message payload, **matching the corresponding API endpoint format exactly**
- `params`: Optional subscription parameters that help clients distinguish between different subscriptions

### Subscription Parameters Context

The `params` field is automatically included when clients have active subscriptions with filters. This helps clients:

- **Distinguish Messages**: Differentiate between multiple subscriptions of the same type (e.g., 5m vs 30m candles)
- **Context Awareness**: Understand which subscription parameters triggered the message
- **Client-Side Routing**: Route messages to appropriate handlers based on subscription context

**Example with parameters:**
```json
{
    "type": "candle_update",
    "timestamp": **********,
    "data": { /* candle data */ },
    "params": {
        "pairAddress": "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2",
        "interval": "5m",
        "tokenMint": "So11111111111111111111111111111111111111112"
    }
}
```

**Example without parameters (no filters applied):**
```json
{
    "type": "candle_update", 
    "timestamp": **********,
    "data": { /* candle data */ }
}
```

## 🔗 API Compatibility

The WebSocket service directly uses type definitions from `superstack-api` to ensure 100% compatibility:

- **Shared Types**: All data structures are imported from `superstack-api` using the `types-only` feature
- **Automatic Sync**: When API types change, WebSocket automatically inherits the changes
- **Zero Duplication**: No need to maintain separate type definitions
- **Consistent Format**: `data` field format matches API responses exactly

### Filter Parameters Mapping

The WebSocket subscription filters support the same parameters as the corresponding HTTP API endpoints:

| Subscription Type | HTTP API Endpoint | Parameter Source | Supported Filters |
|---|---|---|---|
| `token_info` | `/api/token/info/{tokenMint}` | Path + Query | `token_mint` (required), `pool_address` |
| `candle_snapshot` | `/api/candle/snapshot` | Request Body | `pair_address` (required), `interval` (required), `start_time`, `end_time` |
| `token_trades` | `/api/token/trades/{tokenMint}` | Path + Query | `token_mint` (required), `limit`, `max_timestamp`, `maker_type`, `min_usd_amount`, `max_usd_amount`, `maker_address` |
| `token_holders_detailed` | `/api/token/holders/{tokenMint}` | Path + Query | `token_mint` (required), `limit`, `offset` |

### Parameter Validation

- **Required Parameters**: Each API-specific subscription type validates that required parameters are provided
- **Type Validation**: Parameters are validated for correct data types and value ranges
- **Business Logic**: Same validation rules as HTTP API (e.g., `end_time > start_time`, valid `maker_type` values)
- **Error Handling**: Validation errors are returned as error messages to the client

### Filter Behavior

- **Exact Match**: String parameters like `token_mint`, `maker_address` require exact matches
- **Range Filtering**: Numeric parameters like `min_usd_amount`, `max_usd_amount` support range filtering
- **Enum Validation**: Parameters like `maker_type` are validated against allowed values: `["all", "dev", "snipers"]`
- **Time Range**: `start_time` and `end_time` parameters support time-based filtering for historical data

## 🚀 Real-Time Data Flow

### Kafka Topics → WebSocket Messages

| Kafka Topic | WebSocket Message Type | Description |
|---|---|---|
| `candles` | `candle_update` | 1-minute candlestick data |
| `extended_dex_trades` | `trade_update` | Real-time trade information |
| `extended_token_holders` | `token_holders_update` | Token holder statistics |
| `pool_statistics` | `pool_statistics_update` | Pool trading statistics |
| `token_statistics` | `token_update` | Detailed token information updates |
| `token_statistics` | `market_token_update` | Market-focused token updates |
| `new_token` | `token_update` | New token launches (detailed) |
| `new_token` | `market_token_update` | New token launches (market) |

### Subscription Behavior
- **Manual Subscription**: Clients must explicitly subscribe to desired update types
- **No Auto-Subscription**: Connections are established without automatic subscriptions
- **Message Broadcasting**: All Kafka messages are immediately broadcast to subscribed clients

### Client Messages (Client → Server)

#### 1. Subscribe Message

**General Subscription (Detailed Token Updates):**
```json
{
    "type": "Subscribe",
    "subscription_type": "token_updates",
    "filters": {
        "token_addresses": ["address1", "address2"],
        "min_volume": 1000,
        "min_market_cap": 50000,
        "trade_types": ["buy", "sell"],
        "min_trade_amount": 100
    }
}
```

**Market Token Updates Subscription (Market-focused):**
```json
{
    "type": "Subscribe",
    "subscription_type": "market_token_updates",
    "filters": {
        "min_volume": 10000,
        "min_market_cap": 100000
    }
}
```

**API-Specific Subscriptions:**

**Token Info Subscription** (corresponds to `/api/token/info/{tokenMint}`):
```json
{
    "type": "Subscribe",
    "subscription_type": "token_info",
    "filters": {
        "token_mint": "So11111111111111111111111111111111111111112",
        "pool_address": "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"
    }
}
```

**Candle Snapshot Subscription** (corresponds to `/api/candle/snapshot`):
```json
{
    "type": "Subscribe",
    "subscription_type": "candle_snapshot",
    "filters": {
        "pair_address": "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2",
        "interval": "1m",
        "start_time": 1640995200,
        "end_time": 1640995800
    }
}
```

**Token Trades Subscription** (corresponds to `/api/token/trades/{tokenMint}`):
```json
{
    "type": "Subscribe",
    "subscription_type": "token_trades",
    "filters": {
        "token_mint": "So11111111111111111111111111111111111111112",
        "limit": 100,
        "max_timestamp": 1640995800,
        "maker_type": "all",
        "min_usd_amount": 10.0,
        "max_usd_amount": 1000.0,
        "maker_address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
    }
}
```

**Token Holders Subscription** (corresponds to `/api/token/holders/{tokenMint}`):
```json
{
    "type": "Subscribe",
    "subscription_type": "token_holders_detailed",
    "filters": {
        "token_mint": "So11111111111111111111111111111111111111112",
        "limit": 20,
        "offset": 0
    }
}
```

#### 2. Unsubscribe Message
```json
{
    "type": "Unsubscribe",
    "subscription_type": "token_updates"
}
```

#### 3. Heartbeat Message
```json
{
    "type": "Ping"
}
```

### Server Messages (Server → Client)

#### 1. Connection Confirmation
```json
{
    "type": "connected",
    "timestamp": **********,
    "data": {
        "connectionId": "dev_user_de3447f6-499d-454c-8164-c47aa800b281"
    }
}
```

#### 2. Subscription Confirmation
```json
{
    "type": "subscribed",
    "timestamp": **********,
    "data": {
        "subscriptionType": "token_updates",
        "filters": {
            "min_volume": 1000
        }
    }
}
```

#### 3. Token Update (Detailed)
```json
{
    "type": "token_update",
    "timestamp": **********,
    "data": {
        "token_mint": "So11111111111111111111111111111111111111112",
        "name": "Wrapped SOL",
        "symbol": "SOL",
        "description": "Wrapped Solana",
        "image_url": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",
        "twitter": "https://twitter.com/solana",
        "telegram": "",
        "website": "https://solana.com",
        "launch_dex": "pumpfun",
        "launch_dex_url": null,
        "decimals": 9,
        "total_supply": 1000000000.0,
        "circulating_supply": 1000000000.0,
        "chain": "Solana",
        "is_trench_token": false,
        "default_pool_index": 0,
        "current_pool_index": 0,
        "is_user_selected": false,
        "pools": [],
        "current_pool_statistic": {
            "data5m": { "price_change": 0.0, "txns": 0, "buys": 0, "sells": 0, "volume_usd": 0.0, "buy_volume_usd": 0.0, "sell_volume_usd": 0.0, "makers": 0, "buyers": 0, "sellers": 0 },
            "data1h": { "price_change": 0.0, "txns": 0, "buys": 0, "sells": 0, "volume_usd": 0.0, "buy_volume_usd": 0.0, "sell_volume_usd": 0.0, "makers": 0, "buyers": 0, "sellers": 0 },
            "data6h": { "price_change": 0.0, "txns": 0, "buys": 0, "sells": 0, "volume_usd": 0.0, "buy_volume_usd": 0.0, "sell_volume_usd": 0.0, "makers": 0, "buyers": 0, "sellers": 0 },
            "data24h": { "price_change": 0.025, "txns": 14400, "buys": 8640, "sells": 5760, "volume_usd": 1440000.0, "buy_volume_usd": 864000.0, "sell_volume_usd": 576000.0, "makers": 14400, "buyers": 8640, "sellers": 5760 }
        }
    }
}
```

**Key Fields**:
- `total_supply` (number): **Total token supply in UI amount** (normalized by dividing raw supply by 10^decimals)
- `circulating_supply` (number): **Circulating token supply in UI amount** (normalized by dividing raw supply by 10^decimals)
- `decimals` (number): Token decimal places used for normalization

**⚠️ Breaking Change Notice**:
Supply fields now return normalized decimal values instead of raw integer values.

#### 3a. Market Token Update (Market-focused)
```json
{
    "type": "market_token_update",
    "timestamp": **********,
    "data": {
        "tokenMint": "So11111111111111111111111111111111111111112",
        "name": "Wrapped SOL",
        "symbol": "SOL",
        "imageUrl": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",
        "chain": "Solana",
        "twitter": "https://twitter.com/solana",
        "telegram": "",
        "website": "https://solana.com",
        "priceUsd": 100.0,
        "marketCapUsd": 50000000000.0,
        "fdvUsd": 50000000000.0,
        "volumeUsd": 1440000.0,
        "liquidityUsd": 25000000.0,
        "priceChange": 0.025
    }
}
```

#### 4. Candle Update (with subscription parameters)
```json
{
    "type": "candle_update",
    "timestamp": **********,
    "data": {
        "chain": "Solana",
        "pool_address": "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2",
        "open_timestamp_seconds": 1750315160,
        "close_timestamp_seconds": 1750315220,
        "interval": "M1",
        "native_token_usd_price": 100.0,
        "usd_open_price": 99.0,
        "usd_close_price": 100.0,
        "usd_high_price": 101.0,
        "usd_low_price": 98.0,
        "usd_open_market_cap": 50000000000.0,
        "usd_close_market_cap": 50100000000.0,
        "usd_high_market_cap": 50500000000.0,
        "usd_low_market_cap": 49000000000.0,
        "usd_volume": 100000.0,
        "txns": 150
    },
    "params": {
        "pairAddress": "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2",
        "interval": "1m",
        "startTime": 1750315160,
        "endTime": 1750315220
    }
}
```

#### 5. Token Holders Update
```json
{
    "type": "token_holders_update",
    "timestamp": **********,
    "data": {
        "holders": [
            {
                "maker": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
                "maker_type": "Whale",
                "base_balance": 1000.0,
                "bought_ui_amount": 500.0,
                "bought_usd": 50000.0,
                "bought_tx_count": 5,
                "sold_ui_amount": 100.0,
                "sold_usd": 10000.0,
                "sold_tx_count": 2,
                "remaining_ui_amount": 400.0,
                "remaining_usd": 40.0,
                "remaining_percentage": 0.8,
                "pnl_usd": -40000.0
            }
        ],
        "total_holders": 1
    }
}
```

#### 6. Trade Update
```json
{
    "type": "trade_update",
    "timestamp": **********,
    "data": {
        "tx_sig": "3LLRM3yTP...",
        "maker": "FN4...pump",
        "maker_type": "Retail",
        "is_buy": true,
        "token_ui_amount": 525.0,
        "base_ui_amount": 10.5,
        "usd": 210.0,
        "price_usd": 0.02,
        "price_native": 0.02,
        "market_cap_usd": 1000000.0,
        "timestamp": **********
    }
}
```

#### 7. Pool Statistics Update
```json
{
    "type": "pool_statistics_update",
    "timestamp": **********,
    "data": {
        "data5m": {
            "price_change": 0.01,
            "txns": 50,
            "buys": 30,
            "sells": 20,
            "volume_usd": 5000.0,
            "buy_volume_usd": 3000.0,
            "sell_volume_usd": 2000.0,
            "makers": 25,
            "buyers": 15,
            "sellers": 10
        },
        "data1h": {
            "price_change": 0.05,
            "txns": 600,
            "buys": 360,
            "sells": 240,
            "volume_usd": 60000.0,
            "buy_volume_usd": 36000.0,
            "sell_volume_usd": 24000.0,
            "makers": 300,
            "buyers": 180,
            "sellers": 120
        },
        "data6h": {
            "price_change": 0.1,
            "txns": 3600,
            "buys": 2160,
            "sells": 1440,
            "volume_usd": 360000.0,
            "buy_volume_usd": 216000.0,
            "sell_volume_usd": 144000.0,
            "makers": 1800,
            "buyers": 1080,
            "sellers": 720
        },
        "data24h": {
            "price_change": 0.15,
            "txns": 14400,
            "buys": 8640,
            "sells": 5760,
            "volume_usd": 1440000.0,
            "buy_volume_usd": 864000.0,
            "sell_volume_usd": 576000.0,
            "makers": 7200,
            "buyers": 4320,
            "sellers": 2880
        }
    }
}
```

#### 8. Heartbeat Response
```json
{
    "type": "pong",
    "timestamp": **********
}
```

#### 9. Error Message
```json
{
    "type": "error",
    "timestamp": **********,
    "data": {
        "message": "Subscription failed: Invalid subscription type"
    }
}
```

#### 10. Statistics Message
```json
{
    "type": "statistics",
    "timestamp": **********,
    "data": {
        "totalTokens": 1000,
        "totalTrades24h": 50000,
        "totalVolume24h": 2500000.0,
        "activePools": 250
    }
}
```

## Subscription Types

### Available Subscription Types

| Subscription Type | Description | Message Type | Kafka Source |
|---|---|---|---|
| `price_updates` | Price update notifications | `price_update` | Various |
| `token_updates` | Detailed token information updates | `token_update` | `token_statistics`, `new_token` |
| `market_token_updates` | Market-focused token updates | `market_token_update` | `token_statistics`, `new_token` |
| `trade_updates` | Trading data updates | `trade_update` | `extended_dex_trades` |
| `candle_updates` | Candlestick data updates | `candle_update` | `candles` |
| `pool_statistics` | Pool statistics updates | `pool_statistics_update` | `pool_statistics` |
| `token_holders` | Token holder updates | `token_holders_update` | `extended_token_holders` |
| `all_updates` | All types of updates | All message types | All topics |
| `statistics` | System statistics information | `statistics` | Internal |

### API-Specific Subscription Types

| Subscription Type | Description | Required Filters | Message Type |
|---|---|---|---|
| `token_info` | Token information (API compatible) | `token_mint` | `token_update` |
| `candle_snapshot` | Candle data (API compatible) | `pair_address`, `interval` | `candle_update` |
| `token_trades` | Token trades (API compatible) | `token_mint` | `trade_update` |
| `token_holders_detailed` | Token holders (API compatible) | `token_mint` | `token_holders_update` |

### Filter Options

Filters can be used during subscription to limit the data received:

```json
{
    "filters": {
        "token_addresses": ["address1", "address2"],  // Specific token addresses
        "min_volume": 1000,                           // Minimum 24-hour trading volume
        "min_market_cap": 50000,                      // Minimum market cap
        "trade_types": ["buy", "sell"],               // Trade types
        "min_trade_amount": 100                       // Minimum trade amount
    }
}
```

## Heartbeat Mechanism

### Server Heartbeat
- The server automatically sends a `ping` message every 30 seconds (configurable via `WEBSOCKET_PING_INTERVAL_SECONDS`)
- The client should respond to the `ping` message (optional, but recommended)
- If there is no activity on the connection for 5 minutes, it will be considered for cleanup

### Client Heartbeat
```javascript
// Optional: Client actively sends a heartbeat
setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: "Ping" }));
    }
}, 30000); // Send once every 30 seconds
```

## Error Handling

### Common Error Codes

| Error Code | Description | Solution |
|---|---|---|
| 1000 | Normal Closure | No action needed |
| 1001 | Going Away | Reconnect |
| 1002 | Protocol Error | Check message format |
| 1003 | Unsupported Data | Check message content |
| 1006 | Abnormal Closure | Reconnect |
| 1011 | Server Error | Retry later |

### WebSocket Error Types

| Error Type | Description | Example |
|---|---|---|
| `ValidationError` | Filter validation failed | Invalid subscription parameters |
| `SubscriptionError` | Subscription operation failed | Subscription limit exceeded |
| `ConnectionNotFound` | Connection does not exist | Connection was already closed |
| `AuthenticationFailed` | JWT token validation failed | Invalid or expired token |
| `ServiceUnavailable` | Service is not available | Connection limit reached |

## Performance Optimization Suggestions

### 1. Connection Management
- Use a single WebSocket connection to handle all subscriptions
- Implement an automatic reconnection mechanism
- Properly close the connection when the page is unloaded

### 2. Message Handling
- Use debouncing to handle high-frequency messages
- Batch process similar updates
- Avoid performing heavy computations in the message handler

### 3. Memory Management
- Clean up unnecessary subscriptions promptly
- Limit the number of historical messages stored
- Use `WeakMap` to store temporary data

## Limits and Quotas

- **Max Connections**: 10,000 (configurable via `WebSocketConfig`)
- **Max Subscriptions per Connection**: 50 (configurable)
- **Message Buffer Size**: 10,000 (configurable via `WEBSOCKET_BUFFER_SIZE`)
- **Connection Timeout**: 5 minutes of inactivity triggers cleanup
- **Heartbeat Interval**: 30 seconds (configurable via `WEBSOCKET_PING_INTERVAL_SECONDS`)

## Health Check

You can check the service status via an HTTP GET request:
```
GET http://localhost:8081/health
```

Example response:
```json
{
    "status": "healthy",
    "services": {
        "api_service": {
            "status": "up",
            "healthy": true
        },
        "kafka_service": {
            "status": "up",
            "healthy": true
        },
        "websocket_service": {
            "status": "up",
            "healthy": true,
            "stats": {
                "connection_count": 1,
                "max_connections": 10000,
                "uptime_seconds": 3600,
                "status": "healthy"
            }
        }
    },
    "timestamp": **********,
    "uptime_seconds": 3600
}
```

## 🧪 Testing

### Built-in Test Client
A built-in HTML test client is available at `websocket-frontend.html`:

```bash
# Start the WebSocket service
cargo run --release

# Open websocket-frontend.html in your browser
# The client will automatically connect and display real-time messages
```

### Manual Testing with curl
```bash
# Check health
curl http://localhost:8081/health

# Test WebSocket connection (requires wscat)
npm install -g wscat
wscat -c ws://localhost:8081/ws
```

### Integration Testing
```javascript
// Example test script
const WebSocket = require('ws');

const ws = new WebSocket('ws://localhost:8081/ws');

ws.on('open', function open() {
    console.log('Connected');
    
    // Subscribe to token updates
    ws.send(JSON.stringify({
        type: 'Subscribe',
        subscription_type: 'token_updates'
    }));
});

ws.on('message', function message(data) {
    const msg = JSON.parse(data);
    console.log('Received:', msg.type);
});
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   * Check if the URL is correct: `ws://localhost:8081/ws`
   * Confirm that the service is running: `curl http://localhost:8081/health`
   * Check firewall settings and port 8081 availability
   * Verify environment variables are set correctly

2. **Authentication Failed**
   * In development mode: Ensure `DEV_MODE=true` is set
   * In production mode: Verify JWT token is valid and not expired
   * Check token format: `ws://localhost:8081/ws?token=your-jwt-token`
   * Verify API service is running and accessible

3. **No Messages Received**
   * Check Kafka connection: Verify `KAFKA_BOOTSTRAP_SERVERS` is correct
   * Ensure Kafka topics exist and have data
   * Check subscription type: Use `all_updates` for development
   * Verify client connected after WebSocket service started
   * Check logs for message processing errors

4. **Kafka Connection Issues**
   * Check Kafka is running: `docker ps` or service status
   * Verify bootstrap servers: Default is `localhost:9092`
   * Check topic creation: Topics should auto-create
   * Review logs for connection errors

5. **Frequent Disconnections**
   * Check if the heartbeat mechanism is working correctly
   * Ensure the client is properly handling ping/pong messages
   * Check network stability and connection timeout settings
   * Review connection cleanup logs

### Debugging Tools

1. **Browser Developer Tools**: Use the Network tab to view WebSocket connection status and message stream
2. **Server Logs**: Check logs with `RUST_LOG=debug` for detailed information
3. **Health Endpoint**: Monitor service status at `http://localhost:8081/health`
4. **Kafka Tools**: Use `kafka-console-consumer` to verify topic data
5. **Built-in Test Client**: Use `websocket-frontend.html` for interactive testing

### Log Analysis
```bash
# Enable debug logging
RUST_LOG=debug ./target/release/superstack-websocket

# Key log messages to look for:
# - "🚀 Starting SuperStack WebSocket Service"
# - "🌐 WebSocket service listening on 0.0.0.0:8081"
# - "🔌 Starting Kafka consumer..."
# - "✅ Kafka consumer started in background"
# - "New WebSocket connection: <connection_id>"
# - "🔄 Broadcasting <MessageType> message to all_updates"
# - "Connection <id> established, ready for subscriptions"
```

### Configuration Validation
```bash
# Check environment variables
echo "KAFKA_BOOTSTRAP_SERVERS: $KAFKA_BOOTSTRAP_SERVERS"
echo "WEBSOCKET_PORT: $WEBSOCKET_PORT"
echo "DEV_MODE: $DEV_MODE"
echo "API_SERVICE_URL: $API_SERVICE_URL"

# Test Kafka connectivity
kafka-console-consumer --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --topic candles --from-beginning --max-messages 1
```

## Support

If you encounter issues, please check:
- Server log files (enable with `RUST_LOG=debug`)
- Browser console errors and WebSocket connection status
- Network connection status and firewall settings
- Kafka service status and topic availability
- Environment variable configuration
- Authentication token validity (production mode)

For additional support, please refer to the project documentation or contact the development team.