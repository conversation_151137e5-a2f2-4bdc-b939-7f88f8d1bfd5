[package]
name = "superstack-websocket"
version.workspace = true
edition.workspace = true

[dependencies]
tokio = { workspace = true }
axum = { workspace = true, features = ["ws"] }
tower-http = { workspace = true }
tower = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
futures-util = { workspace = true, features = ["sink"] }
uuid = { workspace = true }
lru = { workspace = true }
dashmap = { workspace = true }
async-trait = { workspace = true }
reqwest = { workspace = true }
chrono = { workspace = true }
rdkafka = { workspace = true }
bincode = { workspace = true }
dotenv = { workspace = true }
alloy = { workspace = true }
superstack-api = { path = "../superstack-api" }
superstack-data = { path = "../superstack-data" }

[[bin]]
name = "websocket"
path = "src/main.rs"