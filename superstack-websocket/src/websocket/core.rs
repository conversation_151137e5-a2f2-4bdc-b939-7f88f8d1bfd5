use crate::websocket::{
    traits::{
        <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MetricsCollector, NoOpMetrics, SubscriptionManager,
        WebSocketService,
    },
    types::{
        ClientMessage, ConnectionInfo, MessageSender, Result, ServerMessage, SubscriptionFilters,
        WebSocketConfig, WebSocketError,
    },
};
use async_trait::async_trait;
use axum::extract::ws::Message;
use std::{
    collections::HashMap,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::{mpsc, RwLock};
use tracing::{debug, info, warn};

/// Core WebSocket manager implementing all traits
/// This provides a simple but extensible foundation that can scale to millions of connections
#[derive(Debug)]
pub struct CoreWebSocketManager<M = NoOpMetrics>
where
    M: MetricsCollector + 'static,
{
    /// Active connections: connection_id -> message_sender
    connections: Arc<RwLock<HashMap<String, MessageSender>>>,
    /// Subscriptions: subscription_type -> Vec<connection_id>
    subscriptions: Arc<RwLock<HashMap<String, Vec<String>>>>,
    /// Connection metadata for monitoring and extensions
    connection_info: Arc<RwLock<HashMap<String, ConnectionInfo>>>,
    /// Configuration
    config: WebSocketConfig,
    /// Metrics collector
    metrics: Arc<M>,
    /// Service start time for uptime calculation
    start_time: Instant,
}

impl<M> CoreWebSocketManager<M>
where
    M: MetricsCollector + 'static,
{
    pub fn new(config: WebSocketConfig, metrics: Arc<M>) -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            subscriptions: Arc::new(RwLock::new(HashMap::new())),
            connection_info: Arc::new(RwLock::new(HashMap::new())),
            config,
            metrics,
            start_time: Instant::now(),
        }
    }

    /// Create with default no-op metrics (for V1)
    pub fn with_default_metrics(config: WebSocketConfig) -> CoreWebSocketManager<NoOpMetrics> {
        CoreWebSocketManager::new(config, Arc::new(NoOpMetrics))
    }

    /// Start periodic cleanup task for stale connections
    pub fn start_cleanup_task(self: Arc<Self>) {
        let manager = self.clone();
        let timeout_duration = Duration::from_secs(self.config.connection_timeout_seconds);

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60)); // Check every minute

            loop {
                interval.tick().await;

                let now = chrono::Utc::now().timestamp();
                let mut stale_connections = Vec::new();

                {
                    let connection_info = manager.connection_info.read().await;
                    for (conn_id, info) in connection_info.iter() {
                        if now - info.last_activity > timeout_duration.as_secs() as i64 {
                            stale_connections.push(conn_id.clone());
                        }
                    }
                }

                if !stale_connections.is_empty() {
                    warn!("Cleaning up {} stale connections", stale_connections.len());
                    manager.cleanup_failed_connections(stale_connections).await;
                }
            }
        });
    }

    /// Internal helper to clean up failed connections
    async fn cleanup_failed_connections(&self, failed_ids: Vec<String>) {
        if failed_ids.is_empty() {
            return;
        }

        debug!("Cleaning up {} failed connections", failed_ids.len());

        for conn_id in failed_ids {
            if let Err(e) = ConnectionManager::remove_connection(self, &conn_id).await {
                warn!("Failed to cleanup connection {}: {}", conn_id, e);
            }
            self.metrics.record_connection_event("connection_cleanup");
        }
    }

    /// Check if connection limit is reached
    async fn check_connection_limit(&self) -> Result<()> {
        let count = self.connections.read().await.len();
        if count >= self.config.max_connections {
            self.metrics.record_error("connection_limit_exceeded");
            return Err(WebSocketError::ServiceUnavailable(format!(
                "Connection limit reached: {}",
                self.config.max_connections
            )));
        }
        Ok(())
    }

    /// Get subscription count for a connection
    async fn get_subscription_count(&self, conn_id: &str) -> usize {
        let connection_info = self.connection_info.read().await;
        connection_info.get(conn_id).map(|info| info.subscriptions.len()).unwrap_or(0)
    }
}

#[async_trait]
impl<M> ConnectionManager for CoreWebSocketManager<M>
where
    M: MetricsCollector + 'static,
{
    async fn add_connection(&self, id: String, sender: MessageSender) -> Result<()> {
        // Check connection limit
        self.check_connection_limit().await?;

        let mut connections = self.connections.write().await;
        let mut connection_info = self.connection_info.write().await;

        connections.insert(id.clone(), sender);
        let now = chrono::Utc::now().timestamp();
        connection_info.insert(
            id.clone(),
            ConnectionInfo {
                id: id.clone(),
                connected_at: now,
                last_activity: now,
                subscriptions: Vec::new(),
                subscription_filters: std::collections::HashMap::new(),
                metadata: None,
            },
        );

        let count = connections.len();
        drop(connections);
        drop(connection_info);

        self.metrics.record_connection_count(count);
        self.metrics.record_connection_event("connection_added");

        info!("WebSocket connection added: {} (total: {})", id, count);
        Ok(())
    }

    async fn remove_connection(&self, id: &str) -> Result<()> {
        let mut connections = self.connections.write().await;
        let mut subscriptions = self.subscriptions.write().await;
        let mut connection_info = self.connection_info.write().await;

        // Check if connection exists before attempting to remove
        let connection_existed = connections.contains_key(id);

        if !connection_existed {
            debug!("Connection {} already removed or never existed", id);
            return Ok(()); // Not an error if connection doesn't exist
        }

        // Remove connection with proper cleanup
        if let Some(sender) = connections.remove(id) {
            // Try to close the sender channel gracefully
            // The receiver will detect this and stop trying to send messages
            drop(sender);
        }

        connection_info.remove(id);

        // Remove from all subscriptions efficiently
        for (_, subscribers) in subscriptions.iter_mut() {
            subscribers.retain(|conn_id| conn_id != id);
        }

        // Clean up empty subscription lists to prevent memory leaks
        subscriptions.retain(|_, subscribers| !subscribers.is_empty());

        let count = connections.len();
        drop(connections);
        drop(subscriptions);
        drop(connection_info);

        self.metrics.record_connection_count(count);
        self.metrics.record_connection_event("connection_removed");

        info!("WebSocket connection removed: {} (total: {})", id, count);
        Ok(())
    }

    async fn get_connection_count(&self) -> usize {
        self.connections.read().await.len()
    }

    async fn get_connection_info(&self, id: &str) -> Option<ConnectionInfo> {
        self.connection_info.read().await.get(id).cloned()
    }

    async fn update_connection_metadata(
        &self,
        id: &str,
        metadata: serde_json::Value,
    ) -> Result<()> {
        let mut connection_info = self.connection_info.write().await;
        if let Some(info) = connection_info.get_mut(id) {
            info.metadata = Some(metadata);
            Ok(())
        } else {
            Err(WebSocketError::ConnectionNotFound(id.to_string()))
        }
    }

    async fn update_last_activity(&self, id: &str) -> Result<()> {
        let mut connection_info = self.connection_info.write().await;
        if let Some(info) = connection_info.get_mut(id) {
            info.last_activity = chrono::Utc::now().timestamp();
            Ok(())
        } else {
            Err(WebSocketError::ConnectionNotFound(id.to_string()))
        }
    }
}

#[async_trait]
impl<M> SubscriptionManager for CoreWebSocketManager<M>
where
    M: MetricsCollector + 'static,
{
    async fn subscribe(&self, conn_id: &str, subscription_type: &str) -> Result<()> {
        // Check subscription limit for this connection
        let subscription_count = self.get_subscription_count(conn_id).await;
        if subscription_count >= self.config.max_subscriptions_per_connection {
            self.metrics.record_error("subscription_limit_exceeded");
            return Err(WebSocketError::SubscriptionError(format!(
                "Subscription limit reached for connection {}: {}",
                conn_id, self.config.max_subscriptions_per_connection
            )));
        }

        let mut subscriptions = self.subscriptions.write().await;
        let mut connection_info = self.connection_info.write().await;

        // Add to subscription list
        let subscribers =
            subscriptions.entry(subscription_type.to_string()).or_insert_with(Vec::new);

        if !subscribers.contains(&conn_id.to_string()) {
            subscribers.push(conn_id.to_string());
        }

        // Update connection info
        if let Some(info) = connection_info.get_mut(conn_id) {
            if !info.subscriptions.contains(&subscription_type.to_string()) {
                info.subscriptions.push(subscription_type.to_string());
            }
        }

        self.metrics.record_subscription_event("subscription_added");
        debug!("Connection {} subscribed to {}", conn_id, subscription_type);
        Ok(())
    }

    async fn unsubscribe(&self, conn_id: &str, subscription_type: &str) -> Result<()> {
        let mut subscriptions = self.subscriptions.write().await;
        let mut connection_info = self.connection_info.write().await;

        // Remove from global subscription list
        if let Some(subscribers) = subscriptions.get_mut(subscription_type) {
            subscribers.retain(|id| id != conn_id);
        }

        // Update connection info - remove all filters for this subscription type
        if let Some(info) = connection_info.get_mut(conn_id) {
            info.subscriptions.retain(|sub| sub != subscription_type);
            info.subscription_filters.remove(subscription_type);
            tracing::info!(
                "🗑️ Removed all filters for connection {} subscription {}",
                conn_id,
                subscription_type
            );
        }

        self.metrics.record_subscription_event("subscription_removed");
        debug!("Connection {} unsubscribed from {}", conn_id, subscription_type);
        Ok(())
    }

    async fn get_subscribers(&self, subscription_type: &str) -> Vec<String> {
        let subscriptions = self.subscriptions.read().await;
        let result = subscriptions.get(subscription_type).cloned().unwrap_or_default();
        debug!(
            "get_subscribers for '{}': found {} subscribers: {:?}",
            subscription_type,
            result.len(),
            result
        );
        debug!("All subscriptions: {:?}", subscriptions.keys().collect::<Vec<_>>());

        // Additional debugging: show all subscription details
        for (sub_type, subscribers) in subscriptions.iter() {
            debug!(
                "Subscription '{}' has {} subscribers: {:?}",
                sub_type,
                subscribers.len(),
                subscribers
            );
        }

        // Also check connection_info to see if there's a mismatch
        let connection_info = self.connection_info.read().await;
        debug!("Total connections in connection_info: {}", connection_info.len());
        for (conn_id, info) in connection_info.iter() {
            debug!("Connection '{}' has subscriptions: {:?}", conn_id, info.subscriptions);
        }

        result
    }

    async fn get_connection_subscriptions(&self, conn_id: &str) -> Vec<String> {
        let connection_info = self.connection_info.read().await;
        connection_info.get(conn_id).map(|info| info.subscriptions.clone()).unwrap_or_default()
    }
}

#[async_trait]
impl<M> MessageHandler for CoreWebSocketManager<M>
where
    M: MetricsCollector + 'static,
{
    async fn handle_client_message(&self, conn_id: &str, message: ClientMessage) -> Result<()> {
        match message {
            ClientMessage::Subscribe { subscription_type, filters } => {
                // Validate filters for the subscription type if provided
                if let Some(ref filters) = filters {
                    if let Err(validation_error) =
                        filters.validate_for_subscription(&subscription_type)
                    {
                        let error_response = ServerMessage::Error {
                            message: format!("Filter validation failed: {}", validation_error),
                        };
                        MessageHandler::send_to_connection(self, conn_id, error_response).await?;
                        return Ok(());
                    }
                }

                self.subscribe(conn_id, &subscription_type).await?;

                // Store filters in connection metadata
                let mut connection_info = self.connection_info.write().await;
                if let Some(info) = connection_info.get_mut(conn_id) {
                    let filter_list = info
                        .subscription_filters
                        .entry(subscription_type.clone())
                        .or_insert_with(Vec::new);

                    if let Some(filters) = &filters {
                        // Add specific filter
                        filter_list.push(filters.clone());
                        tracing::info!(
                            "📝 Added filter for connection {} subscription {}: pair_address={:?}, chain={:?}",
                            conn_id,
                            subscription_type,
                            filters.pair_address,
                            filters.chain
                        );
                    } else {
                        // No filters provided - if list is empty, keep it empty (means accept all)
                        tracing::info!(
                            "⚠️ No filters for connection {} subscription {} (accept all)",
                            conn_id,
                            subscription_type
                        );
                    }

                    tracing::info!(
                        "📊 Connection {} subscription {} now has {} filters",
                        conn_id,
                        subscription_type,
                        filter_list.len()
                    );
                } else {
                    tracing::error!("❌ Failed to find connection info for {}", conn_id);
                }

                let response = ServerMessage::Subscribed {
                    subscription_type: subscription_type.clone(),
                    filters: filters.clone(),
                };
                MessageHandler::send_to_connection(self, conn_id, response).await?;

                debug!(
                    "Connection {} subscribed to {} with filters: {:?}",
                    conn_id, subscription_type, filters
                );
                Ok(())
            }
            ClientMessage::Unsubscribe { subscription_type } => {
                self.unsubscribe(conn_id, &subscription_type).await?;

                // Clear filters from connection metadata
                let mut connection_info = self.connection_info.write().await;
                if let Some(info) = connection_info.get_mut(conn_id) {
                    info.subscription_filters.remove(&subscription_type);
                }

                let response =
                    ServerMessage::Unsubscribed { subscription_type: subscription_type.clone() };
                MessageHandler::send_to_connection(self, conn_id, response).await?;

                debug!("Connection {} unsubscribed from {}", conn_id, subscription_type);
                Ok(())
            }
            ClientMessage::Ping => {
                MessageHandler::send_to_connection(self, conn_id, ServerMessage::Pong).await?;
                debug!("Ping received from {}", conn_id);
                Ok(())
            }
        }
    }

    async fn broadcast_message(&self, message: ServerMessage) -> Result<()> {
        let connections = self.connections.read().await;
        let mut failed_connections = Vec::new();

        // Convert to unified WebSocket message format
        let current_time =
            std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs()
                as i64;
        let websocket_message = message.to_websocket_message(current_time, None);
        let json_message = serde_json::to_string(&websocket_message)?;
        self.metrics.record_message_sent("broadcast");

        for (conn_id, sender) in connections.iter() {
            match sender.try_send(Message::Text(json_message.clone().into())) {
                Ok(_) => {
                    // Message sent successfully
                }
                Err(mpsc::error::TrySendError::Full(_)) => {
                    debug!("Message queue full for connection: {}", conn_id);
                    // Don't treat full queue as a failed connection
                }
                Err(mpsc::error::TrySendError::Closed(_)) => {
                    debug!("Connection {} is closed, marking for cleanup", conn_id);
                    failed_connections.push(conn_id.clone());
                }
            }
        }

        drop(connections);

        // Clean up failed connections asynchronously
        if !failed_connections.is_empty() {
            let manager = self.clone();
            tokio::spawn(async move {
                manager.cleanup_failed_connections(failed_connections).await;
            });
        }

        Ok(())
    }

    async fn send_to_connection(&self, conn_id: &str, message: ServerMessage) -> Result<()> {
        // Use read lock initially for better performance
        let connections = self.connections.read().await;

        if let Some(sender) = connections.get(conn_id) {
            // Convert to unified WebSocket message format
            let current_time = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64;
            let websocket_message = message.to_websocket_message(current_time, None);
            let json_message = serde_json::to_string(&websocket_message)?;

            // Use try_send for non-blocking operation
            // This prevents the service from being blocked by a slow/stuck client
            match sender.try_send(Message::Text(json_message.into())) {
                Ok(_) => {
                    // Message sent successfully
                    drop(connections);
                    self.metrics.record_message_sent("direct");
                    return Ok(());
                }
                Err(mpsc::error::TrySendError::Full(_)) => {
                    // Channel is full - client is slow/stuck
                    debug!("Message queue full for connection: {}", conn_id);
                    drop(connections);
                    self.metrics.record_error("channel_full");
                    return Err(WebSocketError::SendError(format!(
                        "Message queue full for connection: {}",
                        conn_id
                    )));
                }
                Err(mpsc::error::TrySendError::Closed(_)) => {
                    // Channel is closed - connection is dead
                    debug!("Connection {} is closed", conn_id);
                    drop(connections);

                    // Clean up the failed connection asynchronously
                    let manager = self.clone();
                    let conn_id_clone = conn_id.to_string();
                    tokio::spawn(async move {
                        manager.cleanup_failed_connections(vec![conn_id_clone]).await;
                    });

                    self.metrics.record_error("connection_closed");
                    return Err(WebSocketError::SendError(format!(
                        "Connection {} is closed",
                        conn_id
                    )));
                }
            }
        } else {
            drop(connections);
            self.metrics.record_error("connection_not_found");
            return Err(WebSocketError::ConnectionNotFound(conn_id.to_string()));
        }
    }

    async fn send_to_subscribers(&self, subscription: &str, message: ServerMessage) -> Result<()> {
        let subscribers = self.get_subscribers(subscription).await;
        if subscribers.is_empty() {
            debug!("No subscribers for: {}", subscription);
            return Ok(());
        }

        self.metrics.record_subscription_event("message_broadcast");
        debug!(
            "Broadcasting to {} subscribers of {} - subscribers: {:?}",
            subscribers.len(),
            subscription,
            subscribers
        );

        let connections = self.connections.read().await;
        let connection_info = self.connection_info.read().await;
        let mut failed_connections = Vec::new();

        let current_time =
            std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs()
                as i64;

        for conn_id in subscribers.iter() {
            // Check if message matches connection's filters and get filters for params
            let (should_send, filters) = if let Some(info) = connection_info.get(conn_id) {
                if let Some(filter_list) = info.subscription_filters.get(subscription) {
                    let matches = SubscriptionFilters::any_matches_message(
                        filter_list,
                        subscription,
                        &message,
                    );
                    tracing::info!(
                        "🔍 Filter check for connection {}: subscription={}, matches={}, filter_count={}",
                        conn_id,
                        subscription,
                        matches,
                        filter_list.len()
                    );

                    // For params, use the first filter if available
                    let first_filter = filter_list.first();
                    (matches, first_filter)
                } else {
                    tracing::info!(
                        "⚠️ No filters for connection {} subscription {}, allowing message",
                        conn_id,
                        subscription
                    );
                    (true, None) // No filters means accept all messages
                }
            } else {
                tracing::info!("⚠️ No connection info for {}, allowing message", conn_id);
                (true, None) // No connection info means accept all messages
            };

            if !should_send {
                tracing::info!("Message filtered out for subscriber: {}", conn_id);
                continue;
            }

            // Convert to unified WebSocket message format with connection-specific filters
            // If no filters, the message will extract params from content automatically
            let websocket_message = message.to_websocket_message(current_time, filters);
            let json_message = serde_json::to_string(&websocket_message)?;

            if let Some(sender) = connections.get(conn_id) {
                match sender.try_send(Message::Text(json_message.into())) {
                    Ok(_) => {
                        // Message sent successfully
                    }
                    Err(mpsc::error::TrySendError::Full(_)) => {
                        debug!("Message queue full for subscriber: {}", conn_id);
                        // Don't treat full queue as a failed connection
                    }
                    Err(mpsc::error::TrySendError::Closed(_)) => {
                        debug!("Subscriber {} connection is closed", conn_id);
                        failed_connections.push(conn_id.clone());
                    }
                }
            } else {
                // Connection not found - might have been cleaned up already
                debug!("Subscriber connection not found: {}", conn_id);
                failed_connections.push(conn_id.clone());
            }
        }

        drop(connections);

        // Clean up failed connections asynchronously
        if !failed_connections.is_empty() {
            let manager = self.clone();
            tokio::spawn(async move {
                manager.cleanup_failed_connections(failed_connections).await;
            });
        }

        self.metrics.record_message_sent("subscription");
        Ok(())
    }
}

#[async_trait]
impl<M> WebSocketService for CoreWebSocketManager<M>
where
    M: MetricsCollector + 'static,
{
    async fn initialize(&self) -> Result<()> {
        info!("WebSocket service initialized");
        self.metrics.record_service_event("service_initialized");
        Ok(())
    }

    async fn handle_connection(&self, conn_id: String, sender: MessageSender) -> Result<()> {
        self.add_connection(conn_id.clone(), sender).await?;

        let response = ServerMessage::Connected { connection_id: conn_id.clone() };
        MessageHandler::send_to_connection(self, &conn_id, response).await?;

        // Note: Auto-subscription to all_updates has been removed
        // Clients must now explicitly subscribe to desired update types
        info!("Connection {} established, ready for subscriptions", conn_id);

        Ok(())
    }

    async fn process_message(&self, conn_id: &str, message: ClientMessage) -> Result<()> {
        self.handle_client_message(conn_id, message).await
    }

    async fn broadcast_update(
        &self,
        subscription_type: &str,
        message: ServerMessage,
    ) -> Result<()> {
        self.send_to_subscribers(subscription_type, message).await
    }

    async fn update_last_activity(&self, conn_id: &str) -> Result<()> {
        ConnectionManager::update_last_activity(self, conn_id).await
    }

    async fn get_stats(&self) -> serde_json::Value {
        let connection_count = self.get_connection_count().await;
        let uptime_seconds = self.start_time.elapsed().as_secs();

        // Get simplified subscription stats
        let connection_info = self.connection_info.read().await;
        let mut subscription_stats = std::collections::HashMap::new();
        let mut total_filters = 0;

        for info in connection_info.values() {
            for (sub_type, filter_list) in &info.subscription_filters {
                total_filters += filter_list.len();

                let entry = subscription_stats.entry(sub_type.clone()).or_insert_with(|| {
                    serde_json::json!({
                        "connections": 0,
                        "total_filters": 0
                    })
                });

                entry["connections"] = (entry["connections"].as_u64().unwrap_or(0) + 1).into();
                entry["total_filters"] = (entry["total_filters"].as_u64().unwrap_or(0) +
                    filter_list.len() as u64)
                    .into();
            }
        }

        serde_json::json!({
            "connection_count": connection_count,
            "max_connections": self.config.max_connections,
            "uptime_seconds": uptime_seconds,
            "total_filters": total_filters,
            "subscription_stats": subscription_stats,
            "status": "healthy"
        })
    }

    async fn shutdown(&self) -> Result<()> {
        info!("WebSocket service shutting down");

        // Send shutdown message to all connections
        let shutdown_message =
            ServerMessage::Error { message: "Service shutting down".to_string() };
        if let Err(e) = self.broadcast_message(shutdown_message).await {
            warn!("Error sending shutdown message: {}", e);
        }

        // Clear all connections
        self.connections.write().await.clear();
        self.subscriptions.write().await.clear();
        self.connection_info.write().await.clear();

        self.metrics.record_service_event("service_shutdown");
        info!("WebSocket service shutdown complete");
        Ok(())
    }

    async fn remove_connection(&self, conn_id: &str) -> Result<()> {
        ConnectionManager::remove_connection(self, conn_id).await
    }

    async fn send_to_connection(&self, conn_id: &str, message: ServerMessage) -> Result<()> {
        MessageHandler::send_to_connection(self, conn_id, message).await
    }
}

impl<M> Clone for CoreWebSocketManager<M>
where
    M: MetricsCollector + 'static,
{
    fn clone(&self) -> Self {
        Self {
            connections: self.connections.clone(),
            subscriptions: self.subscriptions.clone(),
            connection_info: self.connection_info.clone(),
            config: self.config.clone(),
            metrics: self.metrics.clone(),
            start_time: self.start_time,
        }
    }
}

/// Type alias for the default WebSocket manager
pub type DefaultWebSocketManager = CoreWebSocketManager<NoOpMetrics>;
