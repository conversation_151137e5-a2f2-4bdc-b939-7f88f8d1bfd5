use crate::{
    auth::UserInfo,
    websocket::{
        traits::WebSocketService,
        types::{ClientMessage, ServerMessage, WebSocketError},
    },
};
use axum::extract::ws::{Message, WebSocket};
use futures_util::{SinkExt, StreamExt};
use std::sync::Arc;
use tokio::{
    sync::mpsc,
    time::{timeout, Duration},
};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Smart error handling to reduce noise in logs during high concurrency
fn should_log_error_at_error_level(e: &WebSocketError) -> bool {
    match e {
        WebSocketError::ConnectionNotFound(_) => false,
        WebSocketError::SendError(msg) if msg.contains("Channel") => false,
        _ => true,
    }
}

/// Handle a new WebSocket connection with authentication info
pub async fn handle_websocket_connection_with_auth<S>(
    socket: WebSocket,
    websocket_service: Arc<S>,
    user_info: UserInfo,
) where
    S: WebSocketService + 'static,
{
    let connection_id = format!("{}_{}", user_info.user_id, Uuid::new_v4());
    info!(
        "New authenticated WebSocket connection: {} for user: {}",
        connection_id, user_info.user_id
    );

    let (sender, receiver) = socket.split();
    let buffer_size =
        std::env::var("WEBSOCKET_BUFFER_SIZE").ok().and_then(|s| s.parse().ok()).unwrap_or(10_000);
    let (tx, rx) = mpsc::channel(buffer_size);

    // Add connection to service with user info metadata
    let _user_metadata = serde_json::json!({
        "user_id": user_info.user_id,
        "wallet_address": user_info.wallet_address,
        "subscription_level": user_info.subscription_level
    });

    if let Err(e) = websocket_service.handle_connection(connection_id.clone(), tx).await {
        error!("Failed to handle connection {}: {}", connection_id, e);
        return;
    }

    // Update connection with user metadata
    // Note: This requires the websocket_service to implement ConnectionManager trait
    // For now, we'll skip this step and add it later when we refactor the trait hierarchy

    // Send welcome message with connection info
    let welcome_msg = ServerMessage::Connected { connection_id: connection_id.clone() };
    if let Err(e) = websocket_service.send_to_connection(&connection_id, welcome_msg).await {
        error!("Failed to send welcome message: {}", e);
    }

    // Rest of the connection handling is the same as the original function
    handle_connection_tasks(websocket_service, connection_id, sender, receiver, rx).await;
}

/// Handle a new WebSocket connection using the trait-based service
pub async fn handle_websocket_connection<S>(socket: WebSocket, websocket_service: Arc<S>)
where
    S: WebSocketService + 'static,
{
    let connection_id = Uuid::new_v4().to_string();
    info!("New WebSocket connection: {}", connection_id);

    let (sender, receiver) = socket.split();
    let buffer_size =
        std::env::var("WEBSOCKET_BUFFER_SIZE").ok().and_then(|s| s.parse().ok()).unwrap_or(10_000);
    let (tx, rx) = mpsc::channel(buffer_size);

    // Add connection to service
    if let Err(e) = websocket_service.handle_connection(connection_id.clone(), tx).await {
        error!("Failed to handle connection {}: {}", connection_id, e);
        return;
    }

    handle_connection_tasks(websocket_service, connection_id, sender, receiver, rx).await;
}

/// Common connection task handling for both authenticated and unauthenticated connections
async fn handle_connection_tasks<S>(
    websocket_service: Arc<S>,
    connection_id: String,
    mut sender: futures_util::stream::SplitSink<WebSocket, Message>,
    mut receiver: futures_util::stream::SplitStream<WebSocket>,
    mut rx: mpsc::Receiver<Message>,
) where
    S: WebSocketService + 'static,
{
    // Get ping interval from environment or use default
    let ping_interval_seconds = std::env::var("WEBSOCKET_PING_INTERVAL_SECONDS")
        .ok()
        .and_then(|s| s.parse().ok())
        .unwrap_or(30);

    // Spawn task to handle outgoing messages with proper error handling
    let connection_id_clone = connection_id.clone();
    let send_task = tokio::spawn(async move {
        while let Some(message) = rx.recv().await {
            match sender.send(message).await {
                Ok(_) => {
                    // Message sent successfully
                }
                Err(e) => {
                    debug!("Send failed for connection {}: {}", connection_id_clone, e);
                    // Close the receiver channel to signal task completion
                    rx.close();
                    break;
                }
            }
        }

        debug!("Send task completed for connection: {}", connection_id_clone);
    });
    let send_abort_handle = send_task.abort_handle();

    // Spawn heartbeat task to send periodic pings
    let service_clone_heartbeat = websocket_service.clone();
    let connection_id_heartbeat = connection_id.clone();
    let heartbeat_task = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(ping_interval_seconds));

        loop {
            interval.tick().await;

            // Send ping message to keep connection alive
            let ping_msg = ServerMessage::Ping;
            if let Err(e) =
                service_clone_heartbeat.send_to_connection(&connection_id_heartbeat, ping_msg).await
            {
                debug!("Failed to send ping to connection {}: {}", connection_id_heartbeat, e);
                break;
            }

            debug!("Sent ping to connection: {}", connection_id_heartbeat);
        }

        debug!("Heartbeat task completed for connection: {}", connection_id_heartbeat);
    });
    let heartbeat_abort_handle = heartbeat_task.abort_handle();

    // Handle incoming messages with timeout and proper error handling
    let service_clone = websocket_service.clone();
    let connection_id_clone = connection_id.clone();
    let receive_task = tokio::spawn(async move {
        loop {
            // Increase timeout to be more lenient (5 minutes instead of 2 minutes)
            match timeout(Duration::from_secs(300), receiver.next()).await {
                Ok(Some(msg)) => {
                    match msg {
                        Ok(Message::Text(text)) => {
                            // Update last activity time when receiving any text message
                            if let Err(e) =
                                service_clone.update_last_activity(&connection_id_clone).await
                            {
                                debug!(
                                    "Failed to update last activity for {}: {}",
                                    connection_id_clone, e
                                );
                            }

                            if let Err(e) =
                                handle_client_message(&service_clone, &connection_id_clone, &text)
                                    .await
                            {
                                // Use smart error logging
                                if should_log_error_at_error_level(&e) {
                                    error!("Error handling client message: {:?}", e);
                                } else {
                                    debug!("Error handling client message: {:?}", e);
                                }

                                // Send error message back to client only for non-connection errors
                                if should_log_error_at_error_level(&e) {
                                    let error_msg =
                                        ServerMessage::Error { message: format!("{:?}", e) };
                                    if let Err(send_err) = service_clone
                                        .send_to_connection(&connection_id_clone, error_msg)
                                        .await
                                    {
                                        error!("Failed to send error message: {:?}", send_err);
                                        break;
                                    }
                                }
                            }
                        }
                        Ok(Message::Close(_)) => {
                            info!("WebSocket connection closed by client: {}", connection_id_clone);
                            break;
                        }
                        Ok(Message::Ping(_)) => {
                            debug!("Received ping from {}", connection_id_clone);
                            // Update last activity time when receiving ping
                            if let Err(e) =
                                service_clone.update_last_activity(&connection_id_clone).await
                            {
                                debug!(
                                    "Failed to update last activity for {}: {}",
                                    connection_id_clone, e
                                );
                            }
                            // Axum automatically handles pong responses
                        }
                        Ok(Message::Pong(_)) => {
                            debug!("Received pong from {}", connection_id_clone);
                            // Update last activity time when receiving pong
                            if let Err(e) =
                                service_clone.update_last_activity(&connection_id_clone).await
                            {
                                debug!(
                                    "Failed to update last activity for {}: {}",
                                    connection_id_clone, e
                                );
                            }
                        }
                        Ok(Message::Binary(_)) => {
                            warn!("Received binary message from {}, ignoring", connection_id_clone);
                        }
                        Err(e) => {
                            error!("WebSocket error for connection {}: {}", connection_id_clone, e);
                            break;
                        }
                    }
                }
                Ok(None) => {
                    debug!("WebSocket stream ended for connection: {}", connection_id_clone);
                    break;
                }
                Err(_) => {
                    warn!("Connection timeout for: {}", connection_id_clone);
                    break;
                }
            }
        }

        debug!("Receive task completed for connection: {}", connection_id_clone);
    });
    let receive_abort_handle = receive_task.abort_handle();

    // Wait for any task to complete
    let result = tokio::select! {
        send_result = send_task => {
            debug!("Send task completed first for connection: {}", connection_id);
            receive_abort_handle.abort();
            heartbeat_abort_handle.abort();
            send_result
        }
        receive_result = receive_task => {
            debug!("Receive task completed first for connection: {}", connection_id);
            send_abort_handle.abort();
            heartbeat_abort_handle.abort();
            receive_result
        }
        heartbeat_result = heartbeat_task => {
            debug!("Heartbeat task completed first for connection: {}", connection_id);
            send_abort_handle.abort();
            receive_abort_handle.abort();
            heartbeat_result
        }
    };

    // Log any task errors
    if let Err(e) = result {
        if e.is_cancelled() {
            debug!("Task was cancelled for connection: {}", connection_id);
        } else {
            warn!("Task error for connection {}: {}", connection_id, e);
        }
    }

    // **CRITICAL FIX**: Always clean up the connection when handler finishes
    // Add retry mechanism for cleanup in case of transient failures
    let mut cleanup_attempts = 0;
    const MAX_CLEANUP_ATTEMPTS: u32 = 3;

    while cleanup_attempts < MAX_CLEANUP_ATTEMPTS {
        match websocket_service.remove_connection(&connection_id).await {
            Ok(_) => {
                info!("WebSocket connection cleaned up: {}", connection_id);
                break;
            }
            Err(e) => {
                cleanup_attempts += 1;
                warn!(
                    "Failed to remove connection {} (attempt {}): {:?}",
                    connection_id, cleanup_attempts, e
                );

                if cleanup_attempts < MAX_CLEANUP_ATTEMPTS {
                    // Wait a bit before retrying
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }
        }
    }

    if cleanup_attempts >= MAX_CLEANUP_ATTEMPTS {
        error!(
            "Failed to cleanup connection {} after {} attempts",
            connection_id, MAX_CLEANUP_ATTEMPTS
        );
    }

    info!("WebSocket connection handler finished: {}", connection_id);
}

/// Handle incoming client messages using the service
async fn handle_client_message<S>(
    websocket_service: &S,
    connection_id: &str,
    text: &str,
) -> Result<(), WebSocketError>
where
    S: WebSocketService,
{
    let client_message: ClientMessage =
        serde_json::from_str(text).map_err(WebSocketError::JsonError)?;

    websocket_service.process_message(connection_id, client_message).await?;

    Ok(())
}
