use crate::websocket::{
    traits::{DataSource, WebSocketService},
    types::{
        subscription_types, ApiDetailedTokenResponse, ApiMarketTokenResponse,
        ApiPoolStatisticResponse, ApiTimeStatistic, ApiTrade, DeduplicationConfig, KafkaConfig,
        ProcessingStats, Result, ServerMessage, WebSocketError,
    },
};
use alloy::primitives::U256;
use async_trait::async_trait;
use futures_util::StreamExt;
use rdkafka::Message;
use superstack_api::{jupiter::price::get_sol_price, MakerType};

use superstack_data::{
    kafka::{
        config::ConfluentConfig,
        topics::{CandlesMsg, ExtendedDexTradesMsg, ExtendedTokenHoldersMsg, NewTokenMsg},
        KafkaConsumer, KafkaConsumerConfig, KeyedTopic, TopicType,
    },
    postgres::aggregator::{ExtendedDexTrade, PoolStatistic, TokenStatistic},
};

use lru::LruCache;
use std::{
    num::NonZeroUsize,
    sync::{
        atomic::{AtomicU64, Ordering},
        Arc,
    },
    time::{Duration, SystemTime, UNIX_EPOCH},
};
use tokio::{sync::Mutex, time::interval};
use tracing::{debug, error, info, warn};

/// Kafka data source for WebSocket broadcasting with deduplication and filtering
pub struct KafkaDataSource<S>
where
    S: WebSocketService + 'static,
{
    websocket_service: Arc<S>,
    kafka_config: KafkaConfig,
    dedup_config: DeduplicationConfig,

    // Kafka consumer instance
    kafka_consumer: Option<Arc<KafkaConsumer>>,

    // Deduplication caches
    signature_cache: Arc<Mutex<LruCache<String, i64>>>,
    content_hash_cache: Arc<Mutex<LruCache<u64, i64>>>,

    // Processing statistics
    stats_counters: Arc<StatsCounters>,

    // Service state
    is_running: Arc<std::sync::atomic::AtomicBool>,
}

/// Atomic counters for statistics
#[derive(Debug)]
struct StatsCounters {
    messages_processed: AtomicU64,
    messages_filtered: AtomicU64,
    messages_deduplicated: AtomicU64,
    messages_broadcasted: AtomicU64,
    processing_errors: AtomicU64,
}

impl<S> KafkaDataSource<S>
where
    S: WebSocketService + 'static,
{
    pub fn new(
        websocket_service: Arc<S>,
        kafka_config: KafkaConfig,
        dedup_config: DeduplicationConfig,
    ) -> Result<Self> {
        let signature_cache = Arc::new(Mutex::new(LruCache::new(
            NonZeroUsize::new(dedup_config.cache_size).unwrap(),
        )));

        let content_hash_cache = Arc::new(Mutex::new(LruCache::new(
            NonZeroUsize::new(dedup_config.cache_size).unwrap(),
        )));

        let stats_counters = Arc::new(StatsCounters {
            messages_processed: AtomicU64::new(0),
            messages_filtered: AtomicU64::new(0),
            messages_deduplicated: AtomicU64::new(0),
            messages_broadcasted: AtomicU64::new(0),
            processing_errors: AtomicU64::new(0),
        });

        Ok(Self {
            websocket_service,
            kafka_config,
            dedup_config,
            kafka_consumer: None,
            signature_cache,
            content_hash_cache,
            stats_counters,
            is_running: Arc::new(std::sync::atomic::AtomicBool::new(false)),
        })
    }

    /// Start statistics reporting task
    fn start_stats_task(self: Arc<Self>) {
        let stats_task = self.clone();
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(60)); // Report every minute

            while stats_task.is_running.load(Ordering::Relaxed) {
                interval.tick().await;

                let current_time =
                    SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() as i64;

                let stats = ProcessingStats {
                    messages_processed: stats_task
                        .stats_counters
                        .messages_processed
                        .load(Ordering::Relaxed),
                    messages_filtered: stats_task
                        .stats_counters
                        .messages_filtered
                        .load(Ordering::Relaxed),
                    messages_deduplicated: stats_task
                        .stats_counters
                        .messages_deduplicated
                        .load(Ordering::Relaxed),
                    messages_broadcasted: stats_task
                        .stats_counters
                        .messages_broadcasted
                        .load(Ordering::Relaxed),
                    processing_errors: stats_task
                        .stats_counters
                        .processing_errors
                        .load(Ordering::Relaxed),
                    last_processed_timestamp: current_time,
                };

                info!("Kafka processing stats: {:?}", stats);

                // Broadcast statistics to subscribers with reasonable estimates
                let stats_message = ServerMessage::Statistics {
                    total_tokens: stats.messages_processed / 100, /* Rough estimate: ~100
                                                                   * messages per token */
                    total_trades_24h: stats.messages_processed,
                    total_volume_24h: (stats.messages_processed as f64) * 50.0, /* Estimate: $50 avg per trade */
                    active_pools: stats.messages_processed / 200, /* Rough estimate: ~200
                                                                   * messages per pool */
                };

                // Send to all_updates subscribers first
                if let Err(e) = stats_task
                    .websocket_service
                    .broadcast_update(subscription_types::ALL_UPDATES, stats_message.clone())
                    .await
                {
                    warn!("Failed to broadcast statistics to all_updates: {}", e);
                }

                // Also send to specific statistics subscribers
                if let Err(e) = stats_task
                    .websocket_service
                    .broadcast_update(subscription_types::STATISTICS, stats_message)
                    .await
                {
                    warn!("Failed to broadcast statistics to statistics subscribers: {}", e);
                }
            }
        });
    }

    /// Get current processing statistics
    pub async fn get_stats(&self) -> ProcessingStats {
        let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() as i64;

        ProcessingStats {
            messages_processed: self.stats_counters.messages_processed.load(Ordering::Relaxed),
            messages_filtered: self.stats_counters.messages_filtered.load(Ordering::Relaxed),
            messages_deduplicated: self
                .stats_counters
                .messages_deduplicated
                .load(Ordering::Relaxed),
            messages_broadcasted: self.stats_counters.messages_broadcasted.load(Ordering::Relaxed),
            processing_errors: self.stats_counters.processing_errors.load(Ordering::Relaxed),
            last_processed_timestamp: current_time,
        }
    }

    /// Create Kafka consumer using new interface
    fn create_consumer(&self) -> Result<KafkaConsumer> {
        info!("🔌 Connecting to Kafka at: {}", self.kafka_config.bootstrap_servers);

        // Check if local Kafka or Confluent Kafka
        let (api_key, api_secret) = if self.kafka_config.bootstrap_servers.contains("localhost") ||
            self.kafka_config.bootstrap_servers.contains("127.0.0.1")
        {
            // Local Kafka - use empty credentials (PLAINTEXT protocol)
            info!("🏠 Using local Kafka configuration (PLAINTEXT)");
            ("".to_string(), "".to_string())
        } else {
            // Confluent Kafka - use authentication credentials from environment variables
            info!("☁️ Using Confluent Kafka configuration (SASL_SSL)");
            (
                std::env::var("KAFKA_API_KEY").unwrap_or_else(|_| "".to_string()),
                std::env::var("KAFKA_API_SECRET").unwrap_or_else(|_| "".to_string()),
            )
        };

        let confluent_config =
            ConfluentConfig::new(self.kafka_config.bootstrap_servers.clone(), api_key, api_secret);

        // Create KafkaConsumer
        let consumer_config = KafkaConsumerConfig::new_latest(&self.kafka_config.group_id);
        let consumer = KafkaConsumer::new(&confluent_config, &consumer_config).map_err(|e| {
            error!("❌ Failed to create Kafka consumer: {}", e);
            WebSocketError::KafkaError(format!("Failed to create consumer: {}", e))
        })?;

        // Subscribe to specified topics using TopicType enums
        let topics = vec![
            TopicType::Candles,
            TopicType::ExtendedDexTrades,
            TopicType::ExtendedTokenHolders,
            TopicType::PoolStatistic,
            TopicType::TokenStatistic,
            TopicType::NewToken,
        ];
        let topic_names: Vec<String> = topics.iter().map(|t| t.to_string()).collect();
        info!("📋 Subscribing to topics: {:?}", topic_names);

        consumer.subscribe_topics(&topics).map_err(|e| {
            error!("❌ Failed to subscribe to topics: {}", e);
            WebSocketError::KafkaError(format!("Failed to subscribe to topics: {}", e))
        })?;

        info!("✅ Kafka consumer created and subscribed to topics successfully");
        Ok(consumer)
    }

    /// Process a single Kafka message from the new interface
    async fn process_kafka_message<'a>(
        &self,
        message: &rdkafka::message::BorrowedMessage<'a>,
    ) -> Result<()> {
        let topic = message.topic();
        let payload = message
            .payload()
            .ok_or_else(|| WebSocketError::ValidationError("Empty message payload".to_string()))?;

        info!(
            "📨 Received Kafka message from topic: {} (payload size: {} bytes)",
            topic,
            payload.len()
        );
        self.stats_counters.messages_processed.fetch_add(1, Ordering::Relaxed);

        debug!("🔍 Processing message from topic: {}", topic);
        if payload.len() > 0 {
            let preview = if payload.len() > 100 {
                format!("{}...", String::from_utf8_lossy(&payload[..100]))
            } else {
                String::from_utf8_lossy(payload).to_string()
            };
            debug!("📄 Payload preview: {}", preview);
        }

        // Process message based on topic type
        match topic {
            "extended_dex_trades" => {
                // Process trade data
                match ExtendedDexTradesMsg::deserialize_payload(payload) {
                    Ok(trades_msg) => {
                        info!(
                            "🔄 Processing ExtendedDexTradesMsg with {} trades",
                            trades_msg.trades.len()
                        );
                        // Send each trade as a separate update
                        for trade_data in trades_msg.trades {
                            match self.convert_extended_dex_trade_to_api(&trade_data).await {
                                Ok(api_trade) => {
                                    let server_msg = ServerMessage::TradeUpdate(api_trade);
                                    self.broadcast_message(server_msg).await?;
                                }
                                Err(e) => {
                                    error!("❌ Failed to convert trade to API format: {}", e);
                                    continue; // Skip this trade but continue with others
                                }
                            }
                        }
                    }
                    Err(e) => {
                        error!(
                            "❌ Failed to deserialize ExtendedDexTradesMsg from topic {}: {}",
                            topic, e
                        );
                        error!("📄 Payload size: {} bytes", payload.len());
                        if payload.len() > 0 && payload.len() < 1000 {
                            error!("📄 Full payload: {:?}", payload);
                        }
                        self.stats_counters.processing_errors.fetch_add(1, Ordering::Relaxed);
                        return Ok(()); // Skip this message
                    }
                }
            }
            "token_statistic" => {
                // Process token statistics data
                match TokenStatistic::deserialize_payload(payload) {
                    Ok(token_stats) => {
                        info!(
                            "🔄 Processing TokenStatistic for token: {} (chain: {:?})",
                            token_stats.token_address, token_stats.chain
                        );

                        // Convert to detailed token response (existing functionality)
                        match self.convert_token_statistics_to_api(&token_stats) {
                            Ok(api_token) => {
                                let server_msg = ServerMessage::TokenUpdate(api_token);
                                self.broadcast_message(server_msg).await?;
                            }
                            Err(e) => {
                                error!("❌ Failed to convert TokenStatistic to API format: {}", e);
                            }
                        }

                        // Convert to market token response (new functionality)
                        match self.convert_token_statistics_to_market_response(&token_stats) {
                            Ok(market_token) => {
                                let market_server_msg =
                                    ServerMessage::MarketTokenUpdate(market_token);
                                self.broadcast_message(market_server_msg).await?;
                            }
                            Err(e) => {
                                error!(
                                    "❌ Failed to convert TokenStatistic to Market format: {}",
                                    e
                                );
                            }
                        }
                    }
                    Err(e) => {
                        error!(
                            "❌ Failed to deserialize TokenStatistic from topic {}: {}",
                            topic, e
                        );
                        error!("📄 Payload size: {} bytes", payload.len());
                        self.stats_counters.processing_errors.fetch_add(1, Ordering::Relaxed);
                        return Ok(()); // Skip this message
                    }
                }
            }
            "pool_statistic" => {
                // Process pool statistics data
                match PoolStatistic::deserialize_payload(payload) {
                    Ok(pool_stats) => {
                        debug!("Successfully deserialized PoolStatistic message");
                        let api_pool_stats = self.convert_pool_statistics_to_api(&pool_stats)?;
                        let server_msg = ServerMessage::PoolStatisticsUpdate(api_pool_stats);
                        self.broadcast_message(server_msg).await?;
                    }
                    Err(e) => {
                        warn!("Failed to deserialize PoolStatistic from topic {}: {}", topic, e);
                        return Ok(()); // Skip this message
                    }
                }
            }
            "new_token" => {
                // Process new token data
                match NewTokenMsg::deserialize_payload(payload) {
                    Ok(new_token_msg) => {
                        debug!(
                            "Successfully deserialized NewTokenMsg message for token: {}",
                            new_token_msg.token.token_address
                        );

                        // Convert to detailed token response (existing functionality)
                        match self.convert_token_statistics_to_api(&new_token_msg.token) {
                            Ok(api_token) => {
                                let server_msg = ServerMessage::TokenUpdate(api_token);
                                self.broadcast_message(server_msg).await?;
                            }
                            Err(e) => {
                                error!("❌ Failed to convert NewToken to API format: {}", e);
                            }
                        }

                        // Convert to market token response (new functionality)
                        match self.convert_token_statistics_to_market_response(&new_token_msg.token)
                        {
                            Ok(market_token) => {
                                let market_server_msg =
                                    ServerMessage::MarketTokenUpdate(market_token);
                                self.broadcast_message(market_server_msg).await?;
                            }
                            Err(e) => {
                                error!("❌ Failed to convert NewToken to Market format: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        warn!("Failed to deserialize NewTokenMsg from topic {}: {}", topic, e);
                        return Ok(()); // Skip this message
                    }
                }
            }
            "candles" => {
                // Process candle data
                match CandlesMsg::deserialize_payload(payload) {
                    Ok(candles_msg) => {
                        debug!(
                            "Successfully deserialized CandlesMsg message with {} candles",
                            candles_msg.candles.len()
                        );
                        // Send each candle as a separate update
                        for candle in candles_msg.candles {
                            let server_msg = ServerMessage::CandleUpdate(candle);
                            self.broadcast_message(server_msg).await?;
                        }
                    }
                    Err(e) => {
                        warn!("Failed to deserialize CandlesMsg from topic {}: {}", topic, e);
                        return Ok(()); // Skip this message
                    }
                }
            }
            "extended_token_holders" => {
                // Process token holders data
                match ExtendedTokenHoldersMsg::deserialize_payload(payload) {
                    Ok(holders_msg) => {
                        debug!("Successfully deserialized ExtendedTokenHoldersMsg message with {} holders", holders_msg.holders.len());
                        let api_holders =
                            self.convert_extended_token_holders_to_api(&holders_msg)?;
                        let server_msg = ServerMessage::TokenHoldersUpdate(api_holders);
                        self.broadcast_message(server_msg).await?;
                    }
                    Err(e) => {
                        warn!(
                            "Failed to deserialize ExtendedTokenHoldersMsg from topic {}: {}",
                            topic, e
                        );
                        return Ok(()); // Skip this message
                    }
                }
            }
            _ => {
                warn!("⚠️ Received message from unknown/unhandled topic: {}", topic);
                warn!("📄 Payload size: {} bytes", payload.len());
                return Ok(());
            }
        }

        self.stats_counters.messages_broadcasted.fetch_add(1, Ordering::Relaxed);
        Ok(())
    }

    /// Broadcast message to WebSocket service
    async fn broadcast_message(&self, message: ServerMessage) -> Result<()> {
        let message_type = match &message {
            ServerMessage::TokenUpdate(_) => "TokenUpdate",
            ServerMessage::MarketTokenUpdate(_) => "MarketTokenUpdate",
            ServerMessage::TradeUpdate(_) => "TradeUpdate",
            ServerMessage::PoolStatisticsUpdate(_) => "PoolStatisticsUpdate",
            ServerMessage::CandleUpdate(_) => "CandleUpdate",
            ServerMessage::TokenHoldersUpdate(_) => "TokenHoldersUpdate",
            ServerMessage::Statistics { .. } => "Statistics",
            _ => "Other",
        };

        info!("🔄 Broadcasting {} message to all_updates", message_type);

        let specific_subscription_type = match &message {
            ServerMessage::TokenUpdate(_) => Some(subscription_types::TOKEN_UPDATES),
            ServerMessage::MarketTokenUpdate(_) => Some(subscription_types::MARKET_TOKEN_UPDATES),
            ServerMessage::TradeUpdate(_) => Some(subscription_types::TRADE_UPDATES),
            ServerMessage::PoolStatisticsUpdate(_) => Some(subscription_types::POOL_STATISTICS),
            ServerMessage::CandleUpdate(_) => Some(subscription_types::CANDLE_UPDATES),
            ServerMessage::TokenHoldersUpdate(_) => Some(subscription_types::TOKEN_HOLDERS),
            ServerMessage::Statistics { .. } => Some(subscription_types::STATISTICS),
            _ => None,
        };

        // Send to all_updates first
        match self
            .websocket_service
            .broadcast_update(subscription_types::ALL_UPDATES, message.clone())
            .await
        {
            Ok(_) => info!("✅ Successfully sent {} to all_updates", message_type),
            Err(e) => error!("❌ Failed to send {} to all_updates: {}", message_type, e),
        }

        // Send to specific subscription type
        if let Some(subscription_type) = specific_subscription_type {
            match self.websocket_service.broadcast_update(subscription_type, message).await {
                Ok(_) => info!("✅ Successfully sent {} to {}", message_type, subscription_type),
                Err(e) => {
                    error!("❌ Failed to send {} to {}: {}", message_type, subscription_type, e)
                }
            }
        }

        Ok(())
    }

    /// Convert ExtendedDexTrade to API Trade
    async fn convert_extended_dex_trade_to_api(
        &self,
        trade: &ExtendedDexTrade,
    ) -> Result<ApiTrade> {
        let maker_type = MakerType::new(trade.usd);

        // Calculate native price from base_ui_amount and token_ui_amount
        let price_native = if trade.token_ui_amount > 0.0 {
            trade.base_ui_amount / trade.token_ui_amount
        } else {
            0.0
        };

        // For WebSocket, use a simplified but more accurate approach
        // Use current SOL price instead of hardcoded value
        let sol_price = get_sol_price().await.unwrap_or(1.0);
        let sol_amount = trade.usd / sol_price;
        let base_token_symbol = "UNKNOWN".to_string(); // WebSocket doesn't have pool metadata access

        Ok(ApiTrade {
            tx_sig: trade.tx_hash.to_string(),
            maker: trade.maker_address.to_string(),
            maker_type,
            is_buy: trade.is_buy,
            token_ui_amount: trade.token_ui_amount,
            base_ui_amount: trade.base_ui_amount,
            usd: trade.usd,
            sol_amount,
            base_token_symbol,
            price_usd: trade.usd_price,
            price_native,
            market_cap_usd: trade.usd_market_cap,
            timestamp: trade.timestamp_millis as u64,
        })
    }

    /// Convert TokenStatistic to API MarketTokenResponse
    fn convert_token_statistics_to_market_response(
        &self,
        stats: &TokenStatistic,
    ) -> Result<ApiMarketTokenResponse> {
        // For WebSocket, we use the best_pool_address as the default pool
        // but we don't have direct access to pool DEX info here
        // We'll set pool_dex to None for now, as WebSocket updates are real-time
        // and the client should use the API for initial data with complete pool info
        let pool_address = if stats.best_pool_address.is_empty() {
            None
        } else {
            Some(stats.best_pool_address.clone())
        };

        Ok(ApiMarketTokenResponse {
            token_mint: stats.token_address.clone(),
            name: stats.name.clone(),
            symbol: stats.symbol.clone(),
            decimals: stats.decimals,
            image_url: stats.image.clone(),
            chain: stats.chain.to_string(),
            dex: stats.create_dex.to_string(), // Keep for backward compatibility
            pool_address,                      // Best pool address for trading
            pool_dex: None,                    /* WebSocket doesn't have pool DEX info, client
                                                * should use API for complete data */
            twitter: stats.twitter.clone(),
            telegram: stats.telegram.clone(),
            website: stats.website.clone(),
            price_usd: stats.usd_price,
            market_cap_usd: stats.usd_market_cap,
            fdv_usd: stats.usd_market_cap, // For now, same as market cap
            volume_usd: stats.usd_volume_24h, // Use 24h volume as default
            liquidity_usd: stats.usd_liquidity,
            price_change: stats.price_change_24h, // Use 24h price change as default
            candles: vec![],                      /* WebSocket doesn't provide candle data, use
                                                   * API for historical data */
        })
    }

    /// Convert TokenStatistic to API DetailedTokenResponse
    fn convert_token_statistics_to_api(
        &self,
        stats: &TokenStatistic,
    ) -> Result<ApiDetailedTokenResponse> {
        // Parse supply string to u64, handle overflow safely, then convert to UI amount
        let raw_supply = stats
            .u256_supply()
            .map(|u| {
                // Check if the U256 value can fit in u64
                if u > U256::from(u64::MAX) {
                    // If too large, use u64::MAX as fallback
                    u64::MAX
                } else {
                    // Safe to convert
                    u.to::<u64>()
                }
            })
            .unwrap_or(0);

        // Convert raw supply to UI amount using decimals
        let supply_ui_amount = raw_supply as f64 / (10_u64.pow(stats.decimals as u32) as f64);

        Ok(ApiDetailedTokenResponse {
            token_mint: stats.token_address.clone(),
            name: stats.name.clone(),
            symbol: stats.symbol.clone(),
            description: stats.description.clone(),
            image_url: stats.image.clone(),
            twitter: stats.twitter.clone(),
            telegram: stats.telegram.clone(),
            website: stats.website.clone(),
            launch_dex: Some(stats.create_dex.to_string()),
            launch_dex_url: None,
            decimals: stats.decimals,
            total_supply: supply_ui_amount,
            circulating_supply: supply_ui_amount,
            chain: stats.chain.to_string(),
            is_trench_token: stats.is_trench_token,
            default_pool_index: 0,
            current_pool_index: 0,
            is_user_selected: false,
            pools: vec![],
            current_pool_statistic: ApiPoolStatisticResponse {
                data5m: Default::default(),
                data1h: Default::default(),
                data6h: Default::default(),
                data24h: ApiTimeStatistic {
                    price_change: stats.price_change_24h,
                    txns: stats.txns_24h as i64,
                    buys: stats.buy_txns_24h as i64,
                    sells: stats.sell_txns_24h as i64,
                    volume_usd: stats.usd_volume_24h,
                    buy_volume_usd: stats.usd_buy_volume_24h,
                    sell_volume_usd: stats.usd_sell_volume_24h,
                    makers: stats.txns_24h as i64, // Approximate
                    buyers: stats.buy_txns_24h as i64,
                    sellers: stats.sell_txns_24h as i64,
                },
            },
        })
    }

    /// Convert ExtendedTokenHoldersMsg to API Holders
    fn convert_extended_token_holders_to_api(
        &self,
        holders_msg: &ExtendedTokenHoldersMsg,
    ) -> Result<crate::websocket::types::ApiTokenHoldersUpdate> {
        use superstack_api::api::types::indexer::{Holder, MakerType};

        let holders: Vec<Holder> = holders_msg
            .holders
            .iter()
            .map(|h| {
                // Calculate PnL and remaining value
                let remaining_usd = h.remaining_ui_amount * 0.1; // TODO: use actual price calculation
                let pnl_usd = h.received_usd - h.spent_usd;
                let remaining_percentage = if h.bought_ui_amount > 0.0 {
                    h.remaining_ui_amount / h.bought_ui_amount
                } else {
                    0.0
                };

                Holder {
                    maker: h.holder_address.clone(),
                    maker_type: MakerType::new(h.spent_usd), // Determine type based on spent USD
                    base_balance: h.native_token_balance,
                    bought_ui_amount: h.bought_ui_amount,
                    bought_usd: h.spent_usd,
                    bought_tx_count: h.bought_txns,
                    sold_ui_amount: h.sold_ui_amount,
                    sold_usd: h.received_usd,
                    sold_tx_count: h.sold_txns,
                    remaining_ui_amount: h.remaining_ui_amount,
                    remaining_usd,
                    remaining_percentage,
                    pnl_usd,
                }
            })
            .collect();

        Ok(crate::websocket::types::ApiTokenHoldersUpdate {
            holders,
            total_holders: holders_msg.holders.len() as u64,
        })
    }

    /// Convert PoolStatistic to API PoolStatisticResponse
    fn convert_pool_statistics_to_api(
        &self,
        stats: &PoolStatistic,
    ) -> Result<ApiPoolStatisticResponse> {
        let data5m = ApiTimeStatistic {
            price_change: stats.price_change_5m,
            txns: stats.txns_5m as i64,
            buys: stats.buy_txns_5m as i64,
            sells: stats.sell_txns_5m as i64,
            volume_usd: stats.usd_volume_5m,
            buy_volume_usd: stats.usd_buy_volume_5m,
            sell_volume_usd: stats.usd_sell_volume_5m,
            makers: stats.makers_5m as i64,
            buyers: stats.buyers_5m as i64,
            sellers: stats.sellers_5m as i64,
        };

        let data1h = ApiTimeStatistic {
            price_change: stats.price_change_1h,
            txns: stats.txns_1h as i64,
            buys: stats.buy_txns_1h as i64,
            sells: stats.sell_txns_1h as i64,
            volume_usd: stats.usd_volume_1h,
            buy_volume_usd: stats.usd_buy_volume_1h,
            sell_volume_usd: stats.usd_sell_volume_1h,
            makers: stats.makers_1h as i64,
            buyers: stats.buyers_1h as i64,
            sellers: stats.sellers_1h as i64,
        };

        let data6h = ApiTimeStatistic {
            price_change: stats.price_change_6h,
            txns: stats.txns_6h as i64,
            buys: stats.buy_txns_6h as i64,
            sells: stats.sell_txns_6h as i64,
            volume_usd: stats.usd_volume_6h,
            buy_volume_usd: stats.usd_buy_volume_6h,
            sell_volume_usd: stats.usd_sell_volume_6h,
            makers: stats.makers_6h as i64,
            buyers: stats.buyers_6h as i64,
            sellers: stats.sellers_6h as i64,
        };

        let data24h = ApiTimeStatistic {
            price_change: stats.price_change_24h,
            txns: stats.txns_24h as i64,
            buys: stats.buy_txns_24h as i64,
            sells: stats.sell_txns_24h as i64,
            volume_usd: stats.usd_volume_24h,
            buy_volume_usd: stats.usd_buy_volume_24h,
            sell_volume_usd: stats.usd_sell_volume_24h,
            makers: stats.makers_24h as i64,
            buyers: stats.buyers_24h as i64,
            sellers: stats.sellers_24h as i64,
        };

        Ok(ApiPoolStatisticResponse { data5m, data1h, data6h, data24h })
    }
}

#[async_trait]
impl<S> DataSource for KafkaDataSource<S>
where
    S: WebSocketService + 'static,
{
    async fn start(&self) -> Result<()> {
        info!("🚀 Starting Kafka data source...");

        // Set running state
        self.is_running.store(true, Ordering::Relaxed);

        // Create consumer with retry logic
        let consumer = match self.create_consumer() {
            Ok(c) => {
                info!("✅ Kafka consumer created successfully");
                Arc::new(c)
            }
            Err(e) => {
                error!("❌ Failed to create initial Kafka consumer: {}", e);
                return Err(e);
            }
        };

        let self_arc = Arc::new(self.clone());

        // Start background tasks
        self_arc.clone().start_stats_task();

        info!("🔄 Kafka data source started, beginning message consumption...");

        // Implement new message consumption logic
        let mut message_stream = consumer.stream();

        while self_arc.is_running.load(Ordering::Relaxed) {
            tokio::select! {
                message_result = message_stream.next() => {
                    match message_result {
                        Some(Ok(borrowed_message)) => {
                            let topic = borrowed_message.topic();
                            let partition = borrowed_message.partition();
                            let offset = borrowed_message.offset();
                            let payload_size = borrowed_message.payload().map(|p| p.len()).unwrap_or(0);

                            info!("🎯 RAW Kafka message received: topic={}, partition={}, offset={}, size={}",
                                  topic, partition, offset, payload_size);

                            if let Err(e) = self_arc.process_kafka_message(&borrowed_message).await {
                                error!("❌ Failed to process Kafka message: {}", e);
                                self_arc.stats_counters.processing_errors.fetch_add(1, Ordering::Relaxed);
                            }
                        }
                        Some(Err(e)) => {
                            error!("❌ Error receiving Kafka message: {}", e);
                            self_arc.stats_counters.processing_errors.fetch_add(1, Ordering::Relaxed);
                            // Brief delay before retry
                            tokio::time::sleep(Duration::from_millis(100)).await;
                        }
                        None => {
                            warn!("⚠️ Kafka message stream ended");
                            break;
                        }
                    }
                }
                _ = tokio::time::sleep(Duration::from_millis(10)) => {
                    // Periodic status check
                    continue;
                }
            }
        }

        info!("Kafka data source stopped");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        info!("Stopping Kafka data source...");
        self.is_running.store(false, Ordering::Relaxed);
        Ok(())
    }

    async fn health_check(&self) -> bool {
        // Simple health check - try to create a consumer
        self.create_consumer().is_ok()
    }
}

impl<S> Clone for KafkaDataSource<S>
where
    S: WebSocketService + 'static,
{
    fn clone(&self) -> Self {
        Self {
            websocket_service: self.websocket_service.clone(),
            kafka_config: self.kafka_config.clone(),
            dedup_config: self.dedup_config.clone(),
            kafka_consumer: self.kafka_consumer.clone(),
            signature_cache: self.signature_cache.clone(),
            content_hash_cache: self.content_hash_cache.clone(),
            stats_counters: self.stats_counters.clone(),
            is_running: self.is_running.clone(),
        }
    }
}
