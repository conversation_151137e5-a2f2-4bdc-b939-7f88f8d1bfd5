use crate::websocket::types::{
    ClientMessage, ConnectionInfo, MessageSender, Result, ServerMessage,
};
use async_trait::async_trait;
use std::{sync::Arc, time::Duration};

/// Core connection management trait for WebSocket connections
#[async_trait]
pub trait ConnectionManager: Send + Sync {
    async fn add_connection(&self, id: String, sender: MessageSender) -> Result<()>;
    async fn remove_connection(&self, id: &str) -> Result<()>;
    async fn get_connection_count(&self) -> usize;
    async fn get_connection_info(&self, id: &str) -> Option<ConnectionInfo>;
    async fn update_connection_metadata(&self, id: &str, metadata: serde_json::Value)
        -> Result<()>;
    async fn update_last_activity(&self, id: &str) -> Result<()>;
}

/// Message handling and broadcasting trait
#[async_trait]
pub trait MessageHandler: Send + Sync {
    /// Handle incoming client messages
    async fn handle_client_message(&self, conn_id: &str, message: ClientMessage) -> Result<()>;

    /// Broadcast message to all connections
    async fn broadcast_message(&self, message: ServerMessage) -> Result<()>;

    /// Send message to a specific connection
    async fn send_to_connection(&self, conn_id: &str, message: ServerMessage) -> Result<()>;

    /// Send message to all subscribers of a specific type
    async fn send_to_subscribers(&self, subscription: &str, message: ServerMessage) -> Result<()>;
}

/// Subscription management trait
#[async_trait]
pub trait SubscriptionManager: Send + Sync {
    /// Subscribe a connection to a specific type
    async fn subscribe(&self, conn_id: &str, subscription_type: &str) -> Result<()>;

    /// Unsubscribe a connection from a specific type
    async fn unsubscribe(&self, conn_id: &str, subscription_type: &str) -> Result<()>;

    /// Get all subscribers for a subscription type
    async fn get_subscribers(&self, subscription_type: &str) -> Vec<String>;

    /// Get all subscriptions for a connection
    async fn get_connection_subscriptions(&self, conn_id: &str) -> Vec<String>;
}

/// Metrics collection trait for monitoring and analytics
pub trait MetricsCollector: Send + Sync {
    fn record_connection_count(&self, count: usize);

    fn record_message_sent(&self, message_type: &str);

    fn record_latency(&self, operation: &str, duration: Duration);

    fn record_connection_event(&self, event: &str);

    fn record_subscription_event(&self, event: &str);

    fn record_service_event(&self, event: &str);

    fn record_error(&self, error_type: &str);
}

/// Data source trait for external data providers
#[async_trait]
pub trait DataSource: Send + Sync {
    /// Start listening for data updates
    async fn start(&self) -> Result<()>;

    /// Stop listening for data updates
    async fn stop(&self) -> Result<()>;

    /// Check if data source is healthy
    async fn health_check(&self) -> bool;
}

/// Comprehensive WebSocket service trait combining all functionality
#[async_trait]
pub trait WebSocketService: Send + Sync {
    /// Initialize the WebSocket service
    async fn initialize(&self) -> Result<()>;

    /// Handle new WebSocket connection
    async fn handle_connection(&self, conn_id: String, sender: MessageSender) -> Result<()>;

    /// Remove a WebSocket connection
    async fn remove_connection(&self, conn_id: &str) -> Result<()>;

    /// Process client message
    async fn process_message(&self, conn_id: &str, message: ClientMessage) -> Result<()>;

    /// Send message to a specific connection
    async fn send_to_connection(&self, conn_id: &str, message: ServerMessage) -> Result<()>;

    /// Broadcast data update
    async fn broadcast_update(&self, subscription_type: &str, message: ServerMessage)
        -> Result<()>;

    /// Update last activity time for a connection
    async fn update_last_activity(&self, conn_id: &str) -> Result<()>;

    /// Get service statistics
    async fn get_stats(&self) -> serde_json::Value;

    /// Shutdown the service gracefully
    async fn shutdown(&self) -> Result<()>;
}

/// No-op metrics implementation
#[derive(Debug, Default)]
pub struct NoOpMetrics;

impl MetricsCollector for NoOpMetrics {
    fn record_connection_count(&self, _count: usize) {}
    fn record_message_sent(&self, _message_type: &str) {}
    fn record_latency(&self, _operation: &str, _duration: Duration) {}
    fn record_connection_event(&self, _event: &str) {}
    fn record_subscription_event(&self, _event: &str) {}
    fn record_service_event(&self, _event: &str) {}
    fn record_error(&self, _error_type: &str) {}
}

/// Implementation of WebSocketService for Arc<S> where S implements WebSocketService
/// This allows us to use Arc<WebSocketService> in generic contexts
#[async_trait]
impl<S> WebSocketService for Arc<S>
where
    S: WebSocketService + Send + Sync,
{
    async fn initialize(&self) -> Result<()> {
        self.as_ref().initialize().await
    }

    async fn handle_connection(&self, conn_id: String, sender: MessageSender) -> Result<()> {
        self.as_ref().handle_connection(conn_id, sender).await
    }

    async fn remove_connection(&self, conn_id: &str) -> Result<()> {
        self.as_ref().remove_connection(conn_id).await
    }

    async fn process_message(&self, conn_id: &str, message: ClientMessage) -> Result<()> {
        self.as_ref().process_message(conn_id, message).await
    }

    async fn send_to_connection(&self, conn_id: &str, message: ServerMessage) -> Result<()> {
        self.as_ref().send_to_connection(conn_id, message).await
    }

    async fn broadcast_update(
        &self,
        subscription_type: &str,
        message: ServerMessage,
    ) -> Result<()> {
        self.as_ref().broadcast_update(subscription_type, message).await
    }

    async fn update_last_activity(&self, conn_id: &str) -> Result<()> {
        self.as_ref().update_last_activity(conn_id).await
    }

    async fn get_stats(&self) -> serde_json::Value {
        self.as_ref().get_stats().await
    }

    async fn shutdown(&self) -> Result<()> {
        self.as_ref().shutdown().await
    }
}
