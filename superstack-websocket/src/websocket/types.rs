use axum::extract::ws::Message;
use serde::{Deserialize, Serialize};
use thiserror::Error;
use tokio::sync::mpsc;

use superstack_api::{
    utils::timestamp, DetailedTokenResponse, Holders, MakerType, MarketTokenResponse,
    PoolStatisticResponse, TimeStatistic, Trade,
};
use superstack_data::postgres::{
    aggregator::Candle,
    enums::{CandleInterval, Chain},
};

/// Unified WebSocket message format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketMessage {
    pub r#type: String,
    pub timestamp: i64,
    pub data: serde_json::Value,
    /// Optional subscription parameters for context
    /// Helps clients distinguish between different subscriptions of the same type
    #[serde(skip_serializing_if = "Option::is_none")]
    pub params: Option<serde_json::Value>,
}

/// Message type constants
pub mod message_types {
    pub const TOKEN_UPDATE: &str = "token_update";
    pub const MARKET_TOKEN_UPDATE: &str = "market_token_update";
    pub const TRADE_UPDATE: &str = "trade_update";
    pub const POOL_STATISTICS_UPDATE: &str = "pool_statistics_update";
    pub const CANDLE_UPDATE: &str = "candle_update";
    pub const TOKEN_HOLDERS_UPDATE: &str = "token_holders_update";
    pub const PRICE_UPDATE: &str = "price_update";
    pub const CONNECTED: &str = "connected";
    pub const SUBSCRIBED: &str = "subscribed";
    pub const UNSUBSCRIBED: &str = "unsubscribed";
    pub const ERROR: &str = "error";
    pub const PING: &str = "ping";
    pub const PONG: &str = "pong";
    pub const STATISTICS: &str = "statistics";
}

/// Type alias for WebSocket message sender
pub type MessageSender = mpsc::Sender<Message>;

/// Type alias for Result with WebSocketError
pub type Result<T> = std::result::Result<T, WebSocketError>;

/// Type aliases for API compatibility
pub type ApiTimeStatistic = TimeStatistic;
pub type ApiPoolStatisticResponse = PoolStatisticResponse;
pub type ApiDetailedTokenResponse = DetailedTokenResponse;
pub type ApiMarketTokenResponse = MarketTokenResponse;
pub type ApiTrade = Trade;
pub type ApiCandle = Candle;
pub type ApiTokenHoldersUpdate = Holders;

/// WebSocket error types
#[derive(Error, Debug)]
pub enum WebSocketError {
    #[error("JSON serialization/deserialization error: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("Connection not found: {0}")]
    ConnectionNotFound(String),

    #[error("Send error: {0}")]
    SendError(String),

    #[error("Redis error: {0}")]
    RedisError(String),

    #[error("Kafka error: {0}")]
    KafkaError(String),

    #[error("Subscription error: {0}")]
    SubscriptionError(String),

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),

    #[error("Rate limit exceeded")]
    RateLimitExceeded,

    #[error("Message rate limit exceeded for connection: {0}")]
    MessageRateLimitExceeded(String),

    #[error("Subscription rate limit exceeded for connection: {0}")]
    SubscriptionRateLimitExceeded(String),

    #[error("Authentication failed: {0}")]
    AuthenticationFailed(String),

    #[error("Deserialization error: {0}")]
    DeserializationError(String),

    #[error("Validation error: {0}")]
    ValidationError(String),

    #[error("Duplicate message filtered: {0}")]
    DuplicateMessage(String),

    #[error("Internal error: {0}")]
    InternalError(String),
}

/// Client message types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ClientMessage {
    Subscribe { subscription_type: String, filters: Option<SubscriptionFilters> },
    Unsubscribe { subscription_type: String },
    Ping,
}

/// Subscription filters for targeted data streaming
/// Supports parameters from different HTTP API endpoints
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SubscriptionFilters {
    // === Token Info API parameters (/api/token/info/{tokenMint}) ===
    pub token_mint: Option<String>,   // Path parameter
    pub pool_address: Option<String>, // Query parameter

    // === Candle Snapshot API parameters (/api/candle/snapshot) ===
    pub pair_address: Option<String>, // Body parameter
    pub interval: Option<String>,     // Body parameter
    pub start_time: Option<u64>,      // Body parameter
    pub end_time: Option<u64>,        // Body parameter
    pub chain: Option<String>,        // Chain filter

    // === Token Trades API parameters (/api/token/trades/{tokenMint}) ===
    pub limit: Option<i64>,            // Query parameter
    pub max_timestamp: Option<u64>,    // Query parameter
    pub maker_type: Option<String>,    // Query parameter - "all", "dev", "snipers"
    pub min_usd_amount: Option<f64>,   // Query parameter
    pub max_usd_amount: Option<f64>,   // Query parameter
    pub maker_address: Option<String>, // Query parameter

    // === Token Holders API parameters (/api/token/holders/{tokenMint}) ===
    pub offset: Option<i64>, // Query parameter

    // === General filtering parameters ===
    pub token_addresses: Option<Vec<String>>, // Multiple token addresses
    pub min_volume: Option<f64>,              // Minimum volume filter
    pub min_market_cap: Option<f64>,          // Minimum market cap filter
    pub trade_types: Option<Vec<String>>,     // ["buy", "sell"]
    pub min_trade_amount: Option<f64>,        // Minimum trade amount filter
}

impl SubscriptionFilters {
    /// Check if a message matches any of the provided filters
    /// Empty filter list means no filtering (accept all messages)
    pub fn any_matches_message(
        filters: &[SubscriptionFilters],
        subscription_type: &str,
        message: &ServerMessage,
    ) -> bool {
        // Empty list means no filtering, accept all messages
        if filters.is_empty() {
            return true;
        }

        // Check if message matches any filter
        filters.iter().any(|filter| filter.matches_message(subscription_type, message))
    }

    /// Validate filters for a specific subscription type
    pub fn validate_for_subscription(&self, subscription_type: &str) -> Result<()> {
        match subscription_type {
            subscription_types::TOKEN_INFO => {
                if self.token_mint.is_none() {
                    return Err(WebSocketError::ValidationError(
                        "token_mint is required for token_info subscription".to_string(),
                    ));
                }
            }
            subscription_types::CANDLE_SNAPSHOT => {
                if self.pair_address.is_none() {
                    return Err(WebSocketError::ValidationError(
                        "pair_address is required for candle_snapshot subscription".to_string(),
                    ));
                }
                if self.interval.is_none() {
                    return Err(WebSocketError::ValidationError(
                        "interval is required for candle_snapshot subscription".to_string(),
                    ));
                }
                // Validate and normalize time range if provided
                if let Some(start) = self.start_time {
                    timestamp::normalize_and_validate(start).map_err(|e| {
                        WebSocketError::ValidationError(format!("Invalid start_time: {}", e))
                    })?;
                }
                if let Some(end) = self.end_time {
                    timestamp::normalize_and_validate(end).map_err(|e| {
                        WebSocketError::ValidationError(format!("Invalid end_time: {}", e))
                    })?;
                }
                if let (Some(start), Some(end)) = (self.start_time, self.end_time) {
                    let start_normalized = timestamp::normalize_to_seconds(start);
                    let end_normalized = timestamp::normalize_to_seconds(end);
                    if end_normalized <= start_normalized {
                        return Err(WebSocketError::ValidationError(
                            "end_time must be greater than start_time".to_string(),
                        ));
                    }
                }
            }
            subscription_types::TOKEN_TRADES => {
                if self.token_mint.is_none() {
                    return Err(WebSocketError::ValidationError(
                        "token_mint is required for token_trades subscription".to_string(),
                    ));
                }
                // Validate USD amount range if provided
                if let (Some(min), Some(max)) = (self.min_usd_amount, self.max_usd_amount) {
                    if min > max {
                        return Err(WebSocketError::ValidationError(
                            "min_usd_amount cannot be greater than max_usd_amount".to_string(),
                        ));
                    }
                }
                // Validate maker_type if provided
                if let Some(ref maker_type) = self.maker_type {
                    if !["all", "dev", "snipers"].contains(&maker_type.as_str()) {
                        return Err(WebSocketError::ValidationError(
                            "maker_type must be one of: all, dev, snipers".to_string(),
                        ));
                    }
                }
                // Validate max_timestamp if provided
                if let Some(max_ts) = self.max_timestamp {
                    timestamp::normalize_and_validate(max_ts).map_err(|e| {
                        WebSocketError::ValidationError(format!("Invalid max_timestamp: {}", e))
                    })?;
                }
            }
            subscription_types::TOKEN_HOLDERS_DETAILED => {
                if self.token_mint.is_none() {
                    return Err(WebSocketError::ValidationError(
                        "token_mint is required for token_holders_detailed subscription"
                            .to_string(),
                    ));
                }
            }

            _ => {
                // For other subscription types, no specific validation needed
            }
        }
        Ok(())
    }

    /// Check if a message matches the filters for a specific subscription type
    pub fn matches_message(&self, subscription_type: &str, message: &ServerMessage) -> bool {
        tracing::debug!(
            "Checking message match: subscription_type={}, filters={:?}",
            subscription_type,
            self
        );
        tracing::debug!(
            "Filter details: pair_address={:?}, chain={:?}, interval={:?}, start_time={:?}",
            self.pair_address,
            self.chain,
            self.interval,
            self.start_time
        );
        match subscription_type {
            subscription_types::TOKEN_INFO | subscription_types::TOKEN_UPDATES => {
                if let ServerMessage::TokenUpdate(token_data) = message {
                    // Check token_mint filter
                    if let Some(ref token_mint) = self.token_mint {
                        if token_data.token_mint != *token_mint {
                            return false;
                        }
                    }

                    // Check min_market_cap filter
                    if let Some(min_cap) = self.min_market_cap {
                        // Get market cap from current pool info
                        let current_market_cap = token_data
                            .pools
                            .get(token_data.current_pool_index as usize)
                            .map(|pool| pool.market_cap_usd)
                            .unwrap_or(0.0);

                        if current_market_cap < min_cap {
                            return false;
                        }
                    }

                    // Check min_volume filter
                    if let Some(min_vol) = self.min_volume {
                        if token_data.current_pool_statistic.data24h.volume_usd < min_vol {
                            return false;
                        }
                    }

                    return true;
                }
            }
            subscription_types::MARKET_TOKEN_UPDATES => {
                if let ServerMessage::MarketTokenUpdate(market_token_data) = message {
                    // Check token_mint filter
                    if let Some(ref token_mint) = self.token_mint {
                        if market_token_data.token_mint != *token_mint {
                            return false;
                        }
                    }

                    // Check min_market_cap filter
                    if let Some(min_cap) = self.min_market_cap {
                        if market_token_data.market_cap_usd < min_cap {
                            return false;
                        }
                    }

                    // Check min_volume filter
                    if let Some(min_vol) = self.min_volume {
                        if market_token_data.volume_usd < min_vol {
                            return false;
                        }
                    }

                    return true;
                }
            }
            subscription_types::TOKEN_TRADES | subscription_types::TRADE_UPDATES => {
                if let ServerMessage::TradeUpdate(trade_data) = message {
                    // Note: token_mint filtering for trades would require additional context
                    // since Trade struct doesn't contain token_mint directly
                    // This could be implemented by extending the message context or using external
                    // mapping

                    // Check trade_types filter
                    if let Some(ref trade_types) = self.trade_types {
                        let trade_type = if trade_data.is_buy { "buy" } else { "sell" };
                        if !trade_types.contains(&trade_type.to_string()) {
                            return false;
                        }
                    }

                    // Check min_trade_amount filter
                    if let Some(min_amount) = self.min_trade_amount {
                        if trade_data.usd < min_amount {
                            return false;
                        }
                    }

                    // Check USD amount range
                    if let Some(min_usd) = self.min_usd_amount {
                        if trade_data.usd < min_usd {
                            return false;
                        }
                    }
                    if let Some(max_usd) = self.max_usd_amount {
                        if trade_data.usd > max_usd {
                            return false;
                        }
                    }

                    // Check maker_address filter
                    if let Some(ref maker_addr) = self.maker_address {
                        if trade_data.maker != *maker_addr {
                            return false;
                        }
                    }

                    return true;
                }
            }
            subscription_types::CANDLE_SNAPSHOT | subscription_types::CANDLE_UPDATES => {
                if let ServerMessage::CandleUpdate(candle_data) = message {
                    // Check pair_address filter
                    if let Some(ref pair_addr) = self.pair_address {
                        tracing::info!(
                            "🔍 Filtering candle: expected pair_address={}, actual pool_address={}, match={}",
                            pair_addr,
                            candle_data.pool_address,
                            candle_data.pool_address == *pair_addr
                        );
                        if candle_data.pool_address != *pair_addr {
                            tracing::info!(
                                "❌ Candle filtered out: expected={}, actual={}",
                                pair_addr,
                                candle_data.pool_address
                            );
                            return false;
                        }
                    } else {
                        tracing::info!(
                            "⚠️ No pair_address filter provided, allowing candle with pool_address={}",
                            candle_data.pool_address
                        );
                    }

                    // Check chain filter
                    if let Some(ref chain_filter) = self.chain {
                        let candle_chain_str = match candle_data.chain {
                            Chain::Solana => "solana",
                            Chain::Hypercore => "hypercore",
                            Chain::HyperEvm => "hyperevm",
                        };
                        tracing::debug!(
                            "Filtering candle by chain: expected={}, actual={}, match={}",
                            chain_filter,
                            candle_chain_str,
                            candle_chain_str.eq_ignore_ascii_case(chain_filter)
                        );
                        if !candle_chain_str.eq_ignore_ascii_case(chain_filter) {
                            return false;
                        }
                    }

                    // Check interval filter
                    if let Some(ref interval_str) = self.interval {
                        let candle_interval_str = match candle_data.interval {
                            CandleInterval::S1 => "1s",
                            CandleInterval::S5 => "5s",
                            CandleInterval::S15 => "15s",
                            CandleInterval::S30 => "30s",
                            CandleInterval::M1 => "1m",
                            CandleInterval::M5 => "5m",
                            CandleInterval::M15 => "15m",
                            CandleInterval::M30 => "30m",
                            CandleInterval::H1 => "1h",
                            CandleInterval::H4 => "4h",
                            CandleInterval::H8 => "8h",
                            CandleInterval::H12 => "12h",
                            CandleInterval::H24 => "24h",
                            CandleInterval::D3 => "3d",
                            CandleInterval::D7 => "7d",
                            CandleInterval::D30 => "30d",
                        };
                        if candle_interval_str != interval_str {
                            return false;
                        }
                    }

                    // Check time range filters
                    if let Some(start_time) = self.start_time {
                        if (candle_data.open_timestamp_seconds as u64) < start_time {
                            return false;
                        }
                    }
                    if let Some(end_time) = self.end_time {
                        if (candle_data.close_timestamp_seconds as u64) > end_time {
                            return false;
                        }
                    }

                    tracing::debug!("Candle message passed all filters");
                    return true;
                }
            }
            subscription_types::TOKEN_HOLDERS_DETAILED | subscription_types::TOKEN_HOLDERS => {
                if let ServerMessage::TokenHoldersUpdate(_) = message {
                    // Token holders filtering logic can be added here
                    return true;
                }
            }
            subscription_types::POOL_STATISTICS => {
                if let ServerMessage::PoolStatisticsUpdate(pool_data) = message {
                    // Check min_volume filter
                    if let Some(min_vol) = self.min_volume {
                        if pool_data.data24h.volume_usd < min_vol {
                            return false;
                        }
                    }

                    return true;
                }
            }
            _ => {
                // For other subscription types, no specific filtering
                return true;
            }
        }

        false
    }
}

/// Simplified server message types for internal use
/// These are converted to WebSocketMessage before sending
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServerMessage {
    // Business data messages
    TokenUpdate(ApiDetailedTokenResponse),
    MarketTokenUpdate(ApiMarketTokenResponse),
    TradeUpdate(ApiTrade),
    PoolStatisticsUpdate(ApiPoolStatisticResponse),
    CandleUpdate(ApiCandle),
    TokenHoldersUpdate(ApiTokenHoldersUpdate),

    // System messages
    Connected {
        connection_id: String,
    },
    Subscribed {
        subscription_type: String,
        filters: Option<SubscriptionFilters>,
    },
    Unsubscribed {
        subscription_type: String,
    },
    Ping,
    Pong,
    Error {
        message: String,
    },

    // Statistics
    Statistics {
        total_tokens: u64,
        total_trades_24h: u64,
        total_volume_24h: f64,
        active_pools: u64,
    },
}

impl ServerMessage {
    /// Convert ServerMessage to unified WebSocketMessage format
    pub fn to_websocket_message(
        &self,
        timestamp: i64,
        filters: Option<&SubscriptionFilters>,
    ) -> WebSocketMessage {
        let mut message = match self {
            ServerMessage::TokenUpdate(data) => {
                let data_value = match serde_json::to_value(data) {
                    Ok(value) => value,
                    Err(e) => {
                        tracing::error!("❌ Failed to serialize TokenUpdate data: {}", e);
                        serde_json::Value::Null
                    }
                };
                WebSocketMessage {
                    r#type: message_types::TOKEN_UPDATE.to_string(),
                    timestamp,
                    data: data_value,
                    params: None,
                }
            }
            ServerMessage::MarketTokenUpdate(data) => {
                let data_value = match serde_json::to_value(data) {
                    Ok(value) => value,
                    Err(e) => {
                        tracing::error!("❌ Failed to serialize MarketTokenUpdate data: {}", e);
                        serde_json::Value::Null
                    }
                };
                WebSocketMessage {
                    r#type: message_types::MARKET_TOKEN_UPDATE.to_string(),
                    timestamp,
                    data: data_value,
                    params: None,
                }
            }
            ServerMessage::TradeUpdate(data) => {
                let data_value = match serde_json::to_value(data) {
                    Ok(value) => value,
                    Err(e) => {
                        tracing::error!("❌ Failed to serialize TradeUpdate data: {}", e);
                        serde_json::Value::Null
                    }
                };
                WebSocketMessage {
                    r#type: message_types::TRADE_UPDATE.to_string(),
                    timestamp,
                    data: data_value,
                    params: None,
                }
            }
            ServerMessage::PoolStatisticsUpdate(data) => WebSocketMessage {
                r#type: message_types::POOL_STATISTICS_UPDATE.to_string(),
                timestamp,
                data: serde_json::to_value(data).unwrap_or(serde_json::Value::Null),
                params: None,
            },
            ServerMessage::CandleUpdate(data) => WebSocketMessage {
                r#type: message_types::CANDLE_UPDATE.to_string(),
                timestamp,
                data: serde_json::to_value(data).unwrap_or(serde_json::Value::Null),
                params: None,
            },
            ServerMessage::TokenHoldersUpdate(data) => WebSocketMessage {
                r#type: message_types::TOKEN_HOLDERS_UPDATE.to_string(),
                timestamp,
                data: serde_json::to_value(data).unwrap_or(serde_json::Value::Null),
                params: None,
            },
            ServerMessage::Connected { connection_id } => WebSocketMessage {
                r#type: message_types::CONNECTED.to_string(),
                timestamp,
                data: serde_json::json!({ "connectionId": connection_id }),
                params: None,
            },
            ServerMessage::Subscribed { subscription_type, filters } => WebSocketMessage {
                r#type: message_types::SUBSCRIBED.to_string(),
                timestamp,
                data: serde_json::json!({
                    "subscriptionType": subscription_type,
                    "filters": filters
                }),
                params: None,
            },
            ServerMessage::Unsubscribed { subscription_type } => WebSocketMessage {
                r#type: message_types::UNSUBSCRIBED.to_string(),
                timestamp,
                data: serde_json::json!({ "subscriptionType": subscription_type }),
                params: None,
            },
            ServerMessage::Ping => WebSocketMessage {
                r#type: message_types::PING.to_string(),
                timestamp,
                data: serde_json::Value::Null,
                params: None,
            },
            ServerMessage::Pong => WebSocketMessage {
                r#type: message_types::PONG.to_string(),
                timestamp,
                data: serde_json::Value::Null,
                params: None,
            },
            ServerMessage::Error { message } => WebSocketMessage {
                r#type: message_types::ERROR.to_string(),
                timestamp,
                data: serde_json::json!({ "message": message }),
                params: None,
            },
            ServerMessage::Statistics {
                total_tokens,
                total_trades_24h,
                total_volume_24h,
                active_pools,
            } => WebSocketMessage {
                r#type: message_types::STATISTICS.to_string(),
                timestamp,
                data: serde_json::json!({
                    "totalTokens": total_tokens,
                    "totalTrades24h": total_trades_24h,
                    "totalVolume24h": total_volume_24h,
                    "activePools": active_pools
                }),
                params: None,
            },
        };

        // Extract params from message content or filters
        if let Some(filters) = filters {
            // If filters are provided, use them as base and override with message content
            let mut params = serde_json::to_value(filters).unwrap_or(serde_json::Value::Null);

            // Override with message-specific params if available
            if let Some(content_params) = self.extract_subscription_params() {
                if let (Some(params_obj), Some(content_obj)) =
                    (params.as_object_mut(), content_params.as_object())
                {
                    // Merge content params into filter params, with content taking precedence
                    for (key, value) in content_obj {
                        params_obj.insert(key.clone(), value.clone());
                    }
                }
            }

            message.params = Some(params);
        } else {
            // No filters provided, extract params from message content if available
            message.params = self.extract_subscription_params();
        }

        message
    }

    /// Extract subscription parameters from message content
    /// This allows the server to populate params based on the actual message data
    /// rather than relying on client subscription filters
    pub fn extract_subscription_params(&self) -> Option<serde_json::Value> {
        match self {
            ServerMessage::CandleUpdate(candle) => {
                // Convert CandleInterval enum to client-friendly format
                let interval_str = match candle.interval {
                    CandleInterval::S1 => "1s",
                    CandleInterval::S5 => "5s",
                    CandleInterval::S15 => "15s",
                    CandleInterval::S30 => "30s",
                    CandleInterval::M1 => "1m",
                    CandleInterval::M5 => "5m",
                    CandleInterval::M15 => "15m",
                    CandleInterval::M30 => "30m",
                    CandleInterval::H1 => "1h",
                    CandleInterval::H4 => "4h",
                    CandleInterval::H8 => "8h",
                    CandleInterval::H12 => "12h",
                    CandleInterval::H24 => "24h",
                    CandleInterval::D3 => "3d",
                    CandleInterval::D7 => "7d",
                    CandleInterval::D30 => "30d",
                };

                Some(serde_json::json!({
                    "pairAddress": candle.pool_address,
                    "interval": interval_str,
                    "startTime": candle.open_timestamp_seconds,
                    "endTime": candle.close_timestamp_seconds
                }))
            }
            ServerMessage::TokenUpdate(token) => Some(serde_json::json!({
                "tokenMint": token.token_mint,
                "poolAddress": token.pools.get(token.current_pool_index as usize)
                    .map(|pool| pool.address.clone())
                    .unwrap_or_default()
            })),
            ServerMessage::MarketTokenUpdate(token) => Some(serde_json::json!({
                "tokenMint": token.token_mint,
                "chain": token.chain
            })),
            ServerMessage::TradeUpdate(trade) => {
                // Extract token mint from trade data if available
                // Note: This might require additional context or data structure changes
                Some(serde_json::json!({
                    "maker": trade.maker,
                    "makerType": match trade.maker_type {
                        MakerType::Plankton => "plankton",
                        MakerType::Fish => "fish",
                        MakerType::Shrimp => "shrimp",
                        MakerType::Dolphin => "dolphin",
                        MakerType::Whale => "whale",
                    },
                    "usdAmount": trade.usd,
                    "isBuy": trade.is_buy
                }))
            }
            ServerMessage::TokenHoldersUpdate(_holders) => {
                // For holders, we might need additional context
                // This could be enhanced with token mint information
                Some(serde_json::json!({
                    "limit": 20, // Default limit
                    "offset": 0  // Default offset
                }))
            }
            ServerMessage::PoolStatisticsUpdate(_stats) => {
                // Pool statistics might not need specific params
                None
            }
            _ => None,
        }
    }

    /// Get interval duration in seconds for calculating end time
    fn get_interval_seconds(&self, interval: &CandleInterval) -> i64 {
        match interval {
            CandleInterval::S1 => 1,
            CandleInterval::S5 => 5,
            CandleInterval::S15 => 15,
            CandleInterval::S30 => 30,
            CandleInterval::M1 => 60,
            CandleInterval::M5 => 300,
            CandleInterval::M15 => 900,
            CandleInterval::M30 => 1800,
            CandleInterval::H1 => 3600,
            CandleInterval::H4 => 14400,
            CandleInterval::H8 => 28800,
            CandleInterval::H12 => 43200,
            CandleInterval::H24 => 86400,
            CandleInterval::D3 => 259200,
            CandleInterval::D7 => 604800,
            CandleInterval::D30 => 2592000,
        }
    }
}

/// Configuration for WebSocket service
#[derive(Debug, Clone)]
pub struct WebSocketConfig {
    pub max_connections: usize,
    pub max_subscriptions_per_connection: usize,
    pub message_buffer_size: usize,
    pub ping_interval_seconds: u64,
    pub connection_timeout_seconds: u64,
    pub redis_url: String,
    pub kafka_config: KafkaConfig,
    pub deduplication_config: DeduplicationConfig,
    pub rate_limiting_config: RateLimitingConfig,
}

/// Rate limiting configuration
#[derive(Debug, Clone)]
pub struct RateLimitingConfig {
    pub enabled: bool,
    pub messages_per_minute: u32,
    pub subscriptions_per_minute: u32,
    pub window_size_seconds: u64,
}

/// Kafka configuration
#[derive(Debug, Clone)]
pub struct KafkaConfig {
    pub bootstrap_servers: String,
    pub group_id: String,
    pub auto_offset_reset: String,
    pub enable_auto_commit: bool,
    pub session_timeout_ms: u32,
    pub heartbeat_interval_ms: u32,
}

/// Deduplication configuration
#[derive(Debug, Clone)]
pub struct DeduplicationConfig {
    pub enabled: bool,
    pub cache_size: usize,
    pub cache_ttl_seconds: u64,
    pub dedup_by_signature: bool,
    pub dedup_by_content_hash: bool,
}

impl Default for WebSocketConfig {
    fn default() -> Self {
        Self {
            max_connections: 50_000,
            max_subscriptions_per_connection: 100,
            message_buffer_size: 2000,
            ping_interval_seconds: 30,
            connection_timeout_seconds: 300,
            redis_url: "redis://localhost:6379".to_string(),
            kafka_config: KafkaConfig::default(),
            deduplication_config: DeduplicationConfig::default(),
            rate_limiting_config: RateLimitingConfig::default(),
        }
    }
}

impl Default for RateLimitingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            messages_per_minute: 1000,
            subscriptions_per_minute: 100,
            window_size_seconds: 60,
        }
    }
}

impl Default for KafkaConfig {
    fn default() -> Self {
        Self {
            bootstrap_servers: "localhost:9092".to_string(),
            group_id: {
                let uuid = uuid::Uuid::new_v4().to_string()[..8].to_string();
                format!("superstack_websocket_consumer_{}", uuid)
            },
            auto_offset_reset: "latest".to_string(),
            enable_auto_commit: true,
            session_timeout_ms: 30000,
            heartbeat_interval_ms: 10000,
        }
    }
}

impl Default for DeduplicationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            cache_size: 100_000,
            cache_ttl_seconds: 300, // 5 minutes
            dedup_by_signature: true,
            dedup_by_content_hash: true,
        }
    }
}

/// Connection information for monitoring
#[derive(Debug, Clone)]
pub struct ConnectionInfo {
    pub id: String,
    pub connected_at: i64,
    pub last_activity: i64,
    pub subscriptions: Vec<String>,
    /// Filters per subscription type - allows different filters for different subscription types
    /// Each subscription type can have multiple filters that are OR'ed together
    /// Empty Vec means no filtering (accept all messages)
    pub subscription_filters: std::collections::HashMap<String, Vec<SubscriptionFilters>>,
    pub metadata: Option<serde_json::Value>,
}

/// Message processing statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingStats {
    pub messages_processed: u64,
    pub messages_filtered: u64,
    pub messages_deduplicated: u64,
    pub messages_broadcasted: u64,
    pub processing_errors: u64,
    pub last_processed_timestamp: i64,
}

/// Subscription types constants
pub mod subscription_types {
    // === Existing subscription types ===
    pub const PRICE_UPDATES: &str = "price_updates";
    pub const TRADE_UPDATES: &str = "trade_updates";
    pub const TOKEN_UPDATES: &str = "token_updates";
    pub const MARKET_TOKEN_UPDATES: &str = "market_token_updates";
    pub const POOL_STATISTICS: &str = "pool_statistics";
    pub const CANDLE_UPDATES: &str = "candle_updates";
    pub const TOKEN_HOLDERS: &str = "token_holders";
    pub const ALL_UPDATES: &str = "all_updates";
    pub const STATISTICS: &str = "statistics";

    // === API-specific subscription types ===
    pub const TOKEN_INFO: &str = "token_info"; // Corresponds to /api/token/info/{tokenMint}
    pub const CANDLE_SNAPSHOT: &str = "candle_snapshot"; // Corresponds to /api/candle/snapshot
    pub const TOKEN_TRADES: &str = "token_trades"; // Corresponds to /api/token/trades/{tokenMint}
    pub const TOKEN_HOLDERS_DETAILED: &str = "token_holders_detailed"; // Corresponds to
                                                                       // /api/token/holders/
                                                                       // {tokenMint}
}

#[cfg(test)]
mod tests {
    use superstack_api::Chain;

    use super::*;

    #[test]
    fn test_websocket_message_with_params() {
        // Create a sample filter
        let filters = SubscriptionFilters {
            token_mint: Some("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string()),
            interval: Some("5m".to_string()),
            pair_address: Some("58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2".to_string()),
            min_usd_amount: Some(100.0),
            ..Default::default()
        };

        // Create a sample candle
        let candle = ApiCandle {
            chain: Chain::Solana,
            pool_address: "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2".to_string(),
            open_timestamp_seconds: 1640995200,
            close_timestamp_seconds: 1640995260,
            interval: CandleInterval::M5,
            native_token_usd_price: 100.0,
            usd_open_price: 1.0,
            usd_close_price: 1.05,
            usd_high_price: 1.1,
            usd_low_price: 0.9,
            usd_open_market_cap: 50000000.0,
            usd_close_market_cap: 52500000.0,
            usd_high_market_cap: 55000000.0,
            usd_low_market_cap: 45000000.0,
            usd_volume: 1050.0,
            txns: 10,
        };

        let server_message = ServerMessage::CandleUpdate(candle);

        // Test without filters (should extract params from message content)
        let message_without_filters = server_message.to_websocket_message(1640995200, None);
        assert!(message_without_filters.params.is_some());

        let params_without_filters = message_without_filters.params.unwrap();
        let params_obj_without_filters = params_without_filters.as_object().unwrap();

        // Should contain message content params
        assert_eq!(
            params_obj_without_filters.get("pairAddress").unwrap().as_str().unwrap(),
            "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"
        );
        assert_eq!(params_obj_without_filters.get("interval").unwrap().as_str().unwrap(), "5m");

        // Test with filters
        let message_with_params = server_message.to_websocket_message(1640995200, Some(&filters));
        assert!(message_with_params.params.is_some());

        // Verify the params contain the filter data
        let params = message_with_params.params.unwrap();
        let params_obj = params.as_object().unwrap();

        assert_eq!(
            params_obj.get("tokenMint").unwrap().as_str().unwrap(),
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        );
        assert_eq!(params_obj.get("interval").unwrap().as_str().unwrap(), "5m");
        assert_eq!(
            params_obj.get("pairAddress").unwrap().as_str().unwrap(),
            "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"
        );
        assert_eq!(params_obj.get("minUsdAmount").unwrap().as_f64().unwrap(), 100.0);
    }

    #[test]
    fn test_subscription_filters_default() {
        let filters = SubscriptionFilters::default();
        assert!(filters.token_mint.is_none());
        assert!(filters.interval.is_none());
        assert!(filters.pair_address.is_none());
        assert!(filters.min_usd_amount.is_none());
    }

    #[test]
    fn test_pair_address_field_mapping() {
        // Test that pairAddress in JSON maps to pair_address in struct
        let json = r#"{"pairAddress": "9v7qXwvkjekWeZEEmzSABsoDCEs7CrgwYKKi4sRET8H4"}"#;
        let filters: SubscriptionFilters = serde_json::from_str(json).unwrap();
        assert_eq!(
            filters.pair_address,
            Some("9v7qXwvkjekWeZEEmzSABsoDCEs7CrgwYKKi4sRET8H4".to_string())
        );

        // Test serialization back to JSON
        let serialized = serde_json::to_string(&filters).unwrap();
        assert!(serialized.contains("pairAddress"));
        assert!(serialized.contains("9v7qXwvkjekWeZEEmzSABsoDCEs7CrgwYKKi4sRET8H4"));
    }

    #[test]
    fn test_params_extraction_from_message_content() {
        // Test candle message params extraction
        let candle = ApiCandle {
            chain: Chain::Solana,
            pool_address: "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2".to_string(),
            open_timestamp_seconds: 1640995200,
            close_timestamp_seconds: 1640995500,
            interval: CandleInterval::M5,
            native_token_usd_price: 100.0,
            usd_open_price: 1.0,
            usd_close_price: 1.05,
            usd_high_price: 1.1,
            usd_low_price: 0.9,
            usd_open_market_cap: 50000000.0,
            usd_close_market_cap: 52500000.0,
            usd_high_market_cap: 55000000.0,
            usd_low_market_cap: 45000000.0,
            usd_volume: 1050.0,
            txns: 10,
        };

        let candle_message = ServerMessage::CandleUpdate(candle);

        // Test params extraction without filters (should extract from content)
        let websocket_message = candle_message.to_websocket_message(1640995200, None);
        assert!(websocket_message.params.is_some());

        let params = websocket_message.params.unwrap();
        let params_obj = params.as_object().unwrap();

        assert_eq!(
            params_obj.get("pairAddress").unwrap().as_str().unwrap(),
            "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"
        );
        assert_eq!(params_obj.get("interval").unwrap().as_str().unwrap(), "5m");
        assert_eq!(params_obj.get("startTime").unwrap().as_i64().unwrap(), 1640995200);
        assert_eq!(params_obj.get("endTime").unwrap().as_i64().unwrap(), 1640995500);

        // Test trade message params extraction
        let trade = ApiTrade {
            tx_sig: "test_signature".to_string(),
            maker: "test_maker".to_string(),
            maker_type: MakerType::Fish,
            is_buy: true,
            token_ui_amount: 100.0,
            base_ui_amount: 10.0,
            usd: 250.0,
            sol_amount: 1.47, // 250 / 170
            base_token_symbol: "SOL".to_string(),
            price_usd: 2.5,
            price_native: 0.025,
            market_cap_usd: 1000000.0,
            timestamp: 1640995200,
        };

        let trade_message = ServerMessage::TradeUpdate(trade);
        let trade_websocket_message = trade_message.to_websocket_message(1640995200, None);
        assert!(trade_websocket_message.params.is_some());

        let trade_params = trade_websocket_message.params.unwrap();
        let trade_params_obj = trade_params.as_object().unwrap();

        assert_eq!(trade_params_obj.get("maker").unwrap().as_str().unwrap(), "test_maker");
        assert_eq!(trade_params_obj.get("makerType").unwrap().as_str().unwrap(), "fish");
        assert_eq!(trade_params_obj.get("usdAmount").unwrap().as_f64().unwrap(), 250.0);
        assert_eq!(trade_params_obj.get("isBuy").unwrap().as_bool().unwrap(), true);
    }
}

impl Default for SubscriptionFilters {
    fn default() -> Self {
        Self {
            token_mint: None,
            pool_address: None,
            pair_address: None,
            interval: None,
            start_time: None,
            end_time: None,
            chain: None,
            limit: None,
            max_timestamp: None,
            maker_type: None,
            min_usd_amount: None,
            max_usd_amount: None,
            maker_address: None,
            offset: None,
            token_addresses: None,
            min_volume: None,
            min_market_cap: None,
            trade_types: None,
            min_trade_amount: None,
        }
    }
}
