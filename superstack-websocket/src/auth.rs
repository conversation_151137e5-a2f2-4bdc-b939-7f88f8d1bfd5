use serde::{Deserialize, Serialize};
use thiserror::Error;

#[derive(<PERSON>rro<PERSON>, Debug)]
pub enum AuthError {
    #[error("Invalid token")]
    InvalidToken,
    #[error("Request failed: {0}")]
    RequestFailed(String),
    #[error("Service unavailable")]
    ServiceUnavailable,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct TokenValidationRequest {
    pub token: String,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct UserInfo {
    pub user_id: String,
    pub wallet_address: String,
    pub subscription_level: String,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct AuthService {
    api_service_url: String,
    client: reqwest::Client,
}

impl AuthService {
    pub fn new(api_service_url: String) -> Self {
        Self { api_service_url, client: reqwest::Client::new() }
    }

    pub async fn validate_token(&self, token: &str) -> Result<UserInfo, AuthError> {
        let request = TokenValidationRequest { token: token.to_string() };

        let url = format!("{}/internal/validate-token", self.api_service_url);

        let response = self
            .client
            .post(&url)
            .json(&request)
            .send()
            .await
            .map_err(|e| AuthError::RequestFailed(e.to_string()))?;

        if response.status().is_success() {
            let user_info: UserInfo =
                response.json().await.map_err(|e| AuthError::RequestFailed(e.to_string()))?;
            Ok(user_info)
        } else if response.status() == 401 {
            Err(AuthError::InvalidToken)
        } else {
            Err(AuthError::ServiceUnavailable)
        }
    }

    pub async fn health_check(&self) -> bool {
        let url = format!("{}/health", self.api_service_url);

        match self.client.get(&url).send().await {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }
}
