use crate::websocket::KafkaConfig;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct Config {
    pub port: u16,
    pub api_service_url: String,
    pub kafka: KafkaConfig,
}

impl Config {
    pub fn from_env() -> Self {
        Self {
            port: std::env::var("WEBSOCKET_PORT").ok().and_then(|s| s.parse().ok()).unwrap_or(8081),
            api_service_url: std::env::var("API_SERVICE_URL")
                .unwrap_or_else(|_| "http://localhost:8080".to_string()),
            kafka: KafkaConfig {
                bootstrap_servers: std::env::var("KAFKA_BOOTSTRAP_SERVERS")
                    .unwrap_or_else(|_| "localhost:9092".to_string()),
                group_id: std::env::var("KAFKA_GROUP_ID")
                    .unwrap_or_else(|_| "superstack_websocket_consumer".to_string()),
                auto_offset_reset: std::env::var("KAFKA_AUTO_OFFSET_RESET")
                    .unwrap_or_else(|_| "latest".to_string()),
                enable_auto_commit: std::env::var("KAFKA_ENABLE_AUTO_COMMIT")
                    .ok()
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(true),
                session_timeout_ms: std::env::var("KAFKA_SESSION_TIMEOUT_MS")
                    .ok()
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(30000),
                heartbeat_interval_ms: std::env::var("KAFKA_HEARTBEAT_INTERVAL_MS")
                    .ok()
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(10000),
            },
        }
    }
}
