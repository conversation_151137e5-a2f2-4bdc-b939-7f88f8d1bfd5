mod auth;
mod config;
mod websocket;

use axum::{
    extract::{ws::WebSocketUpgrade, Query, State},
    http::StatusCode,
    response::Response,
    routing::get,
    Router,
};
use std::{collections::HashMap, sync::Arc};
use tower_http::cors::{Any, CorsLayer};
use tracing::{error, info};

use crate::{
    auth::AuthService,
    config::Config,
    websocket::{
        handle_websocket_connection_with_auth,
        traits::{DataSource, WebSocketService},
        DeduplicationConfig, DefaultWebSocketManager, KafkaDataSource, WebSocketConfig,
    },
};

#[derive(Clone)]
pub struct AppState {
    websocket_manager: Arc<DefaultWebSocketManager>,
    kafka_source: Arc<KafkaDataSource<DefaultWebSocketManager>>,
    auth_service: Arc<AuthService>,
}

async fn websocket_handler(
    ws: WebSocketUpgrade,
    Query(params): Query<HashMap<String, String>>,
    State(state): State<AppState>,
) -> Result<Response, StatusCode> {
    // Check if development mode is enabled
    let dev_mode = std::env::var("DEV_MODE").map(|v| v.to_lowercase() == "true").unwrap_or(false);

    if dev_mode {
        info!("Development mode enabled - skipping authentication");

        // Create a mock user info for development
        let mock_user = crate::auth::UserInfo {
            user_id: "dev_user".to_string(),
            wallet_address: "dev_wallet_address".to_string(),
            subscription_level: "premium".to_string(),
        };

        Ok(ws.on_upgrade(move |socket| async move {
            info!("New WebSocket connection established in dev mode");

            // Handle WebSocket connection with mock auth
            handle_websocket_connection_with_auth(socket, state.websocket_manager, mock_user).await;
        }))
    } else {
        // Extract token from query parameters
        let token = params.get("token").ok_or(StatusCode::UNAUTHORIZED)?;

        // Validate token with API service
        match state.auth_service.validate_token(token).await {
            Ok(user_info) => {
                info!("WebSocket connection authenticated for user: {}", user_info.user_id);

                Ok(ws.on_upgrade(move |socket| async move {
                    info!("New WebSocket connection established for user: {}", user_info.user_id);

                    // Handle WebSocket connection with real auth
                    handle_websocket_connection_with_auth(
                        socket,
                        state.websocket_manager,
                        user_info,
                    )
                    .await;
                }))
            }
            Err(e) => {
                error!("WebSocket authentication failed: {:?}", e);
                Err(StatusCode::UNAUTHORIZED)
            }
        }
    }
}

async fn health(State(state): State<AppState>) -> axum::Json<serde_json::Value> {
    // Check API service health
    let api_healthy = state.auth_service.health_check().await;

    // Check Kafka health
    let kafka_healthy = state.kafka_source.health_check().await;

    // Get WebSocket stats
    let ws_stats = state.websocket_manager.get_stats().await;

    let overall_status = if api_healthy && kafka_healthy { "healthy" } else { "degraded" };

    let health_info = serde_json::json!({
        "status": overall_status,
        "services": {
            "api_service": {
                "status": if api_healthy { "up" } else { "down" },
                "healthy": api_healthy
            },
            "kafka_service": {
                "status": if kafka_healthy { "up" } else { "down" },
                "healthy": kafka_healthy
            },
            "websocket_service": {
                "status": "up",
                "healthy": true,
                "stats": ws_stats
            }
        },
        "timestamp": chrono::Utc::now().timestamp(),
        "uptime_seconds": std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs()
    });

    axum::Json(health_info)
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Load environment variables from .env file
    dotenv::dotenv().ok();

    // Initialize logging with better formatting
    tracing_subscriber::fmt()
        .with_env_filter(
            tracing_subscriber::EnvFilter::from_default_env()
                .add_directive("superstack_websocket=debug".parse()?),
        )
        .init();

    info!("🚀 Starting SuperStack WebSocket Service");

    // Load configuration
    let mut config = Config::from_env();

    // Generate unique consumer group ID if auto-scalable mode is enabled
    let auto_scalable =
        std::env::var("AUTO_SCALABLE").map(|v| v.to_lowercase() == "true").unwrap_or(false);

    if auto_scalable {
        let uuid = uuid::Uuid::new_v4().to_string()[..8].to_string();
        config.kafka.group_id = format!("{}_{}", config.kafka.group_id, uuid);
        info!(
            "🆔 Auto-scalable mode: Generated unique consumer group ID: {}",
            config.kafka.group_id
        );
    }

    info!(
        "📋 Configuration loaded: port={}, api_url={}, kafka_servers={}, group_id={}",
        config.port, config.api_service_url, config.kafka.bootstrap_servers, config.kafka.group_id
    );

    // Create WebSocket manager
    info!("🔧 Initializing WebSocket manager...");
    let ws_config =
        WebSocketConfig { kafka_config: config.kafka.clone(), ..WebSocketConfig::default() };
    let websocket_manager = Arc::new(DefaultWebSocketManager::with_default_metrics(ws_config));

    // Create Kafka data source
    info!("📡 Creating Kafka data source...");
    let dedup_config = DeduplicationConfig::default();
    let kafka_source =
        match KafkaDataSource::new(websocket_manager.clone(), config.kafka.clone(), dedup_config) {
            Ok(source) => Arc::new(source),
            Err(e) => {
                error!("❌ Failed to create Kafka data source: {}", e);
                return Err(e.into());
            }
        };

    // Create authentication service
    info!("🔐 Creating authentication service...");
    let auth_service = Arc::new(AuthService::new(config.api_service_url.clone()));

    // Initialize WebSocket manager
    info!("⚡ Initializing WebSocket manager...");
    websocket_manager.initialize().await?;
    websocket_manager.clone().start_cleanup_task();

    // Start Kafka consumer in background
    info!("🔌 Starting Kafka consumer...");
    let kafka_source_clone = kafka_source.clone();
    tokio::spawn(async move {
        if let Err(e) = kafka_source_clone.start().await {
            error!("❌ Kafka consumer error: {:?}", e);
        }
    });

    // Give Kafka consumer a moment to initialize
    tokio::time::sleep(std::time::Duration::from_millis(500)).await;
    info!("✅ Kafka consumer started in background");

    // Create application state
    let app_state = AppState { websocket_manager, kafka_source, auth_service };

    // Create routes
    let app = Router::new()
        .route("/ws", get(websocket_handler))
        .route("/health", get(health))
        .layer(CorsLayer::new().allow_origin(Any).allow_methods(Any).allow_headers(Any))
        .with_state(app_state);

    // Start server
    let addr = format!("0.0.0.0:{}", config.port);
    info!("🌐 WebSocket service listening on {}", addr);
    info!("🔗 Health check available at: http://{}/health", addr);
    info!("🔗 WebSocket endpoint available at: ws://{}/ws", addr);

    let listener = tokio::net::TcpListener::bind(&addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}
