# WebSocket Service Configuration
WEBSOCKET_PORT=8081

# API Service URL for authentication
API_SERVICE_URL=http://localhost:8080

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=superstack_websocket_consumer
KAFKA_AUTO_OFFSET_RESET=latest
KAFKA_ENABLE_AUTO_COMMIT=true
KAFKA_SESSION_TIMEOUT_MS=30000
KAFKA_HEARTBEAT_INTERVAL_MS=10000

# Auto-scaling Configuration
# Set to true for Cloud Run auto-scaling environments to generate unique consumer group IDs
AUTO_SCALABLE=false

# WebSocket Configuration
WEBSOCKET_BUFFER_SIZE=10000
WEBSOCKET_MAX_CONNECTIONS=10000
WEBSOCKET_MAX_SUBSCRIPTIONS_PER_CONNECTION=50
WEBSOCKET_CONNECTION_TIMEOUT_SECONDS=300
WEBSOCKET_PING_INTERVAL_SECONDS=30

# Deduplication Configuration
DEDUPLICATION_ENABLED=true
DEDUPLICATION_CACHE_SIZE=100000
DEDUPLICATION_CACHE_TTL_SECONDS=300
DEDUPLICATION_BY_SIGNATURE=true
DEDUPLICATION_BY_CONTENT_HASH=false

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MESSAGES_PER_MINUTE=1000
RATE_LIMIT_SUBSCRIPTIONS_PER_MINUTE=100

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL_SECONDS=60

# Logging
RUST_LOG=info
